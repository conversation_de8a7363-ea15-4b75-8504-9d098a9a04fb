import Intalio from './common.js'
import VisualTracking from './visualTracking.js'
import NoteList from './noteList.js'
import NonArchivedAttachmentList from './nonArchivedAttachmentsList.js'
import LinkedCorrespondence from './linkedCorrespondenceList.js'
import Document from './document.js'
import Attachment from './attachment.js'
import ActivityLogTimeline from './activityLogTimeline.js'
import TransferHistory from './transferHistoryList.js'
import MyTransfer from './myTransfer.js'
import { Categories, IdentityService, CategoryModel, DelegationUsers } from './lookup.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import DocumentDetails from './documentDetails.js'

class VipDocumentDetails extends Intalio.Model
{
    constructor()
    {
        super();
        this.id = null;
        this.status = null;
        this.statusId = null;
        this.createdByUser = null;
        this.referenceNumber = null;
        this.categoryName = null;
        this.documentModel = null;
        this.statuses = null;
        this.showMyTransfer = null;
        this.readonly = null;
        this.delegationId = null;
        this.fromSearch = false;
        this.tabs = [];
        this.tabsWithStatic = [];
        this.tabsActions = [];
        this.showBackButton = true;
        this.fromDraft = false;
        this.sentToUser = false;
        this.attachmentCount = null;
        this.attachmentId = null;
        this.fromSent = false;
        this.fromInbox = false;
        this.isCced = false;
        this.showPreview = false;
        this.nodeId = null;
        this.fromInbox = false;
        this.receiverPerson = null;
        this.senderPerson = null;
        this.isExternalReceiver = false;
        this.isExternalSender = false;
        this.VoiceNote = false;
        this.hasReferenceNumber = false;
        this.modalComponentId = null;
        this.parentComponentId = null;
        this.isModal = false;
        this.attchmentIsLocked = false;
        this.attachmentVersion = null;
    }
}
function refreshInboxItem(id, gSelf)
{
    Common.ajaxGet('/Transfer/GetTransferDetailsById', { id: id }, function (result)
    {
        if (result)
        {
            var htmlLi = '';
            var transfer = result;
            var liClass = "mdl-li";
            if (!transfer.isRead)
            {
                liClass += " unread";
            }
            var lockedByMe = false;
            var delegatedUser = gSelf.model.delegationId !== null ? new DelegationUsers().getById(Number(gSelf.model.delegationId)) : null;
            var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
            if (transfer.isLocked && (transfer.ownerUserId !== null && transfer.ownerUserId === Number($("#hdUserId").val()) && gSelf.model.delegationId === null)
                || (transfer.ownerDelegatedUserId !== null && transfer.ownerDelegatedUserId === Number($("#hdUserId").val())
                    && delegatedUserId === transfer.ownerUserId && gSelf.model.delegationId !== null)
                || (transfer.ownerUserId !== null && delegatedUserId === transfer.ownerUserId && gSelf.model.delegationId !== null))
            {
                lockedByMe = true;
            }
            var htmlIcons = "";
            if (transfer.importanceId)
            {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var j = 0; j < importances.length; j++)
                {
                    if (importances[j].id === transfer.importanceId)
                    {
                        htmlIcons += "<i class='fa fa-exclamation fa-lg mr-sm' title='" + importances[j].text + "' style='color:" + importances[j].color + "'></i>";
                    }
                }
            }
            if (transfer.isOverDue)
            {
                htmlIcons += "<i class='fa fa-clock-o fa-lg text-danger mr-sm' title='" + Resources.OverDue + "'></i>";
            }
            var categories = new Categories().get(window.language, gSelf.model.delegationId);
            let category = $.grep(categories, function (e)
            {
                return e.id === transfer.categoryId;
            });
            if (category[0] && category[0].isBroadcast)
            {
                htmlIcons += "<i class='fa fa-bullhorn text-primary mr-sm'  title='" + Resources.Broadcast + "'></i>";
            } else if (transfer.cced)
            {
                htmlIcons += "<i class='fa fa-cc text-warning mr-sm'  title='" + Resources.CarbonCopy + "'></i>";
            }
            if (transfer.sentToStructure && !transfer.isLocked && !transfer.cced)
            {
                htmlIcons += "<button class='btn btn-xs btn-primary mr-sm lockIcon' style='padding: 0px 3px;' title='" + Resources.Edit + "'><i class='fa fa-edit'></i></button>";
            }
            var lockedByUser = transfer.ownerUserId === Number($("#hdUserId").val()) ? Resources.You : transfer.lockedBy;
            var lockedBy = transfer.lockedByDelegatedUser !== '' ? transfer.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + transfer.lockedBy : lockedByUser;
            var titleLock = Resources.LockedBy + ": " + lockedBy + "\n" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(transfer.lockedDate, null, window.CalendarType);
            if (transfer.sentToStructure && lockedByMe)
            {
                htmlIcons += "<button class='btn btn-xs btn-success mr-sm unlockIcon' style='padding: 0px 3px;' title='" + titleLock + "'><i class='fa fa-unlock fa-white'></i></button>";
            } else if (transfer.isLocked && !transfer.cced)
            {
                htmlIcons += "<i class='fa fa-lock fa-lg text-danger mr-sm' title='" + titleLock + "'></i>";
            }
            var from = transfer.fromStructure !== "" ? transfer.fromStructure + '/' + transfer.fromUser : transfer.fromUser;
            htmlLi += '<li class="' + liClass + '">';
            htmlLi += '<div class="mdl-container">';
            htmlLi += '<div id="leftbox" class="pull-left">';
            htmlLi += '<div class="inside_color_line pull_left"></div>';
            htmlLi += '<input data-id=' + transfer.id + ' data-categoryid=' + transfer.categoryId + ' data-cced=' + transfer.cced + ' data-read=' + transfer.isRead +
                ' data-lockedbyme=' + lockedByMe + ' data-senttouser=' + transfer.sentToUser +
                ' type="checkbox" class="pointer m-sm ml-10px" onclick="event.stopPropagation();">';
            htmlLi += "<input type='hidden' data-id=" + transfer.id + " value='" + JSON.stringify(transfer) + "'/>";
            htmlLi += '</div>';
            htmlLi += '<div id="middlebox" class="mdl-text-msg pull-left pl-sm">';
            htmlLi += '<span class="mdl-span light_grey_color dark_grey_color" title="' + from + '">' + from + '</span>';
            htmlLi += '<span class="mdl-span light_grey_color" title="' + transfer.referenceNumber + '">' + transfer.referenceNumber + '</span>';
            htmlLi += '<span class="mdl-span light_grey_color" title="' + transfer.subject + '">' + transfer.subject + '</span>';
            htmlLi += '</div>';
            htmlLi += '<div id="rightbox" class="pull-left text-right"><div class="mdl-time mr-sm" title="' + Resources.TransferDate + '">' + dateFormat(transfer.transferDate) + '</div>';
            if (htmlIcons !== "")
            {
                htmlLi += '<div class="mdl-action">' + htmlIcons + '</div>';
            }
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</li>';
            var li = $($("input[data-id='" + id + "']").parents("li")[0]);
            li.after(htmlLi);
            li.fadeOut().remove();
            $("input[data-id='" + id + "']").prop('checked', true);
        }
    });
}
function refreshDraftItem(id)
{
    Common.ajaxGet('/Document/GetDraftDocument', { id: id }, function (document)
    {
        if (document)
        {
            var html = '';
            var htmlIcons = "";
            if (document.importanceId)
            {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var j = 0; j < importances.length; j++)
                {
                    if (importances[j].id === document.importanceId)
                    {
                        htmlIcons += "<i class='fa fa-exclamation fa-lg mr-sm' title='" + importances[j].text + "' style='color:" + importances[j].color + "'></i>";
                    }
                }
            }
            html += '<li class="mdl-li active">';
            html += '<div class="mdl-container">';
            html += '<div id="leftbox" class="pull-left">';
            html += '<input data-id=' + document.id + ' data-categoryid=' + document.categoryId + ' type="checkbox" class="pointer m-sm ml-10px" onclick="event.stopPropagation();">';
            html += "<input type='hidden' data-id=" + document.id + " value='" + JSON.stringify(document) + "'/>";
            html += '</div>';
            html += '<div id="middlebox" class="mdl-text-msg pull-left pl-sm">';
            html += '<span class="mdl-span light_grey_color dark_grey_color" title="' + (document.sendingEntity || "") + '">' + (document.sendingEntity || "") + '</span>';
            html += '<span class="mdl-span light_grey_color" title="' + (document.referenceNumber || "") + '">' + (document.referenceNumber || "") + '</span>';
            html += '<span class="mdl-span light_grey_color" title="' + (document.subject || "") + '">' + (document.subject || "") + '</span>';
            html += '</div>';
            html += '<div id="rightbox" class="pull-left text-right"><div class="mdl-time mr-sm" title="' + Resources.CreatedDate + '">' + dateFormat(document.createdDate) + '</div>';
            if (htmlIcons !== "")
            {
                html += '<div class="mdl-action">' + htmlIcons + '</div>';
            }
            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '</li>';
            var li = $($("input[data-id='" + id + "']").parents("li")[0]);
            li.after(html);
            li.fadeOut().remove();
            $("input[data-id='" + id + "']").prop('checked', true);
        }
    });
}
function dateFormat(dateText)
{
    var dateFull = dateText.split(" ")[0].split("/");
    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth() + 1; //January is 0!
    var yy = today.getFullYear();
    var time = DateConverter.toHijriFormated(dateText.split(" ")[0], null, window.CalendarType);
    if (parseInt(dateFull[0]) === dd && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy)
    {
        time = "";
        var timeSeparator = ":";
        var hours = parseInt(dateText.split(" ")[1].split(":")[0]);
        var amPm = Resources.AM;
        if (hours > 12)
        {
            time += (hours - 12) + timeSeparator;
            amPm = Resources.PM;
        } else if (hours === 12)
        {
            time += "12" + timeSeparator;
            amPm = Resources.PM;
        } else
        {
            time += (hours < 10 ? '0' : '') + hours + timeSeparator;
            amPm = Resources.AM;
        }
        var minutes = parseInt(dateText.split(" ")[1].split(":")[1]);
        minutes = (minutes < 10 ? '0' : '') + minutes;
        time += minutes + " " + amPm;
    } else if (parseInt(dateFull[0]) === (dd - 1) && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy)
    {
        time = Resources.Yesterday;
    }
    return time;
}
var gOwnerUserId;
function getTransferDetail(transferId, documentModel, gSelf)
{
    gLocked = false;
    var params = { "id": transferId };
    if (documentModel.delegationId !== null)
    {
        params.delegationId = documentModel.delegationId;
    }
    Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data)
    {
        var wrapper = $("#" + gSelf.model.ComponentId + '_myTransfer');
        var model = new MyTransfer.MyTransfer();
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        if (!documentModel.readonly)
        {
            documentModel.readonly = data.viewMode;
        }
        model.isCCed = data.cced;
        model.VoiceNote = data.voiceNote
        model.readonly = documentModel.readonly;
        model.transferId = transferId;
        model.delegationId = documentModel.delegationId;
        model.sendingEntity = data.sendingEntity;
        model.receivingEntity = data.receivingEntity;
        model.subject = data.subject;
        model.fromStructure = data.fromStructure;
        model.fromUser = data.fromUser;
        model.toStructure = data.toStructure;
        model.toUser = data.toUser;
        model.purpose = data.purpose;
        model.createdDate = data.createdDate;
        model.dueDate = data.dueDate;
        model.openedDate = data.openedDate;
        model.closedDate = data.closedDate;
        model.instruction = data.instruction;
        model.privacyId = data.privacyId;
        model.fromStructureId = data.fromStructureId;
        model.documentId = gSelf.model.documentId;
        model.receivingEntityId = data.receivingEntityId;
        model.ownerUserId = data.ownerUserId;
        model.instruction = data.instruction;
        model.toStructureId = data.toStructureId;
        model.replyToEntity = [{ id: data.fromStructureId, text: data.fromStructure }];
        model.sentToUser = data.sentToUser;
        model.withViewer = window.OpenCorrespondenceMode === CorrespondenceMode.WithViewer;
        model.fromSent = documentModel.fromSent;
        model.parentComponentId = gSelf.model.ComponentId;
        model.categoryId = documentModel.categoryId;
        model.fromVip = true;
        model.nodeId = gSelf.model.nodeId;
        model.fromInbox = documentModel.fromInbox;
        model.closedTransfer = data.closedDate ? true : false;
        model.forSignature = data.forSignature;
        model.workflowStepId = data.workflowStepId;
        model.initiatorUser = data.initiatorUser;
        model.isWorkflowReturned = data.isWorkflowReturned;
        model.MeetingAgenda = (documentModel.categoryId == window.MeetingAgendaId) ? true : false;

        model.hasReferenceNumber = data.hasReferenceNumber;
        model.hasAttachments = data.hasAttachments;
        model.hasUserCofigureSignature = data.hasUserCofigureSignature;
        model.nextStepUserName = data.nextStepUserName;
        model.allowSign = data.allowSign;
        model.isSigned = data.isSigned;
        var currentCategoryModel = new CategoryModel().findFullById(documentModel.categoryId);
        if (typeof currentCategoryModel !== 'undefined' && currentCategoryModel !== "" && currentCategoryModel !== null)
        {
            if (currentCategoryModel.basicAttribute !== "" && currentCategoryModel.basicAttribute !== null)
            {
                let basicAttributes = JSON.parse(currentCategoryModel.basicAttribute);
                if (basicAttributes.length > 0)
                {
                    let receivingEntityObj = $.grep(basicAttributes, function (e)
                    {
                        return e.Name === "ReceivingEntity";
                    });
                    if (receivingEntityObj[0].BroadcastReceivingEntity && receivingEntityObj[0].Type === "internal")
                    {
                        model.isInternalBroadcast = true;
                        model.isBroadcast = true;
                    } else if (receivingEntityObj[0].BroadcastReceivingEntity)
                    {
                        model.isBroadcast = true;
                    }
                }
            }
        }
        var myTransferView = new MyTransfer.MyTransferView(wrapper, model);
        myTransferView.render();
        let target = "#" + gSelf.model.ComponentId + '_myTransfer';
        let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
        SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="myTaskActions"]').attr('id'), true);
        SecurityMatrix.InitTabContextMenu(actions);
        gOwnerUserId = data.ownerUserId;
    }, null, null, null, false);
}
function getVisualTracking(delegationId, gSelf)
{
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_visualTracking');
    var model = new VisualTracking.VisualTracking();
    model.delegationId = delegationId;
    model.documentId = gSelf.model.documentId;
    var documentView = new VisualTracking.VisualTrackingView(wrapper, model);
    documentView.render();
    if (window.OpenCorrespondenceMode !== "OpenCorrespondenceDefault")
    {
        $("#" + model.ComponentId + "_trackingChart").css("height", "353px");
    }
    let target = "#" + gSelf.model.ComponentId + "_visualTracking";
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
    SecurityMatrix.InitTabContextMenu(actions);

}
function getNoteByTask(transferId, documentId, readonly, gSelf)
{
    gLocked = false;
    var model = new NoteList.Note();
    model.transferId = transferId;
    model.documentId = documentId;
    model.readOnly = readonly;
    model.delegationId = gSelf.model.delegationId;
    var wrapper = $("#" + gSelf.model.ComponentId + '_notes');
    var noteView = new NoteList.NoteView(wrapper, model);
    noteView.render();
    let target = "#" + gSelf.model.ComponentId + "_notes";
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="noteToolbarContainer"]').attr('id'), true);
    SecurityMatrix.InitTabContextMenu(actions);
}
function getNonArchivedAttachmentsByTask(transferId, documentId, readonly, gSelf)
{
    gLocked = false;
    var model = new NonArchivedAttachmentList.NonArchivedAttachments();
    model.transferId = transferId;
    model.documentId = documentId;
    model.readOnly = readonly;
    model.delegationId = gSelf.model.delegationId;
    var wrapper = $("#" + gSelf.model.ComponentId + '_nonArchivedAttachments');//todo
    var nonArchivedAttachmentList = new NonArchivedAttachmentList.NonArchivedAttachmentsView(wrapper, model);
    nonArchivedAttachmentList.render();
    let target = "#" + gSelf.model.ComponentId + "_nonArchivedAttachments";
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="nonArchivedAttachmentsToolbarConatiner"]').attr('id'), true);//todo
    SecurityMatrix.InitTabContextMenu(actions);

}
function getLinkedCorrespondences(transferId, documentId, delegationId, readonly, gSelf)
{
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_linkedDocument');
    var model = new LinkedCorrespondence.LinkedCorrespondence();
    model.transferId = transferId;
    model.documentId = documentId;
    model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    model.categories = new Categories().get(window.language);
    model.delegationId = delegationId;
    model.readOnly = readonly;
    model.delegationId = gSelf.model.delegationId;
    var documentView = new LinkedCorrespondence.LinkedCorrespondenceView(wrapper, model);
    documentView.render();
    let target = "#" + gSelf.model.ComponentId + "_linkedDocument";
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="linkedDocumentToolbarContainer"]').attr('id'), true);
    SecurityMatrix.InitTabContextMenu(actions);
}
function getAttachments(transferId, documentId, delegationId, readOnly, categoryId, documentModel, gSelf)
{
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_attachments');
    var model = new Attachment.Attachment();
    model.transferId = transferId;
    model.documentId = documentId;
    model.readOnly = readOnly;
    model.delegationId = delegationId;
    model.categoryId = categoryId;
    model.fromInbox = documentModel.fromInbox;
    model.fromDraft = documentModel.fromDraft;
    model.isCced = documentModel.isCced;
    if (typeof gOwnerUserId === 'undefined')
    {
        Common.ajaxGet("/Transfer/GetTransferOwnerId", { "id": transferId, 'delegationId': model.delegationId, }, function (data)
        {
            gOwnerUserId = data;
        }, null, null, null, false);
    }
    model.ownerUserId = gOwnerUserId;
    model.parentComponentId = gSelf.model.ComponentId;
    model.openCorrespondenceDefault = true;
    model.fromVip = true;
    var attachmentView = new Attachment.AttachmentView(wrapper, model);
    attachmentView.render();
    let target = "#" + gSelf.model.ComponentId + '_attachments';
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="attachmentsActions"]').attr('id'), false);
    SecurityMatrix.InitTabContextMenu(actions);
}
function getActivityLog(documentId, delegationId, gSelf)
{
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_activityLog');
    var model = new ActivityLogTimeline.ActivityLogTimeline();
    model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    model.documentId = documentId;
    model.delegationId = delegationId;
    var documentView = new ActivityLogTimeline.ActivityLogTimelineView(wrapper, model);
    documentView.render();
    let target = "#" + gSelf.model.ComponentId + "_activityLog";
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
    SecurityMatrix.InitTabContextMenu(actions);
}
function getTransfersHistory(transferId, documentId, sentToUser, delegationId, gSelf)
{
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_transferHistory');
    var model = new TransferHistory.TransferHistory();
    model.transferId = transferId;
    model.documentId = documentId;
    model.delegationId = delegationId;
    model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    model.sentToUser = sentToUser;
    var TransferHistoryView = new TransferHistory.TransferHistoryView(wrapper, model);
    TransferHistoryView.render();
    let target = "#" + gSelf.model.ComponentId + '_transferHistory';
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
    SecurityMatrix.InitTabContextMenu(actions);
}
function executeAjaxCall(div, url, openInIframe)
{
    if (!openInIframe)
    {
        Common.ajaxGet(url, null, function (response)
        {
            let wrapper = null;
            wrapper = document.getElementById(div);
            wrapper.innerHTML = "";
            wrapper.innerHTML = response;
        });
    } else
    {
        let wrapper = null;
        wrapper = document.getElementById(div + "Iframe");
        wrapper.src = url;
    }
}
function initTabActions(tabs, tabsActions, readonly, divId)
{
    let tab = $.grep(tabs, function (element)
    {
        return element.Name.includes(divId);
    })[0];
    if (tab !== null && typeof tab !== "undefined")
    {
        let actions = $.grep(tabsActions[Number(tab.Id)], function (element)
        {
            return readonly ? element.ShowInReadMode === true : element.ShowInEditMode === true;
        });
        return actions;
    }
    return [];
}
function getDocument(data, readonly, gSelf)
{
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_documentMetadata');
    var model = new Document.Document();
    model.id = gSelf.model.documentId;
    model.categoryId = data.categoryId;
    model.categoryName = data.categoryName;
    model.referenceNumber = data.referenceNumber;
    model.subject = data.subject;
    model.basicAttributes = data.basicAttributes !== null && data.basicAttributes !== "" ? JSON.parse(data.basicAttributes) : [];
    model.customAttributes = data.customAttributes !== null && data.customAttributes !== "" ? JSON.parse(data.customAttributes) : null;
    model.customAttributesTranslation = data.customAttributesTranslation !== null && data.customAttributesTranslation !== "" ? JSON.parse(data.customAttributesTranslation) : null;
    model.formData = data.formData !== null && data.formData !== "" ? JSON.parse(data.formData) : [];
    model.receivers = data.receivers;
    model.sendingEntityId = data.sendingEntityId;
    model.dueDate = data.dueDate;
    model.priorityId = data.priorityId;
    model.privacyId = data.privacyId;
    model.carbonCopy = data.carbonCopy;
    model.importanceId = data.importanceId;
    model.classificationId = data.classificationId;
    model.sendingEntity = data.sendingEntity;
    model.receivingEntities = data.receivingEntities;
    model.carbonCopies = data.carbonCopies;
    model.classification = data.classification;
    model.documentType = data.documentType;
    model.readonly = readonly && data.enableEdit ? !data.enableEdit : readonly;
    model.delegationId = data.delegationId;
    model.userStructures = new IdentityService().getUserStructures(window.language);
    model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
    model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
    model.importances = new CoreComponents.Lookup.Importances().get(window.language);
    model.createdByStructureId = data.createdByStructureId;
    model.body = data.body;
    model.fromVip = true;
    model.callback = gSelf.callback;
    model.externalReferenceNumber = data.externalReferenceNumber;
    model.keyword = data.keyword;
    model.enableEdit = data.enableEdit;
    model.transferId = gSelf.model.id;
    model.senderPerson = data.senderPerson;
    model.receiverPerson = data.receiverPerson;
    model.isExternalReceiver = data.isExternalReceiver;
    model.isExternalSender = data.isExternalSender;
    model.byTemplate = data.byTemplate;
    var documentView = new Document.DocumentView(wrapper, model);
    let url = readonly ? '/Document/Edit' : '/Document/Save';
    setTimeout(function ()
    {
        $('#txtCustomAttributeSubject').focus();
    }, 500);
    documentView.render({
        url: url,
        params: {
            'CategoryId': data.categoryId,
            'CategoryName': data.categoryName,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (response)
        {
            if (readonly)
            {
                refreshInboxItem(gSelf.model.id, gSelf);
            } else
            {
                refreshDraftItem(gSelf.model.documentId);
            }
            //GridCommon.RefreshCurrentPage("grdDraftItems", false);
        }
    }, {
        url: '/Document/Send',
        params: {
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (response)
        {
            TreeNode.addToNodeCount(TreeNode.MyRequests, 1, 1);
            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
            Common.alertMsg(Common.format(Resources.ReferenceNumberMsg, response), function ()
            {
                //var nodeId = $('[data-inherit="' + TreeNode.MyRequests + '"]').first().data("id");
                //if (nodeId !== undefined && $.isNumeric(nodeId))
                //{
                //    window.location.href = '#myrequests/' + nodeId;
                //} else
                //{
                //    window.location.href = '/';
                //}

                if (readonly) {
                    refreshInboxItem(gSelf.model.id, gSelf);
                } else {
                    refreshDraftItem(gSelf.model.documentId);
                }
            });
        }
    });
    let target = "#" + gSelf.model.ComponentId + '_documentMetadata';
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
    SecurityMatrix.InitTabContextMenu(actions);
}
function getDocumentByTransfer(transferId, delegationId, gSelf)
{
    var params = { id: transferId };
    if (delegationId !== null)
    {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetDocumentByTransferId', params, function (data)
    {
        getDocument(data, true, gSelf);
    }, null, true);

}
function checkReloadTab(tabs, divId, customId)
{
    divId = divId.split("_")[2];
    let tab = $.grep(tabs, function (element)
    {
        return element.Name.includes(divId) || element.Id.toString() === customId;
    })[0];
    if (tab !== null && typeof tab !== "undefined")
    {
        return tab.Reload === true;
    }
    return false;
}
function loadCustomTab(model, documentId, transferId, id, target, typeId, jsfunction, url, openiframe)
{
   
    if (transferId == "") {
        transferId = null;
    }
    if (typeof typeId !== "undefined" && typeId !== null)
    {
        switch (typeId.toString())
        {
            case TabType.Url:
                url = url.replace("$documentId", documentId).replace("$transferId", transferId);
                executeAjaxCall(target.replace("#", ""), url, openiframe);
                break;
            case TabType.JavascriptFunction:
                var customactions = $.grep(model.tabsActions[id], function (element)
                {
                    return !model.readonly ? element.ShowInEditMode !== false : element.ShowInReadMode !== false;
                });
                let actions = undefined;
                for (var i = 0; i < model.tabsActions[id].length; i++) {
                    if (i == 0) {
                        actions = model.tabsActions[id][i].Name;

                    }
                    else {
                        actions += "_" + model.tabsActions[id][i].Name;
                    }

                }
                jsfunction = jsfunction

                    .replace("$documentId", documentId)
                    .replace("$transferId", transferId)
                    .replace("$customactions", JSON.stringify(customactions))
                    .replace("$readOnly", model.readonly)
                    .replace("$delegationId", model.delegationId)
                    .replace("$tabId", id)
                    .replace("$ComponentId", "'" + model.ComponentId + "'")
                    .replace("$categoryId", model.categoryId)
                    .replace("$fromSent", model.fromSent)
                    .replace("$fromInbox", model.fromInbox)
                    .replace("$fromRejectedDocument", model.fromRejectedDocument)
                    .replace("$isCced", model.isCced)
                    .replace("$fromDraft", model.fromDraft)
                    .replace("$DocumentIsCompleted", model.DocumentIsCompleted)
                    .replace("$sentToUser", model.sentToUser)
                    .replace("$parentLinkedDocumentId", model.parentLinkedDocumentId)
                    .replace("$actionName", "'" + actions + "'")
                    .replace("$modalComponentId", "'" + model.parentComponentId + "'")
                    .replace("$nodeId", "'" + model.nodeId + "'")
                    .replace("$fromFollowUp", false)
                    .replace("$fromManageCorrespondance", false)
                    .replace("$resendModel", JSON.stringify(model.resendData))
                    .replace("$fromSearch", model.fromSearch)
                    .replace("$attachmentVersion", model.attachmentVersion != null ? "'" + model.attachmentVersion + "'" : null)
                eval(jsfunction);
                break;
        }
        gLocked = false;
    }
    $(".m-xs").removeClass("btn-warning")
    $(".m-xs").addClass("btn-secondary")
    $(".m-xs").addClass("lightGreyBorder")
    $(".m-xs").removeClass("m-xs");
}
var gLocked = false;

function loadAttachmentPanel(model) {
    if (model.attachmentCount <= 1)
        return;

    $("#" + model.ComponentId + "_attachmentsPanel").show();

    //get attachments and list them
    Common.ajaxGet('/Attachment/List', { documentId: model.documentId }, function (result) {
        var arrFiles = flattenTree(result);
        var buttons = "";
        arrFiles.forEach(file => {
            var attachmentid = file.id.split("_")[1]
            var cssclass = file.parentId == "folder_originalMail" ? "btn-primary" : "";
            buttons += `<button type="button" class="btn btn-xs mr-lg ${cssclass}" title="${file.title}" data-attachmentid="${attachmentid}"><i class="${file.icon}"></i></button>`;
        });
        $("#" + model.ComponentId + "_attachmentsPanel").html(buttons);
        $("#" + model.ComponentId + "_attachmentsPanel").on("click", "button", function () {
            var isCustomMode = window.ViewerMode === '1' ? "false" : "true";

            var attachment = $(this).data("attachmentid")
            var isDraft = model.id === null || typeof model.id === 'undefined' ? true : false;
            var viewerUrl = window.ViewerUrl + "/templates?documentId=" + attachment + "&language=" + window.language + "&token=" +
                window.IdentityAccessToken + "&version=autocheck&ctsDocumentId=" + model.documentId +
                    "&ctsTransferId=" + model.id + "&delegationId=" + model.delegationId + "&isDraft=" + isDraft + "&isCustomMode=" + isCustomMode;
            if (window.viewOnlyMode == 'full' && model.attachmentVersion !== undefined && model.attachmentVersion !== null && model.attachmentVersion.length > 0) {
                viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + model.attachmentVersion)
            }
            if (model.readonly || model.attachmentIslocked || model.fromRejectedDocument) {
                viewerUrl = viewerUrl + "&viewermode=view";
                if (window.viewOnlyMode == 'read')
                    viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + model.attachmentVersion)
            }
            $("#" + model.ComponentId + "_" + "viewerContainer").removeClass("waitingBackground");
            $("#" + model.ComponentId + "_" + "viewerFrame").attr("src", viewerUrl);

            $("#" + model.ComponentId + "_attachmentsPanel .btn").removeClass("btn-primary");
            $(this).addClass("btn-primary");
        })
    });
}

function flattenTree(tree) {
    let flatArray = [];

    function recurse(items) {
        items.forEach(item => {
            if (item.type === "2") {
                const { children, ...rest } = item;
                flatArray.push(rest);
            }

            if (Array.isArray(item.children)) {
                recurse(item.children);
            }
        });
    }

    recurse(tree);
    return flatArray;
}

class VipDocumentDetailsView extends Intalio.ViewAppend
{
    constructor(element, model, callback)
    {
        super(element, "vipdocumentdetails", model);
        this.callback = callback;
    }
    render()
    {
        var gSelf = this;
        var model = this.model;
        var transferId = model.id;
        var documentId = model.documentId;
        gLocked = false;
    
        model.tabs.forEach(function (tab) {
            if (tab.Name == "Notes" && model.attachmentCount > 0) {
                document.getElementById("attachmentCountTotal").innerHTML = `<div class='label label-warning pull-right flex-center circular--portrait'>${model.attachmentCount}</div>`;

            }
            else if (tab.Name == "Attachments" && model.noteCount > 0) {
                
                document.getElementById("noteCountTotal").innerHTML = `<div class='label label-warning pull-right flex-center circular--portrait'>${model.noteCount}</div>`;

            }
            else if (tab.Name == "Linked correspondence" && model.linkedCount > 0 ) {
                document.getElementById("linkedCountTotal").innerHTML = `<div class='label label-warning pull-right flex-center circular--portrait'>${model.linkedCount}</div>`;

            }
        });
        
        if (model.attachmentCount > 0) {
            loadAttachmentPanel(model);
        }
        if (model.id === null || typeof model.id === 'undefined') {
            $("#" + model.ComponentId + "_taskPanel").hide();
            $("#" + model.ComponentId + '_btnMaximizeDocument').hide();
        }

        if (window.FollowUpCategory == this.model.categoryId) {
            $("#" + model.ComponentId + "_taskPanel").hide();
        }

        $("#" + model.ComponentId + '_btnMaximizeDocument').on('click', function () {
            var wrapper = $(".modal-documents");
            var linkedCorrespondenceModel = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocument();
            linkedCorrespondenceModel.reference = model.referenceNumber;
            linkedCorrespondenceModel.subject = $("#" + model.ComponentId + "_subjectTaskPanel").html();
            linkedCorrespondenceModel.from = $("#" + model.ComponentId + "_fromTaskPanel").html();
            linkedCorrespondenceModel.to = $("#" + model.ComponentId + "_toTaskPanel").html();
            linkedCorrespondenceModel.transferDate = $("#" + model.ComponentId + "_transferDateTaskPanel").html();
            //linkedCorrespondenceModel.registerDate = $("#" + model.ComponentId + "_registerDateTaskPanel").html();
            //linkedCorrespondenceModel.registeredBy = $("#" + model.ComponentId + "_registerByTaskPanel").html();
            //subject, from, to, transferDate, registerDate, registeredBy
            var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
            linkedCorrespondenceDocument.render();

            wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

            var newModel = new DocumentDetails.DocumentDetails();

            newModel.categoryId = model.categoryId;
            newModel.delegationId = model.delegationId;
            newModel.id = model.id;
            newModel.documentId = model.documentId;
            newModel.referenceNumber = model.referenceNumber;
            newModel.categoryName = model.categoryName;
            newModel.statusId = model.statusId;
            newModel.createdByUser = model.createdByUser;
            newModel.readonly = model.readonly;
            newModel.attachmentId = model.attachmentId;
            newModel.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            newModel.showMyTransfer = true;
            newModel.fromInbox = true;
            newModel.isCced = model.isCced;
            newModel.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
            newModel.isModal = true;
            newModel.showBackButton = false;
            newModel.tabs = model.tabs;
            newModel.tabsWithStatic = model.tabsWithStatic;
            newModel.tabsActions = model.tabsActions;
            newModel.fromVip = false;
            newModel.fromSent = model.fromSent;
            wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);
            
            var view = new DocumentDetails.DocumentDetailsView(wrapper, newModel);
            view.render();

            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');

            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                if ($(this).data("remove") != true)
                    return;
                $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                swal.close();
                if ($(".modal-documents").children().length > 0) {
                    $('body').addClass('modal-open');
                }
            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

        });
        
        if (window.HideAuditTrail === "True")
        {
            $("a[href='#" + gSelf.model.ComponentId + "_activityLog']").parent().hide();
        }
        if (model.isModal)
        {
            $(gSelf.refs['documentDetailsContainerDiv']).find("h3").hide();
        }
        $(".hidden-xs").click(function ()
        {
            //$(".tabDocumentDetails").scrollingTabs('refresh');
        });
        $(".content-wrapper .toRemove").remove()
        var contentWrapperBtns = "<div class='btn-group pull-right toRemove'>";
        var flipHorizental = "";
        if (language.toLowerCase() === "ar") {
            flipHorizental = "fa-flip-horizontal";
        }
        //contentWrapperBtns += '<div id="' + model.ComponentId + '_ActionsPanel" class="btn-group"> </div>';
        
        contentWrapperBtns += "</div>";
        $(".content-wrapper").prepend(contentWrapperBtns);



        if (!model.showPreview)
        {
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1)').addClass("active");
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(1)').addClass("active in");
            var documentId = $(gSelf.refs['hdDocumentId']).val();
            var transferId = $(gSelf.refs['hdId']).val();
            var id = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("id");
            var target = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').attr("href");//activated tab
            var typeId = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("typeid");
            var jsfunction = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("function");
            var url = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("url");
            var openiframe = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("openiframe");

            $(target).addClass("active in");
            loadCustomTab(model, documentId, transferId, id, target, typeId, jsfunction, url, openiframe, gSelf);
        } else
        {
            var documentId = $(gSelf.refs['hdDocumentId']).val();
            var transferId = $(gSelf.refs['hdId']).val();
            var id = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(2) a').data("id");
            var target = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(2) a').attr("href");//activated tab
            var typeId = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(2) a').data("typeid");
            var jsfunction = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(2) a').data("function");
            var url = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(2) a').data("url");
            var openiframe = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(2) a').data("openiframe");

            loadCustomTab(model, documentId, transferId, id, target, typeId, jsfunction, url, openiframe, gSelf);

            var isCustomMode = window.ViewerMode === '1' ? "false" : "true";

            var isDraft = model.id === null || typeof model.id === 'undefined' ? true : false;
            var viewerUrl = window.ViewerUrl + "/templates?documentId=" + model.attachmentId + "&language=" + window.language + "&token=" +
                window.IdentityAccessToken + "&version=autocheck&ctsDocumentId=" + model.documentId +
                "&ctsTransferId=" + model.id + "&delegationId=" + model.delegationId + "&isDraft=" + isDraft + "&isCustomMode=" + isCustomMode;
            if (window.viewOnlyMode == 'full' && model.attachmentVersion !== undefined && model.attachmentVersion !== null && model.attachmentVersion.length > 0) {
                viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + model.attachmentVersion)
            }
            if (model.readonly || model.attachmentIslocked || model.fromRejectedDocument) {
                viewerUrl = viewerUrl + "&viewermode=view";
                if (window.viewOnlyMode == 'read')
                    viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + model.attachmentVersion)
            }
            if (model.attachmentId == null) {
                $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(2)').addClass("active in");
                $("#" + model.ComponentId + "_tab1").addClass("active in");
                $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(1)').removeClass("active in");
                $("#" + model.ComponentId + "_preview").removeClass("active in");
                $("#" + model.ComponentId + "_preview").addClass("waitingBackground");
            }
            else {
                $("#" + gSelf.model.ComponentId + "_" + "viewerContainer").removeClass("waitingBackground");
                $("#" + gSelf.model.ComponentId + "_" + "viewerFrame").attr("src", viewerUrl);
            }

        }
        if (model.statusId !== 0)
        {
            var status = new CoreComponents.Lookup.Statuses().findById(model.statusId, window.language);
            if (status)
            {
                var statusDiv = "<span class='label mr-sm' style='font-size:13px;padding:6px;background-color:" +
                    (status.color !== null ? status.color : "#27c24c") + "'>" + status.text + "</span>";
                $(gSelf.refs['documentDetailsContainerDiv']).find(".content-heading").append(statusDiv);
            }

        }
        if (model.showBackButton)
        {
            var contentWrapperBtns = "<div class='btn-group pull-right toRemove'>";
            var flipHorizental = "";
            if (language.toLowerCase() === "ar")
            {
                flipHorizental = "fa-flip-horizontal";
            }
            contentWrapperBtns += "<button type='button' class='btn-back btn btn-warning toRemove'><em class='fa fa-reply " + flipHorizental + " mr-sm'></em>" + Resources.Back + "</button>";
            if (!model.fromDraft)
            {
                contentWrapperBtns += "<button type='button' class='btn-export btn btn-success toRemove'><em class='fa fa-cloud-download mr-sm'></em>" + Resources.Export + "</button>";
            }
            contentWrapperBtns += "</div>";
            $(".content-wrapper").prepend(contentWrapperBtns);
        }

        //$(gSelf.refs['tabDocumentDetails']).scrollingTabs({ enableRtlSupport: true });
        //$(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li').click(function ()
        //{
        //    var tab = $(this).find("a[role='tab']").attr("href");
        //    var reloadTab = false;
        //    if (tab !== undefined)
        //    {
        //        var tabId = $(this).find("a[role='tab']").attr("data-id");
        //        reloadTab = checkReloadTab(model.tabsWithStatic, tab.split("#")[1], tabId);
        //    }
        //    var loaded = $(this).data("loaded");
        //    if (!loaded)
        //    {
        //        if (!gLocked)
        //        {
        //            gLocked = true;
        //            try
        //            {
        //                var count = 1;
        //                if (model.showMyTransfer)
        //                {
        //                    count = 2;
        //                }
        //                if (!reloadTab)
        //                {
        //                    $(this).attr("data-loaded", true);
        //                    $(this).attr("data-customloaded", true);
        //                }
        //                if (!model.showPreview)
        //                {
        //                    if (model.fromDraft)
        //                    {
        //                        if ($(this).is(':nth-child(' + (count + 1) + ')'))
        //                        {
        //                            getAttachments(transferId, documentId, model.delegationId, model.readonly, model.categoryId, model, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 2) + ')'))
        //                        {
        //                            getNoteByTask(transferId, documentId, model.readonly, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 3) + ')'))
        //                        {
        //                            getLinkedCorrespondences(transferId, documentId, model.delegationId, model.readonly, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 4) + ')'))
        //                        {
        //                            getNonArchivedAttachmentsByTask(transferId, documentId, model.readonly, gSelf);
        //                        } else if ($(this).index() < 5)
        //                        {
        //                            if (!model.showMyTransfer)
        //                            {
        //                                getDocument(model.documentModel, model.readonly, gSelf);
        //                            } else
        //                            {
        //                                getTransferDetail(transferId, model, gSelf);
        //                            }
        //                        }
        //                    } else
        //                    {
        //                        if ($(this).is(':nth-child(' + count + ')'))
        //                        {
        //                            getDocumentByTransfer(transferId, model.delegationId, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 1) + ')'))
        //                        {
        //                            getAttachments(transferId, documentId, model.delegationId, model.readonly, model.categoryId, model, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 2) + ')'))
        //                        {
        //                            getNoteByTask(transferId, documentId, model.readonly, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 3) + ')'))
        //                        {
        //                            getLinkedCorrespondences(transferId, documentId, model.delegationId, model.readonly, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 4) + ')'))
        //                        {
        //                            getNonArchivedAttachmentsByTask(transferId, documentId, model.readonly, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 5) + ')'))
        //                        {
        //                            getVisualTracking(model.delegationId, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 6) + ')'))
        //                        {
        //                            getActivityLog(documentId, model.delegationId, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 7) + ')'))
        //                        {
        //                            getTransfersHistory(transferId, documentId, model.sentToUser, model.delegationId, gSelf);
        //                        } else if ($(this).index() < 5)
        //                        {
        //                            if (!model.showMyTransfer)
        //                            {
        //                                getDocument(model.documentModel, model.readonly, gSelf);
        //                            } else
        //                            {
        //                                getTransferDetail(transferId, model, gSelf);
        //                            }
        //                        }
        //                    }
        //                } else
        //                {
        //                    if (model.fromDraft)
        //                    {
        //                        if ($(this).is(':nth-child(' + (count + 1) + ')'))
        //                        {
        //                            if (!model.showMyTransfer)
        //                            {
        //                                getDocument(model.documentModel, model.readonly, gSelf);
        //                            } else
        //                            {
        //                                getTransferDetail(transferId, model, gSelf);
        //                            }
        //                        } else if ($(this).is(':nth-child(' + (count + 2) + ')'))
        //                        {
        //                            getAttachments(transferId, documentId, model.delegationId, model.readonly, model.categoryId, model, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 3) + ')'))
        //                        {
        //                            getNoteByTask(transferId, documentId, model.readonly, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 4) + ')'))
        //                        {
        //                            getLinkedCorrespondences(transferId, documentId, model.delegationId, model.readonly, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 5) + ')'))
        //                        {
        //                            getNonArchivedAttachmentsByTask(transferId, documentId, model.readonly, gSelf);
        //                        } else if ($(this).index() < 5)
        //                        {
        //                            if (!model.showMyTransfer)
        //                            {
        //                                getDocument(model.documentModel, model.readonly, gSelf);
        //                            } else
        //                            {
        //                                getTransferDetail(transferId, model, gSelf);
        //                            }
        //                        }
        //                    } else
        //                    {
        //                        if ($(this).is(':nth-child(' + count + ')'))
        //                        {
        //                            if (!model.showMyTransfer)
        //                            {
        //                                getDocument(model.documentModel, model.readonly, gSelf);
        //                            } else
        //                            {
        //                                getTransferDetail(transferId, model, gSelf);
        //                            }
        //                        } else if ($(this).is(':nth-child(' + (count + 1) + ')'))
        //                        {
        //                            if (transferId)
        //                            {
        //                                getDocumentByTransfer(transferId, model.delegationId, gSelf);
        //                            } else
        //                            {
        //                                getDocument(model.documentModel, model.readonly, gSelf);
        //                            }
        //                        } else if ($(this).is(':nth-child(' + (count + 2) + ')'))
        //                        {
        //                            getAttachments(transferId, documentId, model.delegationId, model.readonly, model.categoryId, model, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 3) + ')'))
        //                        {
        //                            getNoteByTask(transferId, documentId, model.readonly, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 4) + ')'))
        //                        {
        //                            getLinkedCorrespondences(transferId, documentId, model.delegationId, model.readonly, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 5) + ')'))
        //                        {
        //                            getNonArchivedAttachmentsByTask(transferId, documentId, model.readonly, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 6) + ')'))
        //                        {
        //                            getVisualTracking(model.delegationId, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 7) + ')'))
        //                        {
        //                            getActivityLog(documentId, model.delegationId, gSelf);
        //                        } else if ($(this).is(':nth-child(' + (count + 8) + ')'))
        //                        {
        //                            getTransfersHistory(transferId, documentId, model.sentToUser, model.delegationId, gSelf);
        //                        } else if ($(this).index() < 5)
        //                        {
        //                            if (!model.showMyTransfer)
        //                            {
        //                                getDocument(model.documentModel, model.readonly, gSelf);
        //                            } else
        //                            {
        //                                getTransferDetail(transferId, model, gSelf);
        //                            }
        //                        }
        //                    }
        //                }
        //            } catch (e)
        //            {
        //                gLocked = false;
        //            }
        //        }
        //    }
        //    if ($(this).hasClass("active"))
        //    {
        //        var customLoaded = $(this).find("a[role='tab']").data("customloaded");
        //        if (!customLoaded)
        //        {
        //            var id = $(this).find("a[role='tab']").data("id");
        //            var target = $(this).find("a[role='tab']").attr("href");//activated tab
        //            var typeId = $(this).find("a[role='tab']").data("typeid");
        //            var jsfunction = $(this).find("a[role='tab']").data("function");
        //            var url = $(this).find("a[role='tab']").data("url");
        //            var openiframe = $(this).find("a[role='tab']").data("openiframe");
        //            loadCustomTab(model, $("#hdDocumentId").val(), $("#hdId").val(), id, target, typeId, jsfunction, url, openiframe);
        //        }
        //    }
        //});
        $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] a[data-toggle="tab"]').on('shown.bs.tab', function (e)
        {
            
            var loaded = $(e.target).data("customloaded");
            if (!loaded)
            {
                var tab = $(this).attr("href");
                var reloadTab = false;
                if (tab !== undefined)
                {
                    var tabId = $(this).attr("data-id");
                    reloadTab = checkReloadTab(model.tabsWithStatic, tab.split("#")[1], tabId);
                }
                if (!reloadTab)
                {
                    $(e.target).attr("data-customloaded", true);
                }
                var documentId = $(gSelf.refs['hdDocumentId']).val();
                var transferId = $(gSelf.refs['hdId']).val();
                var id = $(e.target).data("id");
                var target = $(e.target).attr("href");//activated tab
                var typeId = $(e.target).data("typeid");
                var jsfunction = $(e.target).data("function");
                var url = $(e.target).data("url");
                var openiframe = $(e.target).data("openiframe");
               
                
                loadCustomTab(model, documentId, transferId, id, target, typeId, jsfunction, url, openiframe);
            }
            //setTimeout(function ()
            //{
            //    $(gSelf.refs['tabDocumentDetails']).scrollingTabs('refresh');
            //}, 300);
        });
        $(window).resize(function ()
        {
            //setTimeout(function ()
            //{
            //    $(gSelf.refs['tabDocumentDetails']).scrollingTabs('refresh');
            //}, 500);
        });
    };
    remove()
    {
        $(this.refs[this.model.ComponentId]).remove();
    };
}
export default { VipDocumentDetails, VipDocumentDetailsView };