﻿import Intalio from './common.js'
import VipDocumentDetails from './vipDocumentDetails.js'
import SendTransferModal from './sendTransfer.js'
import { CategoryModel, Categories, IdentityService, DelegationUsers } from './lookup.js'
import SendToReceivingEntityIndex from './sendToReceivingEntity.js'
import Transfer from './transfer.js'
import NoteComponentIndex from './noteComponent.js'
import DocumentInbox from './inboxList.js'
class VipDocumentInbox extends Intalio.Model {
    constructor() {
        super();
        this.title = null;
        this.nodeId = null;
        this.delegationId = null;
        this.categories = null;
        this.privacies = null;
        this.fromStructureInbox = false;
    }
}
function acceptRequest(id) {

    gLocked = false;
    Common.showConfirmMsg(Resources.AcceptRequestConfirmation, function () {
        var params = { "id": id };

        Common.ajaxPost('/Transfer/AcceptRequest', params, function (response) {
            if (response && response.success) {
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                $(".withBorders-o").addClass("waitingBackground");
                $("#inboxDocumentDetailsContainer").empty();
                $($("input[data-id='" + id + "']").parents("li")[0]).fadeOut().remove();
                Common.showScreenSuccessMsg();
            } else if (response && !response.success && response.message != null) {
                Common.alertMsg(response.message);
            } else {
                Common.showScreenErrorMsg();
            }
        });
    });

}
function rejectRequest(id) {

    if (window.EnableConfirmationMessage === "True") {
        Common.showConfirmMsg(Resources.RejectRequestConfirmation, function () {
            let modalWrapper = $(".modal-window");
            let modelIndex = new NoteComponentIndex.NoteComponentIndex();
            modelIndex.callback = function (response) {
                gLocked = false;
                if (response && response.success) {
                    GridCommon.RefreshCurrentPage("grdInboxItems", true);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Custom);

                    $(".withBorders-o").addClass("waitingBackground");
                    $("#inboxDocumentDetailsContainer").empty();
                    $($("input[data-id='" + id + "']").parents("li")[0]).fadeOut().remove();
                    Common.showScreenSuccessMsg();
                } else if (response && !response.success && response.message != null) {
                    Common.alertMsg(response.message);
                } else {
                    Common.showScreenErrorMsg();
                }
            };
            let NoteComponentIndexView = new NoteComponentIndex.NoteComponentIndexView(modalWrapper, modelIndex);

            NoteComponentIndexView.render({
                url: '/Transfer/RejectRequest',
                params: {
                    "id": id
                }
            });
            modalWrapper.off("hidden.bs.modal").on("hidden.bs.modal", function () {
                gLocked = false;
            });

        });
    }
    else {
        let modalWrapper = $(".modal-window");
        let modelIndex = new NoteComponentIndex.NoteComponentIndex();
        modelIndex.callback = function (response) {
            gLocked = false;
            if (response && response.success) {
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.Custom);

                $(".withBorders-o").addClass("waitingBackground");
                $("#inboxDocumentDetailsContainer").empty();
                $($("input[data-id='" + id + "']").parents("li")[0]).fadeOut().remove();
                Common.showScreenSuccessMsg();
            } else if (response && !response.success && response.message != null) {
                Common.alertMsg(response.message);
            } else {
                Common.showScreenErrorMsg();
            }
        };
        let NoteComponentIndexView = new NoteComponentIndex.NoteComponentIndexView(modalWrapper, modelIndex);

        NoteComponentIndexView.render({
            url: '/Transfer/RejectRequest',
            params: {
                "id": id
            }
        });
        modalWrapper.off("hidden.bs.modal").on("hidden.bs.modal", function () {
            gLocked = false;
        });
    
       

    }
}
function dismissCarbonCopy(dismissIds, delegationId, allSelectedData) {
    Common.mask(document.getElementById('vipContainer'), "vipContainer-mask");
    Common.ajaxPost('/Transfer/DismissCarbonCopy',
        {
            'ids': dismissIds, 'delegationId': delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        function (result) {
            if (result != null && result.length > 0) {
                swal.close()
                let msg = "";
                for (var i = 0; i < result.length; i++) {
                    if (!result[i].updated) {
                        var transfer = $.grep(allSelectedData, function (e) {
                            return e.id === result[i].transferId;
                        });
                        if (transfer[0]) {
                            msg += "\n ○ " + transfer[0].referenceNumber;

                        } else {
                            msg += "\n ○ " + result[i].transferId;
                        }
                    }
                }
                if (msg !== "") {
                    setTimeout(function () {
                        Common.alertMsg(Resources.CannotDismissCarbonCopyWarning + msg);
                    }, 300);
                    Common.unmask("vipContainer-mask");
                    for (var i = 0; i < dismissIds.length; i++) {
                        var canDelete = false;
                        var li = $($("input[data-id='" + dismissIds[i] + "']").parents("li")[0]);
                        if (result.length > 0) {
                            if ($.grep(result, function (element, index) {
                                return element.updated && element.transferId === dismissIds[i];
                            }).length > 0) {
                                canDelete = true;
                            }
                        }
                        if (canDelete) {
                            li.fadeOut().remove();
                            if (Number(gSelectedRowId) === Number(dismissIds[i])) {
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#inboxDocumentDetailsContainer").empty();
                            }
                        }
                    }
                    Common.unmask("vipContainer-mask");
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                } else {
                    Common.unmask("vipContainer-mask");
                    Common.showScreenSuccessMsg();
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    for (var i = 0; i < dismissIds.length; i++) {
                        while ($("input[data-id='" + dismissIds[i] + "']")[0]) {
                            $($("input[data-id='" + dismissIds[i] + "']").parents("li")[0]).fadeOut().remove();
                        }
                        if (Number(gSelectedRowId) === Number(dismissIds[i])) {
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#inboxDocumentDetailsContainer").empty();
                        }
                    }
                }
            } else {
                Common.showScreenErrorMsg();
            }
        }, null, false);
}
function sendToReceivingEntity(receivingEntities, broadcastIds, delegationId, transferToType, dueDate) {
    let modalWrapper = $(".modal-window");
    let modelIndex = new SendToReceivingEntityIndex.SendToReceivingEntityIndex();
    modelIndex.purposes = new Helper().getPurpose();
    modelIndex.receivingEntities = receivingEntities;
    modelIndex.transferToType = transferToType;
    modelIndex.customAttributeDueDate = dueDate;
    modelIndex.broadcastIds = broadcastIds;
    modelIndex.fromVip = true;
    modelIndex.isBroadcast = true;
    let sendToReceivingEntityIndexView = new SendToReceivingEntityIndex.SendToReceivingEntityIndexView(modalWrapper, modelIndex, refreshInboxList);
    sendToReceivingEntityIndexView.render();
    $("#modalSendToReceivingEntity").off("shown.bs.modal");
    $('#modalSendToReceivingEntity').on('shown.bs.modal', function () {
        $("#hdSendDeligationId").val(delegationId);
        CKEDITOR.instances.txtAreaInstruction.focus();
    });
}

function openCompleteReasonVIP(callback) {
    // The modal has a header having >> "modal-header" div contains the closing button icon that closes the modal
    // The modal also has a textarea for notes, and two buttons: "Submit" and "Cancel".
    const modal = $(`
                        <div data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade modalCompleteActionVIP" id="CompleteInboxVIP" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                        <div class="modal-header" style="border-bottom:0px; padding-bottom:3px;">
                        <button type="button" ref="completeInboxCloseVIP" id="completeCloseVIP" class="close" data-dismiss="modal">&times;</button>
                        <h4 ref="modalCompleteTitleVIP" class="modal-title"></h4>
                        </div> 
                        <div class="modal-body" style="padding-top: 2px; ">
                        <form ref="formPost" method="post" data-parsley-validate="" novalidate="">
                        <div class="row">
                        <div class="col-md-12" ref="completeReasonContainerVIP">
                        <label class="control-label field-required" style="font-size: medium; ">${Resources.Note}</label>
                        <textarea id="completeReasonVIP" rows="3" data-parsley-required="true"class="form-control" required></textarea>
                        <div class="invalid-feedback" style="display:none; color:red;">
                                                        ${Resources.Thisfieldvalueisrequired}
                        </div>
                        </div>
                        </div>
                        </form>
                        </div>
                        <div class="modal-footer" style="border-top:0px;">
                        <button type="button" class="btn btn-primary" id="submitCompleteReasonVIP">${Resources.Submit}</button>
                        <button type="button" class="btn btn-secondary" id="cancelCompleteVIP" data-bs-dismiss="modal">${Resources.Cancel}</button>
                        </div>
                        </div>
                        </div>
                        </div>
                        `);
    // UI Production: The modal is dynamically created using a jQuery template literal and appended to the body.

    $('body').append(modal); // This body is the default screen html body, so we basically append this modal template into the screen content

    modal.modal('show');   //  displays the modal
    modal.find('#submitCompleteReasonVIP').on('click', function () {
        const textarea = modal.find('#completeReasonVIP');
        const completeVIP = textarea.val().trim();// Removes any leading or trailing whitespace from the complete reason 
        const errorMsg = textarea.siblings('.invalid-feedback');

        if (!completeVIP) {
            textarea.addClass('is-invalid');  // Adds red border
            errorMsg.show();  // Shows the error message
            modal.find('form').addClass('was-validated'); // Ensure Bootstrap applies styles
            return;
        }
        else {
            textarea.removeClass('is-invalid'); // Removes red border
            errorMsg.hide(); // Hides error message
            $("#completeCloseVIP").trigger("click");  //  triggering the close button to close the entire modal with its shadow
            callback(completeVIP);
            // Close the modal after the callback is executed
        }
    });
    // Remove validation styles on input change
    modal.find('#completeReasonVIP').on('input', function () {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').hide();
    });
    modal.find('#cancelCompleteVIP').on('click', function () {
        modal.find('#completeReasonVIP').val('');
        modal.modal('hide');
    });

    modal.on('hidden.bs.modal', function () {
        modal.remove();
    });
}
function completeTransfer(ids, delegationId) {
    openCompleteReasonVIP(function (completeReasonVIP) {
        Common.mask(document.getElementById('vipContainer'), "vipContainer-mask");
        Common.ajaxPost('/Transfer/Complete',
            {
                'ids': ids, 'delegationId': delegationId, 'note': completeReasonVIP, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                if (result != null && result.length > 0) {
                    swal.close()
                    let msg = "";
                    for (var i = 0; i < result.length; i++) {
                        if (!result[i].updated) {
                            msg += "\n ○ " + result[i].uncompletedDocumentReferenceNumber;
                        }
                    }
                    if (msg !== "") {
                        setTimeout(function () {
                            Common.alertMsg(Resources.CannotCompleteWarning + msg);
                        }, 300);
                        Common.unmask("vipContainer-mask");
                        for (var i = 0; i < ids.length; i++) {
                            var canDelete = true;
                            var li = $($("input[data-id='" + ids[i] + "']").parents("li")[0]);
                            if (result.length > 0) {
                                var spans = li.find("#middlebox span");
                                if (spans.length === 3) {
                                    if ($.grep(result, function (element, index) {
                                        return !element.updated && element.uncompletedDocumentReferenceNumber === spans[1].textContent;
                                    }).length > 0) {
                                        canDelete = false;
                                    }
                                }
                            }
                            if (canDelete) {
                                li.fadeOut().remove();
                                if (Number(gSelectedRowId) === Number(ids[i])) {
                                    $(".withBorders-o").addClass("waitingBackground");
                                    $("#inboxDocumentDetailsContainer").empty();
                                }
                            }
                        }
                        Common.unmask("vipContainer-mask");
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    }
                    else {
                        Common.unmask("vipContainer-mask");
                        Common.showScreenSuccessMsg();
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                        TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                        TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                        for (var i = 0; i < ids.length; i++) {
                            $($("input[data-id='" + ids[i] + "']").parents("li")[0]).fadeOut().remove();
                            if (Number(gSelectedRowId) === Number(ids[i])) {
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#inboxDocumentDetailsContainer").empty();
                            }
                        }
                    }
                } else {
                    Common.showScreenErrorMsg();
                }
            }, null, false);
    });
}
function buildFilters(nodeId, categories) {
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index) {
        return element.id.toString() === nodeId;
    })[0];
    var filters = node.filters !== "" && node.filters !== null ? JSON.parse(node.filters) : [];
    if (filters.length > 0) {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++) {
            var filter = filters[i];
            switch (filter) {
                case "ReferenceNumber":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
                        '<input type="text" id="txtFilterInboxReferenceNumber" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "FromDate":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label>' +
                        '<div class="input-group date"><input id="filterInboxFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterInboxFromDateError">' +
                        '<span class="input-group-addon" id="filterInboxFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterInboxFromDateError"></div></div></div></div>';
                    break;
                case "ToDate":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label>' +
                        '<div class="input-group date"><input id="filterInboxToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterInboxToDateError">' +
                        '<span class="input-group-addon" id="filterInboxToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterInboxToDateError"></div></div></div></div>';
                    break;
                case "Category":
                    clearFix += 6;
                    var div = '<div class="col-md-6" id="categoryFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterInboxCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++) {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterInboxCategoryError"></div></div></div>';
                    html += div;
                    break;
                case "Subject":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterInboxSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Read":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label"></label><div class="checkbox c-checkbox">' +
                        '<label><input tabindex="' + i + '" type="checkbox" id="chkFilterInboxRead" name="Read"><span class="fa fa-check"></span>' + Resources.Read + '</label></div></div></div>';
                    break;
                case "Locked":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label"></label><div class="checkbox c-checkbox">' +
                        '<label><input tabindex="' + i + '" type="checkbox" id="chkFilterInboxLocked" name="Locked"><span class="fa fa-check"></span>' + Resources.Locked + '</label></div></div></div>';
                    break;
                case "OverDue":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label"></label><div class="checkbox c-checkbox">' +
                        '<label><input tabindex="' + i + '" type="checkbox" id="chkFilterInboxOverdue" name="Overdue"><span class="fa fa-check"></span>' + Resources.OverDue + '</label></div></div></div>';
                    break;
                case "Purpose":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="purposeFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Purpose + '</label>' +
                        '<select id="cmbFilterInboxPurpose" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxPurposeError" class="form-control"></select></div></div>';
                    break;
                case "Priority":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="priorityFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Priority + '</label>' +
                        '<select id="cmbFilterInboxPriority" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxPriorityError" class="form-control"></select></div></div>';
                    break;
                case "Privacy":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="privacyFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Privacy + '</label>' +
                        '<select id="cmbFilterInboxPrivacy" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxPrivacyError" class="form-control"></select></div></div>';
                    break;
                case "Structure":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="structureFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Structure + '</label>' +
                        '<select id="cmbFilterInboxStructure" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxStructureError" class="form-control"></select></div></div>';
                    break;
                case "User":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="userFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.User + '</label>' +
                        '<select id="cmbFilterInboxUser" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxUserError" class="form-control"></select></div></div>';
                    break;
            }
            if (clearFix === 12 || clearFix === 11) {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '</div>';
        $('#filtersContainer').html(html);

        var clickedSearch = false;
        $('#collapseInboxIcon').click(function () {
            $('#collapseInboxIcon').empty();
            if (clickedSearch) {
                $('#collapseInboxIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
                $('#collapseInboxPanel').attr('class', '');
                $('#collapseInboxPanel').addClass('panel-body panel-collapse collapse in');
                clickedSearch = false;
            } else {
                $('#collapseInboxIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
                $('#collapseInboxPanel').attr('class', '');
                $('#collapseInboxPanel').addClass('panel-body panel-collapse collapse');
                clickedSearch = true;
            }
        });
        $("#btnFilterInboxSearch").on('click', function () {

            gFromSearch = true;
            gNoMoreData = false;
            gPageIndex = 0;
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            loadInboxList();
        });
        $("#btnFilterInboxClear").on('click', function () {
            $("#cmbFilterInboxPurpose").val('').trigger('change');
            $("#cmbFilterInboxPriority").val('').trigger('change');
            $("#cmbFilterInboxPrivacy").val('').trigger('change');
            $("#cmbFilterInboxCategory").val('').trigger('change');
            $("#txtFilterInboxReferenceNumber").val('');
            $("#txtFilterInboxSubject").val('');
            fromDate.clear();
            toDate.clear();
            $("#chkFilterInboxRead").prop("checked", false);
            $("#chkFilterInboxLocked").prop("checked", false);
            $("#chkFilterInboxAssigned").prop("checked", false);
            $("#chkFilterInboxOverdue").prop("checked", false);
            $("#cmbFilterInboxStructure").val('').trigger('change');
            $("#cmbFilterInboxUser").val('').trigger('change');
            gFromSearch = true;
            gNoMoreData = false;
            gPageIndex = 0;
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            loadInboxList();
        });

        $('#cmbFilterInboxPurpose').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Purposes().get(window.language),
            dropdownParent: $('#purposeFilterInboxContainer')
        });
        $("#cmbFilterInboxPurpose").val('').trigger('change');

        $('#cmbFilterInboxPriority').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Priorities().get(window.language),
            dropdownParent: $('#priorityFilterInboxContainer')
        });
        $("#cmbFilterInboxPriority").val('').trigger('change');

        $('#cmbFilterInboxPrivacy').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Privacies().get(window.language),
            dropdownParent: $('#privacyFilterInboxContainer')
        });
        $("#cmbFilterInboxPrivacy").val('').trigger('change');

        $('#cmbFilterInboxCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterInboxContainer')
        });
        $("#cmbFilterInboxCategory").val('').trigger('change');

        $('#cmbFilterInboxStructure').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#structureFilterInboxContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes',
                type: "POST",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term) {
                    return {
                        "text": term.term ? term.term : "", "language": window.language, "attributes": [window.StructureNameAr, window.StructureNameFr]
                    };
                },
                processResults: function (data) {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        var structureName = val.name;
                        if (val.attributes != null && val.attributes.length > 0) {
                            var attributeLang = $.grep(val.attributes, function (e) {
                                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                            });
                            if (attributeLang.length > 0) {
                                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                            }
                        }
                        var item = {};
                        item.id = val.id;
                        item.text = structureName;
                        item.isStructure = true;
                        item.dataId = val.id;
                        listitemsMultiList.push(item);
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        }).on("change", function () {
            if (document.getElementById('cmbFilterInboxUser') !== null) {
                var type = "GET";
                var url = '/api/SearchUsers';
                //var structures = $('#cmbFilterInboxStructure').val();
                //if (structures !== "" && structures !== null)
                //{
                //    type = "POST";
                //    url = '/api/SearchUsersByStructureIds';
                //}
                $('#cmbFilterInboxUser').select2({
                    minimumInputLength: 0,
                    allowClear: false,
                    placeholder: "",
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    dropdownParent: $("#userFilterInboxContainer"),
                    multiple: true,
                    width: "100%",
                    ajax: {
                        delay: 400,
                        url: window.IdentityUrl + url,
                        type: type,
                        dataType: 'json',
                        headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                        data: function (term) {
                            var params = { "text": "", "language": window.language };
                            //var params = { "text": term.term ? term.term : "", "language": window.language };
                            //var structures = $('#cmbFilterInboxStructure').val();
                            //if (structures !== "" && structures !== null)
                            //{
                            //    params.ids = $('#cmbFilterInboxStructure').val();
                            //}
                            return params;
                        },
                        processResults: function (data, term) {
                            var termSearch = term.term ? term.term : "";

                            var structures = $('#cmbFilterInboxStructure').val();
                            var listitemsMultiList = [];
                            $.each(data, function (key, val) {
                                if (structures !== "" && structures !== null &&
                                    !((val.structureIds != null && val.structureIds.some(r => structures.indexOf(String(r)) >= 0)) ||
                                        structures.includes(val.defaultStructureId))) {
                                    return;
                                }
                                var fullName = val.fullName;
                                if (window.language != 'en') {
                                    fullName = getFullNameByLangauge(val);
                                    fullName = fullName.trim() == "" ? val.fullName : fullName;
                                }
                                var allNames = getFullNameInAllLangauge(val);
                                if (allNames.length == 0) allNames.push(fullName);
                                if (termSearch != "" &&
                                    !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                                    return;
                                }
                                let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                                if (!isExist) {
                                    var item = {};
                                    item.id = val.id;
                                    item.text = fullName;
                                    item.isStructure = false;
                                    item.dataId = val.id;
                                    listitemsMultiList.push(item);
                                }
                            });
                            return {
                                results: listitemsMultiList
                            };
                        }
                    },
                    sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
                }).val("").trigger("change");
            }
        });
        $("#cmbFilterInboxStructure").val('').trigger('change');
        $('#cmbFilterInboxUser').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#userFilterInboxContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchUsers',
                type: "GET",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term) {
                    return { "text": "", "language": window.language };
                    //return { "text": term.term ? term.term : "", "language": window.language };
                },
                processResults: function (data, term) {
                    var termSearch = term.term ? term.term : "";

                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        var fullName = val.fullName;
                        if (window.language != 'en') {
                            fullName = getFullNameByLangauge(val);
                            fullName = fullName.trim() == "" ? val.fullName : fullName;
                        }
                        var allNames = getFullNameInAllLangauge(val);
                        if (allNames.length == 0) allNames.push(fullName);
                        if (termSearch != "" &&
                            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                            return;
                        }
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = fullName;
                            item.isStructure = false;
                            item.dataId = val.id;
                            listitemsMultiList.push(item);
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });
        $("#cmbFilterInboxUser").val('').trigger('change');

        var fromDate = $('#filterInboxFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery('#filterInboxToDate').val() && jQuery('#filterInboxToDate').val() !== "" ? jQuery('#filterInboxToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterInboxFromDate_img").click(function () {
            fromDate.toggle();
        });
        var toDate = $('#filterInboxToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery('#fromDate').val() && jQuery('#fromDate').val() !== "" ? jQuery('#fromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterInboxToDate_img").click(function () {
            toDate.toggle();
        });
        $('#txtFilterInboxReferenceNumber').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterInboxSearch').focus();
                }
                else {
                    $('#filterInboxFromDate').focus();
                }
            }
        });
        $('#btnFilterInboxClear').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterInboxSearch').focus();
                }
                else {
                    $('#txtFilterInboxReferenceNumber').focus();
                }
            }
        });
    } else {
        $("#btnOpenSearchInboxModal").remove();
        $("#divSearchInbox").remove();
    }
}
function openDocumentViewMode(id, isRead, delegationId, isCced, nodeId, input) {

    var params = { "Id": id, "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
    if (delegationId !== null) {
        params.DelegationId = delegationId;
    }

    if (isRead === "false" && isCced === "false") {
        Common.showConfirmMsg(Resources.KindlyNoteMarkedRead + "\n" + Resources.OpenConfirmation, function () {
            Common.ajaxPost('/Transfer/View', params, function () {

                var li = $($("input[data-id='" + id + "']").parents("li")[0]);
                var actions = li.find('.mdl-action');
                if (actions.find('button.unReadIcon').length > 0) {
                    actions.find('button.unReadIcon').show();
                } else {
                        var cced = input.getAttribute("data-cced");
                        var isRead = true;
                        var lockedDate = input.getAttribute("data-lockeddate");
                        var lockedBy = input.getAttribute("data-lockedbyme");

                    actions.prepend("<button class='btn btn-xs mr-sm unReadIcon' title='" + Resources.UnRead + "' style='padding: 0px 3px;' " +
                            "data-id='" + id + "' " +
                            "data-isread='" + isRead + "' " +
                            "data-delegationid='" + delegationId + "' " +
                            "data-cced='" + cced + "' " +
                            "data-lockeddate='" + lockedDate + "' " +
                            "data-lockedby='" + lockedBy + "'>" +
                            "<i class='fa fa-envelope text-warning'></i>" +
                            "</button>");
                    

                }
                openDocumentDetails(id, delegationId, true, nodeId);
            }, function () { gLocked = false; });
        }, function () { gLocked = false; });
    } else if (isCced === "true") {
        Common.ajaxPost('/Transfer/View', params, function () {
            openDocumentDetails(id, delegationId, true, nodeId, isCced);
        }, function () { gLocked = false; });
    } else {
        openDocumentDetails(id, delegationId, true, nodeId);
    }
}
function openDocumentEditMode(id, lockedByMe, sentToUser, delegationId, nodeId, title, fromStructureInbox = false, isLocked,input) {

    var params = { "Id": id, "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
    if (delegationId !== null) {
        params.DelegationId = delegationId;
    }
    if (lockedByMe === "false" && (sentToUser === "false" || (sentToUser === "true" && fromStructureInbox && isLocked == "false"))) {
        Common.showConfirmMsg(Resources.LockConfirmation, function () {
            Common.ajaxPost('/Transfer/Lock', params, function (response) {
                if (typeof response !== "boolean") {
                    Common.showConfirmMsg(Resources.AlreadyLockedBy + ": " + response + "\n" + Resources.DoYouWantToOpen, function () {
                        openDocumentDetails(id, delegationId, true, nodeId);
                    }, function () { gLocked = false; });
                } else {
                    var li = $($("input[data-id='" + id + "']").parents("li")[0]);
                    $("input[data-id='" + id + "']").attr('data-lockedbyme', 'true');
                        li.find('.unReadIcon').remove();
                    var actions = li.find('.mdl-action');
                    var titleLock = Resources.LockedBy + ": " + Resources.You /*+ "</br>" + Resources.LockedDate + ": " + date*/;
                    actions.append("<button class='btn btn-xs btn-success mr-sm unlockIcon' style='padding: 0px 3px;' title='" + titleLock + "'><i class='fa fa-unlock fa-white'></i></button>");

                    openDocumentDetails(id, delegationId, false, nodeId);
                }
            }, function () { gLocked = false; });
        }, function () { gLocked = false; });
    } else {
        
            Common.ajaxPost('/Transfer/View', params, function (response) {
                var li = $($("input[data-id='" + id + "']").parents("li")[0]);
                var actions = li.find('.mdl-action');
                if (actions.find('button.unReadIcon').length > 0) {
                    actions.find('button.unReadIcon').show();

                } else {
                    if (lockedByMe === "false") {
                        var cced = input.getAttribute("data-cced");
                        var isRead = true;
                        var lockedDate = input.getAttribute("data-lockeddate");
                        var lockedBy = input.getAttribute("data-lockedbyme");

                        actions.prepend("<button class='btn btn-xs mr-sm unReadIcon' title='" + Resources.UnRead + "' style='padding: 0px 3px;' " +
                            "data-id='" + id + "' " +
                            "data-isread='" + isRead + "' " +
                            "data-delegationid='" + delegationId + "' " +
                            "data-cced='" + cced + "' " +
                            "data-lockeddate='" + lockedDate + "' " +
                            "data-lockedby='" + lockedBy + "'>" +
                            "<i class='fa fa-envelope text-warning'></i>" +
                            "</button>");
                    }

                }

                openDocumentDetails(id, delegationId, false, nodeId);
            }, function () { gLocked = false; });
    }
}
function openDocumentDetails(id, delegationId, readOnly, nodeId, isCced) {


    var params = { id: id };
    params.nodeId = nodeId;
    if (delegationId !== null) {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetDocumentBasicInfoByTransferId', params, function (response) {
        $('.card-max-width').addClass('card-min-width');
        $('.card-max-width').removeClass('card-max-width');
        $('input:checkbox').removeAttr('checked');
        $('#inboxListContainer li').removeClass("active");
        gSelectedRowId = id;
        $("input[data-id='" + id + "']").parent().parent().parent().removeClass('unread').addClass("active");
        $("input[data-id='" + id + "']").attr('data-read', 'true');
        $("input[data-id='" + id + "']").prop('checked', true);

        let item = "liInbox" + nodeId;
        if (window.location.href.indexOf("StructureInbox") > 0)
            item = "liStructureInbox" + nodeId;
        if (delegationId !== null) {
            item = "index-" + nodeId;
        }
        Common.setActiveSidebarMenu(item);
        $(".delegation").removeClass("active");
        var model = new VipDocumentDetails.VipDocumentDetails();
        model.categoryId = response.categoryId;
        model.readonly = false;
        model.delegationId = delegationId;
        model.id = id;
        model.documentId = response.id;
        model.referenceNumber = response.referenceNumber;
        model.categoryName = response.categoryName;
        model.statusId = response.status;
        model.createdByUser = response.createdByUser;
        model.readonly = readOnly === true;
        model.attachmentId = response.attachmentId;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.showMyTransfer = true;
        model.fromInbox = true;
        model.isCced = isCced;
        model.fromInbox = true;
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
        model.tabs = $.grep(tabs, function (element, index) {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
        });
        model.tabsWithStatic = tabs;
        model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
        model.showBackButton = false;
        model.nodeId = nodeId;
        model.showPreview = window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer";
        model.attachmentCount = response.attachmentCount;
        model.noteCount = response.notesCount;
        model.linkedCount = response.linkedCorrespondanceCount;
        model.attchmentIsLocked = response.attachmentIslocked
        model.attachmentVersion = response.attachmentVersion;

        var wrapper = $("#inboxDocumentDetailsContainer");
        wrapper.empty();
        $(".modal-window").empty();
        var view = new VipDocumentDetails.VipDocumentDetailsView(wrapper, model);
        view.render();
        $(".documentHeader").hide();
        $(".waitingBackground").removeClass("waitingBackground");
        gLocked = false;
        $(".vipDetailsPanel").show();
        $(".vipCorrLeftPanel").removeClass("col-lg-12 col-md-12 col-sm-12 col-xs-12");
        $(".vipCorrLeftPanel").addClass("col-lg-3 col-md-4 col-sm-4 col-xs-12");
        $(".mdl-ul").removeAttr("style")
    }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
}
function unlock(id, delegationId, nodeId) {
    Common.showConfirmMsg(Resources.UnlockConfirmation, function () {
        Common.mask(document.getElementById('vipContainer'), "vipContainer-mask");
        var params = { "Id": id, "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
        if (delegationId !== null) {
            params.DelegationId = delegationId;
        }
        Common.ajaxPost('/Transfer/UnLock', params, function (response) {
            if (response === "False") {
                Common.showScreenErrorMsg();
                Common.unmask("vipContainer-mask");
            }
            else if (response === "FileInUse") {
                setTimeout(function () {
                    Common.alertMsg(Resources.FileInUse);
                }, 300);
                Common.unmask("vipContainer-mask");
            } else {
                var li = $($("input[data-id='" + id + "']").parents("li")[0]);
                $("input[data-id='" + id + "']").attr('data-lockedbyme', 'false');
                li.find('.unlockIcon').remove();
             
                var actions = li.find('.mdl-action');
                actions.append("<button class='btn btn-xs btn-primary mr-sm lockIcon' style='padding: 0px 3px;'><i class='fa fa-edit' title='" + Resources.Edit + "'></i></button>");

                if (Number(gSelectedRowId) === Number(id))
                {
                    openDocumentDetails(id, delegationId, true, nodeId);
                }
                Common.unmask("vipContainer-mask");
            }
            gLocked = false;
            refreshInboxList([id])
        //    loadInboxList();
        });
    }, function () { gLocked = false; });
}
function markAsUnread(id, isRead, delegationId, isCced, nodeId, lockedDate, lockedby, button, callback) {
    
    if (!lockedDate) {
        var params = {
            "Id": id,
            "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
        };

        if (delegationId !== null) {
            params.DelegationId = delegationId;
        }

        if (isRead) {
            Common.ajaxPost('/Transfer/UnRead', params, function (response) {
                if (!response.success) {
                    if (response.message === "FileInUse") {
                        setTimeout(function () {
                            Common.alertMsg(Resources.FileInUse);
                        }, 300);
                    }
                    callback(false);
                } else {
                    if (button.closest('li').hasClass('active')) {
                    $(".vipDetailsPanel").hide();
                    $(".vipCorrLeftPanel").removeClass("col-lg-3 col-md-4 col-sm-4");
                    }
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    callback(true); 
                }
            });
        } else {
            callback(false); 
        }
    } else {
        Common.showScreenErrorMsg("This item cannot be marked as unread because it is locked");
        callback(false); 
    }
    loadInboxList();
}
function loadInboxList() {
    if (!gNoMoreData) {
        Common.mask(document.getElementById('inboxListContainer'), "inboxListContainer-mask");
        var params = {};
        addFilters(params);
        params.NodeId = gSelf.model.nodeId;
        params.DelegationId = gSelf.model.delegationId;
        params.start = gPageIndex;
        params.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
        params.FromStructure = fromStructureInbox;
        Common.ajaxPost('/Transfer/ListInboxVip', params, function (response) {
            if (response.length > 0) {
                gPageIndex += window.Paging;
                if (response.length < window.Paging) {
                    gNoMoreData = true;
                }
            } else {
                gNoMoreData = true;
            }
            createListData(response);
            gLocked = false;
            Common.unmask("inboxListContainer-mask");
            if (gFromSearch) {
                $("#divSearchInbox").fadeOut();
            }
        }, function () { gLocked = false; Common.showScreenErrorMsg(); });
    } else {
        gLocked = false;
    }
}
function refreshInboxList(ids) {
    for (var i = 0; i < ids.length; i++) {
        var canDelete = false;
        var li = $($("input[data-id='" + ids[i] + "']").parents("li")[0]);
        var spans = li.find("#middlebox span");
        if (spans.length >= 3 ) {
            canDelete = true;
        }
        if (canDelete) {
            li.fadeOut().remove();
            $(".withBorders-o").addClass("waitingBackground");
            $("#inboxDocumentDetailsContainer").empty();
            gSelectedRowId = null;
        }
    }
    gPageIndex = 0;
    gNoMoreData = false;
    loadInboxList();
}
function createListData(data) {


    var html = '';
    if (data.length === 0 && gPageIndex === 0) {
        html = '<ul class="mdl-ul"><div class="nodata dataTables_empty pt-lg">' + Resources.NoDataToDisplay + '</div></ul>';
        $('#inboxListContainer').html(html);
    } else if (data.length > 0) {
        html = '<ul class="mdl-ul VIPContainerItem">';
        var htmlLi = '';
        for (var i = 0; i < data.length; i++) {
            var transfer = data[i];
            var liClass = "mdl-li";
            var color = "";

            if (!transfer.isRead) {

                liClass += " unread";
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                //loadInboxList();

            }
            var lockedByMe = false;
            //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
            var delegatedUser = gSelf.model.delegationId !== null ? new DelegationUsers().getById(Number(gSelf.model.delegationId)) : null;
            var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
            if (transfer.isLocked && (transfer.ownerUserId !== null && transfer.ownerUserId === Number($("#hdUserId").val()) && gSelf.model.delegationId === null)
                || (transfer.ownerDelegatedUserId !== null && transfer.ownerDelegatedUserId === Number($("#hdUserId").val())
                    && delegatedUserId === transfer.ownerUserId && gSelf.model.delegationId !== null)
                || (transfer.ownerUserId !== null && delegatedUserId === transfer.ownerUserId && gSelf.model.delegationId !== null)) {
                lockedByMe = true;
            }
            var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            var privacies = new CoreComponents.Lookup.Privacies().get(window.language);

            if (window.PriorityPrivacyAction == "2") {
                for (var j = 0; j < privacies.length; j++) {
                    if (privacies[j].id === transfer.privacyId) {
                        color = privacies[j].color;
                        break;
                    }
                }
            } else {
                for (var j = 0; j < priorities.length; j++) {
                    if (priorities[j].id === transfer.priorityId) {
                        color = priorities[j].color;
                        break;
                    }
                }
            }
            var htmlIcons = "";
            if (transfer.importanceId) {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var j = 0; j < importances.length; j++) {
                    if (importances[j].id === transfer.importanceId) {
                        htmlIcons += "<i class='fa fa-exclamation fa-lg mr-sm' title='" + importances[j].text + "' style='color:" + importances[j].color + "'></i>";
                    }
                }
            }
            if (transfer.isOverDue) {
                htmlIcons += "<i class='fa fa-clock-o fa-lg text-danger mr-sm' title='" + Resources.OverDue + "'></i>";
            }
            if (transfer.isRead && !transfer.lockedDate ) {
                htmlIcons += "<button class='btn btn-xs mr-sm unReadIcon' title='" + Resources.UnRead + "' style='padding: 0px 3px;' " +
                    "data-id='" + transfer.id + "' " +
                    "data-isread='" + transfer.isRead + "' " +
                    "data-delegationid='" + transfer.delegationId + "' " +
                    "data-cced='" + transfer.cced + "' " +
                    "data-lockeddate='" + transfer.lockedDate + "' " +
                    "data-lockedby='" + transfer.lockedBy + "'>" +
                    "<i class='fa fa-envelope text-warning'></i>" +
                    "</button>";
            }
            if (transfer.requestStatus == "Pending") {
                let btn = document.createElement("button");
                btn.setAttribute("class", "btn btn-xs btn-primary mr-sm accept");
                btn.setAttribute("title", Resources.Accept);
                btn.setAttribute("clickattr", "acceptRequest(" + transfer.id + ")");
                btn.innerHTML = "<i class='fa fa-check'/>";
                htmlIcons += btn.outerHTML;

                let rejectBtn = document.createElement("button");
                rejectBtn.setAttribute("class", "btn btn-xs btn-danger mr-sm reject");
                rejectBtn.setAttribute("title", Resources.Reject);
                rejectBtn.setAttribute("clickattr", "rejectRequest(" + transfer.id + ")");
                rejectBtn.innerHTML = "<i class='fa fa-close'/>";
                htmlIcons += rejectBtn.outerHTML;
            }

            let category = $.grep(gSelf.model.categories, function (e) {
                return e.id === transfer.categoryId;
            });
            if (category[0] && category[0].isBroadcast) {
                htmlIcons += "<i class='fa fa-bullhorn text-primary mr-sm'  title='" + Resources.Broadcast + "'></i>";
            } else if (transfer.cced) {
                htmlIcons += "<i class='fa fa-cc text-warning mr-sm'  title='" + Resources.CarbonCopy + "'></i>";
            }
            if (!transfer.cced && transfer.requestStatus != "Pending" && (lockedByMe || !transfer.isLocked)) {
                htmlIcons += "<button class='edit btn btn-xs btn-primary mr-sm lockIcon' style='padding: 0px 3px;' title='" + Resources.Edit + "'><i class='fa fa-edit'></i></button>";
            }
            var lockedByUser = transfer.ownerUserId === Number($("#hdUserId").val()) ? Resources.You : transfer.lockedBy;
            var lockedBy = transfer.lockedByDelegatedUser !== '' ? transfer.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + transfer.lockedBy : lockedByUser;
            var titleLock = Resources.LockedBy + ": " + lockedBy + "\n" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(transfer.lockedDate, null, window.CalendarType);
            if ((transfer.sentToStructure || (!transfer.sentToStructure && gSelf.model.fromStructureInbox)) && lockedByMe && gSelf.model.nodeId != window.AcceptRejectNodeId) {
                htmlIcons += "<button class='btn btn-xs btn-success mr-sm unlockIcon' style='padding: 0px 3px;' title='" + titleLock + "'><i class='fa fa-unlock fa-white'></i></button>";
            } else if (transfer.isLocked && !transfer.cced && gSelf.model.nodeId != window.AcceptRejectNodeId) {
                htmlIcons += "<i class='fa fa-lock fa-lg text-danger mr-sm' title='" + titleLock + "'></i>";
            }
            transfer.referenceNumber = transfer.referenceNumber ?? "";
            var from = transfer.fromStructure !== "" ? transfer.fromStructure + '/' + transfer.fromUser : transfer.fromUser;
            var to = "";
            transfer.receivingEntities.forEach((item) => {
                to += item.text;
            })
                htmlLi += '<li class="' + liClass + '" style="color:' + color + ';" data-transfer="' + transfer.id + '">'; // Apply the dynamic color here

            htmlLi += '<div class="mdl-container">';
            htmlLi += '<div id="leftbox" class="pull-left">';
            htmlLi += '<div class="inside_color_line pull_left"></div>';
            htmlLi += '<input data-id=' + transfer.id + ' data-categoryid=' + transfer.categoryId + ' data-cced=' + transfer.cced + ' data-read=' + transfer.isRead +
                ' data-lockedbyme=' + lockedByMe + '  data-islocked=' + transfer.isLocked +
                '  data-senttouser=' + transfer.sentToUser +
                ' type="checkbox" class="pointer m-sm ml-10px" onclick="event.stopPropagation();">';
            htmlLi += "<input type='hidden' data-id=" + transfer.id + " value='" + JSON.stringify(transfer) + "'/>";
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-circle icon-primary'></i></span>"
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-arrow-left'></i></span>"
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-arrow-right'></i></span>"

            htmlLi += '</div>';
            htmlLi += '<div id="middlebox" class="mdl-text-msg pull-left pl-sm card-max-width">';
            //htmlLi += '<span class="dot"></span>';
            htmlLi += '<span class="mdl-span" data-field="referenceNumber" style="color:' + color + '" title="' + transfer.referenceNumber + '" data-ref>' + transfer.referenceNumber + '</span>';

            htmlLi += '<span class="mdl-span text-primary" data-field="subject"  style="color:' + color + '" title="' + transfer.subject + '">' + transfer.subject + '</span>';
            htmlLi += '<span class="mdl-span" data-field="from"  style="color:' + color + '" title="' + from + '">' + from + '</span>';

            htmlLi += '<span class="mdl-span light_grey_color"  data-field="toStructure" style="color:' + color + '" title="' + transfer.toStructure + '">' + transfer.toStructure + '</span>';
            htmlLi += '<span class="mdl-span light_grey_color" data-field="purposeName" style="color:' + color + '" title="' + transfer.purposeName + '">' + transfer.purposeName + '</span>';
            htmlLi += '</div>';
            htmlLi += '<div id="rightbox" class="pull-right text-right"><div class="mdl-time mr-sm" style="color:' + color + '" title="' + Resources.TransferDate + '">' + dateFormat(transfer.transferDate) + '</div>';

            if (htmlIcons !== "") {
                htmlLi += '<div class="mdl-action">' + htmlIcons + '</div>';
            }
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</li>';
        }
        html += htmlLi;
        html += '</ul>';
        if (gPageIndex === 15) {
            $('#inboxListContainer').html(html);
        } else {
            $('#inboxListContainer ul').append(htmlLi);
        }

        $(".unread").css('font-weight', 'bold')

        if (gSelf.model.transferId && gSelf.model.transferId != null) {
            var transfer = data.find(d => d.id == gSelf.model.transferId);
            var checkbox = $('#leftbox input[data-id="' + gSelf.model.transferId.toString() + '"]');
            if (checkbox.length) {
                var container = checkbox.closest('.mdl-container');
                if (container.length) {
                    if (transfer && transfer.sentToStructure) {
                     
                        container.find('#rightbox .edit').click();
                    } else {
                     
                        container.parent().click();
                    }

                }
            }
        }

    }
}
function addFilters(d) {
    d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
    if (gFromSearch) {
        d.PriorityId = $("#cmbFilterInboxPriority").val() !== null && typeof $("#cmbFilterInboxPriority").val() !== "undefined" ? $("#cmbFilterInboxPriority").val() : "0";
        d.PrivacyId = $("#cmbFilterInboxPrivacy").val() !== null && typeof $("#cmbFilterInboxPrivacy").val() !== "undefined" ? $("#cmbFilterInboxPrivacy").val() : "0";
        d.PurposeId = $("#cmbFilterInboxPurpose").val() !== null && typeof $("#cmbFilterInboxPurpose").val() !== "undefined" ? $("#cmbFilterInboxPurpose").val() : "0";
        d.CategoryId = $("#cmbFilterInboxCategory").val() !== null && typeof $("#cmbFilterInboxCategory").val() !== "undefined" ? $("#cmbFilterInboxCategory").val() : "0";
        d.ReferenceNumber = $("#txtFilterInboxReferenceNumber").val() !== "" && typeof $("#txtFilterInboxReferenceNumber").val() !== "undefined" ? $("#txtFilterInboxReferenceNumber").val() : "";
        d.FromDate = $("#filterInboxFromDate").val() !== "" && typeof $("#filterInboxFromDate").val() !== "undefined" ? $("#filterInboxFromDate").val() : "";
        d.ToDate = $("#filterInboxToDate").val() !== "" && typeof $("#filterInboxToDate").val() !== "undefined" ? $("#filterInboxToDate").val() : "";
        d.Read = $("#chkFilterInboxRead").is(':checked');
        d.Locked = $("#chkFilterInboxLocked").is(':checked');
        d.Overdue = $("#chkFilterInboxOverdue").is(':checked');
        d.Subject = $("#txtFilterInboxSubject").val() !== "" && typeof $("#txtFilterInboxSubject").val() !== "undefined" ? $("#txtFilterInboxSubject").val() : "";
        d.StructureIds = $("#cmbFilterInboxStructure").val() !== null && typeof $("#cmbFilterInboxStructure").val() !== "undefined" ? $("#cmbFilterInboxStructure").val() : [];
        d.UserIds = $("#cmbFilterInboxUser").val() !== null && typeof $("#cmbFilterInboxUser").val() !== "undefined" ? $("#cmbFilterInboxUser").val() : [];

    }
}
function dateFormat(dateText) {
    var dateFull = dateText.split(" ")[0].split("/");
    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth() + 1; //January is 0!
    var yy = today.getFullYear();
    var time = DateConverter.toHijriFormated(dateText.split(" ")[0], null, window.CalendarType);
    if (parseInt(dateFull[0]) === dd && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy) {
        time = "";
        var timeSeparator = ":";
        var hours = parseInt(dateText.split(" ")[1].split(":")[0]);
        var amPm = Resources.AM;
        if (hours > 12) {
            time += (hours - 12) + timeSeparator;
            amPm = Resources.PM;
        } else if (hours === 12) {
            time += "12" + timeSeparator;
            amPm = Resources.PM;
        } else {
            time += (hours < 10 ? '0' : '') + hours + timeSeparator;
            amPm = Resources.AM;
        }
        var minutes = parseInt(dateText.split(" ")[1].split(":")[1]);
        minutes = (minutes < 10 ? '0' : '') + minutes;
        time += minutes + " " + amPm;
    } else if (parseInt(dateFull[0]) === (dd - 1) && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy) {
        time = Resources.Yesterday;
    }
    return time;
}
var gLocked = false;
var gNoMoreData = false;
var gFromSearch = false;
var gPageIndex = 0;
var gSelectedRowId, gSelf;
var wrapperParent;
var fromStructureInbox = false;
class VipDocumentInboxView extends Intalio.View {
    constructor(element, model) {
        wrapperParent = model;
        super(element, "vipinbox", model);
    }
    render() {
        $.fn.select2.defaults.set("theme", "bootstrap");
        gSelf = this;
        fromStructureInbox = gSelf.model.fromStructureInbox;
        gLocked = false;
        gPageIndex = 0;
        gNoMoreData = false;
        gFromSearch = false;
        gSelectedRowId = null;
        var categoryModels = new CategoryModel().getFullCategories();
        if (categoryModels.length > 0) {
            for (var i = 0; i < categoryModels.length; i++) {
                if (categoryModels[i].basicAttribute !== "" && categoryModels[i].basicAttribute !== null) {
                    let basicAttributes = JSON.parse(categoryModels[i].basicAttribute);
                    if (basicAttributes.length > 0) {
                        let receivingEntityObj = $.grep(basicAttributes, function (e) {
                            return e.Name === "ReceivingEntity";
                        });
                        if (receivingEntityObj[0].BroadcastReceivingEntity) {
                            for (var j = 0; j < gSelf.model.categories.length; j++) {
                                if (gSelf.model.categories[j].id == categoryModels[i].id) {
                                    gSelf.model.categories[j].isBroadcast = true;
                                    gSelf.model.categories[j].isInternalBroadcast = receivingEntityObj[0].Type === "internal";
                                }
                            }
                        }
                    }
                }
            }
        }
        var followUpCategoryIndex = gSelf.model.categories.findIndex(item => item.id == window.FollowUpCategory);
        gSelf.model.categories.splice(followUpCategoryIndex, 1);

        buildFilters(gSelf.model.nodeId, gSelf.model.categories);
        loadInboxList();
        var lastScrollTop = 0;
        $('#inboxListContainer').on('scroll', function () {
            var div = $(this);
            var st = div.scrollTop();
            if (st > lastScrollTop && st + div.innerHeight() + 10 >= div[0].scrollHeight)
            {
                if (!gLocked)
                {
                    gLocked = true;
                    try {
                        loadInboxList();
                    } catch (e) {
                        gLocked = false;
                    }
                }
            }
            lastScrollTop = st;
        });
        $('#inboxListContainer').on('click', 'li:not(.active)', function (e)
        {
            //$(".unread").css('font-weight', 'bold')
            $(this).removeAttr('style');

            if ($(e.target).hasClass("lockIcon") || $(e.target).hasClass("unlockIcon") || $(e.target).closest('.accept').length || $(e.target).closest('.reject').length || $(e.target).closest('.unReadIcon').length) {
                return;
            }
            
            if (!gLocked)
            {

                gLocked = true;
                try {
                    var input = this.getElementsByTagName('input')[0];
                    if (typeof input !== "undefined") {
                        if (!$(this).hasClass("active")) {
                            var id = input.getAttribute("data-id");
                            var cced = input.getAttribute("data-cced");
                            var read = input.getAttribute("data-read");
                            var lockedByMe = input.getAttribute("data-lockedbyme");
                            var IsLocked = input.getAttribute("data-islocked");
                            var sentToUser = input.getAttribute("data-senttouser");
                            if (Number(gSelectedRowId) !== Number(id)) {
                                if (!$(".filterInfoDiv").hasClass("hidden")) {
                                    $(".filterInfoDiv").addClass("hidden");
                                }
                                if ((sentToUser === "true" || lockedByMe === "true" || IsLocked == "false") && (gSelf.model.nodeId != window.AcceptRejectNodeId)) {
                                    openDocumentEditMode(id, lockedByMe, sentToUser, gSelf.model.delegationId, gSelf.model.nodeId, gSelf.model.title, gSelf.model.fromStructureInbox, IsLocked,input);
                                } else {
                                    openDocumentViewMode(id, read, gSelf.model.delegationId, cced, gSelf.model.nodeId,input);
                                }
                            } else {
                                gLocked = false;
                            }
                        } else {
                            gLocked = false;
                        }
                    } else {
                        gLocked = false;
                    }
                } catch (e) {
                    gLocked = false;
                }
            }
        });

        $('#inboxListContainer').on('click', 'li.active', function (e) {
            if ($(e.target).hasClass("lockIcon") || $(e.target).hasClass("unlockIcon") || $(e.target).closest('.accept').length || $(e.target).closest('.reject').length || $(e.target).closest('.unReadIcon').length) {
                return;
            }
            $(this).removeClass('active');
            gSelectedRowId = null;
            $(this).trigger('click');
        });
        $('#inboxListContainer').on('click', '.lockIcon', function ()
        {
            if (!gLocked)
            {
                gLocked = true;
                try {
                    var input = $($(this).parents("li")[0]).find('input');
                    if (typeof input !== "undefined") {
                        var id = input.attr("data-id");
                        var lockedByMe = input.attr("data-lockedbyme");
                        var sentToUser = input.attr("data-senttouser");
                        var IsLocked = input.getAttribute("data-islocked");
                        if (Number(gSelectedRowId) !== Number(id) || lockedByMe === "false" ) {
                            openDocumentEditMode(id, lockedByMe, sentToUser, gSelf.model.delegationId, gSelf.model.nodeId, gSelf.model.title, gSelf.model.fromStructureInbox, IsLocked,input);
                        } else {
                            gLocked = false;
                        }
                    } else {
                        gLocked = false;
                    }
                } catch (e) {
                    gLocked = false;
                }
            }
        });

        $('#inboxListContainer').on('click', '.unlockIcon', function () {
            if (!gLocked) {
                gLocked = true;
                try {
                    var input = $($(this).parents("li")[0]).find('input');
                    if (typeof input !== "undefined") {
                        var id = input.attr("data-id");
                        var lockedByMe = input.attr("data-lockedbyme");
                        if (lockedByMe === "true")
                        {

                            unlock(id, gSelf.model.delegationId, gSelf.model.nodeId);
                        } else {
                            gLocked = false;
                        }
                    } else {
                        gLocked = false;
                    }
                } catch (e) {
                    gLocked = false;
                }
            }
        });

        $('#inboxListContainer').on('click', '.unReadIcon', function () {
            var button = $(this);
            var id = button.data('id');
            var isRead = button.data('isread') || false;
            var delegationId = button.data('delegationid') === "null" ? null : button.data('delegationid');
            var isCced = button.data('cced') || false;
            var lockedDate = button.data('lockeddate') || '';
            var lockedBy = button.data('lockedby') || '';

            markAsUnread(id, isRead, delegationId, isCced, null, lockedDate, lockedBy, button, function (res) {

                if (isRead && lockedDate === '' && res) {
                    button.closest('li').css('font-weight', 'bold');
                    var input = button.closest('li')[0].getElementsByTagName('input')[0];
                    input.setAttribute("data-read", "false");
                   // button.closest('li').removeClass('active');
                    button.closest('li').addClass('unread');
                    //gSelectedRowId = null;
                    button.hide();
                }
            });
        });
        $('#inboxListContainer').on('click', '.accept', function () {

            if (!gLocked) {
                gLocked = true;
                try {
                    var input = $($(this).parents("li")[0]).find('input');
                    if (typeof input !== "undefined") {
                        var id = input.attr("data-id");
                        
                        acceptRequest(id);
                       
                    } else {
                        gLocked = false;
                    }
                } catch (e) {
                    gLocked = false;
                }
            }
           
        });
        $('#inboxListContainer').on('click', '.reject', function () {

            if (!gLocked) {
                gLocked = true;
                try {
                    var input = $($(this).parents("li")[0]).find('input');
                    if (typeof input !== "undefined") {
                        var id = input.attr("data-id");
                        rejectRequest(id);

                    } else {
                        gLocked = false;
                    }
                } catch (e) {
                    gLocked = false;
                }
            }

        });

        $('#inboxListContainer').on('click', 'input', function () {
            var input = this;
            if (typeof input !== "undefined") {
                input.checked = input.checked ? false : true;
            }
        });
        $('#chkAll').change(function () {
            var checked = $(this).prop('checked');
            $('#inboxListContainer input[type="checkbox"]').prop('checked', checked);
        });
        $('#inboxListContainer').on('change', 'input[type="checkbox"]:not(#chkAll)', function () {
            var all = $('#inboxListContainer input[type="checkbox"]:not(#chkAll)');
            var allChecked = all.length === all.filter(':checked').length;

            $('#chkAll').prop('checked', allChecked);
        });
        $('.toggleVIP').on('click', function () {
            if (window.InboxMode === "LocalVIPView") {
                window.InboxMode = "InboxDefault";
            } else if (window.InboxMode !== "InboxDefault") {
                window.InboxMode = "LocalInboxDefaultView";
            }

            let wrapper = $(".content-wrapper");
            let defaultmodel = new DocumentInbox.DocumentInbox();
            defaultmodel.nodeId = wrapperParent.nodeId;
            defaultmodel.delegationId = wrapperParent.delegationId;
            defaultmodel.categories = new Categories().get(window.language, wrapperParent.delegationId);
            defaultmodel.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            defaultmodel.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            defaultmodel.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            defaultmodel.title = $('.content-heading').text();
            defaultmodel.fromStructureInbox = fromStructureInbox;
            defaultmodel.isExported = wrapperParent.isExported;
            let documentView = new DocumentInbox.DocumentInboxView(wrapper, defaultmodel);
            documentView.render();
        })
        $("#btnInboxComplete").on('click', function () {
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
            if (checkedRows.length > 0) {
                var ids = new Array();
                checkedRows.each(function (index, obj) {
                    ids.push(obj.getAttribute('data-id'));
                });
                var allCced = true;
                var allSelectedData = [];
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $("input[type='hidden'][data-id='" + ids[i] + "']").val();
                    if (selectedRowData && selectedRowData !== "") {
                        selectedRowData = JSON.parse(selectedRowData);
                        allSelectedData.push(selectedRowData);
                        let category = $.grep(gSelf.model.categories, function (e) {
                            return e.id === selectedRowData.categoryId;
                        });
                        if (allCced && (!selectedRowData.cced || category[0].isInternalBroadcast)) {
                            allCced = false;
                        }
                    }
                }
                if (allCced) {
                    Common.alertMsg(Resources.AllSelectedItemsCCed);
                } else {
                    if (window.EnableConfirmationMessage === "True") {
                        Common.showConfirmMsg(Resources.CompleteConfirmation, function () {
                            completeTransfer(ids, gSelf.model.delegationId);
                        });
                    } else {
                        completeTransfer(ids, gSelf.model.delegationId);
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }
        });
        $("#btnInboxTransfer").on('click', function () {
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
            if (checkedRows.length > 0) {
                var ids = new Array();
                checkedRows.each(function (index, obj) {
                    ids.push(obj.getAttribute('data-id'));
                });
                var allCced = true;
                var allReadOnly = true;
                var allSelectedData = [];
                var broadcastIds = [];
                var cCedAndNotBroadcast = false;
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $("input[type='hidden'][data-id='" + ids[i] + "']").val();
                    if (selectedRowData && selectedRowData !== "") {
                        selectedRowData = JSON.parse(selectedRowData);
                        let category = $.grep(gSelf.model.categories, function (e) {
                            return e.id === selectedRowData.categoryId;
                        });
                        if (allCced && (!selectedRowData.cced || category[0].isInternalBroadcast)) {
                            allCced = false;
                        }
                        if (selectedRowData.cced && !category[0].isInternalBroadcast) {
                            cCedAndNotBroadcast = true;
                        }
                        if ((!selectedRowData.cced || category[0].isInternalBroadcast) && !selectedRowData.isLocked) {
                            allReadOnly = false;
                            allSelectedData.push(selectedRowData);
                            if (category[0].isInternalBroadcast) {
                                broadcastIds.push(Number(ids[i]));
                            }
                        } else if (selectedRowData.isLocked) {
                            var delegatedUser = gSelf.model.delegationId !== null ? new DelegationUsers().getById(Number(gSelf.model.delegationId)) : null; //$("#hdDelegatedUserIds").val().split(window.Splitter);
                            var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                            if ((selectedRowData.ownerUserId !== null && selectedRowData.ownerUserId === Number($("#hdUserId").val()) && gSelf.model.delegationId === null)
                                || (selectedRowData.ownerDelegatedUserId !== null && selectedRowData.ownerDelegatedUserId === Number($("#hdUserId").val())
                                    && delegatedUserId === selectedRowData.ownerUserId && gSelf.model.delegationId !== null)
                                || (selectedRowData.ownerUserId !== null && delegatedUserId === Number(selectedRowData.ownerUserId) && gSelf.model.delegationId !== null)) {
                                allReadOnly = false;
                                allSelectedData.push(selectedRowData);
                                if (category[0].isInternalBroadcast) {
                                    broadcastIds.push(Number(ids[i]));
                                }
                            }
                        }
                    }
                }
                if (allCced || allReadOnly) {
                    if (allCced) {
                        Common.alertMsg(Resources.AllSelectedItemsCCed);
                    } else {
                        Common.alertMsg(Resources.AllSelectedItemsAreReadOnly);
                    }
                } else {
                    if (allSelectedData.length > 0) {
                        var nonBroadcastIds = $.grep(allSelectedData, function (value) {
                            return $.inArray(value.id, broadcastIds) < 0;
                        });
                        if (nonBroadcastIds.length == 0) {
                            if (cCedAndNotBroadcast) {
                                Common.alertMsg(Resources.AllSelectedItemsAreBroadcastAndCarbonCopies);
                                return;
                            } else {
                                Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
                                return;
                            }
                        }
                        var dataSelected = allSelectedData.map(t => t.toStructureId).filter(function (value, index, array) {
                            return array.indexOf(value) === index;
                        });
                        if (window.EnableSendingRules === "True" && dataSelected.length > 1) {
                            Common.alertMsg(Resources.AllSelectedItemsHaveDifferentSender);
                            return;
                        }
                        var callback = function (data) {
                            var arrayOfTransfers = [];
                            var transferToStructures = [], transferToStructureIds = [];
                            var ccedTransfer = 0;
                            var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                            var hasPrivacyLevel = true;
                            var htmlPrivacy = Resources.NoUserWithSelectedPrivacy;
                            var structureExist = data.some(a => a.toStructureId !== null && a.toUserId === null);
                            for (var i = 0; i < data.length; i++) {
                                var currentPurpose = $.grep(purposes, function (e) {
                                    return e.id.toString() === data[i].purposeId;
                                });
                                if (currentPurpose[0].cCed === true) {
                                    ccedTransfer++;
                                }
                                if (data[i].toUserId === null) {
                                    transferToStructures.push({ dataId: data[i].toStructureId, text: data[i].name });
                                    transferToStructureIds.push(data[i].toStructureId);
                                }
                                for (var j = 0; j < allSelectedData.length; j++) {
                                    arrayOfTransfers.push({
                                        toStructureId: data[i].toStructureId,
                                        toUserId: data[i].toUserId,
                                        name: data[i].name,
                                        dueDate: data[i].dueDate,
                                        purposeId: data[i].purposeId,
                                        instruction: data[i].instruction,
                                        cced: data[i].cced,
                                        fromStructureId: allSelectedData[j].toStructureId,
                                        parentTransferId: allSelectedData[j].id,
                                        isStructure: data[i].toUserId === null,
                                        documentId: allSelectedData[j].documentId,
                                        privacyId: allSelectedData[j].privacyId,
                                        referenceNumber: allSelectedData[j].referenceNumber
                                    });
                                }
                            }
                            var userStructureIds = $("#hdStructureIds").val();
                            var message = SendTransferModal.checkAbilityToSend(transferToStructureIds.length === 0 ? userStructureIds.split(window.Seperator) : transferToStructureIds, transferToStructures, gSelf.model.selectedPrivacy, gSelf.model.privacies, null, false);
                            if (message === "error") {
                                return;
                            }
                            for (var i = 0; i < arrayOfTransfers.length; i++) {
                                if (arrayOfTransfers[i].toUserId !== null) {
                                    var userObj = new IdentityService().getFullUser(arrayOfTransfers[i].toUserId);
                                    if (userObj !== null) {
                                        var attributePrivacy = $.grep(userObj.attributes, function (e) {
                                            return e.text === window.UserPrivacy ? e.value : 0;
                                        });
                                    }
                                    if (attributePrivacy !== null && attributePrivacy.length > 0) {
                                        if (arrayOfTransfers[i].privacyId > parseInt(attributePrivacy[0].value)) {
                                            hasPrivacyLevel = false;
                                            htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
                                            if (arrayOfTransfers[i].referenceNumber) {
                                                htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
                                            }
                                        }
                                    } else {
                                        hasPrivacyLevel = false;
                                        htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
                                        if (arrayOfTransfers[i].referenceNumber) {
                                            htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
                                        }
                                    }
                                }
                            }
                            if (!hasPrivacyLevel) {
                                message += (message !== "" ? " \n " : "") + htmlPrivacy;
                            }
                            if (message !== "") {
                                if (!structureExist) {
                                    Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                                        SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, gSelf.model.delegationId, true, true, refreshInboxList);
                                    }, function () {
                                    });
                                }
                                else {
                                    if (window.SendWithoutStructureReceiverOrPrivacyLevel === "True") {
                                        Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                                            SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, gSelf.model.delegationId, true, true, refreshInboxList);
                                        }, function () {
                                        });
                                    } else {
                                        Common.alertMsg(message);
                                    }
                                }
                            } else {
                                if (ccedTransfer === data.length) {
                                    Common.showConfirmCcedMsg(Resources.AllSelectedTransfersAreCCconfirmation, function () {
                                        SendTransferModal.transfer(transferComponent, arrayOfTransfers, true, false, true, gSelf.model.delegationId, true, true, refreshInboxList);
                                    }, function () {
                                        SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, true, true, gSelf.model.delegationId, true, true, refreshInboxList);
                                    });
                                } else {
                                    SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, gSelf.model.delegationId, true, true, refreshInboxList);
                                }
                            }
                        };
                        let modalWrapper = $(".modal-window");
                        var transferComponent = new Transfer(dataSelected[0], $("#hdUserId").val(), $("#hdStructureIds").val().split(window.Seperator),
                            window.IsStructureSender === "True", window.EnableSendingRules === "True", window.EnableTransferToUsers === "True", gself.model.documentDueDate, gSelf.model.delegationId,
                            callback, modalWrapper);
                        transferComponent.render();
                        $('.modalTransfer').modal('show');
                        $(".modalTransfer").off("hidden.bs.modal");
                        $(".modalTransfer").off("shown.bs.modal");
                        $('.modalTransfer').on('hidden.bs.modal', function () {
                            $(".modalTransfer").parent().remove();
                            swal.close();
                        });
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }
        });
        $("#btnInboxDismissCopy").on('click', function () {
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
            if (checkedRows.length > 0) {
                var ids = new Array();
                checkedRows.each(function (index, obj) {
                    ids.push(obj.getAttribute('data-id'));
                });
                var atLeastOneCced = false;
                var dismissIds = [];
                var broadcastIds = [];
                var allSelectedData = [];
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $("input[type='hidden'][data-id='" + ids[i] + "']").val();
                    if (selectedRowData && selectedRowData !== "") {
                        selectedRowData = JSON.parse(selectedRowData);
                        if (selectedRowData.cced) {
                            dismissIds.push(Number(ids[i]));
                            allSelectedData.push(selectedRowData);

                            let category = $.grep(gSelf.model.categories, function (e) {
                                return e.id === selectedRowData.categoryId;
                            });
                            if (category[0].isInternalBroadcast) {
                                broadcastIds.push(Number(ids[i]));
                            }
                            if (!atLeastOneCced && !category[0].isInternalBroadcast) {
                                atLeastOneCced = true;
                            }
                        }
                    }
                }
                var nonBroadcastIds = $.grep(dismissIds, function (value) {
                    return $.inArray(Number(value), broadcastIds) < 0;
                });

                if (!atLeastOneCced && broadcastIds.length !== ids.length) {
                    Common.alertMsg(Resources.AllSelectedItemsNotCCed);
                } else if (nonBroadcastIds.length === 0) {
                    Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
                } else {
                    if (dismissIds.length > 0) {
                        if (window.EnableConfirmationMessage === "True") {
                            Common.showConfirmMsg(Resources.DismissCarbonCopyConfirmation, function () {
                                dismissCarbonCopy(dismissIds, gSelf.model.delegationId, allSelectedData);
                            });
                        } else {
                            dismissCarbonCopy(dismissIds, gSelf.model.delegationId, allSelectedData);
                        }
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }
        });
        $(document).click(function (e) {
            if ($(e.target).hasClass("select2-selection__choice__remove") || e.target.tagName.toLowerCase() === "body") {
                return;
            }
        });
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        SecurityMatrix.initContextMenuVip(securityMatrix, gSelf.model.nodeId);
        SecurityMatrix.initToolbarMenuVip(securityMatrix, gSelf.model.nodeId, 'inboxListContainer');

        $('.filterInfoDiv').draggable({ containment: 'window', cancel: '.cancelDrag' });
        $("#btnOpenSearchInboxModal").on("click", function () {
            if (window.language == "ar") {
                if ($(".vipDetailsPanel").css('display') == 'none') {
                    var position = $("#btnOpenSearchInboxModal").position().left + $("#btnOpenSearchInboxModal").width() + 970;
                    $(".filterInfoDiv").attr('style', 'right:' + position + 'px;top:170px;');
                }
                else
                    $(".filterInfoDiv").attr('style', 'right:' + $("#btnOpenSearchInboxModal").position().left + $("#btnOpenSearchInboxModal").width() + 'px;top:170px;');
            }
            else {
                if ($(".vipDetailsPanel").css('display') == 'none') {
                    var position = $("#btnOpenSearchInboxModal").position().left - 220;
                    $(".filterInfoDiv").attr('style', 'left:' + position + 'px;top:170px;');

                }
                else
                    $(".filterInfoDiv").attr('style', 'left:' + $("#btnOpenSearchInboxModal").position().left + 'px;top:170px;');

            }
            if ($(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").removeClass("hidden");
            } else {
                $(".filterInfoDiv").addClass("hidden");
            }
        });

        $("#btnFilterCloseIcon").on("click", function () {
            $(".filterInfoDiv").addClass("hidden");
        });
        $('#filtersContainer input').off('keydown');
        $('#filtersContainer input').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 13) {
                $("#btnFilterInboxSearch").trigger('click');
            }
        });


        $("#liInbox2").on("click", function () {
            if ($(".vipDetailsPanel").css('display') == 'block') {
                // If the panel is currently displayed, hide it and remove classes
                $(".vipDetailsPanel").css('display', 'none');
                $(".vipCorrLeftPanel").removeClass("col-lg-3 col-md-4 col-sm-4");
            } else {
                // If the panel is currently hidden, show it and add classes
                $(".vipDetailsPanel").css('display', 'block');
                $(".vipCorrLeftPanel").addClass("col-lg-3 col-md-4 col-sm-4");
            }
        });
     


    }
}
export default { VipDocumentInbox, VipDocumentInboxView };