﻿var CustomDatatableReport = (function (E) {
    var gArabic = /[\u0600-\u06FF]/;
    var gLetterArray = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"];

    E = {};
    E.getTodayString = function () {
        var today = DateConverter.toHijriFormated(new Date(), null, window.CalendarType);
        if (typeof (today) != 'string') {
            var dd = String(today.getDate()).padStart(2, '0');
            var mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
            var yyyy = today.getFullYear();

            today = dd + '/' + mm + '/' + yyyy;
        }
        return today;
    }
    E.GetLogoBase64String = function () {
        var imgElement = document.getElementById('logoImg');
        var canvas = document.createElement('canvas');
        canvas.width = imgElement.width;
        canvas.height = imgElement.height;
        var ctx = canvas.getContext('2d');
        ctx.drawImage(imgElement, 0, 0);
        return canvas.toDataURL('image/png')
    }

    E.draftReport = {};
    E.draftReport.print = function (win) {
        const doc = win.document;

        const table = doc.querySelector("table");

        if (table) {
            const thead = table.querySelector("thead tr");
            const tbody = table.querySelector("tbody");

            const visibleHeaders = Array.from(thead.querySelectorAll("th")).filter(th => th.innerText.trim() !== "").length;
            const dynamicWidth = visibleHeaders > 0 ? 100 / visibleHeaders + "%" : "auto";

            thead.querySelectorAll("th").forEach((th, colIndex) => {
                const hasHeaderContent = th.innerText.trim() !== "";

                if (hasHeaderContent) {
                    th.style.width = dynamicWidth;

                } else {
                    th.style.display = "none";
                }

                tbody.querySelectorAll("tr").forEach(row => {
                    const td = row.cells[colIndex];
                    if (td) {
                        if (hasHeaderContent) {
                            td.style.width = dynamicWidth;

                        } else {
                            td.style.display = "none";
                        }
                    }
                });
            });

            $(doc.body).prepend(
                '<table style="border:none;width:100%; margin-bottom: 10px;"><tr>' +
                '<td style="width:' + dynamicWidth + '">' + document.getElementById("logoImg").outerHTML + '</td>' +
                '<td style="width:' + dynamicWidth + '"><span>' + E.getTodayString() + '</span></td>' +
                '</tr></table>'
            );
        }
    };

    E.draftReport.excelHTML5 = function (xlsx) {
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];
        //$('row:even c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');


        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
    }
    E.draftReport.pdfHTML5 = function (doc) {
        // Check if the text is Arabic and adjust content and alignment
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t'); // Reverse words for Arabic

            // Adjust table body rows for RTL
            for (var i = 0; i < doc.content[1].table.body.length; i++) {
                var row = doc.content[1].table.body[i];
                row.reverse(); // Reverse row for RTL
                for (var col = 0; col < row.length; col++) {
                    if (gArabic.test(row[col].text)) {
                        row[col].text = row[col].text.replace(/  +/g, ' '); // Remove extra spaces
                        row[col].text = row[col].text.split(' ').join('\n'); // Replace spaces with line breaks
                    }
                }
            }
        }

        // Set up fonts and default style
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            }
        };

        doc.defaultStyle = {
            font: 'Cairo',
            fontSize: 10
        };

        // Step 1: Count non-empty cells in the first row to determine dynamic column widths
        let nonEmptyTextCount = 0;
        doc.content[1].table.body[0].forEach((col) => {
            if (col.text && col.text.trim() !== "") {
                nonEmptyTextCount++;
            }
        });

        const columnWidths = [];

        // Step 2: Calculate the width for each column based on non-empty columns
        const columnWidthPercentage = 100 / nonEmptyTextCount;

        // Ensure all rows have the same number of cells as the header
        doc.content[1].table.body.forEach(row => {
            while (row.length < nonEmptyTextCount) {
                row.push({ text: "" });
            }
        });

        // Step 3: Set the calculated widths to the table
        doc.content[1].table.body[0].forEach((col, index) => {
            if (col.text && col.text.trim() !== "") {
                // Allocate width if column text exists and is non-empty
                columnWidths.push(columnWidthPercentage + "%");
            } else {
                // Allocate 0% width if column text is empty
                columnWidths.push("0%");

                // Check the corresponding cell in the next row (body[1]) and clear its text
                if (doc.content[1].table.body[1] && doc.content[1].table.body[1][index]) {
                    doc.content[1].table.body[1][index].text = "";
                }
            }
        });
        // Apply alternating gray and white background to all content cells except the header row
        doc.content[1].table.body.slice(1).forEach((row, index) => { // Skip the first row (header)
            const rowColor = index % 2 === 0 ? '#f3f3f3' : '#ffffff'; // Alternate colors: gray and white
            row.forEach(cell => {
                cell.fillColor = rowColor; // Set background color for each cell in the row
                cell.alignment = 'center'; // Set content cell alignment to center
            });
        });

        // Apply calculated widths to the table
        doc.content[1].table.widths = columnWidths;

        const textStyle = {};
        function getTextWidth(text) {
            const canvas = document.createElement("canvas");
            const context = canvas.getContext("2d");
            return context.measureText(text).width;
        }

        // Handle text within table cells for proper alignment and formatting
        doc.content[1].table.body[1].forEach(cell => {
            if (cell.text) {
                const textWidth = getTextWidth(cell.text);
                const textWidthPercentage = (textWidth / 100) * 100;

                // Check if text exceeds the available column width
                if (textWidthPercentage > columnWidthPercentage) {
                   

                    cell.style = {
                        maxWidth: columnWidthPercentage + '%',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis'
                    };
                } else {
                    cell.style = {
                        width: columnWidthPercentage + '%',
                    };
                }
            }
        });

        // Set header and footer alignment styles
        doc.styles.tableBodyEven.alignment = 'center';
        doc.styles.tableBodyOdd.alignment = 'center';
        doc.styles.tableHeader.fontSize = 10;
        doc.defaultStyle.fontSize = 8;

        // Add header logo and date with adjusted alignment
        doc.content.splice(0, 0, {
            table: {
                headerRows: 0,
                body: [
                    [{ image: E.GetLogoBase64String(), alignment: 'left' }], // Adjust as needed
                    [{ text: E.getTodayString(), alignment: 'right' }] // Adjust as needed
                ],
                widths: ['100%']
            },
            layout: 'noBorders'
        });
    };

    E.reportsent = {};
    E.reportsent.print = function (win) {
        const doc = win.document;

        const table = doc.querySelector("table");

        if (table) {
            const thead = table.querySelector("thead tr");
            const tbody = table.querySelector("tbody");

            const visibleHeaders = Array.from(thead.querySelectorAll("th")).filter(th => th.innerText.trim() !== "").length;
            const dynamicWidth = visibleHeaders > 0 ? 100 / visibleHeaders + "%" : "auto";

            thead.querySelectorAll("th").forEach((th, colIndex) => {
                const hasHeaderContent = th.innerText.trim() !== "";

                if (hasHeaderContent) {
                    th.style.width = dynamicWidth;

                } else {
                    th.style.display = "none";
                }

                tbody.querySelectorAll("tr").forEach(row => {
                    const td = row.cells[colIndex];
                    if (td) {
                        if (hasHeaderContent) {
                            td.style.width = dynamicWidth;

                        } else {
                            td.style.display = "none";
                        }
                    }
                });
            });

            $(doc.body).prepend(
                '<table style="border:none;width:100%; margin-bottom: 10px;"><tr>' +
                '<td style="width:' + dynamicWidth + '">' + document.getElementById("logoImg").outerHTML + '</td>' +
                '<td style="width:' + dynamicWidth + '"><span>' + E.getTodayString() + '</span></td>' +
                '</tr></table>'
            );
        }
    };


    E.reportsent.excelHTML5 = function (xlsx) {
      
        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
    }
    E.reportsent.pdfHTML5 = function (doc) {
        // Check if the text is Arabic and adjust content and alignment
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t'); // Reverse words for Arabic

            // Adjust table body rows for RTL
            for (var i = 0; i < doc.content[1].table.body.length; i++) {
                var row = doc.content[1].table.body[i];
                row.reverse(); // Reverse row for RTL
                for (var col = 0; col < row.length; col++) {
                    if (gArabic.test(row[col].text)) {
                        row[col].text = row[col].text.replace(/  +/g, ' '); // Remove extra spaces
                        row[col].text = row[col].text.split(' ').join('\n'); // Replace spaces with line breaks
                    }
                }
            }
        }

        // Set up fonts and default style
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            }
        };

        doc.defaultStyle = {
            font: 'Cairo',
            fontSize: 10
        };

        // Step 1: Count non-empty cells in the first row to determine dynamic column widths
        let nonEmptyTextCount = 0;
        doc.content[1].table.body[0].forEach((col) => {
            if (col.text && col.text.trim() !== "") {
                nonEmptyTextCount++;
            }
        });

        const columnWidths = [];

        // Step 2: Calculate the width for each column based on non-empty columns
        const columnWidthPercentage = 100 / nonEmptyTextCount;

        // Ensure all rows have the same number of cells as the header
        doc.content[1].table.body.forEach(row => {
            while (row.length < nonEmptyTextCount) {
                row.push({ text: "" });
            }
        });

        // Step 3: Set the calculated widths to the table
        doc.content[1].table.body[0].forEach((col, index) => {
            if (col.text && col.text.trim() !== "") {
                // Allocate width if column text exists and is non-empty
                columnWidths.push(columnWidthPercentage + "%");
            } else {
                // Allocate 0% width if column text is empty
                columnWidths.push("0%");

                // Check the corresponding cell in the next row (body[1]) and clear its text
                if (doc.content[1].table.body[1] && doc.content[1].table.body[1][index]) {
                    doc.content[1].table.body[1][index].text = "";
                }
            }
        });
        // Apply alternating gray and white background to all content cells except the header row
        doc.content[1].table.body.slice(1).forEach((row, index) => { // Skip the first row (header)
            const rowColor = index % 2 === 0 ? '#f3f3f3' : '#ffffff'; // Alternate colors: gray and white
            row.forEach(cell => {
                cell.fillColor = rowColor; // Set background color for each cell in the row
                cell.alignment = 'center'; // Set content cell alignment to center
            });
        });

        // Apply calculated widths to the table
        doc.content[1].table.widths = columnWidths;

        const textStyle = {};
        function getTextWidth(text) {
            const canvas = document.createElement("canvas");
            const context = canvas.getContext("2d");
            return context.measureText(text).width;
        }

        // Handle text within table cells for proper alignment and formatting
        // Check if the array exists and is an array

        if (Array.isArray(doc.content[1].table.body[1])) {
            doc.content[1].table.body[1].forEach(cell => {
                if (cell.text) {
                    const textWidth = getTextWidth(cell.text);
                    const textWidthPercentage = (textWidth / 100) * 100;

                    // Check if text exceeds the available column width
                    if (textWidthPercentage > columnWidthPercentage) {
                     

                        cell.style = {
                            maxWidth: columnWidthPercentage + '%',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis'
                        };
                    } else {
                        cell.style = {
                            width: columnWidthPercentage + '%',
                        };
                    }
                }
            });

            doc.styles.tableBodyEven.alignment = 'center';
            doc.styles.tableBodyOdd.alignment = 'center';
            doc.styles.tableHeader.fontSize = 10;
            doc.defaultStyle.fontSize = 8;

            // Add header logo and date with adjusted alignment
            doc.content.splice(0, 0, {
                table: {
                    headerRows: 0,
                    body: [
                        [{ image: E.GetLogoBase64String(), alignment: 'left' }], // Adjust as needed
                        [{ text: E.getTodayString(), alignment: 'right' }] // Adjust as needed
                    ],
                    widths: ['100%']
                },
                layout: 'noBorders'


            })
        };




    };

    E.reportinprogresstransfers = {};
    E.reportinprogresstransfers.print = function (win) {
        $(win.document.body).prepend('<table style="border:none;width:100%"><tr>' +
            '<td style="width:60%">' + document.getElementById("logoImg").outerHTML + '</td>' +
            '<td style="width:40%"><span>' + E.getTodayString() + '</span></div>');
    };
    E.reportinprogresstransfers.excelHTML5 = function (xlsx) {
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];
        //$('row:even c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
    }
    E.reportInbox = {};

        
    E.reportInbox.print = function (win) {
        const doc = win.document;

        const table = doc.querySelector("table");

        if (table) {
            const thead = table.querySelector("thead tr");
            const tbody = table.querySelector("tbody");

            const visibleHeaders = Array.from(thead.querySelectorAll("th")).filter(th => th.innerText.trim() !== "").length;
            const dynamicWidth = visibleHeaders > 0 ? 100 / visibleHeaders + "%" : "auto";

            thead.querySelectorAll("th").forEach((th, colIndex) => {
                const hasHeaderContent = th.innerText.trim() !== "";

                if (hasHeaderContent) {
                    th.style.width = dynamicWidth;
               
                } else {
                    th.style.display = "none";
                }

                tbody.querySelectorAll("tr").forEach(row => {
                    const td = row.cells[colIndex];
                    if (td) {
                        if (hasHeaderContent) {
                            td.style.width = dynamicWidth;
                         
                        } else {
                            td.style.display = "none";
                        }
                    }
                });
            });

            $(doc.body).prepend(
                '<table style="border:none;width:100%; margin-bottom: 10px;"><tr>' +
                '<td style="width:' + dynamicWidth + '">' + document.getElementById("logoImg").outerHTML + '</td>' +
                '<td style="width:' + dynamicWidth + '"><span>' + E.getTodayString() + '</span></td>' +
                '</tr></table>'
            );
        }
    };

    E.reportInbox.excelHTML5 = function (xlsx) {
     
       
        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Find column letter for "Id" ===
        let idColLetter = null;

        const headerCells = $('sheetData row:first c', sheet);
        headerCells.each(function () {
            const cell = $(this);
            let cellText = '';

            if (cell.attr('t') === 'inlineStr') {
                cellText = $('is t', cell).text().trim().toLowerCase();
            } else {
                cellText = $('v', cell).text().trim().toLowerCase();
            }

            if (cellText === 'id') {
                const cellRef = cell.attr('r'); // e.g., "B1"
                idColLetter = cellRef.replace(/[0-9]/g, ''); // => "B"
                return false; // break loop
            }
        });

        if (!idColLetter) return; // nothing to remove

        // === Remove all cells in the Id column
        $('sheetData row c', sheet).each(function () {
            const cellRef = $(this).attr('r'); // e.g., "B2"
            const colLetter = cellRef.replace(/[0-9]/g, '');
            if (colLetter === idColLetter) {
                $(this).remove();
            }
        });
       
        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

    }




    E.reportInbox.pdfHTML5 = function (doc) {
        const headerRow = doc.content[1].table.body[0];
        const idIndex = headerRow.findIndex(cell => cell.text === 'Id');

        if (idIndex !== -1) {
            // Remove 'Id' from the header row
            headerRow.splice(idIndex, 1);

            // Also remove 'Id' column from all other rows
            for (let i = 1; i < doc.content[1].table.body.length; i++) {
                doc.content[1].table.body[i].splice(idIndex, 1);
            }
        }
        // Check if the text is Arabic and adjust content and alignment
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t'); // Reverse words for Arabic

            // Adjust table body rows for RTL
            for (var i = 0; i < doc.content[1].table.body.length; i++) {
                var row = doc.content[1].table.body[i];
                row.reverse(); // Reverse row for RTL
                for (var col = 0; col < row.length; col++) {
                    if (gArabic.test(row[col].text)) {
                        row[col].text = row[col].text.replace(/  +/g, ' '); // Remove extra spaces
                        row[col].text = row[col].text.split(' ').join('\n'); // Replace spaces with line breaks
                    }
                }
            }
        }

        // Set up fonts and default style
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            }
        };

        doc.defaultStyle = {
            font: 'Cairo',
            fontSize: 10
        };

        // Filter out empty columns in the header
        const filteredHeader = doc.content[1].table.body[0].filter(col => col.text && col.text.trim() !== "");
        const nonEmptyTextCount = filteredHeader.length;

        const columnWidths = [];
        const columnWidthPercentage = 100 / nonEmptyTextCount;

        // Update table body to exclude empty columns
        doc.content[1].table.body = doc.content[1].table.body.map(row => {
            return row.filter((col, index) => {
                return doc.content[1].table.body[0][index].text && doc.content[1].table.body[0][index].text.trim() !== "";
            });
        });

        // Calculate widths for filtered columns
        filteredHeader.forEach(() => {
            columnWidths.push(columnWidthPercentage + "%");
        });

        // Apply alternating background colors to rows
        doc.content[1].table.body.slice(1).forEach((row, index) => {
            const rowColor = index % 2 === 0 ? '#f3f3f3' : '#ffffff'; // Alternate colors
            row.forEach(cell => {
                cell.fillColor = rowColor; // Set background color
                cell.alignment = 'center'; // Center-align content
            });
        });

        // Apply calculated widths to the table
        doc.content[1].table.widths = columnWidths;

        // Handle text formatting in table cells
        function getTextWidth(text) {
            const canvas = document.createElement("canvas");
            const context = canvas.getContext("2d");
            return context.measureText(text).width;
        }

        if (Array.isArray(doc.content[1].table.body[1])) {
            doc.content[1].table.body[1].forEach(cell => {
                if (cell.text) {
                    const textWidth = getTextWidth(cell.text);
                    const textWidthPercentage = (textWidth / 100) * 100;

                    if (textWidthPercentage > columnWidthPercentage) {
                        if (cell.text.includes(',')) {
                            cell.text = cell.text.replace(/,/g, ' ');
                        }

                        cell.style = {
                            maxWidth: columnWidthPercentage + '%',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis'
                        };
                    } else {
                        cell.style = {
                            width: columnWidthPercentage + '%',
                        };
                    }
                }
            });
        }

        // Add header logo and date
        doc.content.splice(0, 0, {
            table: {
                headerRows: 0,
                body: [
                    [{ image: E.GetLogoBase64String(), alignment: 'left' }],
                    [{ text: E.getTodayString(), alignment: 'right' }]
                ],
                widths: ['100%']
            },
            layout: 'noBorders'
        });
    };

  
    E.reportclosed = {};

    E.reportclosed.print = function (win) {
        const doc = win.document;

        const table = doc.querySelector("table");

        if (table) {
            const thead = table.querySelector("thead tr");
            const tbody = table.querySelector("tbody");

            const visibleHeaders = Array.from(thead.querySelectorAll("th")).filter(th => th.innerText.trim() !== "").length;
            const dynamicWidth = visibleHeaders > 0 ? 100 / visibleHeaders + "%" : "auto";

            thead.querySelectorAll("th").forEach((th, colIndex) => {
                const hasHeaderContent = th.innerText.trim() !== "";

                if (hasHeaderContent) {
                    th.style.width = dynamicWidth;

                } else {
                    th.style.display = "none";
                }

                tbody.querySelectorAll("tr").forEach(row => {
                    const td = row.cells[colIndex];
                    if (td) {
                        if (hasHeaderContent) {
                            td.style.width = dynamicWidth;

                        } else {
                            td.style.display = "none";
                        }
                    }
                });
            });

            $(doc.body).prepend(
                '<table style="border:none;width:100%; margin-bottom: 10px;"><tr>' +
                '<td style="width:' + dynamicWidth + '">' + document.getElementById("logoImg").outerHTML + '</td>' +
                '<td style="width:' + dynamicWidth + '"><span>' + E.getTodayString() + '</span></td>' +
                '</tr></table>'
            );
        }
    };


    E.reportclosed.excelHTML5 = function (xlsx) {
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];
        //$('row:even c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
    }
    E.reportclosed.pdfHTML5 = function (doc) {
        // Check if the text is Arabic and adjust content and alignment
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t'); // Reverse words for Arabic

            // Adjust table body rows for RTL
            for (var i = 0; i < doc.content[1].table.body.length; i++) {
                var row = doc.content[1].table.body[i];
                row.reverse(); // Reverse row for RTL
                for (var col = 0; col < row.length; col++) {
                    if (gArabic.test(row[col].text)) {
                        row[col].text = row[col].text.replace(/  +/g, ' '); // Remove extra spaces
                        row[col].text = row[col].text.split(' ').join('\n'); // Replace spaces with line breaks
                    }
                }
            }
        }

        // Set up fonts and default style
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            }
        };

        doc.defaultStyle = {
            font: 'Cairo',
            fontSize: 10
        };

        // Step 1: Count non-empty cells in the first row to determine dynamic column widths
        let nonEmptyTextCount = 0;
        doc.content[1].table.body[0].forEach((col) => {
            if (col.text && col.text.trim() !== "") {
                nonEmptyTextCount++;
            }
        });

        const columnWidths = [];

        // Step 2: Calculate the width for each column based on non-empty columns
        const columnWidthPercentage = 100 / nonEmptyTextCount;

        // Ensure all rows have the same number of cells as the header
        doc.content[1].table.body.forEach(row => {
            while (row.length < nonEmptyTextCount) {
                row.push({ text: "" });
            }
        });

        // Step 3: Set the calculated widths to the table
        doc.content[1].table.body[0].forEach((col, index) => {
            if (col.text && col.text.trim() !== "") {
                // Allocate width if column text exists and is non-empty
                columnWidths.push(columnWidthPercentage + "%");
            } else {
                // Allocate 0% width if column text is empty
                columnWidths.push("0%");

                // Check the corresponding cell in the next row (body[1]) and clear its text
                if (doc.content[1].table.body[1] && doc.content[1].table.body[1][index]) {
                    doc.content[1].table.body[1][index].text = "";
                }
            }
        });
        // Apply alternating gray and white background to all content cells except the header row
        doc.content[1].table.body.slice(1).forEach((row, index) => { // Skip the first row (header)
            const rowColor = index % 2 === 0 ? '#f3f3f3' : '#ffffff'; // Alternate colors: gray and white
            row.forEach(cell => {
                cell.fillColor = rowColor; // Set background color for each cell in the row
                cell.alignment = 'center'; // Set content cell alignment to center
            });
        });

        // Apply calculated widths to the table
        doc.content[1].table.widths = columnWidths;

        const textStyle = {};
        function getTextWidth(text) {
            const canvas = document.createElement("canvas");
            const context = canvas.getContext("2d");
            return context.measureText(text).width;
        }

        // Handle text within table cells for proper alignment and formatting
        // Check if the array exists and is an array

        if (Array.isArray(doc.content[1].table.body[1])) {
            doc.content[1].table.body[1].forEach(cell => {
                if (cell.text) {
                    const textWidth = getTextWidth(cell.text);
                    const textWidthPercentage = (textWidth / 100) * 100;

                    // Check if text exceeds the available column width
                    if (textWidthPercentage > columnWidthPercentage) {
                     

                        cell.style = {
                            maxWidth: columnWidthPercentage + '%',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis'
                        };
                    } else {
                        cell.style = {
                            width: columnWidthPercentage + '%',
                        };
                    }
                }
            });

            doc.styles.tableBodyEven.alignment = 'center';
            doc.styles.tableBodyOdd.alignment = 'center';
            doc.styles.tableHeader.fontSize = 10;
            doc.defaultStyle.fontSize = 8;

            // Add header logo and date with adjusted alignment
            doc.content.splice(0, 0, {
                table: {
                    headerRows: 0,
                    body: [
                        [{ image: E.GetLogoBase64String(), alignment: 'left' }], // Adjust as needed
                        [{ text: E.getTodayString(), alignment: 'right' }] // Adjust as needed
                    ],
                    widths: ['100%']
                },
                layout: 'noBorders'


            })
        };


        //doc.content[1].table.body[1].forEach(cell => {
        //    if (cell.text) {
        //        const textWidth = getTextWidth(cell.text);
        //        const textWidthPercentage = (textWidth / 100) * 100;

        //        // Check if text exceeds the available column width
        //        if (textWidthPercentage > columnWidthPercentage) {
        //            if (cell.text.includes(',')) {
        //                cell.text = cell.text.replace(/,/g, ' ');
        //            }

        //            cell.style = {
        //                maxWidth: columnWidthPercentage + '%',
        //                overflow: 'hidden',
        //                whiteSpace: 'nowrap',
        //                textOverflow: 'ellipsis'
        //            };
        //        } else {
        //            cell.style = {
        //                width: columnWidthPercentage + '%',
        //            };
        //        }
        //    }
        //});

        // Set header and footer alignment styles

    };

  

    E.reportcompleted = {};
    E.reportcompleted.print = function (win) {
        const doc = win.document;

        const table = doc.querySelector("table");

        if (table) {
            const thead = table.querySelector("thead tr");
            const tbody = table.querySelector("tbody");

            const visibleHeaders = Array.from(thead.querySelectorAll("th")).filter(th => th.innerText.trim() !== "").length;
            const dynamicWidth = visibleHeaders > 0 ? 100 / visibleHeaders + "%" : "auto";

            thead.querySelectorAll("th").forEach((th, colIndex) => {
                const hasHeaderContent = th.innerText.trim() !== "";

                if (hasHeaderContent) {
                    th.style.width = dynamicWidth;

                } else {
                    th.style.display = "none";
                }

                tbody.querySelectorAll("tr").forEach(row => {
                    const td = row.cells[colIndex];
                    if (td) {
                        if (hasHeaderContent) {
                            td.style.width = dynamicWidth;

                        } else {
                            td.style.display = "none";
                        }
                    }
                });
            });

            $(doc.body).prepend(
                '<table style="border:none;width:100%; margin-bottom: 10px;"><tr>' +
                '<td style="width:' + dynamicWidth + '">' + document.getElementById("logoImg").outerHTML + '</td>' +
                '<td style="width:' + dynamicWidth + '"><span>' + E.getTodayString() + '</span></td>' +
                '</tr></table>'
            );
        }
    };


    E.reportcompleted.excelHTML5 = function (xlsx) {
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];
        //$('row:even c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
    }
    E.reportcompleted.pdfHTML5 = function (doc) {
        // Check if the text is Arabic and adjust content and alignment
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t'); // Reverse words for Arabic

            // Adjust table body rows for RTL
            for (var i = 0; i < doc.content[1].table.body.length; i++) {
                var row = doc.content[1].table.body[i];
                row.reverse(); // Reverse row for RTL
                for (var col = 0; col < row.length; col++) {
                    if (gArabic.test(row[col].text)) {
                        row[col].text = row[col].text.replace(/  +/g, ' '); // Remove extra spaces
                        row[col].text = row[col].text.split(' ').join('\n'); // Replace spaces with line breaks
                    }
                }
            }
        }

        // Set up fonts and default style
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            }
        };

        doc.defaultStyle = {
            font: 'Cairo',
            fontSize: 10
        };

        // Step 1: Count non-empty cells in the first row to determine dynamic column widths
        let nonEmptyTextCount = 0;
        doc.content[1].table.body[0].forEach((col) => {
            if (col.text && col.text.trim() !== "") {
                nonEmptyTextCount++;
            }
        });

        const columnWidths = [];

        // Step 2: Calculate the width for each column based on non-empty columns
        const columnWidthPercentage = 100 / nonEmptyTextCount;

        // Ensure all rows have the same number of cells as the header
        doc.content[1].table.body.forEach(row => {
            while (row.length < nonEmptyTextCount) {
                row.push({ text: "" });
            }
        });

        // Step 3: Set the calculated widths to the table
        doc.content[1].table.body[0].forEach((col, index) => {
            if (col.text && col.text.trim() !== "") {
                // Allocate width if column text exists and is non-empty
                columnWidths.push(columnWidthPercentage + "%");
            } else {
                // Allocate 0% width if column text is empty
                columnWidths.push("0%");

                // Check the corresponding cell in the next row (body[1]) and clear its text
                if (doc.content[1].table.body[1] && doc.content[1].table.body[1][index]) {
                    doc.content[1].table.body[1][index].text = "";
                }
            }
        });

        // Apply alternating gray and white background to all content cells except the header row
        doc.content[1].table.body.slice(1).forEach((row, index) => { // Skip the first row (header)
            const rowColor = index % 2 === 0 ? '#f3f3f3' : '#ffffff'; // Alternate colors: gray and white
            row.forEach(cell => {
                cell.fillColor = rowColor; // Set background color for each cell in the row
                cell.alignment = 'center'; // Set content cell alignment to center
            });
        });

        // Apply calculated widths to the table
        doc.content[1].table.widths = columnWidths;

        const textStyle = {};
        function getTextWidth(text) {
            const canvas = document.createElement("canvas");
            const context = canvas.getContext("2d");
            return context.measureText(text).width;
        }

        // Handle text within table cells for proper alignment and formatting
        // Validation check for doc.content[1].table.body[1]
        if (Array.isArray(doc.content[1].table.body[1])) {
            doc.content[1].table.body[1].forEach(cell => {
                if (cell.text) {
                    const textWidth = getTextWidth(cell.text);
                    const textWidthPercentage = (textWidth / 100) * 100;

                    // Check if text exceeds the available column width
                    if (textWidthPercentage > columnWidthPercentage) {
                     
                        cell.style = {
                            maxWidth: columnWidthPercentage + '%',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis'
                        };
                    } else {
                        cell.style = {
                            width: columnWidthPercentage + '%',
                        };
                    }
                }
            });
            // Set header and footer alignment styles
            doc.styles.tableBodyEven.alignment = 'center';
            doc.styles.tableBodyOdd.alignment = 'center';
            doc.styles.tableHeader.fontSize = 10;
            doc.defaultStyle.fontSize = 8;

            // Add header logo and date with adjusted alignment
            doc.content.splice(0, 0, {
                table: {
                    headerRows: 0,
                    body: [
                        [{ image: E.GetLogoBase64String(), alignment: 'left' }], // Adjust as needed
                        [{ text: E.getTodayString(), alignment: 'right' }] // Adjust as needed
                    ],
                    widths: ['100%']
                },
                layout: 'noBorders'
            });

        }


    };
   
    E.reportmyrequest = {};
    E.reportmyrequest.print = function (win) {
        const doc = win.document;

        const table = doc.querySelector("table");

        if (table) {
            const thead = table.querySelector("thead tr");
            const tbody = table.querySelector("tbody");

            const visibleHeaders = Array.from(thead.querySelectorAll("th")).filter(th => th.innerText.trim() !== "").length;
            const dynamicWidth = visibleHeaders > 0 ? 100 / visibleHeaders + "%" : "auto";

            thead.querySelectorAll("th").forEach((th, colIndex) => {
                const hasHeaderContent = th.innerText.trim() !== "";

                if (hasHeaderContent) {
                    th.style.width = dynamicWidth;

                } else {
                    th.style.display = "none";
                }

                tbody.querySelectorAll("tr").forEach(row => {
                    const td = row.cells[colIndex];
                    if (td) {
                        if (hasHeaderContent) {
                            td.style.width = dynamicWidth;

                        } else {
                            td.style.display = "none";
                        }
                    }
                });
            });

            $(doc.body).prepend(
                '<table style="border:none;width:100%; margin-bottom: 10px;"><tr>' +
                '<td style="width:' + dynamicWidth + '">' + document.getElementById("logoImg").outerHTML + '</td>' +
                '<td style="width:' + dynamicWidth + '"><span>' + E.getTodayString() + '</span></td>' +
                '</tr></table>'
            );
        }
    };
    E.reportmyrequest.excelHTML5 = function (xlsx) {
        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // Detect current language, fallback to 'en'
        var lang = window.language || 'en';

        // Check if we need RTL (Arabic or other RTL languages)
        var isRTL = lang.startsWith('ar');

        // Add <sheetViews> with appropriate direction
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // Style rows (optional, for better readability)
        $('row c', sheet).attr('s', '5');
        $('row:first c', sheet).attr('s', '22');
    };

    E.reportmyrequest.pdfHTML5 = function (doc) {
        // Check if the text is Arabic and adjust content and alignment
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t'); // Reverse words for Arabic

            // Adjust table body rows for RTL
            for (var i = 0; i < doc.content[1].table.body.length; i++) {
                var row = doc.content[1].table.body[i];
                row.reverse(); // Reverse row for RTL
                for (var col = 0; col < row.length; col++) {
                    if (gArabic.test(row[col].text)) {
                        row[col].text = row[col].text.replace(/  +/g, ' '); // Remove extra spaces
                        row[col].text = row[col].text.split(' ').join('\n'); // Replace spaces with line breaks
                    }
                }
            }
        }

        // Set up fonts and default style
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            }
        };

        doc.defaultStyle = {
            font: 'Cairo',
            fontSize: 10
        };

        // Step 1: Count non-empty cells in the first row to determine dynamic column widths
        let nonEmptyTextCount = 0;
        doc.content[1].table.body[0].forEach((col) => {
            if (col.text && col.text.trim() !== "") {
                nonEmptyTextCount++;
            }
        });

        const columnWidths = [];

        // Step 2: Calculate the width for each column based on non-empty columns
        const columnWidthPercentage = 100 / nonEmptyTextCount;

        // Ensure all rows have the same number of cells as the header
        doc.content[1].table.body.forEach(row => {
            while (row.length < nonEmptyTextCount) {
                row.push({ text: "" });
            }
        });

        // Step 3: Set the calculated widths to the table
        doc.content[1].table.body[0].forEach((col, index) => {
            if (col.text && col.text.trim() !== "") {
                // Allocate width if column text exists and is non-empty
                columnWidths.push(columnWidthPercentage + "%");
            } else {
                // Allocate 0% width if column text is empty
                columnWidths.push("0%");

                // Check the corresponding cell in the next row (body[1]) and clear its text
                if (doc.content[1].table.body[1] && doc.content[1].table.body[1][index]) {
                    doc.content[1].table.body[1][index].text = "";
                }
            }
        });

        // Apply alternating gray and white background to all content cells except the header row
        doc.content[1].table.body.slice(1).forEach((row, index) => { // Skip the first row (header)
            const rowColor = index % 2 === 0 ? '#f3f3f3' : '#ffffff'; // Alternate colors: gray and white
            row.forEach(cell => {
                cell.fillColor = rowColor; // Set background color for each cell in the row
                cell.alignment = 'center'; // Set content cell alignment to center
            });
        });

        // Apply calculated widths to the table
        doc.content[1].table.widths = columnWidths;

        const textStyle = {};
        function getTextWidth(text) {
            const canvas = document.createElement("canvas");
            const context = canvas.getContext("2d");
            return context.measureText(text).width;
        }

        // Handle text within table cells for proper alignment and formatting
        // Validation check for doc.content[1].table.body[1]
        if (Array.isArray(doc.content[1].table.body[1])) {
            doc.content[1].table.body[1].forEach(cell => {
                if (cell.text) {
                    const textWidth = getTextWidth(cell.text);
                    const textWidthPercentage = (textWidth / 100) * 100;

                    // Check if text exceeds the available column width
                    if (textWidthPercentage > columnWidthPercentage) {


                        cell.style = {
                            maxWidth: columnWidthPercentage + '%',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis'
                        };
                    } else {
                        cell.style = {
                            width: columnWidthPercentage + '%',
                        };
                    }
                }
            });
            // Set header and footer alignment styles
            doc.styles.tableBodyEven.alignment = 'center';
            doc.styles.tableBodyOdd.alignment = 'center';
            doc.styles.tableHeader.fontSize = 10;
            doc.defaultStyle.fontSize = 8;

            // Add header logo and date with adjusted alignment
            doc.content.splice(0, 0, {
                table: {
                    headerRows: 0,
                    body: [
                        [{ image: E.GetLogoBase64String(), alignment: 'left' }], // Adjust as needed
                        [{ text: E.getTodayString(), alignment: 'right' }] // Adjust as needed
                    ],
                    widths: ['100%']
                },
                layout: 'noBorders'
            });

        } 

      
    };
   
    E.reportsnotesfollowup = {};

    E.reportsnotesfollowup.excelHTML5 = function (xlsx) {
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];
        //$('row:even c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
    }
    E.reportinprogresstransfers.pdfHTML5 = function (doc) {
        var logoAlignment = 'left';
        var todayString = 'right';
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            },
            'DejaVuSansCondensed': {
                normal: 'DejaVuSansCondensed.ttf',
                bold: 'DejaVuSansCondensed.ttf',
                italics: 'DejaVuSansCondensed.ttf',
                bolditalics: 'DejaVuSansCondensed.ttf'
            }
        };
        doc.defaultStyle =
        {
            font: 'Cairo',
        };
        doc.content[1].table.body.forEach(function (line, i) {
            if (line[0].style !== 'tableHeader' && line[0].style !== 'tableFooter') {
                line[6].style = line[6].style + "Icon";
            }
        });
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t');
            logoAlignment = 'right';
            todayString = 'left';
        }
        for (var i = 0; i < doc.content[1].table.body.length; i++) {
            var row = doc.content[1].table.body[i];
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            for (var col = 0; col < row.length; col++) {
                if (gArabic.test(row[col].text)) {
                    
                    row[col].text = row[col].text.replace(/  +/g, ' ');
                    row[col].text = row[col].text.split(' ').join('\n');
                }
            }
        }

        doc.content[1].table.widths = ["15%", "15%", "15%", "15%", "15%", "15%", "12%"];
        doc.styles.tableBodyEven.alignment = 'center';
        doc.styles.tableBodyOdd.alignment = 'center';
        doc.styles.tableBodyEvenIcon = {
            fontSize: 14,
            font: 'DejaVuSansCondensed',
            alignment: 'center'
        };
        doc.styles.tableBodyOddIcon = {
            fontSize: 14,
            font: 'DejaVuSansCondensed',
            fillColor: '#F3F3F3',
            alignment: 'center'
        };

        doc.content.splice(0, 0, {
            "table": {
                "headerRows": 0,
                "body": [
                    [
                        {
                            "image": E.GetLogoBase64String(),
                            "alignment": logoAlignment 
                        }
                    ],
                    [                        
                        {
                            "text": E.getTodayString(),
                            alignment: todayString

                        }
                    ]
                ],
                widths: ['100%']
            },
            "layout": "noBorders"
        })
    }


    E.reportcompletedtransfers = {};
    E.reportcompletedtransfers.print = function (win) {
        $(win.document.body).prepend('<table style="border:none;width:100%"><tr>' +
            '<td style="width:60%">' + document.getElementById("logoImg").outerHTML + '</td>' +
            '<td style="width:40%"><span>' + E.getTodayString() + '</span></div>');
    };
    E.reportcompletedtransfers.excelHTML5 = function (xlsx) {
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];
        //$('row:even c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
    }
    E.reportcompletedtransfers.pdfHTML5 = function (doc) {
        var logoAlignment = 'left';
        var todayString = 'right';
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            },
            'DejaVuSansCondensed': {
                normal: 'DejaVuSansCondensed.ttf',
                bold: 'DejaVuSansCondensed.ttf',
                italics: 'DejaVuSansCondensed.ttf',
                bolditalics: 'DejaVuSansCondensed.ttf'
            }
        };
        doc.defaultStyle =
        {
            font: 'Cairo',
        };
        doc.content[1].table.body.forEach(function (line, i) {
            if (line[0].style !== 'tableHeader' && line[0].style !== 'tableFooter') {
                line[6].style = line[6].style + "Icon";
            }
        });
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t');
            logoAlignment = 'right';
            todayString = 'left';
        }
        for (var i = 0; i < doc.content[1].table.body.length; i++) {
            var row = doc.content[1].table.body[i];
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            for (var col = 0; col < row.length; col++) {
                if (gArabic.test(row[col].text)) {
                    row[col].text = row[col].text.replace(/  +/g, ' ');
                    row[col].text = row[col].text.split(' ').join('\n');
                }
            }
        }

        doc.content[1].table.widths = ["15%", "15%", "15%", "15%", "15%", "15%", "12%"];
        doc.styles.tableBodyEven.alignment = 'center';
        doc.styles.tableBodyOdd.alignment = 'center';
        doc.styles.tableBodyEvenIcon = {
            fontSize: 14,
            font: 'DejaVuSansCondensed',
            alignment: 'center'
        };
        doc.styles.tableBodyOddIcon = {
            fontSize: 14,
            font: 'DejaVuSansCondensed',
            fillColor: '#F3F3F3',
            alignment: 'center'
        };

        doc.content.splice(0, 0, {
            "table": {
                "headerRows": 0,
                "body": [
                    [
                        {
                            "image": E.GetLogoBase64String(),
                            "alignment": logoAlignment
                        }
                    ],
                    [
                        {
                            "text": E.getTodayString(),
                            alignment: todayString

                        }
                    ]
                ],
                widths: ['100%']
            },
            "layout": "noBorders"
        })
    }


    E.reportinprogresscorrespondences = {};
 
    E.reportinprogresscorrespondences.print = function (e, dt, button, config, has_child_rows) {
        var data = dt.buttons.exportData(config.exportOptions);
        var addRow = function (d, tag, child_row, styleTr = '', styleTd = '') {
            var str = "<tr " + styleTr + ">";
            for (var i = 0, ien = d.length; i < ien; i++) {
                if (has_child_rows && !child_row && i === 0) continue;
                str += '<' + tag + ' ' + styleTd + '>' + d[i] + '</' + tag + '>';
            }
            return str + '</tr>';
        };
        var addSubTable = function (transfers, colspan) {
            var html = '<tr class="innertable-row">' + '<td class="innertable-row" colspan="' + colspan + '">';
            html += '<div style="padding: 0px 0px 15px 0px;"><table style="width:100%;" class="subTable"><thead class="WhiteBackgroundColorRI" style="width:100%;"><th colspan="3" class="ActivityInstancesInDataTableThRI WhiteBackgroundColorRI"><h5 class="ActivityInstancesInDataTableRI"><b>' + Resources.Transfers + '</b></h5></th></tr>' +
                '<th class="WhiteBackgroundColorRI" style="width:35%;padding: 10px 8px;">' + Resources.From + '</th>' +
                '<th class="WhiteBackgroundColorRI" style="width:20%;padding: 10px 8px;">' + Resources.To + '</th>' +
                '<th class="WhiteBackgroundColorRI" style="width:20%;padding: 10px 8px;">' + Resources.CreatedDate + '</th>' +
                '<th class="WhiteBackgroundColorRI" style="width:20%;padding: 10px 8px;">' + Resources.OverDue + '</th>';

            html = $("#chkOverdue").is(":checked") ? html : html + '<th class="WhiteBackgroundColorRI" style="width:10%;padding: 10px 8px;"></th>'
            html += '</thead><tbody>';

            for (var i = 0; i < transfers.length; i++) {
                var fromText = "";
                if (transfers[i].fromStructure) {
                    fromText += transfers[i].fromStructure;
                }
                if (transfers[i].fromUser) {
                    var user = transfers[i].fromUser;
                    fromText += fromText !== "" ? "/" + user : user;
                }
                var toText = "";
                if (transfers[i].toStructure) {
                    toText += transfers[i].toStructure;
                }
                if (transfers[i].toUser) {
                    var user = transfers[i].toUser;
                    toText += toText !== "" ? "/" + user : user;
                }
                var overdue = transfers[i].isOverDue ? '<td style="padding:5px 8px;width:10%;" class="break-word-classRI" title="' + Resources.OverDue + '"> <i class="fa fa-clock-o fa-lg text-danger"></i></td>' : '<td style="padding:5px 8px;width:10%;" class="break-word-classRI"></td>';
                var customClass = "EvenRowColorRI";
                if (i % 2 !== 0) {
                    customClass = "OddRowColorRI";
                }
                var transferDate = DateConverter.toHijriFormated(transfers[i].transferDate, null, window.CalendarType);
                let d = [fromText, toText, transferDate, transfers[i].isOverDue ? "<div class='mr-sm'>✔</div>" : "<div class='mr-sm'>✖</div>"];
                html += addRow(d, 'td', true, 'class=' + customClass);

            }
            html += '</tbody></table></div>';
            return html + '</td></tr>';
        };
        var html = '<style>.ActivityInstancesInDataTableThRI { padding: 5px 8px!important; color: #33ab8d !important;} .WhiteBackgroundColorRI { background-color: #fff !important;} '
        html += ' .EvenRowColorRI { background-color: #ededed !important;} .OddRowColorRI { background-color: #f7f7f7 !important;}';
        if (window.language == 'ar') {
            html += 'th { text-align: right;}';
        } else {
            html += 'th { text-align: left;}';
        }
        html += '.table>thead>tr>th, .table>thead>tr>td, .table>tbody>tr>th, .table>tbody>tr>td, .table>tfoot>tr>th, .table>tfoot>tr>td {padding: 8px; line-height: 1.52857; vertical-align: top; border-top: 1px solid #eee;}';
        html += 'body { font - family: "Source Sans Pro", sans - serif; color: #656565;}';
        html += 'table { border-collapse: collapse; border-spacing: 0;}';
        html += '.subTable>thead>tr>th, .subTable>thead>tr>td, .table>tbody>tr>th, .subTable>tbody>tr>td, .subTable>tfoot>tr>th, .subTable>tfoot>tr>td {padding: 3px 8px; line-height: 1.22857; vertical-align: top;}';
        html += '.ActivityInstancesInDataTableRI { margin: 0!important; padding: 1px 0!important;}';
        html += "</style>";
        html += '<table class=' + dt.table().node().className + '>';

        if (config.header) {
            html += '<thead>' + addRow(data.header, 'th') + '</thead>';
        }
        html += '<tbody>';
        for (var i = 0, ien = data.body.length; i < ien; i++) {
            html += addRow(data.body[i], 'td');

            if (has_child_rows) {
                if (dt.row(i).data().transfers.length > 0) {
                    html += addSubTable(dt.row(i).data().transfers, data.body[0].length);
                }
            }
        }
        html += '</tbody>';
        if (config.footer && data.footer) {
            html += '<tfoot>' + addRow(data.footer, 'th') + '</tfoot>';
        }

        // Open a new window for the printable table
        var win = window.open();
        var title = config.title;
        if (typeof title === 'function') {
            title = title();
        }

        if (title.indexOf('*') !== -1) {
            title = title.replace('*', $('title').text());
        }
        win.document.close();
        var head = '<title>' + title + '</title>';
        try {
            win.document.head.innerHTML = head; // Work around for Edge
        }
        catch (e) {
            $(win.document.head).html(head); // Old IE
        }

        if (window.language == 'ar') {
            win.document.body.style.direction = 'rtl';
        }
        win.document.body.innerHTML =
            '<h1>' + title + '</h1>'
            + '<div>'
            + (typeof config.message === 'function' ?
                config.message(dt, button, config) :
                config.message
            ) + '</div>' + html;
        $(win.document.body).addClass('dt-print-view');

        $('img', win.document.body).each(function (i, img) {
            img.setAttribute('src', _relToAbs(img.getAttribute('src')));
        });

        if (config.customize) {
            config.customize(win);
        }
        $(win.document.body).prepend('<table style="border:none;width:100%"><tr>' +
            '<td style="width:60%">' + document.getElementById("logoImg").outerHTML + '</td>' +
            '<td style="width:40%"><span>' + E.getTodayString() + '</span></div>');
        setTimeout(function () {
            if (config.autoPrint) {
                win.print();
                win.close();
            }
        }, 250);
    }

    E.reportinprogresscorrespondences.excelHTML5 = function (xlsx) {
        var table = $('#grdItemsHidden').DataTable();
        var numColumns = table.columns().header().count(); // Get number of columns to remove last hidden index column.

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
        var col = $('col', sheet);
        $(col[1]).attr('width', 20); // Set the column width.        
        var sheetData = $('sheetData', sheet).clone(); // Get a clone of the sheet data.
        $('sheetData', sheet).empty(); // Clear the current sheet data for appending rows.
        var DT_row; // Row index from last column.
        var rowCount = 1; // Row count in Excel sheet.
        // Itereate each row in the sheet data.
        $(sheetData).children().each(function (index) {
            var rowIndex = index - 1; // Used for DT row() API to get child data.
            if (index > 0) // Don't process row if its the header row.
            {
                var row = $(this.outerHTML); // Get row
                row.attr('r', rowCount); // Set the Excel row attr to the current Excel row count.
                var colCount = 1;
                // Iterate each cell in the row to change the rwo number.
                row.children().each(function (index) {
                    var cell = $(this);

                    // Set each cell's row value.
                    var rc = cell.attr('r');
                    rc = rc.replace(/\d+$/, "") + rowCount;
                    cell.attr('r', rc);

                    if (colCount === numColumns) {
                        DT_row = cell.text();
                        cell.html('');
                    }

                    colCount++;
                });
                row = row[0].outerHTML; // Get the row HTML and append to sheetData.
                $('sheetData', sheet).append(row);
                rowCount++;
                var row_count = index - 1;
                var childData = table.row(row_count, { search: 'none', order: 'transferDate' }).data().transfers; // Get the child data - could be any data attached to the row.
                if (childData.length > 0) {
                    // Prepare Excel formated row
                    var headerRow = '<row r="' + rowCount +
                        '" s="2"><c t="inlineStr" r="A' + rowCount +
                        '"><is><t>' +
                        '</t></is></c><c t="inlineStr" r="B' + rowCount +
                        '" s="2"><is><t>' + Resources.From +
                        '</t></is></c><c t="inlineStr" r="C' + rowCount +
                        '" s="2"><is><t>' + Resources.To +
                        '</t></is></c><c t="inlineStr" r="D' + rowCount +
                        '" s="2"><is><t>' + Resources.CreatedDate +
                        '</t></is></c><c t="inlineStr" r="E' + rowCount +
                        '" s="2"><is><t>' + Resources.OverDue +
                        '</t></is></c></row>';

                    $('sheetData', sheet).append(headerRow); // Append header row to sheetData.
                    rowCount++; // Inc excelt row counter.
                }
                // The child data is an array of rows
                for (var c = 0; c < childData.length; c++) {
                    var child = childData[c]; // Get row data.
                    var fromText = "";
                    if (child.fromStructure) {
                        fromText += child.fromStructure;
                    }
                    if (child.fromUser) {
                        var user = child.fromUser;
                        fromText += fromText !== "" ? "/" + user : user;
                    }
                    var toText = "";
                    if (child.toStructure) {
                        toText += child.toStructure;
                    }
                    if (child.toUser) {
                        var user = child.toUser;
                        toText += toText !== "" ? "/" + user : user;
                    }
                    var overdue = child.isOverDue ? "✔" : "✖";
                    // Prepare Excel formated row
                    var childRow = '<row r="' + rowCount +
                        '"><c t="inlineStr" r="A' + rowCount +
                        '"><is><t>' +
                        '</t></is></c><c t="inlineStr" r="B' + rowCount +
                        '"><is><t>' + fromText +
                        '</t></is></c><c t="inlineStr" r="C' + rowCount +
                        '"><is><t>' + toText +
                        '</t></is></c><c t="inlineStr" r="D' + rowCount +
                        '"><is><t>' + child.transferDate +
                        '</t></is></c><c t="inlineStr" r="E' + rowCount +
                        '"><is><t>' + overdue +
                        '</t></is></c></row>';
                    $('sheetData', sheet).append(childRow); // Append row to sheetData.
                    rowCount++; // Inc excelt row counter.
                }
            } else {
                var row = $(this.outerHTML);
                var colCount = 1;
                // Remove the last header cell.
                row.children().each(function (index) {
                    var cell = $(this);
                    if (colCount === numColumns) {
                        cell.html('');
                    }
                    colCount++;
                });
                row = row[0].outerHTML;
                $('sheetData', sheet).append(row);
                rowCount++;
            }
        });
    }
    E.reportinprogresscorrespondences.pdfHTML5 = function (doc) {
       
        var logoAlignment = 'left';
        var todayString = 'right';
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            },
            'DejaVuSansCondensed': {
                normal: 'DejaVuSansCondensed.ttf',
                bold: 'DejaVuSansCondensed.ttf',
                italics: 'DejaVuSansCondensed.ttf',
                bolditalics: 'DejaVuSansCondensed.ttf'
            }
        };
        doc.defaultStyle =
        {
            font: 'Cairo',
        };
        doc.content[1].table.body.forEach(function (line, i) {
            if (line[0].style !== 'tableHeader' && line[0].style !== 'tableFooter') {
                line[6].style = line[6].style + "Icon";
            }
        });
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t');
            logoAlignment = 'right';
            todayString = 'left';
        }
        for (var i = 0; i < doc.content[1].table.body.length; i++) {
            var row = doc.content[1].table.body[i];
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            for (var col = 0; col < row.length; col++) {
                if (gArabic.test(row[col].text)) {
                    row[col].text = row[col].text.replace(/  +/g, ' ');
                    row[col].text = row[col].text.split(' ').join('\n');
                }
            }
        }

        doc.content[1].table.widths = ["15%", "15%", "15%", "15%", "15%", "15%", "12%"];
        doc.styles.tableBodyEven.alignment = 'center';
        doc.styles.tableBodyOdd.alignment = 'center';
        doc.styles.tableBodyEvenIcon = {
            fontSize: 14,
            font: 'DejaVuSansCondensed',
            alignment: 'center'
        };
        doc.styles.tableBodyOddIcon = {
            fontSize: 14,
            font: 'DejaVuSansCondensed',
            fillColor: '#F3F3F3',
            alignment: 'center'
        };

        doc.content.splice(0, 0, {
            "table": {
                "headerRows": 0,
                "body": [
                    [
                        {
                            "image": E.GetLogoBase64String(),
                            "alignment": logoAlignment
                        }
                    ],
                    [
                        {
                            "text": E.getTodayString(),
                            alignment: todayString

                        }
                    ]
                ],
                widths: ['100%']
            },
            "layout": "noBorders"
        })
    }


  

    E.reportcompletedcorrespondences = {};
    E.reportcompletedcorrespondences.print = function (e, dt, button, config, has_child_rows) {
        var data = dt.buttons.exportData(config.exportOptions);
        var addRow = function (d, tag, child_row, styleTr = '', styleTd = '') {
            var str = "<tr " + styleTr + ">";
            for (var i = 0, ien = d.length; i < ien; i++) {
                if (has_child_rows && !child_row && i === 0) continue;
                str += '<' + tag + ' ' + styleTd + '>' + d[i] + '</' + tag + '>';
            }
            return str + '</tr>';
        };
        var addSubTable = function (transfers, colspan) {
            var html = '<tr class="innertable-row">' + '<td class="innertable-row" colspan="' + colspan + '">';
            html += '<div style="padding: 0px 0px 15px 0px;"><table style="width:100%;" class="subTable"><thead class="WhiteBackgroundColorRI" style="width:100%;"><th colspan="3" class="ActivityInstancesInDataTableThRI WhiteBackgroundColorRI"><h5 class="ActivityInstancesInDataTableRI"><b>' + Resources.Transfers + '</b></h5></th></tr>' +
                '<th class="WhiteBackgroundColorRI" style="width:35%;padding: 10px 8px;">' + Resources.From + '</th>' +
                '<th class="WhiteBackgroundColorRI" style="width:20%;padding: 10px 8px;">' + Resources.To + '</th>' +
                '<th class="WhiteBackgroundColorRI" style="width:20%;padding: 10px 8px;">' + Resources.CreatedDate + '</th>' +
                '<th class="WhiteBackgroundColorRI" style="width:20%;padding: 10px 8px;">' + Resources.OverDue + '</th>';

            html = $("#chkOverdue").is(":checked") ? html : html + '<th class="WhiteBackgroundColorRI" style="width:10%;padding: 10px 8px;"></th>'
            html += '</thead><tbody>';

            for (var i = 0; i < transfers.length; i++) {
                var fromText = "";
                if (transfers[i].fromStructure) {
                    fromText += transfers[i].fromStructure;
                }
                if (transfers[i].fromUser) {
                    var user = transfers[i].fromUser;
                    fromText += fromText !== "" ? "/" + user : user;
                }
                var toText = "";
                if (transfers[i].toStructure) {
                    toText += transfers[i].toStructure;
                }
                if (transfers[i].toUser) {
                    var user = transfers[i].toUser;
                    toText += toText !== "" ? "/" + user : user;
                }
                var overdue = transfers[i].isOverDue ? '<td style="padding:5px 8px;width:10%;" class="break-word-classRI" title="' + Resources.OverDue + '"> <i class="fa fa-clock-o fa-lg text-danger"></i></td>' : '<td style="padding:5px 8px;width:10%;" class="break-word-classRI"></td>';
                var customClass = "EvenRowColorRI";
                if (i % 2 !== 0) {
                    customClass = "OddRowColorRI";
                }
                var transferDate = DateConverter.toHijriFormated(transfers[i].transferDate, null, window.CalendarType);
                let d = [fromText, toText, transferDate, transfers[i].isOverDue ? "<div class='mr-sm'>✔</div>" : "<div class='mr-sm'>✖</div>"];
                html += addRow(d, 'td', true, 'class=' + customClass);

            }
            html += '</tbody></table></div>';
            return html + '</td></tr>';
        };
        var html = '<style>.ActivityInstancesInDataTableThRI { padding: 5px 8px!important; color: #33ab8d !important;} .WhiteBackgroundColorRI { background-color: #fff !important;} '
        html += ' .EvenRowColorRI { background-color: #ededed !important;} .OddRowColorRI { background-color: #f7f7f7 !important;}';
        if (window.language == 'ar') {
            html += 'th { text-align: right;}';
        } else {
            html += 'th { text-align: left;}';
        }
        html += '.table>thead>tr>th, .table>thead>tr>td, .table>tbody>tr>th, .table>tbody>tr>td, .table>tfoot>tr>th, .table>tfoot>tr>td {padding: 8px; line-height: 1.52857; vertical-align: top; border-top: 1px solid #eee;}';
        html += 'body { font - family: "Source Sans Pro", sans - serif; color: #656565;}';
        html += 'table { border-collapse: collapse; border-spacing: 0;}';
        html += '.subTable>thead>tr>th, .subTable>thead>tr>td, .table>tbody>tr>th, .subTable>tbody>tr>td, .subTable>tfoot>tr>th, .subTable>tfoot>tr>td {padding: 3px 8px; line-height: 1.22857; vertical-align: top;}';
        html += '.ActivityInstancesInDataTableRI { margin: 0!important; padding: 1px 0!important;}';
        html += "</style>";
        html += '<table class=' + dt.table().node().className + '>';

        if (config.header) {
            html += '<thead>' + addRow(data.header, 'th') + '</thead>';
        }
        html += '<tbody>';
        for (var i = 0, ien = data.body.length; i < ien; i++) {
            html += addRow(data.body[i], 'td');

            if (has_child_rows) {
                if (dt.row(i).data().transfers.length > 0) {
                    html += addSubTable(dt.row(i).data().transfers, data.body[0].length);
                }
            }
        }
        html += '</tbody>';
        if (config.footer && data.footer) {
            html += '<tfoot>' + addRow(data.footer, 'th') + '</tfoot>';
        }
        // Open a new window for the printable table
        var win = window.open();
        var title = config.title;
        if (typeof title === 'function') {
            title = title();
        }

        if (title.indexOf('*') !== -1) {
            title = title.replace('*', $('title').text());
        }
        
        win.document.close();
        var head = '<title>' + title + '</title>';
        try {
            win.document.head.innerHTML = head; // Work around for Edge
        }
        catch (e) {
            $(win.document.head).html(head); // Old IE
        }

        if (window.language == 'ar') {
            win.document.body.style.direction = 'rtl';
        }
        win.document.body.innerHTML =
            '<h1>' + title + '</h1>'
            + '<div>'
            + (typeof config.message === 'function' ?
                config.message(dt, button, config) :
                config.message
            ) + '</div>' + html;
        $(win.document.body).addClass('dt-print-view');

        $('img', win.document.body).each(function (i, img) {
            img.setAttribute('src', _relToAbs(img.getAttribute('src')));
        });

        if (config.customize) {
            config.customize(win);
        }
        $(win.document.body).prepend('<table style="border:none;width:100%"><tr>' +
            '<td style="width:60%">' + document.getElementById("logoImg").outerHTML + '</td>' +
            '<td style="width:40%"><span>' + E.getTodayString() + '</span></div>');
        setTimeout(function () {
            if (config.autoPrint) {
                win.print();
                win.close();
            }
        }, 250);
    }
    E.reportcompletedcorrespondences.excelHTML5 = function (xlsx) {
        var table = $('#grdItemsHidden').DataTable();
        var numColumns = table.columns().header().count(); // Get number of columns to remove last hidden index column.
        //var sheet = xlsx.xl.worksheets['sheet1.xml']; // Get sheet.

        //$('row c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
        var col = $('col', sheet);
        $(col[1]).attr('width', 20); // Set the column width.        
        var sheetData = $('sheetData', sheet).clone(); // Get a clone of the sheet data.
        $('sheetData', sheet).empty(); // Clear the current sheet data for appending rows.
        var DT_row; // Row index from last column.
        var rowCount = 1; // Row count in Excel sheet.
        // Itereate each row in the sheet data.
        $(sheetData).children().each(function (index) {
            var rowIndex = index - 1; // Used for DT row() API to get child data.
            if (index > 0) // Don't process row if its the header row.
            {
                var row = $(this.outerHTML); // Get row
                row.attr('r', rowCount); // Set the Excel row attr to the current Excel row count.
                var colCount = 1;
                // Iterate each cell in the row to change the rwo number.
                row.children().each(function (index) {
                    var cell = $(this);

                    // Set each cell's row value.
                    var rc = cell.attr('r');
                    rc = rc.replace(/\d+$/, "") + rowCount;
                    cell.attr('r', rc);

                    if (colCount === numColumns) {
                        DT_row = cell.text();
                        cell.html('');
                    }

                    colCount++;
                });
                row = row[0].outerHTML; // Get the row HTML and append to sheetData.
                $('sheetData', sheet).append(row);
                rowCount++;
                var row_count = index - 1;
                var childData = table.row(row_count, { search: 'none', order: 'transferDate' }).data().transfers; // Get the child data - could be any data attached to the row.
                if (childData.length > 0) {
                    // Prepare Excel formated row
                    var headerRow = '<row r="' + rowCount +
                        '" s="2"><c t="inlineStr" r="A' + rowCount +
                        '"><is><t>' +
                        '</t></is></c><c t="inlineStr" r="B' + rowCount +
                        '" s="2"><is><t>' + Resources.From +
                        '</t></is></c><c t="inlineStr" r="C' + rowCount +
                        '" s="2"><is><t>' + Resources.To +
                        '</t></is></c><c t="inlineStr" r="D' + rowCount +
                        '" s="2"><is><t>' + Resources.CreatedDate +
                        '</t></is></c><c t="inlineStr" r="E' + rowCount +
                        '" s="2"><is><t>' + Resources.OverDue +
                        '</t></is></c></row>';

                    $('sheetData', sheet).append(headerRow); // Append header row to sheetData.
                    rowCount++; // Inc excelt row counter.
                }
                // The child data is an array of rows
                for (var c = 0; c < childData.length; c++) {
                    var child = childData[c]; // Get row data.
                    var fromText = "";
                    if (child.fromStructure) {
                        fromText += child.fromStructure;
                    }
                    if (child.fromUser) {
                        var user = child.fromUser;
                        fromText += fromText !== "" ? "/" + user : user;
                    }
                    var toText = "";
                    if (child.toStructure) {
                        toText += child.toStructure;
                    }
                    if (child.toUser) {
                        var user = child.toUser;
                        toText += toText !== "" ? "/" + user : user;
                    }
                    var overdue = child.isOverDue ? "✔" : "✖";
                    // Prepare Excel formated row
                    var childRow = '<row r="' + rowCount +
                        '"><c t="inlineStr" r="A' + rowCount +
                        '"><is><t>' +
                        '</t></is></c><c t="inlineStr" r="B' + rowCount +
                        '"><is><t>' + fromText +
                        '</t></is></c><c t="inlineStr" r="C' + rowCount +
                        '"><is><t>' + toText +
                        '</t></is></c><c t="inlineStr" r="D' + rowCount +
                        '"><is><t>' + child.transferDate +
                        '</t></is></c><c t="inlineStr" r="E' + rowCount +
                        '"><is><t>' + overdue +
                        '</t></is></c></row>';
                    $('sheetData', sheet).append(childRow); // Append row to sheetData.
                    rowCount++; // Inc excelt row counter.
                }
            } else {
                var row = $(this.outerHTML);
                var colCount = 1;
                // Remove the last header cell.
                row.children().each(function (index) {
                    var cell = $(this);
                    if (colCount === numColumns) {
                        cell.html('');
                    }
                    colCount++;
                });
                row = row[0].outerHTML;
                $('sheetData', sheet).append(row);
                rowCount++;
            }
        });
    }
    E.reportcompletedcorrespondences.pdfHTML5 = function (doc) {
        var todayString = 'right';
        var logoAlignment = 'left';
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            },
            'DejaVuSansCondensed': {
                normal: 'DejaVuSansCondensed.ttf',
                bold: 'DejaVuSansCondensed.ttf',
                italics: 'DejaVuSansCondensed.ttf',
                bolditalics: 'DejaVuSansCondensed.ttf'
            }
        };
        doc.defaultStyle =
        {
            font: 'Cairo'
        };
        var table = $('#grdItemsHidden').DataTable(); // Get the row data in in table order and search applied
        var rowData = table.rows({ order: 'createdDate', search: 'none' }).data();
        var headerLines = 0;  // Offset for accessing rowData array
        var newBody = []; // this will become our new body (an array of arrays(lines))
        //Loop over all lines in the table

        doc.content[1].table.body.forEach(function (line, i) {
            // Remove detail-control column
            newBody.push(
                [line[1], line[2], line[3], line[4], line[5]/*, line[7]*/]
            );
            if (line[0].style !== 'tableHeader' && line[0].style !== 'tableFooter') {
                line[1].style = "rowStyle";
                line[2].style = "rowStyle";
                line[3].style = "rowStyle";
                line[4].style = "rowStyle";
                line[5].style = "rowStyle";
                //line[7].style = "iconRowStyle";
                newBody.push(
                    [
                        { text: Resources.Transfers + ':', style: 'subheader' },
                        { text: Resources.From, style: 'subheader' },
                        { text: Resources.To, style: 'subheader' },
                        { text: Resources.CreatedDate, style: 'subheader' },
                        { text: Resources.OverDue, style: 'subheader' },
                        { text: '', style: 'noneStyle' },
                    ]
                );
                var childs = rowData[i - headerLines].transfers;
                for (var i = 0; i < childs.length; i++) {
                    var child = childs[i];
                    var fromText = "";
                    if (child.fromStructure) {
                        fromText += child.fromStructure;
                    }
                    if (child.fromUser) {
                        var user = child.fromUser;
                        fromText += fromText !== "" ? "/" + user : user;
                    }
                    var toText = "";
                    if (child.toStructure) {
                        toText += child.toStructure;
                    }
                    if (child.toUser) {
                        var user = child.toUser;
                        toText += toText !== "" ? "/" + user : user;
                    }
                    var overdue = child.isOverDue ? "✔" : "✖";
                    // Append child data, matching number of columns in table
                    newBody.push(
                        [
                            { text: '', style: 'defaultStyle' },
                            { text: fromText, style: 'defaultStyle' },
                            { text: toText, style: 'defaultStyle' },
                            { text: child.transferDate, style: 'defaultStyle' },
                            { text: overdue, style: 'iconStyle' },
                            { text: '', style: 'noneStyle' },
                        ]
                    );
                }

            } else {
                headerLines++;
            }
        });
        //Overwrite the old table body with the new one.
        doc.content[1].table.body = newBody;
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t');
            logoAlignment = 'right';
            todayString = 'left';
        }
        for (var i = 0; i < doc.content[1].table.body.length; i++) {
            var row = doc.content[1].table.body[i];
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            for (var col = 0; col < row.length; col++) {
                if (gArabic.test(row[col].text)) {
                    row[col].text = row[col].text.replace(/  +/g, ' ');
                    row[col].text = row[col].text.split(' ').join('\n');
                }
            }
        }
        doc.content[1].table.headerRows = 1; //Overwrite the old table body with the new one.
        doc.content[1].table.widths = ["15%", "15%", "20%", "20%", "15%", "15%", "10%"]
        doc.content[1].layout = 'lightHorizontalLines';
        doc.styles = {
            subheader: {
                fontSize: 9.5,
                bold: true,
                color: 'black',
                alignment: 'center'
            },
            rowStyle: {
                fontSize: 10,
                color: 'black',
                text: 'center',
                fillColor: '#F3F3F3',
                alignment: 'center'
            },
            tableHeader: {
                bold: true,
                fontSize: 11,
                color: 'white',
                fillColor: '#2D4154',
                alignment: 'center'
            },
            defaultStyle: {
                fontSize: 8.5,
                color: 'black',
                alignment: 'center'
            },
            iconStyle: {
                fontSize: 17,
                alignment: 'center',
                font: 'DejaVuSansCondensed'
            },
            iconRowStyle: {
                fontSize: 17,
                fillColor: '#F3F3F3',
                alignment: 'center',
                font: 'DejaVuSansCondensed'
            },
            title: { alignment: "center", fontSize: 15 }
        };
        doc.content.splice(0, 0, {
            "table": {
                "headerRows": 0,
                "body": [
                    [
                        {
                            "image": E.GetLogoBase64String(),
                            "alignment": logoAlignment
                        }
                    ],
                    [
                        {
                            "text": E.getTodayString(),
                            alignment: todayString

                        }
                    ]
                ],
                widths: ['100%']
            },
            "layout": "noBorders"
        })
    }


    E.reportstatisticalcorrespondences = {};
    E.reportstatisticalcorrespondences.print = function (e, dt, button, config, has_child_rows, categories) {
        var data = dt.buttons.exportData(config.exportOptions);
        var addRow = function (d, tag, child_row, styleTr = '', styleTd = '') {
            var str = "<tr " + styleTr + ">";

            for (var i = 0, ien = d.length; i < ien; i++) {
                if (has_child_rows && !child_row && i === 0) continue;
                str += '<' + tag + ' ' + styleTd + '>' + d[i] + '</' + tag + '>';
            }

            return str + '</tr>';
        };

        var addSubTable = function (users, colspan, categories) {
            var html = '<tr class="innertable-row">' + '<td class="innertable-row" colspan="' + colspan + '">';
            html += '<div style="padding: 0px 0px 15px 0px;"><table style="width:100%;" class="subTable"><thead class="WhiteBackgroundColorRI" style="width:100%;"><th colspan="3" class="ActivityInstancesInDataTableThRI WhiteBackgroundColorRI"><h5 class="ActivityInstancesInDataTableRI"><b>' + Resources.ResultsPerUser + '</b></h5></th></tr>' +
                '<th class="WhiteBackgroundColorRI" style="padding: 10px 8px;">' + Resources.User + '</th>';
            $.each(categories, function (key, value) {
                html += '<th class="WhiteBackgroundColorRI" style="padding: 10px 8px;">' + value.text + '</th>';
            });

            html += '<th class="WhiteBackgroundColorRI" style="padding: 10px 8px;">' + Resources.ReceivedTransfers + '</th>';
            html += '<th class="WhiteBackgroundColorRI" style="padding: 10px 8px;">' + Resources.SentTransfers + '</th>';
            html += '</thead><tbody>';

            for (var i = 0; i < users.length; i++) {
                let d = [];

                d.push(users[i].userName);
                for (var j = 0; j < categories.length; j++) {
                    var catCount = 0;
                    $.each(users[i].categoryCount, function (key, value) {
                        if (Number(key) === categories[j].id) {
                            catCount = value;
                            return false;
                        }
                    });
                    d.push(catCount);
                }
                d.push(users[i].countTotalTransfersReceived);
                d.push(users[i].countTotalTransfersSent);

                var customClass = "EvenRowColorRI";
                if (i % 2 !== 0) {
                    customClass = "OddRowColorRI";
                }
                html += addRow(d, 'td', true, 'class=' + customClass);

            }
            html += '</tbody></table></div>';


            return html + '</td></tr>';
        };

        var html = '<style>.ActivityInstancesInDataTableThRI { padding: 5px 8px!important; color: #33ab8d !important;} .WhiteBackgroundColorRI { background-color: #fff !important;} '
        html += ' .EvenRowColorRI { background-color: #ededed !important;} .OddRowColorRI { background-color: #f7f7f7 !important;}';
        if (window.language == 'ar') {
            html += 'th { text-align: right;}';
        } else {
            html += 'th { text-align: left;}';
        }
        html += '.table>thead>tr>th, .table>thead>tr>td, .table>tbody>tr>th, .table>tbody>tr>td, .table>tfoot>tr>th, .table>tfoot>tr>td {padding: 8px; line-height: 1.52857; vertical-align: top; border-top: 1px solid #eee;}';
        html += 'body { font - family: "Source Sans Pro", sans - serif; color: #656565;}';
        html += 'table { border-collapse: collapse; border-spacing: 0;width:100%;}';
        html += '.subTable>thead>tr>th, .subTable>thead>tr>td, .table>tbody>tr>th, .subTable>tbody>tr>td, .subTable>tfoot>tr>th, .subTable>tfoot>tr>td {padding: 3px 8px; line-height: 1.22857; vertical-align: top;}';
        html += '.ActivityInstancesInDataTableRI { margin: 0!important; padding: 1px 0!important;}';
        html += "</style>";
        html += '<table class=' + dt.table().node().className + '>';

        if (config.header) {
            html += '<thead>' + addRow(data.header, 'th') + '</thead>';
        }

        html += '<tbody>';
        for (var i = 0, ien = data.body.length; i < ien; i++) {
            html += addRow(data.body[i], 'td');

            if (has_child_rows) {
                if (dt.row(i).data().users.length > 0) {
                    html += addSubTable(dt.row(i).data().users, data.body[0].length, categories);
                }
            }
        }
        html += '</tbody>';

        if (config.footer && data.footer) {
            html += '<tfoot>' + addRow(data.footer, 'th') + '</tfoot>';
        }

        // Open a new window for the printable table
        var win = window.open();
        var title = config.title;
        if (typeof title === 'function') {
            title = title();
        }

        if (title.indexOf('*') !== -1) {
            title = title.replace('*', $('title').text());
        }
        win.document.close();
        var head = '<title>' + title + '</title>';
        try {
            win.document.head.innerHTML = head; // Work around for Edge
        }
        catch (e) {
            $(win.document.head).html(head); // Old IE
        }

        if (window.language == 'ar') {
            win.document.body.style.direction = 'rtl';
        }
        win.document.body.innerHTML =
            '<h1>' + title + '</h1>'
            + '<div>'
            + (typeof config.message === 'function' ?
                config.message(dt, button, config) :
                config.message
            )
            + '</div>'
            + html;


        $(win.document.body).addClass('dt-print-view');

        $('img', win.document.body).each(function (i, img) {
            img.setAttribute('src', _relToAbs(img.getAttribute('src')));
        });

        if (config.customize) {
            config.customize(win);
        }
        $(win.document.body).prepend('<table style="border:none;width:100%"><tr>' +
            '<td style="width:60%">' + document.getElementById("logoImg").outerHTML + '</td>' +
            '<td style="width:40%"><span>' + E.getTodayString() + '</span></div>');

        setTimeout(function () {
            if (config.autoPrint) {
                win.print();
                win.close();
            }
        }, 250);
    }
    E.reportstatisticalcorrespondences.excelHTML5 = function (xlsx, categories) {
        var table = $('#grdItemsHidden').DataTable();

        // Get number of columns to remove last hidden index column.
        var numColumns = table.columns().header().count();

        // Get sheet.
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];

        //$('row c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
        var col = $('col', sheet);
        // Set the column width.
        $(col[1]).attr('width', 20);

        // Get a clone of the sheet data.        
        var sheetData = $('sheetData', sheet).clone();

        // Clear the current sheet data for appending rows.
        $('sheetData', sheet).empty();

        // Row index from last column.
        var DT_row;
        // Row count in Excel sheet.
        var rowCount = 1;

        // Itereate each row in the sheet data.
        $(sheetData).children().each(function (index) {

            // Used for DT row() API to get child data.
            var rowIndex = index - 1;

            // Don't process row if its the header row.
            if (index > 0) {

                // Get row
                var row = $(this.outerHTML);

                // Set the Excel row attr to the current Excel row count.
                row.attr('r', rowCount);

                var colCount = 1;

                // Iterate each cell in the row to change the row number.
                row.children().each(function (index) {
                    var cell = $(this);

                    // Set each cell's row value.
                    var rc = cell.attr('r');
                    rc = rc.replace(/\d+$/, "") + rowCount;
                    cell.attr('r', rc);

                    if (colCount === numColumns) {
                        DT_row = cell.text();
                        cell.html('');
                    }

                    colCount++;
                });

                // Get the row HTML and append to sheetData.
                row = row[0].outerHTML;
                $('sheetData', sheet).append(row);
                rowCount++;

                // Get the child data - could be any data attached to the row.
                var childData = table.row(rowIndex, { search: 'none', order: 'index' }).data().users;

                if (childData.length > 0) {
                    // Prepare Excel formated row
                    var headerRow = '<row r="' + rowCount + '" s="2">';
                    headerRow += '<c t = "inlineStr" r="A' + rowCount +
                        '" s="2"><is><t>' + Resources.User +
                        '</t></is></c>';
                    var letterArrayIndex = 1;
                    $.each(categories, function (key, value) {
                        headerRow += '<c t = "inlineStr" r="' + gLetterArray[letterArrayIndex] + '' + rowCount +
                            '" s="2"><is><t>' + value.text +
                            '</t></is></c>';
                        letterArrayIndex++;
                    });
                    headerRow += '<c t = "inlineStr" r="' + gLetterArray[letterArrayIndex] + '' + rowCount +
                        '" s="2"><is><t>' + Resources.ReceivedTransfers +
                        '</t></is></c>';

                    headerRow += '<c t = "inlineStr" r="' + gLetterArray[letterArrayIndex + 1] + '' + rowCount +
                        '" s="2"><is><t>' + Resources.SentTransfers +
                        '</t></is></c></row>';

                    // Append header row to sheetData.
                    $('sheetData', sheet).append(headerRow);
                    rowCount++; // Inc excelt row counter.

                }

                // The child data is an array of rows
                for (var c = 0; c < childData.length; c++) {
                    // Get row data.
                    var child = childData[c];

                    var childRow = '<row r="' + rowCount + '">';
                    childRow += '<c t = "inlineStr" r="A' + rowCount +
                        '"><is><t>' + child.userName +
                        '</t></is></c>';
                    var letterArrayIndex = 1;
                    for (var j = 0; j < categories.length; j++) {
                        var catCount = 0;
                        $.each(child.categoryCount, function (key, value) {
                            if (Number(key) === categories[j].id) {
                                catCount = value;
                                return false;
                            }
                        });
                        childRow += '<c t = "inlineStr" r="' + gLetterArray[letterArrayIndex] + '' + rowCount +
                            '" s="52"><is><t>&#8203;' + catCount +
                            '</t></is></c>';
                        letterArrayIndex++;
                    }

                    childRow += '<c t = "inlineStr" r="' + gLetterArray[letterArrayIndex] + '' + rowCount +
                        '" s="52"><is><t>&#8203;' + child.countTotalTransfersReceived +
                        '</t></is></c>';

                    childRow += '<c t = "inlineStr" r="' + gLetterArray[letterArrayIndex + 1] + '' + rowCount +
                        '" s="52"><is><t>&#8203;' + child.countTotalTransfersSent +
                        '</t></is></c></row>';

                    // Append row to sheetData.
                    $('sheetData', sheet).append(childRow);
                    rowCount++; // Inc excelt row counter.

                }
                // Just append the header row and increment the excel row counter.
            } else {
                var row = $(this.outerHTML);

                var colCount = 1;

                // Remove the last header cell.
                row.children().each(function (index) {
                    var cell = $(this);

                    if (colCount === numColumns) {
                        cell.html('');
                    }

                    colCount++;
                });
                row = row[0].outerHTML;
                $('sheetData', sheet).append(row);
                rowCount++;
            }
        });
    }
    E.reportstatisticalcorrespondences.pdfHTML5 = function (doc, pdfMake, columns, categories) {
        var todayString = 'right';
        var logoAlignment = 'left';
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            },
        };
        doc.defaultStyle =
        {
            font: 'Cairo',
        };

        // Get the row data in in table order and search applied
        var table = $('#grdItemsHidden').DataTable();
        var rowData = table.rows({ order: 'applied', search: 'applied' }).data();
        var headerLines = 0;  // Offset for accessing rowData array

        var newBody = []; // this will become our new body (an array of arrays(lines))
        //Loop over all lines in the table
        doc.content[1].table.body.forEach(function (line, i) {
            var bodyLines = [];
            if (i == 0) {
                for (var k = 1; k < line.length; k++) {
                    bodyLines.push(line[k]);

                }
            } else {
                for (var k = 1; k < line.length; k++) {
                    bodyLines.push({ text: line[k].text, style: 'rowStyle' });
                }
            }
            newBody.push(bodyLines);

            if (line[0].style !== 'tableHeader' && line[0].style !== 'tableFooter') {
                var data = rowData[i - headerLines];
                var childBody = [];

                childBody.push(
                    { text: Resources.User, style: 'subheader' }
                );
                for (var k = 2; k < columns.length; k++) {
                    childBody.push(
                        { text: columns[k].title, style: 'subheader' }
                    );
                }
                newBody.push(childBody);


                for (var k = 0; k < data.users.length; k++) {
                    childBody = [];
                    childBody.push(
                        { text: data.users[k].userName, style: 'defaultStyle' }
                    );
                    for (var j = 0; j < categories.length; j++) {
                        var catCount = 0;
                        $.each(data.users[k].categoryCount, function (key, value) {
                            if (Number(key) === categories[j].id) {
                                catCount = value;
                                return false;
                            }
                        });
                        childBody.push(
                            { text: catCount, style: 'defaultStyle' }
                        );
                    }
                    childBody.push(
                        { text: data.users[k].countTotalTransfersReceived, style: 'defaultStyle' }
                    );
                    childBody.push(
                        { text: data.users[k].countTotalTransfersSent, style: 'defaultStyle' }
                    );
                    newBody.push(childBody);
                }
            } else {
                headerLines++;
            }
        });

        //Overwrite the old table body with the new one.
        doc.content[1].table.body = newBody;
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t');
            logoAlignment = 'right';
            todayString = 'left';
        }
        for (var i = 0; i < doc.content[1].table.body.length; i++) {
            var row = doc.content[1].table.body[i];
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            for (var col = 0; col < row.length; col++) {
                if (gArabic.test(row[col].text)) {
                    row[col].text = row[col].text.replace(/  +/g, ' ');
                    row[col].text = row[col].text.split(' ').join('\n');
                }
            }
        }

        doc.content[1].table.headerRows = 1;
        var singleWidth = 100 / doc.content[1].table.body[0].length;
        var arrayWidth = [];
        for (var i = 0; i < doc.content[1].table.body[0].length; i++) {
            arrayWidth.push(singleWidth + "%");
        }
        doc.content[1].table.widths = arrayWidth;

        doc.content[1].layout = 'lightHorizontalLines';

        doc.styles = {
            subheader: {
                fontSize: 9.5,
                bold: true,
                color: 'black',
                //fillColor: '#c9c1e0',
                alignment: 'center'
            },
            rowStyle: {
                fontSize: 10,
                color: 'black',
                text: 'center',
                fillColor: '#F3F3F3',
                alignment: 'center'
            },
            tableHeader: {
                bold: true,
                fontSize: 11,
                color: 'white',
                fillColor: '#2D4154',
                alignment: 'center'
            },
            defaultStyle: {
                fontSize: 8.5,
                color: 'black',
                //fillColor: '#FFFFFF',
                alignment: 'center'
            },
            title: { alignment: "center", fontSize: 15 }
        };
        doc.content.splice(0, 0, {
            "table": {
                "headerRows": 0,
                "body": [
                    [
                        {
                            "image": E.GetLogoBase64String(),
                            "alignment": logoAlignment
                        }
                    ],
                    [
                        {
                            "text": E.getTodayString(),
                            alignment: todayString

                        }
                    ]
                ],
                widths: ['100%']
            },
            "layout": "noBorders"
        })
    }


    E.reportoperationbyuser = {};
    E.reportoperationbyuser.print = function (e, dt, button, config, has_child_rows) {
        var data = dt.buttons.exportData(config.exportOptions);
        var addRow = function (d, tag, child_row, styleTr = '', styleTd = '') {
            var str = "<tr " + styleTr + ">";

            for (var i = 0, ien = d.length; i < ien; i++) {
                if (has_child_rows && !child_row && i === 0) continue;
                str += '<' + tag + ' ' + styleTd + '>' + d[i] + '</' + tag + '>';
            }

            return str + '</tr>';
        };

        var addSubTable = function (note, colspan) {
            var html = '<tr class="innertable-row">' + '<td class="innertable-row" colspan="' + colspan + '">';
            html += '<div style="padding: 0px 0px 5px 0px;"><table style="width:100%;" class="subTable"><tbody class="WhiteBackgroundColorRI" style="width:100%;"></tr>' +
                '<th class="WhiteBackgroundColorRI" style="width:15%;padding: 10px 8px;">' + Resources.Note + '</th>' +
                '<td class="WhiteBackgroundColorRI"  style="width:85%;padding: 10px 8px;float:left;">' + note + '</td>';
            html += '</tbody></table></div>';
            return html + '</td></tr>';
        };

        var html = '<style>.ActivityInstancesInDataTableThRI { padding: 5px 8px!important; color: #33ab8d !important;} .WhiteBackgroundColorRI { background-color: #fff !important;} '
        html += ' .EvenRowColorRI { background-color: #ededed !important;} .OddRowColorRI { background-color: #f7f7f7 !important;}';
        if (window.language == 'ar') {
            html += 'th { text-align: right;}';
        } else {
            html += 'th { text-align: left;}';
        }
        html += '.table>thead>tr>th, .table>thead>tr>td, .table>tbody>tr>th, .table>tbody>tr>td, .table>tfoot>tr>th, .table>tfoot>tr>td {padding: 8px; line-height: 1.52857; vertical-align: top; border-top: 1px solid #eee;}';
        html += 'body { font - family: "Source Sans Pro", sans - serif; color: #656565;}';
        html += 'table { border-collapse: collapse; border-spacing: 0;width:100%;}';
        html += '.subTable>thead>tr>th, .subTable>thead>tr>td, .table>tbody>tr>th, .subTable>tbody>tr>td, .subTable>tfoot>tr>th, .subTable>tfoot>tr>td {padding: 3px 8px; line-height: 1.22857; vertical-align: top;}';
        html += '.ActivityInstancesInDataTableRI { margin: 0!important; padding: 1px 0!important;}';
        html += "</style>";
        html += '<table class=' + dt.table().node().className + '>';

        if (config.header) {
            html += '<thead>' + addRow(data.header, 'th') + '</thead>';
        }

        html += '<tbody>';
        for (var i = 0, ien = data.body.length; i < ien; i++) {
            html += addRow(data.body[i], 'td');
            var dt_note = dt.row(i).data().note ? dt.row(i).data().note : "-";
            html += addSubTable(dt_note, data.body[0].length - 1);
        }
        html += '</tbody>';

        if (config.footer && data.footer) {
            html += '<tfoot>' + addRow(data.footer, 'th') + '</tfoot>';
        }

        // Open a new window for the printable table
        var win = window.open();
        var title = config.title;
        if (typeof title === 'function') {
            title = title();
        }

        if (title.indexOf('*') !== -1) {
            title = title.replace('*', $('title').text());
        }
        win.document.close();
        var head = '<title>' + title + '</title>';
        try {
            win.document.head.innerHTML = head; // Work around for Edge
        }
        catch (e) {
            $(win.document.head).html(head); // Old IE
        }

        if (window.language == 'ar') {
            win.document.body.style.direction = 'rtl';
        }
        win.document.body.innerHTML =
            '<h1>' + title + '</h1>'
            + '<div>'
            + (typeof config.message === 'function' ?
                config.message(dt, button, config) :
                config.message
            )
            + '</div>'
            + html;


        $(win.document.body).addClass('dt-print-view');

        $('img', win.document.body).each(function (i, img) {
            img.setAttribute('src', _relToAbs(img.getAttribute('src')));
        });

        if (config.customize) {
            config.customize(win);
        }
        $(win.document.body).prepend('<table style="border:none;width:100%"><tr>' +
            '<td style="width:60%">' + document.getElementById("logoImg").outerHTML + '</td>' +
            '<td style="width:40%"><span>' + E.getTodayString() + '</span></div>');

        setTimeout(function () {
            if (config.autoPrint) {
                win.print();
                win.close();
            }
        }, 250);
    }
    E.reportoperationbyuser.excelHTML5 = function (xlsx) {
        var table = $('#grdItemsHidden').DataTable();

        // Get number of columns to remove last hidden index column.
        var numColumns = table.columns().header().count();

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

        var col = $('col', sheet);
        // Set the column width.
        $(col[1]).attr('width', 20);

        // Get a clone of the sheet data.        
        var sheetData = $('sheetData', sheet).clone();

        // Clear the current sheet data for appending rows.
        $('sheetData', sheet).empty();

        // Row index from last column.
        var DT_row;
        // Row count in Excel sheet.
        var rowCount = 1;

        // Itereate each row in the sheet data.
        $(sheetData).children().each(function (index) {

            // Used for DT row() API to get child data.
            var rowIndex = index - 1;

            // Don't process row if its the header row.
            if (index > 0) {

                // Get row
                var row = $(this.outerHTML);

                // Set the Excel row attr to the current Excel row count.
                row.attr('r', rowCount);

                var colCount = 1;

                // Iterate each cell in the row to change the row number.
                row.children().each(function (index) {
                    var cell = $(this);

                    // Set each cell's row value.
                    var rc = cell.attr('r');
                    rc = rc.replace(/\d+$/, "") + rowCount;
                    cell.attr('r', rc);

                    if (colCount === numColumns) {
                        DT_row = cell.text();
                        cell.html('');
                    }

                    colCount++;
                });

                // Get the row HTML and append to sheetData.
                row = row[0].outerHTML;
                $('sheetData', sheet).append(row);
                rowCount++;

                // Get the child data - could be any data attached to the row.
                var note = table.row(rowIndex, { search: 'none', order: 'index' }).data().note;

                // Prepare Excel formated row
                var headerRow = '<row r="' + rowCount + '" s="2">';
                headerRow += '<c t = "inlineStr" r="A' + rowCount +
                    '" s="2"><is><t>' + Resources.Note +
                    '</t></is></c>';
                headerRow += '<c t = "inlineStr" r="B' + rowCount +
                    '"><is><t>' + (note ? note : "-") +
                    '</t></is></c></row>';
                $('sheetData', sheet).append(headerRow);
                rowCount++; // Inc excelt row counter.
            } else {
                var row = $(this.outerHTML);

                var colCount = 1;

                // Remove the last header cell.
                row.children().each(function (index) {
                    var cell = $(this);

                    if (colCount === numColumns) {
                        cell.html('');
                    }

                    colCount++;
                });
                row = row[0].outerHTML;
                $('sheetData', sheet).append(row);
                rowCount++;
            }
        });

    }
    E.reportoperationbyuser.pdfHTML5 = function (doc, pdfMake) {
        var todayString = 'right';
        var logoAlignment = 'left';
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            },
        };
        doc.defaultStyle =
        {
            font: 'Cairo',
        };

        // Get the row data in in table order and search applied
        var table = $('#grdItemsHidden').DataTable();
        var rowData = table.rows({ order: 'applied', search: 'applied' }).data();
        var headerLines = 0;  // Offset for accessing rowData array

        var newBody = []; // this will become our new body (an array of arrays(lines))
        //Loop over all lines in the table
        doc.content[1].table.body.forEach(function (line, i) {
            var bodyLines = [];
            if (i == 0) {
                for (var k = 0; k < line.length; k++) {
                    bodyLines.push(line[k]);
                }
            } else {
                for (var k = 0; k < line.length; k++) {
                    bodyLines.push({ text: line[k].text, style: 'rowStyle' });
                }
            }
            newBody.push(bodyLines);

            if (line[0].style !== 'tableHeader' && line[0].style !== 'tableFooter') {
                var data = rowData[i - headerLines];
                var childBody = [];

                childBody.push(
                    { text: Resources.Note, style: 'subheader' }
                );
                childBody.push(
                    { text: data.note ? data.note : "-", style: 'defaultStyle', colSpan: 5 }
                );
                newBody.push(childBody);
            } else {
                headerLines++;
            }
        });

        //Overwrite the old table body with the new one.
        doc.content[1].table.body = newBody;
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t');
            logoAlignment = 'right';
            todayString = 'left';
        }
        for (var i = 0; i < doc.content[1].table.body.length; i++) {
            var row = doc.content[1].table.body[i];
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            for (var col = 0; col < row.length; col++) {
                if (gArabic.test(row[col].text)) {
                    row[col].text = row[col].text.replace(/  +/g, ' ');
                    row[col].text = row[col].text.split(' ').join('\n');
                }
            }
        }

        doc.content[1].table.headerRows = 1;
        var singleWidth = 100 / doc.content[1].table.body[0].length;
        var arrayWidth = [];
        for (var i = 0; i < doc.content[1].table.body[0].length; i++) {
            arrayWidth.push(singleWidth + "%");
        }
        doc.content[1].table.widths = arrayWidth;

        doc.content[1].layout = 'lightHorizontalLines';

        doc.styles = {
            subheader: {
                fontSize: 9.5,
                bold: true,
                color: 'black',
                alignment: 'center'
            },
            rowStyle: {
                fontSize: 10,
                color: 'black',
                text: 'center',
                fillColor: '#F3F3F3',
                alignment: 'center'
            },
            tableHeader: {
                bold: true,
                fontSize: 11,
                color: 'white',
                fillColor: '#2D4154',
                alignment: 'center'
            },
            defaultStyle: {
                fontSize: 8.5,
                color: 'black',
                //fillColor: '#FFFFFF',
                alignment: 'left'
            },
            title: { alignment: "center", fontSize: 15 }
        };
        doc.content.splice(0, 0, {
            "table": {
                "headerRows": 0,
                "body": [
                    [
                        {
                            "image": E.GetLogoBase64String(),
                            "alignment": logoAlignment
                        }
                    ],
                    [
                        {
                            "text": E.getTodayString(),
                            alignment: todayString

                        }
                    ]
                ],
                widths: ['100%']
            },
            "layout": "noBorders"
        })
    }

    E.reportoperationbycorrespondence = {};
    E.reportoperationbycorrespondence.print = function (e, dt, button, config, has_child_rows) {
        var data = dt.buttons.exportData(config.exportOptions);
        var addRow = function (d, tag, child_row, styleTr = '', styleTd = '') {
            var str = "<tr " + styleTr + ">";

            for (var i = 0, ien = d.length; i < ien; i++) {
                if (has_child_rows && !child_row && i === 0) continue;
                str += '<' + tag + ' ' + styleTd + '>' + d[i] + '</' + tag + '>';
            }

            return str + '</tr>';
        };

        var addSubTable = function (note, colspan) {
            var html = '<tr class="innertable-row">' + '<td class="innertable-row" colspan="' + colspan + '">';
            html += '<div style="padding: 0px 0px 5px 0px;"><table style="width:100%;" class="subTable"><tbody class="WhiteBackgroundColorRI" style="width:100%;"></tr>' +
                '<th class="WhiteBackgroundColorRI" style="width:15%;padding: 10px 8px;">' + Resources.Note + '</th>' +
                '<td class="WhiteBackgroundColorRI"  style="width:85%;padding: 10px 8px;float:left;">' + note + '</td>';
            html += '</tbody></table></div>';
            return html + '</td></tr>';
        };

        var html = '<style>.ActivityInstancesInDataTableThRI { padding: 5px 8px!important; color: #33ab8d !important;} .WhiteBackgroundColorRI { background-color: #fff !important;} '
        html += ' .EvenRowColorRI { background-color: #ededed !important;} .OddRowColorRI { background-color: #f7f7f7 !important;}';
        if (window.language == 'ar') {
            html += 'th { text-align: right;}';
        } else {
            html += 'th { text-align: left;}';
        }
        html += '.table>thead>tr>th, .table>thead>tr>td, .table>tbody>tr>th, .table>tbody>tr>td, .table>tfoot>tr>th, .table>tfoot>tr>td {padding: 8px; line-height: 1.52857; vertical-align: top; border-top: 1px solid #eee;}';
        html += 'body { font - family: "Source Sans Pro", sans - serif; color: #656565;}';
        html += 'table { border-collapse: collapse; border-spacing: 0;width:100%;}';
        html += '.subTable>thead>tr>th, .subTable>thead>tr>td, .table>tbody>tr>th, .subTable>tbody>tr>td, .subTable>tfoot>tr>th, .subTable>tfoot>tr>td {padding: 3px 8px; line-height: 1.22857; vertical-align: top;}';
        html += '.ActivityInstancesInDataTableRI { margin: 0!important; padding: 1px 0!important;}';
        html += "</style>";
        html += '<table class=' + dt.table().node().className + '>';

        if (config.header) {
            html += '<thead>' + addRow(data.header, 'th') + '</thead>';
        }

        html += '<tbody>';
        for (var i = 0, ien = data.body.length; i < ien; i++) {
            html += addRow(data.body[i], 'td');
            var dt_note = dt.row(i).data().note ? dt.row(i).data().note : "-";
            html += addSubTable(dt_note, data.body[0].length - 1);
        }
        html += '</tbody>';

        if (config.footer && data.footer) {
            html += '<tfoot>' + addRow(data.footer, 'th') + '</tfoot>';
        }

        // Open a new window for the printable table
        var win = window.open();
        var title = config.title;
        if (typeof title === 'function') {
            title = title();
        }

        if (title.indexOf('*') !== -1) {
            title = title.replace('*', $('title').text());
        }
        win.document.close();
        var head = '<title>' + title + '</title>';
        try {
            win.document.head.innerHTML = head; // Work around for Edge
        }
        catch (e) {
            $(win.document.head).html(head); // Old IE
        }

        if (window.language == 'ar') {
            win.document.body.style.direction = 'rtl';
        }
        win.document.body.innerHTML =
            '<h1>' + title + '</h1>'
            + '<div>'
            + (typeof config.message === 'function' ?
                config.message(dt, button, config) :
                config.message
            )
            + '</div>'
            + html;


        $(win.document.body).addClass('dt-print-view');

        $('img', win.document.body).each(function (i, img) {
            img.setAttribute('src', _relToAbs(img.getAttribute('src')));
        });

        if (config.customize) {
            config.customize(win);
        }
        $(win.document.body).prepend('<table style="border:none;width:100%"><tr>' +
            '<td style="width:60%">' + document.getElementById("logoImg").outerHTML + '</td>' +
            '<td style="width:40%"><span>' + E.getTodayString() + '</span></div>');
        setTimeout(function () {
            if (config.autoPrint) {
                win.print();
                win.close();
            }
        }, 250);
    }
    E.reportoperationbycorrespondence.excelHTML5 = function (xlsx) {
        var table = $('#grdItemsHidden').DataTable();

        // Get number of columns to remove last hidden index column.
        var numColumns = table.columns().header().count();

        // Get sheet.

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

        var col = $('col', sheet);
        // Set the column width.
        $(col[1]).attr('width', 20);

        // Get a clone of the sheet data.        
        var sheetData = $('sheetData', sheet).clone();

        // Clear the current sheet data for appending rows.
        $('sheetData', sheet).empty();

        // Row index from last column.
        var DT_row;
        // Row count in Excel sheet.
        var rowCount = 1;

        // Itereate each row in the sheet data.
        $(sheetData).children().each(function (index) {

            // Used for DT row() API to get child data.
            var rowIndex = index - 1;

            // Don't process row if its the header row.
            if (index > 0) {

                // Get row
                var row = $(this.outerHTML);

                // Set the Excel row attr to the current Excel row count.
                row.attr('r', rowCount);

                var colCount = 1;

                // Iterate each cell in the row to change the row number.
                row.children().each(function (index) {
                    var cell = $(this);

                    // Set each cell's row value.
                    var rc = cell.attr('r');
                    rc = rc.replace(/\d+$/, "") + rowCount;
                    cell.attr('r', rc);

                    if (colCount === numColumns) {
                        DT_row = cell.text();
                        cell.html('');
                    }

                    colCount++;
                });

                // Get the row HTML and append to sheetData.
                row = row[0].outerHTML;
                $('sheetData', sheet).append(row);
                rowCount++;

                // Get the child data - could be any data attached to the row.
                var note = table.row(rowIndex, { search: 'none', order: 'index' }).data().note;

                // Prepare Excel formated row
                var headerRow = '<row r="' + rowCount + '" s="2">';
                headerRow += '<c t = "inlineStr" r="A' + rowCount +
                    '" s="2"><is><t>' + Resources.Note +
                    '</t></is></c>';
                headerRow += '<c t = "inlineStr" r="B' + rowCount +
                    '"><is><t>' + (note ? note : "-") +
                    '</t></is></c></row>';
                $('sheetData', sheet).append(headerRow);
                rowCount++; // Inc excelt row counter.
            } else {
                var row = $(this.outerHTML);

                var colCount = 1;

                // Remove the last header cell.
                row.children().each(function (index) {
                    var cell = $(this);

                    if (colCount === numColumns) {
                        cell.html('');
                    }

                    colCount++;
                });
                row = row[0].outerHTML;
                $('sheetData', sheet).append(row);
                rowCount++;
            }
        });

    }
    E.reportoperationbycorrespondence.pdfHTML5 = function (doc, pdfMake) {
        var todayString = 'right';
        var logoAlignment = 'left';
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            },
        };
        doc.defaultStyle =
        {
            font: 'Cairo',
        };

        // Get the row data in in table order and search applied
        var table = $('#grdItemsHidden').DataTable();
        var rowData = table.rows({ order: 'applied', search: 'applied' }).data();
        var headerLines = 0;  // Offset for accessing rowData array

        var newBody = []; // this will become our new body (an array of arrays(lines))
        //Loop over all lines in the table
        doc.content[1].table.body.forEach(function (line, i) {
            var bodyLines = [];
            if (i == 0) {
                for (var k = 0; k < line.length; k++) {
                    bodyLines.push(line[k]);
                }
            } else {
                for (var k = 0; k < line.length; k++) {
                    bodyLines.push({ text: line[k].text, style: 'rowStyle' });
                }
            }
            newBody.push(bodyLines);

            if (line[0].style !== 'tableHeader' && line[0].style !== 'tableFooter') {
                var data = rowData[i - headerLines];
                var childBody = [];

                childBody.push(
                    { text: Resources.Note, style: 'subheader' }
                );
                childBody.push(
                    { text: data.note ? data.note : "-", style: 'defaultStyle', colSpan: 4 }
                );
                newBody.push(childBody);
            } else {
                headerLines++;
            }
        });

        //Overwrite the old table body with the new one.
        doc.content[1].table.body = newBody;
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t');
            logoAlignment = 'right';
            todayString = 'left';
        }
        for (var i = 0; i < doc.content[1].table.body.length; i++) {
            var row = doc.content[1].table.body[i];
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            for (var col = 0; col < row.length; col++) {
                if (gArabic.test(row[col].text)) {
                    row[col].text = row[col].text.replace(/  +/g, ' ');
                    row[col].text = row[col].text.split(' ').join('\n');
                }
            }
        }

        doc.content[1].table.headerRows = 1;
        var singleWidth = 100 / doc.content[1].table.body[0].length;
        var arrayWidth = [];
        for (var i = 0; i < doc.content[1].table.body[0].length; i++) {
            arrayWidth.push(singleWidth + "%");
        }
        doc.content[1].table.widths = arrayWidth;

        doc.content[1].layout = 'lightHorizontalLines';

        doc.styles = {
            subheader: {
                fontSize: 9.5,
                bold: true,
                color: 'black',
                alignment: 'center'
            },
            rowStyle: {
                fontSize: 10,
                color: 'black',
                text: 'center',
                fillColor: '#F3F3F3',
                alignment: 'center'
            },
            tableHeader: {
                bold: true,
                fontSize: 11,
                color: 'white',
                fillColor: '#2D4154',
                alignment: 'center'
            },
            defaultStyle: {
                fontSize: 8.5,
                color: 'black',
                //fillColor: '#FFFFFF',
                alignment: 'left'
            },
            title: { alignment: "center", fontSize: 15 }
        };
        doc.content.splice(0, 0, {
            "table": {
                "headerRows": 0,
                "body": [
                    [
                        {
                            "image": E.GetLogoBase64String(),
                            "alignment": logoAlignment
                        }
                    ],
                    [
                        {
                            "text": E.getTodayString(),
                            alignment: todayString

                        }
                    ]
                ],
                widths: ['100%']
            },
            "layout": "noBorders"
        })
    }


    E.reportTransfersSentToStructure = {};

    E.reportTransfersSentToStructure.print = function (win) {
        $(win.document.body).prepend('<table style="border:none;width:100%"><tr>' +
            '<td style="width:60%">' + document.getElementById("logoImg").outerHTML + '</td>' +
            '<td style="width:40%"><span>' + E.getTodayString() + '</span></div>');
    };
    E.reportTransfersSentToStructure.excelHTML5 = function (xlsx) {
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];
        //$('row:even c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
    }
    E.reportTransfersSentToStructure.pdfHTML5 = function (doc) {
        var logoAlignment = 'left';
        var todayString = 'right';
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            },
            'DejaVuSansCondensed': {
                normal: 'DejaVuSansCondensed.ttf',
                bold: 'DejaVuSansCondensed.ttf',
                italics: 'DejaVuSansCondensed.ttf',
                bolditalics: 'DejaVuSansCondensed.ttf'
            }
        };
        doc.defaultStyle =
        {
            font: 'Cairo',
        };
        //doc.content[1].table.body.forEach(function (line, i) {
        //    if (line[0].style !== 'tableHeader' && line[0].style !== 'tableFooter') {
        //        line[6].style = line[6].style + "Icon";
        //    }
        //});
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t');
            logoAlignment = 'right';
            todayString = 'left';
        }
        for (var i = 0; i < doc.content[1].table.body.length; i++) {
            var row = doc.content[1].table.body[i];
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            for (var col = 0; col < row.length; col++) {
                if (gArabic.test(row[col].text)) {

                    row[col].text = row[col].text.replace(/  +/g, ' ');
                    row[col].text = row[col].text.split(' ').join('\n');
                }
            }
        }

        doc.content[1].table.widths = ["15%", "15%", "15%", "15%", "15%", "15%", "12%"];
        doc.styles.tableBodyEven.alignment = 'center';
        doc.styles.tableBodyOdd.alignment = 'center';
        doc.styles.tableBodyEvenIcon = {
            fontSize: 14,
            font: 'DejaVuSansCondensed',
            alignment: 'center'
        };
        doc.styles.tableBodyOddIcon = {
            fontSize: 14,
            font: 'DejaVuSansCondensed',
            fillColor: '#F3F3F3',
            alignment: 'center'
        };

        doc.content.splice(0, 0, {
            "table": {
                "headerRows": 0,
                "body": [
                    [
                        {
                            "image": E.GetLogoBase64String(),
                            "alignment": logoAlignment
                        }
                    ],
                    [
                        {
                            "text": E.getTodayString(),
                            alignment: todayString

                        }
                    ]
                ],
                widths: ['100%']
            },
            "layout": "noBorders"
        })
    }


    E.reportOutgoingFromDepartment = {};

    E.reportOutgoingFromDepartment.print = function (win) {
        $(win.document.body).prepend('<table style="border:none;width:100%"><tr>' +
            '<td style="width:60%">' + document.getElementById("logoImg").outerHTML + '</td>' +
            '<td style="width:40%"><span>' + E.getTodayString() + '</span></div>');
    };
    E.reportOutgoingFromDepartment.excelHTML5 = function (xlsx) {
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];
        //$('row:even c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
    }
    E.reportOutgoingFromDepartment.pdfHTML5 = function (doc) {
        var logoAlignment = 'left';
        var todayString = 'right';
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            },
            'DejaVuSansCondensed': {
                normal: 'DejaVuSansCondensed.ttf',
                bold: 'DejaVuSansCondensed.ttf',
                italics: 'DejaVuSansCondensed.ttf',
                bolditalics: 'DejaVuSansCondensed.ttf'
            }
        };
        doc.defaultStyle =
        {
            font: 'Cairo',
        };
        //doc.content[1].table.body.forEach(function (line, i) {
        //    if (line[0].style !== 'tableHeader' && line[0].style !== 'tableFooter') {
        //        line[6].style = line[6].style + "Icon";
        //    }
        //});
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t');
            logoAlignment = 'right';
            todayString = 'left';
        }
        for (var i = 0; i < doc.content[1].table.body.length; i++) {
            var row = doc.content[1].table.body[i];
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            if (gArabic.test(doc.content[0].text)) {
                row.reverse();
            }
            for (var col = 0; col < row.length; col++) {
                if (gArabic.test(row[col].text)) {

                    row[col].text = row[col].text.replace(/  +/g, ' ');
                    row[col].text = row[col].text.split(' ').join('\n');
                }
            }
        }
        
        doc.content[1].table.widths = ["25%", "15%", "15%", "15%", "15%"];
        doc.styles.tableBodyEven.alignment = 'center';
        doc.styles.tableBodyOdd.alignment = 'center';
        doc.styles.tableBodyEvenIcon = {
            fontSize: 14,
            font: 'DejaVuSansCondensed',
            alignment: 'center'
        };
        doc.styles.tableBodyOddIcon = {
            fontSize: 14,
            font: 'DejaVuSansCondensed',
            fillColor: '#F3F3F3',
            alignment: 'center'
        };

        doc.content.splice(0, 0, {
            "table": {
                "headerRows": 0,
                "body": [
                    [
                        {
                            "image": E.GetLogoBase64String(),
                            "alignment": logoAlignment
                        }
                    ],
                    [
                        {
                            "text": E.getTodayString(),
                            alignment: todayString

                        }
                    ]
                ],
                widths: ['200%']
            },
            "layout": "noBorders"
        })
    }

    E.FollowUpReport = {};
    E.FollowUpReport.print = function (win) {
        const doc = win.document;

        const table = doc.querySelector("table");

        if (table) {
            const thead = table.querySelector("thead tr");
            const tbody = table.querySelector("tbody");

            const visibleHeaders = Array.from(thead.querySelectorAll("th")).filter(th => th.innerText.trim() !== "").length;
            const dynamicWidth = visibleHeaders > 0 ? 100 / visibleHeaders + "%" : "auto";

            thead.querySelectorAll("th").forEach((th, colIndex) => {
                const hasHeaderContent = th.innerText.trim() !== "";

                if (hasHeaderContent) {
                    th.style.width = dynamicWidth;

                } else {
                    th.style.display = "none";
                }

                tbody.querySelectorAll("tr").forEach(row => {
                    const td = row.cells[colIndex];
                    if (td) {
                        if (hasHeaderContent) {
                            td.style.width = dynamicWidth;

                        } else {
                            td.style.display = "none";
                        }
                    }
                });
            });

            $(doc.body).prepend(
                '<table style="border:none;width:100%; margin-bottom: 10px;"><tr>' +
                '<td style="width:' + dynamicWidth + '">' + document.getElementById("logoImg").outerHTML + '</td>' +
                '<td style="width:' + dynamicWidth + '"><span>' + E.getTodayString() + '</span></td>' +
                '</tr></table>'
            );
        }
    };
    E.FollowUpReport.excelHTML5 = function (xlsx) {
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];
        //$('row:even c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
    }
    E.FollowUpReport.pdfHTML5 = function (doc) {
        // Check if the text is Arabic and adjust content and alignment
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t'); // Reverse words for Arabic

            // Adjust table body rows for RTL
            for (var i = 0; i < doc.content[1].table.body.length; i++) {
                var row = doc.content[1].table.body[i];
                row.reverse(); // Reverse row for RTL
                for (var col = 0; col < row.length; col++) {
                    if (gArabic.test(row[col].text)) {
                        row[col].text = row[col].text.replace(/  +/g, ' '); // Remove extra spaces
                        row[col].text = row[col].text.split(' ').join('\n'); // Replace spaces with line breaks
                    }
                }
            }
        }

        // Set up fonts and default style
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            }
        };

        doc.defaultStyle = {
            font: 'Cairo',
            fontSize: 10
        };

        // Step 1: Count non-empty cells in the first row to determine dynamic column widths
        let nonEmptyTextCount = 0;
        doc.content[1].table.body[0].forEach((col) => {
            if (col.text && col.text.trim() !== "") {
                nonEmptyTextCount++;
            }
        });

        const columnWidths = [];

        // Step 2: Calculate the width for each column based on non-empty columns
        const columnWidthPercentage = 100 / nonEmptyTextCount;

        // Ensure all rows have the same number of cells as the header
        doc.content[1].table.body.forEach(row => {
            while (row.length < nonEmptyTextCount) {
                row.push({ text: "" });
            }
        });

        // Step 3: Set the calculated widths to the table
        doc.content[1].table.body[0].forEach((col, index) => {
            if (col.text && col.text.trim() !== "") {
                // Allocate width if column text exists and is non-empty
                columnWidths.push(columnWidthPercentage + "%");
            } else {
                // Allocate 0% width if column text is empty
                columnWidths.push("0%");

                // Check the corresponding cell in the next row (body[1]) and clear its text
                if (doc.content[1].table.body[1] && doc.content[1].table.body[1][index]) {
                    doc.content[1].table.body[1][index].text = "";
                }
            }
        });
        // Apply alternating gray and white background to all content cells except the header row
        doc.content[1].table.body.slice(1).forEach((row, index) => { // Skip the first row (header)
            const rowColor = index % 2 === 0 ? '#f3f3f3' : '#ffffff'; // Alternate colors: gray and white
            row.forEach(cell => {
                cell.fillColor = rowColor; // Set background color for each cell in the row
                cell.alignment = 'center'; // Set content cell alignment to center
            });
        });

        // Apply calculated widths to the table
        doc.content[1].table.widths = columnWidths;

        const textStyle = {};
        function getTextWidth(text) {
            const canvas = document.createElement("canvas");
            const context = canvas.getContext("2d");
            return context.measureText(text).width;
        }

        // Handle text within table cells for proper alignment and formatting
        if (Array.isArray(doc.content[1].table.body[1])) {
            doc.content[1].table.body[1].forEach(cell => {
                if (cell.text) {
                    const textWidth = getTextWidth(cell.text);
                    const textWidthPercentage = (textWidth / 100) * 100;

                    // Check if text exceeds the available column width
                    if (textWidthPercentage > columnWidthPercentage) {


                        cell.style = {
                            maxWidth: columnWidthPercentage + '%',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis'
                        };
                    } else {
                        cell.style = {
                            width: columnWidthPercentage + '%',
                        };
                    }
                }
            });
        }
        // Set header and footer alignment styles
        doc.styles.tableBodyEven.alignment = 'center';
        doc.styles.tableBodyOdd.alignment = 'center';
        doc.styles.tableHeader.fontSize = 10;
        doc.defaultStyle.fontSize = 8;

        // Add header logo and date with adjusted alignment
        doc.content.splice(0, 0, {
            table: {
                headerRows: 0,
                body: [
                    [{ image: E.GetLogoBase64String(), alignment: 'left' }], // Adjust as needed
                    [{ text: E.getTodayString(), alignment: 'right' }] // Adjust as needed
                ],
                widths: ['100%']
            },
            layout: 'noBorders'
        });
    };

    E.reportExportedDocuments = {};
    E.reportExportedDocuments.print = function (win) {
        const doc = win.document;

        const table = doc.querySelector("table");

        if (table) {
            const thead = table.querySelector("thead tr");
            const tbody = table.querySelector("tbody");

            const visibleHeaders = Array.from(thead.querySelectorAll("th")).filter(th => th.innerText.trim() !== "").length;
            const dynamicWidth = visibleHeaders > 0 ? 100 / visibleHeaders + "%" : "auto";

            thead.querySelectorAll("th").forEach((th, colIndex) => {
                const hasHeaderContent = th.innerText.trim() !== "";

                if (hasHeaderContent) {
                    th.style.width = dynamicWidth;

                } else {
                    th.style.display = "none";
                }

                tbody.querySelectorAll("tr").forEach(row => {
                    const td = row.cells[colIndex];
                    if (td) {
                        if (hasHeaderContent) {
                            td.style.width = dynamicWidth;

                        } else {
                            td.style.display = "none";
                        }
                    }
                });
            });

            $(doc.body).prepend(
                '<table style="border:none;width:100%; margin-bottom: 10px;"><tr>' +
                '<td style="width:' + dynamicWidth + '">' + document.getElementById("logoImg").outerHTML + '</td>' +
                '<td style="width:' + dynamicWidth + '"><span>' + E.getTodayString() + '</span></td>' +
                '</tr></table>'
            );
        }
    };
    E.reportExportedDocuments.excelHTML5 = function (xlsx) {
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];
        //$('row:even c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }


        // === Find column letter for "Id" ===
        let idColLetter = null;

        const headerCells = $('sheetData row:first c', sheet);
        headerCells.each(function () {
            const cell = $(this);
            let cellText = '';

            if (cell.attr('t') === 'inlineStr') {
                cellText = $('is t', cell).text().trim().toLowerCase();
            } else {
                cellText = $('v', cell).text().trim().toLowerCase();
            }

            if (cellText === 'id') {
                const cellRef = cell.attr('r'); // e.g., "B1"
                idColLetter = cellRef.replace(/[0-9]/g, ''); // => "B"
                return false; // break loop
            }
        });

        if (!idColLetter) return; // nothing to remove

        // === Remove all cells in the Id column
        $('sheetData row c', sheet).each(function () {
            const cellRef = $(this).attr('r'); // e.g., "B2"
            const colLetter = cellRef.replace(/[0-9]/g, '');
            if (colLetter === idColLetter) {
                $(this).remove();
            }
        });

        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');

    }
    E.reportExportedDocuments.pdfHTML5 = function (doc) {
        const headerRow = doc.content[1].table.body[0];
        const idIndex = headerRow.findIndex(cell => cell.text === 'Id');

        if (idIndex !== -1) {
            // Remove 'Id' from the header row
            headerRow.splice(idIndex, 1);

            // Also remove 'Id' column from all other rows
            for (let i = 1; i < doc.content[1].table.body.length; i++) {
                doc.content[1].table.body[i].splice(idIndex, 1);
            }
        }
        // Check if the text is Arabic and adjust content and alignment
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t'); // Reverse words for Arabic

            // Adjust table body rows for RTL
            for (var i = 0; i < doc.content[1].table.body.length; i++) {
                var row = doc.content[1].table.body[i];
                row.reverse(); // Reverse row for RTL
                for (var col = 0; col < row.length; col++) {
                    if (gArabic.test(row[col].text)) {
                        row[col].text = row[col].text.replace(/  +/g, ' '); // Remove extra spaces
                        row[col].text = row[col].text.split(' ').join('\n'); // Replace spaces with line breaks
                    }
                }
            }
        }

        // Set up fonts and default style
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            }
        };

        doc.defaultStyle = {
            font: 'Cairo',
            fontSize: 10
        };

        // Step 1: Count non-empty cells in the first row to determine dynamic column widths
        let nonEmptyTextCount = 0;
        doc.content[1].table.body[0].forEach((col) => {
            if (col.text && col.text.trim() !== "") {
                nonEmptyTextCount++;
            }
        });

        const columnWidths = [];

        // Step 2: Calculate the width for each column based on non-empty columns
        const columnWidthPercentage = 100 / nonEmptyTextCount;

        // Ensure all rows have the same number of cells as the header
        doc.content[1].table.body.forEach(row => {
            while (row.length < nonEmptyTextCount) {
                row.push({ text: "" });
            }
        });

        // Step 3: Set the calculated widths to the table
        doc.content[1].table.body[0].forEach((col, index) => {
            if (col.text && col.text.trim() !== "") {
                // Allocate width if column text exists and is non-empty
                columnWidths.push(columnWidthPercentage + "%");
            } else {
                // Allocate 0% width if column text is empty
                columnWidths.push("0%");

                // Check the corresponding cell in the next row (body[1]) and clear its text
                if (doc.content[1].table.body[1] && doc.content[1].table.body[1][index]) {
                    doc.content[1].table.body[1][index].text = "";
                }
            }
        });
        // Apply alternating gray and white background to all content cells except the header row
        doc.content[1].table.body.slice(1).forEach((row, index) => { // Skip the first row (header)
            const rowColor = index % 2 === 0 ? '#f3f3f3' : '#ffffff'; // Alternate colors: gray and white
            row.forEach(cell => {
                cell.fillColor = rowColor; // Set background color for each cell in the row
                cell.alignment = 'center'; // Set content cell alignment to center
            });
        });

        // Apply calculated widths to the table
        doc.content[1].table.widths = columnWidths;

        const textStyle = {};
        function getTextWidth(text) {
            const canvas = document.createElement("canvas");
            const context = canvas.getContext("2d");
            return context.measureText(text).width;
        }

        // Handle text within table cells for proper alignment and formatting
        if (Array.isArray(doc.content[1].table.body[1])) {
            doc.content[1].table.body[1].forEach(cell => {
                if (cell.text) {
                    const textWidth = getTextWidth(cell.text);
                    const textWidthPercentage = (textWidth / 100) * 100;

                    // Check if text exceeds the available column width
                    if (textWidthPercentage > columnWidthPercentage) {


                        cell.style = {
                            maxWidth: columnWidthPercentage + '%',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis'
                        };
                    } else {
                        cell.style = {
                            width: columnWidthPercentage + '%',
                        };
                    }
                }
            });
        }

        // Set header and footer alignment styles
        doc.styles.tableBodyEven.alignment = 'center';
        doc.styles.tableBodyOdd.alignment = 'center';
        doc.styles.tableHeader.fontSize = 10;
        doc.defaultStyle.fontSize = 8;

        // Add header logo and date with adjusted alignment
        doc.content.splice(0, 0, {
            table: {
                headerRows: 0,
                body: [
                    [{ image: E.GetLogoBase64String(), alignment: 'left' }], // Adjust as needed
                    [{ text: E.getTodayString(), alignment: 'right' }] // Adjust as needed
                ],
                widths: ['100%']
            },
            layout: 'noBorders'
        });
    };

    E.reportRejectedDocuments = {};
    E.reportRejectedDocuments.print = function (win) {
        const doc = win.document;

        const table = doc.querySelector("table");

        if (table) {
            const thead = table.querySelector("thead tr");
            const tbody = table.querySelector("tbody");

            const visibleHeaders = Array.from(thead.querySelectorAll("th")).filter(th => th.innerText.trim() !== "").length;
            const dynamicWidth = visibleHeaders > 0 ? 100 / visibleHeaders + "%" : "auto";

            thead.querySelectorAll("th").forEach((th, colIndex) => {
                const hasHeaderContent = th.innerText.trim() !== "";

                if (hasHeaderContent) {
                    th.style.width = dynamicWidth;

                } else {
                    th.style.display = "none";
                }

                tbody.querySelectorAll("tr").forEach(row => {
                    const td = row.cells[colIndex];
                    if (td) {
                        if (hasHeaderContent) {
                            td.style.width = dynamicWidth;

                        } else {
                            td.style.display = "none";
                        }
                    }
                });
            });

            $(doc.body).prepend(
                '<table style="border:none;width:100%; margin-bottom: 10px;"><tr>' +
                '<td style="width:' + dynamicWidth + '">' + document.getElementById("logoImg").outerHTML + '</td>' +
                '<td style="width:' + dynamicWidth + '"><span>' + E.getTodayString() + '</span></td>' +
                '</tr></table>'
            );
        }
    };
    E.reportRejectedDocuments.excelHTML5 = function (xlsx) {
        //var sheet = xlsx.xl.worksheets['sheet1.xml'];
        //$('row:even c', sheet).each(function (i) {
        //    $(this).attr('s', '5');
        //});
        //$('row:first c', sheet).attr('s', '22');
        //$('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');


        var sheet = xlsx.xl.worksheets['sheet1.xml'];

        // === Add RTL or LTR based on current language ===
        var lang = window.language || 'en'; // adjust this if your language detection is different
        var isRTL = lang.startsWith('ar');   // RTL for Arabic

        // Check if <sheetViews> exists, add or update it
        var sheetViews = $('sheetViews', sheet);
        if (sheetViews.length === 0) {
            $('worksheet', sheet).prepend(
                `<sheetViews><sheetView workbookViewId="0" rightToLeft="${isRTL ? 1 : 0}"/></sheetViews>`
            );
        } else {
            $('sheetView', sheetViews).attr('rightToLeft', isRTL ? '1' : '0');
        }


        // === Find column letter for "Id" ===
        let idColLetter = null;

        const headerCells = $('sheetData row:first c', sheet);
        headerCells.each(function () {
            const cell = $(this);
            let cellText = '';

            if (cell.attr('t') === 'inlineStr') {
                cellText = $('is t', cell).text().trim().toLowerCase();
            } else {
                cellText = $('v', cell).text().trim().toLowerCase();
            }

            if (cellText === 'id') {
                const cellRef = cell.attr('r'); // e.g., "B1"
                idColLetter = cellRef.replace(/[0-9]/g, ''); // => "B"
                return false; // break loop
            }
        });

        if (!idColLetter) return; // nothing to remove

        // === Remove all cells in the Id column
        $('sheetData row c', sheet).each(function () {
            const cellRef = $(this).attr('r'); // e.g., "B2"
            const colLetter = cellRef.replace(/[0-9]/g, '');
            if (colLetter === idColLetter) {
                $(this).remove();
            }
        });
        // === Your existing style logic ===
        $('row:even c', sheet).each(function (i) {
            $(this).attr('s', '5');
        });
        $('row:first c', sheet).attr('s', '22');

        // Optional: Rename the worksheet tab
        $('sheets sheet', xlsx.xl['workbook.xml']).attr('name', 'New name');
    }
    E.reportRejectedDocuments.pdfHTML5 = function (doc) {

        const headerRow = doc.content[1].table.body[0];
        const idIndex = headerRow.findIndex(cell => cell.text === 'Id');

        if (idIndex !== -1) {
            // Remove 'Id' from the header row
            headerRow.splice(idIndex, 1);

            // Also remove 'Id' column from all other rows
            for (let i = 1; i < doc.content[1].table.body.length; i++) {
                doc.content[1].table.body[i].splice(idIndex, 1);
            }
        }
        // Check if the text is Arabic and adjust content and alignment
        if (gArabic.test(doc.content[0].text)) {
            doc.content[0].text = doc.content[0].text.split(' ').reverse().join('\t'); // Reverse words for Arabic

            // Adjust table body rows for RTL
            for (var i = 0; i < doc.content[1].table.body.length; i++) {
                var row = doc.content[1].table.body[i];
                row.reverse(); // Reverse row for RTL
                for (var col = 0; col < row.length; col++) {
                    if (gArabic.test(row[col].text)) {
                        row[col].text = row[col].text.replace(/  +/g, ' '); // Remove extra spaces
                        row[col].text = row[col].text.split(' ').join('\n'); // Replace spaces with line breaks
                    }
                }
            }
        }

        // Set up fonts and default style
        pdfMake.fonts = {
            'Cairo': {
                normal: 'Cairo-Regular.ttf',
                bold: 'Cairo-Regular.ttf',
                italics: 'Cairo-Regular.ttf',
                bolditalics: 'Cairo-Regular.ttf'
            }
        };

        doc.defaultStyle = {
            font: 'Cairo',
            fontSize: 10
        };

        // Step 1: Count non-empty cells in the first row to determine dynamic column widths
        let nonEmptyTextCount = 0;
        doc.content[1].table.body[0].forEach((col) => {
            if (col.text && col.text.trim() !== "") {
                nonEmptyTextCount++;
            }
        });

        const columnWidths = [];

        // Step 2: Calculate the width for each column based on non-empty columns
        const columnWidthPercentage = 100 / nonEmptyTextCount;

        // Ensure all rows have the same number of cells as the header
        doc.content[1].table.body.forEach(row => {
            while (row.length < nonEmptyTextCount) {
                row.push({ text: "" });
            }
        });

        // Step 3: Set the calculated widths to the table
        doc.content[1].table.body[0].forEach((col, index) => {
            if (col.text && col.text.trim() !== "") {
                // Allocate width if column text exists and is non-empty
                columnWidths.push(columnWidthPercentage + "%");
            } else {
                // Allocate 0% width if column text is empty
                columnWidths.push("0%");

                // Check the corresponding cell in the next row (body[1]) and clear its text
                if (doc.content[1].table.body[1] && doc.content[1].table.body[1][index]) {
                    doc.content[1].table.body[1][index].text = "";
                }
            }
        });
        // Apply alternating gray and white background to all content cells except the header row
        doc.content[1].table.body.slice(1).forEach((row, index) => { // Skip the first row (header)
            const rowColor = index % 2 === 0 ? '#f3f3f3' : '#ffffff'; // Alternate colors: gray and white
            row.forEach(cell => {
                cell.fillColor = rowColor; // Set background color for each cell in the row
                cell.alignment = 'center'; // Set content cell alignment to center
            });
        });

        // Apply calculated widths to the table
        doc.content[1].table.widths = columnWidths;

        const textStyle = {};
        function getTextWidth(text) {
            const canvas = document.createElement("canvas");
            const context = canvas.getContext("2d");
            return context.measureText(text).width;
        }

        // Handle text within table cells for proper alignment and formatting
        if (Array.isArray(doc.content[1].table.body[1])) {
            doc.content[1].table.body[1].forEach(cell => {
                if (cell.text) {
                    const textWidth = getTextWidth(cell.text);
                    const textWidthPercentage = (textWidth / 100) * 100;

                    // Check if text exceeds the available column width
                    if (textWidthPercentage > columnWidthPercentage) {


                        cell.style = {
                            maxWidth: columnWidthPercentage + '%',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis'
                        };
                    } else {
                        cell.style = {
                            width: columnWidthPercentage + '%',
                        };
                    }
                }
            });
        }

        // Set header and footer alignment styles
        doc.styles.tableBodyEven.alignment = 'center';
        doc.styles.tableBodyOdd.alignment = 'center';
        doc.styles.tableHeader.fontSize = 10;
        doc.defaultStyle.fontSize = 8;

        // Add header logo and date with adjusted alignment
        doc.content.splice(0, 0, {
            table: {
                headerRows: 0,
                body: [
                    [{ image: E.GetLogoBase64String(), alignment: 'left' }], // Adjust as needed
                    [{ text: E.getTodayString(), alignment: 'right' }] // Adjust as needed
                ],
                widths: ['100%']
            },
            layout: 'noBorders'
        });
    };
    return E;
}(CustomDatatableReport));

globalThis.CTSReportDatatblesCustomComponents = new class {
    constructor() {
        this.CustomDatatableReport = CustomDatatableReport;
    }
}();