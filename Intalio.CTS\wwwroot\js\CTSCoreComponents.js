﻿
var appRouter = Backbone.Router.extend({
    routes: {
        'systemdelegation': 'systemDelegationRoute',
        'delegation': 'delegationRoute',
        'exceptionlog': 'exceptionLogRoute',
        'purpose': 'purposeRoute',
        'status': 'statusRoute',
        'priority': 'priorityRoute',
        'applicationserver': 'applicationServerRoute',
        'parameter': 'parameterRoute',
        'translatordictionary': 'translatorDictionaryRoute',
        'scanconfiguration': 'scanConfigurationRoute',
        'notificationtemplate': 'notificationTemplateRoute',
        'organizationManagement': 'organizationManagementRoute',
        'manageTemplate': 'manageTemplateRoute',
        'manageCategory': 'manageCategoryRoute',
        'categoryreferencenumber': 'categoryReferenceNumberRoute',
        'categoryreferencecounter': 'categoryReferenceCounterRoute',
        'classification': 'classificationRoute',
        'importance': 'importanceRoute',
        'privacy': 'privacyRoute',
        'attachmentfolder': 'attachmentfolderRoute',
        'todolist': 'toDoListRoute',
        'assembly': 'assemblyRoute',
        'activitylog': 'activityLogRoute',
        'menu': 'menuRoute',
        'action': 'actionRoute',
        'tab': 'tabRoute',
        'user': 'userRoute',
        'role': 'roleRoute',
        'node': 'nodeRoute',
        'category': 'categoryRoute',
        'lookup': 'lookupRoute',
        'documenttype': 'documenttypeRoute',
        //'search': 'searchRoute',
        //'search/:term': 'searchRoute',
        'advanceSearch': 'advanceSearchRoute',
        'advanceSearch/:delegationId': 'advanceSearchRoute',
        'advanceSearchConfiguration': 'advanceSearchConfigurationRoute',
        'nonarchivedattachmentstypes': 'nonArchivedAttachmentsTypesRoute',
        'barcodeconfiguration': 'barcodeConfigurationRoute',
        'filingPlan': 'filingPlanRoute',
        'nodelist': 'nodeListRoute',
        'bookmarks': 'bookmarksRoute',
        'systemdashboard': 'systemDashboardRoute',
        'userdashboard': 'userDashboardRoute',
        'systeminprogresstransfers': 'systemInProgressTransfersRoute',
        'averagedurationforcorrespondencecompletion': 'averageDurationForCorrespondenceCompletionRoute',
        'averagedurationforcorrespondencedelay': 'averageDurationForCorrespondenceDelayRoute',
        'averagedurationfortransfercompletion': 'averageDurationForTransferCompletionRoute',
        'averagedurationfortransferdelay': 'averageDurationForTransferDelayRoute',
        'reportinprogresstransfers': 'reportInProgressTransfersRoute',
        'reportcompletedtransfers': 'reportCompletedTransfersRoute',
        'reportOperationByUser': 'reportOperationByUserRoute',
        'reportOperationByCorrespondence': 'reportOperationByCorrespondenceRoute',
        'reportstatisticalcorrespondences': 'reportStatisticalCorrespondencesRoute',
        'reportcorrespondencedetailfilter': 'reportCorrespondenceDetailFilterRoute',
        'reportinprogresscorrespondences': 'reportInProgressCorrespondencesRoute',
        'reportcompletedcorrespondences': 'reportCompletedCorrespondencesRoute',
        'entitygroup': 'entityGroupRoute',
        'manageCorrespondence': 'manageCorrespondenceRoute',
        'autoForward': 'autoForwardRoute',
        'favoriteStructures': 'favoriteStructuresRoute',
        'distributionList': 'distributionListRoute',
        'movetransfers': 'movetransfersRoute',
        'committee': 'committeeRoute',
        'createMeetingAgenda': 'meetingAgendaRoute',
        'listsecureusers': 'listsecureusers',
        '': 'rootRoute',
        'g2g/:G2G_DocumentInbox': 'g2gRoute',
        'g2g/:G2G_DocumentInboxRejected': 'g2gRoute',
        'g2g/:G2G_DocumentInboxPendingToReceive': 'g2gRoute',
        'g2g/:G2G_DocumentInboxRecalled': 'g2gRoute',
        'g2g/:G2G_DocumentInboxReceiveOrReject': 'g2gRoute',
        'g2g/:G2G_DocumentInboxQueued': 'g2gRoute',
        'g2g/:G2G_DocumentInboxSent': 'g2gRoute',
        'g2g/:G2G_DocumentInboxIncomingRejected': 'g2gRoute',
        'g2g/:G2G_DocumentInboxIncomingRecalled': 'g2gRoute',
        'g2gOrganizationMapping': 'g2gOrganizationMappingRoute',

    },
    systemDelegationRoute: function () {
        CTSCoreComponents.CustomMenus.systemDelegation();
    },
    delegationRoute: function () {
        CustomMenus.delegationRoute();
    },
    rootRoute: function () {
        CustomMenus.rootRoute();
    },
    applicationServerRoute: function () {
        CustomMenus.applicationServerRoute();
    },
    parameterRoute: function () {
        CustomMenus.parameterRoute();
    },
    exceptionLogRoute: function () {
        CustomMenus.exceptionLogRoute();
    },
    purposeRoute: function () {
        CustomMenus.purposeRoute();
    },
    statusRoute: function () {
        CustomMenus.statusRoute();
    },
    priorityRoute: function () {
        CustomMenus.priorityRoute();
    },
    translatorDictionaryRoute: function () {
        CustomMenus.translatorDictionaryRoute();
    },
    scanConfigurationRoute: function () {
        CustomMenus.scanConfigurationRoute();
    },
    notificationTemplateRoute: function () {
        CustomMenus.notificationTemplateRoute();
    },
    organizationManagementRoute: function () {
        CustomMenus.organizationManagementRoute();
    },
    manageTemplateRoute: function () {
        CustomMenus.manageTemplateRoute();
    },
    manageCategoryRoute: function () {
        CustomMenus.manageCategoryRoute();
    },
    categoryReferenceNumberRoute: function () {
        CustomMenus.categoryReferenceNumberRoute();
    },
    categoryReferenceCounterRoute: function () {
        CustomMenus.categoryReferenceCounterRoute();
    },
    classificationRoute: function () {
        CustomMenus.classificationRoute();
    },
    importanceRoute: function () {
        CustomMenus.importanceRoute();
    },
    privacyRoute: function () {
        CustomMenus.privacyRoute();
    },
    toDoListRoute: function () {
        CustomMenus.toDoListRoute();
    },
    assemblyRoute: function () {
        CustomMenus.assemblyRoute();
    },
    activityLogRoute: function () {
        CustomMenus.activityLogRoute();
    },
    menuRoute: function () {
        CustomMenus.menuRoute();
    },
    actionRoute: function () {
        CustomMenus.actionRoute();
    },
    tabRoute: function () {
        CustomMenus.tabRoute();
    },
    userRoute: function () {
        CustomMenus.userRoute();
    },
    roleRoute: function () {
        CustomMenus.roleRoute();
    },
    nodeRoute: function () {
        CustomMenus.nodeRoute();
    },
    categoryRoute: function () {
        CustomMenus.categoryRoute();
    },
    attachmentfolderRoute: function () {
        CustomMenus.attachmentfolderRoute();
    },
    lookupRoute: function () {
        CustomMenus.lookupRoute();
    },
    documenttypeRoute: function () {
        CustomMenus.documenttypeRoute();
    },
    searchRoute: function (term) {
        CustomMenus.searchRoute(term);
    },
    advanceSearchRoute: function (delegationId) {
        CustomMenus.advanceSearchRoute(delegationId);
    },
    advanceSearchConfigurationRoute: function () {
        CustomMenus.advanceSearchConfigurationRoute();
    },
    noteRoute: function () {
        CustomMenus.noteRoute();
    },
    barcodeConfigurationRoute: function () {
        CustomMenus.barcodeConfigurationRoute();
    },
    filingPlanRoute: function () {
        CustomMenus.filingPlanRoute();
    },
    nonArchivedAttachmentsTypesRoute: function () {
        CustomMenus.nonArchivedAttachmentsTypesRoute();
    },
    nodeListRoute: function () {
        CustomMenus.nodeListRoute();
    },
    bookmarksRoute: function () {
        CustomMenus.bookmarksRoute();
    },
    systemDashboardRoute: function () {
        CustomMenus.systemDashboardRoute();
    },
    userDashboardRoute: function () {
        CustomMenus.userDashboardRoute();
    },
    averageDurationForCorrespondenceCompletionRoute: function () {
        CustomMenus.averageDurationForCorrespondenceCompletionRoute();
    },
    averageDurationForCorrespondenceDelayRoute: function () {
        CustomMenus.averageDurationForCorrespondenceDelayRoute();
    },
    averageDurationForTransferCompletionRoute: function () {
        CustomMenus.averageDurationForTransferCompletionRoute();
    },
    averageDurationForTransferDelayRoute: function () {
        CustomMenus.averageDurationForTransferDelayRoute();
    },
    reportStatisticalCorrespondencesRoute: function () {
        CustomMenus.reportStatisticalCorrespondencesRoute();
    },
    reportCorrespondenceDetailFilterRoute: function () {
        CustomMenus.reportCorrespondenceDetailFilterRoute();
    },
    reportInProgressTransfersRoute: function () {
        CustomMenus.reportInProgressTransfersRoute();
    },
    reportCompletedTransfersRoute: function () {
        CustomMenus.reportCompletedTransfersRoute();
    },
    reportOperationByUserRoute: function () {
        CustomMenus.reportOperationByUserRoute();
    },
    reportOperationByCorrespondenceRoute: function () {
        CustomMenus.reportOperationByCorrespondenceRoute();
    },
    reportInProgressCorrespondencesRoute: function () {
        CustomMenus.reportInProgressCorrespondencesRoute();
    },
    reportCompletedCorrespondencesRoute: function () {
        CustomMenus.reportCompletedCorrespondencesRoute();
    },
    entityGroupRoute: function () {
        CustomMenus.entityGroupRoute();
    },
    favoriteStructuresRoute: function () {
        CustomMenus.favoriteStructuresRoute();
    },
    distributionListRoute: function () {
        CustomMenus.distributionListRoute();
    },
    manageCorrespondenceRoute: function () {
        CustomMenus.manageCorrespondenceRoute();
    }
    , autoForwardRoute: function () {
        CustomMenus.autoForwardRoute();
    },
    movetransfersRoute: function () {
        CustomMenus.movetransfers();
    },
    committeeRoute: function () {

        CustomMenus.committee();
    },
    meetingAgendaRoute: function () {

        CustomMenus.createMeeatinAgenda();
    },
    g2gRoute: function (ViewName) {
        CustomNodes.g2g(ViewName);
    },
    listsecureusers: function() {
        CustomMenus.listSecureUsers()
    }
});
var app = new appRouter();
var gLocked = false;
var gOwnerUserId;
var CustomTabs = (function (E) {
    E = {};

    E.openTransferDetail = function (transferId, delegationId, ComponentId, tabId, documentId, readOnly, fromSent, categoryId, fromInbox, DocumentIsCompleted, actionName, modalComponentId) {
        

        var params = { "id": transferId };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data) {
            gLocked = false;
            var wrapper = $("#" + ComponentId + '_tab' + tabId);
            var model = new CTSComponents.MyTransfer.MyTransfer();
            model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            if (!readOnly) {
                readOnly = data.viewMode;
            }
            model.parentTransferId = (data.parentTransferId == null || data.parentTransferId == undefined) ? null : data.parentTransferId;
            model.isCCed = data.cced;
            model.readonly = readOnly;
            model.transferId = transferId;
            model.delegationId = delegationId;
            model.sendingEntity = data.sendingEntity;
            model.receivingEntity = data.receivingEntity;
            model.subject = data.subject;
            model.fromStructure = data.fromStructure;
            model.fromUser = data.fromUser;
            model.toStructure = data.toStructure;
            model.toUser = data.toUser;
            model.purpose = data.purpose;
            model.createdDate = data.createdDate;
            model.dueDate = data.dueDate;
            model.openedDate = data.openedDate;
            model.closedDate = data.closedDate;
            model.instruction = data.instruction;
            model.privacyId = data.privacyId;
            model.fromStructureId = data.fromStructureId;
            model.documentId = documentId;
            model.receivingEntityId = data.receivingEntityId;
            model.ownerUserId = data.ownerUserId;
            model.instruction = data.instruction;
            model.VoiceNote = data.voiceNote;
            model.toStructureId = data.toStructureId;
            model.replyToEntity = [{ id: data.fromStructureId, text: data.fromStructure }];
            model.sentToUser = data.sentToUser;
            model.withViewer = window.OpenCorrespondenceMode === CorrespondenceMode.WithViewer;
            model.fromSent = fromSent;
            if (modalComponentId != null && modalComponentId != undefined) {
                model.parentComponentId = modalComponentId;
            }
            else {
                model.parentComponentId = ComponentId;
            }
            model.categoryId = categoryId;
            model.fromInbox = fromInbox;
            model.DocumentIsCompleted = DocumentIsCompleted;
            model.closedTransfer = data.closedDate ? true : false;
            model.byTemplate = data.byTemplate;
            model.forSignature = data.forSignature;
            model.workflowStepId = data.workflowStepId;
            model.hasReferenceNumber = data.hasReferenceNumber;
            model.initiatorUser = data.initiatorUser;
            model.isWorkflowReturned = data.isWorkflowReturned;
            model.MeetingAgenda = (categoryId == window.MeetingAgendaId) ? true : false;
            model.hasAttachments = data.hasAttachments;
            model.hasUserCofigureSignature = data.hasUserCofigureSignature;
            model.nextStepUserName = data.nextStepUserName;
            model.allowSign = data.allowSign;
            model.referenceNumber = data.referenceNumber;
            if (actionName != undefined)
                model.actionName = actionName;
            var currentCategoryModel = new CTSComponents.CategoryModel().findFullById(categoryId);
            if (typeof currentCategoryModel !== 'undefined' && currentCategoryModel !== "" && currentCategoryModel !== null) {
                if (currentCategoryModel.basicAttribute !== "" && currentCategoryModel.basicAttribute !== null) {
                    let basicAttributes = JSON.parse(currentCategoryModel.basicAttribute);
                    if (basicAttributes.length > 0) {
                        let receivingEntityObj = $.grep(basicAttributes, function (e) {
                            return e.Name === "ReceivingEntity";
                        });
                        if (receivingEntityObj[0].BroadcastReceivingEntity && receivingEntityObj[0].Type === "internal") {
                            model.isInternalBroadcast = true;
                            model.isBroadcast = true;
                        } else if (receivingEntityObj[0].BroadcastReceivingEntity) {
                            model.isBroadcast = true;
                        }
                    }
                }
            }

            var myTransferView = new CTSComponents.MyTransfer.MyTransferView(wrapper, model);
            myTransferView.render();
            gOwnerUserId = data.ownerUserId;

        }, null, null, null, false);
    }

    E.openVisualTracking = function (delegationId, ComponentId, tabId, documentId) {

        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new CTSComponents.VisualTracking.VisualTracking();
        model.delegationId = delegationId;
        model.documentId = documentId;
        var documentView = new CTSComponents.VisualTracking.VisualTrackingView(wrapper, model);
        documentView.render();
        if (window.OpenCorrespondenceMode !== "OpenCorrespondenceDefault") {
            $("#" + model.ComponentId + "_trackingChart").css("height", "353px");
        }


    }
    E.openNoteByTask = function (transferId, documentId, readOnly, delegationId, ComponentId, tabId, actionName) {
        var params = { "id": transferId };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data) {

            if (!readOnly) {
                readOnly = data.viewMode;
            }
        }, null, null, null, false);
        gLocked = false;
        var model = new CTSComponents.DocumentNote.Note();
        model.transferId = transferId;
        model.documentId = documentId;
        model.delegationId = delegationId;
        model.readOnly = readOnly;
        if (actionName != undefined)
            model.actionName = actionName;
        if (categoryId == window.FollowUpCategory)
            model.readOnly = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var noteView = new CTSComponents.DocumentNote.NoteView(wrapper, model);
        noteView.render();

    }
    E.openNonArchivedAttachmentsByTask = function (transferId, documentId, readOnly, delegationId, ComponentId, tabId, actionName) {
        var params = { "id": transferId };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data) {

            if (!readOnly) {
                readOnly = data.viewMode;
            }
        }, null, null, null, false);
        gLocked = false;
        var model = new CTSComponents.DocumentNonArchivedAttachment.NonArchivedAttachments();
        model.transferId = transferId;
        model.documentId = documentId;
        model.delegationId = delegationId;
        model.readOnly = readOnly;
        if (actionName != undefined)
            model.actionName = actionName;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var nonArchivedAttachmentList = new CTSComponents.DocumentNonArchivedAttachment.NonArchivedAttachmentsView(wrapper, model);
        nonArchivedAttachmentList.render();


    }
    E.openLinkedCorrespondences = function (transferId, documentId, delegationId, readOnly, ComponentId, tabId, actionName) {
        var params = { "id": transferId };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data) {

            if (!readOnly) {
                readOnly = data.viewMode;
            }
        }, null, null, null, false);
        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new CTSComponents.DocumentLinkCorrespondence.LinkedCorrespondence();
        model.transferId = transferId;
        model.documentId = documentId;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.categories = new CTSComponents.Categories().get(window.language);
        model.delegationId = delegationId;
        model.readOnly = readOnly;
        if (actionName != 'undefined')
            model.actionName = actionName;
        var documentView = new CTSComponents.DocumentLinkCorrespondence.LinkedCorrespondenceView(wrapper, model);
        documentView.render();

    }
    E.openAttachments = function (transferId, documentId, delegationId, readOnly, categoryId, fromInbox, fromDraft, isCced, ComponentId, tabId, parentLinkedDocumentId, actionName) {
        var params = { "id": transferId };
        params.categoryId = categoryId;
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        var showAttachmentProperties = false;
        Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data) {

            if (!readOnly) {
                readOnly = data.viewMode;
            }
            showAttachmentProperties = data.showAttachmentProperties
        }, null, null, null, false);
        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new CTSComponents.DocumentAttachment.Attachment();
        model.transferId = transferId;
        model.documentId = documentId;
        model.readOnly = readOnly;
        model.delegationId = delegationId;
        model.categoryId = categoryId;
        model.fromInbox = fromInbox;
        model.fromDraft = fromDraft;
        model.isCced = isCced;
        model.ownerUserId = gOwnerUserId;
        model.parentLinkedDocumentId = parentLinkedDocumentId;
        model.parentComponentId = ComponentId;
        model.showAttachmentProperties = showAttachmentProperties;
        if (actionName != undefined)
            model.actionName = actionName;

        var attachmentView = new CTSComponents.DocumentAttachment.AttachmentView(wrapper, model);
        attachmentView.render();

    }
    E.openActivityLog = function (documentId, delegationId, ComponentId, tabId) {

        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new CTSComponents.ActivityLogTimeline.ActivityLogTimeline();
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.documentId = documentId;
        model.delegationId = delegationId;
        var documentView = new CTSComponents.ActivityLogTimeline.ActivityLogTimelineView(wrapper, model);
        documentView.render();

    }
    E.openTransfersHistory = function (transferId, documentId, sentToUser, delegationId, ComponentId, tabId) {

        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new CTSComponents.TransferHistory.TransferHistory();
        model.transferId = transferId;
        model.documentId = documentId;
        model.delegationId = delegationId;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.sentToUser = sentToUser;
        var TransferHistoryView = new CTSComponents.TransferHistory.TransferHistoryView(wrapper, model);
        TransferHistoryView.render();

    }

    E.openDocument = function (data, readOnly, ComponentId, tabId, documentId, transferId, categoryId, fromDraft, actionName, modalComponentId) {


        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new CTSComponents.DocumentMetadata.Document();
        if (tabId == window.BasketAttribute) {
            model.actionName = "Attribute.Save";
        }
        model.id = documentId;
        model.categoryId = data.categoryId;
        model.categoryName = data.categoryName;
        model.referenceNumber = data.referenceNumber;
        model.subject = data.subject;
        model.basicAttributes = data.basicAttributes !== null && data.basicAttributes !== "" ? JSON.parse(data.basicAttributes) : [];
        model.customAttributes = data.customAttributes !== null && data.customAttributes !== "" ? JSON.parse(data.customAttributes) : null;
        model.customAttributesTranslation = data.customAttributesTranslation !== null && data.customAttributesTranslation !== "" ? JSON.parse(data.customAttributesTranslation) : null;
        model.formData = data.formData !== null && data.formData !== "" ? JSON.parse(data.formData) : [];
        model.receivers = data.receivers;
        model.sendingEntityId = data.sendingEntityId;
        model.dueDate = data.dueDate;
        model.priorityId = data.priorityId;
        model.privacyId = data.privacyId;
        model.carbonCopy = data.carbonCopy;
        model.importanceId = data.importanceId;
        model.classificationId = data.classificationId;
        model.sendingEntity = data.sendingEntity;
        model.receivingEntities = data.receivingEntities;
        model.carbonCopies = data.carbonCopies;
        model.classification = data.classification;
        model.documentType = data.documentType;
        model.readonly = readOnly && data.enableEdit ? !data.enableEdit : readOnly;
        model.delegationId = data.delegationId;
        model.userStructures = new CTSComponents.IdentityService().getUserStructures(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.importances = new CoreComponents.Lookup.Importances().get(window.language);
        model.createdByStructureId = data.createdByStructureId;
        model.body = data.body;
        model.externalReferenceNumber = data.externalReferenceNumber;
        model.keyword = data.keyword;
        model.enableEdit = data.enableEdit;
        model.transferId = transferId;
        model.senderPerson = data.senderPerson;
        model.receiverPerson = data.receiverPerson;
        model.isExternalReceiver = data.isExternalReceiver;
        model.isExternalSender = data.isExternalSender;
        model.byTemplate = data.byTemplate;
        model.actionComponentId = modalComponentId;
        model.parentComponentId = ComponentId;
        if (modalComponentId != undefined && modalComponentId != null && modalComponentId != "null")
            model.fromVip = false;
        if (actionName != undefined)
            model.actionName = actionName;
        var documentView = new CTSComponents.DocumentMetadata.DocumentView(wrapper, model);
        setTimeout(function () {
            $('#txtCustomAttributeSubject').focus();
        }, 500);
        let url = readOnly ? '/Document/Edit' : '/Document/Save';
        documentView.render({
            url: url,
            params: {
                'CategoryId': data.categoryId,
                'CategoryName': data.categoryName,
                '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            callback: function (response) {
                if (readOnly) {
                    GridCommon.RefreshCurrentPage("grdInboxItems", false);
                } else {
                    GridCommon.RefreshCurrentPage("grdDraftItems", false);
                }
            }
        }, {
            url: '/Document/Send',
            params: {
                '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            callback: function (response) {
                TreeNode.addToNodeCount(TreeNode.MyRequests, 1, 1);
                TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                Common.alertMsg(Common.format(Resources.ReferenceNumberMsg, response), function () {
                    var nodeId = $('[data-inherit="' + TreeNode.MyRequests + '"]').first().data("id");
                    if (nodeId !== undefined && $.isNumeric(nodeId)) {
                        window.location.href = '#myrequests/' + nodeId;
                    } else {
                        window.location.href = '/';
                    }
                });
            }
        });
    }
    E.openDocumentByTransfer = function (transferId, delegationId, ComponentId, tabId, documentId, readOnly, categoryId, fromDraft, actionName, modalComponentId) {

        if (window.location.hash.includes('#search')) {
            Common.ajaxGet('/Document/GetSearchDocument', { id: documentId }, function (data) {
                E.openDocument(data, readOnly, ComponentId, tabId, documentId, transferId, categoryId, fromDraft, actionName, modalComponentId);
            }, null, true);
        }

        else {
            var params = { id: transferId };
            if (delegationId !== null) {
                params.delegationId = delegationId;
            }


            if (fromDraft == true || transferId == null) {

                Common.ajaxGet('/Document/Get', { id: documentId }, function (data) {
                    if (window.location.hash.toLowerCase().includes("sent")) {
                        data.enableEdit = false;
                    }
                    E.openDocument(data, readOnly, ComponentId, tabId, documentId, transferId, categoryId, fromDraft, actionName, modalComponentId);
                }, null, true);
            }


            else {
                Common.ajaxGet('/Document/GetDocumentByTransferId', params, function (data) {
                    if (window.location.hash.toLowerCase().includes("sent")) {
                        data.enableEdit = false;
                    }

                    E.openDocument(data, true, ComponentId, tabId, documentId, transferId, categoryId, fromDraft, actionName, modalComponentId);
                }, null, true);
            }
        }
    }

    E.openDocumentBasket = function (documentId, basketId, ComponentId, tabId, actionName) {
        var params = { id: documentId, basketId: basketId };
        Common.ajaxGet('/Document/GetDocument', params, function (response) {
            E.openDocument(response, true, ComponentId, tabId, documentId, null, 3, false, actionName);

        }, null, true);
    }

    E.openAgendaTopicsList = function (documentId, delegationId, ComponentId, tabId, readonly, categoryId, actionName) {
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new CTSComponents.AgendaTopicsList.AgendaTopicsList();
        model.documentId = documentId;
        model.delegationId = delegationId;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.readOnly = readonly;
        model.MeetingResolution = (categoryId == window.MeetingResolutionId) ? true : false;
        if (actionName != undefined)
            model.actionName = actionName;
        var documentView = new CTSComponents.AgendaTopicsList.AgendaTopicsListView(wrapper, model);
        documentView.render();
    };
    return E;
}(CustomTabs));
var transferComponent;
var CustomActions = (function (E) {

    E = {};

    E.openSearchDocumentEdit = function (data, delegationId, fromLink) {
        var params = {};
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        var url = "";
        if (window.location.hash == "#search") {
            params = { id: data.id };
        }
        else {
            params = { id: data.documentId };
        }



        Common.ajaxGet("/Document/GetSearchDocumentEdit", params, function (response) {

            if (response && response === "NoAccess") {
                Common.alertMsg(Resources.NoPermission);
            } else {
                if (!response.id) {
                    return;
                }
                var wrapper = $(".modal-window");
                wrapper.empty();
                var model = new CTSComponents.searchLinkedDocumnet.SearchLinkedDocumnet();
                var searchLinkedDocumnet = new CTSComponents.searchLinkedDocumnet.SearchLinkedDocumnetView(wrapper, model);
                //var model = new SearchLinkedDocumnet.SearchLinkedDocumnet();
                //var searchLinkedDocumnet = new SearchLinkedDocumnet.SearchLinkedDocumnetView(wrapper, model);
                searchLinkedDocumnet.render();
                model = new CTSComponents.DocumentMetadata.Document()
                //model = new Document.Document();
                model.id = params.id;
                model.categoryId = response.categoryId;
                model.categoryName = response.categoryName;
                model.referenceNumber = response.referenceNumber;
                model.subject = response.subject;
                model.basicAttributes = response.basicAttributes !== null && response.basicAttributes !== "" ? JSON.parse(response.basicAttributes) : [];
                model.customAttributes = response.customAttributes !== null && response.customAttributes !== "" ? JSON.parse(response.customAttributes) : null;
                model.customAttribsutesTranslation = response.customAttributesTranslation !== null && response.customAttributesTranslation !== "" ? JSON.parse(response.customAttributesTranslation) : null;
                model.formData = response.formData !== null && response.formData !== "" ? JSON.parse(response.formData) : [];
                model.receivers = response.receivers;
                model.sendingEntityId = response.sendingEntityId;
                model.dueDate = response.dueDate;
                model.statusId = response.status;
                model.priorityId = response.priorityId;
                model.privacyId = response.privacyId;
                model.carbonCopy = response.carbonCopy;
                model.importanceId = response.importanceId;
                model.classificationId = response.classificationId;
                model.sendingEntity = response.sendingEntity;
                model.receivingEntities = response.receivingEntities;
                model.carbonCopies = response.carbonCopies;
                model.classification = response.classification;
                model.documentType = response.documentType;
                model.readonly = false;
                model.delegationId = response.delegationId;
                model.userStructures = new CTSComponents.IdentityService().getUserStructures(window.language);
                model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                model.importances = new CoreComponents.Lookup.Importances().get(window.language);

                model.createdByStructureId = response.createdByStructureId;
                model.body = response.body;
                model.externalReferenceNumber = response.externalReferenceNumber;
                model.keyword = response.keyword;
                model.enableEdit = response.enableEdit;
                model.byTemplate = response.byTemplate;
                model.isDocumentEdit = true;



                wrapper = $(searchLinkedDocumnet.refs['linkDocumentDiv2']);
                wrapper.empty();
                var view = new CTSComponents.DocumentMetadata.DocumentView(wrapper, model);
                $(view.refs['cmbUserStructures']).val(model.createdByStructureId);
                var title = response.categoryName;
                if (response.referenceNumber) {
                    title += ' - ' + response.referenceNumber;
                }
                if (response.createdByUser) {
                    title += ' - ' + response.createdByUser;
                }
                $(searchLinkedDocumnet.refs['modalSearchLinkedDocumnetTitle']).html(title);
                $(searchLinkedDocumnet.refs['modalSearchLinkedDocumnet']).off("hidden.bs.modal");
                $(searchLinkedDocumnet.refs['modalSearchLinkedDocumnet']).off("shown.bs.modal");
                $(searchLinkedDocumnet.refs['modalSearchLinkedDocumnet']).on('shown.bs.modal', function () {
                });
                $(searchLinkedDocumnet.refs['modalSearchLinkedDocumnet']).on('hidden.bs.modal', function () {
                    $(searchLinkedDocumnet.refs[searchLinkedDocumnet.model.ComponentId]).remove();
                    swal.close();
                    //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
                    //    $('body').addClass('modal-open');
                    //}
                });
                //$(searchLinkedDocumnet.refs['modalSearchLinkedDocumnet']).modal("show");
                //view.render();
                let url = '/Document/SaveComplete';
                view.render({
                    url: url,
                    params: {
                        'CategoryId': model.categoryId,
                        'CategoryName': model.categoryName,
                        'createdByStructureId': model.createdByStructureId,
                        '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    }

                    //    ,
                    //    callback: function (response) {
                    //        if (model.readonly) {
                    //            GridCommon.RefreshCurrentPage("grdInboxItems", false);
                    //        } else {
                    //            GridCommon.RefreshCurrentPage("grdDraftItems", false);
                    //        }
                    //    }
                }

                );


                $(".modalSearchLinkedDocumnet").modal("show");





            }
        }, function () { Common.showScreenErrorMsg(); }, true);
    }


    function createListData(data) {
        var delegationId = window.location.hash.split("/")[2];
        if (delegationId == undefined)
            delegationId = null;
        var html = '';
        if (data.length === 0 && gPageIndex === 0) {
            html = '<ul class="mdl-ul"><div class="nodata dataTables_empty pt-lg">' + Resources.NoDataToDisplay + '</div></ul>';
            $('#inboxListContainer').html(html);
        } else if (data.length > 0) {
            html = '<ul class="mdl-ul">';
            var htmlLi = '';
            for (var i = 0; i < data.length; i++) {
                var transfer = data[i];
                var liClass = "mdl-li";
                if (!transfer.isRead) {
                    liClass += " unread";
                }
                var lockedByMe = false;
                //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
                var delegatedUser = delegationId !== null ? new CTSComponents.DelegationUsers().getById(Number(delegationId)) : null;
                var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                if (transfer.isLocked && (transfer.ownerUserId !== null && transfer.ownerUserId === Number($("#hdUserId").val()) && delegationId === null)
                    || (transfer.ownerDelegatedUserId !== null && transfer.ownerDelegatedUserId === Number($("#hdUserId").val())
                        && delegatedUserId === transfer.ownerUserId && delegationId !== null)
                    || (transfer.ownerUserId !== null && delegatedUserId === transfer.ownerUserId && delegationId !== null)) {
                    lockedByMe = true;
                }
                var htmlIcons = "";
                if (transfer.importanceId) {
                    var importances = new CoreComponents.Lookup.Importances().get(window.language);
                    for (var j = 0; j < importances.length; j++) {
                        if (importances[j].id === transfer.importanceId) {
                            htmlIcons += "<i class='fa fa-exclamation fa-lg mr-sm' title='" + importances[j].text + "' style='color:" + importances[j].color + "'></i>";
                        }
                    }
                }
                if (transfer.isOverDue) {
                    htmlIcons += "<i class='fa fa-clock-o fa-lg text-danger mr-sm' title='" + Resources.OverDue + "'></i>";
                }
                let categories = new CTSComponents.Categories().get(window.language, null)
                let category = $.grep(categories, function (e) {
                    return e.id === transfer.categoryId;
                });
                if (category[0] && category[0].isBroadcast) {
                    htmlIcons += "<i class='fa fa-bullhorn text-primary mr-sm'  title='" + Resources.Broadcast + "'></i>";
                } else if (transfer.cced) {
                    htmlIcons += "<i class='fa fa-cc text-warning mr-sm'  title='" + Resources.CarbonCopy + "'></i>";
                }
                if (transfer.sentToStructure && !transfer.isLocked && !transfer.cced) {
                    htmlIcons += "<button class='btn btn-xs btn-primary mr-sm lockIcon' style='padding: 0px 3px;' title='" + Resources.Edit + "'><i class='fa fa-edit'></i></button>";
                }
                var lockedByUser = transfer.ownerUserId === Number($("#hdUserId").val()) ? Resources.You : transfer.lockedBy;
                var lockedBy = transfer.lockedByDelegatedUser !== '' ? transfer.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + transfer.lockedBy : lockedByUser;
                var titleLock = Resources.LockedBy + ": " + lockedBy + "\n" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(transfer.lockedDate, null, window.CalendarType);
                if (transfer.sentToStructure && lockedByMe) {
                    htmlIcons += "<button class='btn btn-xs btn-success mr-sm unlockIcon' style='padding: 0px 3px;' title='" + titleLock + "'><i class='fa fa-unlock fa-white'></i></button>";
                } else if (transfer.isLocked && !transfer.cced) {
                    htmlIcons += "<i class='fa fa-lock fa-lg text-danger mr-sm' title='" + titleLock + "'></i>";
                }
                var from = transfer.fromStructure !== "" ? transfer.fromStructure + '/' + transfer.fromUser : transfer.fromUser;
                htmlLi += '<li class="' + liClass + '">';
                htmlLi += '<div class="mdl-container">';
                htmlLi += '<div id="leftbox" class="pull-left">';
                htmlLi += '<div class="inside_color_line pull_left"></div>';
                htmlLi += '<input data-id=' + transfer.id + ' data-categoryid=' + transfer.categoryId + ' data-cced=' + transfer.cced + ' data-read=' + transfer.isRead +
                    ' data-lockedbyme=' + lockedByMe + ' data-senttouser=' + transfer.sentToUser +
                    ' type="checkbox" class="pointer m-sm ml-10px" onclick="event.stopPropagation();">';
                htmlLi += "<input type='hidden' data-id=" + transfer.id + " value='" + JSON.stringify(transfer) + "'/>";
                htmlLi += '</div>';
                htmlLi += '<div id="middlebox" class="mdl-text-msg pull-left pl-sm">';
                //htmlLi += '<span class="dot"></span>';
                htmlLi += '<span class="mdl-span light_grey_color dark_grey_color" title="' + from + '">' + from + '</span>';
                htmlLi += '<span class="mdl-span light_grey_color" title="' + transfer.referenceNumber + '">' + transfer.referenceNumber + '</span>';
                htmlLi += '<span class="mdl-span light_grey_color" title="' + transfer.subject + '">' + transfer.subject + '</span>';
                htmlLi += '</div>';
                htmlLi += '<div id="rightbox" class="pull-left text-right"><div class="mdl-time mr-sm" title="' + Resources.TransferDate + '">' + dateFormat(transfer.transferDate) + '</div>';
                if (htmlIcons !== "") {
                    htmlLi += '<div class="mdl-action">' + htmlIcons + '</div>';
                }
                htmlLi += '</div>';
                htmlLi += '</div>';
                htmlLi += '</div>';
                htmlLi += '</li>';
            }
            html += htmlLi;
            html += '</ul>';
            if (gPageIndex === 15) {
                $('#inboxListContainer').html(html);
            } else {
                $('#inboxListContainer ul').append(htmlLi);
            }
        }
    }
    function addFilters(d) {
        if (gFromSearch) {
            d.PriorityId = $("#cmbFilterInboxPriority").val() !== null && typeof $("#cmbFilterInboxPriority").val() !== "undefined" ? $("#cmbFilterInboxPriority").val() : "0";
            d.PrivacyId = $("#cmbFilterInboxPrivacy").val() !== null && typeof $("#cmbFilterInboxPrivacy").val() !== "undefined" ? $("#cmbFilterInboxPrivacy").val() : "0";
            d.PurposeId = $("#cmbFilterInboxPurpose").val() !== null && typeof $("#cmbFilterInboxPurpose").val() !== "undefined" ? $("#cmbFilterInboxPurpose").val() : "0";
            d.CategoryId = $("#cmbFilterInboxCategory").val() !== null && typeof $("#cmbFilterInboxCategory").val() !== "undefined" ? $("#cmbFilterInboxCategory").val() : "0";
            d.ReferenceNumber = $("#txtFilterInboxReferenceNumber").val() !== "" && typeof $("#txtFilterInboxReferenceNumber").val() !== "undefined" ? $("#txtFilterInboxReferenceNumber").val() : "";
            d.FromDate = $("#filterInboxFromDate").val() !== "" && typeof $("#filterInboxFromDate").val() !== "undefined" ? $("#filterInboxFromDate").val() : "";
            d.ToDate = $("#filterInboxToDate").val() !== "" && typeof $("#filterInboxToDate").val() !== "undefined" ? $("#filterInboxToDate").val() : "";
            d.Read = $("#chkFilterInboxRead").is(':checked');
            d.Locked = $("#chkFilterInboxLocked").is(':checked');
            d.Overdue = $("#chkFilterInboxOverdue").is(':checked');
            d.Subject = $("#txtFilterInboxSubject").val() !== "" && typeof $("#txtFilterInboxSubject").val() !== "undefined" ? $("#txtFilterInboxSubject").val() : "";
            d.StructureIds = $("#cmbFilterInboxStructure").val() !== null && typeof $("#cmbFilterInboxStructure").val() !== "undefined" ? $("#cmbFilterInboxStructure").val() : [];
            d.UserIds = $("#cmbFilterInboxUser").val() !== null && typeof $("#cmbFilterInboxUser").val() !== "undefined" ? $("#cmbFilterInboxUser").val() : [];
        }
    }
    function dateFormat(dateText) {
        var dateFull = dateText.split(" ")[0].split("/");
        var today = new Date();
        var dd = today.getDate();
        var mm = today.getMonth() + 1; //January is 0!
        var yy = today.getFullYear();
        var time = DateConverter.toHijriFormated(dateText.split(" ")[0], null, window.CalendarType);
        if (parseInt(dateFull[0]) === dd && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy) {
            time = "";
            var timeSeparator = ":";
            var hours = parseInt(dateText.split(" ")[1].split(":")[0]);
            var amPm = Resources.AM;
            if (hours > 12) {
                time += (hours - 12) + timeSeparator;
                amPm = Resources.PM;
            } else if (hours === 12) {
                time += "12" + timeSeparator;
                amPm = Resources.PM;
            } else {
                time += (hours < 10 ? '0' : '') + hours + timeSeparator;
                amPm = Resources.AM;
            }
            var minutes = parseInt(dateText.split(" ")[1].split(":")[1]);
            minutes = (minutes < 10 ? '0' : '') + minutes;
            time += minutes + " " + amPm;
        } else if (parseInt(dateFull[0]) === (dd - 1) && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy) {
            time = Resources.Yesterday;
        }
        return time;
    }

    var gNoMoreData = false;
    var gFromSearch = false;
    var gPageIndex = 0;
    var gSelectedRowId, gSelf;
    function loadInboxList() {
        if (!gNoMoreData) {
            var delegationId = window.location.hash.split("/")[2];
            if (delegationId == undefined)
                delegationId = null;
            var nodeId = window.location.hash.split("/")[1];
            if (nodeId == undefined)
                nodeId = null;
            Common.mask(document.getElementById('inboxListContainer'), "inboxListContainer-mask");
            var params = {};
            addFilters(params);
            params.NodeId = nodeId;
            params.DelegationId = delegationId;
            params.start = gPageIndex;
            Common.ajaxPost('/Transfer/ListInboxVip', params, function (response) {
                if (response.length > 0) {
                    gPageIndex += window.Paging;
                    if (response.length < window.Paging) {
                        gNoMoreData = true;
                    }
                } else {
                    gNoMoreData = true;
                }
                createListData(response);
                gLocked = false;
                Common.unmask("inboxListContainer-mask");
                if (gFromSearch) {
                    $("#divSearchInbox").fadeOut();
                }
            }, function () { gLocked = false; Common.showScreenErrorMsg(); });
        } else {
            gLocked = false;
        }
    }
    function refreshInboxList(ids) {
        for (var i = 0; i < ids.length; i++) {
            var canDelete = false;
            var li = $($("input[data-id='" + ids[i] + "']").parents("li")[0]);
            var spans = li.find("#middlebox span");
            if (spans.length === 3) {
                canDelete = true;
            }
            if (canDelete) {
                li.fadeOut().remove();
                $(".withBorders-o").addClass("waitingBackground");
                $("#inboxDocumentDetailsContainer").empty();
                gSelectedRowId = null;
            }
        }
        gPageIndex = 0;
        gNoMoreData = false;
        loadInboxList();
    }

    function completeTransfer(ids, delegationId) {
        Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
        Common.ajaxPost('/Transfer/Complete',
            {
                'ids': ids, 'delegationId': delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                if (result != null && result.length > 0) {
                    swal.close()
                    let msg = "";
                    for (var i = 0; i < result.length; i++) {
                        if (!result[i].updated) {
                            msg += "\n ○ " + result[i].uncompletedDocumentReferenceNumber;
                        }
                    }
                    if (msg !== "") {
                        setTimeout(function () {
                            Common.alertMsg(Resources.CannotCompleteWarning + msg);
                        }, 300);
                        Common.unmask("inboxListContainer-mask");
                        GridCommon.Refresh(gTableName);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    }
                    else {
                        Common.unmask("inboxListContainer-mask");
                        Common.showScreenSuccessMsg();
                        GridCommon.Refresh(gTableName);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    }
                } else {
                    Common.showScreenErrorMsg();
                }
            }, null, false);
    }

    function completeTransferVip(ids, delegationId) {
        Common.mask(document.getElementById('vipContainer'), "vipContainer-mask");
        Common.ajaxPost('/Transfer/Complete',
            {
                'ids': ids, 'delegationId': delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                if (result != null && result.length > 0) {
                    swal.close()
                    let msg = "";
                    for (var i = 0; i < result.length; i++) {
                        if (!result[i].updated) {
                            msg += "\n ○ " + result[i].uncompletedDocumentReferenceNumber;
                        }
                    }
                    if (msg !== "") {
                        setTimeout(function () {
                            Common.alertMsg(Resources.CannotCompleteWarning + msg);
                        }, 300);
                        Common.unmask("vipContainer-mask");
                        for (var i = 0; i < ids.length; i++) {
                            var canDelete = true;
                            var li = $($("input[data-id='" + ids[i] + "']").parents("li")[0]);
                            if (result.length > 0) {
                                var spans = li.find("#middlebox span");
                                if (spans.length === 3) {
                                    if ($.grep(result, function (element, index) {
                                        return !element.updated && element.uncompletedDocumentReferenceNumber === spans[1].textContent;
                                    }).length > 0) {
                                        canDelete = false;
                                    }
                                }
                            }
                            if (canDelete) {
                                li.fadeOut().remove();
                                if (Number(gSelectedRowId) === Number(ids[i])) {
                                    $(".withBorders-o").addClass("waitingBackground");
                                    $("#inboxDocumentDetailsContainer").empty();
                                }
                            }
                        }
                        Common.unmask("vipContainer-mask");
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    }
                    else {
                        Common.unmask("vipContainer-mask");
                        Common.showScreenSuccessMsg();
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                        TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                        for (var i = 0; i < ids.length; i++) {
                            $($("input[data-id='" + ids[i] + "']").parents("li")[0]).fadeOut().remove();
                            if (Number(gSelectedRowId) === Number(ids[i])) {
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#inboxDocumentDetailsContainer").empty();
                            }
                        }
                    }
                } else {
                    Common.showScreenErrorMsg();
                }
            }, null, false);
    }
    function dismissCarbonCopy(dismissIds, delegationId, allSelectedData) {
        Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
        Common.ajaxPost('/Transfer/DismissCarbonCopy',
            {
                'ids': dismissIds, 'delegationId': delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                if (result != null && result.length > 0) {
                    swal.close()
                    let msg = "";
                    for (var i = 0; i < result.length; i++) {
                        if (!result[i].updated) {
                            var transfer = $.grep(allSelectedData, function (e) {
                                return e.id === result[i].transferId;
                            });
                            if (transfer[0]) {
                                msg += "\n ○ " + transfer[0].referenceNumber;

                            } else {
                                msg += "\n ○ " + result[i].transferId;
                            }
                        }
                    }
                    if (msg !== "") {
                        setTimeout(function () {
                            Common.alertMsg(Resources.CannotDismissCarbonCopyWarning + msg);
                        }, 300);
                        Common.unmask("inboxListContainer-mask");
                        GridCommon.Refresh(gTableName);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    } else {
                        Common.unmask("inboxListContainer-mask");
                        Common.showScreenSuccessMsg();
                        GridCommon.Refresh(gTableName);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    }
                } else {
                    Common.showScreenErrorMsg();
                }
            }, null, false);
    }

    function dismissCarbonCopyVip(dismissIds, delegationId, allSelectedData) {
        Common.mask(document.getElementById('vipContainer'), "vipContainer-mask");
        Common.ajaxPost('/Transfer/DismissCarbonCopy',
            {
                'ids': dismissIds, 'delegationId': delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                if (result != null && result.length > 0) {
                    swal.close()
                    let msg = "";
                    for (var i = 0; i < result.length; i++) {
                        if (!result[i].updated) {
                            var transfer = $.grep(allSelectedData, function (e) {
                                return e.id === result[i].transferId;
                            });
                            if (transfer[0]) {
                                msg += "\n ○ " + transfer[0].referenceNumber;

                            } else {
                                msg += "\n ○ " + result[i].transferId;
                            }
                        }
                    }
                    if (msg !== "") {
                        setTimeout(function () {
                            Common.alertMsg(Resources.CannotDismissCarbonCopyWarning + msg);
                        }, 300);
                        Common.unmask("vipContainer-mask");
                        for (var i = 0; i < dismissIds.length; i++) {
                            var canDelete = false;
                            var li = $($("input[data-id='" + dismissIds[i] + "']").parents("li")[0]);
                            if (result.length > 0) {
                                if ($.grep(result, function (element, index) {
                                    return element.updated && element.transferId === dismissIds[i];
                                }).length > 0) {
                                    canDelete = true;
                                }
                            }
                            if (canDelete) {
                                li.fadeOut().remove();
                                if (Number(gSelectedRowId) === Number(dismissIds[i])) {
                                    $(".withBorders-o").addClass("waitingBackground");
                                    $("#inboxDocumentDetailsContainer").empty();
                                }
                            }
                        }
                        Common.unmask("vipContainer-mask");
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    } else {
                        Common.unmask("vipContainer-mask");
                        Common.showScreenSuccessMsg();
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        for (var i = 0; i < dismissIds.length; i++) {
                            while ($("input[data-id='" + dismissIds[i] + "']")[0]) {
                                $($("input[data-id='" + dismissIds[i] + "']").parents("li")[0]).fadeOut().remove();
                            }
                            if (Number(gSelectedRowId) === Number(dismissIds[i])) {
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#inboxDocumentDetailsContainer").empty();
                            }
                        }
                    }
                } else {
                    Common.showScreenErrorMsg();
                }
            }, null, false);
    }

    E.completeTransfer = function () {

        var delegationId = window.location.hash.split("/")[2];
        if (delegationId == undefined)
            delegationId = null;
        let categories = new CTSComponents.Categories().get(window.language, null)
        var gTableName = "grdInboxItems";
        var ids = GridCommon.GetSelectedRows(gTableName);
        if (ids.length > 0) {
            var lstReceivers = [];
            var allSelectedData = [];
            if (ids.length > 0) {
                var table = $('#' + gTableName).DataTable();
                var allRows = table.rows().data();
                var allCced = true;
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $.grep(allRows, function (element, index) {
                        return element.id === Number(ids[i]);
                    });
                    allSelectedData.push(selectedRowData[0]);
                    let category = $.grep(categories, function (e) {
                        return e.id === selectedRowData[0].categoryId;
                    });
                    if (selectedRowData && selectedRowData[0] && allCced && (!selectedRowData[0].cced || category[0].isInternalBroadcast)) {
                        allCced = false;
                    }
                }
                if (allCced) {
                    Common.alertMsg(Resources.AllSelectedItemsCCed);
                } else {
                    if (window.EnableConfirmationMessage === "True") {
                        Common.showConfirmMsg(Resources.CompleteConfirmation, function () {
                            completeTransfer(ids, delegationId);
                        });
                    } else {
                        completeTransfer(ids, delegationId);
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }

        }

        else {
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
            if (checkedRows.length > 0) {
                var ids = new Array();
                checkedRows.each(function (index, obj) {
                    ids.push(obj.getAttribute('data-id'));
                });
                var allCced = true;
                var allSelectedData = [];
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $("input[type='hidden'][data-id='" + ids[i] + "']").val();
                    if (selectedRowData && selectedRowData !== "") {
                        selectedRowData = JSON.parse(selectedRowData);
                        allSelectedData.push(selectedRowData);
                        let category = $.grep(categories, function (e) {
                            return e.id === selectedRowData.categoryId;
                        });
                        if (allCced && (!selectedRowData.cced || category[0].isInternalBroadcast)) {
                            allCced = false;
                        }
                    }
                }
                if (allCced) {
                    Common.alertMsg(Resources.AllSelectedItemsCCed);
                } else {
                    if (window.EnableConfirmationMessage === "True") {
                        Common.showConfirmMsg(Resources.CompleteConfirmation, function () {
                            completeTransferVip(ids, delegationId);
                        });
                    } else {
                        completeTransferVip(ids, delegationId);
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }
        }



    }

    E.dismissCarbonCopy = function () {

        var delegationId = window.location.hash.split("/")[2];
        if (delegationId == undefined)
            delegationId = null;
        var gTableName = "grdInboxItems";


        var ids = GridCommon.GetSelectedRows(gTableName);
        if (ids.length > 0) {
            if (ids.length > 0) {
                var table = $('#' + gTableName).DataTable();
                var allRows = table.rows().data();
                var atLeastOneCced = false;
                var dismissIds = [];
                var broadcastIds = [];
                var allSelectedData = [];
                //var categories = [];
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $.grep(allRows, function (element, index) {
                        return element.id === Number(ids[i]);
                    });
                    if (selectedRowData && selectedRowData[0] && selectedRowData[0].cced) {
                        allSelectedData.push(selectedRowData[0]);
                        let categories = new CTSComponents.Categories().get(window.language, null)

                        let category = $.grep(categories, function (e) {
                            return e.id === selectedRowData[0].categoryId;
                        });
                        if (category[0].isInternalBroadcast) {
                            broadcastIds.push(Number(ids[i]));
                        }
                        if (!atLeastOneCced && !category[0].isInternalBroadcast) {
                            atLeastOneCced = true;
                        }
                        dismissIds.push(Number(ids[i]));
                    }
                }
                var nonBroadcastIds = $.grep(dismissIds, function (value) {
                    return $.inArray(Number(value), broadcastIds) < 0;
                });

                if (!atLeastOneCced && broadcastIds.length !== ids.length) {
                    Common.alertMsg(Resources.AllSelectedItemsNotCCed);
                } else if (nonBroadcastIds.length === 0) {
                    Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
                }
                else {
                    if (dismissIds.length > 0) {
                        if (window.EnableConfirmationMessage === "True") {
                            Common.showConfirmMsg(Resources.DismissCarbonCopyConfirmation, function () {
                                dismissCarbonCopy(dismissIds, delegationId, allSelectedData);
                            });
                        } else {
                            dismissCarbonCopy(dismissIds, delegationId, allSelectedData);
                        }
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }
        }

        else {
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
            if (checkedRows.length > 0) {
                var ids = new Array();
                checkedRows.each(function (index, obj) {
                    ids.push(obj.getAttribute('data-id'));
                });
                var atLeastOneCced = false;
                var dismissIds = [];
                var broadcastIds = [];
                var allSelectedData = [];
                for (var i = 0; i < ids.length; i++) {


                    let categories = new CTSComponents.Categories().get(window.language, null)
                    var selectedRowData = $("input[type='hidden'][data-id='" + ids[i] + "']").val();
                    if (selectedRowData && selectedRowData !== "") {
                        selectedRowData = JSON.parse(selectedRowData);
                        if (selectedRowData.cced) {
                            dismissIds.push(Number(ids[i]));
                            allSelectedData.push(selectedRowData);

                            let category = $.grep(categories, function (e) {
                                return e.id === selectedRowData.categoryId;
                            });
                            if (category[0].isInternalBroadcast) {
                                broadcastIds.push(Number(ids[i]));
                            }
                            if (!atLeastOneCced && !category[0].isInternalBroadcast) {
                                atLeastOneCced = true;
                            }
                        }
                    }
                }
                var nonBroadcastIds = $.grep(dismissIds, function (value) {
                    return $.inArray(Number(value), broadcastIds) < 0;
                });

                if (!atLeastOneCced && broadcastIds.length !== ids.length) {
                    Common.alertMsg(Resources.AllSelectedItemsNotCCed);
                } else if (nonBroadcastIds.length === 0) {
                    Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
                } else {
                    if (dismissIds.length > 0) {
                        if (window.EnableConfirmationMessage === "True") {
                            Common.showConfirmMsg(Resources.DismissCarbonCopyConfirmation, function () {
                                dismissCarbonCopyVip(dismissIds, delegationId, allSelectedData);
                            });
                        } else {
                            dismissCarbonCopyVip(dismissIds, delegationId, allSelectedData);
                        }
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }
        }

    }


    E.transfer = function () {

        var delegationId = window.location.hash.split("/")[2];
        if (delegationId == undefined)
            delegationId = null;
        var gTableName = "grdInboxItems";
        var ids = GridCommon.GetSelectedRows(gTableName);
        let categories = new CTSComponents.Categories().get(window.language, null)
        var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        if (ids.length > 0) {
            if (ids.length > 0) {
                var table = $('#' + gTableName).DataTable();
                var allRows = table.rows().data();
                var allCced = true;
                var cCedAndNotBroadcast = false;
                var allReadOnly = true;
                var allSelectedData = [];
                var broadcastIds = [];
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $.grep(allRows, function (element, index) {
                        return element.id === Number(ids[i]);
                    });
                    if (selectedRowData && selectedRowData[0]) {

                        let category = $.grep(categories, function (e) {
                            return e.id === selectedRowData[0].categoryId;
                        });

                        if (allCced && (!selectedRowData[0].cced || category[0].isInternalBroadcast)) {
                            allCced = false;
                        }
                        if (selectedRowData[0].cced && !category[0].isInternalBroadcast) {
                            cCedAndNotBroadcast = true;
                        }
                        if ((!selectedRowData[0].cced || category[0].isInternalBroadcast) && !selectedRowData[0].isLocked && selectedRowData[0].workflowStepId == null) {
                            allReadOnly = false;
                            allSelectedData.push(selectedRowData[0]);
                            if (category[0].isInternalBroadcast) {
                                broadcastIds.push(Number(ids[i]));
                            }
                        } else if (selectedRowData[0].isLocked) {
                            //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
                            var delegatedUser = delegationId !== null ? new CTSComponents.DelegationUsers().getById(Number(delegationId)) : null; //$("#hdDelegatedUserIds").val().split(window.Splitter);
                            var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                            if ((selectedRowData[0].ownerUserId !== null && selectedRowData[0].ownerUserId === Number($("#hdUserId").val()) && delegationId === null)
                                || (selectedRowData[0].ownerDelegatedUserId !== null && selectedRowData[0].ownerDelegatedUserId === Number($("#hdUserId").val())
                                    && delegatedUserId === selectedRowData[0].ownerUserId && delegationId !== null)
                                || (selectedRowData[0].ownerUserId !== null && delegatedUserId === Number(selectedRowData[0].ownerUserId) && delegationId !== null)) {
                                allReadOnly = false;
                                allSelectedData.push(selectedRowData[0]);
                                if (category[0].isInternalBroadcast) {
                                    broadcastIds.push(Number(ids[i]));
                                }
                            }
                        }
                    }
                }



                if (allCced || allReadOnly) {
                    if (allCced) {
                        Common.alertMsg(Resources.AllSelectedItemsCCed);
                    } else {
                        Common.alertMsg(Resources.AllSelectedItemsAreReadOnlyOrHasWorkflow);
                    }
                } else {
                    if (allSelectedData.length > 0) {
                        var nonBroadcastIds = $.grep(allSelectedData, function (value) {
                            return $.inArray(value.id, broadcastIds) < 0;
                        });
                        if (nonBroadcastIds.length == 0) {
                            if (cCedAndNotBroadcast) {
                                Common.alertMsg(Resources.AllSelectedItemsAreBroadcastAndCarbonCopies);
                                return;
                            } else {
                                Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
                                return;
                            }
                        }
                        var dataSelected = allSelectedData.map(t => t.toStructureId).filter(function (value, index, array) {
                            return array.indexOf(value) === index;
                        });
                        if (window.EnableSendingRules === "True" && dataSelected.length > 1) {
                            Common.alertMsg(Resources.AllSelectedItemsHaveDifferentSender);
                            return;
                        }
                        var callback = function (data) {
                            var arrayOfTransfers = [];
                            var transferToStructures = [], transferToStructureIds = [];
                            var ccedTransfer = 0;
                            var purposes = new CoreComponents.Lookup.Purposes().get(window.language);

                            var hasPrivacyLevel = true;
                            var htmlPrivacy = Resources.NoUserWithSelectedPrivacy;
                            var structureExist = data.some(a => a.toStructureId !== null && a.toUserId === null);
                            let selectedPrivacy = $.grep(privacies, function (e) {
                                return e.id === selectedRowData[0].privacyId;
                            });
                            for (var i = 0; i < data.length; i++) {
                                var currentPurpose = $.grep(purposes, function (e) {
                                    return e.id.toString() === data[i].purposeId;
                                });
                                if (currentPurpose[0].cCed === true) {
                                    ccedTransfer++;
                                }
                                if (data[i].toUserId === null) {
                                    transferToStructures.push({ dataId: data[i].toStructureId, text: data[i].name });
                                    transferToStructureIds.push(data[i].toStructureId);
                                }
                                for (var j = 0; j < allSelectedData.length; j++) {
                                    arrayOfTransfers.push({
                                        toStructureId: data[i].toStructureId,
                                        toUserId: data[i].toUserId,
                                        name: data[i].name,
                                        dueDate: data[i].dueDate,
                                        purposeId: data[i].purposeId,
                                        instruction: data[i].instruction,
                                        cced: data[i].cced,
                                        fromStructureId: allSelectedData[j].toStructureId,
                                        parentTransferId: allSelectedData[j].id,
                                        isStructure: data[i].toUserId === null,
                                        documentId: allSelectedData[j].documentId,
                                        privacyId: allSelectedData[j].privacyId,
                                        referenceNumber: allSelectedData[j].referenceNumber
                                    });
                                }
                            }

                            var userStructureIds = $("#hdStructureIds").val();

                            var message = CTSComponents.SendTransferModal.checkAbilityToSend(transferToStructureIds.length === 0 ? userStructureIds.split(window.Seperator) : transferToStructureIds, transferToStructures, selectedPrivacy, privacies, null, false);
                            if (message === "error") {
                                return;
                            }
                            for (var i = 0; i < arrayOfTransfers.length; i++) {
                                if (arrayOfTransfers[i].toUserId !== null) {
                                    // arrayOfTransfers[i].privacyId = 0;
                                    var userObj = new CTSComponents.IdentityService().getFullUser(arrayOfTransfers[i].toUserId);
                                    if (userObj !== null) {
                                        var attributePrivacy = $.grep(userObj.attributes, function (e) {
                                            return e.text === window.UserPrivacy ? e.value : 0;
                                        });
                                        //if (attributePrivacy.length > 0)
                                        //{
                                        //    arrayOfTransfers[i].privacyId = attributePrivacy[0].value === "" ? 0 : attributePrivacy[0].value;
                                        //}
                                    }
                                    //var currentPrivacy = $.grep(model.privacies, function (e)
                                    //{
                                    //    return e.id.toString() === arrayOfTransfers[i].privacyId.toString();
                                    //});
                                    if (attributePrivacy !== null && attributePrivacy.length > 0) {
                                        if (arrayOfTransfers[i].privacyId > parseInt(attributePrivacy[0].value)) {
                                            hasPrivacyLevel = false;
                                            htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
                                            if (arrayOfTransfers[i].referenceNumber) {
                                                htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
                                            }
                                        }
                                    } else {
                                        hasPrivacyLevel = false;
                                        htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
                                        if (arrayOfTransfers[i].referenceNumber) {
                                            htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
                                        }
                                    }
                                }
                            }
                            if (!hasPrivacyLevel) {
                                message += (message !== "" ? " \n " : "") + htmlPrivacy;
                            }
                            if (message !== "") {
                                if (!structureExist) {
                                    Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                                        CTSComponents.SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true);
                                    }, function () {
                                    });
                                }
                                else {
                                    if (window.SendWithoutStructureReceiverOrPrivacyLevel === "True") {
                                        Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                                            CTSComponents.SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true);
                                        }, function () {
                                        });
                                    } else {
                                        Common.alertMsg(message);
                                    }
                                }
                            } else {
                                if (ccedTransfer === data.length) {
                                    Common.showConfirmCcedMsg(Resources.AllSelectedTransfersAreCCconfirmation, function () {
                                        CTSComponents.SendTransferModal.transfer(transferComponent, arrayOfTransfers, true, false, true, delegationId, true);
                                    }, function () {
                                        CTSComponents.SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, true, true, delegationId, true);
                                    });
                                } else {
                                    CTSComponents.SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true);
                                }
                            }
                        };
                        let modalWrapper = $(".modal-window");
                        transferComponent = new CTSComponents.Transfer(dataSelected[0], $("#hdUserId").val(), $("#hdStructureIds").val().split(window.Seperator),
                            window.IsStructureSender === "True", window.EnableSendingRules === "True", window.EnableTransferToUsers === "True", "", delegationId,
                            callback, modalWrapper);
                        transferComponent.render();
                        $('.modalTransfer').modal('show');
                        $(".modalTransfer").off("hidden.bs.modal");
                        $(".modalTransfer").off("shown.bs.modal");
                        $('.modalTransfer').on('hidden.bs.modal', function () {
                            $(".modalTransfer").parent().remove();
                            swal.close();
                        });
                    }
                }
            }

            else {


                Common.alertMsg(Resources.NoRowSelected);
            }

        }

        else {
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
            if (checkedRows.length > 0) {
                var ids = new Array();
                checkedRows.each(function (index, obj) {
                    ids.push(obj.getAttribute('data-id'));
                });
                var allCced = true;
                var allReadOnly = true;
                var allSelectedData = [];
                var broadcastIds = [];
                var cCedAndNotBroadcast = false;
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $("input[type='hidden'][data-id='" + ids[i] + "']").val();
                    if (selectedRowData && selectedRowData !== "") {
                        selectedRowData = JSON.parse(selectedRowData);
                        let category = $.grep(categories, function (e) {
                            return e.id === selectedRowData.categoryId;
                        });
                        if (allCced && (!selectedRowData.cced || category[0].isInternalBroadcast)) {
                            allCced = false;
                        }
                        if (selectedRowData.cced && !category[0].isInternalBroadcast) {
                            cCedAndNotBroadcast = true;
                        }
                        if ((!selectedRowData.cced || category[0].isInternalBroadcast) && !selectedRowData.isLocked) {
                            allReadOnly = false;
                            allSelectedData.push(selectedRowData);
                            if (category[0].isInternalBroadcast) {
                                broadcastIds.push(Number(ids[i]));
                            }
                        } else if (selectedRowData.isLocked) {
                            var delegatedUser = delegationId !== null ? new CTSComponents.DelegationUsers().getById(Number(delegationId)) : null; //$("#hdDelegatedUserIds").val().split(window.Splitter);
                            var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                            if ((selectedRowData.ownerUserId !== null && selectedRowData.ownerUserId === Number($("#hdUserId").val()) && delegationId === null)
                                || (selectedRowData.ownerDelegatedUserId !== null && selectedRowData.ownerDelegatedUserId === Number($("#hdUserId").val())
                                    && delegatedUserId === selectedRowData.ownerUserId && delegationId !== null)
                                || (selectedRowData.ownerUserId !== null && delegatedUserId === Number(selectedRowData.ownerUserId) && delegationId !== null)) {
                                allReadOnly = false;
                                allSelectedData.push(selectedRowData);
                                if (category[0].isInternalBroadcast) {
                                    broadcastIds.push(Number(ids[i]));
                                }
                            }
                        }
                    }
                }
                if (allCced || allReadOnly) {
                    if (allCced) {
                        Common.alertMsg(Resources.AllSelectedItemsCCed);
                    } else {
                        Common.alertMsg(Resources.AllSelectedItemsAreReadOnly);
                    }
                } else {
                    if (allSelectedData.length > 0) {
                        var nonBroadcastIds = $.grep(allSelectedData, function (value) {
                            return $.inArray(value.id, broadcastIds) < 0;
                        });
                        if (nonBroadcastIds.length == 0) {
                            if (cCedAndNotBroadcast) {
                                Common.alertMsg(Resources.AllSelectedItemsAreBroadcastAndCarbonCopies);
                                return;
                            } else {
                                Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
                                return;
                            }
                        }
                        var dataSelected = allSelectedData.map(t => t.toStructureId).filter(function (value, index, array) {
                            return array.indexOf(value) === index;
                        });
                        if (window.EnableSendingRules === "True" && dataSelected.length > 1) {
                            Common.alertMsg(Resources.AllSelectedItemsHaveDifferentSender);
                            return;
                        }
                        var callback = function (data) {
                            var arrayOfTransfers = [];
                            var transferToStructures = [], transferToStructureIds = [];
                            var ccedTransfer = 0;
                            var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                            var hasPrivacyLevel = true;
                            var htmlPrivacy = Resources.NoUserWithSelectedPrivacy;
                            var structureExist = data.some(a => a.toStructureId !== null && a.toUserId === null);
                            let selectedPrivacy = $.grep(privacies, function (e) {
                                return e.id === selectedRowData.privacyId;
                            });
                            for (var i = 0; i < data.length; i++) {
                                var currentPurpose = $.grep(purposes, function (e) {
                                    return e.id.toString() === data[i].purposeId;
                                });
                                if (currentPurpose[0].cCed === true) {
                                    ccedTransfer++;
                                }
                                if (data[i].toUserId === null) {
                                    transferToStructures.push({ dataId: data[i].toStructureId, text: data[i].name });
                                    transferToStructureIds.push(data[i].toStructureId);
                                }
                                for (var j = 0; j < allSelectedData.length; j++) {
                                    arrayOfTransfers.push({
                                        toStructureId: data[i].toStructureId,
                                        toUserId: data[i].toUserId,
                                        name: data[i].name,
                                        dueDate: data[i].dueDate,
                                        purposeId: data[i].purposeId,
                                        instruction: data[i].instruction,
                                        cced: data[i].cced,
                                        fromStructureId: allSelectedData[j].toStructureId,
                                        parentTransferId: allSelectedData[j].id,
                                        isStructure: data[i].toUserId === null,
                                        documentId: allSelectedData[j].documentId,
                                        privacyId: allSelectedData[j].privacyId,
                                        referenceNumber: allSelectedData[j].referenceNumber
                                    });
                                }
                            }
                            var userStructureIds = $("#hdStructureIds").val();
                            var message = CTSComponents.SendTransferModal.checkAbilityToSend(transferToStructureIds.length === 0 ? userStructureIds.split(window.Seperator) : transferToStructureIds, transferToStructures, selectedPrivacy, privacies, null, false);
                            if (message === "error") {
                                return;
                            }
                            for (var i = 0; i < arrayOfTransfers.length; i++) {
                                if (arrayOfTransfers[i].toUserId !== null) {
                                    var userObj = new CTSComponents.IdentityService().getFullUser(arrayOfTransfers[i].toUserId);
                                    if (userObj !== null) {
                                        var attributePrivacy = $.grep(userObj.attributes, function (e) {
                                            return e.text === window.UserPrivacy ? e.value : 0;
                                        });
                                    }
                                    if (attributePrivacy !== null && attributePrivacy.length > 0) {
                                        if (arrayOfTransfers[i].privacyId > parseInt(attributePrivacy[0].value)) {
                                            hasPrivacyLevel = false;
                                            htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
                                            if (arrayOfTransfers[i].referenceNumber) {
                                                htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
                                            }
                                        }
                                    } else {
                                        hasPrivacyLevel = false;
                                        htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
                                        if (arrayOfTransfers[i].referenceNumber) {
                                            htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
                                        }
                                    }
                                }
                            }
                            if (!hasPrivacyLevel) {
                                message += (message !== "" ? " \n " : "") + htmlPrivacy;
                            }
                            if (message !== "") {
                                if (!structureExist) {
                                    Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                                        CTSComponents.SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true, true, refreshInboxList);
                                    }, function () {
                                    });
                                }
                                else {
                                    if (window.SendWithoutStructureReceiverOrPrivacyLevel === "True") {
                                        Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                                            CTSComponents.SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true, true, refreshInboxList);
                                        }, function () {
                                        });
                                    } else {
                                        Common.alertMsg(message);
                                    }
                                }
                            } else {
                                if (ccedTransfer === data.length) {
                                    Common.showConfirmCcedMsg(Resources.AllSelectedTransfersAreCCconfirmation, function () {
                                        CTSComponents.SendTransferModal.transfer(transferComponent, arrayOfTransfers, true, false, true, delegationId, true, true, refreshInboxList);
                                    }, function () {
                                        CTSComponents.SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, true, true, delegationId, true, true, refreshInboxList);
                                    });
                                } else {
                                    CTSComponents.SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true, true, refreshInboxList);
                                }
                            }
                        };
                        let modalWrapper = $(".modal-window");
                        var transferComponent = new CTSComponents.Transfer(dataSelected[0], $("#hdUserId").val(), $("#hdStructureIds").val().split(window.Seperator),
                            window.IsStructureSender === "True", window.EnableSendingRules === "True", window.EnableTransferToUsers === "True", "", delegationId,
                            callback, modalWrapper);
                        transferComponent.render();
                        $('.modalTransfer').modal('show');
                        $(".modalTransfer").off("hidden.bs.modal");
                        $(".modalTransfer").off("shown.bs.modal");
                        $('.modalTransfer').on('hidden.bs.modal', function () {
                            $(".modalTransfer").parent().remove();
                            swal.close();
                        });
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }
        }




    }
    E.addToBasket = function () {
        var delegationId = window.location.hash.split("/")[2];
        if (delegationId == undefined)
            delegationId = null;

        var selectedRadio = document.querySelector('input[name="InboxMode"]:checked');
        var selectedValue = selectedRadio.value;
        var selectedDocumentIds = [];
        var gTableName = "grdInboxItems";
        var ids = GridCommon.GetSelectedRows(gTableName);
        if (ids.length > 0) {
            var table = $('#' + gTableName).DataTable();
            var allRows = table.rows().data();
            for (var i = 0; i < ids.length; i++) {
                var selectedRowData = $.grep(allRows, function (element, index) {
                    return element.id === Number(ids[i]);
                });
                if (selectedValue == "InboxDefault")
                    selectedDocumentIds.push(selectedRowData[0].documentForm.id);
                else if (selectedValue == "InboxDefaultWithGrouping")
                    selectedDocumentIds.push(selectedRowData[0].documentId);
            }
        }
        if (selectedValue == "InboxVIPView") {
            var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
            if (checkedRows.length > 0) {
                var dids = new Array();
                checkedRows.each(function (index, obj) {
                    dids.push(obj.getAttribute('data-id'));
                });
            }
            for (var i = 0; i < dids.length; i++) {
                var selectedRowData = $("input[type='hidden'][data-id='" + dids[i] + "']").val();
                if (selectedRowData && selectedRowData !== "") {
                    selectedRowData = JSON.parse(selectedRowData);
                    selectedDocumentIds.push(selectedRowData.documentId);
                }
            }
        }
        var wrapper = $(".modal-window");
        var modelIndex = new BasketFromNodeIndex.AddBasketFromNodeIndex();
        modelIndex.selectedDocumentIds = selectedDocumentIds;

        var grpIndex = new BasketFromNodeIndex.AddBasketFromNodeIndexView(wrapper, modelIndex);
        grpIndex.render();
        $('#modalAddToBasketTitle').html(Resources.Add);
        $('#modalAddToBasket').addClass('modalScroll');
        $('#modalAddToBasket').modal('show');
        $("#modalAddToBasket").off("hidden.bs.modal");
        $("#modalAddToBasket").off("shown.bs.modal");
        $('#modalAddToBasket').on('shown.bs.modal', function () {
        });
        $('#modalAddToBasket').on('hidden.bs.modal', function () {
            $('#indexAddToBasketContainer').show();
            swal.close();
            modelIndex.selectedDocumentIds = [];
            $("#modalAddToBasket").remove();
            swal.close();
        });
    }
    return E;
}(CustomActions));
var CustomMenus = (function (E) {
    E = {};
    E.systemDelegation = function () {
        if (window.location.href.indexOf("systemdelegation") < 0) {
            window.location.href = window.location.origin + "/#systemdelegation";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#systemdelegation']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");

        let view = new CTSComponents.CTSSystemDelegation.SystemDelegationView(wrapper);
        view.render();

    };
    E.advanceSearchRoute = function (delegationId) {

        Common.ajaxGet('/AdvanceSearchConfiguration/GetConfiguration', null, function (response) {
            $('.sidebar li[class="active"]').removeClass("active");

            $("a[href='#advanceSearch']").parent().addClass("active");
            $(".delegation").removeClass("active");
            let wrapper = $(".content-wrapper");
            let model = new CTSComponents.DocumentAdvanceSearch.DocumentAdvanceSearchModel();
            model.delegationId = delegationId;
            model.configuration = response;
            model.delegationUsers = new CTSComponents.DelegationUsers().get(window.language);
            if (response && response.content != null && response.content != "") {
                if (JSON.parse(response.content).components.length > 0) {
                    model.drawContentBuilder = true;
                }
            }
            let view = new CTSComponents.DocumentAdvanceSearch.DocumentAdvanceSearchView(wrapper, model);
            view.render();
        });

    }
    E.delegationRoute = function () {

        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#delegation']").parent().addClass("active");
        $(".delegation").removeClass("active");

        var userId = $("#hdUserId").val();
        var structureIds = $("#hdStructureIds").val().split(window.Seperator);
        var hdHasManager = $("#hdHasManager").val();
        let wrapper = $(".content-wrapper");
        let view = new CTSComponents.CTSDelegation.DelegationView(wrapper, userId, structureIds, hdHasManager);//params userId,structureIds,hasManager,"DocumentTypes" pass as param to change categories label, default is "Categories"
        view.render();

    }
    E.rootRoute = function () {
        window.onload = function () {
            let view = new CTSComponents.Home.HomeView($(".content-wrapper"));
            view.render();
        };
    }
    E.exceptionLogRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#exceptionlog']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.ExceptionLogComponent();
        view.render();
    }
    E.purposeRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#purpose']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.Purpose.Purpose();
        model.showCced = true;
        let view = new CTSComponents.Purpose.PurposeView(wrapper, model);
        //let view = new CoreComponents.PurposeComponent(true);//showCced param pass to show cced column default is true
        view.render();
    }
    E.statusRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#status']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.StatusComponent();
        view.render();
    }
    E.priorityRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#priority']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.PriorityComponent();
        view.render();
    }
    E.applicationServerRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#applicationserver']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.ApplicationServerComponent();
        view.render();
    }
    E.parameterRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#parameter']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new CTSComponents.Parameter.ParameterView(wrapper, null);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();

        let modelIndex = new CTSComponents.ParameterIndex.ParameterIndex();
        let parameterIndexView = new CTSComponents.ParameterIndex.ParameterIndexView(modalWrapper, modelIndex);
        parameterIndexView.render();

        let modelEventIndex = new CTSComponents.EventReceiverIndex.EventReceiverIndex();
        let eventIndexView = new CTSComponents.EventReceiverIndex.EventReceiverIndexView(modalWrapper, modelEventIndex);
        eventIndexView.render();

        var exportModel = new CTSComponents.Export.Export();
        var viewexport = new CTSComponents.Export.ExportView(modalWrapper, exportModel);
        viewexport.render();

        var importModel = new CTSComponents.Import.Import();
        var viewimport = new CTSComponents.Import.ImportView(modalWrapper, importModel);
        viewimport.render();
    }
    E.translatorDictionaryRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#translatordictionary']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.DictionaryComponent();
        view.render();
    }
    E.notificationTemplateRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#notificationtemplate']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.NotificationTemplateComponent();
        view.render();
    }
    E.scanConfigurationRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#scanconfiguration']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new CTSComponents.ScanConfiguration.ScanConfigurationView(wrapper, null);
        documentView.render();
        let modalWrapper = $(".modal-window"); modalWrapper.empty();
        let model = new CTSComponents.ScanConfigurationIndex.ScanConfigurationIndex();
        model.categories = new CTSComponents.ScanConfigurationIndex.ScanConfigurationIndex().get();
        let scanConfigurationIndexView = new CTSComponents.ScanConfigurationIndex.ScanConfigurationIndexView(modalWrapper, model);
        scanConfigurationIndexView.render();
    }
    E.organizationManagementRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#organizationManagement']").parent().addClass("active");
        $(".delegation").removeClass("active");
        var isSendingRulesEnabled = window.EnableSendingRules === "True";
        var searchAssignedSecurity = { enabled: true, showStructures: true, showUsers: false, showGroups: false };
        //params: isSendingRulesEnabled default false,showSendingRules default false, searchAssignedSecurity default {enabled:false,showStructures:false,showUsers:false,showGroups:false}
        let wrapper = $(".content-wrapper");
        let view = new CTSComponents.OrganizationManagement.OrganizationManagementView(wrapper, isSendingRulesEnabled, true, searchAssignedSecurity);
        view.render();
    }
    E.manageTemplateRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#manageTemplate']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new CTSComponents.Template.TemplateView(wrapper, null);
        documentView.render();
        let modalWrapper = $("#contentDiv");


        let modelIndex = new CTSComponents.TemplateIndex.TemplateIndex();
        let categoriesObj = new CTSComponents.CategoryModel().get();
        let categoriesByTemplates = [];
        for (var i = 0; i < categoriesObj.length; i++) {
            if (categoriesObj[i].byTemplate) {
                var catName = window.language === "en" ? categoriesObj[i].name : window.language === "ar" ? categoriesObj[i].nameAr : categoriesObj[i].nameFr;
                categoriesByTemplates.push({ id: categoriesObj[i].id, text: catName });
            }
        }
        modelIndex.categories = categoriesByTemplates;
        let templateIndexView = new CTSComponents.TemplateIndex.TemplateIndexView(modalWrapper, modelIndex);
        templateIndexView.render();

        let modalTreeWrapper = $(".modal-window"); modalTreeWrapper.empty();
        let treeNodeActionsView = new CTSComponents.TreeNodeActions.TreeNodeActions(modalTreeWrapper, null);
        treeNodeActionsView.render();
    }
    E.manageCategoryRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#manageCategory']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");

        let model = new CTSComponents.Category.Category();
        model.categories = new CTSComponents.Categories().get(window.language);
        let documentView = new CTSComponents.Category.CategoryView(wrapper, model);
        documentView.render();

        let modalWrapper = $(".modal-window");
        modalWrapper.empty();

        let categoryindexIndexView = new CTSComponents.CategoryIndex.CategoryIndexView(modalWrapper, null);
        categoryindexIndexView.render();
        let categoryManagement = new CTSComponents.CategoryManagementView.CategoryManagementView(modalWrapper, null);
        categoryManagement.render();
        let categoryindexImportView = new CTSComponents.CategoryImport.CategoryImportView(modalWrapper, null);
        categoryindexImportView.render();


    }
    E.categoryReferenceNumberRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#categoryreferencenumber']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new CTSComponents.CategoryReferenceNumber.CategoryReferenceNumberView(wrapper, null);
        documentView.render();
        let modalWrapper = $(".modal-window"); modalWrapper.empty();
        let model = new CTSComponents.CategoryReferenceNumberIndex.CategoryReferenceNumberIndex();
        model.categories = new CTSComponents.CategoryReferenceNumberIndex.CategoryReferenceNumberIndex().get();
        let categoryReferenceNumberIndexView = new CTSComponents.CategoryReferenceNumberIndex.CategoryReferenceNumberIndexView(modalWrapper, model);
        categoryReferenceNumberIndexView.render();
    }
    E.categoryReferenceCounterRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#categoryreferencecounter']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new CTSComponents.CategoryReferenceCounter.CategoryReferenceCounterView(wrapper, null);
        view.render();
        let modalWrapper = $(".modal-window"); modalWrapper.empty();
        let referenceCounterView = new CTSComponents.CategoryReferenceCounterIndex.CategoryReferenceCounterIndexView(modalWrapper, null);
        referenceCounterView.render();
    }
    E.classificationRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#classification']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.ClassificationComponent();
        view.render();
    }
    E.importanceRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#importance']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.ImportanceComponent();
        view.render();
    }
    E.privacyRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#privacy']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.PrivacyComponent();
        view.render();
    }
    E.toDoListRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#toDoList']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.ToDoListComponent();
        view.render();
    }
    E.assemblyRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#assembly']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.AssemblyComponent();
        view.render();
    }
    E.activityLogRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#activitylog']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new CTSComponents.ActivityLog.ActivityLog();
        model.categories = new CTSComponents.Categories().get(window.language);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        if (model.statuses.length > 0) {
            model.statuses = model.statuses.filter(function (obj) {
                return obj.id !== SystemStatus.Draft;
            });
        }
        let documentView = new CTSComponents.ActivityLog.ActivityLogView(wrapper, model);
        documentView.render();

        let modalWrapper = $(".modal-window");
        modalWrapper.empty();
        let auditTrailValuesView = new CTSComponents.AuditTrailValues.AuditTrailValuesView(modalWrapper, null);
        auditTrailValuesView.render();

    }
    E.menuRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#menu']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.MenuComponent();
        view.render();
    }
    E.actionRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#action']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");
        let view = new CTSComponents.Action.ActionView(wrapper);//"DocumentTypes" pass as param to change categories label, default is "Categories"
        // let view = new CoreComponents.ActionComponent();//"DocumentTypes" pass as param to change categories label, default is "Categories"
        view.render();
    }
    E.tabRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#tab']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new CTSComponents.Tab.TabView(wrapper); //"DocumentTypes" pass as param to change categories label, default is "Categories"
        //let view = new CoreComponents.TabComponent();
        view.render();
    }
    E.userRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#user']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new CTSComponents.User.UserView(wrapper);
        view.render();
    }
    E.roleRoute = function () {

        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#role']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new CTSComponents.Role.RoleView(wrapper);
        view.render();
    }
    E.nodeRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#node']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.NodeComponent();
        view.render();
    }
    E.categoryRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#category']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.CategoryComponent();//"DocumentTypes" pass as param to change categories label, default is "Categories"
        view.render();
    }
    E.attachmentfolderRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#attachmentfolder']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.AttachmentFolder.AttachmentFolder();
        model.categories = new CTSComponents.Categories().get(window.language);
        let documentView = new CTSComponents.AttachmentFolder.AttachmentFolderView(wrapper, model);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();
        let modelIndex = new CTSComponents.AttachmentFolderIndex.AttachmentFolderIndex();
        let attachmentFolderIndexView = new CTSComponents.AttachmentFolderIndex.AttachmentFolderIndexView(modalWrapper, modelIndex);
        attachmentFolderIndexView.render();
    }
    E.lookupRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#lookup']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.LookupComponent();
        view.render();
    }
    E.documenttypeRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#documenttype']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.DocumentTypeComponent();
        view.render();
    }
    E.searchRoute = function (term) {
        $('.sidebar li[class="active"]').removeClass("active");
        Common.setActiveSidebarMenu("liSearch");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.DocumentSearch.DocumentSearch();
        model.categories = new CTSComponents.Categories().get(window.language);
        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        if (statuses.length > 0) {
            statuses = statuses.filter(function (obj) {
                return obj.id !== SystemStatus.Draft;
            });
        }
        model.statuses = statuses;
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.importances = new CoreComponents.Lookup.Importances().get(window.language);
        model.delegationUsers = new CTSComponents.DelegationUsers().get(window.language);
        let view = new CTSComponents.DocumentSearch.DocumentSearchView(wrapper, model, term != null ? term : '');
        view.render();
    }
    E.advanceSearchConfigurationRoute = function () {

        Common.ajaxGet('/AdvanceSearchConfiguration/GetConfiguration', null, function (response) {
            $('.sidebar li[class="active"]').removeClass("active");

            $("a[href='#advanceSearchConfiguration']").parent().addClass("active");
            $(".delegation").removeClass("active");

            let wrapper = $(".content-wrapper");

            let model = new CTSComponents.DocumentAdvanceSearchConfiguration.DocumentAdvanceSearchConfiguration();
            model.configuration = response;
            let view = new CTSComponents.DocumentAdvanceSearchConfiguration.DocumentAdvanceSearchConfigurationView(wrapper, model);
            view.render();

            let modalWrapper = $(".modal-window");
            modalWrapper.empty();
            let documentAdvanceSearchConfigurationColumnsView = new CTSComponents.DocumentAdvanceSearchConfigurationColumns.AdvanceSearchConfigurationColumnsView(modalWrapper, null);
            documentAdvanceSearchConfigurationColumnsView.render();
        });

    }
    E.nonArchivedAttachmentsTypesRoute = function () {

        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#nonarchivedattachmentstypes']").parent().addClass("active");
        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.NonArchivedAttachmentsTypes.NonArchivedAttachmentsTypes();
        model.types = new CTSComponents.Types().get(window.language);
        let documentView = new CTSComponents.NonArchivedAttachmentsTypes.NonArchivedAttachmentsTypesView(wrapper, model);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();
        let documenttypeIndexView = new CTSComponents.NonArchivedAttachmentsTypesIndex.NonArchivedAttachmentsTypesIndexView(modalWrapper, null);
        documenttypeIndexView.render();
    }
    E.barcodeConfigurationRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#barcodeconfiguration']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let modelIndex = new CTSComponents.BarcodeConfigurationIndex.BarcodeConfigurationIndex();
        modelIndex.categories = new CTSComponents.Categories().get(window.language);
        let documentView = new CTSComponents.BarcodeConfiguration.BarcodeConfigurationView(wrapper, modelIndex);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();
        let model = new CTSComponents.BarcodeConfigurationIndex.BarcodeConfigurationIndex();
        model.categories = new CTSComponents.BarcodeConfigurationIndex.BarcodeConfigurationIndex().get();
        let barcodeConfigurationIndexView = new CTSComponents.BarcodeConfigurationIndex.BarcodeConfigurationIndexView(modalWrapper, model);
        barcodeConfigurationIndexView.render();

        let modelBarcodeIndex = new CTSComponents.BarcodeIndex.BarcodeIndex();
        let barcodeIndexView = new CTSComponents.BarcodeIndex.BarcodeIndexView(modalWrapper, modelBarcodeIndex);
        barcodeIndexView.render();
    }
    E.filingPlanRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");
        $('a[href*="#filingPlan"]').parent().addClass("active")
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new CTSComponents.FilingPlan.FilingPlan();

        let view = new CTSComponents.FilingPlan.FilingPlanView(wrapper, model);
        view.render();

        let modalWrapper = $("#addEditPanel");

        let modelIndex = new CTSComponents.FilingPlanIndex.FilingPlanIndex();
        let menuIndexView = new CTSComponents.FilingPlanIndex.FilingPlanIndexView(modalWrapper, modelIndex);
        menuIndexView.render();
    }
    E.nodeListRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#nodelist']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new CTSComponents.NodeList.NodeList();

        let view = new CTSComponents.NodeList.NodeListView(wrapper, model);
        view.render();

        let modalWrapper = $("#addEditPanel");
        modalWrapper.empty();

        let modelIndex = new CTSComponents.NodeIndex.NodeIndex();
        modelIndex.roles = new CTSComponents.IdentityService().getRoles();
        let menuIndexView = new CTSComponents.NodeIndex.NodeIndexView(modalWrapper, modelIndex);
        menuIndexView.render();
    }
    E.bookmarksRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#bookmarks']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new CTSComponents.Bookmark.Bookmark();
        model.categories = new CTSComponents.Categories().get(window.language);
        let bookmarkView = new CTSComponents.Bookmark.BookmarkView(wrapper, model);
        bookmarkView.render();
    }
    E.systemDashboardRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#systemDashboard']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.SystemDashboard.SystemDashboard();
        model.purposes = new CoreComponents.Lookup.Purposes().get(window.language);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.categories = new CTSComponents.Categories().get(window.language);

        let view = new CTSComponents.SystemDashboard.SystemDashboardView(wrapper, model);
        view.render();
    }
    E.userDashboardRoute = function () {

        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#userDashboard']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");
        let view = new CTSComponents.UserDashboard.UserDashboardView(wrapper);
        view.render();
    }
    E.averageDurationForCorrespondenceCompletionRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#averageDurationForCorrespondenceCompletion']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");

        let view = new CTSComponents.KPI.KPIView(wrapper, "AverageDurationForCorrespondenceCompletion", true);
        view.render();
    }
    E.averageDurationForCorrespondenceDelayRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#averageDurationForCorrespondenceDelay']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");

        let view = new CTSComponents.KPI.KPIView(wrapper, "AverageDurationForCorrespondenceDelay", true);
        view.render();
    }
    E.averageDurationForTransferCompletionRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#averageDurationForTransferCompletion']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");

        let view = new CTSComponents.KPI.KPIView(wrapper, "AverageDurationForTransferCompletion");
        view.render();
    }
    E.averageDurationForTransferDelayRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#averageDurationForTransferDelay']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");

        let view = new CTSComponents.KPI.KPIView(wrapper, "AverageDurationForTransferDelay");
        view.render();
    }
    E.reportInProgressTransfersRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportInProgressTransfers']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new CTSComponents.ReportInProgressTransfers.ReportInProgressTransfersView(wrapper, null);
        documentView.render();
    }
    E.reportCompletedTransfersRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportCompletedTransfers']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new CTSComponents.ReportCompletedTransfers.ReportCompletedTransfersView(wrapper, null);
        documentView.render();
    }
    E.reportOperationByUserRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportOperationByUser']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.ReportOperationByUser.ReportOperationByUser();
        model.categories = new CTSComponents.Categories().get(window.language);
        let documentView = new CTSComponents.ReportOperationByUser.ReportOperationByUserView(wrapper, model);
        documentView.render();
    }
    E.reportOperationByCorrespondenceRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportOperationByCorrespondence']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.ReportOperationByCorrespondence.ReportOperationByCorrespondence();
        let documentView = new CTSComponents.ReportOperationByCorrespondence.ReportOperationByCorrespondenceView(wrapper, null);
        documentView.render();
    }
    E.reportStatisticalCorrespondencesRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportstatisticalcorrespondences']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.ReportStatisticalCorrespondences.ReportStatisticalCorrespondences();
        model.categories = new CTSComponents.Categories().get(window.language);
        let documentView = new CTSComponents.ReportStatisticalCorrespondences.ReportStatisticalCorrespondencesView(wrapper, model);
        documentView.render();
    }
    E.reportCorrespondenceDetailFilterRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportcorrespondencedetailfilter']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.ReportCorrespondenceDetailFilter.ReportCorrespondenceDetailFilter();
        let documentView = new CTSComponents.ReportCorrespondenceDetailFilter.ReportCorrespondenceDetailFilterView(wrapper, model);
        documentView.render();
    }
    E.reportInProgressCorrespondencesRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportInProgressCorrespondences']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new CTSComponents.ReportInProgressCorrespondences.ReportInProgressCorrespondencesView(wrapper, null);
        documentView.render();
    }
    E.reportCompletedCorrespondencesRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportCompletedCorrespondences']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new CTSComponents.ReportCompletedCorrespondences.ReportCompletedCorrespondencesView(wrapper, null);
        documentView.render();
    }
    E.entityGroupRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#entitygroup']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new CTSComponents.EntityGroup.EntityGroupView(wrapper, null);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();


    }
    E.autoForwardRoute = function () {

        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#autoForward']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.AutoForward.AutoForward();
        model.enableTransferToUsers = window.EnableTransferToUsers;
        model.itemsNames = [];
        let documentView = new CTSComponents.AutoForward.AutoForwardView(wrapper, model);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();
    }

    E.favoriteStructuresRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#favoriteStructures']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");


        let favoriteStructuresView = new CTSComponents.FavoriteStructures.FavoriteStructuresView(wrapper, null);
        favoriteStructuresView.render();
    }
    E.distributionListRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#distributionList']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new CTSComponents.DistributionList.DistributionList();

        let distributionListView = new CTSComponents.DistributionList.DistributionListView(wrapper, model);
        distributionListView.render();

        let modalWrapper = $(".modal-window");
        modalWrapper.empty();

        let distributionIndexIndexView = new CTSComponents.DistributionIndex.DistributionIndexView(modalWrapper, null);
        distributionIndexIndexView.render();

    }
    E.AutoForwardRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#AutoForward']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.AutoForward.AutoForward();
        model.enableTransferToUsers = window.EnableTransferToUsers;
        let view = new CTSComponents.AutoForward.AutoForwardView(wrapper, model);
        view.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();

    }
    E.manageCorrespondenceRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#manageCorrespondence']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new CTSComponents.DocumentManageCorrespondence.DocumentManageCorrespondence();
        model.categories = new CTSComponents.Categories().get(window.language);
        model.delegationUsers = new CTSComponents.DelegationUsers().get(window.language);
        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        if (statuses.length > 0) {
            statuses = statuses.filter(function (obj) {
                return obj.id !== SystemStatus.Draft;
            });
        }
        model.statuses = statuses;
        let view = new CTSComponents.DocumentManageCorrespondence.DocumentManageCorrespondenceView(wrapper, model);
        view.render();
    }
    E.committee = function () {
        if (window.location.href.indexOf("committee") < 0) {
            window.location.href = window.location.origin + "/#committee";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#committee']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");

        let view = new CTSComponents.Committee.CommitteeView(wrapper);
        view.render();

    };
    E.createMeeatinAgenda = function () {
        if (window.location.href.indexOf("createMeetingAgenda") < 0) {
            window.location.href = window.location.origin + "/#createMeetingAgenda";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        // $("a[href='#committee']").parent().addClass("active");
        $(".delegation").removeClass("active");

        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        var wrapper = $(".content-wrapper");
        var modelIndex = new CTSComponents.MeetingAgendaIndex.MeetingAgendaIndex();
        // modelIndex.transferId = self.model.transferId;
        //modelIndex.documentId = self.model.documentId;
        //modelIndex.delegationId = self.model.delegationId;
        var linkedCorrespondenceIndex = new CTSComponents.MeetingAgendaIndex.MeetingAgendaIndexView(wrapper, modelIndex);
        linkedCorrespondenceIndex.render();
        var wrapper = $("#linkSearchDiv");
        let model = new CTSComponents.DocumentSearch.DocumentSearch();
        model.categories = new CoreComponents.Lookup.Categories().get(window.language);
        model.statuses = statuses.filter(function (el) { return el.text !== Resources.Draft; });
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.delegationUsers = new CTSComponents.DelegationUsers().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.importances = new CoreComponents.Lookup.Importances().get(window.language);
        // model.documentId = self.model.documentId;
        model.fromLink = true;
        let view = new CTSComponents.DocumentSearch.DocumentSearchView(wrapper, model);
        view.render();
        $('#searchContainerDiv').find("h3").hide();
        setTimeout(function () { $('#cmbSearchFilterCategory').focus(); }, 200);
    };

    E.movetransfers = function () {
        if (window.location.href.indexOf("movetransfers") < 0) {
            window.location.href = window.location.origin + "/#movetransfers";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#movetransfers']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        var model = new CTSComponents.movetransfers.MoveTransfers();
        model.categories = new CoreComponents.Lookup.Categories().get(window.language);
        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        if (statuses.length > 0) {
            statuses = statuses.filter(function (obj) {
                return obj.id !== SystemStatus.Draft;
            });
        }
        model.statuses = statuses;
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.importances = new CoreComponents.Lookup.Importances().get(window.language);
        let view = new CTSComponents.movetransfers.MoveTransfersView(wrapper, model);
        view.render();

    };

    E.listSecureUsers = function()
    {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#listsecureusers']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new CTSComponents.ListSecureUsers(wrapper);
        view.render();
    }
    return E;
}   
(CustomMenus));

var CustomNodes = (function (E) {
    E = {};
    E.g2g = function (ViewName) {
        window.location.href = window.location.origin + "/#g2g/" + ViewName;

        var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
        var node = $.grep(nodes, function (element, index) {
            if (element.customFunctions != null && element.customFunctions.includes(ViewName)) {
                return element.customFunctions.includes(ViewName);
            }
        })[0];
        if (node != null) {
            $('.sidebar li[class="active"]').removeClass("active");
            Common.setActiveSidebarMenu("liCustom" + node.id);
            $(".delegation").removeClass("active");
            let wrapper = $(".content-wrapper");
            let model = new g2g.Documentg2g();
            model.nodeId = node.id;
            model.viewName = ViewName;
            model.nodeName = node.name;
            model.categories = new Categories().get(window.language);
            let documentView = new g2g.Documentg2gView(wrapper, model);
            documentView.render();
        }
    };
    return E;
}(CustomNodes));

globalThis.CTSCoreComponents = new class {
    constructor() {
        this.CustomTabs = CustomTabs;
        this.CustomActions = CustomActions;
        this.CustomMenus = CustomMenus;
        this.CustomNodes = CustomNodes;
    }
}();
function getSimpleSearchForSearchModel() {
    let hashArray = window.location.hash.split("/");
    var returnedVal = decodeURIComponent(hashArray[1]);
    return returnedVal;
}


$("#txtSimpleSearchFilter").click(function (event) {

  

    if (event.keyCode === 13) {
        if ($('#txtSimpleSearchFilter').val().trim())
            window.location.hash = "#search/" + encodeURIComponent($('#txtSimpleSearchFilter').val());
    }

});

$("#txtSimpleSearchFilter").keypress(function (event) {
   

    if (event.keyCode === 13) {
        if ($('#txtSimpleSearchFilter').val().trim())
            window.location.hash = "#search/" + encodeURIComponent($('#txtSimpleSearchFilter').val());
    }

});
(function (window, document, $, undefined) {


    $(function () {


        var navSearch = new navbarSearchInput();

        // Open search input
        var $searchOpen = $('[data-search-open]');

        $searchOpen
            .on('click', function (e) { e.stopPropagation(); })
            .on('click', navSearch.toggle);

        // Close search input
        var $searchDismiss = $('[data-search-dismiss]');
        var inputSelector = '.navbar-form input[type="text"]';

        $(inputSelector)
            .on('click', function (e) { e.stopPropagation(); })
            .on('keyup', function (e) {
                if (e.keyCode == 27) // ESC
                    navSearch.dismiss();
            });

        // click anywhere closes the search
        $(document).on('click', navSearch.dismiss);
        // dismissable options
        $searchDismiss
            .on('click', function (e) { e.stopPropagation(); })
            .on('click', navSearch.dismiss);

    });

    var navbarSearchInput = function () {
        var navbarFormSelector = 'form.navbar-form';
        return {
            toggle: function () {

                var navbarForm = $(navbarFormSelector);

                navbarForm.toggleClass('open');

                var isOpen = navbarForm.hasClass('open');

                navbarForm.find('input')[isOpen ? 'focus' : 'blur']();

            },

            dismiss: function () {
                $(navbarFormSelector)
                    .removeClass('open') // Close control
                    .find('input[type="text"]').blur() // remove focus
                    .val('')                    // Empty input
                    ;
            }
        };

    }

})(window, document, window.jQuery);


