﻿using Intalio.Core;
using Intalio.Core.API;
using Intalio.Core.Model;
using Intalio.Core.UI.Controllers;
using Intalio.Core.UI.Filters;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Filters;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using DelegationListViewModel = Intalio.CTS.Core.Model.DelegationListViewModel;
using DelegationMenuModel = Intalio.CTS.Core.Model.DelegationMenuModel;
using DelegationModel = Intalio.CTS.Core.Model.DelegationModel;
using DelegationViewModel = Intalio.CTS.Core.Model.DelegationViewModel;
using ManageDelegation = Intalio.CTS.Core.API.ManageDelegation;
using SystemDelegationListViewModel = Intalio.CTS.Core.Model.SystemDelegationListViewModel;
using SystemDelegationViewModel = Intalio.CTS.Core.Model.SystemDelegationViewModel;

namespace Intalio.CTS.Core.Controllers
{
    [Route("CTS/[controller]/[action]")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public class DelegationController : BaseController
    {
        #region Ajax

        /// <summary>
        /// Create or edit delegation
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.Delegation) })]
        public IActionResult Save(DelegationViewModel model)
        {
            try
            {
                Result retValue = new Result();
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                bool success = false;
                if (ModelState.IsValid)
                {
                    var checkedDelegation =
                        ManageDelegation.CheckUnique(UserId, model);
                    if (!checkedDelegation.Exists)
                    {
                        if (checkedDelegation.Code == 1)
                        {
                            retValue.Message = TranslationUtility.Translate("UserAlreadyDelegated", Language);
                        }
                        else if (checkedDelegation.Code == 2)
                        {
                            retValue.Message = TranslationUtility.Translate("IncorrectDuration", Language);
                        }
                        else if (checkedDelegation.Code == 3)
                        {
                            retValue.Message = TranslationUtility.Translate("SameCategoryAlreadyAssignedOnSelectedDuration", Language);
                        }
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        return StatusCode(code, retValue);
                    }
                    if (model.Id.HasValue)
                    {
                        success = ManageDelegation.Edit(UserId, Language, model);
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                    }
                    else
                    {
                        ManageDelegation.Create(UserId, Language, model);
                        success = true;
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                    }
                    if (success)
                    {
                        retValue.Id = model.Id.Value;
                    }
                }
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List all delegations created by the user
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<DelegationListViewModel>), 200)]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.Delegation) })]
        public JsonResult List([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                //int documentTypeId = Convert.ToInt32(Request.Form["DocumentTypeId"][0]);
                long toUserId = Request.Form["ToUserId"].Count > 0 ? Convert.ToInt64(Request.Form["ToUserId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                if (toUserId != default)
                {
                    filter.Add("ToUserId", toUserId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("FromDate", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date.AddDays(1).AddTicks(-1);
                    filter.Add("ToDate", to, Operator.LessThanOrEqualTo);
                }
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    int columnNb = Convert.ToInt32(column);
                    switch (columnNb)
                    {
                        case 4:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "FromDate" });
                            break;
                        case 5:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "ToDate" });
                            break;
                        case 6:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = ManageDelegation.List(start, length, UserId, filter, sorting);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// Delete delegation
        /// </summary>
        /// <remarks>The delegation must be <b>created by the current user</b></remarks>
        /// <param name="ids">Ids of the delegations to be deleted</param>
        /// <returns></returns>
        [HttpDelete]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.Delegation) })]
        public IActionResult Delete(List<long> ids)
        {
            try
            {
                ManageDelegation.Delete(UserId, ids);
                string hostURL = string.Format("{0}://{1}", Request?.Scheme, Request?.Host.Value);
                ManageApplicationServer.ClearCacheAllServers(hostURL, typeof(ManageDelegation).Name);
                return Ok();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List all delegations delegated to the current user
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        [ProducesResponseType(typeof(List<DelegationMenuModel>), 200)]
        public IActionResult ListDelegationToUser()
        {
            try
            {
                return Ok(ManageDelegation.ListDelegatedToUser(UserId));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List delegation by id
        /// </summary>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        [ProducesResponseType(typeof(DelegationModel), 200)]
        public IActionResult GetByDelegationId(long delegationId)
        {
            try
            {
                return Ok(ManageDelegation.GetByDelegationId(UserId, delegationId));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        #region Administrator

        [HideInSwagger]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.SystemDelegation) })]
        [HttpPost]
        public IActionResult SaveSystemDelegation(SystemDelegationViewModel model)
        {
            try
            {
                Result retValue = new Result();
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                bool success = false;
                if (ModelState.IsValid)
                {
                    var checkedDelegation = ManageDelegation.CheckUnique(model);
                    if (!checkedDelegation.Exists)
                    {
                        if (checkedDelegation.Code == 1)
                        {
                            retValue.Message = TranslationUtility.Translate("UserAlreadyDelegated", Language);
                        }
                        else if (checkedDelegation.Code == 2)
                        {
                            retValue.Message = TranslationUtility.Translate("IncorrectDuration", Language);
                        }
                        else if (checkedDelegation.Code == 3)
                        {
                            retValue.Message = TranslationUtility.Translate("SameCategoryAlreadyAssignedOnSelectedDuration", Language);
                        }
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        return StatusCode(code, retValue);
                    }
                    if (model.Id.HasValue)
                    {
                        success = ManageDelegation.EditSystemDelegation(UserId, Language, model);
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        if (success)
                        {
                            string hostURL = string.Format("{0}://{1}", Request?.Scheme, Request?.Host.Value);
                            ManageApplicationServer.ClearCacheAllServers(hostURL, typeof(ManageDelegation).Name);
                        }
                    }
                    else
                    {
                        ManageDelegation.CreateSystemDelegation(UserId, Language, model);
                        success = true;
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                    }
                    if (success)
                    {
                        retValue.Id = model.Id.Value;
                    }
                }
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HideInSwagger]
        [CustomMenusAuthorizationFilter(new string[] {nameof(CustomMenus.SystemDelegation)})]
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<SystemDelegationListViewModel>), 200)]
        public JsonResult ListSystemDelegation([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                long fromUserId = Request.Form["FromUserId"].Count > 0 ? Convert.ToInt64(Request.Form["FromUserId"][0]) : default;
                long toUserId = Request.Form["ToUserId"].Count > 0 ? Convert.ToInt64(Request.Form["ToUserId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                if (fromUserId != default)
                {
                    filter.Add("FromUserId", fromUserId, Operator.Equals);
                }
                if (toUserId != default)
                {
                    filter.Add("ToUserId", toUserId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("FromDate", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date.AddDays(1).AddTicks(-1);
                    filter.Add("ToDate", to, Operator.LessThanOrEqualTo);
                }
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    int columnNb = Convert.ToInt32(column);
                    switch (columnNb)
                    {
                        case 4:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "FromDate" });
                            break;
                        case 5:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "ToDate" });
                            break;
                        case 6:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = ManageDelegation.ListSystemDelegation(start, length, filter, sorting);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        [HideInSwagger]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.SystemDelegation) })]
        [HttpDelete]
        public IActionResult DeleteSystemDelegation(List<long> ids)
        {
            try
            {
                ManageDelegation.DeleteSystemDelegation(UserId, ids);
                string hostURL = string.Format("{0}://{1}", Request?.Scheme, Request?.Host.Value);
                ManageApplicationServer.ClearCacheAllServers(hostURL, typeof(ManageDelegation).Name);
                return Ok();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        #endregion

        #endregion
    }
}