const gulp = require('gulp');
const through2 = require('through2');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const yargs = require('yargs');
const { hideBin } = require('yargs/helpers');

const argv = yargs(hideBin(process.argv)).argv;
const root = argv.publishDir;

if(!root)
{
    throw new Error("publishDir should be provided")
}
    
const sourceDirs = [
    `${root}\\components\\**\\*.js`,
    `${root}\\JS\\**\\*.js`
];

function generateHash(filePath) {
    try {
        const content = fs.readFileSync(filePath);
        return crypto.createHash('md5').update(content).digest('hex').substring(0, 10);
    } catch {
        return '0000000000'; // fallback hash if file not found
    }
}

// Regex to match import statements
const importPattern = /import\s+.+?(['"])(.+?\.js)\1/gim;


gulp.task('version-js-imports', function () {
    // ANSI color codes
    const RESET = '\x1b[0m';
    const BRIGHT = '\x1b[1m';
    const DIM = '\x1b[2m';

    const FG_GREEN = '\x1b[32m';
    const FG_CYAN = '\x1b[36m';
    const FG_MAGENTA = '\x1b[35m';
    const FG_YELLOW = '\x1b[33m';
    const FG_BLUE = '\x1b[34m';
    const FG_GRAY = '\x1b[90m';

    console.log();
    console.log(`${FG_BLUE}${BRIGHT} GULP TASK STARTED ${RESET}`);
    console.log(`${FG_GRAY}----------------------------------------${RESET}`);

    console.log(`${FG_GREEN}• Running Gulp in:${RESET} ${FG_CYAN}${root}${RESET}`);
    console.log(`${FG_GREEN}• Source Directories:${RESET}`);
    sourceDirs.forEach(dir => {
        console.log(`  ${FG_YELLOW}${dir}${RESET}`);
    });

    console.log(`${FG_GRAY}----------------------------------------${RESET}`);
    console.log(`${FG_BLUE}⏳ Appending version query (?v=HASH) to JS import paths...${RESET}\n`);

    return gulp.src(sourceDirs, { base: root })
        .pipe(through2.obj(function (file, _, cb) {
        if (file.isBuffer()) {
            let content = file.contents.toString();

            content = content.replace(importPattern, (match, quote, importPath) => {
                if (importPath.includes('?v=')) return match;

                const absoluteImportedPath = path.resolve(path.dirname(file.path), importPath);
                const hash = generateHash(absoluteImportedPath);
                const versionedPath = `${importPath}?v=${hash}`;

                return match.replace(importPath, versionedPath);
            });

            file.contents = Buffer.from(content);
        }
        cb(null, file);
        }))
        .pipe(gulp.dest(root));
});
