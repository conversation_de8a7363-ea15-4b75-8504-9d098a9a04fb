﻿
namespace Intalio.CTS.Core.Model
{
    public class DocumentDetailsModel : DocumentViewModel
    {
        public string ReferenceNumber { get; set; }
        public long? SenderPerson { get; set; }
        public bool IsExternalSender { get; set; }
        public string? ReceiverPerson { get; set; }
        public bool IsExternalReceiver { get; set; }
        public short Status { get; set; }
        public string CreatedByUser { get; set; }
        public long CreatedByUserId { get; set; }
        public string? CreatedDate { get; set; }
        public long? AttachmentId { get; set; }
        public string AttachmentVersion { get; set; }
        public long? AttachmentCount { get; set; }
        public long? NotesCount { get; set; }
        public long? LinkedCorrespondanceCount { get; set; }
        public bool? ByTemplate { get; set; }
        public bool? HasUserCofigureSignature { get; set; }
        public bool TemplateHasSignature { get; set; }
        public string AttachmentExtention { get; set; }
        public bool AllowSign { get; set; }
        public bool? IsSigned { get; set; }
        public bool? AttachmentIslocked { get; set; }

        //public bool IsSigned { get; set; }
        //public bool? IsSigned { get; set; }

        //public string? AttachmentExtention { get; set; }

        //public bool TemplateHasSignature { get; set; }

        //public string attachmentExtention { get; set; }

    }
}