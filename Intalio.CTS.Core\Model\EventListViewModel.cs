﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.Model
{
    public class EventListViewModel
    {
        public long Id { get; set; }
        public string CreatedBy { get; set; }
        public long? CreatedByUserId { get; set; }
        public string Name { get; set; }
        public string Location { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        public string Description { get; set; }
        public DateTime CreatedDate { get; set; }
    }
}
