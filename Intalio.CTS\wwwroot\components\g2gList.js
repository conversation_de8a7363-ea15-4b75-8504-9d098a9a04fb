import Intalio from './common.js'
import DocumentDetails from './documentDetails.js'
import { Categories } from './lookup.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'

class Documentg2g extends Intalio.Model
{
    constructor()
    {
        super();
        this.nodeId = null;
        this.delegationId = null;
        this.categories = null;
        this.statuses = null;
        this.priorities = null;
        this.privacies = null;
        this.viewName = null;
        this.nodeName = null;
    }
}
function format(row, nodeId)
{
    return '<table style="width:100%" cellspacing="0" border="0">' + buildColumnsDetails(row, nodeId) + '</table>';
}
function openDocument(id, delegationId, nodeId)
{
    var params = { id: id };
    if (delegationId !== null)
    {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetSearchDocument', params, function (response)
    {
        gLocked = false;
        let item = "liCustom" + nodeId;
        if (delegationId !== null)
        {
            item = "completed-" + nodeId;
        }
        Common.setActiveSidebarMenu(item);
        $(".delegation").removeClass("active");
        //$("#gridContainerDiv").hide();
        var model = new DocumentDetails.DocumentDetails();
        model.categoryId = response.categoryId;
        model.documentId = response.id;
        model.referenceNumber = response.referenceNumber;
        model.categoryName = response.categoryName;
        model.statusId = response.status;
        model.documentModel = response;
        model.readonly = true;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.showMyTransfer = false;
        model.attachmentId = response.attachmentId;
        model.formData = (response.formData || "") !== "" ? eval("(" + response.formData + ")") : null;
        model.formDesigner = (response.formDesigner || "") !== "" ? JSON.parse(response.formDesigner) : null;
        model.formDesignerTranslation = (response.formDesignerTranslation || "") !== "" ? JSON.parse(response.formDesignerTranslation) : null;
        model.g2gInternalId = response.g2GInternalId;
        model.fromSearch = true;

        model.showBackButton = false;
        model.isModal = true;

        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
        model.tabs = $.grep(tabs, function (element, index)
        {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
        });
        model.tabsWithStatic = tabs;
        model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
        var wrapper = $(".modal-documents");

        var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
        linkedCorrespondenceModel.reference = response.referenceNumber;
        linkedCorrespondenceModel.subject = response.subject;

        var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
        linkedCorrespondenceDocument.render();

        model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
        wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);
        var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
        view.render();

        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
            $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');

        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
            if ($(this).data("remove") != true)
                return;
            $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
            swal.close();
            //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
            //    $('body').addClass('modal-open');
            //}
        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

    }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
}
function buildFilters(nodeId, categories)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var filterJson = '["ReferenceNumber","FromDate","ToDate","Category","Subject"]'
    var filters = JSON.parse(filterJson);

    if (filters.length > 0)
    {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++)
        {
            var filter = filters[i];
            switch (filter)
            {
                case "ReferenceNumber":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
                        '<input type="text" id="txtFilterCompletedReferenceNumber" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "FromDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label>' +
                        '<div class="input-group date"><input id="filterCompletedFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterCompletedFromDateError">' +
                        '<span class="input-group-addon" id="filterCompletedFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterCompletedFromDateError"></div></div></div></div>';
                    break;
                case "ToDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label>' +
                        '<div class="input-group date"><input id="filterCompletedToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterCompletedToDateError">' +
                        '<span class="input-group-addon" id="filterCompletedToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterCompletedToDateError"></div></div></div></div>';
                    break;
                case "Category":
                    clearFix += 3;
                    var div = '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="categoryFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterCompletedCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++)
                    {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterCompletedCategoryError"></div></div></div>';
                    html += div;
                    break;
                case "Subject":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterCompletedSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Purpose":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="purposeFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.Purpose + '</label>' +
                        '<select id="cmbFilterCompletedPurpose" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedPurposeError" class="form-control"></select></div></div>';
                    break;
                case "Priority":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="priorityFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.Priority + '</label>' +
                        '<select id="cmbFilterCompletedPriority" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedPriorityError" class="form-control"></select></div></div>';
                    break;
                case "Privacy":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="privacyFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.Privacy + '</label>' +
                        '<select id="cmbFilterCompletedPrivacy" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedPrivacyError" class="form-control"></select></div></div>';
                    break;
                case "Structure":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="structureFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.Structure + '</label>' +
                        '<select id="cmbFilterCompletedStructure" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedStructureError" class="form-control"></select></div></div>';
                    break;
                case "User":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="userFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.User + '</label>' +
                        '<select id="cmbFilterCompletedUser" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedUserError" class="form-control"></select></div></div>';
                    break;
            }
            if (clearFix === 12 || clearFix === 11)
            {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 list-action-filter"><button id="btnFilterCompletedSearch" tabindex="6" type="button" class="btn btn-primary">' + Resources.Search + '</button>' +
            ' <button id="btnFilterCompletedClear" tabindex="7" type="button" class="btn btn-clear">' + Resources.Clear + '</button></div>';
        html += '</div>';
        $('#filtersContainer').html(html);

        var clickedSearch = false;
        $('#collapseCompletedIcon').click(function ()
        {
            $('#collapseCompletedIcon').empty();
            if (clickedSearch)
            {
                $('#collapseCompletedIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
                $('#collapseCompletedPanel').attr('class', '');
                $('#collapseCompletedPanel').addClass('panel-body panel-collapse collapse in');
                clickedSearch = false;
            } else
            {
                $('#collapseCompletedIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
                $('#collapseCompletedPanel').attr('class', '');
                $('#collapseCompletedPanel').addClass('panel-body panel-collapse collapse');
                clickedSearch = true;
            }
        });
        $("#btnFilterCompletedSearch").on('click', function ()
        {
            GridCommon.Refresh(gTableName);
        });
        $("#btnFilterCompletedClear").on('click', function ()
        {
            $("#cmbFilterCompletedPurpose").val('').trigger('change');
            $("#cmbFilterCompletedPriority").val('').trigger('change');
            $("#cmbFilterCompletedPrivacy").val('').trigger('change');
            $("#cmbFilterCompletedCategory").val('').trigger('change');
            $("#cmbFilterCompletedStatus").val('').trigger('change');
            $("#txtFilterCompletedReferenceNumber").val('');
            $("#txtFilterCompletedSubject").val('');
            fromDate.clear();
            toDate.clear();
            $("#cmbFilterCompletedStructure").val('').trigger('change');
            $("#cmbFilterCompletedUser").val('').trigger('change');
            GridCommon.Refresh(gTableName);
        });
        $('#cmbFilterCompletedPurpose').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Purposes().get(window.language),
            dropdownParent: $('#purposeFilterCompletedContainer')
        });
        $("#cmbFilterCompletedPurpose").val('').trigger('change');

        $('#cmbFilterCompletedPriority').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Priorities().get(window.language),
            dropdownParent: $('#priorityFilterCompletedContainer')
        });
        $("#cmbFilterCompletedPriority").val('').trigger('change');

        $('#cmbFilterCompletedPrivacy').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Privacies().get(window.language),
            dropdownParent: $('#privacyFilterCompletedContainer')
        });
        $("#cmbFilterCompletedPrivacy").val('').trigger('change');

        $('#cmbFilterCompletedCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterCompletedContainer')
        });
        $("#cmbFilterCompletedCategory").val('').trigger('change');

        $('#cmbFilterCompletedStructure').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#structureFilterCompletedContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes',
                type: "POST",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term)
                {
                    return {
                        "text": term.term ? term.term : "", "language": window.language, "attributes": [window.StructureNameAr, window.StructureNameFr]
                    };
                },
                processResults: function (data)
                {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val)
                    {
                        var structureName = val.name;
                        if (val.attributes != null && val.attributes.length > 0)
                        {
                            var attributeLang = $.grep(val.attributes, function (e)
                            {
                                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                            });
                            if (attributeLang.length > 0)
                            {
                                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                            }
                        }
                        var item = {};
                        item.id = val.id;
                        item.text = structureName;
                        item.isStructure = true;
                        item.dataId = val.id;
                        listitemsMultiList.push(item);
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        }).on("change", function ()
        {
            if (document.getElementById('cmbFilterCompletedUser') !== null)
            {
                var type = "GET";
                var url = '/api/SearchUsers';
                var structures = $('#cmbFilterCompletedStructure').val();
                //if (structures !== "" && structures !== null)
                //{
                //    type = "POST";
                //    url = '/api/SearchUsersByStructureIds';
                //}
                $('#cmbFilterCompletedUser').select2({
                    minimumInputLength: 0,
                    allowClear: false,
                    placeholder: "",
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    dropdownParent: $("#userFilterCompletedContainer"),
                    multiple: true,
                    width: "100%",
                    ajax: {
                        delay: 400,
                        url: window.IdentityUrl + url,
                        type: type,
                        dataType: 'json',
                        headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                        data: function (term)
                        {
                            var params = { "text":  "", "language": window.language };
                            //var params = { "text": term.term ? term.term : "", "language": window.language };
                            //var structures = $('#cmbFilterCompletedStructure').val();
                            //if (structures !== "" && structures !== null)
                            //{
                            //    params.ids = $('#cmbFilterCompletedStructure').val();
                            //}
                            return params;
                        },
                        processResults: function (data, term)
                        {
                            var termSearch = term.term ? term.term : "";

                            var structures = $('#cmbFilterCompletedStructure').val();
                            var listitemsMultiList = [];
                            $.each(data, function (key, val)
                            {
                                if (structures !== "" && structures !== null &&
                                    !((val.structureIds != null && val.structureIds.some(r => structures.indexOf(String(r)) >= 0)) ||
                                        structures.includes(val.defaultStructureId))) {
                                    return;
                                }
                                var fullName = val.fullName;
                                if (window.language != 'en') {
                                    fullName = getFullNameByLangauge(val);
                                   fullName= fullName.trim() == "" ? val.fullName : fullName;
                                }
                                var allNames = getFullNameInAllLangauge(val);
                                if (allNames.length == 0) allNames.push(fullName);
                                if (termSearch != "" &&
                                    !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                                    return;
                                }
                                let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                                if (!isExist) {
                                    var item = {};
                                    item.id = val.id;
                                    item.text = fullName;
                                    item.isStructure = false;
                                    item.dataId = val.id;
                                    listitemsMultiList.push(item);
                                }
                            });
                            return {
                                results: listitemsMultiList
                            };
                        }
                    },
                    sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
                }).val("").trigger("change");
            }
        });
        $("#cmbFilterCompletedStructure").val('').trigger('change');
        $('#cmbFilterCompletedUser').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#userFilterCompletedContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchUsers',
                type: "GET",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term)
                {
                    return { "text": "", "language": window.language };
                    //return { "text": term.term ? term.term : "", "language": window.language };
                },
                processResults: function (data, term)
                {
                    var termSearch = term.term ? term.term : "";

                    var listitemsMultiList = [];
                    $.each(data, function (key, val)
                    {
                        var fullName = val.fullName;
                        if (window.language != 'en') {
                            fullName = getFullNameByLangauge(val);
                           fullName= fullName.trim() == "" ? val.fullName : fullName;
                        }
                        var allNames = getFullNameInAllLangauge(val);
                        if (allNames.length == 0) allNames.push(fullName);
                        if (termSearch != "" &&
                            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                            return;
                        }
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = fullName;
                            item.isStructure = false;
                            item.dataId = val.id;
                            listitemsMultiList.push(item);
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });
        $("#cmbFilterCompletedUser").val('').trigger('change');

        var fromDate = $('#filterCompletedFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    maxDate: jQuery('#filterCompletedToDate').val() && jQuery('#filterCompletedToDate').val() !== "" ? jQuery('#filterCompletedToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterCompletedFromDate_img").click(function ()
        {
            fromDate.toggle();
        });
        var toDate = $('#filterCompletedToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    minDate: jQuery('#filterCompletedFromDate').val() && jQuery('#filterCompletedFromDate').val() !== "" ? jQuery('#filterCompletedFromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterCompletedToDate_img").click(function ()
        {
            toDate.toggle();
        });
        $('#txtFilterCompletedReferenceNumber').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterCompletedSearch').focus();
                }
                else
                {
                    $('#filterCompletedFromDate').focus();
                }
            }
        });
        $('#btnFilterCompletedClear').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterCompletedSearch').focus();
                }
                else
                {
                    $('#filterCompletedFromDate').focus();
                }
            }
        });
    } else
    {
        $(".searchToRemove").remove();
        $("#gridPanel").removeAttr("style");
        $("#gridPanel").addClass("panel-default");
    }
}
function buildColumns(gridcolumns, nodeId,self)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var columnjson = '[{"name":"G2G_REF_NO","order":"1","isColumnDetail":false},{"name":"ReferenceNumber","order":"2","isColumnDetail":false},{"name":"Category","order":"3","isColumnDetail":false},{"name":"SendingEntity","order":"4","isColumnDetail":false},{"name":"ReceivingEntity","order":"5","isColumnDetail":false},{"name":"Subject","order":"6","isColumnDetail":false},{"name":"TransferDate","order":"7","isColumnDetail":false},{"name":"Owner","order":"8","isColumnDetail":false},{"name":"SendingEntity","order":"1","isColumnDetail":true},{"name":"ReceivingEntity","order":"2","isColumnDetail":true},{"name":"Purpose","order":"3","isColumnDetail":true},{"name":"Priority","order":"4","isColumnDetail":true},{"name":"Privacy","order":"5","isColumnDetail":true},{"name":"CreatedBy","order":"6","isColumnDetail":true},{"name":"ClosedDate","order":"8","isColumnDetail":true}]'
    var columns =  JSON.parse(columnjson) ;
    var columnDetails = $.grep(columns, function (element, index)
    {
        return element.isColumnDetail === true;
    });
    if (columnDetails.length > 0)
    {
        if (self.model.viewName != "G2G_DocumentInboxIncomingRejected" && self.model.viewName != "G2G_DocumentInboxIncomingRecalled") {
            gridcolumns.push({
                "className": 'details-control',
                "orderable": false,
                "data": null,
                "defaultContent": '',
                width: '16px'
            });
        }
       
    }
    gridcolumns.push(
        {
            "className": "text-left",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            data: "importanceId",
            'render': function (data, type, full, meta)
            {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var i = 0; i < importances.length; i++)
                {
                    if (importances[i].id === data)
                    {
                        let btnView = document.createElement("i");
                        btnView.setAttribute("class", "fa fa-exclamation fa-lg");
                        btnView.setAttribute("style", "color:" + importances[i].color);
                        btnView.setAttribute("title", importances[i].text);
                        return btnView.outerHTML;
                    }
                }
                return "";
            }
        });
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var customColumn = [];
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (!column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                customColumn.push({ columnName: column.name, functionName: column.customFunctionName });
                var customColumnKeyword = column.customFunctionName;
                var escapedVariable = column.customFunctionName.replace(/"/g, '&quot;');

                gridcolumns.push({
                    title: GetCustomAttributeTranslationByLangauge(customColumnKeyword), "orderable": false, 'defaultContent': '<div data-function="' + escapedVariable + '"></div>',
                    "createdCell": function (td, cellData, rowData, row, col)
                    {
                        var htmlCell = "";
                        var div = td.children[0];
                        var customFunctionName = div !== null && typeof div !== "undefined" ? $(div).attr("data-function") : "";
                        //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                        //{
                        //    var functionName = customFunctionName.split("(")[0];
                        //    htmlCell = eval(functionName + "(" + JSON.stringify(rowData) + ")");
                        //}

                        var customColumns = JSON.parse(rowData.documentForm.form);
                        var columnName = customColumn.find(f => f.functionName == customFunctionName).columnName;
                        htmlCell = htmlCell == "" && customColumns != null ? (customColumns[columnName] == undefined ? "" : customColumns[columnName]) : htmlCell;

                        $(td).html(htmlCell);
                    }
                });
            } else
            {
                switch (column.name)
                {
                    case "Category":
                        gridcolumns.push({
                            title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"],
                            render: function (data, type, full, meta)
                            {
                                var categories = new Categories().get(window.language);
                                for (var i = 0; i < categories.length; i++)
                                {
                                    if (categories[i].id === data)
                                    {
                                        return categories[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "ReferenceNumber":
                        gridcolumns.push({ title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": false, "className": "min-max-width-50-150" });
                        break;
                    case "G2G_REF_NO":
                        gridcolumns.push({ title: Resources.G2GRefNo, data: "g2G_REF_NO", render: $.fn.dataTable.render.text(), "orderable": false, "className": "min-max-width-50-150" });
                        break;
                    case "Subject":
                        gridcolumns.push({ title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"], "className": "min-max-width-50-150" });
                        break;
                    case "From":
                        gridcolumns.push({
                            title: Resources.From, "orderable": false,
                            "render": function (data, type, full, meta)
                            {
                                var retValue = "";
                                if (full.fromStructure)
                                {
                                    retValue += full.fromStructure;
                                }
                                if (full.fromUser)
                                {
                                    var user = full.fromUser;
                                    retValue += retValue !== "" ? "/" + user : user;
                                }
                                return retValue;
                            }
                        });
                        break;
                    case "To":
                        gridcolumns.push({
                            title: Resources.To, "orderable": false,
                            "render": function (data, type, full, meta) {
                                var retValue = "";
                                if (full.toStructure) {
                                    retValue += full.toStructure;
                                }
                                if (full.toUser) {
                                    var user = full.toUser;
                                    retValue += retValue !== "" ? "/" + user : user;
                                }
                                return retValue;
                            }
                        });
                        break;
                    case "TransferDate":
                        gridcolumns.push({
                            title: Resources.TransferDate, data: "transferDate", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.transferDate, null, window.CalendarType);
                            }
                        });
                        break;
                    case "Owner":
                        gridcolumns.push({ title: Resources.Owner, data: "lockedBy", "orderable": false });
                        break;
                    case "SendingEntity":
                        gridcolumns.push({ title: Resources.SendingEntity, data: "sendingEntity", "orderable": false, width: "100px" });
                        break;
                    case "ReceivingEntity":
                        gridcolumns.push({ title: Resources.ReceivingEntity, data: "receivingEntity", "orderable": false, width: "100px" });
                        break;
                    case "Purpose":
                        gridcolumns.push({
                            title: Resources.Purpose, data: "purpose", "orderable": false, width: "100px",
                            render: function (data, type, full, meta)
                            {
                                var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                                for (let i = 0; i < purposes.length; i++)
                                {
                                    if (purposes[i].id === full.purposeId)
                                    {
                                        return purposes[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "Priority":
                        gridcolumns.push({
                            title: Resources.Priority, data: "priority", "orderable": false, width: "100px",
                            render: function (data, type, full, meta)
                            {
                                var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                                for (let i = 0; i < priorities.length; i++)
                                {
                                    if (priorities[i].id === full.priorityId)
                                    {
                                        return priorities[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "Privacy":
                        gridcolumns.push({
                            title: Resources.Privacy, data: "privacy", "orderable": false, width: "100px",
                            render: function (data, type, full, meta)
                            {
                                var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                                for (let i = 0; i < privacies.length; i++)
                                {
                                    if (privacies[i].id === full.privacyId)
                                    {
                                        return privacies[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "CreatedBy":
                        gridcolumns.push({ title: Resources.CreatedBy, data: "createdByUser", "orderable": false, width: "100px" });
                        break;
                    case "ClosedDate":
                        gridcolumns.push({
                            title: Resources.ClosedDate, data: "closedDate", "orderable": false, width: "100px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.closedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                            }
                        });
                        break;
                }
            }
        }
    }
}
function buildColumnsDetails(row, nodeId)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var columnjson = '[{"name":"Category","order":"1","isColumnDetail":false},{"name":"ReferenceNumber","order":"2","isColumnDetail":false},{"name":"From","order":"3","isColumnDetail":false},{"name":"Subject","order":"4","isColumnDetail":false},{"name":"TransferDate","order":"5","isColumnDetail":false},{"name":"Owner","order":"6","isColumnDetail":false},{"name":"SendingEntity","order":"1","isColumnDetail":true},{"name":"ReceivingEntity","order":"2","isColumnDetail":true},{"name":"Purpose","order":"3","isColumnDetail":true},{"name":"Priority","order":"4","isColumnDetail":true},{"name":"Privacy","order":"5","isColumnDetail":true},{"name":"CreatedBy","order":"6","isColumnDetail":true},{"name":"ClosedDate","order":"8","isColumnDetail":true}]'

    var columns =  JSON.parse(columnjson) ;
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var html = "";
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                var customColumn = column.name;
                var customColumnKeyword = column.customFunctionName;

                var htmlCell = "";
                var customFunctionName = column.customFunctionName;
                //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                //{
                //    var functionName = customFunctionName.split("(")[0];
                //    htmlCell = eval(functionName + "(" + JSON.stringify(row.data()) + ")");
                //}

                var customColumns = JSON.parse(row.data().documentForm.form);
                htmlCell = htmlCell == "" && customColumns != null ? (customColumns[customColumn] == undefined ? "" : customColumns[customColumn]) : htmlCell;

                html += '<tr><th style="width: 10%;padding:5px">' + (GetCustomAttributeTranslationByLangauge(customColumnKeyword)) + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + htmlCell + '</td></tr>';

            } else
            {
                switch (column.name)
                {
                    case "Category":
                        var category = "";
                        var categories = new Categories().get(window.language);
                        for (var i = 0; i < categories.length; i++)
                        {
                            if (categories[i].id === row.data().categoryId)
                            {
                                category = categories[i].text;
                            }
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Category + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + category + '</td></tr>';
                        break;
                    case "ReferenceNumber":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReferenceNumber + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().referenceNumber || '') + '</td></tr>';
                        break;
                    case "Subject":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Subject + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().subject || '') + '</td></tr>';
                        break;
                    case "From":
                        var from = "";
                        if (row.data().fromStructure)
                        {
                            from += row.data().fromStructure;
                        }
                        if (row.data().fromUser)
                        {
                            var user = row.data().fromUser;
                            from += from !== "" ? "/" + user : user;
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.From + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + from + '</td></tr>';
                        break;
                    case "TransferDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.TransferDate + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().transferDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "Owner":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Owner + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().lockedBy || '') + '</td></tr>';
                        break;
                    case "SendingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.SendingEntity + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().sendingEntity || '') + '</td></tr>';
                        break;
                    case "ReceivingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReceivingEntity + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().receivingEntity || '') + '</td></tr>';
                        break;
                    case "Purpose":
                        var purpose = "";
                        var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                        for (let i = 0; i < purposes.length; i++)
                        {
                            if (purposes[i].id === row.data().purposeId)
                            {
                                purpose = purposes[i].text;
                            }
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Purpose + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + purpose + '</td></tr>';
                        break;
                    case "Priority":
                        var priority = "";
                        var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                        for (let i = 0; i < priorities.length; i++)
                        {
                            if (priorities[i].id === row.data().priorityId)
                            {
                                priority = priorities[i].text;
                            }
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Priority + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + priority + '</td></tr>';
                        break;
                    case "Privacy":
                        var privacy = "";
                        var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                        for (let i = 0; i < privacies.length; i++)
                        {
                            if (privacies[i].id === row.data().privacyId)
                            {
                                privacy = privacies[i].text;
                            }
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Privacy + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + privacy + '</td></tr>';
                        break;
                    case "CreatedBy":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.CreatedBy + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().createdByUser || '') + '</td></tr>';
                        break;
                    case "ClosedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ClosedDate + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().closedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';
                        break;
                }
            }
        }
    }
    return html;
}
var gTableName = "grdg2gItems";
var gLocked = false;
class Documentg2gView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "g2g", model);
        this.statuses = model.statuses;
        this.viewName = model.viewName;
        this.nodeName = model.nodeName;
    }
    render()
    {
        var self = this;
        var model = this.model;
        document.getElementById("nodeName").innerText =model.nodeName;
        $.fn.select2.defaults.set("theme", "bootstrap");
        buildFilters(self.model.nodeId, self.model.categories);
        Common.gridCommon();
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName);
        var columns = [{ visible: buttons.length > 0, title: '<input id="chkAll" type="checkbox" />', width: '16px', "orderable": false, "render": function (data, type, row) { return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />"; } },
            { title: "Id", data: "id", visible: false, "orderable": false }];
        buildColumns(columns, self.model.nodeId, self)
        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta)
            {
                if (self.model.viewName != "G2G_DocumentInboxIncomingRejected" && self.model.viewName != "G2G_DocumentInboxIncomingRecalled") {
                    let btnView = document.createElement("button");
                    btnView.setAttribute("class", "btn btn-xs btn-warning view");
                    btnView.setAttribute("title", Resources.View);
                    btnView.setAttribute("clickattr", "openDocument(" + full.documentId + "," + model.delegationId + ", " + self.model.nodeId + ")");
                    btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                    return btnView.outerHTML;
                }
                else {
                    return null;
                }
            }
        });
        SecurityMatrix.getRowActions(securityMatrix, columns, self.model.nodeId);
        var table = $("#" + gTableName)
            .on('draw.dt', function ()
            {
                $('#' + gTableName + ' tbody tr td').each(function ()
                {
                    this.setAttribute('title', $(this).text());
                });
                GridCommon.CheckSelectedRows(gTableName);
            })
            .DataTable({
                "createdRow": function (row, data, dataIndex)
                {
                    var color = "";
                    var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                    for (var i = 0; i < priorities.length; i++)
                    {
                        if (priorities[i].id === data.priorityId)
                        {
                            color = priorities[i].color;
                        }
                    }
                    if (color !== "")
                    {
                        $(row).attr('style', "color:" + color);
                    }
                },
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/G2G/List",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d)
                    {
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.NodeId = self.model.nodeId;
                        d.ViewName = self.model.viewName;
                        d.DelegationId = model.delegationId;
                        d.PriorityId = $("#cmbFilterCompletedPriority").val() !== null && typeof $("#cmbFilterCompletedPriority").val() !== "undefined" ? $("#cmbFilterCompletedPriority").val() : "0";
                        d.PrivacyId = $("#cmbFilterCompletedPrivacy").val() !== null && typeof $("#cmbFilterCompletedPrivacy").val() !== "undefined" ? $("#cmbFilterCompletedPrivacy").val() : "0";
                        d.PurposeId = $("#cmbFilterCompletedPurpose").val() !== null && typeof $("#cmbFilterCompletedPurpose").val() !== "undefined" ? $("#cmbFilterCompletedPurpose").val() : "0";
                        d.CategoryId = $("#cmbFilterCompletedCategory").val() !== null && typeof $("#cmbFilterCompletedCategory").val() !== "undefined" ? $("#cmbFilterCompletedCategory").val() : "0";
                        d.ReferenceNumber = $("#txtFilterCompletedReferenceNumber").val() !== "" && typeof $("#txtFilterCompletedReferenceNumber").val() !== "undefined" ? $("#txtFilterCompletedReferenceNumber").val() : "";
                        d.Subject = $("#txtFilterCompletedSubject").val() !== "" && typeof $("#txtFilterCompletedSubject").val() !== "undefined" ? $("#txtFilterCompletedSubject").val() : "";
                        d.FromDate = $("#filterCompletedFromDate").val() !== "" && typeof $("#filterCompletedFromDate").val() !== "undefined" ? $("#filterCompletedFromDate").val() : "";
                        d.ToDate = $("#filterCompletedToDate").val() !== "" && typeof $("#filterCompletedToDate").val() !== "undefined" ? $("#filterCompletedToDate").val() : "";
                        d.StructureIds = $("#cmbFilterCompletedStructure").val() !== null && typeof $("#cmbFilterCompletedStructure").val() !== "undefined" ? $("#cmbFilterCompletedStructure").val() : [];
                        d.UserIds = $("#cmbFilterCompletedUser").val() !== null && typeof $("#cmbFilterCompletedUser").val() !== "undefined" ? $("#cmbFilterCompletedUser").val() : [];
                        d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val()
                        return d;
                    }
                },
                "order": [],
                "columns": columns,
                "fnInitComplete": function (settings, json)
                {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons"B>ltrpi',
                buttons: buttons
            });
        //GridCommon.AddCheckBoxEvents(gTableName);
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        $('#' + gTableName + ' tbody').on('click', 'td.details-control', function ()
        {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown())
            {
                row.child.hide();
                tr.removeClass('shown');
            }
            else
            {
                row.child(format(row, model.nodeId)).show();
                tr.addClass('shown');
            }
        });
        $('#' + gTableName + ' tbody').on('click', ".view", function ()
        {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#' + gTableName + ' tbody').on('dblclick', 'tr', function ()
        {
            if (!gLocked)
            {
                gLocked = true;
                try
                {
                    var onclick = $(this).find(".view").attr("clickattr");
                    eval(onclick);
                } catch (e)
                {
                    gLocked = false;
                }
            }
        });
        SecurityMatrix.InitToolbarColor();
        SecurityMatrix.InitContextMenu(securityMatrix, self.model.nodeId);
        $('#filtersContainer input').off('keydown', function (e) { });
        $('#filtersContainer input').on('keydown', function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                $("#btnFilterCompletedSearch").trigger('click');
            }
        });
    }
}
export default { Documentg2g, Documentg2gView };