﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<NoWin32Manifest>true</NoWin32Manifest>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
		<PackageId>Intalio.CTS</PackageId>
		<Authors>Intalio</Authors>
		<Company>Intalio</Company>
		<Product>Intalio.CTS</Product>
		<Version>4.0.0</Version>
		<LangVersion>8.0</LangVersion>
    <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
	</PropertyGroup>

	<PropertyGroup>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<RootNamespace>Intalio.CTS</RootNamespace>
		<AssemblyName>Intalio.CTS</AssemblyName>
		<AssemblyVersion>4.0.0.0</AssemblyVersion>
		<UserSecretsId>6703714d-030e-4ca6-aaa4-cb0ab623e3ea</UserSecretsId>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>C:\Workspace\CTS\Intalio.CTS\Intalio.CTS.xml</DocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		<Content Remove="logs\**" />
		<EmbeddedResource Remove="logs\**" />
		<None Remove="logs\**" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Remove="Controllers\DashboardAssignedStructures.cs" />
	</ItemGroup>

	<ItemGroup>
		<None Include="wwwroot\*" />
		<Content Remove="web.config" />
		<Content Remove="wwwroot\dist-versioned-files\*" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AspNetCore.HealthChecks.Elasticsearch" Version="6.0.2" />
		<PackageReference Include="AspNetCore.HealthChecks.Network" Version="6.0.4" />
		<PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="6.0.2" />
		<PackageReference Include="AspNetCore.HealthChecks.Oracle" Version="6.0.3" />
		<PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="6.0.2" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="6.0.5" />
		<PackageReference Include="AspNetCore.HealthChecks.Uris" Version="6.0.3" />
		<PackageReference Include="BuildBundlerMinifier" Version="3.2.449" />
		<PackageReference Include="Cronos" Version="0.7.1" />
		<PackageReference Include="Dapper" Version="2.0.123" />
		<PackageReference Include="Dapper.Oracle" Version="2.0.3" />
		<PackageReference Include="IdentityModel" Version="6.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.8" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="6.0.8" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="6.0.8" />
		<PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.8.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.8">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
		<PackageReference Include="Novell.Directory.Ldap.NETStandard" Version="3.6.0" />
		<PackageReference Include="Npgsql" Version="6.0.11" />
		<PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="6.0.3" />
		<PackageReference Include="Serilog" Version="4.2.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="6.1.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.4.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.4.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.4.0" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.3" />
	</ItemGroup>

	<ItemGroup>
		<None Include="bundleconfig.json" />
		<None Include="web.config" />
		<None Include="wwwroot\templates\action.handlebars" />
		<None Include="wwwroot\templates\activitylog.handlebars" />
		<None Include="wwwroot\templates\activityloginfo.handlebars" />
		<None Include="wwwroot\templates\activitylogtimeline.handlebars" />
		<None Include="wwwroot\templates\addBasketcorrespondenceindex.handlebars" />
		<None Include="wwwroot\templates\addBasketindex.handlebars" />
		<None Include="wwwroot\templates\addccandsend.handlebars" />
		<None Include="wwwroot\templates\addressbook.handlebars" />
		<None Include="wwwroot\templates\addressbookindex.handlebars" />
		<None Include="wwwroot\templates\adminlog.handlebars" />
		<None Include="wwwroot\templates\adminLogInfoView.handlebars" />
		<None Include="wwwroot\templates\advancesearch.handlebars" />
		<None Include="wwwroot\templates\advancesearchconfiguration.handlebars" />
		<None Include="wwwroot\templates\advancesearchconfigurationcolumns.handlebars" />
		<None Include="wwwroot\templates\advancesearchconfigurationtranslation.handlebars" />
		<None Include="wwwroot\templates\Agendacorrespondencedocument.handlebars" />
		<None Include="wwwroot\templates\agendaresolution.handlebars" />
		<None Include="wwwroot\templates\AgendaTopicsList.handlebars" />
		<None Include="wwwroot\templates\assignee.handlebars" />
		<None Include="wwwroot\templates\assigneeindex.handlebars" />
		<None Include="wwwroot\templates\attachment.handlebars" />
		<None Include="wwwroot\templates\attachmentfolder.handlebars" />
		<None Include="wwwroot\templates\attachmentfolderindex.handlebars" />
		<None Include="wwwroot\templates\attachmentindex.handlebars" />
		<None Include="wwwroot\templates\attachmentproperties.handlebars" />
		<None Include="wwwroot\templates\attachmentpropertiesindex.handlebars" />
		<None Include="wwwroot\templates\attachmentpropertiestranslation.handlebars" />
		<None Include="wwwroot\templates\attachmentsecurity.handlebars" />
		<None Include="wwwroot\templates\attachmentsreplybycategory.handlebars" />
		<None Include="wwwroot\templates\attachmentuserindex.handlebars" />
		<None Include="wwwroot\templates\audittrailvalues.handlebars" />
		<None Include="wwwroot\templates\autoforward.handlebars" />
		<None Include="wwwroot\templates\barcodeconfiguration.handlebars" />
		<None Include="wwwroot\templates\barcodeconfigurationindex.handlebars" />
		<None Include="wwwroot\templates\barcodeindex.handlebars" />
		<None Include="wwwroot\templates\basket.handlebars" />
		<None Include="wwwroot\templates\basketFromNode.handlebars" />
		<None Include="wwwroot\templates\bookmark.handlebars" />
		<None Include="wwwroot\templates\bookmarkindex.handlebars" />
		<None Include="wwwroot\templates\byfile.handlebars" />
		<None Include="wwwroot\templates\byscan.handlebars" />
		<None Include="wwwroot\templates\CabinetTree.handlebars" />
		<None Include="wwwroot\templates\categorybasicattribute.handlebars" />
		<None Include="wwwroot\templates\categorybasicindex.handlebars" />
		<None Include="wwwroot\templates\categoryimport.handlebars" />
		<None Include="wwwroot\templates\categoryindex.handlebars" />
		<None Include="wwwroot\templates\categorymanagement.handlebars" />
		<None Include="wwwroot\templates\categoryreferencecounter.handlebars" />
		<None Include="wwwroot\templates\categoryreferencecounterindex.handlebars" />
		<None Include="wwwroot\templates\categoryreferencenumber.handlebars" />
		<None Include="wwwroot\templates\categoryreferencenumberindex.handlebars" />
		<None Include="wwwroot\templates\categoryreply.handlebars" />
		<None Include="wwwroot\templates\categorysearchindex.handlebars" />
		<None Include="wwwroot\templates\categorysearchtranslation.handlebars" />
		<None Include="wwwroot\templates\closed.handlebars" />
		<None Include="wwwroot\templates\committee.handlebars" />
		<None Include="wwwroot\templates\committeeindex.handlebars" />
		<None Include="wwwroot\templates\completed.handlebars" />
		<None Include="wwwroot\templates\copyOptions.handlebars" />
		<None Include="wwwroot\templates\createbyfile.handlebars" />
		<None Include="wwwroot\templates\createbytemplate.handlebars" />
		<None Include="wwwroot\templates\ctsaddressbook.handlebars" />
		<None Include="wwwroot\templates\ctsaddressbookFromCTS.handlebars" />
		<None Include="wwwroot\templates\ctsaddressbooknonstructuresender.handlebars" />
		<None Include="wwwroot\templates\ctsaddressbookwithoutsendingrules.handlebars" />
		<None Include="wwwroot\templates\ctsaddressbookwithsendingrules.handlebars" />
		<None Include="wwwroot\templates\ctscoreaddressbookindex.handlebars" />
		<None Include="wwwroot\templates\ctscoreAssignedStructuresaddressbookindex.handlebars" />
		<None Include="wwwroot\templates\ctscoreDashboardAssignedStructures.handlebars" />
		<None Include="wwwroot\templates\ctscorenotificationtemplate.handlebars" />
		<None Include="wwwroot\templates\ctscoreorganizationmanagement.handlebars" />
		<None Include="wwwroot\templates\ctscoresearchassignedstructures.handlebars" />
		<None Include="wwwroot\templates\ctscoresendingrules.handlebars" />
		<None Include="wwwroot\templates\customattributeindex.handlebars" />
		<None Include="wwwroot\templates\customattributetranslation.handlebars" />
		<None Include="wwwroot\templates\customizeNodeColomns.handlebars" />
		<None Include="wwwroot\templates\dashboardcountpercategoryandstatus.handlebars" />
		<None Include="wwwroot\templates\dashboardpercentageofcorrespondencescompletedvsinprogress.handlebars" />
		<None Include="wwwroot\templates\dashboardstatisticsforcompletedcorrespondences.handlebars" />
		<None Include="wwwroot\templates\dashboardstatisticsforcompletedtransfers.handlebars" />
		<None Include="wwwroot\templates\dashboardstatisticsforinprogresscorrespondences.handlebars" />
		<None Include="wwwroot\templates\dashboardstatisticsforinprogresstransfers.handlebars" />
		<None Include="wwwroot\templates\dashboardsystem.handlebars" />
		<None Include="wwwroot\templates\dashboardtransferaveragecompletiontime.handlebars" />
		<None Include="wwwroot\templates\dashboarduser.handlebars" />
		<None Include="wwwroot\templates\delegation.handlebars" />
		<None Include="wwwroot\templates\designatedPerson.handlebars" />
		<None Include="wwwroot\templates\distributionindex.handlebars" />
		<None Include="wwwroot\templates\distributionlist.handlebars" />
		<None Include="wwwroot\templates\document.handlebars" />
		<None Include="wwwroot\templates\documentbarcode.handlebars" />
		<None Include="wwwroot\templates\documentcomplete.handlebars" />
		<None Include="wwwroot\templates\documentdetails.handlebars" />
		<None Include="wwwroot\templates\documentdetailswithviewer.handlebars" />
		<None Include="wwwroot\templates\draft.handlebars" />
		<None Include="wwwroot\templates\duplicatesignatureregion.handlebars" />
		<None Include="wwwroot\templates\editsecureuserid.handlebars" />
		<None Include="wwwroot\templates\emailAttachmentModal.handlebars" />
		<None Include="wwwroot\templates\entitygroup.handlebars" />
		<None Include="wwwroot\templates\entitygroupbook.handlebars" />
		<None Include="wwwroot\templates\entitygroupindex.handlebars" />
		<None Include="wwwroot\templates\eventindex.handlebars" />
		<None Include="wwwroot\templates\eventList.handlebars" />
		<None Include="wwwroot\templates\eventreceiverindex.handlebars" />
		<None Include="wwwroot\templates\exceptionList.handlebars" />
		<None Include="wwwroot\templates\export.handlebars" />
		<None Include="wwwroot\templates\exportedDocuments.handlebars" />
		<None Include="wwwroot\templates\exportOptions.handlebars" />
		<None Include="wwwroot\templates\favoriteStructures.handlebars" />
		<None Include="wwwroot\templates\filingplan.handlebars" />
		<None Include="wwwroot\templates\filingplanindex.handlebars" />
		<None Include="wwwroot\templates\followUp.handlebars" />
		<None Include="wwwroot\templates\followUpAttachments.handlebars" />
		<None Include="wwwroot\templates\followUpDetails.handlebars" />
		<None Include="wwwroot\templates\followUpIndex.handlebars" />
		<None Include="wwwroot\templates\followuporiginaldocument.handlebars" />
		<None Include="wwwroot\templates\followUpPanel.handlebars" />
		<None Include="wwwroot\templates\followUpPanelIndex.handlebars" />
		<None Include="wwwroot\templates\followUpPanelItems.handlebars" />
		<None Include="wwwroot\templates\followUpPostponeindex.handlebars" />
		<None Include="wwwroot\templates\followUpsecurityindex.handlebars" />
		<None Include="wwwroot\templates\followUpUsers.handlebars" />
		<None Include="wwwroot\templates\fromToDate.handlebars" />
		<None Include="wwwroot\templates\g2g.handlebars" />
		<None Include="wwwroot\templates\g2gexport.handlebars" />
		<None Include="wwwroot\templates\generateletter.handlebars" />
		<None Include="wwwroot\templates\home.handlebars" />
		<None Include="wwwroot\templates\import.handlebars" />
		<None Include="wwwroot\templates\inbox.handlebars" />
		<None Include="wwwroot\templates\instructionindex.handlebars" />
		<None Include="wwwroot\templates\instructionList.handlebars" />
		<None Include="wwwroot\templates\kpi.handlebars" />
		<None Include="wwwroot\templates\linkedcorrespondence.handlebars" />
		<None Include="wwwroot\templates\linkedcorrespondencedocument.handlebars" />
		<None Include="wwwroot\templates\linkedcorrespondenceindex.handlebars" />
		<None Include="wwwroot\templates\listallstructureusers.handlebars" />
		<None Include="wwwroot\templates\listsecureusers.handlebars" />
		<None Include="wwwroot\templates\liststructureusers.handlebars" />
		<None Include="wwwroot\templates\managecategory.handlebars" />
		<None Include="wwwroot\templates\managecorrespondence.handlebars" />
		<None Include="wwwroot\templates\manageDepartmentUsers.handlebars" />
		<None Include="wwwroot\templates\manageFollowUp.handlebars" />
		<None Include="wwwroot\templates\manageStructureUsersCorrespondences.handlebars" />
		<None Include="wwwroot\templates\meetingagendaindex.handlebars" />
		<None Include="wwwroot\templates\movetransfers.handlebars" />
		<None Include="wwwroot\templates\multipleupload.handlebars" />
		<None Include="wwwroot\templates\myFollowUp.handlebars" />
		<None Include="wwwroot\templates\myrequests.handlebars" />
		<None Include="wwwroot\templates\mytransfer.handlebars" />
		<None Include="wwwroot\templates\node.handlebars" />
		<None Include="wwwroot\templates\nodeindex.handlebars" />
		<None Include="wwwroot\templates\nodelist.handlebars" />
		<None Include="wwwroot\templates\nonarchivedattachments.handlebars" />
		<None Include="wwwroot\templates\nonarchivedattachmentsindex.handlebars" />
		<None Include="wwwroot\templates\nonarchivedattachmentstypes.handlebars" />
		<None Include="wwwroot\templates\nonarchivedattachmentstypesindex.handlebars" />
		<None Include="wwwroot\templates\note.handlebars" />
		<None Include="wwwroot\templates\noteComponent.handlebars" />
		<None Include="wwwroot\templates\noteindex.handlebars" />
		<None Include="wwwroot\templates\OMAddNewOrExistUser.handlebars" />
		<None Include="wwwroot\templates\OMDepartmentUserMetaData.handlebars" />
		<None Include="wwwroot\templates\OrganiztionManagementDepartmentUser.handlebars" />
		<None Include="wwwroot\templates\outgoingIncomingRequests.handlebars" />
		<None Include="wwwroot\templates\parameter.handlebars" />
		<None Include="wwwroot\templates\parameterindex.handlebars" />
		<None Include="wwwroot\templates\previewbeforesign.handlebars" />
		<None Include="wwwroot\templates\privacy.handlebars" />
		<None Include="wwwroot\templates\profile.handlebars" />
		<None Include="wwwroot\templates\purpose.handlebars" />
		<None Include="wwwroot\templates\reassign.handlebars" />
		<None Include="wwwroot\templates\rejectedDocuments.handlebars" />
		<None Include="wwwroot\templates\reportattachmentdetail.handlebars" />
		<None Include="wwwroot\templates\reportattachmentdetailexport.handlebars" />
		<None Include="wwwroot\templates\reportcompletedcorrespondences.handlebars" />
		<None Include="wwwroot\templates\reportcompletedtransfers.handlebars" />
		<None Include="wwwroot\templates\reportcorrespondencedetail.handlebars" />
		<None Include="wwwroot\templates\reportcorrespondencedetailexport.handlebars" />
		<None Include="wwwroot\templates\reportcorrespondencedetailfilter.handlebars" />
		<None Include="wwwroot\templates\reportinprogresscorrespondences.handlebars" />
		<None Include="wwwroot\templates\reportinprogresstransfers.handlebars" />
		<None Include="wwwroot\templates\reportoperationbycorrespondence.handlebars" />
		<None Include="wwwroot\templates\reportoperationbyuser.handlebars" />
		<None Include="wwwroot\templates\reportOutgoingFromDepartment.handlebars" />
		<None Include="wwwroot\templates\reportstatisticalcorrespondences.handlebars" />
		<None Include="wwwroot\templates\reporttransferssenttostructure.handlebars" />
		<None Include="wwwroot\templates\role.handlebars" />
		<None Include="wwwroot\templates\scanconfiguration.handlebars" />
		<None Include="wwwroot\templates\scanconfigurationindex.handlebars" />
		<None Include="wwwroot\templates\search.handlebars" />
		<None Include="wwwroot\templates\searchlinkeddocumnet.handlebars" />
		<None Include="wwwroot\templates\SelectExistUser.handlebars" />
		<None Include="wwwroot\templates\sendtoreceivingentity.handlebars" />
		<None Include="wwwroot\templates\sent.handlebars" />
		<None Include="wwwroot\templates\signatureTemplates.handlebars" />
		<None Include="wwwroot\templates\systemdelegation.handlebars" />
		<None Include="wwwroot\templates\tab.handlebars" />
		<None Include="wwwroot\templates\taskEmailReminder.handlebars" />
		<None Include="wwwroot\templates\tasksearch.handlebars" />
		<None Include="wwwroot\templates\templateindex.handlebars" />
		<None Include="wwwroot\templates\templatelist.handlebars" />
		<None Include="wwwroot\templates\templatesmanagement.handlebars" />
		<None Include="wwwroot\templates\transfer.handlebars" />
		<None Include="wwwroot\templates\transferhistory.handlebars" />
		<None Include="wwwroot\templates\transferinstruction.handlebars" />
		<None Include="wwwroot\templates\translatordictionary.handlebars" />
		<None Include="wwwroot\templates\translatordictionaryindex.handlebars" />
		<None Include="wwwroot\templates\treenodeactions.handlebars" />
		<None Include="wwwroot\templates\user.handlebars" />
		<None Include="wwwroot\templates\userNodeIndex.handlebars" />
		<None Include="wwwroot\templates\userNodeList.handlebars" />
		<None Include="wwwroot\templates\userNodeListCorrespondance.handlebars" />
		<None Include="wwwroot\templates\versionhistory.handlebars" />
		<None Include="wwwroot\templates\vipclosed.handlebars" />
		<None Include="wwwroot\templates\vipcompleted.handlebars" />
		<None Include="wwwroot\templates\vipdocumentdetails.handlebars" />
		<None Include="wwwroot\templates\vipdraft.handlebars" />
		<None Include="wwwroot\templates\vipinbox.handlebars" />
		<None Include="wwwroot\templates\vipmyrequests.handlebars" />
		<None Include="wwwroot\templates\vipsent.handlebars" />
		<None Include="wwwroot\templates\vipUserNodeListCorrespondance.handlebars" />
		<None Include="wwwroot\templates\visualtracking.handlebars" />
		<None Include="wwwroot\templates\visualtrackingmodal.handlebars" />
		<None Include="wwwroot\templates\widget.handlebars" />
		<None Include="wwwroot\templates\workflow.handlebars" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Middlewares\" />
		<Folder Include="Properties\PublishProfiles\" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Intalio.Core.UI\Intalio.Core.UI.csproj" />
		<ProjectReference Include="..\Intalio.Core\Intalio.Core.csproj" />
		<ProjectReference Include="..\Intalio.CTS.Core\Intalio.CTS.Core.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Reference Include="Hangfire.AspNetCore">
			<HintPath>..\Packages\Hangfire.AspNetCore.dll</HintPath>
		</Reference>
		<Reference Include="Hangfire.Core">
			<HintPath>..\Packages\Hangfire.Core.dll</HintPath>
		</Reference>
		<Reference Include="Hangfire.Oracle.Core">
			<HintPath>..\Packages\Hangfire.Oracle.Core.dll</HintPath>
		</Reference>
		<Reference Include="Hangfire.PostgreSql">
			<HintPath>..\Packages\Hangfire.PostgreSql.dll</HintPath>
		</Reference>
		<Reference Include="Hangfire.SqlServer">
			<HintPath>..\Packages\Hangfire.SqlServer.dll</HintPath>
		</Reference>
		<Reference Include="HealthChecks.Hangfire">
			<HintPath>..\Packages\HealthChecks.Hangfire.dll</HintPath>
		</Reference>
		<Reference Include="Intalio.IAM.Core">
		  <HintPath>..\Packages\Intalio.IAM.Core.dll</HintPath>
		</Reference>
    <Reference Include="Hangfire.Console">
      <HintPath>..\Packages\Hangfire.Console.dll</HintPath>
    </Reference>
	</ItemGroup>
	<ItemGroup>
		<None Update="Intalio.Core.UI.xml">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>
	<ItemGroup>
		<Content Include="../Packages/ar/Hangfire.Core.resources.dll" CopyToOutputDirectory="Always" />
	</ItemGroup>
	<ItemGroup>
		<Content Include="../Packages/fr/Hangfire.Core.resources.dll" CopyToOutputDirectory="Always" />
	</ItemGroup>
	<ProjectExtensions>
		<VisualStudio>
			<UserProperties />
		</VisualStudio>
	</ProjectExtensions>
	<Target Name="PreBuild" BeforeTargets="PreBuildEvent">
		<Exec Command="npx handlebars &quot;$(ProjectDir)wwwroot\templates&quot; -f &quot;$(ProjectDir)wwwroot\js\templates.min.js&quot;" />
	</Target>
	<Target Name="RunGulpBeforeBuild" BeforeTargets="Publish">
		<Exec Command="npx gulp version-js-imports --publishDir=&quot;$(PublishDir)wwwroot&quot;" />
	</Target>


	<!--<Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="handlebars &quot;$(ProjectDir)wwwroot\templates&quot; -f &quot;$(ProjectDir)wwwroot\js\templates.min.js&quot;" />
  </Target>-->
</Project>
