﻿import Intalio from './common.js'
import DocumentDetails from './documentDetails.js'
import { IdentityService, Categories, CategoryModel, DelegationUsers, Helper, Nodes } from './lookup.js'
import SendTransferModal from './sendTransfer.js'
import ReportCorrespondenceDetailExport from './reportCorrespondenceDetailExport.js'
import SendToReceivingEntityIndex from './sendToReceivingEntity.js'
import Transfer from './transfer.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'

class UserNodeDocument extends Intalio.Model {
    constructor() {
        super();
        this.nodeId = null;
        this.delegationId = null;
        this.categories = null;
        this.statuses = null;
        this.priorities = null;
        this.privacies = null;
    }
}


function completeTransfer(ids, model) {
    Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
    Common.ajaxPost('/Transfer/Complete',
        {
            'ids': ids, 'delegationId': model.delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        function (result) {
            if (result != null && result.length > 0) {
                swal.close()
                let msg = "";
                for (var i = 0; i < result.length; i++) {
                    if (!result[i].updated) {
                        msg += "\n ○ " + result[i].uncompletedDocumentReferenceNumber;
                    }
                }
                if (msg !== "") {
                    setTimeout(function () {
                        Common.alertMsg(Resources.CannotCompleteWarning + msg);
                    }, 300);
                    Common.unmask("inboxListContainer-mask");
                    GridCommon.Refresh(gTableName);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                }
                else {
                    Common.unmask("inboxListContainer-mask");
                    Common.showScreenSuccessMsg();
                    GridCommon.Refresh(gTableName);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                }
            } else {
                Common.showScreenErrorMsg();
            }
        }, null, false);
}

function requestToCompleteTask(ids, model) {
    Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
    Common.ajaxPost('/Transfer/RequestToComplete',
        {
            'ids': ids, 'delegationId': model.delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        function (result) {
            if (result != null && result.length > 0) {
                swal.close()
                let msg = "";
                for (var i = 0; i < result.length; i++) {
                    if (!result[i].updated) {
                        msg += "\n ○ " + result[i].documentId + " " + result[i].message;
                    }
                }
                if (msg !== "") {
                    setTimeout(function () {
                        Common.alertMsg(Resources.CannotCompleteWarning + msg);
                    }, 300);
                    Common.unmask("inboxListContainer-mask");
                    GridCommon.Refresh(gTableName);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                }
                else {
                    Common.unmask("inboxListContainer-mask");
                    Common.showScreenSuccessMsg();
                    GridCommon.Refresh(gTableName);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                }
            } else {
                Common.showScreenErrorMsg();
            }
        }, null, false);
}

function dismissCarbonCopy(dismissIds, model, allSelectedData) {
    Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
    Common.ajaxPost('/Transfer/DismissCarbonCopy',
        {
            'ids': dismissIds, 'delegationId': model.delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        function (result) {
            if (result != null && result.length > 0) {
                swal.close()
                let msg = "";
                for (var i = 0; i < result.length; i++) {
                    if (!result[i].updated) {
                        var transfer = $.grep(allSelectedData, function (e) {
                            return e.id === result[i].transferId;
                        });
                        if (transfer[0]) {
                            msg += "\n ○ " + transfer[0].referenceNumber;

                        } else {
                            msg += "\n ○ " + result[i].transferId;
                        }
                    }
                }
                if (msg !== "") {
                    setTimeout(function () {
                        Common.alertMsg(Resources.CannotDismissCarbonCopyWarning + msg);
                    }, 300);
                    Common.unmask("inboxListContainer-mask");
                    GridCommon.Refresh(gTableName);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                } else {
                    Common.unmask("inboxListContainer-mask");
                    Common.showScreenSuccessMsg();
                    GridCommon.Refresh(gTableName);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                }
            } else {
                Common.showScreenErrorMsg();
            }
        }, null, false);
}
function format(row, nodeId) {
    return '<table style="width:100%" cellspacing="0" border="0">' + buildColumnsDetails(row, nodeId) + '</table>';
}
function unlock(row, id, delegationId, nodeId) {
    gLocked = true;
    Common.showConfirmMsg(Resources.UnlockConfirmation, function () {
        Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
        var params = { "Id": id, "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
        if (delegationId !== null) {
            params.DelegationId = delegationId;
        }
        Common.ajaxPost('/Transfer/UnLock', params, function (response) {
            gLocked = false;
            if (response === "False") {
                Common.showScreenErrorMsg();
                Common.unmask("inboxListContainer-mask");
            }
            else if (response === "FileInUse") {
                setTimeout(function () {
                    Common.alertMsg(Resources.FileInUse);
                }, 300);
                Common.unmask("inboxListContainer-mask");
            } else {
                var onclick = $(row).parent().parent().parent().find(".edit").attr("clickattr").replace("true", "false");
                $(row).parent().parent().parent().find(".edit").attr("clickattr", onclick);
                let btnView = document.createElement("button");
                btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                btnView.setAttribute("title", Resources.View);
                btnView.setAttribute("clickattr", "openDocumentViewMode(" + id + ",true," + delegationId + "," + false + ", " + nodeId + ")");
                btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                $(row).parent().parent().parent().find(".edit").parent().prepend(btnView.outerHTML);
                $(row).remove();
                Common.unmask("inboxListContainer-mask");
                //$("#divLock" + id).empty();
            }
        }, function () {
            gLocked = false; Common.showScreenErrorMsg();
            Common.unmask("inboxListContainer-mask");
        });
    });
}
function openDocumentViewMode(id, isRead, delegationId, isCced, nodeId) {
    gLocked = false;
    var params = { "Id": id, "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
    if (delegationId !== null) {
        params.DelegationId = delegationId;
    }
    if (!isRead && !isCced) {
        Common.showConfirmMsg(Resources.KindlyNoteMarkedRead + "\n" + Resources.OpenConfirmation, function () {
            Common.ajaxPost('/Transfer/View', params, function () {
                GridCommon.RefreshCurrentPage("grdInboxItems", true);
                openDocumentDetails(id, delegationId, true, nodeId);
            });
        });
    } else if (isCced) {
        Common.ajaxPost('/Transfer/View', params, function () {
            GridCommon.RefreshCurrentPage("grdInboxItems", true);
            openDocumentDetails(id, delegationId, true, nodeId, isCced);
        });
    } else {
        openDocumentDetails(id, delegationId, true, nodeId);
    }
}
function openDocumentEditMode(id, lockedByMe, sentToUser, delegationId, nodeId) {
    gLocked = false;
    var params = { "Id": id, "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
    if (delegationId !== null) {
        params.DelegationId = delegationId;
    }
    if (!lockedByMe && !sentToUser) {
        Common.showConfirmMsg(Resources.LockConfirmation, function () {
            Common.ajaxPost('/Transfer/Lock', params, function (response) {
                if (typeof response !== "boolean") {
                    var titleLock = Resources.LockedBy + ": " + response + "</br>" + Resources.LockedDate + ": ";
                    var html = "<div class='mr-sm text-left' title=''><i class='fa fa-lock fa-lg text-danger infoDivIcon' title='" + titleLock + "'></i><div class='infoDiv font-13' style='opacity: 0;'>" +
                        titleLock + "</div></div>&nbsp;";
                    $("#divLock" + id).append(html);
                    setTimeout(function () {
                        Common.showConfirmMsg(Resources.AlreadyLockedBy + ": " + response + "\n" + Resources.DoYouWantToOpen, function () {
                            GridCommon.RefreshCurrentPage("grdInboxItems", true);
                            openDocumentDetails(id, delegationId, false, nodeId);
                        }, function () {
                            GridCommon.RefreshCurrentPage("grdInboxItems", true);
                        });
                    }, 300);
                } else {
                    GridCommon.RefreshCurrentPage("grdInboxItems", true);
                    openDocumentDetails(id, delegationId, false, nodeId);
                }
            });
        });
    } else {
        Common.ajaxPost('/Transfer/View', params, function (response) {
            GridCommon.RefreshCurrentPage("grdInboxItems", true);
            openDocumentDetails(id, delegationId, false, nodeId);
        });
    }
}
function openDocumentDetails(id, delegationId, readOnly, nodeId, isCced) {

    gLocked = false;
    var params = { id: id };
    params.nodeId = nodeId;
    if (delegationId !== null) {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Transfer/GetTransferDetailsById', params, function (response) {
        let item = "liInbox" + nodeId;

        if (delegationId !== null) {
            item = "index-" + nodeId;
        }
        Common.setActiveSidebarMenu(item);
        $(".delegation").removeClass("active");
        var wrapper = $(".modal-documents");
        var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
        linkedCorrespondenceModel.reference = response.referenceNumber;
        linkedCorrespondenceModel.subject = response.subject;
        linkedCorrespondenceModel.documentId = response.documentId;
        var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
        linkedCorrespondenceDocument.render();

        var model = new DocumentDetails.DocumentDetails();
        model.categoryId = response.categoryId;
        model.delegationId = delegationId;
        model.id = id;
        model.documentId = response.documentId;
        model.referenceNumber = response.referenceNumber;
        model.categoryName = response.categoryName;
        model.statusId = response.status;
        model.createdByUser = response.createdByUser;
        model.readonly = readOnly === true;
        model.attachmentId = response.attachmentId;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.showMyTransfer = true;
        model.fromInbox = true;
        model.isCced = isCced;
        model.fromInbox = true;
        model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
        model.isModal = true;
        model.showBackButton = false;
        model.requestStatus = response.requestStatus;
        model.nodeId = nodeId;

        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));

        var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
        model.tabs = $.grep(tabs, function (element, index) {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
        });
        model.tabsWithStatic = tabs;
        model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;

        wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

        var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
        view.render();

        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
            $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
            if ($(this).data("remove") != true)
                return;
            $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
            swal.close();
            
        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

        $(document).off('click', '.btn-back');
        $(document).on('click', '.btn-back', function () {
            $("#gridContainerDiv").show();
            view.remove();
            $(".toRemove").remove();
        });

        $(document).off('click', '.btn-export');
        $(document).on('click', '.btn-export', function () {
            var wrapper = $(".modal-window");
            var model = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExport();
            model.documentId = response.id;
            model.delegationId = delegationId;
            var reportCorrespondenceDetailExportView = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExportView(wrapper, model);
            reportCorrespondenceDetailExportView.render();

            $("#modalReportCorrespondenceDetailExport").off("hidden.bs.modal");
            $("#modalReportCorrespondenceDetailExport").off("shown.bs.modal");
            $("#modalReportCorrespondenceDetailExport").on('shown.bs.modal', function () { });
            $("#modalReportCorrespondenceDetailExport").on('hidden.bs.modal', function () { });
            $("#modalReportCorrespondenceDetailExport").modal("show");

        });

    }, function () { Common.showScreenErrorMsg(); }, true);




}
function buildFilters(nodeId, categories) {
    //var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var nodes = new Nodes().getTreeUserNodes(window.language);
    var node = $.grep(nodes.children, function (element, index) {
        return element.id === nodeId;
    })[0];
    var filters = node.data.filters !== "" && node.data.filters !== null ? JSON.parse(node.data.filters) : [];

    if (filters.length > 0) {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++) {
            var filter = filters[i];
            switch (filter) {
                case "ReferenceNumber":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
                        '<input type="text" id="txtFilterInboxReferenceNumber" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "FromDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label>' +
                        '<div class="input-group date"><input id="filterInboxFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterInboxFromDateError">' +
                        '<span class="input-group-addon" id="filterInboxFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterInboxFromDateError"></div></div></div></div>';
                    break;
                case "ToDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label>' +
                        '<div class="input-group date"><input id="filterInboxToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterInboxToDateError">' +
                        '<span class="input-group-addon" id="filterInboxToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterInboxToDateError"></div></div></div></div>';
                    break;
                case "Category":
                    clearFix += 3;
                    var div = '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="categoryFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterInboxCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++) {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterInboxCategoryError"></div></div></div>';
                    html += div;
                    break;
                case "Subject":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterInboxSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Read":
                    clearFix += 2;
                    html += '<div class="col-lg-2 col-md-2 col-sm-6 col-xs-6"><div class="form-group"><label class="control-label"></label><div class="checkbox c-checkbox">' +
                        '<label><input tabindex="' + i + '" type="checkbox" id="chkFilterInboxRead" name="Read"><span class="fa fa-check"></span>' + Resources.Read + '</label></div></div></div>';
                    break;
                case "Locked":
                    clearFix += 2;
                    html += '<div class="col-lg-2 col-md-2 col-sm-6 col-xs-6"><div class="form-group"><label class="control-label"></label><div class="checkbox c-checkbox">' +
                        '<label><input tabindex="' + i + '" type="checkbox" id="chkFilterInboxLocked" name="Locked"><span class="fa fa-check"></span>' + Resources.Locked + '</label></div></div></div>';
                    break;
                case "OverDue":
                    clearFix += 2;
                    html += '<div class="col-lg-2 col-md-2 col-sm-6 col-xs-6"><div class="form-group"><label class="control-label"></label><div class="checkbox c-checkbox">' +
                        '<label><input tabindex="' + i + '" type="checkbox" id="chkFilterInboxOverdue" name="Overdue"><span class="fa fa-check"></span>' + Resources.OverDue + '</label></div></div></div>';
                    break;
                case "Purpose":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="purposeFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Purpose + '</label>' +
                        '<select id="cmbFilterInboxPurpose" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxPurposeError" class="form-control"></select></div></div>';
                    break;
                case "Priority":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="priorityFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Priority + '</label>' +
                        '<select id="cmbFilterInboxPriority" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxPriorityError" class="form-control"></select></div></div>';
                    break;
                case "Privacy":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="privacyFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Privacy + '</label>' +
                        '<select id="cmbFilterInboxPrivacy" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxPrivacyError" class="form-control"></select></div></div>';
                    break;
                case "Structure":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="structureFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Structure + '</label>' +
                        '<select id="cmbFilterInboxStructure" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxStructureError" class="form-control"></select></div></div>';
                    break;
                case "User":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="userFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.User + '</label>' +
                        '<select id="cmbFilterInboxUser" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxUserError" class="form-control"></select></div></div>';
                    break;
            }
            if (clearFix === 12 || clearFix === 11) {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 list-action-filter"><button id="btnFilterInboxSearch" tabindex="9" type="button" class="btn btn-primary">' + Resources.Search + '</button>' +
            ' <button id="btnFilterInboxClear" tabindex="10" type="button" class="btn btn-clear">' + Resources.Clear + '</button></div>';
        html += '</div>';
        $('#filtersContainer').html(html);

  
        let clickedSearch = true; // true means collapsed

        if (clickedSearch) {
            $('#collapseInboxIcon').html('<i class="fa fa-angle-down fa-lg"></i>');
            $('#collapseInboxPanel').attr('class', 'panel-body panel-collapse collapse'); // Collapsed
        } else {
            $('#collapseInboxIcon').html('<i class="fa fa-angle-up fa-lg"></i>');
            $('#collapseInboxPanel').attr('class', 'panel-body panel-collapse collapse in'); // Expanded
        }

        $('#collapseInboxIcon').on('click', function () {
            if (clickedSearch) {
                $('#collapseInboxIcon').html('<i class="fa fa-angle-up fa-lg"></i>');
                $('#collapseInboxPanel').attr('class', 'panel-body panel-collapse collapse in');
                clickedSearch = false;
            } else {
                $('#collapseInboxIcon').html('<i class="fa fa-angle-down fa-lg"></i>');
                $('#collapseInboxPanel').attr('class', 'panel-body panel-collapse collapse');
                clickedSearch = true;
            }
        });

        $("#btnFilterInboxSearch").on('click', function () {
            GridCommon.Refresh(gTableName);
        });
        $("#btnFilterInboxClear").on('click', function () {
            $("#cmbFilterInboxPurpose").val('').trigger('change');
            $("#cmbFilterInboxPriority").val('').trigger('change');
            $("#cmbFilterInboxPrivacy").val('').trigger('change');
            $("#cmbFilterInboxCategory").val('').trigger('change');
            $("#txtFilterInboxReferenceNumber").val('');
            $("#txtFilterInboxSubject").val('');
            fromDate.clear();
            toDate.clear();
            $("#chkFilterInboxRead").prop("checked", false);
            $("#chkFilterInboxLocked").prop("checked", false);
            $("#chkFilterInboxAssigned").prop("checked", false);
            $("#chkFilterInboxOverdue").prop("checked", false);
            $("#cmbFilterInboxStructure").val('').trigger('change');
            $("#cmbFilterInboxUser").val('').trigger('change');
            GridCommon.Refresh(gTableName);
        });

        $('#cmbFilterInboxPurpose').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Purposes().get(window.language),
            dropdownParent: $('#purposeFilterInboxContainer')
        });
        $("#cmbFilterInboxPurpose").val('').trigger('change');

        $('#cmbFilterInboxPriority').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Priorities().get(window.language),
            dropdownParent: $('#priorityFilterInboxContainer')
        });
        $("#cmbFilterInboxPriority").val('').trigger('change');

        $('#cmbFilterInboxPrivacy').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Privacies().get(window.language),
            dropdownParent: $('#privacyFilterInboxContainer')
        });
        $("#cmbFilterInboxPrivacy").val('').trigger('change');

        $('#cmbFilterInboxCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterInboxContainer')
        });
        $("#cmbFilterInboxCategory").val('').trigger('change');

        $('#cmbFilterInboxStructure').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#structureFilterInboxContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes',
                type: "POST",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term) {
                    return {
                        "text": term.term ? term.term : "", "language": window.language, "attributes": [window.StructureNameAr, window.StructureNameFr]
                    };
                },
                processResults: function (data) {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        var structureName = val.name;
                        if (val.attributes != null && val.attributes.length > 0) {
                            var attributeLang = $.grep(val.attributes, function (e) {
                                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                            });
                            if (attributeLang.length > 0) {
                                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                            }
                        }
                        var item = {};
                        item.id = val.id;
                        item.text = structureName;
                        item.isStructure = true;
                        item.dataId = val.id;
                        listitemsMultiList.push(item);
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        }).on("change", function () {
            if (document.getElementById('cmbFilterInboxUser') !== null) {
                var type = "GET";
                var url = '/api/SearchUsers';
                var structures = $('#cmbFilterInboxStructure').val();
                //if (structures !== "" && structures !== null)
                //{
                //    type = "POST";
                //    url = '/api/SearchUsersByStructureIds';
                //}
                $('#cmbFilterInboxUser').select2({
                    minimumInputLength: 0,
                    allowClear: false,
                    placeholder: "",
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    dropdownParent: $("#userFilterInboxContainer"),
                    multiple: true,
                    width: "100%",
                    ajax: {
                        delay: 400,
                        url: window.IdentityUrl + url,
                        type: type,
                        dataType: 'json',
                        headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                        data: function (term) {
                            var params = { "text": "", "language": window.language };
                            //var params = { "text": term.term ? term.term : "", "language": window.language };
                            //var structures = $('#cmbFilterInboxStructure').val();
                            //if (structures !== "" && structures !== null)
                            //{
                            //    params.ids = $('#cmbFilterInboxStructure').val();
                            //}
                            return params;
                        },
                        processResults: function (data, term) {
                            var structures = $('#cmbFilterInboxStructure').val();
                            var termSearch = term.term ? term.term : "";
                            var listitemsMultiList = [];
                            $.each(data, function (key, val) {
                                if (structures !== "" && structures !== null &&
                                    !((val.structureIds != null && val.structureIds.some(r => structures.indexOf(String(r)) >= 0)) ||
                                        structures.includes(val.defaultStructureId))) {
                                    return;
                                }
                                var fullName = val.fullName;
                                if (window.language != 'en') {
                                    fullName = getFullNameByLangauge(val);
                                    fullName = fullName.trim() == "" ? val.fullName : fullName
                                }
                                var allNames = getFullNameInAllLangauge(val);
                                if (allNames.length == 0) allNames.push(fullName);
                                if (termSearch != "" &&
                                    !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                                    return;
                                }
                                let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                                if (!isExist) {
                                    var item = {};
                                    item.id = val.id;
                                    item.text = fullName;
                                    item.isStructure = false;
                                    item.dataId = val.id;
                                    listitemsMultiList.push(item);
                                }

                            });
                            return {
                                results: listitemsMultiList
                            };
                        }
                    },
                    sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
                }).val("").trigger("change");
            }
        });
        $("#cmbFilterInboxStructure").val('').trigger('change');
        $('#cmbFilterInboxUser').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#userFilterInboxContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchUsers',
                type: "GET",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term) {
                    return { "text": "", "language": window.language };
                    //return { "text": term.term ? term.term : "", "language": window.language };
                },
                processResults: function (data, term) {
                    var termSearch = term.term ? term.term : "";

                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        var fullName = val.fullName;
                        if (window.language != 'en') {
                            fullName = getFullNameByLangauge(val);
                            fullName = fullName.trim() == "" ? val.fullName : fullName;
                        }
                        var allNames = getFullNameInAllLangauge(val);
                        if (allNames.length == 0) allNames.push(fullName);
                        if (termSearch != "" &&
                            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                            return;
                        }
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = fullName;
                            item.isStructure = false;
                            item.dataId = val.id;
                            listitemsMultiList.push(item);
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });
        $("#cmbFilterInboxUser").val('').trigger('change');

        var fromDate = $('#filterInboxFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery('#filterInboxToDate').val() && jQuery('#filterInboxToDate').val() !== "" ? jQuery('#filterInboxToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterInboxFromDate_img").click(function () {
            fromDate.toggle();
        });
        var toDate = $('#filterInboxToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery('#fromDate').val() && jQuery('#fromDate').val() !== "" ? jQuery('#fromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterInboxToDate_img").click(function () {
            toDate.toggle();
        });
        $('#txtFilterInboxReferenceNumber').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterInboxSearch').focus();
                }
                else {
                    $('#filterInboxFromDate').focus();
                }
            }
        });
        $('#btnFilterInboxClear').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterInboxSearch').focus();
                }
                else {
                    $('#txtFilterInboxReferenceNumber').focus();
                }
            }
        });
    } else {
        $('.searchToRemove > div:first-child').hide();
        $("#gridPanel").removeAttr("style");
        $("#gridPanel").addClass("panel-default");
    }
}
function buildColumns(gridcolumns, nodeId, fromChildTable) {
    var nodes = new Nodes().getTreeUserNodes(window.language);
    var node = $.grep(nodes.children, function (element, index) {
        return element.id === nodeId;
    })[0];
    var columns = node.data.columns !== "" && node.data.columns !== null ? JSON.parse(node.data.columns) : [];
    var columnDetails = $.grep(columns, function (element, index) {
        return element.isColumnDetail === true;
    });
    if (columnDetails.length > 0 || (IsInboxModeWithGrouping && !fromChildTable)) {
        gridcolumns.push(
            {
                title: fromChildTable ? '' : '<a id="expandAll" class="expand-control expand"></a>',
                "className": 'details-control',
                "orderable": false,
                "data": null,
                "defaultContent": '',
                width: '16px'
            });
    }
    gridcolumns.push(
        {
            "className": "text-left",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            data: "importanceId",
            'render': function (data, type, full, meta) {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var i = 0; i < importances.length; i++) {
                    if (importances[i].id === data) {
                        let btnView = document.createElement("i");
                        btnView.setAttribute("class", "fa fa-exclamation fa-lg");
                        btnView.setAttribute("style", "color:" + importances[i].color);
                        btnView.setAttribute("title", importances[i].text);
                        return btnView.outerHTML;
                    }
                }
                return "";
            }
        });
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var customColumn = [];
    for (var i = 0; i < columns.length; i++) {
        var column = columns[i];
        if (!column.isColumnDetail) {
            if (column.isCustom === true) {
                customColumn.push({ columnName: column.name, functionName: column.customFunctionName });
                var customColumnKeyword = column.customFunctionName;
                var escapedVariable = column.customFunctionName.replace(/"/g, '&quot;');

                gridcolumns.push({
                    title: GetCustomAttributeTranslationByLangauge(customColumnKeyword), "orderable": false, 'defaultContent': '<div data-function="' + escapedVariable + '"></div>',
                    "createdCell": function (td, cellData, rowData, row, col) {
                        var htmlCell = "";
                        var div = td.children[0];
                        var customFunctionName = div !== null && typeof div !== "undefined" ? $(div).attr("data-function") : "";
                        //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                        //{
                        //    var functionName = customFunctionName.split("(")[0];
                        //    htmlCell = eval(functionName + "(" + JSON.stringify(rowData) + ")");
                        //}

                        var customColumns = JSON.parse(rowData.documentForm.form);
                        var columnName = customColumn.find(f => f.functionName == customFunctionName).columnName;
                        htmlCell = htmlCell == "" && customColumns != null ? (customColumns[columnName] == undefined ? "" : customColumns[columnName]) : htmlCell;

                        $(td).html(htmlCell);
                    }
                });
            } else {
                switch (column.name) {
                    case "Category":
                        gridcolumns.push({
                            title: Resources.Category, data: "categoryId", "orderable": !fromChildTable, orderSequence: fromChildTable == true ? [] : ["asc", "desc"],
                            render: function (data, type, full, meta) {
                                var categories = new Categories().get(window.language);
                                for (var i = 0; i < categories.length; i++) {
                                    if (categories[i].id === data) {
                                        return categories[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "ReferenceNumber":
                        gridcolumns.push({ title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": false, "className": "min-max-width-50-150" });
                        break;
                    case "Subject":
                        gridcolumns.push({ title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "orderable": !fromChildTable, orderSequence: fromChildTable == true ? [] : ["asc", "desc"], "className": "min-max-width-50-150" });
                        break;
                    case "From":
                        gridcolumns.push({
                            visible: fromChildTable | !IsInboxModeWithGrouping,

                            title: Resources.From, "orderable": false,
                            "render": function (data, type, full, meta) {
                                var retValue = "";
                                if (full.fromStructure) {
                                    retValue += full.fromStructure;
                                }
                                if (full.fromUser) {
                                    var user = full.fromUser;
                                    retValue += retValue !== "" ? "/" + user : user;
                                }
                                return retValue;
                            }
                        });
                        break;
                    case "TransferDate":
                        gridcolumns.push({
                            visible: fromChildTable | !IsInboxModeWithGrouping,

                            title: Resources.TransferDate, data: "transferDate",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.transferDate, null, window.CalendarType);
                            }, "orderable": !fromChildTable, orderSequence: fromChildTable == true ? [] : ["asc", "desc"], width: "100px"
                        });
                        break;
                    case "CreatedDate":
                        gridcolumns.push({
                            title: Resources.CreatedDate, data: "createdDate", "orderable": !fromChildTable, orderSequence: fromChildTable == true ? [] : ["asc", "desc"], width: "100px",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
                            }
                        });
                        break;
                    case "SendingEntity":
                        gridcolumns.push({ title: Resources.SendingEntity, data: "sendingEntity", "orderable": false, width: "100px" });
                        break;
                    case "ReceivingEntity":
                        gridcolumns.push({ title: Resources.ReceivingEntity, data: "receivingEntity", "orderable": false, width: "100px" });
                        break;
                    case "Purpose":
                        gridcolumns.push({
                            title: Resources.Purpose, data: "purpose", "orderable": false, width: "100px",
                            render: function (data, type, full, meta) {
                                var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                                for (let i = 0; i < purposes.length; i++) {
                                    if (purposes[i].id === full.purposeId) {
                                        return purposes[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "Priority":
                        gridcolumns.push({
                            title: Resources.Priority, data: "priority", "orderable": false, width: "100px",
                            render: function (data, type, full, meta) {
                                var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                                for (let i = 0; i < priorities.length; i++) {
                                    if (priorities[i].id === full.priorityId) {
                                        return priorities[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "Privacy":
                        gridcolumns.push({
                            title: Resources.Privacy, data: "privacy", "orderable": false, width: "100px",
                            render: function (data, type, full, meta) {
                                var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                                for (let i = 0; i < privacies.length; i++) {
                                    if (privacies[i].id === full.privacyId) {
                                        return privacies[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "CreatedBy":
                        gridcolumns.push({ title: Resources.CreatedBy, data: "createdByUser", "orderable": false, width: "100px" });
                        break;
                    case "OpenedDate":
                        gridcolumns.push({
                            title: Resources.OpenedDate, data: "openedDate", "orderable": false, width: "100px",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.openedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                            }
                        });
                        break;
                    case "DueDate":
                        gridcolumns.push({
                            title: Resources.DueDate, data: "dueDate", "orderable": false, width: "100px",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.dueDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                            }
                        });
                        break;
                }
            }
        }
    }
}
function buildColumnsDetails(row, nodeId) {
   
    //var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var nodes = new Nodes().getTreeUserNodes(window.language);
    var node = $.grep(nodes.children, function (element, index) {
        return element.id === nodeId.toString();
    })[0];
    var columns = node.data.columns !== "" && node.data.columns !== null ? JSON.parse(node.data.columns) : [];
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var html = "";
    for (var i = 0; i < columns.length; i++) {
        var column = columns[i];
        if (column.isColumnDetail) {
            if (column.isCustom === true) {
                var customColumn = column.name;
                var customColumnKeyword = column.customFunctionName;

                var htmlCell = "";
                var customFunctionName = column.customFunctionName;
                //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                //{
                //    var functionName = customFunctionName.split("(")[0];
                //    htmlCell = eval(functionName + "(" + JSON.stringify(row.data()) + ")");
                //}

                var customColumns = JSON.parse(row.data().documentForm.form);
                htmlCell = htmlCell == "" && customColumns != null ? (customColumns[customColumn] == undefined ? "" : customColumns[customColumn]) : htmlCell;

                html += '<tr><th style="width: 15%;padding:5px">' + (GetCustomAttributeTranslationByLangauge(customColumnKeyword)) + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + htmlCell + '</td></tr>';

            } else {
                switch (column.name) {
                    case "Category":
                        var category = "";
                        var categories = new Categories().get(window.language);
                        for (var i = 0; i < categories.length; i++) {
                            if (categories[i].id === row.data().categoryId) {
                                category = categories[i].text;
                            }
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Category + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + category + '</td></tr>';
                        break;
                    case "ReferenceNumber":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.ReferenceNumber + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().referenceNumber || '') + '</td></tr>';
                        break;
                    case "Subject":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Subject + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().subject || '') + '</td></tr>';
                        break;
                    case "From":
                        var from = "";
                        if (row.data().fromStructure) {
                            from += row.data().fromStructure;
                        }
                        if (row.data().fromUser) {
                            var user = row.data().fromUser;
                            from += from !== "" ? "/" + user : user;
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.From + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + from + '</td></tr>';
                        break;
                    case "TransferDate":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.TransferDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().transferDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "CreatedDate":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.CreatedDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().createdDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "SendingEntity":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.SendingEntity + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().sendingEntity || '') + '</td></tr>';
                        break;
                    case "ReceivingEntity":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.ReceivingEntity + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().receivingEntity || '') + '</td></tr>';
                        break;
                    case "Purpose":
                        var purpose = "";
                        var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                        for (let i = 0; i < purposes.length; i++) {
                            if (purposes[i].id === row.data().purposeId) {
                                purpose = purposes[i].text;
                            }
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Purpose + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + purpose + '</td></tr>';
                        break;
                    case "Priority":
                        var priority = "";
                        var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                        for (let i = 0; i < priorities.length; i++) {
                            if (priorities[i].id === row.data().priorityId) {
                                priority = priorities[i].text;
                            }
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Priority + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + priority + '</td></tr>';
                        break;
                    case "Privacy":
                        var privacy = "";
                        var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                        for (let i = 0; i < privacies.length; i++) {
                            if (privacies[i].id === row.data().privacyId) {
                                privacy = privacies[i].text;
                            }
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Privacy + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + privacy + '</td></tr>';
                        break;
                    case "CreatedBy":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.CreatedBy + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().createdByUser || '') + '</td></tr>';
                        break;
                    case "OpenedDate":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.OpenedDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().openedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "DueDate":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.DueDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().dueDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';
                        break;
                }
            }
        }
    }
    return html;
}
function sendToReceivingEntity(receivingEntities, broadcastIds, delegationId, transferToType, dueDate) {
    let modalWrapper = $(".modal-window");
    let modelIndex = new SendToReceivingEntityIndex.SendToReceivingEntityIndex();
    modelIndex.purposes = new Helper().getPurpose();
    modelIndex.receivingEntities = receivingEntities;
    modelIndex.transferToType = transferToType;
    modelIndex.customAttributeDueDate = dueDate;
    modelIndex.broadcastIds = broadcastIds;
    modelIndex.isBroadcast = true;
    let sendToReceivingEntityIndexView = new SendToReceivingEntityIndex.SendToReceivingEntityIndexView(modalWrapper, modelIndex);
    sendToReceivingEntityIndexView.render();
    $("#modalSendToReceivingEntity").off("shown.bs.modal");
    $('#modalSendToReceivingEntity').on('shown.bs.modal', function () {
        $("#hdSendDeligationId").val(delegationId);
        CKEDITOR.instances.txtAreaInstruction.focus();
    });
}

function formatChild(self, model, buttons, parentRow) {
    var parentData = parentRow.data();
    var childrenData = datatableParentsAndChildren
        .filter(function (item) {
            return item.parent.id === parentData.id;
        }).flatMap(function (item) {
            return item.children;
        });
    var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
    var Buttons = SecurityMatrix.getToolbarActions(securityMatrix, 2, gTableName);
    var childrenColumns = [
        {
            visible: buttons.length > 0,
            title: '',
            width: '16px',
            "orderable": false,
            "render": function (data, type, row) {
                return !_isFollowUpNode ? "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />" : "<input type='checkbox' parent=" + row.documentId + " data-id=" + row.id + " checked/>";
            }
        },
        { title: "Id", data: "id", visible: false, "orderable": false }];
    buildColumns(childrenColumns, self.model.nodeId, true)
    childrenColumns.push({
        "className": "text-right",
        "autoWidth": false,
        "bAutoWidth": false,
        width: "16px",
        'orderable': false,
        'sortable': false,
        'render': function (data, type, full, meta) {
            var html = "";
            if (full.isOverDue) {
                html += "<div class='mr-sm' title='" + Resources.OverDue + "'><i class='fa fa-clock-o fa-lg text-danger'></i></div>&nbsp;";
            }
            if (full.isRead) {
                html += "<div class='mr-sm' title='" + Resources.Read + "'><i class='fa fa-envelope-open-o text-info'></i></div>&nbsp;";
            }
            if (!_isFollowUpNode) {
                if (!full.sentToUser) {
                    //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
                    var delegatedUser = model.delegationId !== null ? new DelegationUsers().getById(Number(model.delegationId)) : null;  //$("#hdDelegatedUserIds").val().split(window.Splitter);
                    var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                    if (full.isLocked && (full.ownerUserId !== null && full.ownerUserId !== Number($("#hdUserId").val()) && model.delegationId === null)
                        || (full.ownerDelegatedUserId !== null && full.ownerDelegatedUserId !== Number($("#hdUserId").val()) && model.delegationId !== null)
                        || (full.ownerUserId !== null && delegatedUserId !== full.ownerUserId && model.delegationId !== null)) {
                        var lockedBy = full.lockedByDelegatedUser !== '' ? full.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + full.lockedBy : full.lockedBy;
                        var titleLock = Resources.LockedBy + ": " + lockedBy + "\n" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(full.lockedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                        html += "<div class='mr-sm text-left infoDivIcon' title=''><i class='fa fa-lock fa-lg text-danger' title='" + titleLock + "'></i><div class='infoDiv font-13' style='opacity: 0;'>" +
                            titleLock + "</div></div>&nbsp;";
                    }
                }
                let category = $.grep(self.model.categories, function (e) {
                    return e.id === full.categoryId;
                });
                if (category[0] && category[0].isBroadcast) {
                    html += "<div class='mr-sm' title='" + Resources.Broadcast + "'><i class='fa fa-bullhorn text-primary'></i></div>&nbsp;";
                } else if (full.cced) {
                    html += "<div class='mr-sm' title='" + Resources.CarbonCopy + "'><i class='fa fa-cc text-warning'></i></div>&nbsp;";
                }
                if (full.sentToUser) {
                    html += "<div class='mr-sm' title='" + Resources.SentToUser + "'><i class='icon-user text-success'></i></div>&nbsp;";
                }
                if (full.sentToStructure) {
                    html += "<div class='mr-sm' title='" + Resources.SentToStructure + "'><i class='fa fa-building-o text-primary'></i></div>&nbsp;";
                }
            } else {
                var requestToCompleteStatus = new CoreComponents.Lookup.Statuses().findById(full.transferStatusId, 'en');
                if (requestToCompleteStatus) {
                    if (requestToCompleteStatus.text.replace(/\s/g, '').toLowerCase() === "requesttocomplete") {
                        //if (requestToCompleteStatus.id === 7) {
                        html += "<div class='mr-sm' title='" + Resources.RequestToComplete + "'><i class='fa fa-check-square-o mr-sm text-info'></i></div>&nbsp;";
                    }
                }
            }

            if (full.hasAttachment) {
                html += "<div class='mr-sm' title='" + (full.attachmentCount != null ? full.attachmentCount.toString() : "0") + " " + Resources.Attachments + "'><i class='fa fa-paperclip text-info'></i></div>&nbsp;";
            }

            return "<div id='divLock" + full.id + "' style='display: inline-flex;'>" + html + "</div>";
        }
    });
    childrenColumns.push({
        "className": "text-right",
        "autoWidth": false,
        "bAutoWidth": false,
        width: "16px",
        'orderable': false,
        'sortable': false,
        'render': function (data, type, full, meta) {
            var html = "";
            var lockedByMe = false;
            //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
            var delegatedUser = model.delegationId !== null ? new DelegationUsers().getById(Number(model.delegationId)) : null;  //$("#hdDelegatedUserIds").val().split(window.Splitter);
            var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
            if (full.isLocked && (full.ownerUserId !== null && full.ownerUserId === Number($("#hdUserId").val()) && model.delegationId === null)
                || (full.ownerDelegatedUserId !== null && full.ownerDelegatedUserId === Number($("#hdUserId").val())
                    && delegatedUserId === full.ownerUserId && model.delegationId !== null)
                || (full.ownerUserId !== null && delegatedUserId === full.ownerUserId && model.delegationId !== null)) {
                lockedByMe = true;
            }
            if ((!lockedByMe && !full.sentToUser) || full.cced) {
                let btnView = document.createElement("button");
                if ((lockedByMe || full.ownerUserId === null) && !full.cced) {
                    btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                } else {
                    btnView.setAttribute("class", "btn btn-xs btn-warning view");
                }
                btnView.setAttribute("title", Resources.View);
                btnView.setAttribute("clickattr", "openDocumentViewMode(" + full.id + "," + (full.openedDate !== "") + "," + model.delegationId + "," + full.cced + ", " + inheritFromInboxNode.data.id /*self.model.nodeId*/ + ")");
                btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                html += btnView.outerHTML;
            }

            if (!full.sentToUser && lockedByMe) {
                var lockedBy = '';
                if (full.ownerDelegatedUserId !== null) {
                    if (full.ownerDelegatedUserId === Number($("#hdUserId").val())) {
                        lockedBy = Resources.You + " " + Resources.OnBehalfOf + " " + full.lockedBy;
                    } else {
                        lockedBy = full.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + Resources.You;
                    }
                } else {
                    if (full.ownerUserId === Number($("#hdUserId").val())) {
                        lockedBy = Resources.You;
                    } else {
                        lockedBy = full.lockedBy;
                    }
                }

                var title = Resources.LockedBy + ": " + lockedBy + "</br>" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(full.lockedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                let btnLock = document.createElement("button");
                btnLock.setAttribute("id", 'btnUnLock');
                btnLock.setAttribute("title", '');
                btnLock.setAttribute("class", "btn btn-xs btn-success mr-sm unlock infoDivIcon");
                btnLock.innerHTML = "<i class='fa fa-unlock fa-white'></i><div class='infoDiv font-13 text-left' style='opacity: 0;'>" + title + "</div>";
                btnLock.setAttribute("clickattr", "unlock(this," + full.id + "," + model.delegationId + ", " + inheritFromInboxNode.data.id/*self.model.nodeId*/ + ")");
                html += btnLock.outerHTML;
            }
            if ((lockedByMe || full.ownerUserId === null) && !full.cced) {
                let btn = document.createElement("button");
                btn.setAttribute("class", "btn btn-xs btn-primary edit");
                btn.setAttribute("title", Resources.Edit);
                btn.setAttribute("clickattr", "openDocumentEditMode(" + full.id + "," + lockedByMe + "," + full.sentToUser + "," + model.delegationId + ", " + inheritFromInboxNode.data.id /*self.model.nodeId*/ + ")");
                btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                html += btn.outerHTML;
            }
            return "<div style='display: inline-flex;'>" + html + "</div>";
        }
    });

    SecurityMatrix.getRowActions(securityMatrix, childrenColumns, inheritFromInboxNode.data.id/*self.model.nodeId*/);
    var childTable = $('<table id="childTable_' + parentData.id + '" class="table table-striped table-hover followUpTasks" style="border: 1px solid black;width: 100%;"></table>');

    childTable.DataTable({
        data: childrenData,
        columns: childrenColumns,
        paging: false,
        searching: false,
        info: false

    });

    childTable.on('click', 'td.details-control', function (event) {
        event.stopPropagation(); // Stop event propagation to parent table
        var tr = $(this).closest('tr');
        var row = childTable.DataTable().row(tr);
        if (row.child.isShown()) {
            row.child.hide();
            tr.removeClass('shown');
        } else {
            row.child(format(row, inheritFromInboxNode.data.id /*self.model.nodeId*/)).show();
            tr.addClass('shown');
        }
    });

    childTable.on('click', ".edit,.view,.unlock", function () {
        var onclick = $(this).attr("clickattr");
        eval(onclick);
    });
    childTable.find('tbody').on('dblclick', 'tr', function (event) {
        if (!gLocked) {
            gLocked = true;
            try {
                var onclick = $(this).find(".edit").attr("clickattr");
                if (!onclick) {
                    onclick = $(this).find(".view").attr("clickattr");
                }
                eval(onclick);
            } catch (e) {
                gLocked = false;
            }
        }
    });
    childTable.on('click', 'td.followUpTask', function (event) {
        event.stopPropagation();
        let tr = $(this).closest('tr');
        var data = childTable.DataTable().row(tr).data();
        if ($('[data-id=' + data.id + ']').prop('checked')) {
            var isAllChecked = 0;
            $('[parent=' + data.documentId + ']').each(function () {
                if (!this.checked)
                    isAllChecked = 1;
            });
            if (isAllChecked == 0) {
                $('[data-documentId=' + data.documentId + ']').prop("checked", true);
                $('[data-documentId=' + data.documentId + ']').trigger("change");
            }
        }
        else {
            $('[data-documentId=' + data.documentId + ']').prop("checked", false);
            $('[data-documentId=' + data.documentId + ']').trigger("change");
        }
    });
    return childTable
}
var gTableName = "grdInboxItems";
var transferComponent;
var gLocked = false;
var dataTableParents = [];
var datatableParentsAndChildren = [];
var IsInboxModeWithGrouping = window.InboxMode === "InboxDefaultWithGrouping";
var _isFollowUpNode = false;
class UserNodeDocumentView extends Intalio.View {
    constructor(element, model) {
        super(element, "userNodeListCorrespondance", model);
    }
    render() {

        var self = this;
        var model = this.model;
        var params = { nodeId: model.nodeId };
        var nodesData = new Nodes().getTreeNodes();
        var inboxNodeData = null;
        var inheritFromInboxNode = null;

        for (var i = 0; i < nodesData.children.length; i++) {
            var element = nodesData.children[i];

            // Look for a child node with data.inherit === "Inbox"
            for (var j = 0; j < element.children.length; j++) {
                var child = element.children[j];

                if (child.data && child.data.inherit === "Inbox") {
                    inheritFromInboxNode = child;
                    inboxNodeData = element;
                    break; // Exit inner loop
                }
            }

            if (inheritFromInboxNode) {
                break; // Exit outer loop
            }
        }
        Common.ajaxGet('/Node/GetNameById', params, function (response) {

            document.getElementById("NodeName").innerText = response;

                        }, null, false);
 



        if (self.model.nodeId == window.AssignedToMeNode)
            $('.content-heading').text(Resources.AssignedToMe);


        Common.setActiveSidebarMenu("liUserNode" + self.model.nodeId);

        var categoryModels = new CategoryModel().getFullCategories();
        if (categoryModels.length > 0) {
            for (var i = 0; i < categoryModels.length; i++) {
                if (categoryModels[i].basicAttribute !== "" && categoryModels[i].basicAttribute !== null) {
                    let basicAttributes = JSON.parse(categoryModels[i].basicAttribute);
                    if (basicAttributes.length > 0) {
                        let receivingEntityObj = $.grep(basicAttributes, function (e) {
                            return e.Name === "ReceivingEntity";
                        });
                        if (receivingEntityObj[0].BroadcastReceivingEntity) {
                            for (var j = 0; j < self.model.categories.length; j++) {
                                if (self.model.categories[j].id == categoryModels[i].id) {
                                    self.model.categories[j].isBroadcast = true;
                                    self.model.categories[j].isInternalBroadcast = receivingEntityObj[0].Type === "internal";
                                }
                            }
                        }
                    }
                }
            }
        }
        _isFollowUpNode = isFollowUpNode(self.model.nodeId);
        $.fn.select2.defaults.set("theme", "bootstrap");
        buildFilters(self.model.nodeId, self.model.categories);
        Common.gridCommon();
        let categoriesIds = undefined;
        for (var i = 0; i < model.categories.length; i++) {
            if (i == 0) {
                categoriesIds = model.categories[i].id;

            }
            else {
                categoriesIds += "_" + model.categories[i].id;
            }

        }
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        for (var i = 0; i < 7; i++) {


            for (var j = 0; j < securityMatrix.SecurityNodes[i + 1].Actions.length; j++) {
                if (securityMatrix.SecurityNodes[i + 1].Actions[j].Name == "DismissCustom") {
                    securityMatrix.SecurityNodes[i + 1].Actions[j].JsFunction = securityMatrix.SecurityNodes[i + 1].Actions[j].JsFunction
                        .replace("$delegationId", model.delegationId)
                        .replace("$categories", categoriesIds)
                }
            }

        }
        //var buttons = SecurityMatrix.getToolbarActions(securityMatrix, 2/*self.model.nodeId*/, gTableName);
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, inheritFromInboxNode.data.id, gTableName).map(button => {
            button.className = (button.className || '') + ' hidden conditional-buttons';
            return button;
        });



        var exportButton = [{
            className: 'btn-sm btn-primary',

            extend: 'print',
            text: Resources.Print,
            title: function () {
                return Resources.reportInbox;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.reportInbox + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [':visible', 2, 3]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportInbox.print
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'excelHtml5',
            title: function () {
                return Resources.reportInbox;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.reportInbox + '_' + formattedDate + '_' + formattedTime;
            },


            exportOptions: {
                columns: [2, 3, ':visible']

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportInbox.excelHTML5
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'pdfHtml5',
            title: function () {
                return Resources.reportInbox;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.reportInbox + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [':visible', 2, 3]
            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportInbox.pdfHTML5
            },
      
        ];

        var allButtons = [exportButton, ...buttons];
        //buttons.push({
        //    className: 'btn-sm btn-primary',
        //    text: '<em class="fa fa-check-square-o mr-sm"></em>' + Resources.Complete,
        //    action: function (e, dt, node, config)
        //    {
        //        var ids = GridCommon.GetSelectedRows(gTableName);
        //        var lstReceivers = [];
        //        var allSelectedData = [];
        //        if (ids.length > 0)
        //        {
        //            var table = $('#' + gTableName).DataTable();
        //            var allRows = table.rows().data();
        //            var allCced = true;
        //            for (var i = 0; i < ids.length; i++)
        //            {
        //                var selectedRowData = $.grep(allRows, function (element, index)
        //                {
        //                    return element.id === Number(ids[i]);
        //                });
        //                allSelectedData.push(selectedRowData[0]);
        //                let category = $.grep(self.model.categories, function (e)
        //                {
        //                    return e.id === selectedRowData[0].categoryId;
        //                });
        //                if (selectedRowData && selectedRowData[0] && allCced && (!selectedRowData[0].cced || category[0].isInternalBroadcast))
        //                {
        //                    allCced = false;
        //                }
        //            }
        //            if (allCced)
        //            {
        //                Common.alertMsg(Resources.AllSelectedItemsCCed);
        //            } /*else*/
        //            //{
        //            //    //if (window.EnableConfirmationMessage === "True")
        //            //    //{
        //            //    //    //Common.showConfirmMsg(Resources.CompleteConfirmation, function ()
        //            //    //    //{
        //            //    //    //    completeTransfer(ids, model);
        //            //    //    //});
        //            //    //} else
        //            //    //{
        //            //    //    completeTransfer(ids, model);
        //            //    //}
        //            //}
        //        }
        //        else
        //        {
        //            Common.alertMsg(Resources.NoRowSelected);
        //        }
        //    }
        //});
        //buttons.push({

        //    className: 'btn-sm btn-primary',
        //    text: '<em class="fa fa-paper-plane-o mr-sm"></em>' + Resources.Transfer,
        //    action: function (e, dt, node, config)
        //    {
        //        var ids = GridCommon.GetSelectedRows(gTableName);
        //        if (ids.length > 0)
        //        {
        //            var table = $('#' + gTableName).DataTable();
        //            var allRows = table.rows().data();
        //            var allCced = true;
        //            var cCedAndNotBroadcast = false;
        //            var allReadOnly = true;
        //            var allSelectedData = [];
        //            var broadcastIds = [];
        //            for (var i = 0; i < ids.length; i++)
        //            {
        //                var selectedRowData = $.grep(allRows, function (element, index)
        //                {
        //                    return element.id === Number(ids[i]);
        //                });
        //                if (selectedRowData && selectedRowData[0])
        //                {
        //                    let category = $.grep(self.model.categories, function (e)
        //                    {
        //                        return e.id === selectedRowData[0].categoryId;
        //                    });
        //                    if (allCced && (!selectedRowData[0].cced || category[0].isInternalBroadcast))
        //                    {
        //                        allCced = false;
        //                    }
        //                    if (selectedRowData[0].cced && !category[0].isInternalBroadcast)
        //                    {
        //                        cCedAndNotBroadcast = true;
        //                    }
        //                    if ((!selectedRowData[0].cced || category[0].isInternalBroadcast) && !selectedRowData[0].isLocked)
        //                    {
        //                        allReadOnly = false;
        //                        allSelectedData.push(selectedRowData[0]);
        //                        if (category[0].isInternalBroadcast)
        //                        {
        //                            broadcastIds.push(Number(ids[i]));
        //                        }
        //                    } else if (selectedRowData[0].isLocked)
        //                    {
        //                        //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
        //                        var delegatedUser = model.delegationId !== null ? new DelegationUsers().getById(Number(model.delegationId)) : null; //$("#hdDelegatedUserIds").val().split(window.Splitter);
        //                        var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
        //                        if ((selectedRowData[0].ownerUserId !== null && selectedRowData[0].ownerUserId === Number($("#hdUserId").val()) && model.delegationId === null)
        //                            || (selectedRowData[0].ownerDelegatedUserId !== null && selectedRowData[0].ownerDelegatedUserId === Number($("#hdUserId").val())
        //                                && delegatedUserId === selectedRowData[0].ownerUserId && model.delegationId !== null)
        //                            || (selectedRowData[0].ownerUserId !== null && delegatedUserId === Number(selectedRowData[0].ownerUserId) && model.delegationId !== null))
        //                        {
        //                            allReadOnly = false;
        //                            allSelectedData.push(selectedRowData[0]);
        //                            if (category[0].isInternalBroadcast)
        //                            {
        //                                broadcastIds.push(Number(ids[i]));
        //                            }
        //                        }
        //                    }
        //                }
        //            }

        //            if (allCced || allReadOnly)
        //            {
        //                if (allCced)
        //                {
        //                    Common.alertMsg(Resources.AllSelectedItemsCCed);
        //                } else
        //                {
        //                    Common.alertMsg(Resources.AllSelectedItemsAreReadOnly);
        //                }
        //            } else
        //            {
        //                if (allSelectedData.length > 0)
        //                {
        //                    var nonBroadcastIds = $.grep(allSelectedData, function (value)
        //                    {
        //                        return $.inArray(value.id, broadcastIds) < 0;
        //                    });
        //                    if (nonBroadcastIds.length == 0)
        //                    {
        //                        if (cCedAndNotBroadcast)
        //                        {
        //                            Common.alertMsg(Resources.AllSelectedItemsAreBroadcastAndCarbonCopies);
        //                            return;
        //                        } else
        //                        {
        //                            Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
        //                            return;
        //                        }
        //                    }
        //                    var dataSelected = allSelectedData.map(t => t.toStructureId).filter(function (value, index, array)
        //                    {
        //                        return array.indexOf(value) === index;
        //                    });
        //                    if (window.EnableSendingRules === "True" && dataSelected.length > 1)
        //                    {
        //                        Common.alertMsg(Resources.AllSelectedItemsHaveDifferentSender);
        //                        return;
        //                    }
        //                    var callback = function (data)
        //                    {
        //                        var arrayOfTransfers = [];
        //                        var transferToStructures = [], transferToStructureIds = [];
        //                        var ccedTransfer = 0;
        //                        var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
        //                        var hasPrivacyLevel = true;
        //                        var htmlPrivacy = Resources.NoUserWithSelectedPrivacy;
        //                        var structureExist = data.some(a => a.toStructureId !== null && a.toUserId === null);
        //                        for (var i = 0; i < data.length; i++)
        //                        {
        //                            var currentPurpose = $.grep(purposes, function (e)
        //                            {
        //                                return e.id.toString() === data[i].purposeId;
        //                            });
        //                            if (currentPurpose[0].cCed === true)
        //                            {
        //                                ccedTransfer++;
        //                            }
        //                            if (data[i].toUserId === null)
        //                            {
        //                                transferToStructures.push({ dataId: data[i].toStructureId, text: data[i].name });
        //                                transferToStructureIds.push(data[i].toStructureId);
        //                            }
        //                            for (var j = 0; j < allSelectedData.length; j++)
        //                            {
        //                                arrayOfTransfers.push({
        //                                    toStructureId: data[i].toStructureId,
        //                                    toUserId: data[i].toUserId,
        //                                    name: data[i].name,
        //                                    dueDate: data[i].dueDate,
        //                                    purposeId: data[i].purposeId,
        //                                    instruction: data[i].instruction,
        //                                    cced: data[i].cced,
        //                                    fromStructureId: allSelectedData[j].toStructureId,
        //                                    parentTransferId: allSelectedData[j].id,
        //                                    isStructure: data[i].toUserId === null,
        //                                    documentId: allSelectedData[j].documentId,
        //                                    privacyId: allSelectedData[j].privacyId,
        //                                    referenceNumber: allSelectedData[j].referenceNumber
        //                                });
        //                            }
        //                        }
        //                        var userStructureIds = $("#hdStructureIds").val();
        //                        var message = SendTransferModal.checkAbilityToSend(transferToStructureIds.length === 0 ? userStructureIds.split(window.Seperator) : transferToStructureIds, transferToStructures, model.selectedPrivacy, model.privacies, null, false);
        //                        if (message === "error")
        //                        {
        //                            return;
        //                        }
        //                        for (var i = 0; i < arrayOfTransfers.length; i++)
        //                        {
        //                            if (arrayOfTransfers[i].toUserId !== null)
        //                            {
        //                                // arrayOfTransfers[i].privacyId = 0;
        //                                var userObj = new IdentityService().getFullUser(arrayOfTransfers[i].toUserId);
        //                                if (userObj !== null)
        //                                {
        //                                    var attributePrivacy = $.grep(userObj.attributes, function (e)
        //                                    {
        //                                        return e.text === window.UserPrivacy ? e.value : 0;
        //                                    });
        //                                    //if (attributePrivacy.length > 0)
        //                                    //{
        //                                    //    arrayOfTransfers[i].privacyId = attributePrivacy[0].value === "" ? 0 : attributePrivacy[0].value;
        //                                    //}
        //                                }
        //                                //var currentPrivacy = $.grep(model.privacies, function (e)
        //                                //{
        //                                //    return e.id.toString() === arrayOfTransfers[i].privacyId.toString();
        //                                //});
        //                                if (attributePrivacy !== null && attributePrivacy.length > 0)
        //                                {
        //                                    if (arrayOfTransfers[i].privacyId > parseInt(attributePrivacy[0].value))
        //                                    {
        //                                        hasPrivacyLevel = false;
        //                                        htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
        //                                        if (arrayOfTransfers[i].referenceNumber)
        //                                        {
        //                                            htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
        //                                        }
        //                                    }
        //                                } else
        //                                {
        //                                    hasPrivacyLevel = false;
        //                                    htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
        //                                    if (arrayOfTransfers[i].referenceNumber)
        //                                    {
        //                                        htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
        //                                    }
        //                                }
        //                            }
        //                        }
        //                        if (!hasPrivacyLevel)
        //                        {
        //                            message += (message !== "" ? " \n " : "") + htmlPrivacy;
        //                        }
        //                        if (message !== "")
        //                        {
        //                            if (!structureExist)
        //                            {
        //                                Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function ()
        //                                {
        //                                    SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, model.delegationId, true);
        //                                }, function ()
        //                                {
        //                                });
        //                            }
        //                            else
        //                            {
        //                                if (window.SendWithoutStructureReceiverOrPrivacyLevel === "True")
        //                                {
        //                                    Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function ()
        //                                    {
        //                                        SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, model.delegationId, true);
        //                                    }, function ()
        //                                    {
        //                                    });
        //                                } else
        //                                {
        //                                    Common.alertMsg(message);
        //                                }
        //                            }
        //                        } else
        //                        {
        //                            if (ccedTransfer === data.length)
        //                            {
        //                                Common.showConfirmCcedMsg(Resources.AllSelectedTransfersAreCCconfirmation, function ()
        //                                {
        //                                    SendTransferModal.transfer(transferComponent, arrayOfTransfers, true, false, true, model.delegationId, true);
        //                                }, function ()
        //                                {
        //                                    SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, true, true, model.delegationId, true);
        //                                });
        //                            } else
        //                            {
        //                                SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, model.delegationId, true);
        //                            }
        //                        }
        //                    };
        //                    let modalWrapper = $(".modal-window");
        //                    transferComponent = new Transfer(dataSelected[0], $("#hdUserId").val(), $("#hdStructureIds").val().split(window.Seperator),
        //                        window.IsStructureSender === "True", window.EnableSendingRules === "True", window.EnableTransferToUsers === "True", "", model.delegationId,
        //                        callback, modalWrapper);
        //                    transferComponent.render();
        //                    $('.modalTransfer').modal('show');
        //                    $(".modalTransfer").off("hidden.bs.modal");
        //                    $(".modalTransfer").off("shown.bs.modal");
        //                    $('.modalTransfer').on('hidden.bs.modal', function ()
        //                    {
        //                        $(".modalTransfer").parent().remove();
        //                        swal.close();
        //                    });
        //                }
        //            }
        //        }
        //        else
        //        {
        //            Common.alertMsg(Resources.NoRowSelected);
        //        }
        //    }
        //});
        //buttons.push({
        //    className: 'btn-sm btn-primary',
        //    text: '<em class="fa fa-ban mr-sm"></em>' + Resources.DismissCopy,
        //    action: function (e, dt, node, config)
        //    {
        //        var ids = GridCommon.GetSelectedRows(gTableName);
        //        if (ids.length > 0)
        //        {
        //            var table = $('#' + gTableName).DataTable();
        //            var allRows = table.rows().data();
        //            var atLeastOneCced = false;
        //            var dismissIds = [];
        //            var broadcastIds = [];
        //            var allSelectedData = [];
        //            for (var i = 0; i < ids.length; i++)
        //            {
        //                var selectedRowData = $.grep(allRows, function (element, index)
        //                {
        //                    return element.id === Number(ids[i]);
        //                });
        //                if (selectedRowData && selectedRowData[0] && selectedRowData[0].cced)
        //                {
        //                    allSelectedData.push(selectedRowData[0]);
        //                    let category = $.grep(self.model.categories, function (e)
        //                    {
        //                        return e.id === selectedRowData[0].categoryId;
        //                    });
        //                    if (category[0].isInternalBroadcast)
        //                    {
        //                        broadcastIds.push(Number(ids[i]));
        //                    }
        //                    if (!atLeastOneCced && !category[0].isInternalBroadcast)
        //                    {
        //                        atLeastOneCced = true;
        //                    }
        //                    dismissIds.push(Number(ids[i]));
        //                }
        //            }
        //            var nonBroadcastIds = $.grep(dismissIds, function (value)
        //            {
        //                return $.inArray(Number(value), broadcastIds) < 0;
        //            });

        //            if (!atLeastOneCced && broadcastIds.length !== ids.length)
        //            {
        //                Common.alertMsg(Resources.AllSelectedItemsNotCCed);
        //            } else if (nonBroadcastIds.length === 0)
        //            {
        //                Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
        //            }
        //            else
        //            {
        //                if (dismissIds.length > 0)
        //                {
        //                    if (window.EnableConfirmationMessage === "True")
        //                    {
        //                        Common.showConfirmMsg(Resources.DismissCarbonCopyConfirmation, function ()
        //                        {
        //                            dismissCarbonCopy(dismissIds, model, allSelectedData);
        //                        });
        //                    } else
        //                    {
        //                        dismissCarbonCopy(dismissIds, model, allSelectedData);
        //                    }
        //                }
        //            }
        //        }
        //        else
        //        {
        //            Common.alertMsg(Resources.NoRowSelected);
        //        }
        //    }
        //});
        var columns = [{
            visible: buttons.length > 0, title: '<input id="chkAll" type="checkbox" />', width: '16px', "orderable": false,
            "render": function (data, type, row) {
                if (!IsInboxModeWithGrouping) {

                    return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />";
                } else {
                    return null;
                }
            }
        },
        { title: "Id", data: "id", visible: false, "orderable": false }];
        buildColumns(columns, self.model.nodeId, false);
        columns.push({
            'visible': _isFollowUpNode,
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = "";
                if (full.isOverDue) {
                    html += "<div class='mr-sm' title='" + Resources.OverDue + "'><i class='fa fa-clock-o fa-lg text-danger'></i></div>&nbsp;";
                }
                if (full.hasAttachment) {
                    html += "<div class='mr-sm' title='" + (full.attachmentCount != null ? full.attachmentCount.toString() : "0") + " " + Resources.Attachments + "'><i class='fa fa-paperclip text-info'></i></div>&nbsp;";
                }
                var requestToCompleteStatus = new CoreComponents.Lookup.Statuses().findById(full.status, 'en');
                if (requestToCompleteStatus) {
                    //if (requestToCompleteStatus.text.replace(/\s/g, '').toLowerCase() === "completed") {
                    if (requestToCompleteStatus.id === 3) {
                        html += "<div class='mr-sm' title='" + Resources.Completed + "'><i class='fa fa-check-square-o mr-sm text-success'></i></div>&nbsp;";
                    }
                }

                return "<div id='divLock" + full.id + "' style='display: inline-flex;'>" + html + "</div>";
            }
        });
        columns.push({
            'visible': !IsInboxModeWithGrouping,
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = "";
                if (full.isOverDue) {
                    html += "<div class='mr-sm' title='" + Resources.OverDue + "'><i class='fa fa-clock-o fa-lg text-danger'></i></div>&nbsp;";
                }
                if (full.isRead) {
                    html += "<div class='mr-sm' title='" + Resources.Read + "'><i class='fa fa-envelope-open-o text-info'></i></div>&nbsp;";
                }
                if (!full.sentToUser) {
                    //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
                    var delegatedUser = model.delegationId !== null ? new DelegationUsers().getById(Number(model.delegationId)) : null;  //$("#hdDelegatedUserIds").val().split(window.Splitter);
                    var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                    if (full.isLocked && (full.ownerUserId !== null && full.ownerUserId !== Number($("#hdUserId").val()) && model.delegationId === null)
                        || (full.ownerDelegatedUserId !== null && full.ownerDelegatedUserId !== Number($("#hdUserId").val()) && model.delegationId !== null)
                        || (full.ownerUserId !== null && delegatedUserId !== full.ownerUserId && model.delegationId !== null)) {
                        var lockedBy = full.lockedByDelegatedUser !== '' ? full.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + full.lockedBy : full.lockedBy;
                        var titleLock = Resources.LockedBy + ": " + lockedBy + "\n" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(full.lockedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                        html += "<div class='mr-sm text-left infoDivIcon' title=''><i class='fa fa-lock fa-lg text-danger' title='" + titleLock + "'></i><div class='infoDiv font-13' style='opacity: 0;'>" +
                            titleLock + "</div></div>&nbsp;";
                    }
                }
                let category = $.grep(self.model.categories, function (e) {
                    return e.id === full.categoryId;
                });
                if (category[0] && category[0].isBroadcast) {
                    html += "<div class='mr-sm' title='" + Resources.Broadcast + "'><i class='fa fa-bullhorn text-primary'></i></div>&nbsp;";
                } else if (full.cced) {
                    html += "<div class='mr-sm' title='" + Resources.CarbonCopy + "'><i class='fa fa-cc text-warning'></i></div>&nbsp;";
                }
                if (full.sentToUser) {
                    html += "<div class='mr-sm' title='" + Resources.SentToUser + "'><i class='icon-user text-success'></i></div>&nbsp;";
                }
                if (full.sentToStructure) {
                    html += "<div class='mr-sm' title='" + Resources.SentToStructure + "'><i class='fa fa-building-o text-primary'></i></div>&nbsp;";
                }
                if (full.hasAttachment) {
                    html += "<div class='mr-sm' title='" + (full.attachmentCount != null ? full.attachmentCount.toString() : "0") + " " + Resources.Attachments + "'><i class='fa fa-paperclip text-info'></i></div>&nbsp;";
                }

                return "<div id='divLock" + full.id + "' style='display: inline-flex;'>" + html + "</div>";
            }
        });
        columns.push({
            'visible': !IsInboxModeWithGrouping,
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = "";
                var lockedByMe = false;
                //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
                var delegatedUser = model.delegationId !== null ? new DelegationUsers().getById(Number(model.delegationId)) : null;  //$("#hdDelegatedUserIds").val().split(window.Splitter);
                var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                if (full.isLocked && (full.ownerUserId !== null && full.ownerUserId === Number($("#hdUserId").val()) && model.delegationId === null)
                    || (full.ownerDelegatedUserId !== null && full.ownerDelegatedUserId === Number($("#hdUserId").val())
                        && delegatedUserId === full.ownerUserId && model.delegationId !== null)
                    || (full.ownerUserId !== null && delegatedUserId === full.ownerUserId && model.delegationId !== null)) {
                    lockedByMe = true;
                }
                if ((!lockedByMe && !full.sentToUser) || full.cced) {
                    let btnView = document.createElement("button");
                    if ((lockedByMe || full.ownerUserId === null) && !full.cced) {
                        btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                    } else {
                        btnView.setAttribute("class", "btn btn-xs btn-warning view");
                    }
                    btnView.setAttribute("title", Resources.View);
                    btnView.setAttribute("clickattr", "openDocumentViewMode(" + full.id + "," + (full.openedDate !== "") + "," + model.delegationId + "," + full.cced + ", " + inheritFromInboxNode.data.id /*self.model.nodeId*/ + ")");
                    btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                    html += btnView.outerHTML;
                }

                if (!full.sentToUser && lockedByMe) {
                    var lockedBy = '';
                    if (full.ownerDelegatedUserId !== null) {
                        if (full.ownerDelegatedUserId === Number($("#hdUserId").val())) {
                            lockedBy = Resources.You + " " + Resources.OnBehalfOf + " " + full.lockedBy;
                        } else {
                            lockedBy = full.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + Resources.You;
                        }
                    } else {
                        if (full.ownerUserId === Number($("#hdUserId").val())) {
                            lockedBy = Resources.You;
                        } else {
                            lockedBy = full.lockedBy;
                        }
                    }

                    var title = Resources.LockedBy + ": " + lockedBy + "</br>" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(full.lockedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                    let btnLock = document.createElement("button");
                    btnLock.setAttribute("id", 'btnUnLock');
                    btnLock.setAttribute("title", '');
                    btnLock.setAttribute("class", "btn btn-xs btn-success mr-sm unlock infoDivIcon");
                    btnLock.innerHTML = "<i class='fa fa-unlock fa-white'></i><div class='infoDiv font-13 text-left' style='opacity: 0;'>" + title + "</div>";
                    btnLock.setAttribute("clickattr", "unlock(this," + full.id + "," + model.delegationId + ", " + inheritFromInboxNode.data.id/*self.model.nodeId*/ + ")");
                    html += btnLock.outerHTML;
                }
                if ((lockedByMe || full.ownerUserId === null) && !full.cced) {
                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-primary edit");
                    btn.setAttribute("title", Resources.Edit);
                    btn.setAttribute("clickattr", "openDocumentEditMode(" + full.id + "," + lockedByMe + "," + full.sentToUser + "," + model.delegationId + ", " + inheritFromInboxNode.data.id/*self.model.nodeId*/ + ")");
                    btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                    html += btn.outerHTML;
                }
                return "<div style='display: inline-flex;'>" + html + "</div>";
            }
        });
        if (!IsInboxModeWithGrouping) {
            SecurityMatrix.getRowActions(securityMatrix, columns, inheritFromInboxNode.data.id /*self.model.nodeId*/);
        }
      
        let table = $("#" + gTableName)
            .DataTable({

                "createdRow": function (row, data, dataIndex) {
                    var color = "";
                    var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                    for (var i = 0; i < priorities.length; i++) {
                        if (priorities[i].id === data.priorityId) {
                            color = priorities[i].color;
                        }
                    }
                    if (color !== "") {
                        $(row).attr('style', "color:" + color);
                    }
                    if (!data.isRead) {
                        $(row).css('font-weight', 'bold');
                    }
                    $(row).css('cursor', 'pointer');
                },
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/Transfer/ListInbox",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d) {
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.NodeId = self.model.nodeId;
                        d.DelegationId = model.delegationId;
                        d.PriorityId = $("#cmbFilterInboxPriority").val() !== null && typeof $("#cmbFilterInboxPriority").val() !== "undefined" ? $("#cmbFilterInboxPriority").val() : "0";
                        d.PrivacyId = $("#cmbFilterInboxPrivacy").val() !== null && typeof $("#cmbFilterInboxPrivacy").val() !== "undefined" ? $("#cmbFilterInboxPrivacy").val() : "0";
                        d.PurposeId = $("#cmbFilterInboxPurpose").val() !== null && typeof $("#cmbFilterInboxPurpose").val() !== "undefined" ? $("#cmbFilterInboxPurpose").val() : "0";
                        d.CategoryId = $("#cmbFilterInboxCategory").val() !== null && typeof $("#cmbFilterInboxCategory").val() !== "undefined" ? $("#cmbFilterInboxCategory").val() : "0";
                        d.ReferenceNumber = $("#txtFilterInboxReferenceNumber").val() !== "" && typeof $("#txtFilterInboxReferenceNumber").val() !== "undefined" ? $("#txtFilterInboxReferenceNumber").val() : "";
                        d.FromDate = $("#filterInboxFromDate").val() !== "" && typeof $("#filterInboxFromDate").val() !== "undefined" ? $("#filterInboxFromDate").val() : "";
                        d.ToDate = $("#filterInboxToDate").val() !== "" && typeof $("#filterInboxToDate").val() !== "undefined" ? $("#filterInboxToDate").val() : "";
                        d.Read = $("#chkFilterInboxRead").is(':checked');
                        d.Locked = $("#chkFilterInboxLocked").is(':checked');
                        d.Overdue = $("#chkFilterInboxOverdue").is(':checked');
                        d.Subject = $("#txtFilterInboxSubject").val() !== "" && typeof $("#txtFilterInboxSubject").val() !== "undefined" ? $("#txtFilterInboxSubject").val() : "";
                        d.StructureIds = $("#cmbFilterInboxStructure").val() !== null && typeof $("#cmbFilterInboxStructure").val() !== "undefined" ? $("#cmbFilterInboxStructure").val() : [];
                        d.UserIds = $("#cmbFilterInboxUser").val() !== null && typeof $("#cmbFilterInboxUser").val() !== "undefined" ? $("#cmbFilterInboxUser").val() : [];
                        d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
                        return d;
                    }, "dataSrc": function (response) {
                        if (response.data && response.data != "") {
                            var result = [];
                            response.data.map(function (obj) {
                                var value = {
                                    key: obj.documentId.toString(),
                                    arr: response.data.filter((item) => item.documentId == obj.documentId)
                                }
                                if (result.length == 0 || (result.length > 0 && !result.filter((element) => element.key == obj.documentId).length > 0))
                                    result.push(value);
                            }, {});
                            datatableParentsAndChildren = result.map(function (item) {
                                return {
                                    parent: item.arr[0],
                                    children: item.arr
                                };
                            });
                            dataTableParents = datatableParentsAndChildren.map(function (item) {
                                return item.parent;
                            });
                        }
                        var returnValue = IsInboxModeWithGrouping ? dataTableParents : response.data;
                        return returnValue;
                    }
                },
                "order": [],
                "columns": columns,
                dom: '<"html5buttons "B>ltr<"pull-left"p><"pull-right"i>',

                "fnInitComplete": function (settings, json) {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                buttons: allButtons
            });
        table.on('draw.dt', function () {
            $('#' + gTableName + " td input[type='checkbox']").on('click', function () {
                if ($(this).is(":checked"))

                    $(".conditional-buttons").removeClass("hidden");

                // $(".html5buttons").removeClass("hidden");
                else if (GridCommon.GetSelectedRows(gTableName).length == 1)
                    $(".conditional-buttons").addClass("hidden");

            });

            $('#grdItems tbody tr td').each(function () {
                this.setAttribute('title', $(this).text());
            });
            GridCommon.CheckSelectedRows(gTableName);

            if ($('#expandAll')[0] != undefined) {
                $('#expandAll')[0].classList.add("expand");
                $('#expandAll')[0].classList.remove("colllapse");
            }


        })


        //table.on('draw.dt', function () {
        //    $('#grdItems tbody tr td').each(function () {
        //        this.setAttribute('title', $(this).text());
        //    });
        //    GridCommon.CheckSelectedRows(gTableName);
        //})

   

        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        GridCommon.AddCheckBoxEvents(gTableName);
        if (window.AllowRowSelection == "True") {
            $('#' + gTableName + ' tbody').on('click', 'tr', function () {

                let checkbox = $(this).find('input[type="checkbox"]');
                if (checkbox.is(":checked")) {
                    checkbox.prop("checked", true);
                } else {
                    checkbox.prop("checked", false);
                }
                UpdateButtonVisibility(gTableName);
            });
        }
        function UpdateButtonVisibility() {
            var selectedRows = GridCommon.GetSelectedRows(gTableName).length;
            if (selectedRows > 0) {
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".conditional-buttons").addClass("hidden");
            }
        }

        function UpdateButtonVisibility(tableId) {
            var selectedRows = GridCommon.GetSelectedRows(tableId).length;

            // Show buttons if at least one row is selected
            if (selectedRows > 0) {
                //$(".html5buttons").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");

                //table.buttons.add(allButtons);

            } else if (selectedRows == 0) {
                $(".conditional-buttons").addClass("hidden");

                //    $(".html5buttons").addClass("hidden");
            }
        }
        if (!IsInboxModeWithGrouping) {
            $('#' + gTableName + ' tbody').on('click', ".edit,.view,.unlock", function () {
                var onclick = $(this).attr("clickattr");
                eval(onclick);
            });
            $('#' + gTableName + ' tbody').on('dblclick', 'tr', function () {
                if (!gLocked) {
                    gLocked = true;
                    try {
                        var onclick = $(this).find(".edit").attr("clickattr");
                        if (!onclick) {
                            onclick = $(this).find(".view").attr("clickattr");
                        }
                        eval(onclick);
                    } catch (e) {
                        gLocked = false;
                    }
                }
            });
        }
        else {
            $('#expandAll').on('click', function () {
                var expandAllbtn = this;

                table.rows().eq(0).each(function (index) {
                    var row = table.row(index);
                    var tr = row.node();
                    if (expandAllbtn.classList.contains("expand")) {
                        if (row.child.isShown()) {
                            return;
                        }
                        row.child(formatChild(self, model, buttons, row)).show();
                        tr.classList.add("shown");
                    }
                    else if (expandAllbtn.classList.contains("colllapse")) {
                        row.child.hide();
                        tr.classList.remove("shown")
                    }
                });
                // use word colllapse not collapse to avoid hide the expandAll btn 
                if (expandAllbtn.classList.contains("expand")) {
                    expandAllbtn.classList.add("colllapse");
                    expandAllbtn.classList.remove("expand");

                } else if (expandAllbtn.classList.contains("colllapse")) {
                    expandAllbtn.classList.add("expand");
                    expandAllbtn.classList.remove("colllapse");
                }
            });

            $('#' + gTableName + ' tbody').on('dblclick', 'tr', function () {

                $(this).find('td.details-control').trigger('click');

            });
        }
        $('#' + gTableName + ' tbody').on('click', 'td.details-control', function () {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            }
            else {
                if (IsInboxModeWithGrouping) {

                    row.child(formatChild(self, model, buttons, row)).show();
                } else {

                    row.child(format(row, model.nodeId)).show();
                }

                tr.addClass('shown');
            }
            if (_isFollowUpNode) {
                $('[data-documentid=' + row.data().documentId + ']').prop('checked', !row.child.isShown());
                $('[data-documentid=' + row.data().documentId + ']').trigger("change");
            }
        });
        $('#' + gTableName + ' tbody').on('click', 'td.parentCheckAll', function (event) {
            event.stopPropagation();
            let tr = $(this).closest('tr');
            var data = table.row(tr).data();
            $('[parent=' + data.documentId + ']').prop('checked', $('[data-documentId=' + data.documentId + ']').prop('checked'));
            $('[parent=' + data.documentId + ']').trigger("change");
        });
        SecurityMatrix.InitToolbarColor();
        SecurityMatrix.InitContextMenu(securityMatrix, inheritFromInboxNode.data.id/* self.model.nodeId*/);
        $('#filtersContainer input').off('keydown', function (e) { });
        $('#filtersContainer input').on('keydown', function (e) {
            var code = e.keyCode || e.which;
            if (code === 13) {
                $("#btnFilterInboxSearch").trigger('click');
            }
        });

        $('#' + gTableName + ' #chkAll').on('click', function () {
            if ($(this).is(":checked"))
                $(".conditional-buttons").removeClass("hidden");

            else
                $(".conditional-buttons").addClass("hidden");

        });


    }
}
export default { UserNodeDocument, UserNodeDocumentView };