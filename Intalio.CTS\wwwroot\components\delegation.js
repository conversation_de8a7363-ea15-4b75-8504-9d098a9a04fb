﻿import Intalio from './common.js'
import { Categories } from './lookup.js'
class Delegation extends Intalio.Model {
    constructor() {
        super();
        this.securityMatrixCategory = "Categories";
        this.structureIds = [];
        this.userId = null;
        this.managerId = null;
    }
}

function openDelegationWindow(delegation) {
    if (delegation) {
        delegation = JSON.parse(delegation);
        $(self.refs["hdId"]).val(delegation.id);
        $(self.refs["cmbIndexCategory"]).val(delegation.categoryIds).trigger("change");
        $(self.refs["cmbPrivacyIndex"]).val(delegation.privacyId).trigger("change");
        $(self.refs["notes"]).val(delegation.note);
        if (delegation.toUserValueText.id.toString() !== self.model.managerId.toString()) {
            $(self.refs["cmbIndexToUser"]).append(new Option(delegation.toUserValueText.text, delegation.toUserValueText.id, true, true)).trigger('change');
        } else {
            $(self.refs["chkDelegateToManager"]).prop("checked", true).trigger("change");
        }
        $(self.refs["chkOldCorrespondence"]).prop("checked", delegation.showOldCorrespondecne).trigger("change");
        //$(self.refs["chksign"]).prop("checked", delegation.allowSign).trigger("change");
        $(self.refs["chkDraftInbox"]).prop("checked", delegation.draftInbox).trigger("change");

        $(self.refs['indexFromDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery(self.refs['indexToDate']).val() && jQuery(self.refs['indexToDate']).val() !== "" ? jQuery(self.refs['indexToDate']).val() : false,
                    minDate: delegation.fromDate && delegation.fromDate !== "" && new Date(delegation.fromDate) < new Date() ? delegation.fromDate : moment().format('DD/MM/YYYY')
                });
            },
            onChange: function () {
                $(self.refs['indexFromDate']).parsley().validate();
            }
        }).setDate(delegation.fromDate);
        $(self.refs['indexToDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery(self.refs['indexFromDate']).val() && jQuery(self.refs['indexFromDate']).val() !== "" ? jQuery(self.refs['indexFromDate']).val() : false,
                });
                if (!(jQuery(self.refs['indexFromDate']).val() && jQuery(self.refs['indexFromDate']).val() !== "")) {
                    this.set({
                        minDate: moment().format('DD/MM/YYYY')
                    });
                }
            },
            onChange: function () {
                $(self.refs['indexToDate']).parsley().validate();
            }
        }).setDate(delegation.toDate);
        $(self.refs['indexStartDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery(self.refs['indexToDate']).val() && jQuery(self.refs['indexToDate']).val() !== "" ? jQuery(self.refs['indexToDate']).val() : false,
                    
                });
            },
            onChange: function () {
                $(self.refs['indexStartDate']).parsley().validate();
            }
        }).setDate(delegation.startDate);
        $(self.refs['modalDelegationTitle']).html(Resources.Edit);
    } else {// New Delegation
        var lstPrivacies = new CoreComponents.Lookup.Privacies().get(window.language);

        $(self.refs["cmbPrivacyIndex"]).val(lstPrivacies[0].id).select2();
        $(self.refs['chkOldCorrespondence']).prop("checked", false).change();
        //$(self.refs["chksign"]).prop("checked", false).change();
        $(self.refs["chkDraftInbox"]).prop("checked", false).change();
        $(self.refs['modalDelegationTitle']).html(Resources.New);
    }
    $(self.refs['modalDelegation']).modal('show');
    $(self.refs["modalDelegation"]).off("hidden.bs.modal");
    $(self.refs["modalDelegation"]).off("shown.bs.modal");
    $(self.refs['modalDelegation']).on('shown.bs.modal', function () {
        $(self.refs['indexFromDate']).focus();
    });
    $(self.refs['modalDelegation']).on('hidden.bs.modal', function () {
        $(self.refs['formPost']).parsley().reset();
        $(self.refs['notes']).val("");
        $(self.refs['hdId']).val("");
        $(self.refs["cmbIndexCategory"]).val("").trigger("change");
        $(self.refs["cmbIndexToUser"]).val("").trigger("change");
        $(self.refs['indexFromDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery(self.refs['indexToDate']).val() && jQuery(self.refs['indexToDate']).val() !== "" ? jQuery(self.refs['indexToDate']).val() : false,
                    minDate: moment().format('DD/MM/YYYY')
                });
            },
            onChange: function () {
                $(self.refs['indexFromDate']).parsley().validate();
            }
        }).setDate("");
        $(self.refs['indexToDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery(self.refs['indexFromDate']).val() && jQuery(self.refs['indexFromDate']).val() !== "" ? jQuery(self.refs['indexFromDate']).val() : false,
                });
                if (!(jQuery(self.refs['indexFromDate']).val() && jQuery(self.refs['indexFromDate']).val() !== "")) {
                    this.set({
                        minDate: moment().format('DD/MM/YYYY')
                    });
                }
            },
            onChange: function () {
                $(self.refs['indexToDate']).parsley().validate();
            }
        }).setDate("");
        if ($(self.refs['chkDelegateToManager']) !== null && typeof $(self.refs['chkDelegateToManager']) !== "undefined") {
            $(self.refs['chkDelegateToManager']).prop("checked", false);
        }
        
        $(self.refs["cmbIndexCategory"]).attr("required", "required");
        $(self.refs['indexCategoryContainer']).show();
        $(self.refs["cmbIndexToUser"]).attr("required", "required");
        $(self.refs['indexToUserContainer']).show();
        $(self.refs['managerContainer']).addClass("mt-26px");
        swal.close();
    });
}
function format(row) {
    let items = $.grep(new Categories().get(window.language), function (element, index) {
        return row.data().categoryIds.includes(element.id);
    });
    var categories = [];
    for (var i = 0; i < items.length; i++) {
        categories.push(items[i].text);
    }
    return '<table style="width:100%" cellspacing="0" border="0">' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources[self.model.securityMatrixCategory] + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;">' + (categories || '') + '</td>' +
        '</tr>' +
        '<tr>'+
        '<th style="width: 10%;padding:5px">' + Resources.Note + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;">' + (row.data().note || '') + '</td>' +
        '</tr>' +
        '</table>';
}
function addDelegation(params) {
    var btn = $(self.refs['btnSubmit']);
    btn.button('loading');
    var btnClose = $(self.refs['btnClose']);
    var btnCloseX = $(self.refs['delegationClose']);
    btnClose.attr('disabled', 'disabled');
    btnCloseX.attr('disabled', 'disabled');
    Common.ajaxPost('CTS/Delegation/Save', params, function (data) {
        if (data.message === Resources.UserAlreadyDelegated || data.message === Resources.IncorrectDuration ||
            data.message === Resources.SameCategoryAlreadyAssignedOnSelectedDuration) {
            Common.alertMsg(data.message);
        } else {
            Common.showScreenSuccessMsg();
            $(self.refs['hdId']).val(data.id);
            $(self.refs['modalDelegationTitle']).html(Resources.Edit);
            $(self.refs["grdItems"]).DataTable().ajax.reload();
            $(self.refs["modalDelegation"]).modal('hide');

        }
        btn.button('reset');
        btnClose.removeAttr('disabled');
        btnCloseX.removeAttr('disabled');
    }, function () { btn.button('reset'); btnClose.removeAttr('disabled'); btnCloseX.removeAttr('disabled'); Common.showScreenErrorMsg(); }, false);
}
var self;
class DelegationView extends Intalio.View {
    constructor(element, userId, structureIds, managerId, securityMatrixCategory) {

        var model = new Delegation();
        model.managerId = managerId;
        model.userId = userId;
        model.structureIds = structureIds;
        if (securityMatrixCategory) {
            model.securityMatrixCategory = securityMatrixCategory;
        }
        super(element, "delegation", model);
    }
    render() {
        self = this;

        $.fn.select2.defaults.set("theme", "bootstrap");
        $(".selectAllSpan").after(Resources.SelectAll + " " + Resources[self.model.securityMatrixCategory].toLowerCase());
        var clickedSearch = false;
        $(self.refs['collapseIcon']).click(function () {
            $(self.refs['collapseIcon']).empty();
            if (clickedSearch) {
                $(self.refs['collapseIcon']).append('<i class="fa fa-angle-up fa-lg"></i>');
                $(self.refs['collapsePanel']).attr('class', '');
                $(self.refs['collapsePanel']).addClass('panel-body panel-collapse collapse in');
                clickedSearch = false;
            } else {
                $(self.refs['collapseIcon']).append('<i class="fa fa-angle-down fa-lg"></i>');
                $(self.refs['collapsePanel']).attr('class', '');
                $(self.refs['collapsePanel']).addClass('panel-body panel-collapse collapse');
                clickedSearch = true;
            }
        });
        $(self.refs["btnSearch"]).on('click', function () {
            table.ajax.reload();
        });
        $(self.refs["btnClear"]).on('click', function () {
            $(self.refs["cmbCategory"]).val('').trigger('change');
            $(self.refs["cmbToUser"]).val('').trigger('change');
            fromDate.clear();
            toDate.clear();
            table.ajax.reload();
        });
        $(self.refs['cmbToUser']).select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $(self.refs['toUserContainer']),
            ajax: {
                type: 'POST',
                url: window.IdentityUrl + '/api/SearchUsersByStructureIds',
                headers: {
                    "Authorization": 'Bearer ' + window.IdentityAccessToken
                },
                dataType: 'json',
                delay: 400,
                data: function (params) {
                    var query = {
                        'ids': self.model.structureIds,
                        'text': params.term || "",
                        'language': "",
                        'showOnlyActiveUsers': true
                    };
                    return query;
                },
                processResults: function (data) {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = val.fullName;
                            if (item.id !== Number(self.model.userId)) {
                                listitemsMultiList.push(item);
                            }
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            }
        });
        $(self.refs["cmbToUser"]).val('').trigger('change');



        var fromDate = $(self.refs['fromDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery(self.refs['toDate']).val() && jQuery(self.refs['toDate']).val() !== "" ? jQuery(self.refs['toDate']).val() : false
                });
            }
        });
        $(self.refs["fromDate_img"]).click(function () {
            fromDate.toggle();
        });
        var toDate = $(self.refs['toDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery(self.refs['fromDate']).val() && jQuery(self.refs['fromDate']).val() !== "" ? jQuery(self.refs['fromDate']).val() : false
                });
            }
        });
        $(self.refs["toDate_img"]).click(function () {
            toDate.toggle();
        });
       
        var startDate = $(self.refs['indexStartDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery(self.refs['toDate']).val() && jQuery(self.refs['toDate']).val() !== "" ? jQuery(self.refs['toDate']).val() : false

                });
            }
        });
        $(self.refs["indexStartDate_img"]).click(function () {
            startDate.toggle();
        });
        $(self.refs['cmbToUser']).keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $(self.refs['btnSearch']).focus();
                }
                else {
                    $(self.refs['fromDate']).focus();
                }
            }
        });
        $(self.refs['btnClear']).keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $(self.refs['btnSearch']).focus();
                }
                else {
                    $(self.refs['toDate']).focus();
                }
            }
        });
        Common.gridCommon();
        let table = $(self.refs["grdItems"])
            .on('draw.dt',
                function () {
                    $(self.refs['grdItems']).find('tbody tr td').each(function (index) {
                        this.setAttribute('title', $(this).text());
                    });
                })
            .DataTable({
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "CTS/Delegation/List",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d) {
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        //d.CategoryId = $(self.refs["cmbCategory").val() !== null && typeof $(self.refs["cmbCategory").val() !== "undefined" ? $(self.refs["cmbCategory").val() : "0";
                        d.ToUserId = $(self.refs["cmbToUser"]).val() !== null && typeof $(self.refs["cmbToUser"]).val() !== "undefined" ? $(self.refs["cmbToUser"]).val() : "0";
                        d.FromDate = $(self.refs["fromDate"]).val() !== "" && typeof $(self.refs["fromDate"]).val() !== "undefined" ? $(self.refs["fromDate"]).val() : "";
                        d.ToDate = $(self.refs["toDate"]).val() !== "" && typeof $(self.refs["toDate"]).val() !== "undefined" ? $(self.refs["toDate"]).val() : "";
                        
                        return d;
                    }
                },
                "order": [],
                "columns": [
                    {
                        title: '<input id="chkAll" type="checkbox" />', width: '16px', "orderable": false,
                        "render": function (data, type, row) {
                            return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />";
                        }
                    },
                    {
                        "className": 'details-control',
                        "orderable": false,
                        "data": null,
                        "defaultContent": '',
                        width: '16px'
                    },
                    { title: "Id", data: "id", visible: false, "orderable": false },
                    { title: Resources.FullName, data: "toUser", render: $.fn.dataTable.render.text(), "orderable": false },
                    {
                        title: Resources.FromDate, data: "fromDate", "orderable": true, orderSequence: ["asc", "desc"], width: "200px",
                        render: function (data, type, full, meta) {
                            return DateConverter.toHijriFormated(full.fromDate, null, window.CalendarType);
                        }
                    },
                    {
                        title: Resources.ToDate, data: "toDate", "orderable": true, orderSequence: ["asc", "desc"], width: "200px",
                        render: function (data, type, full, meta) {
                            return DateConverter.toHijriFormated(full.toDate, null, window.CalendarType);
                        }
                    },
                    {
                        title: Resources.CreatedDate, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                        render: function (data, type, full, meta) {
                            return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
                        }
                    },
                    { title: Resources.Privacy, data: "privacyName", render: $.fn.dataTable.render.text(), "orderable": false },
                    {
                        "className": "text-center",
                        "autoWidth": false,
                        "bAutoWidth": false,
                        width: "16px",
                        'orderable': false,
                        'sortable': false,
                        'render': function (data, type, full, meta) {
                            let btn = document.createElement("button");
                            btn.setAttribute("class", "btn btn-xs btn-primary edit");
                            btn.setAttribute("title", Resources.Edit);
                            btn.setAttribute("clickAttr", "openDelegationWindow('" + JSON.stringify(full) + "')");
                            btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                            return btn.outerHTML;
                        }
                    }
                ],
                "fnInitComplete": function (settings, json) {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons"B>trpi',
                buttons: [
                    {
                        className: 'btn-sm btn-danger',
                        text: Resources.Delete,
                        action: function (e, dt, node, config) {
                            var checkedRows = $(self.refs['grdItems']).find('tbody').find('input[type="checkbox"]:checked');
                            if (checkedRows.length > 0) {
                                Common.showConfirmMsg(Resources.DeleteConfirmation, function () {
                                    var ids = new Array();
                                    checkedRows.each(function (index, obj) {
                                        ids.push(obj.getAttribute('data-id'));
                                    });
                                    Common.ajaxDelete('CTS/Delegation/Delete',
                                        {
                                            'ids': ids, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                                        },
                                        function (result) {
                                            table.ajax.reload();
                                        }, null, false);
                                });
                            }
                            else {
                                Common.alertMsg(Resources.NoRowSelected);
                            }
                        }
                    }
                ]
            });
        $($(self.refs['grdItems']).find('tbody')).on('click', 'tr', function () {
            var input = this.getElementsByTagName('input')[0];
            if (typeof input !== "undefined")
                input.checked = input.checked ? false : true;
        });

        $($(self.refs['grdItems']).find('tbody')).on('click', ".edit", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        $('#chkAll').change(function () {
            $('tbody tr td input[type="checkbox"]').prop('checked', $(this).prop('checked'));
        });

        $($(self.refs['grdItems']).find('tbody')).on('click', 'td.details-control', function () {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            }
            else {
                row.child(format(row)).show();
                tr.addClass('shown');
            }
        });
        $(self.refs["btnNewDelegation"]).click(function () {
            openDelegationWindow();
        });

        $(self.refs['cmbIndexCategory']).select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            multiple: "multiple",
            dropdownParent: $(self.refs['indexCategoryContainer']),
            data: new Categories().get(window.language)
        }).on('change', function () {
            $(this).trigger('input');
        });
        $(self.refs["cmbIndexCategory"]).val('').trigger('change');
        $(self.refs['cmbIndexToUser']).select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $(self.refs['indexToUserContainer']),
            ajax: {
                type: 'POST',
                url: window.IdentityUrl + '/api/SearchUsersByStructureIds',
                headers: {
                    "Authorization": 'Bearer ' + window.IdentityAccessToken
                },
                dataType: 'json',
                delay: 400,
                data: function (params) {
                    var query = {
                        'ids': self.model.structureIds,
                        'text': params.term || "",
                        'language': "",
                        'showOnlyActiveUsers': true
                    };
                    return query;
                },
                processResults: function (data) {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = val.fullName;
                            item.firstName = val.firstName;
                            item.lastName = val.lastName;
                            item.roleId = 0;
                            if (item.id !== Number(self.model.userId)) {
                                listitemsMultiList.push(item);
                            }
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            }
        }).on('change', function () {
            $(this).trigger('input');
        });
        $(self.refs["cmbIndexToUser"]).val('').trigger('change');


        $(self.refs['cmbPrivacyIndex']).select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Privacies().get(window.language),
            dropdownParent: $(self.refs['privacyIndexContainer'])
        }).on('change', function () {
            $(this).trigger('input');
        });

        $(self.refs['indexFromDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery(self.refs['indexToDate']).val() && jQuery(self.refs['indexToDate']).val() !== "" ? jQuery(self.refs['indexToDate']).val() : false,
                    minDate: moment().format('DD/MM/YYYY')
                });
            },
            onChange: function () {
                $(self.refs['indexFromDate']).parsley().validate();
            }
        });

        $(self.refs['indexStartDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery(self.refs['indexToDate']).val() && jQuery(self.refs['indexToDate']).val() !== "" ? jQuery(self.refs['indexToDate']).val() : false,
                });
            },
            onChange: function () {
                $(self.refs['indexFromDate']).parsley().validate();
            }
        });
        $(self.refs["indexFromDate_img"]).click(function () {
            if ($(self.refs['indexFromDate']).next('input').length > 0) {
                $(self.refs['indexFromDate']).next('input').focus();
            } else {
                $(self.refs['indexFromDate']).focus();
            }
        });
        $(self.refs['indexToDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery(self.refs['indexFromDate']).val() && jQuery(self.refs['indexFromDate']).val() !== "" ? jQuery(self.refs['indexFromDate']).val() : false,
                });
                if (!(jQuery(self.refs['indexFromDate']).val() && jQuery(self.refs['indexFromDate']).val() !== "")) {
                    this.set({
                        minDate: moment().format('DD/MM/YYYY')
                    });
                }
            },
            onChange: function () {
                $(self.refs['indexToDate']).parsley().validate();
            }
        });
        $(self.refs["indexToDate_img"]).click(function () {
            if ($(self.refs['indexToDate']).next('input').length > 0) {
                $(self.refs['indexToDate']).next('input').focus();
            } else {
                $(self.refs['indexToDate']).focus();
            }
        });

        $(self.refs["indexStartDate_img"]).click(function () {
            if ($(self.refs['indexStartDate']).next('input').length > 0) {
                $(self.refs['indexStartDate']).next('input').focus();
            } else {
                $(self.refs['indexStartDate']).focus();
            }
        });
        $(self.refs['btnClose']).keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $(self.refs['btnSubmit']).focus();
                }
                else {
                    $(self.refs['indexFromDate']).focus();
                }
            }
        });
        $(self.refs['indexFromDate']).keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $(self.refs['btnClose']).focus();
                }
                else {
                    $(self.refs['indexToDate']).focus();
                }
            }
        });
        $(self.refs['formPost']).keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 13) {
                e.preventDefault();
                $(self.refs['btnSubmit']).trigger("click");
            }
        });
        $(self.refs['chkAllCategories']).on('click', function () {
            $(self.refs["cmbIndexCategory"]).select2('destroy').find('option').prop('selected', 'selected').end().select2();
        });
        $(self.refs['chkDelegateToManager']).change(function () {
            if (this.checked) {
                $(self.refs["cmbIndexToUser"]).removeAttr("required");
                $(self.refs['indexToUserContainer']).hide();
                $(self.refs['managerContainer']).removeClass("mt-26px");
            } else {
                $(self.refs["cmbIndexToUser"]).attr("required", "required");
                $(self.refs['indexToUserContainer']).show();
                $(self.refs['managerContainer']).addClass("mt-26px");
            }
        });
        $(self.refs['chkOldCorrespondence']).change(function () {
            if (this.checked) {
                $(self.refs['indexStartDateContainer']).show();
            } else {
                $(self.refs['indexStartDate']).val('').change();
                $(self.refs['indexStartDateContainer']).hide();
            }
        });
        $(self.refs['btnSubmit']).on('click', function () {
            var $form = $(self.refs['formPost']);
            $form.parsley().reset();
            var isValid = $form.parsley().validate();
            if (isValid) {
                let categoryIds = $(self.refs["cmbIndexCategory"]).val();
                let toUserId = $(self.refs["cmbIndexToUser"]).val();
                let privacyId = $(self.refs["cmbPrivacyIndex"]).val();
                let note = $(self.refs["notes"]).val();
                if ($(self.refs['chkDelegateToManager']) !== null && typeof $(self.refs['chkDelegateToManager']) !== "undefined") {
                    if ($(self.refs['chkDelegateToManager']).prop("checked")) {
                        toUserId = self.model.managerId;
                    }
                }
                let showOldCorrespondence = $(self.refs['chkOldCorrespondence']).prop("checked");
                let allowSign = true; //$(self.refs['chksign']).prop("checked");
                let draftInbox = $(self.refs['chkDraftInbox']).prop("checked");
                let fromDate = $(self.refs["indexFromDate"]).val();
                let toDate = $(self.refs["indexToDate"]).val();
                let startDate = $(self.refs["indexStartDate"]).val();
                let params = {
                    'CategoryIds': categoryIds,
                    'ToUserId': toUserId,
                    'PrivacyId': privacyId,
                    'FromDate': fromDate,
                    'ToDate': toDate,
                    'ShowOldCorespondence': showOldCorrespondence,
                    'AllowSign': allowSign,
                    'DraftInbox': draftInbox,
                    'StartDate': startDate,
                    'Note':note,
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                };
                if ($(self.refs["hdId"]).val()) {
                    params.Id = $(self.refs["hdId"]).val();
                }
                addDelegation(params);
            }
        });
    }
}
export default { DelegationView };
