{"name": "Intalio.Case.Portal", "version": "1.0.0", "private": true, "description": "", "main": "index.js", "scripts": {"build": "webpack --mode=development --watch", "release": "webpack --mode=production"}, "keywords": [], "type": "commonjs", "author": "", "license": "ISC", "devDependencies": {"crypto": "^1.0.1", "fs": "^0.0.1-security", "gulp": "^5.0.1", "gulp-replace": "^1.1.4", "through2": "^4.0.2", "yargs": "^18.0.0"}, "dependencies": {"@tanker/file-ponyfill": "^2.5.0", "is-accessor-descriptor": "^3.0.1"}}