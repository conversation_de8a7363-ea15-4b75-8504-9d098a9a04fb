﻿IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'SentReport')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('SentReport', N'Sent Report', N'تقرير البريد المرسل', N'Rapports envoyés', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'DraftReport')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('DraftReport', N'Draft Report', N'تقرير المسودة', N'Projet de rapport', 1)
END


IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'MyRequestReport')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('MyRequestReport', N'My Request Report', N'طلباتي', N'MaRequêteRapport', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'CompletedReport')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('CompletedReport', N'Completed Report', N'تقرير البريد المكتمل', N'Rapport terminé', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'ClosedReport')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('ClosedReport', N'Closed Report', N'تقرير البريد المغلق', N'Rapport fermé', 1)
END



