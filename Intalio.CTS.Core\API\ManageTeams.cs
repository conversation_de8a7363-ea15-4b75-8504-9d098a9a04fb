﻿using Intalio.Core;
using Intalio.Core.DAL;
using Intalio.Core.Model;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.API
{
    public static class ManageTeams
    {

        public static bool Create(TeamModel model, long createdByUserId,long structureId, List<long> userIds,List<long>structureIds)
        {
            bool retValue = false;
            var isNotUnique = CheckUnique(model.Id, model.Name, createdByUserId);
            if (isNotUnique)
                return retValue;
            var Team = new Team
            {
                Name = model.Name,
                CreatedByUserId = createdByUserId,
                CreatedDate = DateTime.Now,
                StructureId = structureId

            };
            Team.Insert();
            model.Id = Team.Id;
            for (int i = 0; i < userIds.Count; i++)
            {
                var teamUser = new TeamUsers
                {
                    UserId = userIds[i],
                    TeamId = Team.Id,  
                    StructureId = structureIds[i],
                    CreatedByUserId = createdByUserId,
                    CreatedDate = DateTime.Now
                };

                teamUser.Insert();  
            }
            retValue = true;
            return retValue;

        }


        
        public static bool Edit(TeamModel model, long UserId, long StructureId, List<long> userIds, List<long> structureIds)
        {
            bool retValue = false;
            var isNotUnique = CheckUnique(model.Id, model.Name, UserId);
            if (isNotUnique)
                return retValue;
            Team item = new Team().Find((long)model.Id);

            if (item != null)
            {
                item.Name = model.Name;
                item.StructureId = StructureId;
                item.Update();
                var currentTeamUsers = new TeamUsers().GetTeamUsersByTeam(item.Id);

                foreach (var teamUser in currentTeamUsers)
                {
                    if (!userIds.Contains(teamUser.UserId))
                    {
                        teamUser.Delete();
                    }
                }
                for (int i = 0; i < userIds.Count; i++)
                {
                    
                    var teamUser = new TeamUsers().Find(userIds[i], item.Id, structureIds[i]);

                    if (teamUser != null)
                    {
                        teamUser.StructureId = structureIds[i];
                        teamUser.UserId = userIds[i];
                        teamUser.Update(); 
                    }
                    else
                    {
                        
                        var newTeamUser = new TeamUsers
                        {
                            UserId = userIds[i],
                            TeamId = item.Id,
                            StructureId = structureIds[i],
                            CreatedByUserId = UserId,
                            CreatedDate = DateTime.Now
                        };
                        newTeamUser.Insert(); 
                    }
                }

                retValue = true;
            }

            return retValue;
        }

        public static APIResponseViewModel Delete(long id,Language language)
        {
            APIResponseViewModel retValue = new APIResponseViewModel{ Success=false ,Message="" };
            if (new Team().CheckHaveFollowUp(id))
            {
                retValue.Message = TranslationUtility.Translate("TeamHasFollowUp", language);
                return retValue;
            }
            Team item = new Team().Find(id);
            if (item != null)
            {
                item.Delete(id);
                retValue.Success = true;
            }
            return retValue;
        }


        public static async Task<(int, List<TeamModel>)> List(int startIndex, int pageSize,
          long userId, int roleId, long structureId, Language language = Language.EN)
        {

            var retValue = (0, new List<TeamModel>());


            Team item = new Team();
            var countResult = item.GetCount(userId);
            var itemList = await item.ListAsync(startIndex, pageSize, userId, structureId);

            retValue = (await countResult, itemList.Select(t => new TeamModel
            {
                Id = t.Id,
                Name = t.Name,
                StructureId = structureId,
                CreatedDate = t.CreatedDate,
                CreatedByUserId = t.CreatedByUserId

            }).ToList());


            return retValue;
        }

        /// <summary>
        /// List teams returns all teams DDL.
        /// </summary>
        /// <returns></returns>
        public static List<ValueText> ListTeams(long userId, long structureId)
        {
            return new Team().GetListStructureId(structureId).Select(t => new ValueText { Id = t.Id, Text = t.Name }).ToList();
        }
        public static async Task<List<TeamModel>> GetTeamsByStructureId(long structureId)

        {
            var teams = new List<TeamModel>();
            Team item = new Team();

            var itemList = await item.ListAsyncByStructureId(structureId);

            teams = itemList.Select(t => new TeamModel
            {
                Id = t.Id,
                Name = t.Name,
                StructureId = t.StructureId,
                CreatedDate = t.CreatedDate,
                CreatedByUserId = t.CreatedByUserId
            }).ToList();

            return teams;
        }

        public static async Task<TeamModel> Get(long id)

        {
            var team =  new Team().FindWithInclude(id);
            return (TeamModelView)team;
        }
        public static bool CheckUnique(long? id, string name,  long UserId)
        {
            return new Team().CheckUnique(id, name,  UserId);
        }
    }

}