﻿using Aspose.Pdf.Operators;
using Aspose.Words.Lists;
using Intalio.Core;
using Intalio.Core.API;
using Intalio.Core.DAL;
using Intalio.Core.Model;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using Microsoft.VisualBasic;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.POIFS.FileSystem;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static iTextSharp.text.pdf.AcroFields;
using Constants = Intalio.Core.Constants;

namespace Intalio.CTS.Core.API
{
    public static class ManageFollowUp
    {

        #region public Methods

        /// <summary>
        /// Add FollowUp
        /// </summary>
        /// <param name="model"></param>
        /// <param name="userId"></param>
        /// <param name="roleId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <returns></returns>
        public static async Task<(bool Status,long FollowUpId ,string Message)> Create(FollowUpCreateModel model, long userId, int roleId, List<long> structureIds, bool isStructureReceiver, bool isStructureSender, short privacyLevel,long structureId, Language language = Language.EN)
        {
            using (FollowUp item = new FollowUp())
            {
                (bool status, long FollowUpId, string Message) retValue = (false, 0, "");
                var documentModel = new DocumentViewModel()
                {
                    DocumentCopyId = model.DocumentId,
                    CategoryId = (short)Configuration.FollowUpCategory,
                    CreatedByStructureId = structureId,
                    CopyOptionsModal = new CopyOptionsModal() { WithEntities = true },

                };

                try
                {
                    if (await CheckIfExistsForSameUser(model.DocumentId, userId, structureId))
                    {
                        var Message = TranslationUtility.Translate("FollowUpExistsForSameUser", language);
                        return retValue = (false, 0, Message);
                    }

                    if (model.TeamId == default && !model.IsPrivate)
                    {
                        var Message = TranslationUtility.Translate("FollowUpTeamEmptyAndNotPrivate", language);
                        return retValue = (false, 0, Message);
                    }

                    var createdDocument = await ManageDocument.CreateCopy(documentModel, null, userId, roleId, structureIds, isStructureReceiver, isStructureSender, privacyLevel, structureId, null, null, language);
                    if (!createdDocument.success)
                    {
                        retValue.Message = createdDocument.message;
                    }
                    if (createdDocument.success && documentModel.Id != default)
                    {

                        var followUpUsers = new List<FollowUpUsers>
                        {
                            new FollowUpUsers()
                            {
                                UserId = userId,
                                StructureId = structureId,
                                FollowUpSecurityId = (short)FollowUpRoles.Owner,
                                CreatedByUserId = userId,
                                CreatedDate = DateTime.Now,

                            }
                        };

                        var document = new Document().FindIncludeDocumentForm((long)documentModel.Id);
                        var form = "{\"isPrivate\":" + model.IsPrivate.ToString().ToLower() + ",\"team\":" + model.TeamId.ToString() + "}";
                        document.DocumentForm.Form = form;
                        document.DueDate = model.ToDate;
                        document.UpdateIncludeDocumentForm();

                        if (model.TeamId != default && (!model.IsPrivate))
                        {
                            var teamUsers = new Team().FindWithInclude(model.TeamId).TeamUsers;

                            foreach (var user in teamUsers)
                            {
                                if (user.UserId != userId)
                                {
                                    followUpUsers.Add(new FollowUpUsers()
                                    {
                                        UserId = user.UserId,
                                        StructureId = user.StructureId,
                                        FollowUpSecurityId = (short)FollowUpRoles.Editor,
                                        CreatedByUserId = userId,
                                        CreatedDate = DateTime.Now,
                                    });
                                }

                            }
                        }

                        var followUpItem = await item.Insert(new FollowUp()
                        {
                            OriginalDocumentId = model.DocumentId,
                            DocumentId = (long)documentModel.Id,
                            CreatedByUserId = userId,
                            IsPrivate = model.IsPrivate,
                            FollowUpStatusId = (long)FollowUpStatus.InProgress,
                            FollowUpFromDate = model.FromDate,
                            FollowUpToDate = model.ToDate,
                            TeamId = model.TeamId == default ? null : model.TeamId,
                            FollowUpUsers = followUpUsers
                        });

                        if (followUpItem.Id != default)
                        {
                             FollowUpPanel followUpPanel = new FollowUpPanel
                             {
                                 FollowUpId = followUpItem.Id,
                                 CreatedDate = DateTime.Now,
                                 ModifiedDate = DateTime.Now,
                                 CreatedByUserId = userId,
                                 ModifiedByUSerId = userId,
                             };
                            followUpPanel.Insert();

                            var documentLogsIds = new ActivityLog().GetByDocumentId((long)documentModel.Id).Select(l => l.Id).ToList();
                            new ActivityLog().Delete(documentLogsIds);
                            List<string> receivingEntities = new List<string>();
                            foreach (var receiver in followUpItem.FollowUpDocument.DocumentReceiverEntity)
                            {
                                string text = string.Empty;
                                if (receiver.EntityGroupId.HasValue)
                                {
                                    text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                                    : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                                    : receiver.EntityGroup.Name;
                                }
                                else
                                {
                                    text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                                   : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                                   : receiver.Structure.Name;
                                }
                                receivingEntities.Add(text);
                            }
                            var newDocumentValue = JsonConvert.SerializeObject(new FollowUpDetailsModel
                            {
                                Id = followUpItem.Id,
                                FollowUpStatusId = followUpItem.FollowUpStatusId,
                                FollowUpStatusName = followUpItem.FollowUpStatus != default(DAL.FollowUpStatus) ? language == Language.AR ? followUpItem.FollowUpStatus.NameAr : language == Language.FR ? followUpItem.FollowUpStatus.NameFr : followUpItem.FollowUpStatus.Name : String.Empty,
                                Subject = followUpItem.FollowUpDocument.Subject,
                                FollowUpFromDate = !followUpItem.FollowUpFromDate.IsNull() ? followUpItem.FollowUpFromDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                                FollowUpToDate = !followUpItem.FollowUpToDate.IsNull() ? followUpItem.FollowUpToDate.ToString(Constants.DATE_FORMAT) : string.Empty,
                                Priority = followUpItem.FollowUpDocument.Priority != default(DAL.Priority) ? language == Language.AR ? followUpItem.FollowUpDocument.Priority.NameAr : language == Language.FR ? followUpItem.FollowUpDocument.Priority.NameFr : followUpItem.FollowUpDocument.Priority.Name : String.Empty,

                                SendingEntityName = followUpItem.FollowUpDocument.SendingEntity != default(Structure) ? language == Language.AR ? !string.IsNullOrEmpty(followUpItem.FollowUpDocument.SendingEntity.NameAr) ? followUpItem.FollowUpDocument.SendingEntity.NameAr : followUpItem.FollowUpDocument.SendingEntity.Name
                                                   : language == Language.FR ? !string.IsNullOrEmpty(followUpItem.FollowUpDocument.SendingEntity.NameFr) ? followUpItem.FollowUpDocument.SendingEntity.NameFr : followUpItem.FollowUpDocument.SendingEntity.Name
                                                   : followUpItem.FollowUpDocument.SendingEntity.Name : String.Empty,
                                ReceiversName = receivingEntities,
                                IsPrivate = true,
                                Keyword = string.Empty,

                            });

                            ManageActivityLog.AddActivityLog(documentModel.Id, null, (int)ActivityLogs.CreateFollowUp, userId, "", newDocumentValue, TranslationUtility.Translate("CreateFollowUpNote", language));

                            retValue = (true, followUpItem.Id, "");
                        }
                    }

                    return retValue;
                }
                catch (Exception excption)
                {
                    if (documentModel.Id != default)
                    {
                        var ids = new List<long>
                        {
                            (long)documentModel.Id
                        };
                       await ManageDocument.Delete(userId, structureIds[0], ids);
                    }
                    retValue.Message= excption.InnerException!=null? excption.InnerException.Message: excption.Message;
                    return retValue;
                }
            }
           
        }
        /// <summary>
        /// For FollowUp Details
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(bool Status,FollowUpDetailsModel Model,string Message)> Get(long userId, long id, int roleId, long structureId, Language language = Language.EN)
        {
            (bool Status, FollowUpDetailsModel Model, string Message) retValue = (false,null,"");
            if (!(await CheckIfUserCanAccess(id,userId, structureId)))
            {
                retValue.Message = TranslationUtility.Translate("UserCantAccessFollowUp", language);

                return retValue;

            }
            FollowUp item = await new FollowUp().FindWithIncludeAsync(id);
            
            if (item != null)
            {
                var isReadonly = false;
               var followUpUserRole = (short)FollowUpRoles.Editor;
                if (item.FollowUpUsers.Any())
                {
                    var userFollowUpRole= item.FollowUpUsers.FirstOrDefault(d=>d.UserId==userId && d.StructureId == structureId).FollowUpSecurityId;
                   
                    if ((short)FollowUpRoles.Reader== userFollowUpRole)
                    {
                        isReadonly = true;
                    }
                    followUpUserRole = userFollowUpRole;
                }
                
                ValueText sendingEntity = null;
                if (item.OriginalDocument.SendingEntity != default(Structure))
                {
                    sendingEntity = new ValueText
                    {
                        Id = item.OriginalDocument.SendingEntity.Id,
                        Text = language == Language.AR ? !string.IsNullOrEmpty(item.OriginalDocument.SendingEntity.NameAr) ? item.OriginalDocument.SendingEntity.NameAr : item.OriginalDocument.SendingEntity.Name
                        : language == Language.FR ? !string.IsNullOrEmpty(item.OriginalDocument.SendingEntity.NameFr) ? item.OriginalDocument.SendingEntity.NameFr : item.OriginalDocument.SendingEntity.Name
                        : item.OriginalDocument.SendingEntity.Name
                    };
                }
              
                var statusName = string.Empty;
                if (item.FollowUpStatus != null)
                {
                    statusName = item.FollowUpStatus.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(item.FollowUpStatus.NameAr))
                    {
                        statusName = item.FollowUpStatus.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(item.FollowUpStatus.NameFr))
                    {
                        statusName = item.FollowUpStatus.NameFr;
                    }
                }
                var priority = string.Empty;
                if (item.FollowUpDocument.Priority != null)
                {
                    priority = item.FollowUpDocument.Priority.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(item.FollowUpDocument.Priority.NameAr))
                    {
                        priority = item.FollowUpDocument.Priority.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(item.FollowUpDocument.Priority.NameFr))
                    {
                        priority = item.FollowUpDocument.Priority.NameFr;
                    }
                }
                List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                List<ReceivingEntityModel> receivingEntities = new List<ReceivingEntityModel>();

                foreach (var receiver in item.OriginalDocument.DocumentReceiverEntity)
                {
                    long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                    bool isEntityGroup = receiver.EntityGroupId.HasValue;
                    string text = string.Empty;
                    if (isEntityGroup)
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                        : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                        : receiver.EntityGroup.Name;
                    }
                    else
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                       : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                       : receiver.Structure.Name;
                    }

                    receivers.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        IsEntityGroup = isEntityGroup
                    });

                    receivingEntities.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        Text = text,
                        IsEntityGroup = isEntityGroup
                    });
                }
             
                retValue = (true,new FollowUpDetailsModel
                {
                    FollowUpId = item.Id,
                    FollowUpUserRole = followUpUserRole,
                    IsPrivate = item.IsPrivate.HasValue? item.IsPrivate.Value:false,
                    TeamId=item.TeamId,
                    FollowUpDocument = item.FollowUpDocument,
                    OriginalDocument = item.OriginalDocument,
                    CategoryId = item.FollowUpDocument.CategoryId,
                    Subject = item.FollowUpDocument.Subject,
                    FollowUpStatusName = statusName,
                    FollowUpStatusId = item.FollowUpStatusId,
                    DueDate = !item.FollowUpToDate.Date.IsNull() ? item.FollowUpToDate.Date.ToString(Constants.DATE_FORMAT) : string.Empty,
                    PriorityId = item.FollowUpDocument.PriorityId,
                    Priority = priority,
                    SendingEntityId = item.OriginalDocument.SendingEntityId,
                    SendingEntity = sendingEntity,
                    Receivers = receivers,
                    ReceivingEntities = receivingEntities,
                    Readonly = isReadonly,
                    CreatedByUser = item.CreatedByUser == null ? String.Empty :
                        (language == Language.EN ?
                        $"{item.CreatedByUser?.Firstname} {item.CreatedByUser?.Lastname}" :
                        $"{IdentityHelperExtension.GetFullName(item.CreatedByUserId, language)}"),
                    CreatedByStructureId = item.FollowUpDocument.CreatedByStructureId,
                    Keyword = item.FollowUpDocument.DocumentForm != null ? item.FollowUpDocument.DocumentForm.Keyword : string.Empty,
                    CreatedByUserId = item.CreatedByUserId,
                },"");
            }
            return retValue;
        }
        /// <summary>
        /// List documents with FollowUp status.
        /// Must be created by the user
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="filter"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<FollowUpListViewModel>)> ListFollowUp(int startIndex, int pageSize, long userId, long structureId,
            ExpressionBuilderFilters filter = null, List<SortExpression> sortExpression = null,Language language = Language.EN)
        {
            using (FollowUp item = new FollowUp())
            {
                var filterExp = filter != null ? ExpressionBuilder.GetExpression<FollowUp>(filter, ExpressionBuilderOperator.And) : null;
                var countResult =await item.GetFollowUpCountAsync(userId, structureId, filterExp);
                var itemList = await item.ListFollowUpAsync(startIndex, pageSize, userId, structureId, filterExp, sortExpression?.OrderByExpression<FollowUp>());
                return ( countResult, itemList.Select(t =>
                {
                    var sendingEntity = string.Empty;
                    if (t.OriginalDocument.SendingEntity != null)
                    {
                        sendingEntity = t.OriginalDocument.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.OriginalDocument.SendingEntity.NameAr))
                        {
                            sendingEntity = t.OriginalDocument.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.OriginalDocument.SendingEntity.NameFr))
                        {
                            sendingEntity = t.OriginalDocument.SendingEntity.NameFr;
                        }
                    }
                    var StatusName = string.Empty;
                    if (t.FollowUpStatus != null)
                    {
                        StatusName = t.FollowUpStatus.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FollowUpStatus.NameAr))
                        {
                            StatusName = t.FollowUpStatus.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FollowUpStatus.NameFr))
                        {
                            StatusName = t.FollowUpStatus.NameFr;
                        }
                    }
                    string receivingEntity = string.Empty;
                    string structureText = string.Empty;
                    string entityGroupText = string.Empty;
                    foreach (var receiver in t.OriginalDocument.DocumentReceiverEntity)
                    {
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                            entityGroupText += text + Constants.SEPARATOR;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                            structureText += text + Constants.SEPARATOR;
                        }
                    }
                    receivingEntity = $"{structureText}{entityGroupText}";
                    if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.Substring(receivingEntity.Length - 1) == Constants.SEPARATOR)
                    {
                        receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                    }

                    var isReadonly = false;
                    short? followUpUserRole = null;
                    if (t.FollowUpUsers.Any())
                    {
                        var userFollowUpRole = t.FollowUpUsers.FirstOrDefault(d => d.UserId == userId && d.StructureId == structureId).FollowUpSecurityId;
                        followUpUserRole = userFollowUpRole;
                        if ((short)FollowUpRoles.Reader == userFollowUpRole)
                        {
                            isReadonly = true;
                        }
                    }
                    return new FollowUpListViewModel
                    {
                        Id = t.Id,
                        DocumentId = t.DocumentId,
                        OriginalDocumentId = t.OriginalDocumentId,
                        Subject = t.FollowUpDocument.Subject,
                        SendingEntity = sendingEntity,
                        ReceivingEntity = receivingEntity,
                        CategoryId = t.OriginalDocument.CategoryId,
                        ReferenceNumber = t.OriginalDocument.ReferenceNumber,
                        StatusId = t.FollowUpStatus.Id,
                        StatusName = StatusName,
                        PriorityId = t.FollowUpDocument.PriorityId,
                        PrivacyId = t.FollowUpDocument.PrivacyId,
                        CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        DueDate=t.FollowUpToDate.ToString(Constants.DATE_FORMAT),
                        TeamId=t.TeamId,
                        IsPrivate=t.IsPrivate.Value,
                        IsOverDue= DateTime.Now.Date > t.FollowUpToDate,
                        IsHasNote = t.FollowUpDocument.Note.Any(),
                        FollowUpUserRole = followUpUserRole,
                        Readonly = isReadonly,
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// Get documents with FollowUp status count.
        /// Must be created by the user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public static async Task<(int Total, int Today)> GetFollowUpCounts(long userId, long? loggedInStructureId, ExpressionBuilderFilters filter = null)
        {
            using (FollowUp item = new FollowUp())
            {

                var filterExp = filter != null ? ExpressionBuilder.GetExpression<FollowUp>(filter, ExpressionBuilderOperator.And) : null;
                var FollowUpTotal = await item.GetFollowUpCountAsync(userId, (long)loggedInStructureId, filterExp);
                var FollowUpToday = await item.GetFollowUpTodayCountAsync(userId, (long)loggedInStructureId, filterExp);
                return (FollowUpTotal, FollowUpToday);
            }
        }

        /// <summary>
        /// List all followUpStatuses
        /// </summary>
        /// <returns></returns>
        public static List<FollowUpStatusViewModel> ListAllFollowUpStatuses()
        {
            return (Configuration.EnableCaching ? new DAL.FollowUpStatus().ListWithCaching() : new DAL.FollowUpStatus().List()).Select(t => (FollowUpStatusViewModel)t).ToList();
        }

        /// <summary>
        /// List categories returns all categories DDL.
        /// </summary>
        /// <param name="language"></param>
        /// <returns></returns>
        public static List<ValueText> ListFollowUpStatuses(Language language = Language.EN)
        {
            var retValue = new List<ValueText>();
            
            var list = Configuration.EnableCaching ? new DAL.FollowUpStatus().ListWithCaching() : new DAL.FollowUpStatus().List();
            if (language == Language.EN)
            {
                retValue = list.Select(t => new ValueText { Id = t.Id, Text = t.Name , Color = GetFollowUpStatusColor(t.Id) }).ToList();
            }
            else if (language == Language.FR)
            {
                retValue = list.Select(t => new ValueText { Id = t.Id, Text = (string.IsNullOrEmpty(t.NameFr) ? t.Name : t.NameFr), Color = GetFollowUpStatusColor(t.Id) }).ToList();
            }
            else
            {
                retValue = list.Select(t => new ValueText { Id = t.Id, Text = (string.IsNullOrEmpty(t.NameAr) ? t.Name : t.NameAr), Color = GetFollowUpStatusColor(t.Id) }).ToList();
            }
            return retValue;
        }

        /// <summary>
        /// List FollowUp Security returns all FollowUp Security DDL.
        /// </summary>
        /// <param name="language"></param>
        /// <returns></returns>
        public static List<ValueText> ListFollowUpSecurityDDL(Language language = Language.EN)
        {
            var retValue = new List<ValueText>();
            
            var list =  new DAL.FollowUpSecurity().List();
            if (language == Language.EN)
            {
                retValue = list.Select(t => new ValueText { Id = t.Id, Text = t.Name }).ToList();
            }
            else if (language == Language.FR)
            {
                retValue = list.Select(t => new ValueText { Id = t.Id, Text = (string.IsNullOrEmpty(t.NameFr) ? t.Name : t.NameFr) }).ToList();
            }
            else
            {
                retValue = list.Select(t => new ValueText { Id = t.Id, Text = (string.IsNullOrEmpty(t.NameAr) ? t.Name : t.NameAr) }).ToList();
            }
            return retValue;
        }

        /// <summary>
        /// List documents with FollowUp status.
        /// Must be created by the user
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="filter"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<TransferListViewModel>)> ListFollowUpDocumentTransfers(int startIndex, int pageSize,
            ExpressionBuilderFilters filter = null, List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;
                var itemsCount = await item.FollowUpDocumentTransfersCountAsync(filterExp);
                var itemList = await item.ListFollowUpDocumentTransfersAsync(startIndex, pageSize, filterExp, sortExpression?.OrderByExpression<Transfer>());
                return (itemsCount, itemList.Select(t =>
                {

                    var categoryName = string.Empty;
                    if (t.Document.Category != null)
                    {
                        categoryName = t.Document.Category.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.Document.Category.NameAr))
                        {
                            categoryName = t.Document.Category.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.Document.Category.NameFr))
                        {
                            categoryName = t.Document.Category.NameFr;
                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructure!=null)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(toStructure))
                        {
                            toStructure = t.ToStructure.Name;

                        }
                    }
                    var toUser = t.ToUser != null ? IdentityHelperExtension.GetUser(t.ToUser.Id, Configuration.IdentityAccessToken , language) :null;
                    var dueDate = t.DueDate.HasValue ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : "";

                    return new TransferListViewModel
                    {
                        Id = t.Id,
                        Document=t.Document,
                        ReferenceNumber = t.Document.ReferenceNumber,
                        CategoryName= categoryName,
                        ToUser = toUser!=null?$"{toUser.FirstName} {toUser.LastName}":"",
                        ToUserEmail = toUser != null ? toUser.Email:"",
                        ToUserId = t.ToUserId,
                        ToStructure= toStructure,
                        ToStructureId= t.ToStructureId,
                        DueDate = dueDate,
                        DocumentDueDate=t.Document.DueDate.HasValue? t.Document.DueDate.Value.ToString(Constants.DATE_FORMAT):"",
                        StatusId = t.StatusId
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// Add User To FollowUp
        /// </summary>
        /// <param name="model"></param>
        /// <param name="userId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(bool Status, FollowUpUsersModel model, string Message)> AddUserToFollowUp(FollowUpSecurityModel model, long userId, Language language = Language.EN)
        {
            using (FollowUpUsers item = new FollowUpUsers())
            {
                (bool status, FollowUpUsersModel model, string Message) retValue = (false, new FollowUpUsersModel() { }, "");

                try
                {
                    item.FollowUpId = model.FollowUpId;
                    item.UserId = model.UserId;
                    item.StructureId = model.StructureId;
                    item.FollowUpSecurityId = model.FollowUpSecurityId;
                    item.CreatedByUserId = userId;
                    item.CreatedDate = DateTime.Now;

                    item.Insert();

                    if (item.Id != default)
                    {

                        ManageActivityLog.AddActivityLog(model.DocumentId, null, (int)ActivityLogs.AddFollowUpUser, userId, "", item.ToString(), TranslationUtility.Translate("AddFollowUpUser", language));

                        retValue = (true, (FollowUpUsersModel)item, "");
                    }

                    return retValue;
                }
                catch (Exception excption)
                {
                    retValue.Message = excption.InnerException != null ? excption.InnerException.Message : excption.Message;
                    return retValue;
                }
            }

        }

        public static async Task<(bool Status, FollowUpUsersModel model, string Message)> DeleteUserFromFollowUp(long followUpUserId,long followUpDocumentId, long userId, Language language = Language.EN)
        {
            using (FollowUpUsers item = new FollowUpUsers())
            {
                (bool status, FollowUpUsersModel model, string Message) retValue = (false, new FollowUpUsersModel() { }, "");

                try
                {
                    var oldItem = item.Find(followUpUserId);

                    item.Delete(followUpUserId);

                    ManageActivityLog.AddActivityLog(followUpDocumentId, null, (int)ActivityLogs.RemoveFollowUpUser, userId, "", oldItem.ToString(), TranslationUtility.Translate("RemoveFollowUpUser", language));

                    retValue = (true, (FollowUpUsersModel)oldItem, "");


                    return retValue;
                }
                catch (Exception excption)
                {
                    retValue.Message = excption.InnerException != null ? excption.InnerException.Message : excption.Message;
                    return retValue;
                }
            }

        }
        public static async Task<List<FollowUpResponModel>> CompleteFollowUp(List<FollowUpActionModel> followUpCompleteModels, long userId,long structureId, Language language = Language.EN)
        {
            using (FollowUp item = new FollowUp())
            {
                List<FollowUpResponModel> retValue = new List<FollowUpResponModel>();


                var followUpItems = await item.ListWithIncludeAsync(followUpCompleteModels.Select(s=>s.FollowUpId).ToList());
                for (int i = 0; i < followUpItems.Count; i++)
                {

                    try
                    {
                        if (followUpItems[i] != null && (followUpItems[i].CreatedByUserId == userId ||
                            followUpItems[i].FollowUpUsers.Any(s => s.UserId == userId && s.StructureId == structureId) &&
                            followUpItems[i].FollowUpUsers.FirstOrDefault(s => s.UserId == userId && s.StructureId == structureId).FollowUpSecurityId == (short)FollowUpRoles.Owner))
                        {
                            var documentItem = await new Document().FindAsync(followUpItems[i].DocumentId);

                            var oldestItem = JsonConvert.SerializeObject((FollowUpInfoModel)followUpItems[i]).ToString();
                            followUpItems[i].FollowUpStatusId = (long)FollowUpStatus.Completed;

                            documentItem.StatusId = (short)DocumentStatus.Completed;
                            documentItem.ClosedDate = DateTime.Now;

                            documentItem.UpdateStatusAndClosedDate();
                            followUpItems[i].Update(userId);
                            var newtItem = JsonConvert.SerializeObject((FollowUpInfoModel)followUpItems[i]).ToString();

                            ManageActivityLog.AddActivityLog(followUpItems[i].DocumentId, null, (int)ActivityLogs.CompleteFollowUp, userId, oldestItem, newtItem, TranslationUtility.Translate("CompleteFollowUp", language));
                            retValue.Add(new FollowUpResponModel()
                            {
                                Updated = true,
                                FollowUpId = followUpItems[i].Id
                            });

                        }
                        else
                        {
                            retValue.Add(new FollowUpResponModel()
                            {
                                Message = TranslationUtility.Translate("UserCantCompleteFollowUp", language),
                                Subject = followUpCompleteModels.FirstOrDefault(d => d.FollowUpId == followUpItems[i].Id).Subject,
                                Updated = false,
                                FollowUpId = followUpItems[i].Id
                            });
                        }
                    }
                    catch (Exception excption)
                    {
                        retValue.Add(new FollowUpResponModel()
                        {
                            Message = excption.InnerException != null ? excption.InnerException.Message : excption.Message,
                            Subject =  followUpCompleteModels.FirstOrDefault(d => d.FollowUpId== followUpItems[i].Id).Subject,
                            Updated = false,
                            FollowUpId= followUpItems[i].Id
                        });
                        continue;
                    }
                }


                return retValue;

            }

        }

        public static async Task<List<FollowUpResponModel>> PostponeFollowUp(FollowUpPostponeModel followUpPostponeModels, long userId, long structureId, Language language = Language.EN)
        {

            using (FollowUp item = new FollowUp())
            {
                List<FollowUpResponModel> retValue = new List<FollowUpResponModel>();


                var followUpItems = await item.ListWithIncludeAsync(followUpPostponeModels.FollowUpActionModels.Select(s => s.FollowUpId).ToList());
                for (int i = 0; i < followUpItems.Count; i++)
                {

                    try
                    {
                        if (followUpItems[i] != null && (followUpItems[i].CreatedByUserId==userId ||
                            followUpItems[i].FollowUpUsers.Any(s=>s.UserId==userId && s.StructureId== structureId) &&
                            followUpItems[i].FollowUpUsers.FirstOrDefault(s => s.UserId == userId && s.StructureId == structureId).FollowUpSecurityId == (short)FollowUpRoles.Owner))
                        {
                            var documentItem = await new Document().FindAsync(followUpItems[i].DocumentId);

                            var oldestItem = JsonConvert.SerializeObject((FollowUpInfoModel)followUpItems[i]).ToString();
                            followUpItems[i].FollowUpStatusId = (long)FollowUpStatus.Postponed;
                            switch (followUpPostponeModels.PostponeType)
                            {
                                case "ToDate":
                                    followUpItems[i].FollowUpToDate = followUpPostponeModels.ToDate;
                                    break;

                                case "ToPeriod":
                                    var totalDays = (followUpPostponeModels.Weeks * 7) + followUpPostponeModels.Days;
                                    var toDate= followUpItems[i].FollowUpToDate.AddMonths(followUpPostponeModels.Months).AddDays(totalDays);
                                    followUpItems[i].FollowUpToDate = toDate;

                                    break;
                            }


                            documentItem.StatusId = (short)DocumentStatus.Postponed;
                            documentItem.DueDate = Convert.ToDateTime(followUpItems[i].FollowUpToDate);

                            documentItem.Update();
                            followUpItems[i].Update(userId);
                            var newtItem = JsonConvert.SerializeObject((FollowUpInfoModel)followUpItems[i]).ToString();

                            ManageActivityLog.AddActivityLog(followUpItems[i].DocumentId, null, (int)ActivityLogs.PostponeFollowUp, userId, oldestItem, newtItem, TranslationUtility.Translate("PostponeFollowUp", language));
                            retValue.Add(new FollowUpResponModel()
                            {
                                Updated = true,
                                FollowUpId = followUpItems[i].Id
                            });

                        }
                        else
                        {
                            retValue.Add(new FollowUpResponModel()
                            {
                                Message = TranslationUtility.Translate("UserCantPostponeFollowUp", language),
                                Subject = followUpPostponeModels.FollowUpActionModels.FirstOrDefault(d => d.FollowUpId == followUpItems[i].Id).Subject,
                                Updated = false,
                                FollowUpId = followUpItems[i].Id
                            });
                        }
                    }
                    catch (Exception excption)
                    {
                        retValue.Add(new FollowUpResponModel()
                        {
                            Message = excption.InnerException != null ? excption.InnerException.Message : excption.Message,
                            Subject = followUpPostponeModels.FollowUpActionModels.FirstOrDefault(d => d.FollowUpId == followUpItems[i].Id).Subject,
                            Updated = false,
                            FollowUpId = followUpItems[i].Id
                        });
                        continue;
                    }
                }


                return retValue;

            }

        }

        public static async Task<List<FollowUpResponModel>> CancelFollowUp(List<FollowUpActionModel> followUpCancelModels, long userId,long structureId, Language language = Language.EN)
        {
            using (FollowUp item = new FollowUp())
            {
                List<FollowUpResponModel> retValue = new List<FollowUpResponModel>();


                var followUpItems = await item.ListWithIncludeAsync(followUpCancelModels.Select(s => s.FollowUpId).ToList());
                for (int i = 0; i < followUpItems.Count; i++)
                {

                    try
                    {
                        if (followUpItems[i] != null && (followUpItems[i].CreatedByUserId == userId ||
                            followUpItems[i].FollowUpUsers.Any(s => s.UserId == userId && s.StructureId == structureId) &&
                            followUpItems[i].FollowUpUsers.FirstOrDefault(s => s.UserId == userId && s.StructureId == structureId).FollowUpSecurityId == (short)FollowUpRoles.Owner))
                        {
                            var documentItem = await new Document().FindAsync(followUpItems[i].DocumentId);

                            var oldestItem = JsonConvert.SerializeObject((FollowUpInfoModel)followUpItems[i]).ToString();
                            followUpItems[i].FollowUpStatusId = (long)FollowUpStatus.Canceled;
                            documentItem.StatusId = (short)DocumentStatus.Canceled;

                            documentItem.UpdateStatusAndClosedDate();
                            followUpItems[i].Update(userId);
                            var newtItem = JsonConvert.SerializeObject((FollowUpInfoModel)followUpItems[i]).ToString();

                            ManageActivityLog.AddActivityLog(followUpItems[i].DocumentId, null, (int)ActivityLogs.CancelFollowUp, userId, oldestItem, newtItem, TranslationUtility.Translate("CancelFollowUp", language));
                            retValue.Add(new FollowUpResponModel()
                            {
                                Updated = true,
                                FollowUpId = followUpItems[i].Id
                            });

                        }
                        else
                        {
                            retValue.Add(new FollowUpResponModel()
                            {
                                Message = TranslationUtility.Translate("UserCantCancelFollowUp", language),
                                Subject = followUpCancelModels.FirstOrDefault(d => d.FollowUpId == followUpItems[i].Id).Subject,
                                Updated = false,
                                FollowUpId = followUpItems[i].Id
                            });
                        }
                    }
                    catch (Exception excption)
                    {
                        retValue.Add(new FollowUpResponModel()
                        {
                            Message = excption.InnerException != null ? excption.InnerException.Message : excption.Message,
                            Subject = followUpCancelModels.FirstOrDefault(d => d.FollowUpId == followUpItems[i].Id).Subject,
                            Updated = false,
                            FollowUpId = followUpItems[i].Id
                        });
                        continue;
                    }
                }


                return retValue;

            }
        }
        public static List<FollowUpUsers> GetFollowUpUsers(long followUpId)
        {
            using (FollowUpUsers item = new FollowUpUsers())
            {
                return  item.GetFollowUpUsers(followUpId);
            }
        }

        /// <summary>
        /// Edit User To FollowUp
        /// </summary>
        /// <param name="model"></param>
        /// <param name="userId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static (bool Status, FollowUpUsersModel model, string Message) UpdateFollowUpUserRole(FollowUpSecurityModel model, long userId, Language language = Language.EN)
        {

            (bool status, FollowUpUsersModel model, string Message) retValue = (false, new FollowUpUsersModel(), "");

            try
            {
                var followUp = new FollowUp().Find(model.FollowUpId);
                if (followUp == null)
                    return (false, null, TranslationUtility.Translate("InvalidFollowUp", language));

                if (followUp.CreatedByUserId != userId)
                    return (false, null, TranslationUtility.Translate("YouAreNotFollowUpOwner", language));

                if (model.Id == default)
                    return (false, null, TranslationUtility.Translate("InvalidFollowUpUserId", language));

                var item = new FollowUpUsers().Find(model.Id);
                if (item == null)
                    return (false, null, TranslationUtility.Translate("FollowUpUserNotFound", language));
                
                if (item.UserId == followUp.CreatedByUserId)
                    return (false, null, TranslationUtility.Translate("CantChangeFollowUpCreatorRole", language));

                var oldestItem = (FollowUpUsersModel)item;
                item.FollowUpSecurityId = model.FollowUpSecurityId;
                item.Update();

                ManageActivityLog.AddActivityLog(
                    model.DocumentId,
                    null,
                    (int)ActivityLogs.EditFollowUpUserRole,
                    userId,
                    oldestItem.ToString(),
                    item.ToString(),
                    TranslationUtility.Translate("EditFollowUpUserRole", language)
                );

                return (true, (FollowUpUsersModel)item, "");
            }
            catch (Exception exception)
            {
                return (false, null, exception.InnerException?.Message ?? exception.Message);
            }



        }
        public static async Task<(int, List<FollowUpPanelListViewModel>)> ListFollowUpPanelItems(int startIndex, int pageSize, long followupId, Language language = Language.EN)
        {
            using (FollowUpPanel item = new FollowUpPanel())
            {
                var itemList = await item.ListFollowUpPanelItemsAsync(startIndex, pageSize, new List<long> { followupId });
                return (itemList.count, itemList.list.Select(t => (FollowUpPanelListViewModel)t).ToList());
            }
        }
        public static async Task<(int, List<FollowUpPanelListViewModel>)> ListFollowUpPanel(int startIndex, int pageSize, long userId, long structureId,
          ExpressionBuilderFilters filter = null, List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            using (FollowUpPanel item = new FollowUpPanel())
            {
                var filterExp = filter != null ? ExpressionBuilder.GetExpression<FollowUp>(filter, ExpressionBuilderOperator.And) : null;
                var followUpIds = await new FollowUp().ListFollowUpIdsAsync(startIndex, pageSize, userId, structureId, filterExp);
                var itemList =  await item.ListFollowUpPanel(startIndex, pageSize, followUpIds, sortExpression.OrderByExpression<FollowUpPanel>());
                return (itemList.count, itemList.list.Select(t =>(FollowUpPanelListViewModel)t).ToList());
            }
        }
        /// <summary>
        /// Add FollowUp panel
        /// </summary>
        /// <param name="model"></param>
        /// <param name="userId"></param>
        /// <param name="roleId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <returns></returns>
        public static async Task<(bool Status, long FollowUpId, string Message)> CreateFollowUpPanel(FollowUpPanelCreateModel model, long userId, Language language = Language.EN)
        {
            using (FollowUpPanel item = new FollowUpPanel())
            {
                (bool status, long FollowUpId, string Message) retValue = (false, 0, "");
                try
                {
                        var followUpPanelItem = await item.Insert(new FollowUpPanel()
                        {
                            FollowUpId = model.FollowUpId,
                            TransferredTo = model.TransferredTo,
                            TransferredDate=model.TransferredDate,
                            CreatedByUserId = userId,
                            CreatedDate = DateTime.Now,
                            Event= model.Event,
                            EventDate= model.EventDate,
                            FollowUpPanelStatus=model.FollowUpPanelStatus,
                            ModifiedByUSerId=userId,
                            ModifiedDate=DateTime.Now,
                            Notes=model.Notes,
                            ResponsibleUser=model.ResponsibleUser,
                        });
                            return (true, followUpPanelItem.Id, "");
                }
                catch (Exception excption)
                {
                    retValue.Message = excption.InnerException != null ? excption.InnerException.Message : excption.Message;
                    return retValue;
                }
            }

        }

        public static async Task<bool> CheckIfDocumentHasFollowUpAccess(long userId, long? structureId, long documentId)
        {
            using (FollowUp item = new FollowUp())
            {
                return await item.CheckIfDocumentHasFollowUpAccess(userId, structureId, documentId);
            }
        }
        #endregion
        #region Private Methods
        private static async Task<bool> CheckIfExistsForSameUser(long documentId, long userId, long structureId)
        {
            using (FollowUp item = new FollowUp())
            {
                return await item.CheckIfExistsForSameUser(documentId, userId, structureId);
            }
        }

        public static async Task<bool> CheckIfUserCanAccess(long id, long userId,long structureId)
        {
            using (FollowUp item = new FollowUp())
            {
                return await item.CheckIfUserCanAccess(id,userId, structureId);
            }
        }

        private static string GetFollowUpStatusColor(long statusId) 
        {
            switch (statusId)
            {
                case (long)FollowUpStatus.InProgress:
                    return "#27c24c";
                
                case (long)FollowUpStatus.Overdued:
                    return "#F44336";
                
                case (long)FollowUpStatus.Canceled:
                    return "#ec2626";
                    
                case (long)FollowUpStatus.Completed:
                    return "#ec2626";
                    
                case (long)FollowUpStatus.Postponed:
                    return "#FFA500";

                default: return "#27c24c";


            }
        }
        #endregion


    }
}
