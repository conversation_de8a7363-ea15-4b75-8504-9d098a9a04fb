﻿import Intalio from './common.js'
import { User } from './lookup.js'
import { IdentityService, Helper } from './lookup.js'
function getStructuresSignatures() {
    Common.ajaxGet('/StructureSignature/GetData', null,
        function (data) {
            data.data.forEach(signature => {
                $("input[type=radio][id='radio" + signature.structureId + "_" + signature.templateId + "']").prop('checked', true);
            });
            
        }, null, false);
}
function fetchSignaturesForStructure(structures, signatures) {

    structures.forEach(struct => {
            const slider = document.getElementById(`slider${struct.id}`);
            const signatureDiv = document.createElement('div');
            signatureDiv.classList.add('bg-white');
            signatureDiv.innerHTML = `
                    <div class="form-group">
                        <div class="input-group">
                            <div class="radio c-radio">
                                <label class="col-form-label" for="radio${struct.id}_${-1}">
                                <input id="radio${struct.id}_${-1}" class="form-control signRadio" type="radio" name="structure${struct.id}" value="${-1}">
                                    <span class="fa fa-circle"></span>

                                </label>
                            </div>
                        </div>
                    </div>
                    <label for="radio${struct.id}_${-1}">
                    <img id="img${-1}" src="../images/no-image-icon-32.png" alt="none" style="height: 100px;" />
                    </label>
                `;

            slider.appendChild(signatureDiv);
            signatures.forEach(signature => {
                const signatureDiv = document.createElement('div');
                signatureDiv.classList.add('bg-white');

                signatureDiv.innerHTML = `
                    <div class="form-group">
                        <div class="input-group">
                            <div class="radio c-radio">
                                <label class="col-form-label" for="radio${struct.id}_${signature.id}">
                                <input id="radio${struct.id}_${signature.id}" class="form-control signRadio" type="radio" name="structure${struct.id}" value="${signature.id}">
                                    <span class="fa fa-circle"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <label for="radio${struct.id}_${signature.id}">
                    <img id="img${signature.id}" src="${window.DSURL}/api/signature/template/${signature.id}/image?token=${IdentityAccessToken}" alt="${signature.name}" style="height: 100px;" />
                    </label>
                `;

                slider.appendChild(signatureDiv);
               
            });
        $(slider).slick({
            infinite: false,
            slidesToShow: 5,
            slidesToScroll: 1,
            autoplay: false,
            arrows: true,
            prevArrow: '<button type="button" class="slick-prev">&#9664;</button>',
            nextArrow: '<button type="button" class="slick-next">&#9654;</button>'
        });
        
    });
            $('.signatures-slider .radio').on('click', function () {
                $(this).find('input').prop('checked', true)
            });

            
            getStructuresSignatures();

    $(window).on('resize', function () {
        $('.slick-slider').each(function () {
            $(this).slick('setPosition');
        });
    });
}


function deleteTeam(id, self) {
    try {
        Common.showConfirmMsg(Resources.DeleteTeamConfirmation, function () {
          
            Common.ajaxDelete('/Teams/Delete', { 'id': id, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val() }, function (data) {
                if (data && data.success) {
                    Common.showScreenSuccessMsg();
                    $(self.refs['grdTeamItems']).DataTable().ajax.reload();
                } else if (data && !data.success && data.message != "") {
                    Common.alertMsg(data.message);
                } else {
                    Common.showScreenErrorMsg();
                }
                    
                }, null, false);
            
        });
    } catch (ex) {
        
    }
}
function initializeSelect2(self) {
    var headers = {};
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    var structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
    var structureName = new CoreComponents.Lookup.Structure().getWithCaching(structureId, window.language);
    var usersUrl = location.origin + '/User/GetUsersStructuresFromCTS';
    var x = $("#teams-select");
    var y = $("#UserListError");
    x.select2({
        minimumInputLength: 0,
        allowClear: false,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        width: "100%",
        ajax: {
            delay: 400,
            url: usersUrl,
            type: "POST",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                return {
                    "searchUser": term.term ? term.term : "",
                    "structureType": "Both",
                    'fromSendingandReceiving': false,
                    'filterByLoggedInStructure': true
                };
            },
            processResults: function (data, term) {
                var listitemsMultiList = [];
                $.each(data, function (key, val) {
                    if (val.userStructure != null && val.userStructure.length > 0) {
                        $.each(val.userStructure, function (key, userStructureVal) {
                            var item = {};
                            item.id = "User_" + userStructureVal.structureId + "_" + userStructureVal.userId;
                            if (window.language === "ar") {
                                var structureName = userStructureVal.structure.nameAr;
                            }
                            else if (window.language === "fr") {
                                var structureName = userStructureVal.structure.nameFr;
                            }
                            else {
                                var structureName = userStructureVal.structure.name;
                            }
                            var userName = userStructureVal.user.firstname + " " + userStructureVal.user.lastname;
                            item.text = structureName + " / " + userName;
                            item.icon = "fa fa-user-o";
                            item.isStructure = false;
                            item.dataId = userStructureVal.userId;
                            item.structureId = userStructureVal.structureId;
                            listitemsMultiList.push(item);
                        });
                    }
                });
                return {
                    results: listitemsMultiList
                };
            }
        },
        sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
    });
}
function submitTeam(self, closeModal) {
    var $form = $(self.refs['formIndexPost']);
    $form.parsley().reset();

    var TeamName = $(self.refs['teamName']).val();
    var userObj = $(self.refs['teams-select']).select2('data');
    var userIds = userObj.map(function (user) {
        return user.id.split('_')[2]; 
    });

    var structureIds = userObj.map(function (user) {
        return user.id.split('_')[1]; 
    });


    var isValid = $form.parsley().validate();
    if (isValid) {
        let params = {
            'Name': TeamName,
            'UserIds': userIds,
            'StructureIds': structureIds,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        };

        if (self.model.TeamId) {
            params.Id = self.model.TeamId;
        }

        var btn = $(self.refs['saveTeamBtn']);
        btn.button('loading');
        var btnClose = $(self.refs['cancelBtn']);
        var btnCloseX = $(self.refs['close"']);
        btnClose.attr('disabled', 'disabled');
        btnCloseX.attr('disabled', 'disabled');
    

        Common.ajaxPost('/Teams/Index', params, function (data) {
            if (data.message !== null && data.message !== "") {
                setTimeout(function () {
                    Common.alertMsg(data.message);
                }, 300);
            }
            else {
                Common.showScreenSuccessMsg();
                self.model.TeamId = data.id;
                $(self.refs['teamModaltitle']).html(Resources.Edit);
                $("#grdTeamItems").DataTable().ajax.reload();
                if (closeModal) {
                    $(self.refs['cancelBtn']).trigger("click");
                }
            }

            btn.button('reset');
            btnClose.removeAttr('disabled');
            btnCloseX.removeAttr('disabled');
            

           
        }, function () {

            btn.button('reset');
            btnClose.removeAttr('disabled');
            btnCloseX.removeAttr('disabled');
            Common.alertMsg(Resources.TeamNameAlreadyExist);
        }, {
            contentType: 'application/json',
            dataType: 'json'
        });
    }
}
function openTeamWindow(row, self) {
    var wrapper = $(".modal-window");
    let tr = $(row).closest('tr');
    let srow = $(self.refs['grdTeamItems']).DataTable().row(tr);
    var team = srow.data();

    if (team) {

        $(self.refs['teamModaltitle']).html("Edit <span class='read-mode-team'>" + team.name + "</span>");

        self.model.TeamId = team.id;
        $.ajax({
            url: '/TeamUsers/GetTeamUsers',
            method: 'GET',
            data: { teamId: team.id },
            success: function (response) {
                if (response && response.length > 0) {
                    team.teamUsers = response;
                    setData(team, self);
                }
            },
            error: function () {
                console.error('Error fetching users for the team');
            }
        });
    } else {
        $(self.refs['teamModaltitle']).html(Resources.New);
        self.model.TeamId = '';
    }

    $(self.refs['teamModal']).off("hidden.bs.modal");
    $(self.refs['teamModal']).off("shown.bs.modal");
    $(self.refs['teamModal'])
        .off("hidden.bs.modal")
        .on("hidden.bs.modal", function () {
            self.model.TeamId = '';
            $(self.refs['formIndexPost']).parsley().reset();
            $(self.refs['userPostMessage']).html('');
        });

    $(self.refs['teamModal']).modal('show');
    
}


function setData(data, self) {

    if (data.name) {
        $(self.refs['teamName']).val(data.name);
    } 

    if (data.teamUsers && data.teamUsers.length > 0) {
        for (var i = 0; i < data.teamUsers.length; i++) {
            var userOption = {
                id: "User_" + data.teamUsers[i].structureId + "_" + data.teamUsers[i].userId,
                text: data.teamUsers[i].structure.name + " / " + data.teamUsers[i].user.firstname + " " + data.teamUsers[i].user.lastname
            };

            var newOption = new Option(userOption.text, userOption.id, true, true);
            $(self.refs['teams-select']).append(newOption).trigger('change');
        }

        $(self.refs['teams-select']).val(data.teamUsers.map(function (user) {
            return "User_" + user.structureId + "_" + user.userId;
        })).trigger('change');
    }
}


function format(row) {
    let teamId = row.data().id; 
    let userListHtml = ''; 

    $.ajax({
        url: `/TeamUsers/GetTeamUsers?teamId=${teamId}`, 
        method: 'GET',
        success: function (response) {
            if (response && response.length > 0) {
                userListHtml = '<table style="width:100%" cellspacing="0" border="0">';
                userListHtml += '<thead><tr><th style="width: 10%;padding:5px">' + Resources.Users +'</th></tr></thead>';
                userListHtml += '<tbody>';
                
                response.forEach(item => {
                    if (item.user && item.user.firstname && item.user.lastname) {
                        userListHtml += '<tr>' +
                            `<td style="width: 90%;padding:5px"> ${item.user.firstname} ${item.user.lastname}</td>` +
                            '</tr>';
                    } else {
                        userListHtml += '<tr>' +
                            '<td style="width: 10%;padding:5px">' + Resources.UnknownUser +'</td>' +
                            '</tr>';
                    }
                });

                userListHtml += '</table>';
            } else {
                userListHtml = '<table style="width:100%" cellspacing="0" border="0">' +
                    '<tr>' +
                    '<td style="padding:5px;">' + Resources.NoUsersAvailable +'</td>' +
                    '</tr>' +
                    '</table>';
            }

            if (row.child) {
                row.child(userListHtml).show();
            }
        },
        error: function (xhr, status, error) {
            console.error("Error fetching users for team: " + error);
            if (row.child) {
                row.child('<p>Error loading users</p>').show(); 
            }
        }
    });
}
function resetForm(self) {
    $(self.refs['teamName']).val('');

    $(self.refs['teams-select']).empty().trigger('change');

    $(self.refs['userPostMessage']).html('');

    self.model.TeamId = '';
}
class Profile extends Intalio.Model {
    constructor() {
        super();
       this.TeamId = null;
       
    }
}

var self;

class ProfileView extends Intalio.View {
    constructor(element, model) {
        super(element, "profile", model);
    }
    render() {
        self = this;
        Common.gridCommon();
        $('#teams-select').select2({
            templateResult: formatOption,
            templateSelection: formatSelection,
            closeOnSelect: false
        });
        initializeSelect2(self);
        function formatOption(option) {
            return $('<div class="checkbox c-checkbox"><label><input type="checkbox" id="checkbox' + option.id + '"  name="member"/>  <span class="fa fa-check"></span>' + option.text + ' </label></div>');
        }

        function formatSelection(option) {
            return option.text || option.id;
        }

        var table = $(self.refs["grdTeamItems"]).DataTable({
            
            processing: true,
            ordering: true,
            serverSide: true,
            pageLength: 10,
            ajax: {

                url: "/Teams/List",
                type: "GET",
                datatype: "json",
                data: function (d) {
                    return {
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val(),
                        draw: d.draw,
                        start: d.start,
                        length: d.length
                    };
                }
            },
            order: [],
            columns: [
                {
                    className: 'details-control width20',
                    orderable: false,
                    data: null,
                    defaultContent: '',
                    width: '16px'
                },
                {
                    title: Resources.Teams , data: "name", orderable: false, className: "min-max-width-50-250"
                },
                { title: "Id", data: "id", visible: false, orderable: false },
              
                {
                    className: "text-right width20 minwidth55",
                    autoWidth: false,
                    bAutoWidth: false,
                    width: "16px",
                    orderable: false,
                    sortable: false,
                    render: function (data, type, full, meta) {
                        var html = "";
                        if (true) {
                            let btn = document.createElement("button");
                            btn.setAttribute("class", "btn btn-xs mr-sm btn-warning edit");
                            btn.setAttribute("title", Resources.Edit );
                            btn.setAttribute("clickAttr", "openTeamWindow(this)");
                            btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                            html += btn.outerHTML;

                            if (true) {
                            
                                    btn = document.createElement("button");
                                    btn.setAttribute("class", "btn btn-xs btn-danger delete");
                                    btn.setAttribute("title", Resources.Delete );
                                    btn.setAttribute("clickAttr", "deleteTeam(" + full.id + ")");
                                    btn.innerHTML = "<i class='fa fa-trash fa-white'/>";
                                    html += btn.outerHTML;
                                
                            }
                        }
                        return html;
                    }
                }
            ],
            fnInitComplete: function (settings, json) {
                $('[data-toggle="tooltip"]').tooltip();
            },
            dom: '<"html5buttons "B>ltrpi',
            buttons: [
                {
                    className: 'btn-sm btn-primary btnNewTeam',
                    text: '<span class="fa fa-plus-circle mr-sm"><span> <span style="font-family:sans-serif">' + Resources.New +'</span>'
                }
            ]
        });


        $(self.refs["grdTeamItems"]).find('td').tooltip({
            delay: 0,
            track: true,
            fade: 250
        });
        function closeModal() {
            $('#teamModal').modal('hide');
        }
        $(self.refs['saveTeamBtn']).on('click', function (Team) {
                if (window.EnableConfirmationMessage === "True") {
                    Common.showConfirmMsg(Resources.submitTeamConfirmation, function () {
                        submitTeam(self, closeModal);
                    });
                } else {
                    submitTeam(self, closeModal);
                }
            
        });
        $('.btnNewTeam').click(function () {
            openTeamWindow(null, self);
        });

        $(self.refs['grdTeamItems']).on('click', 'td.details-control', function () {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            } else {
                format(row); 
                row.child.show(); 
                tr.addClass('shown');
            }
        });
        $(self.refs['grdTeamItems']).on('click', ".edit", function () {
            var onclick = $(this).attr("clickattr").slice(0, -1) + ",self)";
            eval(onclick);
        });
        $(document).on("click", ".team-row .details-control", function () {
            $(this).parent().next(".expandable-content").toggle();
        });
        $("#cancelBtn").click(function () {
            resetForm(self);
            $(self.refs['formIndexPost']).parsley().reset();
            $("#teamModal").modal('hide');
        });
        $("#close").click(function () {
            resetForm(self);
            $(self.refs['formIndexPost']).parsley().reset();
            $("#teamModal").modal('hide');
        });
        $(self.refs['teamModal']).on('hidden.bs.modal', function () {
            resetForm(self);  
            $(self.refs['formIndexPost']).parsley().reset();
        });
        $(self.refs['grdTeamItems']).on('click', ".delete", function () {
            var onclick = $(this).attr("clickattr").slice(0, -1) + ",self)";
            eval(onclick);
        });
        var userObj = new IdentityService().getFullUser(self.model.userId);
        var structureObj = new IdentityService().getFullStructure(userObj.defaultStructureId, window.language);

        var lang = window.language.toLowerCase();
        var name = structureObj.name; 

        if (lang === "ar" || lang === "fr") {
            var attr = structureObj.attributes.find(a => a.text === (lang === "ar" ? "NameAr" : "NameFr"));
            if (attr && attr.value) {
                name = attr.value;
            }
        }

        $("#email").text(userObj.email);
        $("#userName").text(userObj.username);
        $("#fullName").text(userObj.fullName);
        $("#role").text(userObj.role.name);
        $("#structure").text(name);
        $("#dateOfBirth").text(userObj.email);
       

        $.ajax({
            method: "GET",
            url: `${window.DSURL}/api/signature/template`,
            headers: {
                "Authorization": "Bearer " + IdentityAccessToken
            },
            success: function (signatures) {
        $.ajax({
            method: "GET",
            url: 'User/GetAllUserStructuresWithoutUserName',
            dataType: 'json',
            data: {
                text: "",
                //name: userObj.username
            },
            success: function (structures) {
                var listitemsMultiList = [];
                $.each(structures, function (key, val) {
                    var structureName = "";
                    var structureItem = val.map(function (item) {
                        return item.structure;
                    });


                    if (structureItem) {
                        $.each(structureItem, function (key, val) {
                            if (window.language === "ar" && val.nameAr !="")
                                structureName = val.nameAr;
                            else
                                structureName = val.name;
                            
                            if (val.attributes != null && val.attributes.length > 0) {
                                var attributeLang = $.grep(val.attributes, function (e) {
                                    return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                                });
                                if (attributeLang.length > 0) {
                                    structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                                }
                            }


                            var item = {};
                            item.id = val.id;
                            item.text = structureName;
                            item.isStructure = true;
                            item.dataId = val.id;
                            listitemsMultiList.push(item);
                        });


                    }
                });
                const container = document.getElementById('signatureContainer');

                listitemsMultiList.forEach(structure => {
                    const div = document.createElement('div');
                    div.classList.add('bg-white', 'form-group');

                    div.innerHTML = `
                                <div class="panel-heading clickable" data-toggle="collapse" data-target="#panel${structure.id}" aria-expanded="true">
                            ${structure.text}
                            <span id="collapseInboxIcon" class="pull-right"><i class="fa fa-angle-down fa-lg"></i></span>
                        </div>
                        <div id="panel${structure.id}" class="panel-collapse collapse" aria-expanded="true">
                            <div class="panel-body">
                                <div class="signatures-slider" id="slider${structure.id}">
                                </div>
                            </div>
                        </div>
                            `;
                    container.appendChild(div);
                    
                });
                fetchSignaturesForStructure(listitemsMultiList, signatures);
            }

        });
            }, error: function () {
                    console.error(`Failed to load signatures`);
                }
        });
        $('#btnSubmit').on('click', function () {

            var $form = $('#formPost');
            $form.parsley().reset();
            var isValid = $form.parsley().validate();
            if (isValid) {

                const selectedImages = [];
                $("input[type='radio'].signRadio:checked").each(function () {
                    const StructureId = $(this).attr('name').replace('structure', '');
                    const TemplateId = $(this).val(); 
                    selectedImages.push({ StructureId, TemplateId });
                });

                let params = {
                    'model': selectedImages,
                };
                Common.ajaxPost('/StructureSignature/Save', params, function (data) {
                    if (data.message !== null && data.message !== "" && data.message !== undefined) {
                        setTimeout(function () {
                            Common.alertMsg(data.message);
                        }, 300);
                    }
                    else {
                        Common.showScreenSuccessMsg();
                    }
                }), function () {  Common.showScreenErrorMsg(); }, false;
            }
        });
    }
}
export default { Profile, ProfileView };
