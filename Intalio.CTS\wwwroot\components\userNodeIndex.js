import Intalio from './common.js'
import { Categories, CategoryModel, Structure, IdentityService, User } from './lookup.js'
class UserNodeIndex extends Intalio.Model
{
    constructor()
    {
        super();
        this.roles = [];
    }
}


function sortTableAlphabetically() {
    var tableData = gColumnTable.rows().data().toArray();

    var orderedItems = tableData.filter(item => item.order !== null && item.order !== "");
    var unorderedItems = tableData.filter(item => item.order === null || item.order === "");

    orderedItems.sort(function (a, b) {
        return Number(a.order) - Number(b.order);
    });

    unorderedItems.sort(function (a, b) {
        return a.name.localeCompare(b.name);
    });

    var sortedData = [...orderedItems, ...unorderedItems];

    gColumnTable.clear();
    gColumnTable.rows.add(sortedData).draw();
}

var UserNodeInheritProperties = (function (E)
{
    let structures = null;
    E = {};
    E.Draft = "Draft";
    E.Inbox = "Inbox";
    E.Completed = "Completed";
    E.MyRequests = "MyRequests";
    E.Sent = "Sent";
    E.Closed = "Closed";
    E.Filters = function (name)
    {
        switch (name)
        {
            case E.Draft:
                return ["FromDate", "ToDate", "Category", "Subject"];
            case E.Inbox:
                return ["ReferenceNumber", "FromDate", "ToDate", "Category", "Subject", "Purpose", "Priority", "Privacy", "Read", "Locked", "OverDue", "Structure", "User"];
            case E.Completed:
            case E.Sent:
                return ["ReferenceNumber", "FromDate", "ToDate", "Category", "Subject", "Purpose", "Priority", "Privacy", "Structure", "User"];
            case E.MyRequests:
                return ["ReferenceNumber", "FromDate", "ToDate", "Category", "Subject", "OverDue"];
            case E.Closed:
                return ["ReferenceNumber", "FromDate", "ToDate", "Category", "Subject"];
        }
    };
    E.Columns = function (name)
    {
        switch (name)
        {
            case E.Draft:
                return ["Category", "Subject", "ReferenceNumber", "SendingEntity", "ReceivingEntity", "CreatedDate", "ModifiedDate", "Custom"];
            case E.MyRequests:
                return ["Category", "Subject", "ReferenceNumber", "SendingEntity", "ReceivingEntity", "CreatedDate", "DueDate", "Custom"];
            case E.Inbox:
                return ["Category", "ReferenceNumber", "Subject", "From", "TransferDate", "CreatedDate", "SendingEntity", "ReceivingEntity", "Purpose", "Priority", "Privacy", "CreatedBy", "OpenedDate", "DueDate", "Custom"];
            case E.Completed:
                return ["Category", "ReferenceNumber", "Subject", "From", "TransferDate", "Owner", "SendingEntity", "ReceivingEntity", "Purpose", "Priority", "Privacy", "CreatedBy", "OpenedDate", "ClosedDate", "Custom"];
            case E.Sent:
                return ["Category", "ReferenceNumber", "Subject", "To", "TransferDate", "Owner", "SendingEntity", "ReceivingEntity", "Purpose", "Priority", "Privacy", "CreatedBy", "OpenedDate", "ClosedDate", "Custom"];
            case E.Closed:
                return ["Category", "Subject", "ReferenceNumber", "SendingEntity", "ReceivingEntity", "CreatedDate", "ClosedDate", "Custom"];
        }
    };
    E.Conditions = function (name)
    {
        switch (name)
        {
            case E.Draft:
            case E.MyRequests:
                return ["Category", "DocumentCreatedDate"];
            case E.Inbox:
                return ["Category", "DocumentCreatedDate", "TransferCreatedDate", "Read", "Locked", "OverDue", "FromStructure", "FromUser", "IsSigned"];
            case E.Completed:
                return ["Category", "DocumentCreatedDate", "TransferClosedDate"];
            case E.Sent:
                return ["Category", "DocumentCreatedDate", "TransferClosedDate", "IsClosedTransfer", "IsCompletedFollowUp"];
            case E.Closed:
                return ["Category", "DocumentCreatedDate", "DocumentCompletedDate"];
        }
    };
    E.getFilters = function (name)
    {
        var filterList = E.Filters(name);
        var options = new Array();
        filterList.forEach(function (filter, index)
        {
            options.push({
                id: filter,
                text: Resources[filter] || filter
            });
        });
        return options;
    };
    E.userGetColumns = function (name)
    {
        var columnList = E.Columns(name);
        var options = new Array();
        columnList.forEach(function (column, index)
        {
            options.push({
                id: column,
                text: Resources[column] || column
            });
        });
        return options;
    };
    E.userInitColumns = function (name)
    {
        $("#userColumnDiv").removeClass("col-md-3").addClass("col-md-6");
        if ($("#userColumnNumberDiv").length > 0)
        {
            if ($("#userOrderColumnContainer").hasClass("col-md-3"))
            {
                $("#userOrderColumnContainer").attr("class", "").addClass("col-md-6");
            } else
            {
                $("#userOrderColumnContainer").attr("class", "").addClass("col-md-3");
            }
        }
        $("#userColumnNumberDiv").remove();
        $("#userColumnFunctionDiv").remove();
        $("#tooltipCustom").remove();
        switch (name)
        {
            case "Custom":
                $("#userColumnDiv").removeClass("col-md-6").addClass("col-md-3");
                $("#ColumnLegend").append(' <label id="tooltipCustom" class="control-label"><i class="fa fa-info-circle font-15 infoDivIcon"></i><div class="infoDiv font-13" style="opacity:0;width: 250px;left: auto!important;right: auto!important;">' +
                    Resources.JsFunctionName + ' ' + Resources.MustReturnValidString + '</div></label>');
                if ($("#userOrderColumnContainer").hasClass("col-md-3"))
                {
                    $("#userOrderColumnContainer").removeClass("col-md-3").addClass("col-md-2");
                    $("#userColumnDiv").after('<div class="col-md-2" id="userColumnNumberDiv"><div class="form-group"><input type="text" required="required" id="txtColumnNameUserName" data-parsley-group="columns" placeholder="' +
                        Resources.Name + '" class="form-control" autocomplete="off" tabindex="14" data-parsley-length="[1, 50]" maxlength="50" aria-hidden="true"></div></div><div class="col-md-2" id="userColumnFunctionDiv"><div class="form-group"><input type="text" required="required" id="txtColumnFunctionUserNode" data-parsley-group="columns" placeholder="' +
                        Resources.JsFunctionName + '" class="form-control" autocomplete="off" tabindex="14" data-parsley-length="[1, 50]" maxlength="50" aria-hidden="true"></div></div>');
                } else
                {
                    $("#userOrderColumnContainer").removeClass("col-md-6").addClass("col-md-3");
                    $("#userColumnDiv").after('<div class="col-md-3" id="userColumnNumberDiv"><div class="form-group"><input type="text" required="required" id="txtColumnNameUserName" data-parsley-group="columns" placeholder="' +
                        Resources.Name + '" class="form-control" autocomplete="off" tabindex="14" data-parsley-length="[1, 50]" maxlength="50" aria-hidden="true"></div></div><div class="col-md-3" id="userColumnFunctionDiv"><div class="form-group"><input type="text" required="required" id="txtColumnFunctionUserNode" data-parsley-group="columns" placeholder="' +
                        Resources.JsFunctionName + '" class="form-control" autocomplete="off" tabindex="14" data-parsley-length="[1, 50]" maxlength="50" aria-hidden="true"></div></div>');
                }
                break;
        }
    };
    E.userGetConditions = function (name)
    {
        var conditionsList = E.Conditions(name);
        var options = new Array();
        conditionsList.forEach(function (condition, index)
        {
            options.push({
                id: condition,
                text: Resources[condition] || condition
            });
        });
        return options;
    };
    E.initConditionValue = function (name)
    {
        $("#valueContainer").empty();
        var html = "";
        switch (name)
        {
            case "Category":
                html = '<div class="form-group" id="userConditionCategoryContainer"><select id="usercmbConditionUserNodeCategory" data-parsley-group="conditions" ' +
                    'tabindex="16" required="required" data-parsley-errors-container="#usercmbConditionUserNodeCategoryError" class="form-control"></select><div id="usercmbConditionUserNodeCategoryError"></div></div>';
                $("#valueContainer").html(html);
                $('#usercmbConditionUserNodeCategory').select2({
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    width: "100%",
                    data: new Categories().get(window.language),
                    allowClear: false,
                    placeholder: Resources.Category,
                    multiple: "multiple",
                    dropdownParent: $('#userConditionCategoryContainer')
                });
                $("#usercmbConditionUserNodeCategory").val('').trigger('change');
                break;
            case "DocumentCreatedDate":
            case "DocumentCompletedDate":
            case "TransferCreatedDate":
            case "TransferClosedDate":
                html = '<div class="row"><div class="col-md-4"><div class="form-group" id="userConditionDateContainer"><select id="usercmbConditionUserNodeDate" data-parsley-group="conditions" ' +
                    'tabindex="16" required="required" data-parsley-errors-container="#usercmbConditionUserNodeDateError" class="form-control"><option value="Year">' + Resources.Year + '</option>' +
                    '<option value="Month">' + Resources.Month + '</option><option value="Day">' + Resources.Day + '</option></select><div id="usercmbConditionUserNodeDateError"></div></div></div>' +
                    '<div class="col-md-3"><div class="form-group" id="userConditionDateSignContainer"><select id="usercmbConditionUserNodeDateSign" data-parsley-group="conditions" ' +
                    'tabindex="17" required="required" data-parsley-errors-container="#usercmbConditionUserNodeDateSignError" class="form-control"><option value="<"><</option><option value="<="><=</option></select><div id="usercmbConditionUserNodeDateSignError"></div></div></div>' +
                    '<div class="col-md-5"><div class="form-group"><input type="number" required="required" min="1" id="userTxtDateNumber" data-parsley-group="conditions" placeholder="' + Resources.Number + '" class="form-control" max="100" autocomplete="off" tabindex="18" aria-hidden="true"></div></div></div>';
                $("#valueContainer").html(html);
                $('#usercmbConditionUserNodeDate').select2({
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    width: "100%",
                    allowClear: false,
                    dropdownParent: $('#userConditionDateContainer')
                });
                $('#usercmbConditionUserNodeDateSign').select2({
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    width: "100%",
                    allowClear: false,
                    dropdownParent: $('#userConditionDateSignContainer')
                });
                break;
            case "Read":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="userChkConditionRead" tabindex="16" type="checkbox" name="Read" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.Read + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "Locked":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="userChkConditionLocked" tabindex="16" type="checkbox" name="Locked" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.Locked + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "OverDue":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="userChkConditionOverdue" tabindex="16" type="checkbox" name="Overdue" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.OverDue + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "IsClosedTransfer":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="userChkConditionIsClosedTransfer" tabindex="16" type="checkbox" name="IsClosedTransfer" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.IsClosedTransfer + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "IsCompletedFollowUp":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="userChkConditionIsCompletedFollowUp" tabindex="16" type="checkbox" name="IsCompletedFollowUp" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.IsCompletedFollowUp + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "FromStructure":
                html = '<div class="form-group" id="userConditionFromStructureContainer"><select id="usercmbConditionUserNodeFromStructure" data-parsley-group="conditions" ' +
                    'tabindex="16" required="required" data-parsley-errors-container="#usercmbConditionUserNodeFromStructureError" class="form-control"></select><div id="usercmbConditionUserNodeFromStructureError"></div></div>';
                $("#valueContainer").html(html);
                var headers = {};
                var url = window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes';
                headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
                $("#usercmbConditionUserNodeFromStructure").select2({
                    minimumInputLength: 0,
                    allowClear: true,

                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    placeholder: Resources.FromStructure,
                    multiple: "multiple",
                    width: "100%",
                    dropdownParent: $('#userConditionFromStructureContainer'),

                    ajax: {
                        delay: 250,
                        url: url,
                        type: "POST",
                        dataType: 'json',
                        headers: typeof headers !== "undefined" ? headers : "",
                        data: function (term) {
                            return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr] };
                        },
                        processResults: function (data) {
                            return {
                                results: structureDataForSelect2(data)

                            };
                            structures = processResults();
                        }
                    }
                });
                $("#usercmbConditionUserNodeFromStructure").val('').trigger('change');
                break;

            case "FromUser":
                html = '<div class="form-group" id="userConditionFromUserContainer"><select id="usercmbConditionUserNodeFromUser" data-parsley-group="conditions" ' +
                    'tabindex="16" required="required" data-parsley-errors-container="#usercmbConditionUserNodeFromUserError" class="form-control"></select><div id="usercmbConditionUserNodeFromUserError"></div></div>';
                $("#valueContainer").html(html);
                var headers = {};
                var url = window.IdentityUrl + '/api/GetUsersAndStructuresWithSearchAttributes';
                headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
                $("#usercmbConditionUserNodeFromUser").select2({
                    minimumInputLength: 0,
                    allowClear: true,

                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    placeholder: Resources.FromUser,
                    multiple: "multiple",
                    width: "100%",
                    dropdownParent: $('#userConditionFromUserContainer'),

                    ajax: {
                        delay: 250,
                        url: url,
                        type: "POST",
                        dataType: 'json',
                        headers: typeof headers !== "undefined" ? headers : "",
                        data: function (term) {
                            return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr, window.FirstNameAr, window.LastNameAr], };
                        },
                        processResults: function (data, term) {
                            var listitemsMultiList = [];
                            var termSearch = term.term ? term.term : "";
                            let strcutredCached = new Map();
                            $.each(data.users, function (key, val) {
                                var currentStructure;
                                if (strcutredCached[val.structureIds[0]] != undefined) {
                                    currentStructure = strcutredCached[val.structureIds[0]]
                                } else {
                                    currentStructure = new IdentityService().getFullStructure(val.structureIds[0], window.language);
                                    strcutredCached[currentStructure.id] = currentStructure;
                                }
                                var structureName = currentStructure.name;
                                if (currentStructure.attributes != null && currentStructure.attributes.length > 0) {
                                    var attributeLang = $.grep(currentStructure.attributes, function (e) {
                                        return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                                    });
                                    if (attributeLang.length > 0) {
                                        structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                                    }
                                }
                                var fullName = val.fullName;
                                if (window.language != 'en') {
                                    fullName = getFullNameByLangauge(val);
                                }
                                fullName = fullName.trim() == "" ? val.fullName : fullName;
                                var item = {};
                                item.id = "User_" + val.structureIds[0] + "_" + val.id;
                                item.text = structureName + " / " + fullName;
                                item.icon = "fa fa-user-o";
                                item.isStructure = false;
                                item.dataId = val.id;
                                item.structureId = val.structureIds.length > 0 ? val.structureIds[0] : val.defaultStructureId;
                                item.structureIds = val.structureIds;
                                item.defaultStructureId = val.defaultStructureId;
                                listitemsMultiList.push(item);
                            });
                            return {
                                results: listitemsMultiList

                            };
                        }
                    }
                });
                $("#usercmbConditionUserNodeFromUser").val('').trigger('change');
                break;
            case "IsSigned":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionIsSigned" tabindex="16" type="checkbox" name="IsSigned" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.IsSigned + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;

        }
    };
    E.getConditionValue = function (json, name)
    {
        switch (name)
        {
            case "Category":
                var categoryArray = [];
                var dataSelected = $('#usercmbConditionUserNodeCategory').select2("data");
                for (var i = 0; i < dataSelected.length; i++)
                {
                    categoryArray.push({ id: dataSelected[i].id, text: dataSelected[i].text })
                }
                json.value = categoryArray;
                break;
            case "DocumentCreatedDate":
            case "DocumentCompletedDate":
            case "TransferCreatedDate":
            case "TransferClosedDate":
                var datejson = {};
                datejson.date = $('#usercmbConditionUserNodeDate').val();
                datejson.sign = $('#usercmbConditionUserNodeDateSign').val();
                datejson.number = $('#userTxtDateNumber').val();
                json.value = datejson;
                json.isDate = true;
                break;
            case "Read":
                json.value = $('#userChkConditionRead').prop("checked");
                break;
            case "Locked":
                json.value = $('#userChkConditionLocked').prop("checked");
                break;
            case "OverDue":
                json.value = $('#userChkConditionOverdue').prop("checked");
                break;
            case "IsClosedTransfer":
                json.value = $('#userChkConditionIsClosedTransfer').prop("checked");
                break;
            case "IsCompletedFollowUp":
                json.value = $('#userChkConditionIsCompletedFollowUp').prop("checked");
                break;
            case "FromStructure":
                var StructureArray = [];
                var dataSelected = $('#usercmbConditionUserNodeFromStructure').select2("data");
                for (var i = 0; i < dataSelected.length; i++) {
                    StructureArray.push({ id: dataSelected[i].id, text: dataSelected[i].text })
                }
                json.value = StructureArray;
                break;

            case "FromUser":
                var UserArray = [];
                var dataSelected = $('#usercmbConditionUserNodeFromUser').select2("data");
                for (var i = 0; i < dataSelected.length; i++) {
                    UserArray.push({ id: dataSelected[i].id.split("_")[2], text: dataSelected[i].text })
                }
                json.value = UserArray;
                break;
            case "IsSigned":
                json.value = $('#chkConditionIsSigned').prop("checked");
                break;

        }
    };
    E.setConditionValue = function (data, name)
    {
        switch (name)
        {
            case "Category":
                var categoryIds = [];
                var categories = new CategoryModel().get();
                for (var i = 0; i < data.value.length; i++)
                {
                    var item = $.grep(categories, function (item)
                    {
                        return item.name === data.value[i].text || item.nameFr == data.value[i].text || item.nameAr == data.value[i].text;
                    });
                    categoryIds.push(item[0].id);
                }
                $('#usercmbConditionUserNodeCategory').val(categoryIds).trigger("change");
                break;
            case "DocumentCreatedDate":
            case "DocumentCompletedDate":
            case "TransferCreatedDate":
            case "TransferClosedDate":
                $('#usercmbConditionUserNodeDate').val(data.value.date).trigger("change");
                $('#usercmbConditionUserNodeDateSign').val(data.value.sign).trigger("change");
                $('#userTxtDateNumber').val(data.value.number);
                break;
            case "Read":
                $('#userChkConditionRead').prop("checked", data.value);
                break;
            case "Locked":
                $('#userChkConditionLocked').prop("checked", data.value);
                break;
            case "OverDue":
                $('#userChkConditionOverdue').prop("checked", data.value);
                break;
            case "IsClosedTransfer":
                $('#userChkConditionIsClosedTransfer').prop("checked", data.value);
                break;
            case "IsCompletedFollowUp":
                $('#userChkConditionIsCompletedFollowUp').prop("checked", data.value);
                break;
            
            case "FromStructure":
                var fromStructureIds = [];
                var structuries = new Structure().getAllStructures(window.language);
                for (var i = 0; i < data.value.length; i++) {
                    var item = $.grep(structuries, function (item) {
                        return item.text === data.value[i].text;
                    });
                    fromStructureIds.push(item[0].id);
                }
                if (data.value.length > 0) {
                    data.value.forEach(item => {
                        let value = item;
                        $('#usercmbConditionUserNodeFromStructure').append(new Option(item.text, item.text, true, true)); // append it, making it selected by default
                    });
                }
                else {
                    $('#usercmbConditionUserNodeFromStructure').val(fromStructureIds).trigger("change");
                }
                break;

            case "FromUser":
                var fromUserIds = [];
                var users = new User().getUsers(window.language);
                for (var i = 0; i < data.value.length; i++) {
                    var item = $.grep(users, function (item) {
                        return item.text === data.value[i].text;
                    });
                    fromUserIds.push(item[0].id);
                }

                if (data.value.length > 0) {
                    data.value.forEach(item => {
                        let value = item;
                        $('#usercmbConditionUserNodeFromUser').append(new Option(item.text, item.text, true, true)); // append it, making it selected by default
                    });
                }
                else {
                    $('#usercmbConditionUserNodeFromUser').val(fromUserIds).trigger("change");
                }

                break;
            case "IsSigned":
                $('#chkConditionIsSigned').prop("checked", data.value);
                break;



        }
    };
    E.renderConditionValue = function (data, name)
    {
        var html = "";
        switch (name)
        {
            case "Category":
                var categories = new CategoryModel().get();
                html = "";
                for (var i = 0; i < data.value.length; i++)
                {
                    var item = $.grep(categories, function (item)
                    {
                        return item.name === data.value[i].text || item.nameFr == data.value[i].text || item.nameAr == data.value[i].text;
                    });
                    if (item.length)
                    {
                        var itemName = item[0].name;
                        if (window.language === 'ar')
                        {
                            itemName = item[0].nameAr;
                        } else if (window.language === 'fr')
                        {
                            itemName = item[0].nameFr;
                        }
                        if (html === "")
                        {
                            html += itemName;
                        } else
                        {

                            html += "," + itemName;
                        }
                    }
                }
                return html;
            case "Status":
                var statuses = new Statuses().get(window.language);
                for (var i = 0; i < data.value.length; i++)
                {
                    var item = $.grep(statuses, function (item)
                    {
                        return item.id == data.value[i];
                    });
                    if (item.length)
                    {
                        if (html === "")
                        {
                            html += item[0].text;
                        } else
                        {

                            html += "," + item[0].text;
                        }
                    }
                }
                return html;
            case "DocumentCreatedDate":
            case "TransferCreatedDate":
            case "DocumentCompletedDate":
                var json = data.value;
                return Resources[json.date] + " " + json.sign + " " + json.number;
            case "Read":
            case "Locked":
            case "OverDue":
            case "IsSigned":
            case "IsClosedTransfer":
                if (data.value === true)
                {
                    return '<em class="fa success fa-check"></em>';
                }
                return '<em class="fa danger fa-close"></em>';
            case "IsCompletedFollowUp":
                if (data.value === true) {
                    return '<em class="fa success fa-check"></em>';
                }
                return '<em class="fa danger fa-close"></em>';
            case "FromStructure":
                var structuries = new Structure().getAllStructures(window.language);
                html = "";
                for (var i = 0; i < data.value.length; i++) {
                    var item = $.grep(structuries, function (item) {
                        return item.text === data.value[i].text;
                    });
                    if (item.length) {
                        var itemName = item[0].text;
                        if (window.language === 'ar') {
                            itemName = item[0].text;
                        } else if (window.language === 'fr') {
                            itemName = item[0].text;
                        }
                        if (html === "") {
                            html += itemName;
                        } else {

                            html += "," + itemName;
                        }
                    }
                }
                return html;
            case "FromUser":
                var users = new User().getUsers(window.language);
                html = "";
                for (var i = 0; i < data.value.length; i++) {
                    var item = $.grep(users, function (item) {
                        return item.text === data.value[i].text;
                    });
                    if (item.length) {
                        var itemName = item[0].text;
                        if (window.language === 'ar') {
                            itemName = item[0].text;
                        } else if (window.language === 'fr') {
                            itemName = item[0].text;
                        }
                        if (html === "") {
                            html += itemName;
                        } else {

                            html += "," + itemName;
                        }
                    }
                }
                return html;

        }
    };
    return E;
}(UserNodeInheritProperties));

function getStructureName(data) {
    var structureName = data.name;
    if (data.attributes != null && data.attributes.length > 0) {
        var attributeLang = $.grep(data.attributes, function (e) {
            return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
        });
        if (attributeLang.length > 0) {
            structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
        }
    }
    return structureName;
}
function structureDataForSelect2(data) {
    var retVal = [];
    if (typeof data !== 'undefined' && data.items) {
        for (var i = 0; i < data.items.length; i++) {
            retVal.push({
                id: data.items[i].id,
                text: getStructureName(data.items[i])
            });
        }
    } else if (data) {
        for (var i = 0; i < data.length; i++) {
            retVal.push({
                id: data[i].id,
                text: getStructureName(data[i])
            });
        }
    }
    return retVal;
}

function getUserName(data) {
    return data.fullName;
}
function userDataForSelect2(data) {
    var retVal = [];
    if (typeof data !== 'undefined' && data.items) {
        for (var i = 0; i < data.items.length; i++) {
            retVal.push({
                id: data.items[i].id,
                text: getUserName(data.items[i])
            });
        }
    } else if (data) {
        for (var i = 0; i < data.length; i++) {
            retVal.push({
                id: data[i].id,
                text: getStructureName(data[i])
            });
        }
    }
    return retVal;
}

function userMakeParent()
{
    $("#userCmbInherit").removeAttr("required");
    if ($("#userCmbInherit").val() === NodeInherit.Custom)
    {
        userMakeNotCustom();
    }
    $('.tohide').hide();
}
function userMakeNotParent()
{
    $('.tohide').show();
    $("#userCmbInherit").attr("required", "required");
    if ($("#userCmbInherit").val() === NodeInherit.Custom)
    {
        UserMakeCustom();
    } else
    {
        userMakeNotCustom();
    }
}
function UserMakeCustom()
{
    $('.noncustominherit').hide();
    $('.custominherit').show();
    $("#txtJsFunctionName").attr("required", "required");
    $("#txtJsFunctionName").attr("data-parsley-group", "primary");
    $("#txtJsFunctionName").parent().find(".control-label").addClass("field-required");
    //if ($("#chkEnableTotalCount").prop('checked'))
    //{
    //    $("#txtTotalCountJsFunctionName").attr("required", "required");
    //    $("#txtTotalCountJsFunctionName").attr("data-parsley-group", "primary");
    //    $("#txtTotalCountJsFunctionName").parent().find(".control-label").addClass("field-required");
    //} else
    //{
    //    $("#txtTotalCountJsFunctionName").removeAttr("required");
    //    $("#txtTotalCountJsFunctionName").removeAttr("data-parsley-group");
    //    $("#txtTotalCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
    //}
    //if ($("#chkEnableTodayCount").prop('checked'))
    //{
    //    $("#txtTodayCountJsFunctionName").attr("required", "required");
    //    $("#txtTodayCountJsFunctionName").attr("data-parsley-group", "primary");
    //    $("#txtTodayCountJsFunctionName").parent().find(".control-label").addClass("field-required");
    //} else
    //{
    //    $("#txtTodayCountJsFunctionName").removeAttr("required");
    //    $("#txtTodayCountJsFunctionName").removeAttr("data-parsley-group");
    //    $("#txtTodayCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
    //}
}
function userMakeNotCustom()
{
    $('.custominherit').hide();
    $('.noncustominherit').show();
    $("#txtJsFunctionName").removeAttr("required");
    $("#txtJsFunctionName").removeAttr("data-parsley-group");
    $("#txtJsFunctionName").parent().find(".control-label").removeClass("field-required");
    //$("#txtTotalCountJsFunctionName").removeAttr("required");
    //$("#txtTotalCountJsFunctionName").removeAttr("data-parsley-group");
    //$("#txtTotalCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
    //$("#txtTodayCountJsFunctionName").removeAttr("required");
    //$("#txtTodayCountJsFunctionName").removeAttr("data-parsley-group");
    //$("#txtTodayCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
}
function userChangeInheritProperties(value)
{
    var msg = $("#userPostMsg");
    msg.hide();
    if (value)
    {
        if (value !== NodeInherit.Custom)
        {
            $('#columnsTableUserNode').DataTable().rows().remove().draw();
            $('#conditionsTableUserNode').DataTable().rows().remove().draw();
            userMakeNotCustom();

            $('#cmbFiltersUserNode').select2('destroy').select2().empty();
            $('#cmbFiltersUserNode').select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                data: UserNodeInheritProperties.getFilters(value),
                allowClear: true,
                placeholder: Resources.SelectFilters,
                multiple: "multiple",
                dropdownParent: $('#filtersContainerUserNode')
            }).on("select2:select", function (evt)
            {
                var element = evt.params.data.element;
                var $element = $(element);
                $element.detach();
                $(this).append($element);
                $(this).trigger("change");
            });
            $("#cmbFiltersUserNode").val('').trigger('change');

            $('#cmbColumnUserNode').select2('destroy').select2().empty();
            $('#cmbColumnUserNode').select2().off("change");
            $('#cmbColumnUserNode').select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                data: UserNodeInheritProperties.userGetColumns(value),
                allowClear: true,
                placeholder: Resources.SelectColumn + " *",
                dropdownParent: $('#columnContainerUserNode'),
                templateResult: function (data)
                {
                    if (data.id === "Custom")
                    {
                        return $("<span class='text-danger'>" + data.text + "</span>");
                    }
                    return data.text;
                }
            }).on('change', function ()
            {
                $(this).trigger('input');
                UserNodeInheritProperties.userInitColumns($(this).val());
            });
            $("#cmbColumnUserNode").val('').trigger('change');

            $('#cmbConditionUserNode').select2('destroy').select2().empty();
            $('#cmbConditionUserNode').select2().off("change");
            $('#cmbConditionUserNode').select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                data: UserNodeInheritProperties.userGetConditions(value),
                allowClear: true,
                placeholder: Resources.SelectCondition + " *",
                dropdownParent: $('#conditionContainerUserNode')
            }).on('change', function ()
            {
                $(this).trigger('input');
                UserNodeInheritProperties.initConditionValue($(this).val());
            });
            $("#cmbConditionUserNode").val('').trigger('change');

            userInitColumns(true);
            $("#columnDetailContainerUserNode").show();
            $("#userOrderColumnContainer").removeClass("col-md-6").addClass("col-md-3");

            msg.show();
            switch (value)
            {
                case NodeInherit.Draft:
                    msg.html(Resources.DraftInheritMessage);
                    break;
                case NodeInherit.Inbox:
                    msg.html(Resources.InboxInheritMessage);
                    break;
                case NodeInherit.Completed:
                    msg.html(Resources.CompletedInheritMessage);
                    break;
                case NodeInherit.MyRequests:
                    msg.html(Resources.MyRequestsInheritMessage);
                    break;
                case NodeInherit.Sent:
                    msg.html(Resources.SentInheritMessage);
                    break;
                case NodeInherit.Closed:
                    msg.html(Resources.ClosedInheritMessage);
                    break;
            }
        } else
        {
            UserMakeCustom();
        }
    }
}
var gColumnTable;
function userInitColumns(enableColumnDetail, firstInit)
{
    Common.gridCommon();
    if (!firstInit)
    {
        $('#columnsTableUserNode').DataTable().destroy();
    }
    gColumnTable = $('#columnsTableUserNode').DataTable({
        dom: 't',
        destroy: true,
        paging: false,
        ordering: false,
        columns: [
            {
                title: Resources.Name,
                "orderable": false,
                "render": function (data, type, row)
                {
                    var name = row.name;
                    var translated = Resources[row.name];
                    if (translated)
                    {
                        name = translated;
                    }
                    if (row.isCustom)
                    {
                        return "<span class='text-danger'>" + Resources.Custom + " : (" + Resources.Name + ": " + name + " , " + Resources.JsFunctionName + ": " + row.customFunctionName + ")</span>";
                    }
                    return name;
                }
            },
            {
                title: Resources.Order,
                'width': '100px',
                "orderable": false,
                "render": function (data, type, row)
                {
                    return row.order;
                }
            },
            {
                title: Resources.ColumnDetail,
                visible: enableColumnDetail,
                'width': '100px',
                "orderable": false,
                "render": function (data, type, row)
                {
                    if (row.isColumnDetail !== "" && row.isColumnDetail !== null && row.isColumnDetail === true)
                    {
                        return '<em class="fa success fa-check"></em>';
                    }
                    return '<em class="fa danger fa-close"></em>';
                }
            },
            {
                "autoWidth": false,
                "bAutoWidth": false,
                'width': '10px',
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta)
                {
                    let btnedit = document.createElement("button");
                    btnedit.setAttribute("class", "btn btn-xs btn-primary");
                    btnedit.setAttribute("title", Resources.Edit);
                    btnedit.setAttribute("type", "button");
                    btnedit.setAttribute("action", "edit");
                    btnedit.innerHTML = "<i class='fa fa-edit fa-white'/>";
                    return btnedit.outerHTML;
                }
            },
            {
                "autoWidth": false,
                "bAutoWidth": false,
                'width': '10px',
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta)
                {
                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-danger");
                    btn.setAttribute("title", Resources.Delete);
                    btn.setAttribute("type", "button");
                    btn.setAttribute("action", "delete");
                    btn.innerHTML = "<i class='fa fa-trash-o'/>";
                    return btn.outerHTML;
                }
            }
        ]
    });
    $('#addColumnUserNode').off('click').on('click', function () {
        var $form = $('#formPostUserNode');
        $form.parsley().reset({ group: 'columns' });
        var isValid = $form.parsley().validate({ group: 'columns' });

        if (isValid) {
            var columns = userGetColumns(gColumnTable); // Fetch current columns
            var name = $("#cmbColumnUserNode").val();
            var order = $("#txtColumnOrderUserNode").val();

            var isNewColumnDetail = $("#chkColumnDetailUserNode").prop("checked");
            if (name !== '') {
                if ($(this).attr('action') === 'addRow') {
                 
                    var existingName = $.grep(columns, function (item) {
                        return item.name === name;
                    });

                    var existingOrder = [];
                    if (order && order.trim() !== "") {
                        existingOrder = $.grep(columns, function (item) {
                            return item.order == order && item.isColumnDetail === isNewColumnDetail;
                        });
                    }
                    //var existingOrder = $.grep(columns, function (item) {
                    //    // Only compare orders if the new order is not null/empty
                    //    return item.order !== null && item.order !== "" && item.order == order;
                    //});

                    if (existingName.length) {
                        Common.alertMsg(Resources.AlreadyExists);
                    } else if (existingOrder.length) {
                        Common.alertMsg(Resources.OrderAlreadyExists); // New message for duplicate order
                    } else {
                        var json = {};
                        if (name === "Custom") {
                            json.name = $("#txtColumnNameUserName").val();
                            json.customFunctionName = $("#txtColumnFunctionUserNode").val();
                            json.isCustom = true;
                        } else {
                            json.name = name;
                        }
                        json.order = order || null;
                        json.isColumnDetail = $("#chkColumnDetailUserNode").prop("checked");

                        gColumnTable.row.add(json).draw();
                        sortTableAlphabetically();

                        $('#addColumnUserNode').attr('action', 'addRow');
                        $('#cmbColumnUserNode').attr('required', 'required');
                        $('#cmbColumnUserNode').val("").trigger("change");
                        $('#txtColumnOrderUserNode').val("");
                        $("#chkColumnDetailUserNode").prop("checked", false);
                    }
                }

                if ($(this).attr('action') === 'confirmEdit') {
                    // Edit column logic
                    const json = {};
                    if (name === "Custom") {
                        json.name = $("#txtColumnNameUserName").val();
                        json.customFunctionName = $("#txtColumnFunctionUserNode").val();
                        json.isCustom = true;
                    } else {
                        json.name = name;
                    }
                    json.order = order;
                    json.isColumnDetail = $("#chkColumnDetailUserNode").prop("checked");

                    gColumnTable.row($(this).attr('rowindex')).data(json).draw();
                    sortTableAlphabetically();

                    // Reset form fields
                    $('#addColumnUserNode').attr('action', 'addRow');
                    $('#cmbColumnUserNode').attr('required', 'required');
                    $('#cmbColumnUserNode').val("").trigger("change");
                    $('#txtColumnOrderUserNode').val("");
                    $("#chkColumnDetailUserNode").prop("checked", false);
                }
            }
        }
    });


    $('#columnsTableUserNode').on('click', 'tbody tr button[action="edit"]', function ()
    {
        const row = gColumnTable.row($(event.target).closest('tr'));
        $('#addColumnUserNode').attr('rowindex', row.index());
        $('#addColumnUserNode').attr('action', 'confirmEdit');
        var data = row.data();
        $('#cmbColumnUserNode').attr('required', 'required')
        if (data.isCustom === true)
        {
            $('#cmbColumnUserNode').val("Custom").trigger("change");
            $("#txtColumnNameUserName").val(data.name);
            $("#txtColumnFunctionUserNode").val(data.customFunctionName);
        } else
        {
            $('#cmbColumnUserNode').val(data.name).trigger("change");
        }
        $('#txtColumnOrderUserNode').val(data.order);
        $("#chkColumnDetailUserNode").prop("checked", data.isColumnDetail);
    });
}
var gConditionTable;
function userInitConditions()
{
    Common.gridCommon();
    gConditionTable = $('#conditionsTableUserNode').DataTable({
        dom: 't',
        paging: false,
        ordering: false,
        columns: [
            {
                title: Resources.Name,
                "orderable": false,
                "render": function (data, type, row)
                {
                    var translated = Resources[row.name];
                    if (translated)
                    {
                        return translated;
                    }
                    return row.name;
                }
            },
            {
                title: Resources.Value,
                "orderable": false,
                "render": function (data, type, row)
                {
                    return UserNodeInheritProperties.renderConditionValue(row, row.name);
                }
            },
            {
                "autoWidth": false,
                "bAutoWidth": false,
                'width': '10px',
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta)
                {
                    let btnedit = document.createElement("button");
                    btnedit.setAttribute("class", "btn btn-xs btn-primary");
                    btnedit.setAttribute("title", Resources.Edit);
                    btnedit.setAttribute("type", "button");
                    btnedit.setAttribute("action", "edit");
                    btnedit.innerHTML = "<i class='fa fa-edit fa-white'/>";
                    return btnedit.outerHTML;
                }
            },
            {
                "autoWidth": false,
                "bAutoWidth": false,
                'width': '10px',
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta)
                {
                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-danger");
                    btn.setAttribute("title", Resources.Delete);
                    btn.setAttribute("type", "button");
                    btn.setAttribute("action", "delete");
                    btn.innerHTML = "<i class='fa fa-trash-o'/>";
                    return btn.outerHTML;
                }
            }
        ]
    });
    $('#addConditionUserNode').off('click').on('click', function ()
    {
        var $form = $('#formPostUserNode');
        $form.parsley().reset({ group: 'conditions' });
        var isValid = $form.parsley().validate({ group: 'conditions' });
        if (isValid)
        {
            var conditions = userGetConditions(gConditionTable);
            var name = $("#cmbConditionUserNode").val();
            if (name !== '')
            {
                if ($(this).attr('action') === 'addRow')
                {
                    var item = $.grep(conditions, function (item)
                    {
                        return item.name === name;
                    });

                    if (!item.length)
                    {
                        var json = {};
                        json.name = name;
                        UserNodeInheritProperties.getConditionValue(json, name);
                        gConditionTable.row.add(json).draw();
                        $('#addConditionUserNode').attr('action', 'addRow');
                        $('#cmbConditionUserNode').attr('required', 'required')
                        $('#cmbConditionUserNode').val("").trigger("change");
                    } else
                    {
                        Common.alertMsg(Resources.AlreadyExists);
                    }
                }
            }
            if ($(this).attr('action') === 'confirmEdit')
            {
                const json = {};
                json.name = name;
                UserNodeInheritProperties.getConditionValue(json, name);
                gConditionTable.row($(this).attr('rowindex')).data(json).draw();
                $('#addConditionUserNode').attr('action', 'addRow');
                $('#cmbConditionUserNode').attr('required', 'required')
                $('#cmbConditionUserNode').val("").trigger("change");
            }
        }
    });
    $('#conditionsTableUserNode').on('click', 'tbody tr button[action="delete"]', function (event)
    {
        gConditionTable.row($(event.target).closest('tr')).remove().draw();
        $('#addConditionUserNode').attr('action', 'addRow');
    });
    $('#conditionsTableUserNode').on('click', 'tbody tr button[action="edit"]', function ()
    {
        const row = gConditionTable.row($(event.target).closest('tr'));
        $('#addConditionUserNode').attr('rowindex', row.index());
        $('#addConditionUserNode').attr('action', 'confirmEdit');
        var data = row.data();
        $('#cmbConditionUserNode').attr('required', 'required')
        $('#cmbConditionUserNode').val(data.name).trigger("change");
        UserNodeInheritProperties.setConditionValue(data, data.name);
    });
}
function userGetColumns(datatable)
{
    var data = datatable.rows().data();
    var columns = [];
    data.each(function (value, index)
    {
        var jsonobj = {};
        jsonobj.name = value.name;
        jsonobj.order = value.order;
        jsonobj.isColumnDetail = value.isColumnDetail;
        jsonobj.customFunctionName = value.customFunctionName;
        jsonobj.isCustom = value.isCustom;
        columns.push(jsonobj);
    });
    return columns;
}
function userGetConditions(datatable)
{
    var data = datatable.rows().data();
    var conditions = [];
    data.each(function (value, index)
    {
        var jsonobj = {};
        jsonobj.name = value.name;
        jsonobj.value = value.value;
        jsonobj.isDate = value.isDate;
        jsonobj.sign = value.sign;
        conditions.push(jsonobj);
    });
    return conditions;
}
var iconsList = ["fa-500px", "fa-address-book", "fa-address-book-o", "fa-address-card", "fa-address-card-o", "fa-adjust", "fa-adn", "fa-align-center", "fa-align-justify", "fa-align-left", "fa-align-right", "fa-amazon", "fa-ambulance", "fa-american-sign-language-interpreting", "fa-anchor", "fa-android", "fa-angellist", "fa-angle-double-down", "fa-angle-double-left", "fa-angle-double-right", "fa-angle-double-up", "fa-angle-down", "fa-angle-left", "fa-angle-right", "fa-angle-up", "fa-apple", "fa-archive", "fa-area-chart", "fa-arrow-circle-down", "fa-arrow-circle-left", "fa-arrow-circle-o-down", "fa-arrow-circle-o-left", "fa-arrow-circle-o-right", "fa-arrow-circle-o-up", "fa-arrow-circle-right", "fa-arrow-circle-up", "fa-arrow-down", "fa-arrow-left", "fa-arrow-right", "fa-arrow-up", "fa-arrows", "fa-arrows-alt", "fa-arrows-h", "fa-arrows-v", "fa-asl-interpreting", "fa-assistive-listening-systems", "fa-asterisk", "fa-at", "fa-audio-description", "fa-automobile", "fa-backward", "fa-balance-scale", "fa-ban", "fa-bandcamp", "fa-bank", "fa-bar-chart", "fa-bar-chart-o", "fa-barcode", "fa-bars", "fa-bath", "fa-bathtub", "fa-battery", "fa-battery-0", "fa-battery-1", "fa-battery-2", "fa-battery-3", "fa-battery-4", "fa-battery-empty", "fa-battery-full", "fa-battery-half", "fa-battery-quarter", "fa-battery-three-quarters", "fa-bed", "fa-beer", "fa-behance", "fa-behance-square", "fa-bell", "fa-bell-o", "fa-bell-slash", "fa-bell-slash-o", "fa-bicycle", "fa-binoculars", "fa-birthday-cake", "fa-bitbucket", "fa-bitbucket-square", "fa-bitcoin", "fa-black-tie", "fa-blind", "fa-bluetooth", "fa-bluetooth-b", "fa-bold", "fa-bolt", "fa-bomb", "fa-book", "fa-bookmark", "fa-bookmark-o", "fa-braille", "fa-briefcase", "fa-btc", "fa-bug", "fa-building", "fa-building-o", "fa-bullhorn", "fa-bullseye", "fa-bus", "fa-buysellads", "fa-cab", "fa-calculator", "fa-calendar", "fa-calendar-check-o", "fa-calendar-minus-o", "fa-calendar-o", "fa-calendar-plus-o", "fa-calendar-times-o", "fa-camera", "fa-camera-retro", "fa-car", "fa-caret-down", "fa-caret-left", "fa-caret-right", "fa-caret-square-o-down", "fa-caret-square-o-left", "fa-caret-square-o-right", "fa-caret-square-o-up", "fa-caret-up", "fa-cart-arrow-down", "fa-cart-plus", "fa-cc", "fa-cc-amex", "fa-cc-diners-club", "fa-cc-discover", "fa-cc-jcb", "fa-cc-mastercard", "fa-cc-paypal", "fa-cc-stripe", "fa-cc-visa", "fa-certificate", "fa-chain", "fa-chain-broken", "fa-check", "fa-check-circle", "fa-check-circle-o", "fa-check-square", "fa-check-square-o", "fa-chevron-circle-down", "fa-chevron-circle-left", "fa-chevron-circle-right", "fa-chevron-circle-up", "fa-chevron-down", "fa-chevron-left", "fa-chevron-right", "fa-chevron-up", "fa-child", "fa-chrome", "fa-circle", "fa-circle-o", "fa-circle-o-notch", "fa-circle-thin", "fa-clipboard", "fa-clock-o", "fa-clone", "fa-close", "fa-cloud", "fa-cloud-download", "fa-cloud-upload", "fa-cny", "fa-code", "fa-code-fork", "fa-codepen", "fa-codiepie", "fa-coffee", "fa-cog", "fa-cogs", "fa-columns", "fa-comment", "fa-comment-o", "fa-commenting", "fa-commenting-o", "fa-comments", "fa-comments-o", "fa-compass", "fa-compress", "fa-connectdevelop", "fa-contao", "fa-copy", "fa-copyright", "fa-creative-commons", "fa-credit-card", "fa-credit-card-alt", "fa-crop", "fa-crosshairs", "fa-css3", "fa-cube", "fa-cubes", "fa-cut", "fa-cutlery", "fa-dashboard", "fa-dashcube", "fa-database", "fa-deaf", "fa-deafness", "fa-dedent", "fa-delicious", "fa-desktop", "fa-deviantart", "fa-diamond", "fa-digg", "fa-dollar", "fa-dot-circle-o", "fa-download", "fa-dribbble", "fa-drivers-license", "fa-drivers-license-o", "fa-dropbox", "fa-drupal", "fa-edge", "fa-edit", "fa-eercast", "fa-eject", "fa-ellipsis-h", "fa-ellipsis-v", "fa-empire", "fa-envelope", "fa-envelope-o", "fa-envelope-open", "fa-envelope-open-o", "fa-envelope-square", "fa-envira", "fa-eraser", "fa-etsy", "fa-eur", "fa-euro", "fa-exchange", "fa-exclamation", "fa-exclamation-circle", "fa-exclamation-triangle", "fa-expand", "fa-expeditedssl", "fa-external-link", "fa-external-link-square", "fa-eye", "fa-eye-slash", "fa-eyedropper", "fa-fa", "fa-facebook", "fa-facebook-f", "fa-facebook-official", "fa-facebook-square", "fa-fast-backward", "fa-fast-forward", "fa-fax", "fa-feed", "fa-female", "fa-fighter-jet", "fa-file", "fa-file-archive-o", "fa-file-audio-o", "fa-file-code-o", "fa-file-excel-o", "fa-file-image-o", "fa-file-movie-o", "fa-file-o", "fa-file-pdf-o", "fa-file-photo-o", "fa-file-picture-o", "fa-file-powerpoint-o", "fa-file-sound-o", "fa-file-text", "fa-file-text-o", "fa-file-video-o", "fa-file-word-o", "fa-file-zip-o", "fa-files-o", "fa-film", "fa-filter", "fa-fire", "fa-fire-extinguisher", "fa-firefox", "fa-first-order", "fa-flag", "fa-flag-checkered", "fa-flag-o", "fa-flash", "fa-flask", "fa-flickr", "fa-floppy-o", "fa-folder", "fa-folder-o", "fa-folder-open", "fa-folder-open-o", "fa-font", "fa-font-awesome", "fa-fonticons", "fa-fort-awesome", "fa-forumbee", "fa-forward", "fa-foursquare", "fa-free-code-camp", "fa-frown-o", "fa-futbol-o", "fa-gamepad", "fa-gavel", "fa-gbp", "fa-ge", "fa-gear", "fa-gears", "fa-genderless", "fa-get-pocket", "fa-gg", "fa-gg-circle", "fa-gift", "fa-git", "fa-git-square", "fa-github", "fa-github-alt", "fa-github-square", "fa-gitlab", "fa-gittip", "fa-glass", "fa-glide", "fa-glide-g", "fa-globe", "fa-google", "fa-google-plus", "fa-google-plus-circle", "fa-google-plus-official", "fa-google-plus-square", "fa-google-wallet", "fa-graduation-cap", "fa-gratipay", "fa-grav", "fa-group", "fa-h-square", "fa-hacker-news", "fa-hand-grab-o", "fa-hand-lizard-o", "fa-hand-o-down", "fa-hand-o-left", "fa-hand-o-right", "fa-hand-o-up", "fa-hand-paper-o", "fa-hand-peace-o", "fa-hand-pointer-o", "fa-hand-rock-o", "fa-hand-scissors-o", "fa-hand-spock-o", "fa-hand-stop-o", "fa-handshake-o", "fa-hard-of-hearing", "fa-hashtag", "fa-hdd-o", "fa-header", "fa-headphones", "fa-heart", "fa-heart-o", "fa-heartbeat", "fa-history", "fa-home", "fa-hospital-o", "fa-hotel", "fa-hourglass", "fa-hourglass-1", "fa-hourglass-2", "fa-hourglass-3", "fa-hourglass-end", "fa-hourglass-half", "fa-hourglass-o", "fa-hourglass-start", "fa-houzz", "fa-html5", "fa-i-cursor", "fa-id-badge", "fa-id-card", "fa-id-card-o", "fa-ils", "fa-image", "fa-imdb", "fa-inbox", "fa-indent", "fa-industry", "fa-info", "fa-info-circle", "fa-inr", "fa-instagram", "fa-institution", "fa-internet-explorer", "fa-ioxhost", "fa-italic", "fa-joomla", "fa-jpy", "fa-jsfiddle", "fa-key", "fa-keyboard-o", "fa-krw", "fa-language", "fa-laptop", "fa-lastfm", "fa-lastfm-square", "fa-leaf", "fa-leanpub", "fa-legal", "fa-lemon-o", "fa-level-down", "fa-level-up", "fa-life-bouy", "fa-life-buoy", "fa-life-ring", "fa-life-saver", "fa-lightbulb-o", "fa-line-chart", "fa-link", "fa-linkedin", "fa-linkedin-square", "fa-linode", "fa-linux", "fa-list", "fa-list-alt", "fa-list-ol", "fa-list-ul", "fa-location-arrow", "fa-lock", "fa-long-arrow-down", "fa-long-arrow-left", "fa-long-arrow-right", "fa-long-arrow-up", "fa-low-vision", "fa-magic", "fa-magnet", "fa-mail-forward", "fa-mail-reply", "fa-mail-reply-all", "fa-male", "fa-map", "fa-map-marker", "fa-map-o", "fa-map-pin", "fa-map-signs", "fa-maxcdn", "fa-meanpath", "fa-medium", "fa-medkit", "fa-meetup", "fa-meh-o", "fa-microchip", "fa-microphone", "fa-microphone-slash", "fa-minus", "fa-minus-circle", "fa-minus-square", "fa-minus-square-o", "fa-mixcloud", "fa-mobile", "fa-mobile-phone", "fa-modx", "fa-money", "fa-moon-o", "fa-mortar-board", "fa-motorcycle", "fa-mouse-pointer", "fa-music", "fa-navicon", "fa-neuter", "fa-newspaper-o", "fa-object-group", "fa-object-ungroup", "fa-odnoklassniki", "fa-odnoklassniki-square", "fa-opencart", "fa-openid", "fa-opera", "fa-optin-monster", "fa-outdent", "fa-pagelines", "fa-paint-brush", "fa-paper-plane", "fa-paper-plane-o", "fa-paperclip", "fa-paragraph", "fa-paste", "fa-pause", "fa-pause-circle", "fa-pause-circle-o", "fa-paw", "fa-paypal", "fa-pencil", "fa-pencil-square", "fa-pencil-square-o", "fa-percent", "fa-phone", "fa-phone-square", "fa-photo", "fa-picture-o", "fa-pie-chart", "fa-pied-piper", "fa-pied-piper-alt", "fa-pied-piper-pp", "fa-pinterest", "fa-pinterest-p", "fa-pinterest-square", "fa-plane", "fa-play", "fa-play-circle", "fa-play-circle-o", "fa-plug", "fa-plus", "fa-plus-circle", "fa-plus-square", "fa-plus-square-o", "fa-podcast", "fa-power-off", "fa-print", "fa-product-hunt", "fa-puzzle-piece", "fa-qq", "fa-qrcode", "fa-question", "fa-question-circle", "fa-question-circle-o", "fa-quora", "fa-quote-left", "fa-quote-right", "fa-ra", "fa-random", "fa-ravelry", "fa-rebel", "fa-recycle", "fa-reddit", "fa-reddit-alien", "fa-reddit-square", "fa-refresh", "fa-registered", "fa-remove", "fa-renren", "fa-reorder", "fa-repeat", "fa-reply", "fa-reply-all", "fa-resistance", "fa-retweet", "fa-rmb", "fa-road", "fa-rocket", "fa-rotate-left", "fa-rotate-right", "fa-rouble", "fa-rss", "fa-rss-square", "fa-rub", "fa-ruble", "fa-rupee", "fa-s15", "fa-safari", "fa-save", "fa-scissors", "fa-scribd", "fa-search", "fa-search-minus", "fa-search-plus", "fa-sellsy", "fa-send", "fa-send-o", "fa-server", "fa-share", "fa-share-alt", "fa-share-alt-square", "fa-share-square", "fa-share-square-o", "fa-shekel", "fa-sheqel", "fa-shield", "fa-ship", "fa-shirtsinbulk", "fa-shopping-bag", "fa-shopping-basket", "fa-shopping-cart", "fa-shower", "fa-sign-in", "fa-sign-language", "fa-sign-out", "fa-signal", "fa-signing", "fa-simplybuilt", "fa-sitemap", "fa-skyatlas", "fa-skype", "fa-slack", "fa-sliders", "fa-slideshare", "fa-smile-o", "fa-snapchat", "fa-snapchat-ghost", "fa-snapchat-square", "fa-snowflake-o", "fa-soccer-ball-o", "fa-sort", "fa-sort-alpha-asc", "fa-sort-alpha-desc", "fa-sort-amount-asc", "fa-sort-amount-desc", "fa-sort-asc", "fa-sort-desc", "fa-sort-down", "fa-sort-numeric-asc", "fa-sort-numeric-desc", "fa-sort-up", "fa-soundcloud", "fa-space-shuttle", "fa-spinner", "fa-spoon", "fa-spotify", "fa-square", "fa-square-o", "fa-stack-exchange", "fa-stack-overflow", "fa-star", "fa-star-half", "fa-star-half-empty", "fa-star-half-full", "fa-star-half-o", "fa-star-o", "fa-steam", "fa-steam-square", "fa-step-backward", "fa-step-forward", "fa-stethoscope", "fa-sticky-note", "fa-sticky-note-o", "fa-stop", "fa-stop-circle", "fa-stop-circle-o", "fa-street-view", "fa-strikethrough", "fa-stumbleupon", "fa-stumbleupon-circle", "fa-subscript", "fa-subway", "fa-suitcase", "fa-sun-o", "fa-superpowers", "fa-superscript", "fa-support", "fa-table", "fa-tablet", "fa-tachometer", "fa-tag", "fa-tags", "fa-tasks", "fa-taxi", "fa-telegram", "fa-television", "fa-tencent-weibo", "fa-terminal", "fa-text-height", "fa-text-width", "fa-th", "fa-th-large", "fa-th-list", "fa-themeisle", "fa-thermometer", "fa-thermometer-0", "fa-thermometer-1", "fa-thermometer-2", "fa-thermometer-3", "fa-thermometer-4", "fa-thermometer-empty", "fa-thermometer-full", "fa-thermometer-half", "fa-thermometer-quarter", "fa-thermometer-three-quarters", "fa-thumb-tack", "fa-thumbs-down", "fa-thumbs-o-down", "fa-thumbs-o-up", "fa-thumbs-up", "fa-ticket", "fa-times", "fa-times-circle", "fa-times-circle-o", "fa-times-rectangle", "fa-times-rectangle-o", "fa-tint", "fa-toggle-down", "fa-toggle-left", "fa-toggle-off", "fa-toggle-on", "fa-toggle-right", "fa-toggle-up", "fa-trademark", "fa-train", "fa-trash", "fa-trash-o", "fa-tree", "fa-trello", "fa-tripadvisor", "fa-trophy", "fa-truck", "fa-try", "fa-tty", "fa-tumblr", "fa-tumblr-square", "fa-turkish-lira", "fa-tv", "fa-twitch", "fa-twitter", "fa-twitter-square", "fa-umbrella", "fa-underline", "fa-undo", "fa-universal-access", "fa-university", "fa-unlink", "fa-unlock", "fa-unlock-alt", "fa-unsorted", "fa-upload", "fa-usb", "fa-usd", "fa-user", "fa-user-circle", "fa-user-circle-o", "fa-user-md", "fa-user-o", "fa-user-plus", "fa-user-secret", "fa-user-times", "fa-users", "fa-vcard", "fa-vcard-o", "fa-viacoin", "fa-viadeo", "fa-viadeo-square", "fa-video-camera", "fa-vimeo", "fa-vimeo-square", "fa-vine", "fa-vk", "fa-volume-control-phone", "fa-volume-down", "fa-volume-off", "fa-volume-up", "fa-warning", "fa-wechat", "fa-weibo", "fa-weixin", "fa-whatsapp", "fa-wheelchair", "fa-wheelchair-alt", "fa-wifi", "fa-wikipedia-w", "fa-window-close", "fa-window-close-o", "fa-window-maximize", "fa-window-minimize", "fa-window-restore", "fa-windows", "fa-won", "fa-wordpress", "fa-wpbeginner", "fa-wpexplorer", "fa-wpforms", "fa-wrench", "fa-xing", "fa-xing-square", "fa-y-combinator", "fa-y-combinator-square", "fa-yahoo", "fa-yc", "fa-yc-square", "fa-yelp", "fa-yen", "fa-yoast", "fa-youtube", "fa-youtube-play", "fa-youtube-square"];
class UserNodeIndexView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "userNodeIndex", model);
    }
    render()
    {
        $('#btnSubmitUserNode').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#addConditionUserNode').focus();
                }
                else
                {
                    $('#txtNameUserNode').focus();
                }
            }
        });
        $.fn.select2.defaults.set("theme", "bootstrap");
        $('#txtNameUserNode').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnSubmitUserNode').focus();
                }
                else
                {
                    $('#txtOrderUserNode').focus();
                }
            }
        });
        $('#formPostUserNode').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                e.preventDefault();
                $('#btnSubmitUserNode').trigger("click");
            }
        });
        var options = new Array();
        iconsList.forEach(function (icon, index)
        {
            options.push({
                id: 'fa ' + icon,
                text: '<i class="fa fa-fw ' + icon + '"></i> fa ' + icon
            });
        });
        $('#cmbIconUserNode').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            data: options,
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#iconContainerUserNode'),
            escapeMarkup: function (markup)
            {
                return markup;
            }
        }).on('change', function ()
        {
            $(this).trigger('input');
        });
        $("#cmbIconUserNode").val('').trigger('change');
        $('#cmbFiltersUserNode').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: Resources.SelectFilters,
            multiple: "multiple",
            dropdownParent: $('#filtersContainerUserNode')
        }).on("select2:select", function (evt)
        {
            var element = evt.params.data.element;
            var $element = $(element);
            $element.detach();
            $(this).append($element);
            $(this).trigger("change");
        });
        $("#cmbFiltersUserNode").val('').trigger('change');
        $('#cmbColumnUserNode').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: Resources.SelectColumn + " *",
            dropdownParent: $('#columnContainerUserNode'),
            templateResult: function (data)
            {
                if (data.id === "Custom")
                {
                    return $("<span class='text-danger'>" + data.text + "</span>");
                }
                return data.text;
            }
        });
        $("#cmbColumnUserNode").val('').trigger('change');
        $('#cmbConditionUserNode').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: Resources.SelectCondition + " *",
            dropdownParent: $('#conditionContainerUserNode')
        });
        $("#cmbConditionUserNode").val('').trigger('change');
        $('#cmbInheritUserNode').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            
            dropdownParent: $('#inheritContainerUserNode')
        }).on('change', function ()
        {
            //$(this).trigger('input');
            userChangeInheritProperties($(this).val()/* NodeInherit.Inbox*/);
        });
        $('#cmbInheritUserNode').val("").trigger("change");
     
        $('#btnSubmitUserNode').on('click', function ()
        {
            var $form = $('#formPostUserNode');
            $form.parsley().reset();
            var isValid = $form.parsley().validate({ group: 'primary' });
            if (isValid)
            {
                let name = $("#txtNameUserNode").val();
                let order = $("#txtOrderUserNode").val();
                let parentId = $("#hdParentIdUserNode").val();
                //let roleIds = $("#cmbRoles").val() || [];
                let params = {
                    'Name': name,
                    'Order': order || "1",
                    'ParentNodeId': parentId,
                    //'RoleIds': roleIds,
                    'Visible': $("#chkVisibleUserNode").prop('checked'),
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                };
                if (document.getElementById('hdIdUserNode').value)
                {
                    params.Id = document.getElementById('hdIdUserNode').value;
                }
                //if (parentId === "")
                //{
                //    params.Icon = $("#cmbIcon").val();
                //}
                //if (!$("#chkParent").prop('checked'))
                //{
                var inherit = $("#cmbInheritUserNode").val();
                var icon = $("#cmbIconUserNode").val();
                params.Inherit = inherit;
                params.icon = icon;
                    //params.EnableTotalCount = $("#chkEnableTotalCount").prop('checked');
                    //params.EnableTodayCount = $("#chkEnableTodayCount").prop('checked');
                    if (inherit !== NodeInherit.Custom)
                    {
                        var filters = $("#cmbFiltersUserNode").val();
                        params.Filters = filters ? JSON.stringify(filters) : "";
                        var columns = userGetColumns(gColumnTable);
                        params.Columns = columns ? JSON.stringify(columns) : "";
                        var conditions = userGetConditions(gConditionTable);
                        params.Conditions = conditions ? JSON.stringify(conditions) : "";
                    } else
                    {
                        params.CustomFunctions = $("#txtJsFunctionName").val() + window.Splitter + $("#txtTotalCountJsFunctionName").val() + window.Splitter + $("#txtTodayCountJsFunctionName").val();
                    }
                //}
                var btn = $('#btnSubmitUserNode');
                btn.button('loading');
                var btnClose = $('#btnCloseUserNode');
                btnClose.attr('disabled', 'disabled');
                params.IsUserNode = true;
                Common.ajaxPost('/Node/SaveUserNode', params, function (data)
                {
                    if (data === "NameAlreadyExist")
                    {
                        Common.alertMsg(Resources.NameAlreadyExists);
                    }
                    else if (data === "OrderAlreadyExists") {
                        Common.alertMsg(Resources.OrderAlreadyExists);
                    }
                    else
                    {
                        var children = [];
                        var name = Common.translate(data.name);
                        if (document.getElementById('hdIdUserNode').value)
                        {
                            var fullNode = $('#jstree').jstree().get_node(data.id);
                            fullNode.name = data.name;
                            fullNode.data = data;
                            $('#jstree').jstree().redraw_node(fullNode);
                            $('#jstree').jstree().rename_node(fullNode, name);
                        } else
                        {
                            var parentNodeId = parentId !== "" ? parentId : "0"
                            let newNode = {
                                'children': children,
                                'data': data,
                                'id': data.id,
                                'parentId': parentNodeId,
                                'state': { opened: true, disabled: false, selected: false },
                                'text': name
                            };
                            $('#jstree').jstree().create_node(parentNodeId, newNode, "last", null);
                        }
                        Common.showScreenSuccessMsg();
                        document.getElementById('hdIdUserNode').value = data.id;
                        $('#panelNodeTitle').html(data.name + " " + "(" + Resources.Edit + ")");
                    }
                    btn.button('reset');
                    btnClose.removeAttr('disabled');
                }, function () { btn.button('reset'); btnClose.removeAttr('disabled'); Common.showScreenErrorMsg(); }, false);
            }
        });
        //$('#chkParent').change(function ()
        //{
        //    if (!this.checked)
        //    {
        //        userMakeNotParent();
        //    } else
        //    {
        //        userMakeParent();
        //    }
        //});
        $('#chkAllFiltersUserNode').on('click', function ()
        {
            $("#cmbFiltersUserNode").select2('destroy').find('option').prop('selected', 'selected').end().select2();
        });
        //$('#chkEnableTodayCount').change(function ()
        //{
        //    if ($("#userCmbInherit").val() === NodeInherit.Custom)
        //    {
        //        if (!this.checked)
        //        {
        //            $("#txtTodayCountJsFunctionName").removeAttr("required");
        //            $("#txtTodayCountJsFunctionName").removeAttr("data-parsley-group");
        //            $("#txtTodayCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
        //        } else
        //        {
        //            $("#txtTodayCountJsFunctionName").attr("required", "required");
        //            $("#txtTodayCountJsFunctionName").attr("data-parsley-group", "primary");
        //            $("#txtTodayCountJsFunctionName").parent().find(".control-label").addClass("field-required");
        //        }
        //    }
        //});
        //$('#chkEnableTotalCount').change(function ()
        //{
        //    if ($("#userCmbInherit").val() === NodeInherit.Custom)
        //    {
        //        if (!this.checked)
        //        {
        //            $("#txtTotalCountJsFunctionName").removeAttr("required");
        //            $("#txtTotalCountJsFunctionName").removeAttr("data-parsley-group");
        //            $("#txtTotalCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
        //        } else
        //        {
        //            $("#txtTotalCountJsFunctionName").attr("required", "required");
        //            $("#txtTotalCountJsFunctionName").attr("data-parsley-group", "primary");
        //            $("#txtTotalCountJsFunctionName").parent().find(".control-label").addClass("field-required");
        //        }
        //    }
        //});
        //$('#chkAllRoles').on('click', function ()
        //{
        //    $("#cmbRoles").select2('destroy').find('option').prop('selected', 'selected').end().select2();
        //});

        userInitColumns(false, true);
        userInitConditions();

        $('#columnsTableUserNode').on('click', 'tbody tr button[action="delete"]', function (event) {
            gColumnTable.row($(event.target).closest('tr')).remove().draw();
            $('#addColumnUserNode').attr('action', 'addRow');
        });
    }
}

//window.UserNodeIndex = UserNodeIndex;
//window.UserNodeIndexView = UserNodeIndexView;
export default { UserNodeIndex, UserNodeIndexView };
