<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Intalio.CTS.Core</name>
    </assembly>
    <members>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.Upload(System.Int64,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Int16,Intalio.CTS.Core.Model.FileViewModel,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Int32,System.Boolean)">
            <summary>
            Add file
            </summary>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="folderId"></param>
            <param name="categoryId"></param>
            <param name="file"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="structureId"></param>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.UploadOriginalMail(System.Int64,System.Nullable{System.Int64},System.Int16,Intalio.CTS.Core.Model.FileViewModel,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Int32)">
            <summary>
            Add original mail file
            </summary>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="categoryId"></param>
            <param name="file"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="structureId"></param>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.UploadAttachment(System.Int64,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Int16,Intalio.CTS.Core.Model.FileViewModel,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Int32,System.Boolean)">
            <summary>
            Add file
            </summary>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="folderId"></param>
            <param name="categoryId"></param>
            <param name="file"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="structureId"></param>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.List(System.Int64,System.Nullable{System.Int64},System.Int64,System.Int32,System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            List files
            </summary>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="language"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.Rename(Intalio.CTS.Core.Model.AttachmentNodeModel,Intalio.Core.Language,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16)">
            <summary>
            Rename file
            </summary>
            <param name="model"></param>
            <param name="language"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.CheckUnique(System.Nullable{System.Int64},System.Nullable{System.Int64},System.Int64,System.String,System.String)">
            <summary>
            Check unique file
            </summary>
            <param name="id"></param>
            <param name="folderId"></param>
            <param name="documentId"></param>
            <param name="name"></param>
            <param name="extension"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.Delete(System.Int64,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Delete file
            </summary>
            <param name="id"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.Replace(System.Int64,Intalio.CTS.Core.Model.FileViewModel,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Boolean,System.Nullable{System.Int64},System.Int32)">
            <summary>
            Replace file
            </summary>
            <param name="id"></param>
            <param name="file"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="isOriginalMail"></param>
            <param name="structureId"></param>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.GetStorageAttachmentModel(System.Int64)">
            <summary>
            Get file
            From storage
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.Download(System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Boolean)">
            <summary>
            Download file
            </summary>
            <param name="id"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="logAction"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.DownloadFromViewer(System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Boolean,System.Boolean,System.String)">
            <summary>
            Download file from the Viewer
            </summary>
            <param name="id"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="logAction"></param>
            <param name="withAnnotations"></param>
            <param name="version"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.TransferHasLockedAttachments(System.Int64)">
            <summary>
            Check if transfer has locked attachments
            </summary>
            <param name="transferId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.DocumentHasLockedAttachments(System.Int64)">
            <summary>
            Check if document has locked attachments
            </summary>
            <param name="documentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.Checkout(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Int64,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Boolean)">
            <summary>
            Checkout attachment
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="attachmentId"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="delegationId"></param>
            <param name="isDraft"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.CheckIn(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Int64,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Boolean)">
            <summary>
            CheckIn attachment
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="attachmentId"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="delegationId"></param>
            <param name="isDraft"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.GetAttachmentData(System.Int64,System.String,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Int32)">
            <summary>
            Get attachment data
            From storage
            </summary>
            <param name="fileId"></param>
            <param name="version"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.GetAttachmentInfo(System.Int64,System.String,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int32,System.Int64,System.Nullable{System.Int64})">
            <summary>
            Get attachment metdata
            From storage
            </summary>
            <param name="fileId"></param>
            <param name="version"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.ConvertToPdf(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Convert original attachment to pdf
            </summary>
            <param name="id"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <remarks>
            A new version of file will be create even when the status of the document is draft.
            </remarks>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.GenerateLetterValidation(System.Int64,System.Boolean,System.Boolean,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            validate generate letter
            </summary>
            <param name="id"></param>
            <param name="generateReceivingEntity"></param>
            <param name="generateCarbonCopy"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.GenerateLetter(System.Int64,System.Boolean,System.Boolean,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Boolean,System.Boolean)">
            <summary>
            Generate letter for each receiving entity and carbon copy
            </summary>
            <param name="id"></param>
            <param name="generateReceivingEntity"></param>
            <param name="generateCarbonCopy"></param>
            <param name="convertToPdf"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="isPreview"></param>
            <remarks>
            Generate word document for each receiving entity and carbon copy.<br/><br/>
            Document status: <br/>
            <li><b>Draft</b> : replace all bookmarks</li>
            <li><b>In Progress</b> : replace receiving entity and carbon copy bookmarks</li>
            Merge all documents generated into one word document
            Convert the document to pdf in case <b>convertToPdf</b> is true and replace the file in storage in case <b>isPreview</b> is false
            A new version of file will be create even when the status of the document is draft.
            </remarks>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.ListVersionHistory(System.Int64,System.Int32,System.Int32,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Boolean)">
            <summary>
            List file versions
            </summary>
            <param name="fileId"></param>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="forCopy"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.DeleteVersion(System.Int64,System.Int64,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Delete version
            </summary>
            <param name="id"></param>
            <param name="fileId"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.RestoreVersion(System.Int64,System.Int64,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Restore version
            </summary>
            <param name="id"></param>
            <param name="fileId"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.ListAllVersions(System.Int64)">
            <summary>
            List all versions
            </summary>
            <param name="fileId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.ListAllVersionsIncludeCurrent(System.Int64)">
            <summary>
            List all versions
            Including the current version
            </summary>
            <param name="fileId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.CheckIsAttachmentOriginal(System.Int64)">
            <summary>
            Check if attachment is original document by id
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.DownloadFromViewertoUploadOnDMS(System.Int64)">
            <summary>
            Download file from the Viewer
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.CheckIsAttachmentOriginalNotSigned(System.Int64)">
            <summary>
            Check if attachment is original document and not signed
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.CloneVersionInViewer(System.Int64,System.String,System.String)">
            <summary>
            Clone annotations from old file version to new file version
            </summary>
            <param name="fileId"></param>
            <param name="fromVersion"></param>
            <param name="toVersion"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.HttpJsonPostAsync``1(System.String,System.String,System.Net.Http.StringContent)">
            <summary>
            Used for viewer only
            Authentication Basic not Bearor
            </summary>
            <typeparam name="T"></typeparam>
            <param name="url"></param>
            <param name="token"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachment.SetFileFormData(Intalio.CTS.Core.Model.FileViewModel,System.Collections.Generic.List{System.String},System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Used to set file data for storage 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachmentFolder.Create(System.Int64,Intalio.CTS.Core.Model.AttachmentFolderModel)">
            <summary>
            Add attachment folder
            </summary>
            <param name="userId"></param>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachmentFolder.Edit(System.Int64,Intalio.CTS.Core.Model.AttachmentFolderModel)">
            <summary>
            Edit attachment folder
            </summary>
            <param name="userId"></param>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachmentFolder.Delete(System.Int32)">
            <summary>
            Delete attachment folder
            </summary>
            <param name="id"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachmentFolder.List(System.Nullable{System.Int16},System.Nullable{System.Int32})">
            <summary>
            List attachment folder
            </summary>
            <param name="categoryId"></param>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachmentFolder.CheckUnique(System.Nullable{System.Int32},System.Nullable{System.Int16},System.Nullable{System.Int32},System.String)">
            <summary>
            Check unique attachment folder
            </summary>
            <param name="id"></param>
            <param name="categoryId"></param>
            <param name="parentId"></param>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachmentFolder.ListByCategoryId(System.Int16)">
            <summary>
            List attachment folder by category
            </summary>
            <param name="categoryId"></param>
            <returns></returns>
        </member>
        <member name="T:Intalio.CTS.Core.API.ManageAttachmentSecurity">
            <summary>
            A class To manage the security of the attachments
            Add,Update Or Delete
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachmentSecurity.AddAsync(System.Collections.Generic.List{Intalio.CTS.Core.Model.AttachmentSecurityModel},System.Nullable{System.Int64})">
            <summary>
            Add the attachment Securities
            UserId and StructureId
            </summary>
            <param name="model"></param>
            <param name="AttachmentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachmentSecurity.RemoveSecurityByAttachmentId(System.Nullable{System.Int64})">
            <summary>
            delete the attachment Securities to update with new user and structure ids
            
            </summary>
            <param name="AttachmentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachmentSecurity.ChackAttachmentSecurity(Intalio.CTS.Core.DAL.Attachment,System.Int64,System.Collections.Generic.List{System.Int64})">
            <summary>
            check if the user included in the attachment securities
            </summary>
            <param name="attachment">must included attachment securities</param>
            <param name="UserId"></param>
            <param name="StructureIds"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachmentSecurity.discardChangesAndCheckOutFile(System.Int64,System.String)">
            <summary>
            discard Changes And CheckOut File
            </summary>
            <param name="AttachmentId"></param>
            <param name="version"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageAttachmentSecurity.HttpDeleteAsync``1(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="url"></param>
            <param name="token"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.Create(Intalio.CTS.Core.Model.BasketViewModel,System.Int64)">
            <summary>
            Add Basket
            </summary>
            <param name="model"></param>
            <param name="UserId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.Edit(Intalio.CTS.Core.Model.BasketViewModel,System.Int64)">
            <summary>
            Edit Basket
            </summary>
            <param name="model"></param>
            <param name="UserId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.DeleteBasket(System.Int64,System.Int64)">
            <summary>
            Delete Basket
            </summary>
            <param name="id"></param>
            <param name="UserId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.DeleteBasketDocuments(System.Collections.Generic.List{System.Int64},System.Int32,System.Int64)">
            <summary>
            Delete List Of Documents in Custom Basket
            </summary>
            <param name="documentIds"></param>
            <param name="basketId"></param>
            <param name="UserId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.AddBasketDocuments(System.Collections.Generic.List{System.Int64},System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16)">
             <summary>
            Add List Of Documents in Custom Basket
             </summary>
             <param name="documentIds"></param>
             <param name="basketId"></param>
             <param name="UserId"></param>
             <param name="structureIds"></param>
             <param name="isStructureReceiver"></param>
             <param name="privacyLevel"></param>
             <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.Remove(System.Int64,System.Int64)">
            <summary>
            Remove User From Basket
            </summary>
            <param name="id"></param>
            <param name="UserId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.CheckUnique(System.Nullable{System.Int64},System.String,System.String,System.String,System.Int64)">
            <summary>
            Check unique Basket
            </summary>
            <param name="id"></param>
            <param name="name"></param>
            <param name="nameAr"></param>
            <param name="nameFr"></param>
            <param name="UserId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.List(System.Int64)">
            <summary>
            List Basket
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.ListWithEditRole(System.Int64)">
            <summary>
            List Basket with edit role
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.ListMyBasket(System.Int64)">
            <summary>
            List My Basket
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.ListOtherBasket(System.Int64)">
            <summary>
            List Other Basket
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBasket.GetBasketOwner(System.Int64,System.Int64)">
            <summary>
            Get Basket by id
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBookmark.GenerateLetter(System.Byte[],System.Int64,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Generate Letter
            </summary>
            <param name="word"></param>
            <param name="documentId"></param>
            <param name="convertToPdf"></param>
            <param name="generateReceivingEntity"></param>
            <param name="generateCarbonCopy"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBookmark.ReplaceBookmark(System.Byte[],System.Int64,System.Boolean,System.String)">
            <summary>
            Replace bookmark
            </summary>
            <param name="word"></param>
            <param name="documentId"></param>
            <returns></returns>
            
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBookmark.GetBookmarksByCategoryId(System.Int32)">
            <summary>
            Get bookmarks by categoryId
            </summary>
            <param name="categoryId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageBookmark.GetBookmarkFormById(System.Int32)">
            <summary>
            Get bookmark form by id
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.Create(Intalio.CTS.Core.Model.CategoryViewModel,System.Int64)">
            <summary>
            Add category
            </summary>
            <param name="model"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.Edit(Intalio.CTS.Core.Model.CategoryViewModel,System.Int64,System.Int64)">
            <summary>
            Edit category
            </summary>
            <param name="model"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.Delete(System.Collections.Generic.List{System.Int16})">
            <summary>
            Delete categories
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.CheckUnique(System.Nullable{System.Int16},System.String,System.String,System.String)">
            <summary>
            Check unique category
            </summary>
            <param name="id"></param>
            <param name="name"></param>
            <param name="nameAr"></param>
            <param name="nameFr"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.List(System.String)">
            <summary>
            List category
            By name
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.ListAllCategories">
            <summary>
            List all categories
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.ListCategories(System.Int64,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            List categories by delegation. If delegation not specified, it returns all categories.
            </summary>
            <param name="userId"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.ListCategories(System.String,Intalio.Core.Language)">
            <summary>
            List category
            </summary>
            <param name="name"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.ListNotUsedCategories(System.Collections.Generic.List{System.Int16},Intalio.Core.Language)">
            <summary>
            List not used categories
            </summary>
            <param name="ids"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.ListCategoryIds">
            <summary>
            List category ids
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.GetCustomAttribute(System.Int64)">
            <summary>
            Get category custom attributes
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.UpdateCustomAttribute(Intalio.CTS.Core.Model.CategoryAttributeViewModel)">
            <summary>
            Update category custom attributes
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.GetSearchAttribute(System.Int16)">
            <summary>
            Get category search custom attributes
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.UpdateSearchAttribute(Intalio.CTS.Core.Model.CategoryAttributeViewModel)">
            <summary>
            Update category search custom attributes
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.UpdateBasicAttribute(Intalio.CTS.Core.Model.CategoryAttributeViewModel)">
            <summary>
            Update category basic attribute
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.GetBasicAttribute(System.Int64)">
            <summary>
            Get category basic attributes
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.ListCategoriesByRoleId(System.Int32,System.Int64,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            List categories by roleId
            </summary>
            <param name="roleId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.CheckCategoryPermission(System.Int16,System.Int32)">
            <summary>
            Check category permission by roleId
            </summary>
            <param name="categoryId"></param>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.GetAllowedExtensionsByFile(System.Int16)">
            <summary>
            Get allowed extensions
            </summary>
            <param name="categoryId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.CheckCategoryByFileOrTemplate(System.Int16)">
            <summary>
            Check category by file or by template
            </summary>
            <param name="categoryId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.Import(System.Int64,System.String)">
            <summary>
            Import category
            </summary>
            <param name="userId"></param>
            <param name="jsonData"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.Export(System.Collections.Generic.List{System.Int16})">
            <summary>
            Export category
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.FindWithCaching(System.Int16)">
            <summary>
            Force use caching
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.UpdateConfiguration(System.Int16,System.String,System.String)">
            <summary>
            Update category configuration object value by key
            </summary>
            <param name="id"></param>
            <param name="keyWord"></param>
            <param name="keyWordObject"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.GetConfiguration(System.Int64)">
            <summary>
            Get category configuration
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.ListFullCategories(System.Int64,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            List categories by delegation. If delegation not specified, it returns all categories.
            </summary>
            <param name="userId"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.GetAttachmentProperties(System.Int64)">
            <summary>
            Get category AttachmentProperties
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategory.UpdateAttachmentProperties(Intalio.CTS.Core.Model.CategoryAttributeViewModel)">
            <summary>
            Update category AttachmentProperties
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceCounter.Create(System.Int64,Intalio.CTS.Core.Model.CategoryReferenceCounterViewModel)">
            <summary>
            Add category reference counter
            </summary>
            <param name="userId"></param>
            <param name="model"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceCounter.Edit(Intalio.CTS.Core.Model.CategoryReferenceCounterViewModel)">
            <summary>
            Edit category reference counter
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceCounter.Delete(System.Int64,System.Int64)">
            <summary>
            Delete category reference counter
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceCounter.List(System.Int32,System.Int32,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression})">
            <summary>
            List category reference counter
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceCounter.ListCategoryReferenceCounters">
            <summary>
            List category reference counter
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceCounter.CheckIfExists(System.String)">
            <summary>
            Check category reference counter 
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceCounter.GetReferenceNumberSequence(System.Int16)">
            <summary>
            Get reference number sequence
            </summary>
            <param name="categoryId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceNumber.List">
            <summary>
            List category reference number
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceNumber.Create(System.Int64,System.Nullable{System.Int16},System.Collections.Generic.List{Intalio.CTS.Core.Model.CategoryReferenceNumberViewModel})">
            <summary>
            Add category reference number
            </summary>
            <param name="userId"></param>
            <param name="categoryId"></param>
            <param name="model"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceNumber.ListCategoryReferenceNumberGeneratorTypes(Intalio.Core.Language)">
            <summary>
            List category reference Number Generator Types
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceNumber.DeleteByCategoryId(System.Collections.Generic.List{System.Nullable{System.Int16}})">
            <summary>
            Delete category reference number by category id
            </summary>
            <param name="categoryId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceNumber.CheckIfAllCategoriesExists">
            <summary>
            Check all category exists
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceNumber.GetByCategoryId(System.Int16)">
            <summary>
            Get category reference number by category id
            </summary>
            <param name="categoryId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceNumber.GetForSequenceByCategoryId(System.Int16)">
            <summary>
            Get sequence by category id
            </summary>
            <param name="categoryId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceNumber.CheckIfCounterInUse(System.String)">
            <summary>
            Check counter in use
            </summary>
            <param name="counterId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCategoryReferenceNumber.ListUsedCategories">
            <summary>
            List used categories
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCommittee.Create(Intalio.CTS.Core.Model.CommitteeViewModel)">
            <summary>
            Add entity group
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCommittee.Edit(Intalio.CTS.Core.Model.CommitteeViewModel)">
            <summary>
            Edit entity group
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCommittee.Delete(System.Collections.Generic.List{System.Int64})">
            <summary>
            Delete groups
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCommittee.CheckUnique(System.Nullable{System.Int64},System.String,System.String,System.String)">
            <summary>
            Check unique entity group
            </summary>
            <param name="id"></param>
            <param name="name"></param>
            <param name="nameAr"></param>
            <param name="nameFr"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCommittee.List(System.String,Intalio.Core.Language)">
            <summary>
            List Entity Group
            By name
            </summary>
            <param name="name"></param>
            /// <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCommittee.FilterCommittees(System.Collections.Generic.List{System.Int64})">
            <summary>
            Filter entityGroups
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCommittee.ListStructuresAndUserInGroupById(System.Int64)">
            <summary>
            List Structures and user in Group by entityGroupId from StructureUserGroup
            </summary>
            <param name="entityGroupId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageCustomizationFile.Import(System.Int64,Intalio.Core.CustomizationFileType,Intalio.Core.Model.ExportCustomizationFileViewModel)">
            <summary>
            Import custom file
            </summary>
            <param name="userId"></param>
            <param name="type"></param>
            <param name="model"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDashboardAssignedStructures.FindDashboardAssignedStructures(System.Int64)">
            <summary>
            Returns a list of DashboardAssignedStructures records of a specific structureId input
            </summary>
            <param name="structureId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDashboardAssignedStructures.GetDashboardAssignStructures(System.Collections.Generic.List{System.Int64})">
            <summary>
            Returns a list of DashboardAssignedStructures records of multiple Structures existing in both the table and the input list
            </summary>
            <param name="structureIds"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDistribution.IsExist(System.Int64,Intalio.CTS.Core.Model.DistributionModel)">
            <summary>
            Check If Distribution Item Name Exist 
            </summary>
            <param name="userId"></param>
            <param name="distributionModel"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDistribution.Delete(System.Int64,System.Int64)">
            <summary>
            Delete 
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDistribution.Search(System.String,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int64,System.Nullable{System.Int32},Intalio.Core.Language)">
            <summary>
            
            </summary>
            <param name="text">Text To Search</param>
            <param name="delegationId">Delegation id if exist else null</param>
            <param name="userStructureId">User Structure Id When Transfer</param>
            <param name="structureIds">User Structure Ids</param>
            <param name="isStructureSender">User Structure Sender</param>
            <param name="userId">User Id</param>
            <param name="pageSize">Number of filtered item returned</param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDistribution.List(System.String,System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int64,Intalio.Core.Language)">
            <summary>
            
            </summary>
            <param name="textToSearch"></param>
            <param name="userStructureId"></param>
            <param name="userStructureIds"></param>
            <param name="isStructureSender"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDistribution.List(System.String,System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int64,System.Boolean,System.Boolean)">
            <summary>
            Get List of Distribution Items based on
            Text Search, is User Sender Strucutre, Enable Transfer To user and Sending Rules
            </summary>
            <param name="textToSearch"></param>
            <param name="userStructureId"></param>
            <param name="userStructureIds"></param>
            <param name="isStructureSender"></param>
            <param name="userId"></param>
            <param name="enableTransferToUsers"></param>
            <param name="enableSendingRules"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDistribution.FilterDistributionsAndStructuresList(System.Boolean,System.Boolean,System.Boolean,System.Collections.Generic.List{System.Int64},System.Collections.Generic.List{Intalio.CTS.Core.DAL.Distribution},System.Collections.Generic.List{System.Int64},Intalio.CTS.Core.DAL.Distribution)">
            <summary>
            Filter Structure inside Distribution Item based on
            Is User Structure Sender, Enabled Send to user, and Sending Rules
            if count of structures inside Distrubtion item is zeor then remove it
            </summary>
            <param name="isStructureSender"></param>
            <param name="enableSendingRules"></param>
            <param name="enableTransferToUsers"></param>
            <param name="userStructureIds"></param>
            <param name="lstDistributions"></param>
            <param name="lstStructureIdsSendingRules"></param>
            <param name="distributionItem"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.UpdateDocumentStatusById(System.Int64,Intalio.CTS.Core.DocumentStatus)">
            <summary>
            Update document status
            </summary>
            <param name="item"></param>
            <param name="status"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.UpdateDocumentStatus(Intalio.CTS.Core.DAL.Document,Intalio.CTS.Core.DocumentStatus)">
            <summary>
            Update document status
            </summary>
            <param name="item"></param>
            <param name="status"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.UpdateDocumentStatusByIds(System.Collections.Generic.List{System.Int64},Intalio.CTS.Core.DocumentStatus)">
            <summary>
            Update documents statuses by ids
            </summary>
            <param name="ids"></param>
            <param name="status"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.HasExternalReferencedDocument(Intalio.CTS.Core.DAL.Document,System.Int64,System.Boolean)">
            <summary>
            Check if document has an external document linked to it by external reference number
            </summary>
            <param name="item"></param>
            <param name="userId"></param>
            <param name="isEnableEdit"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CopyDocumentMetadataByCategory(Intalio.CTS.Core.Model.DocumentViewModel,System.Int64,System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Collections.Generic.List{System.String},Intalio.Core.Language)">
            <summary>
            Copy document metadata to a new document
            </summary>
            <param name="model"></param>
            <param name="userId"></param>
            <param name="structureId"></param>
            <param name="prevDocId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="AttachmentIds"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.Create(Intalio.CTS.Core.Model.DocumentViewModel,Intalio.CTS.Core.Model.FileViewModel,System.Int64,System.Int32,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,Intalio.Core.Language)">
            <summary>
            Add document
            </summary>
            <param name="model"></param>
            <param name="file"></param>
            /// <param name="userId"></param>
            <param name="roleId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CreateCopy(Intalio.CTS.Core.Model.DocumentViewModel,Intalio.CTS.Core.Model.FileViewModel,System.Int64,System.Int32,System.Collections.Generic.List{System.Int64},System.Boolean,System.Boolean,System.Int16,System.Int64,System.String,System.Nullable{Intalio.CTS.Core.ActivityLogs},Intalio.Core.Language,System.Nullable{System.Int64},System.Boolean)">
            <summary>
            Add document
            </summary>
            <param name="model"></param>
            <param name="file"></param>
            <param name="userId"></param>
            <param name="roleId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegatedUserId"></param>
            <param name="isStructureSender"></param>
            <param name="language"></param>
            <param name="structureId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.Edit(Intalio.CTS.Core.Model.DocumentViewModel,System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Boolean,System.Nullable{System.Int64},Intalio.Core.Language,System.Boolean)">
            <summary>
            Edit document
            </summary>
            <param name="model"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="transferId"></param>
            <param name="StructureId"></param>
            <param name="language"></param>
            <returns></returns>
            
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.Delete(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean)">
            <summary>
            Delete document
            </summary>
            <param name="userId"></param>
            <param name="ids"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.Export(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64},System.Int16,System.Nullable{System.DateTime},System.String,System.Int64,System.Byte[],System.Boolean,Intalio.Core.Language)">
            <summary>
            Send document
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureSender"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="transferId"></param>
            <param name="purposeId"></param>
            <param name="dueDate"></param>
            <param name="instruction"></param>
            <param name="VoiceNote"></param>
            <param name="structureId"></param>
            <returns></returns>
            
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.FindCreatedByUser(System.Int64,System.Int64,System.Int32,System.Nullable{System.Int64},Intalio.Core.Language,System.Boolean,Intalio.CTS.Core.Model.DelegationModel)">
            <summary>
            For Draft
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CheckStatusInUse(System.Collections.Generic.List{System.Int16})">
            <summary>
            Check statuses in use
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.ListPrioritiesInUse(System.Collections.Generic.List{System.Int16})">
            <summary>
            Check priorities in use
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.FilterCategories(System.Collections.Generic.List{System.Int16})">
            <summary>
            Filter categories
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CheckClassificationInUse(System.Collections.Generic.List{System.Int16})">
            <summary>
            Check classifications in use
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CheckImportanceInUse(System.Collections.Generic.List{System.Int16})">
            <summary>
            Check importances in use
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CheckPrivacyInUse(System.Collections.Generic.List{System.Int16})">
            <summary>
            Check privacies in use
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CheckDocumentTypeInUse(System.Collections.Generic.List{System.Int16})">
            <summary>
            Check documentTypes in use
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.ListDraft(System.Int32,System.Int32,System.Int64,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},System.Nullable{System.Int64},Intalio.Core.Language,System.Boolean,System.Int16)">
            <summary>
            List documents with draft status.
            Must be created by the user
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.ListMyRequests(System.Int32,System.Int32,System.Int64,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List non draft documents.
            Must be created by the user
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.ListCustomBasketDocuments(System.Int32,System.Int32,System.Collections.Generic.List{System.Int64},Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List Custom Basket documents.
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="DocumentIds"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.ListClosed(System.Int32,System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List closed documents.
            Must be created by user or document transfer sent to the user or the user structures
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.GetDraftCounts(System.Int64,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters,System.Nullable{System.Int64},System.Int16)">
            <summary>
            Get documents with draft status count.
            Must be created by the user
            </summary>
            <param name="userId"></param>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.GetMyRequestsCounts(System.Int64,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters)">
            <summary>
            Get non documents count.
            Must be created by the user
            </summary>
            <param name="userId"></param>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.GetClosedCounts(System.Int64,System.Collections.Generic.List{System.Int64},System.Nullable{System.Int64},System.Boolean,System.Int16,Intalio.Core.ExpressionBuilderFilters)">
            <summary>
            Get closed documents count.
            Must be created by user or document transfer sent to the user or the user structures
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.GetDocument(System.Int64,System.Int64,System.Nullable{System.Int32},System.Collections.Generic.List{System.Int64},Intalio.Core.Language,System.Nullable{System.Int32})">
            <summary>
            Must be created by user
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="language"></param>
            <param name="basketId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.GetSearchDocument(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64},System.Nullable{System.Int64},Intalio.Core.Language,System.Boolean)">
            <summary>
            Get document
            Must be created by user or have access on any document transfer
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.FindDocumentBasicInfoByTransferId(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64},Intalio.Core.Language,System.Boolean,System.Nullable{System.Int16})">
            <summary>
            Get document metadata by transferId
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.FindDocumentBasicInfo(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Get document metadata
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.GetTrackingData(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Int32,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Get document tracking data.
            Including document transfers
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="documentId"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.FindByTransferId(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Boolean,System.Nullable{System.Int64},Intalio.Core.Language,System.Int32)">
            <summary>
            For Non Draft
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.GenerateBarcode(System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Generate barcode
            </summary>
            <param name="documentId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.FindDocumentIdByFileId(System.Int64)">
            <summary>
            Find document id by file id
            </summary>
            <param name="fileId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CheckDocumentDraft(System.Int64)">
            <summary>
            Check document draft
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CheckOriginalDocumentLocked(System.Int64)">
            <summary>
            Check if original document is locked
            </summary>
            <param name="documentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CheckHasLockedAttachmentsWithOriginal(System.Int64)">
            <summary>
            Check if original document is locked
            </summary>
            <param name="documentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.UpdateDocumentStatus(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,Intalio.CTS.Core.DocumentStatus,System.Nullable{System.Int64})">
            <summary>
            Update document status
            </summary>
            <param name="documentId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="status"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.HasExternalReferencedDocument(System.Int64,System.Int64)">
            <summary>
            Check if document has an external document linked to it by external reference number
            </summary>
            <param name="id"></param>
            /// <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.Complete(System.Int64,System.Int64,System.Int64,System.Int16,System.Collections.Generic.List{System.Int64},System.String,System.Boolean)">
            <summary>
            Complete draft document
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="purposeId"></param>
            <param name="visibleForStructureUsers"></param>
            <returns></returns>
            <remarks>
            Document will be completed and a closed transfer will be created and sent to document created by structure in case visibleForStructureUsers is <b>true</b>
            Document will be completed and a closed transfer will be created and sent to current user in case visibleForStructureUsers is <b>false</b>
            </remarks>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CreateReplyByCategory(Intalio.CTS.Core.Model.DocumentViewModel,Intalio.CTS.Core.Model.FileViewModel,System.Int64,System.Int64,System.Int64,System.Int32,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Boolean,System.Collections.Generic.List{System.String},Intalio.Core.Language)">
            <summary>
            Add document for reply by category
            </summary>
            <param name="model"></param>
            <param name="file"></param>
            <param name="prevDocId"></param>
            <param name="userId"></param>
            <param name="structureId"></param>
            <param name="roleId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="isStructureSender"></param>
            <param name="privacyLevel"></param>
            <param name="AttachmentIds"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.ListMyRequestsVip(System.Int32,System.Int64,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language)">
            <summary>
            List non draft documents for vip mode.
            Must be created by the user
            </summary>
            <param name="startIndex"></param>
            <param name="userId"></param>
            <param name="filter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.ListClosedVip(System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language)">
            <summary>
            List closed documents for vip mode.
            Must be created by user or document transfer sent to the user or the user structures
            </summary>
            <param name="startIndex"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="filter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.ListDraftVip(System.Int32,System.Int64,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language,System.Boolean,System.Int16)">
            <summary>
            List documents with draft status for vip mode.
            Must be created by the user
            </summary>
            <param name="startIndex"></param>
            <param name="userId"></param>
            <param name="filter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.GetDraftDocument(System.Int64,System.Int64,Intalio.Core.Language)">
            <summary>
            Must be created by user
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.UpdateDocumentIsSigned(System.Int64,System.Boolean,System.String)">
            <summary>
            Update IsSigned property of target document by id
            </summary>
            <param name="id"></param>
            <param name="isSigned"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.IsDocumentRegistered(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16)">
            <summary>
            Check if document is registered
            </summary>
            <param name="id"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.IsDocumentCompleted(System.Int64)">
            <summary>
            Check if document is Completed
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CreateMeetingResolution(Intalio.CTS.Core.Model.DocumentViewModel,Intalio.CTS.Core.Model.FileViewModel,System.Int64,System.Int64,System.Int64,System.Int32,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Boolean)">
            <summary>
            Add document for reply by Meeting Resolution
            </summary>
            <param name="model"></param>
            <param name="file"></param>
            <param name="prevDocId"></param>
            <param name="userId"></param>
            <param name="structureId"></param>
            <param name="roleId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="isStructureSender"></param>
            <param name="privacyLevel"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CreateMeeting(Intalio.CTS.Core.Model.DocumentViewModel,Intalio.CTS.Core.Model.FileViewModel,System.Collections.Generic.List{System.Int64},System.Int64,System.Int64,System.Int64,System.Int32,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Boolean)">
            <summary>
            Add document for reply by Meeting 
            </summary>
            <param name="model"></param>
            <param name="file"></param>
            <param name="SelectedDocumentIds"></param>
            <param name="userId"></param>
            <param name="structureId"></param>
            <param name="roleId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="isStructureSender"></param>
            <param name="privacyLevel"></param>
            <param name="PreviousDocumentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CreateMeetingAgenda(Intalio.CTS.Core.Model.DocumentViewModel,Intalio.CTS.Core.Model.FileViewModel,System.Collections.Generic.List{System.Int64},System.Int64,System.Int32,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16)">
            <summary>
            Add document
            </summary>
            <param name="model"></param>
            <param name="file"></param>
            <param name="userId"></param>
            <param name="roleId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="SelectedDocumentIds"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.DeleteFollowUpDocument(System.Int64)">
            <summary>
            Delete FollowUp document
            </summary>
            <param name="documentId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.Reopen(System.Collections.Generic.List{System.Int64})">
            <summary>
            Reopen followUp that closed
            </summary>
            <param name="documentIds"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.Archive(System.Int64,System.Int64,System.Int32,System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Complete draft document
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="roleId"></param>
            <param name="isStructureReciver"></param>
            <param name="structureId"></param>
            <param name="StructureIds"></param>
            <param name="privacyLevel"></param>
            <param name="rootFolderId"></param>
            <returns></returns>
            <remarks>
            Document will be completed and a closed transfer will be created and sent to document created by structure in case visibleForStructureUsers is <b>true</b>
            Document will be completed and a closed transfer will be created and sent to current user in case visibleForStructureUsers is <b>false</b>
            </remarks>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.ResendRejectedDocument(System.Int64,System.Nullable{System.Int64},System.Int64,System.Int64,Intalio.Core.Language)">
            <summary>
            Recall transfer
            </summary>
            <param name="id"></param>
            <param name="delegationId"></param>
            <param name="userId"></param>
            <param name="structureId"></param>
            <param name="lang"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocument.CheckDocumentHasAttachments(System.Int64)">
            <summary>
            Check if document has an attachments
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocumentLock.LockDocumentAsync(System.Int64,System.Int64)">
            <summary>
            Locks the document by the given userId
            </summary>
            <param name="documentId">The document id to be locked</param>
            <param name="userId">The user id who has triggered this action</param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocumentLock.LockedByUserAsync(System.Int64)">
            <summary>
            Returns the id of the user that locked the document
            </summary>
            <param name="documentId">The document id</param>
            <returns>The user id returned, might be null if the document is not locked</returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocumentLock.UnlockDocument(System.Int64,System.Int64,System.Int64,System.Int16)">
            <summary>
            Unlocks the document
            </summary>
            <param name="documentId">The id of the document that was locked</param>
            <param name="userId">The user id who was responsible for locking the document</param>
            <returns>Returns a status corresponding to the unlock if it was successful or failure</returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageDocumentLock.EditAfterExport(System.Int64,System.Int64,System.Int64,System.Int16,Intalio.Core.Language)">
            <summary>
            Unlocks the document
            </summary>
            <param name="documentId">The id of the document that was locked</param>
            <param name="userId">The user id who was responsible for locking the document</param>
            <param name="structureId"></param>
            <param name="privacyLevel"></param>
            <param name="language"></param>
            <returns>Returns a status corresponding to the unlock if it was successful or failure</returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageEntityGroup.Create(Intalio.CTS.Core.Model.EntityGroupViewModel)">
            <summary>
            Add entity group
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageEntityGroup.Edit(Intalio.CTS.Core.Model.EntityGroupViewModel)">
            <summary>
            Edit entity group
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageEntityGroup.Delete(System.Collections.Generic.List{System.Int64})">
            <summary>
            Delete groups
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageEntityGroup.CheckUnique(System.Nullable{System.Int64},System.String,System.String,System.String)">
            <summary>
            Check unique entity group
            </summary>
            <param name="id"></param>
            <param name="name"></param>
            <param name="nameAr"></param>
            <param name="nameFr"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageEntityGroup.List(System.String,System.Nullable{System.Byte})">
            <summary>
            List Entity Group
            By name
            </summary>
            <param name="name"></param>
            /// <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageEntityGroup.FilterGroups(System.Collections.Generic.List{System.Int64})">
            <summary>
            Filter entityGroups
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageEntityGroup.ListStructuresAndUserInGroupById(System.Int64)">
            <summary>
            List Structures and user in Group by entityGroupId from StructureUserGroup
            </summary>
            <param name="entityGroupId"></param>
            <returns></returns>
        </member>
        <member name="T:Intalio.CTS.Core.API.ManageFavoriteStructure">
            <summary>
            Manage Favorite Structure
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFavoriteStructure.Create(System.Int64,Intalio.CTS.Core.Model.FavoriteStructureModel)">
            <summary>
            Create New Favorite
            </summary>
            <param name="userId">Created User Id</param>
            <param name="favoriteStructureModel">Favorite Strcute Model</param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFavoriteStructure.IsExist(System.Int64,Intalio.CTS.Core.Model.FavoriteStructureModel)">
            <summary>
            Check if Favorite Item Exist
            </summary>
            <param name="userId">User Id</param>
            <param name="favoriteStructureModel">Favorite Strucutre Model</param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFavoriteStructure.Delete(System.Int64)">
            <summary>
            Delete Facorite Strucutre
            </summary>
            <param name="id">Favorite Strucutre Id</param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFavoriteStructure.List(System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int64,Intalio.Core.Language)">
            <summary>
            
            </summary>
            <param name="userStructureId">Selectd User Structed Id When Trnasfer Corresponding</param>
            <param name="userStructureIds">User Structures</param>
            <param name="isStructureSender">Is User Strucutre Sender</param>
            <param name="userId">User Id</param>
            <param name="language">Language</param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFavoriteStructure.List(System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int64,System.Boolean,System.Boolean)">
            <summary>
            Get List Of Favorite Structures based on Structure Sender Or Sending Rules Or Enable Transfer To Users
            </summary>
            <param name="userStructureId"></param>
            <param name="userStructureIds"></param>
            <param name="isStructureSender"></param>
            <param name="userId"></param>
            <param name="enableTransferToUsers"></param>
            <param name="enableSendingRules"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFilingPlan.Create(System.Int64,Intalio.CTS.Core.Model.FilingPlanViewModel)">
            <summary>
            Add filing plan
            </summary>
            <param name="userId"></param>
            <param name="model"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFilingPlan.Edit(System.Int64,Intalio.CTS.Core.Model.FilingPlanViewModel)">
            <summary>
            Edit filing plan
            </summary>
            <param name="userId"></param>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFilingPlan.Delete(System.Int64,System.Int64)">
            <summary>
            Delete filing plan
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFilingPlan.ListFilingPlan(System.Nullable{System.Int16},Intalio.Core.Language)">
            <summary>
            List filing plan
            </summary>
            <param name="categoryId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFilingPlan.ListFilingPlanKeyword(Intalio.Core.Language)">
            <summary>
            List filing plan keyword
            </summary>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFilingPlan.ListFilingPlanByCategoryId(System.Int16)">
            <summary>
            List filing plan for category or get global
            </summary>
            <param name="categoryId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFolder.Create(Intalio.CTS.Core.Model.AttachmentNodeModel,Intalio.Core.Language,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16)">
            <summary>
            Add folder
            </summary>
            <param name="model"></param>
            <param name="language"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFolder.Edit(Intalio.CTS.Core.Model.AttachmentNodeModel,Intalio.Core.Language,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16)">
            <summary>
            Edit folder
            </summary>
            <param name="model"></param>
            <param name="language"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFolder.CheckUnique(System.Nullable{System.Int64},System.Nullable{System.Int64},System.String,System.Int64)">
            <summary>
            Check folder unique
            </summary>
            <param name="id"></param>
            <param name="parentId"></param>
            <param name="name"></param>
            <param name="documentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFolder.HasChildren(System.Int64)">
            <summary>
            Check folder has children
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFolder.Delete(System.Int64,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Delete folder
            </summary>
            <param name="id"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFolder.GetFolderPath(System.Int64)">
            <summary>
            Get folder path
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFolder.Find(System.String,System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
            Get folder by name,parentId and documentId
            </summary>
            <param name="name"></param>
            <param name="parentId"></param>
            <param name="documentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFollowUp.Create(Intalio.CTS.Core.Model.FollowUpCreateModel,System.Int64,System.Int32,System.Collections.Generic.List{System.Int64},System.Boolean,System.Boolean,System.Int16,System.Int64,Intalio.Core.Language)">
            <summary>
            Add FollowUp
            </summary>
            <param name="model"></param>
            <param name="userId"></param>
            <param name="roleId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFollowUp.Get(System.Int64,System.Int64,System.Int32,System.Int64,Intalio.Core.Language)">
            <summary>
            For FollowUp Details
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFollowUp.ListFollowUp(System.Int32,System.Int32,System.Int64,System.Int64,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List documents with FollowUp status.
            Must be created by the user
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFollowUp.GetFollowUpCounts(System.Int64,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters)">
            <summary>
            Get documents with FollowUp status count.
            Must be created by the user
            </summary>
            <param name="userId"></param>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFollowUp.ListAllFollowUpStatuses">
            <summary>
            List all followUpStatuses
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFollowUp.ListFollowUpStatuses(Intalio.Core.Language)">
            <summary>
            List categories returns all categories DDL.
            </summary>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFollowUp.ListFollowUpSecurityDDL(Intalio.Core.Language)">
            <summary>
            List FollowUp Security returns all FollowUp Security DDL.
            </summary>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFollowUp.ListFollowUpDocumentTransfers(System.Int32,System.Int32,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List documents with FollowUp status.
            Must be created by the user
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFollowUp.AddUserToFollowUp(Intalio.CTS.Core.Model.FollowUpSecurityModel,System.Int64,Intalio.Core.Language)">
            <summary>
            Add User To FollowUp
            </summary>
            <param name="model"></param>
            <param name="userId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFollowUp.UpdateFollowUpUserRole(Intalio.CTS.Core.Model.FollowUpSecurityModel,System.Int64,Intalio.Core.Language)">
            <summary>
            Edit User To FollowUp
            </summary>
            <param name="model"></param>
            <param name="userId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageFollowUp.CreateFollowUpPanel(Intalio.CTS.Core.Model.FollowUpPanelCreateModel,System.Int64,Intalio.Core.Language)">
            <summary>
            Add FollowUp panel
            </summary>
            <param name="model"></param>
            <param name="userId"></param>
            <param name="roleId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageG2G.List(System.String,System.Int32,System.Int32,System.Int64,System.Int64,System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List G2G transfers. 
            Must be sent to the user or the user structures 
            </summary>
            <param name="ViewName"></param>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageG2G.GetG2GCounts(System.String,System.Int64,System.Int64,System.Nullable{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters)">
            <summary>
            Get g2g transfers count. 
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageImportExport.GetExportedData(Intalio.CTS.Core.Model.ExportModel)">
            <summary>
            Get exported data
            </summary>
            <param name="exportModel"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageImportExport.ImportData(System.Int64,System.String)">
            <summary>
            Import json
            </summary>
            <param name="userId"></param>
            <param name="jsonData"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageLinkedDocument.Create(System.Int64,System.Collections.Generic.List{System.Int64},System.Int64,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,Intalio.Core.Language)">
            <summary>
            Add linked document
            </summary>
            <param name="userId"></param>
            <param name="linkDocumentIds"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="delegationId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageLinkedDocument.List(System.Int64,System.Nullable{System.Int64},System.Int64,System.Int32,System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            List linked document
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageLinkedDocument.Delete(System.Int64,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Delete linked document
            </summary>
            <param name="id"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageLinkedDocument.DeleteByDocumentId(System.Int64)">
            <summary>
            Delete linked document by MainDocumentId
            </summary>
            <param name="documentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNode.GetNodeConditionsInExpressionBuilder(System.Int16)">
            <summary>
            Get node conditions in expression builder
            </summary>
            <param name="nodeId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNonArchivedAttachments.Create(Intalio.CTS.Core.Model.NonArchivedAttachmentsViewModel,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            User must have access on transfer or on document
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNonArchivedAttachments.Edit(Intalio.CTS.Core.Model.NonArchivedAttachmentsViewModel,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Case 1: CreatedByUserId equals current user id
            Case 2: if delegationId have value return to case 1
            Case 3: if delegationId don't have value and CreatedByUserId equals current user id 
            and transferId if have value must equal to nonArchivedAttachments transferId 
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNonArchivedAttachments.List(System.Int32,System.Int32,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            User must have access on transfer or on document
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNonArchivedAttachments.Delete(System.Int64,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Case 1: CreatedByUserId equals current user id
            Case 2: if delegationId have value return to case 1
            Case 3: if delegationId don't have value and CreatedByUserId equals current user id 
            and transferId if have value must equal to nonArchivedAttachments transferId 
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNonArchivedAttachmentsTypes.Create(Intalio.CTS.Core.Model.NonArchivedAttachmentsTypesViewModel)">
            <summary>
            Add non archived attachments types
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNonArchivedAttachmentsTypes.Edit(Intalio.CTS.Core.Model.NonArchivedAttachmentsTypesViewModel)">
            <summary>
            Edit non archived attachments types
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNonArchivedAttachmentsTypes.Delete(System.Collections.Generic.List{System.Int16})">
            <summary>
            Delete non archived attachments types
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNonArchivedAttachmentsTypes.List(System.String)">
            <summary>
            List non archived attachments types
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNonArchivedAttachmentsTypes.ListTypes(Intalio.Core.Language)">
            <summary>
            List non archived attachments types
            </summary>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNonArchivedAttachmentsTypes.CheckUnique(System.Nullable{System.Int16},System.String,System.String,System.String)">
            <summary>
            Check unique non archived attachments types
            </summary>
            <param name="id"></param>
            <param name="name"></param>
            <param name="nameAr"></param>
            <param name="nameFr"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNote.Create(Intalio.CTS.Core.Model.NoteViewModel,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            User must have access on transfer or on document
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNote.Edit(Intalio.CTS.Core.Model.NoteViewModel,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Case 1: CreatedByUserId equals current user id
            Case 2: if delegationId have value return to case 1
            Case 3: if delegationId don't have value and CreatedByUserId equals current user id 
            and transferId if have value must equal to note transferId 
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNote.Delete(System.Int64,System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Case 1: CreatedByUserId equals current user id
            Case 2: if delegationId have value return to case 1
            Case 3: if delegationId don't have value and CreatedByUserId equals current user id 
            and transferId if have value must equal to note transferId 
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageNote.List(System.Int32,System.Int32,System.Int64,System.Nullable{System.Int64},System.Int64,System.Int32,System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            User must have access on transfer or on document
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManagePurpose.ListPurposes(Intalio.Core.Language)">
            <summary>
            It will check for EnableCaching parameter
            </summary>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManagePurpose.ListSecurity">
            <summary>
            Security list purpose
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManagePurpose.GetCCedPurpose">
            <summary>
            It will check for EnableCaching parameter
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManagePurpose.FindWithCaching(System.Int16)">
            <summary>
            Force use caching
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListCompletedCorrespondences(System.Int32,System.Int32,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List Completed correspondences.
            Must be non draft.
            Not all data is retrieved (paging is supported).
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="filter"></param>
            <param name="stcFilter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <param name="sortExpression"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListCompletedCorrespondencesRetrievingAllData(Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language)">
            <summary>
            List Completed correspondence.
            Must be non draft.
            All data is retrieved (paging is not supported). 
            </summary>
            <param name="filter"></param>
            <param name="stcFilter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListInProgressCorrespondences(System.Int32,System.Int32,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List InProgress correspondences.
            Must be non draft.
            Not all data is retrieved (paging is supported).
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="filter"></param>
            <param name="stcFilter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <param name="sortExpression"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListInProgressCorrespondencesRetrievingAllData(Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language)">
            <summary>
            List InProgress correspondence.
            Must be non draft.
            All data is retrieved (paging is not supported). 
            </summary>
            <param name="filter"></param>
            <param name="stcFilter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListTransfersSentToStructureAsync(System.Int32,System.Int32,System.Int32,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List InProgress transfers.
            Must be non draft.
            Not all data is retrieved (paging is supported).
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="filter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <param name="sortExpression"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListTransfersSentToStructureRetrievingAllData(System.Int32,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language)">
            <summary>
            List InProgress transfers.
            Must be non draft.
            All data is retrieved (paging is not supported). 
            </summary>
            <param name="filter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListOutgoingFromDepartmentAsync(System.Int32,System.Int32,System.Int32,System.Boolean,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List InProgress transfers.
            Must be non draft.
            Not all data is retrieved (paging is supported).
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="departmentId"></param>
            <param name="getAll"></param>
            <param name="language"></param>
            <param name="sortExpression"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListInProgressTransfers(System.Int32,System.Int32,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List InProgress transfers.
            Must be non draft.
            Not all data is retrieved (paging is supported).
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="filter"></param>
            <param name="stcFilter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <param name="sortExpression"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListInProgressTransfersRetrievingAllData(Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language)">
            <summary>
            List InProgress transfers.
            Must be non draft.
            All data is retrieved (paging is not supported). 
            </summary>
            <param name="filter"></param>
            <param name="stcFilter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListCompletedTransfers(System.Int32,System.Int32,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List completed transfers.
            Must be non draft.
            Not all data is retrieved (paging is supported).
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="filter"></param>
            <param name="stcFilter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <param name="sortExpression"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListCompletedTransfersRetrievingAllData(Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language)">
            <summary>
            List Completed transfers.
            Must be non draft
            All data is retrieved (paging is not supported). 
            </summary>
            <param name="filter"></param>
            <param name="stcFilter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListOperationByUser(System.Int32,System.Int32,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List Operations By User.
            Must be non draft.
            Not all data is retrieved (paging is supported).
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="filter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <param name="sortExpression"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListOperationByUserRetrievingAllData(Intalio.Core.ExpressionBuilderFilters,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language)">
            <summary>
            List Operations By User.
            Must be non draft.
            All data is retrieved (paging is not supported).
            </summary>
            <param name="filter"></param>
            <param name="userFilter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListOperationByCorrespondence(System.Int32,System.Int32,Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List Operations By Correspondence.
            Must be non draft.
            Not all data is retrieved (paging is supported).
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="filter"></param>
            <param name="language"></param>
            <param name="sortExpression"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListOperationByCorrespondenceRetrievingAllData(Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language)">
            <summary>
            List Operations By Correspondence.
            Must be non draft.
            All data is retrieved (paging is not supported). 
            </summary>
            <param name="filter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.GetCreatedCountByStructure(System.Int64,System.Collections.Generic.List{System.Int64},System.Int32,System.Int32,System.Int32,System.Boolean,System.Int16,System.Collections.Generic.List{System.Int64},System.Collections.Generic.List{System.Int64},System.String,System.String,Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language,System.Boolean)">
            <summary>
            Displays for each structure; can include users also, the total number of 
            created correspondence per category and the total number of sent and received transfers
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="roleId"></param>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="selectedStructures"></param>
            <param name="selectedUsers"></param>
            <param name="fromDate"></param>
            <param name="toDate"></param>
            <param name="filter"></param>
            <param name="language"></param>
            <param name="includeUsers"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.GetStatisticalReportCreatedByUser(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Int32,System.Boolean,System.Int16,System.Collections.Generic.List{System.Int64},System.Collections.Generic.List{System.Int64},System.String,System.String,Intalio.Core.Language)">
            <summary>
            Displays for each  each user inside a structure the total number of 
            created correspondence per category and the total number of sent and received transfers
            </summary>
            <param name="targetStructureId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="roleId"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="selectedUsers"></param>
            <param name="selectedStructures"></param>
            <param name="fromDate"></param>
            <param name="toDate"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.FindDocumentIdByReferenceNumberSecured(System.String,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16)">
            <summary>
            Return the Id of a document by reference number if the user has access
            </summary>
            <param name="referenceNumber"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.GetDocumentCorrespondenceDetail(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Get correspondence detail by document id
            </summary>
            <param name="documentId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListTransferCorrespondenceDetail(System.Int64,System.Int32,System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Collections.Generic.List{Intalio.Core.SortExpression},System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Get transfers details of a targeted correspondence by document id
            Not all data is retrieved (paging is supported).
            </summary>
            <param name="documentId"></param>
            <param name="start"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="sortExpression"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListTransferCorrespondenceDetailRetrievingAllData(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Collections.Generic.List{Intalio.Core.SortExpression},System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Get transfers details of a targeted correspondence by document id
            All data is retrieved (paging is not supported).
            </summary>
            <param name="documentId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="sortExpression"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListNoteCorrespondenceDetail(System.Int64,System.Int32,System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            List notes for Correspondence Detail Report
            Not all data is retrieved (paging is supported).
            </summary>
            <param name="documentId"></param>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListNoteCorrespondenceDetailRetrievingAllData(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            List notes for Correspondence Detail Report
            All data is retrieved (paging is not supported).
            </summary>
            <param name="documentId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListNonArchivedAttachmentsCorrespondenceDetail(System.Int64,System.Int32,System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            List non archive attachments for Correspondence Detail Report
            Not all data is retrieved (paging is supported).
            </summary>
            <param name="documentId"></param>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListNonArchivedAttachmentsCorrespondenceDetailRetrievingAllData(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            List non archive attachments for Correspondence Detail Report
            All data is retrieved (paging is not supported).
            </summary>
            <param name="documentId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ListLinkedDocumentCorrespondenceDetail(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            List linked document for  Correspondence Detail Report
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="documentId"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ExportPdfCorrespondenceDetailDocument(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Export correspondence detail report as pdf
            </summary>
            <param name="documentId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ExportExcelCorrespondenceDetailDocument(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Export correspondence detail report as excel
            </summary>
            <param name="documentId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.ConvertToPdf(System.Byte[],System.String)">
            <summary>
            convert any attachment which is not pdf to pdf
            </summary>
            <param name="data"></param>
            <param name="fileName"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.IsPdf(System.Byte[])">
            <summary>
            check attachment is pdf or not
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageReport.MergePdfDocuments(System.Collections.Generic.List{System.Byte[]})">
            <summary>
            Merge Attachments  as pdf
            </summary>
            <param name="pdfDocuments"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageScanConfiguration.CreateScanConfiguration(Intalio.CTS.Core.Model.ScanConfigurationModel)">
            <summary>
            Create scan configuration
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageScanConfiguration.Create(System.Collections.Generic.List{Intalio.CTS.Core.Model.ScanConfigurationModel})">
            <summary>
            Create scan configurations
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageScanConfiguration.Edit(Intalio.CTS.Core.Model.ScanConfigurationModel)">
            <summary>
            Edit scan configuration
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageScanConfiguration.Delete(System.Collections.Generic.List{System.Int16})">
            <summary>
            Delete scan configuration
            </summary>
            <param name="ids"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageScanConfiguration.List">
            <summary>
            List scan configuration
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageScanConfiguration.CheckUnique(System.Nullable{System.Int16},System.Nullable{System.Int16})">
            <summary>
            Check unique scan configuration
            </summary>
            <param name="id"></param>
            <param name="categoryId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageScanConfiguration.ListUsedCategories">
            <summary>
            List used categories scan configuration
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageScanConfiguration.ListByCategoryId(System.Int32)">
            <summary>
            List scan configuration by category
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageScanConfiguration.GetGlobalOrByCategoryId(System.Int32)">
            <summary>
            Get scan configuration
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageSearch.SearchFollowUp(Intalio.CTS.Core.Model.SearchModel,System.Int32,System.Int32,System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Int16,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List searched documents
            </summary>
            <param name="model"></param>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageSearch.Search(Intalio.CTS.Core.Model.SearchModel,System.Int32,System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Collections.Generic.List{Intalio.Core.SortExpression},System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            List searched documents
            </summary>
            <param name="model"></param>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageSearch.AdvanceSearch(Intalio.CTS.Core.Model.AdvanceSearchModel,System.Collections.Generic.List{Intalio.Core.Model.FormBuilderInputs},System.Int32,System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language,System.String)">
            <summary>
            List searched documents
            </summary>
            <remarks>
            SearchComparisonOperator must be added in search control Custom Properties.<br/><br/>
            Search comparison operator values:<br/>Like ,Equal ,LessThanOrEqual ,LessThan ,GreaterThanOrEqual ,GreaterThan ,NotEqual ,Contains<br/><br/>
            The default value is related to the control type. The default value: <br/>
            <li><b>Equal</b> is used for controls: <i>checkbox</i>, <i>number</i>, <i>datetime</i>, <i>time</i></li>
            <li><b>Like</b> is used for controls: <i>textfield</i>, <i>textarea</i>, <i>select</i>, <i>radio</i>, <i>tags</i> and<i>"other controls"</i></li>
            </remarks>
            <param name="model"></param>
            <param name="additionInputs"></param>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Intalio.CTS.Core.API.ManageSearch.ManageCorrespondenceSearch(Intalio.CTS.Core.Model.SearchModel,System.Int32,System.Int32,System.Int64,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)" -->
        <member name="M:Intalio.CTS.Core.API.ManageSearchAssignedSecurity.FindSearchAssignedSecurity(System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
             Return list of search assigned security to user or structure
            </summary>
            <param name="userId"></param>
            <param name="structureId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageSearchAssignedSecurity.GetSearchAssignedSecurity(System.Int64,System.Collections.Generic.List{System.Int64})">
            <summary>
            Get search assign security by user and user structures
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageSendingRule.ListStructureIdsBySendingRules(System.Int64,System.Int64,System.Nullable{System.Int64})">
            <summary>
            List structureIds by structure and userId
            </summary>
            <param name="userId"></param>
            <param name="structureId"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageStructure.Provision(System.Collections.Generic.List{System.Int64})">
            <summary>
            Mapping structures from IAM
            </summary>
            <param name="ids"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTeams.ListTeams(System.Int64,System.Int64)">
            <summary>
            List teams returns all teams DDL.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTemplate.Create(Intalio.CTS.Core.Model.TemplateModel,System.Int64)">
            <summary>
            Add template
            Without data
            </summary>
            <param name="model"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTemplate.Edit(Intalio.CTS.Core.Model.TemplateModel,System.Int64)">
            <summary>
            Edit template
            </summary>
            <param name="model"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTemplate.Delete(System.Int32)">
            <summary>
            Delete template
            </summary>
            <param name="id"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTemplate.List(System.Nullable{System.Int16},System.Nullable{System.Int64},System.Nullable{System.Int32},System.Boolean)">
            <summary>
            List template
            </summary>
            <param name="categoryId"></param>
            <param name="structureId"></param>
            <param name="id"></param>
            <param name="withOutGlobal"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTemplate.CheckUnique(System.Nullable{System.Int32},System.Nullable{System.Int16},System.Nullable{System.Int64},System.Nullable{System.Int32},System.String)">
            <summary>
            Check unique template
            </summary>
            <param name="id"></param>
            <param name="categoryId"></param>
            <param name="structureId"></param>
            <param name="parentId"></param>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTemplate.FindById(System.Int32)">
            <summary>
            Get template
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTemplate.FindAttachmentDataByTemplateId(System.Int32)">
            <summary>
            Get template data
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTemplate.CreateFile(Intalio.CTS.Core.Model.TemplateModel,System.Int64)">
            <summary>
            Add template
            With data
            </summary>
            <param name="model"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTemplate.ListTemplatesByCategoryId(System.Int16,System.Collections.Generic.List{System.Int64},System.Int64,Intalio.Core.Language)">
            <summary>
            List templates
            </summary>
            <param name="categoryId"></param>
            <param name="structureIds"></param>
            <param name="userId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTemplate.PersonalTemplateList(System.Nullable{System.Int64})">
            <summary>
            List template
            </summary>
            <param name="categoryId"></param>
            <param name="structureId"></param>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.Close(System.Int64)">
            <summary>
            Close transfer by id
            </summary>
            <param name="id"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.CloseParentTransferByList(System.Collections.Generic.List{System.Int64})">
            <summary>
            Close transfers by ids
            </summary>
            <param name="ids"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.CompleteAndSendToReceiverEntity(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Nullable{System.Int64},System.Int16,Intalio.CTS.Core.DAL.Document,System.Nullable{System.DateTime},System.String)">
            <summary>
            Create and complete transfers based on document receiver
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureSender"></param>
            <param name="transferId"></param>
            <param name="item"></param>
            <param name="dueDate"></param>
            <param name="instruction"></param>
            <param name="structureId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.CheckPurposesInUse(System.Collections.Generic.List{System.Int16})">
            <summary>
            Check purposes in use
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.ListInbox(System.Int32,System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Int64,System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language,System.Boolean)">
            <summary>
            List in progress transfers. 
            Must be sent to the user or the user structures 
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="structureId"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <param name="fromStructure"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.ListCompleted(System.Int32,System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List closed transfers. 
            Must be sent to the user or the user structures 
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.GetInboxCounts(System.Int64,System.Collections.Generic.List{System.Int64},System.Nullable{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters,System.Nullable{System.Boolean},System.Boolean)">
            <summary>
            Get in progress transfers count. 
            Must be sent to the user or the user structures 
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <param name="isOverdue"></param>
            <param name="loggedInStructureId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.GetCompletedCounts(System.Int64,System.Collections.Generic.List{System.Int64},System.Nullable{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters)">
            <summary>
            Get closed transfers count. 
            Must be sent to the user or the user structures 
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.GetTransferVoiceNoteById(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Get transfer metadata
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.GetTransferInfoById(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64},Intalio.Core.Language,System.Int64,System.Boolean)">
            <summary>
            Get transfer metadata
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <param name="categoryId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.SendToReceiverEntity(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Nullable{System.Int64},Intalio.CTS.Core.DAL.Document,System.Int16,System.Nullable{System.DateTime},System.String,Intalio.Core.Language,System.Byte[],System.Boolean,System.Boolean,System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
            Create transfers based on document receiver and carbon copy
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureSender"></param>
            <param name="transferId"></param>
            <param name="item"></param>
            <param name="purposeId"></param>
            <param name="dueDate"></param>
            <param name="instruction"></param>
            <param name="VoiceNote"></param>
            <param name="structureId"></param>
            <param name="isBroadcast"></param>
            <param name="VoiceNotePrivacy"></param>
            
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.ReplyAsync(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Boolean,System.Int16,System.Int64,System.Int64,System.Int16,System.Nullable{System.DateTime},System.String,System.Byte[],System.Boolean,System.Int64,System.Nullable{System.Int64},Intalio.CTS.Core.ReplyType,System.Boolean,Intalio.Core.Language)">
            <summary>
            Reply transfer to previous structure or user
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureSender"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="purposeId"></param>
            <param name="dueDate"></param>
            <param name="instruction"></param>
            <param name="structureId"></param>
            <param name="delegationId"></param>
            <param name="transferToType"></param>
            <param name="VoiceNote"></param>
            <param name="VoiceNotePrivacy"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.Close(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64})">
            <summary>
            Close transfer by id
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.View(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64})">
            <summary>
            View transfer (mark as read)
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.UnRead(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64},System.Nullable{Intalio.Core.Language})">
            <summary>
             (mark as Unread)
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.Lock(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64})">
            <summary>
            Lock transfer
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.UnLock(System.Int64,System.Int64,System.Int32,System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
            Unlock transfer
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.ListTransferHistory(System.Int32,System.Int32,System.Int64,System.Nullable{System.Int64},System.Int64,System.Int32,System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List transfer history
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.Recall(System.Int64,System.Int64,System.Nullable{System.Int64},System.String,Intalio.Core.Language,System.Boolean)">
            <summary>
            Recall transfer
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.RecallWithNewTransfer(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64},System.String,Microsoft.AspNetCore.SignalR.IHubContext{Intalio.CTS.Core.CommunicationHub},Intalio.Core.Language,System.Boolean)">
            <summary>
            Recall transfer
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.StructureUserRecall(System.Int64,System.Int64,System.Nullable{System.Int64},System.String,Intalio.Core.Language)">
            <summary>
            Recall transfer
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.Complete(System.Int64,System.Collections.Generic.List{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.String,System.Nullable{System.Int64},System.Boolean,System.Nullable{System.Boolean},System.Boolean,System.Nullable{Intalio.Core.Language},System.Collections.Generic.List{System.Nullable{System.Int64}})">
            <summary>
            Complete transfer
            </summary>
            <param name="userId"></param>
            <param name="ids"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.ListSent(System.Int32,System.Int32,System.Int64,System.Int64,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language,System.Boolean)">
            <summary>
            List in progress transfers. 
            Must be created by the user
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <param name="structureId"></param>
            <param name="fromStructure"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.GetSentCounts(System.Int64,System.Collections.Generic.List{System.Int64},System.Nullable{System.Int64},System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters,System.Boolean)">
            <summary>
            Get in progress transfers count. 
            Must be created by the user
            </summary>
            <param name="userId"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.Transfer(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Boolean,System.Int16,System.Collections.Generic.List{Intalio.CTS.Core.Model.TransferModel},System.Boolean,System.Nullable{System.Int64},System.Action{System.Collections.Generic.List{Intalio.CTS.Core.Model.TransferModel}},Intalio.Core.Language,System.Boolean)">
            <summary>
            Transfer
            </summary>
            <param name="userId"></param>
            <param name="structureId"></param>
            <param name="structureIds"></param>
            <param name="isStructureSender"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="transfers"></param>
            <param name="delegationId"></param>
            <param name="maintainTransfer"></param>
            <param name="StructureId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.DismissCarbonCopy(System.Int64,System.Collections.Generic.List{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Dismiss carbon copy(ies) by transfer(s) id(s)
            </summary>
            <param name="userId"></param>
            <param name="ids"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.ListInboxVip(System.Int32,System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language,System.Boolean)">
            <summary>
            List in progress transfers for vip mode. 
            Must be sent to the user or the user structures 
            </summary>
            <param name="startIndex"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.ListCompletedVip(System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language)">
            <summary>
            List closed transfers for vip mode.  
            Must be sent to the user or the user structures 
            </summary>
            <param name="startIndex"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.ListSentVip(System.Int32,System.Int64,System.Int64,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters,Intalio.Core.Language,System.Boolean)">
            <summary>
            List in progress transfers for vip mode.  
            Must be created by the user
            </summary>
            <param name="startIndex"></param>
            <param name="userId"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.BroadcastSend(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64},System.Nullable{System.DateTime},System.String,System.Int64,System.Nullable{System.Int64})">
            <summary>
            Broadcast Send
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureSender"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="transferId"></param>
            <param name="dueDate"></param>
            <param name="instruction"></param>
            <param name="structureId"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.BroadcastComplete(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Boolean,System.Int16,System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Nullable{System.DateTime},System.String,System.Int64,System.Nullable{System.Int64})">
            <summary>
            Broadcast Complete
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureSender"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="documentId"></param>
            <param name="ids"></param>
            <param name="dueDate"></param>
            <param name="instruction"></param>
            <param name="structureId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.GetTransferDetailsById(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Get transfer and document metadata
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.GetTransferOwnerId(System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64})">
            <summary>
            Get transfer owner id
            </summary>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.Cancel(System.Int64,System.Int64,System.Int32,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            Cancel transfer
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.SignDocument(System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Boolean,System.Int16,System.Int64,System.Nullable{System.Int64},Intalio.Core.Language,System.String)">
            <summary>
            Function to add user signature using viewer
            </summary>
            <param name="documentId"></param>
            <param name="userId"></param>
            <param name="StructureIds"></param>
            <param name="isStructureReciever"></param>
            <param name="isStructureSender"></param>
            <param name="privacyLevel"></param>
            <param name="templateId"></param>
            <param name="lang"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.RejectDocument(System.Int64,System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,Intalio.Core.Language)">
            <summary>
            Add rejected image to document
            </summary>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="userId"></param>
            <param name="StructureIds"></param>
            <param name="isStructureReciever"></param>
            <param name="privacyLevel"></param>
            <param name="lang"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.PreviewTransfersForMoving(Intalio.CTS.Core.Model.MoveTransferModel,System.Int32,System.Int32,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            Preview Transfers For Moving. 
            </summary>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.MoveTransfers(Intalio.CTS.Core.Model.MoveTransferModel,Intalio.Core.Language)">
            <summary>
            Move Transfers. 
            </summary>
            <param name="model"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.Archive(System.Int64,System.Int64,System.Int32,System.Nullable{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
            Archive document
            </summary>
            <param name="userId"></param>
            <param name="id"></param>
            <param name="roleId"></param>
            <param name="isStructureReciver"></param>
            <param name="structureId"></param>
            <param name="StructureIds"></param>
            <param name="privacyLevel"></param>
            <param name="rootFolderId"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.GetSignBookmarkCoordinates(System.Byte[])">
            <summary>
            Function to get signature position in file and return page width asnd height
            </summary>
            <param name="document"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.CheckOutFile(System.Int64,System.Int64,System.Nullable{System.Int64},System.String,System.Nullable{System.Int64},System.Boolean,System.String)">
            <summary>
            Checkout file using viewer
            </summary>
            <param name="storageAttachmentId"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="version"></param>
            <param name="delgationId"></param>
            <param name="isDraft"></param>
            <param name="usertoken"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.DiscardCheckOutFile(System.Int64,System.String,System.String)">
            <summary>
            Checkout file using viewer
            </summary>
            <param name="storageAttachmentId"></param>
            <param name="version"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.GetSignature(System.Int64,System.String)">
            <summary>
            get temmplate signatue from DS
            </summary>
            <param name="templateId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.SaveSignature(System.Byte[],System.Int32,System.String,System.Double,System.Double,System.Single,System.Single,System.Int64,System.String,System.Int64)">
            <summary>
            Add signature to document using viewer
            </summary>
            <param name="signature"></param>
            <param name="pageNumber"></param>
            <param name="language"></param>
            <param name="positionX"></param>
            <param name="positionY"></param>
            <param name="pageWidth"></param>
            <param name="pageHeight"></param>
            <param name="templateId"></param>
            <param name="version"></param>
            <param name="attachmentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.CheckInFile(System.Int64,System.Int64,System.Nullable{System.Int64},System.String,System.Nullable{System.Int64},System.Boolean,System.String)">
            <summary>
            Check in file using viewer
            </summary>
            <param name="attachmentId"></param>
            <param name="documentId"></param>
            <param name="transferId"></param>
            <param name="version"></param>
            <param name="delgationId"></param>
            <param name="isDraft"></param>
            <param name="usertoken"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.AddRejectedImage(System.Byte[])">
            <summary>
            Draw rejected Image to file
            </summary>
            <param name="file"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.TransferFollowUpToAssigneeAsync(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Boolean,System.Int16,Intalio.CTS.Core.Model.AssigneeTransferModel,Intalio.Core.Language)">
            <summary>
            TransferFollowUpToAssignee
            </summary>
            <param name="userId"></param>
            <param name="structureId"></param>
            <param name="structureIds"></param>
            <param name="isStructureSender"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="transfer"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.RequestToComplete(System.Int64,System.Collections.Generic.List{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.Language)">
            <summary>
            RequestToComplete transfer
            </summary>
            <param name="userId"></param>
            <param name="ids"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.ListFollowUps(System.Int32,System.Int32,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},Intalio.Core.ExpressionBuilderFilters,System.Collections.Generic.List{Intalio.Core.SortExpression},Intalio.Core.Language)">
            <summary>
            List followups. 
            Must be sent to the user or the user structures 
            </summary>
            <param name="followUpFilters"></param>
            <param name="startIndex"></param>
            <param name="pageSize"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="isStructureReceiver"></param>
            <param name="privacyLevel"></param>
            <param name="delegationId"></param>
            <param name="filter"></param>
            <param name="sortExpression"></param>
            <param name="language"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.SendEmailReminder(Intalio.CTS.Core.Model.EmailReminderModel,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Send email reminder for task
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageTransfer.createFollowUpFromTransfer(System.Collections.Generic.List{Intalio.CTS.Core.Model.TransferModel})">
            <summary>
            Create FollowUp and Assginee from transfer
            </summary>
            <param name="transfers"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUser.Provision(System.Collections.Generic.List{System.Int64})">
            <summary>
            Mapping users from IAM
            </summary>
            <param name="ids"></param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUser.GetUser(System.Int64)">
            <summary>
            Get user from iam
            </summary>
            <param name="id">user id</param>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUser.UpdateConfiguration(System.Int64,System.String)">
            <summary>
            Update user configuration
            </summary>
            <param name="id">User id</param>
            <param name="configuration">user configuration (json)</param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUser.GetConfiguration(System.Int64)">
            <summary>
            Get user configuration
            </summary>
            <param name="id">User id</param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUser.GetLoggedInStructure(System.Int64)">
            <summary>
            Get logged in structure
            </summary>
            <param name="id">User id</param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUser.GetUserStructureByStructureId(System.Int64,System.Int64)">
            <summary>
            Get User Structure by Strucure Id
            </summary>
            <param name="id">User id</param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUser.UpdateLoggedInStructure(System.Int64,System.Int64,System.Boolean)">
            <summary>
            Update user logged in structure
            </summary>
            <param name="id">User id</param>
            <param name="loggedInStrucureId">user logged In Strucure Id</param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUserAccess.HaveAccess(System.Int64,System.Nullable{System.Int64},System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Boolean)">
            <summary>
            TransferId has value check access on transfer and closedDate is null or
            Check on created by user or have access on any document transfer
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUserAccess.HaveAccess(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64},System.Boolean)">
            <summary>
            Check on created by user or have access on any document transfer
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUserAccess.HaveAccessAsync(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Check on created by user or have access on any document transfer
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUserAccess.HaveTransferAccess(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Check access on transfer
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUserAccess.HaveTransferAccessAsync(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Check access on transfer
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUserAccess.HaveActiveTransferAccess(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Check access on transfer and closedDate not null
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUserAccess.HaveActiveTransferAccessAsync(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Nullable{System.Int64})">
            <summary>
            Check access on transfer and closedDate not null
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUserAccess.HaveEditAccess(System.Int64,System.Nullable{System.Int64},System.Nullable{System.Int64},System.Nullable{System.Int64},System.Int64,System.Nullable{System.Int64})">
            <summary>
            Must be called after checking if the user have access to the document/transfer and the transfer is not closed
            </summary>
            <param name="createdByUserId"></param>
            <param name="createdByDelegatedUserId"></param>
            <param name="createdTransferId"></param>
            <param name="currentTransferId"></param>
            <param name="userId"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.API.ManageUserAccess.CanSend(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Collections.Generic.List{System.Int64},System.Boolean,System.Nullable{System.Int64},System.Boolean)">
            <summary>
            Check if user is structure sender and received structures contains the user structures.
            Check if enable sending rules and received structures contains the user/structure sending rules structures.
            </summary>
            <param name="structureId"></param>
            <param name="userId"></param>
            <param name="structureIds"></param>
            <param name="toStructureIds"></param>
            <param name="isStructureSender"></param>
            <param name="delegationId"></param>
            <returns></returns>
        </member>
        <member name="T:Intalio.CTS.Core.DAL.AttachmentForm">
            <summary>
            We used table splitting for performance.
            We have separated form field
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Distribution.FilterStructureInsideDistribution(System.Boolean,System.Boolean,System.Boolean,System.Collections.Generic.List{System.Int64},System.Collections.Generic.List{System.Int64})">
            <summary>
            Filter Structure inside Distribution Item based on
            Is User Structure Sender, Enabled Send to user, and Sending Rules
            </summary>
            <param name="isStructureSender"></param>
            <param name="enableSendingRules"></param>
            <param name="enableTransferToUsers"></param>
            <param name="userStructureIds"></param>
            <param name="lstStructureIdsSendingRules"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.DistributionStructure.UpdateListDistributionStructures(System.Collections.Generic.List{Intalio.CTS.Core.DAL.DistributionStructure},System.Collections.Generic.List{Intalio.CTS.Core.DAL.DistributionStructure})">
            <summary>
            
            </summary>
            <param name="distributionStructureListToCreate"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeTransfers(System.Int64)">
            <summary>
            Include Category,CreatedUser Transfer
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeAll(System.Int64)">
            <summary>
            Include: Category, DocumentForm, DocumentReceiverEntity, DocumentCarbonCopy, SendingEntity, Structure, CreatedByUser, Classification, DocumentType
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeForCopy(System.Int64)">
            <summary>
            Include: DocumentForm, DocumentReceiverEntity, DocumentCarbonCopy, LinkedDocumentDocument, Note, Attachmentnavigation
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeForExport(System.Int64)">
            <summary>
            Include: DocumentForm, DocumentReceiverEntity, DocumentCarbonCopy, LinkedDocumentDocument, Note, Attachment ,NonArchivedAttachments
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeDocumentForm(System.Int64)">
            <summary>
            Include DocumentForm, Include DocumentReceiverEntity, Include DocumentCarbonCopy
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeDocumentFormAsync(System.Int64)">
            <summary>
            Include DocumentForm, Include DocumentReceiverEntity, Include DocumentCarbonCopy
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeDocumentFormClassification(System.Int64)">
            <summary>
            Include: DocumentForm, DocumentReceiverEntity, SendingEntity, DocumentCarbonCopy, Structure, CreatedByUser, Classification
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeDocumentMetadataAsync(System.Int64)">
            <summary>
            Include: DocumentForm, DocumentReceiverEntity, DocumentCarbonCopy, Structure, Classification, Priority, Privacy, Importance
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeReceiversAndCarbonCopy(System.Int64)">
            <summary>
            Include: DocumentReceiverEntity, DocumentCarbonCopy
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeEntities(System.Int64)">
            <summary>
            Include: DocumentReceiverEntity, DocumentCarbonCopy, Structure, EntityGroup
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeReceiversAndCarbonCopyAndCategory(System.Int64)">
            <summary>
            Include: DocumentReceiverEntity, DocumentCarbonCopy
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.CheckHasLockedAttachments(System.Int64)">
            <summary>
            Without original
            </summary>
            <param name="documentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.CheckOriginalDocumentLocked(System.Int64)">
            <summary>
            Original
            </summary>
            <param name="documentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.CheckOriginalDocumentLockedByUser(System.Int64,System.Int64)">
            <summary>
            Original locked by user
            </summary>
            <param name="documentId"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.CheckHasLockedAttachmentsWithOriginal(System.Int64)">
            <summary>
            Attachments with original locked
            </summary>
            <param name="documentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.CheckHasLockedAttachmentsByUser(System.Int64,System.Int64)">
            <summary>
            Attachments with original locked by user
            </summary>
            <param name="documentId"></param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeTransfersPrioritiesAndPriorities(System.Int64)">
            <summary>
            Include Category,CreatedUser,Priority,Privacy Transfer
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Document.FindIncludeTransfersPrioritiesAndPrioritiesAsync(System.Int64)">
            <summary>
            Include Category,CreatedUser,Priority,Privacy Transfer
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Intalio.CTS.Core.DAL.DocumentForm">
            <summary>
            We used table splitting for performance.
            We have separated form field
            </summary>
        </member>
        <member name="T:Intalio.CTS.Core.DAL.FavoriteStructure">
            <summary>
            Favorite Structure Model
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.UpdateStatusAndClosedDateByList(System.Collections.Generic.List{Intalio.CTS.Core.DAL.Transfer})">
            <summary>
            Set close date and status as completed
            </summary>
            <param name="transfers"></param>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindIncludeDocument(System.Int64)">
            <summary>
            Include Document
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindIncludeDocumentAndTostructure(System.Int64)">
            <summary>
            Include Document
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindIncludeDocumentAndReceiversAndCategory(System.Int64)">
            <summary>
            Include Document and Receivers and Category
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindIncludeDocumentAndCategory(System.Int64)">
            <summary>
            Include Document and Category
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindIncludeDocumentAndPrivacy(System.Int64)">
            <summary>
            Include Document and privacy
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindIncludeDocumentAndPrivacyandSendingEntity(System.Int64)">
            <summary>
            Include Document and privacy
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindIncludeDocumentCategoryUser(System.Int64)">
            <summary>
            Include Document , Category and User
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindIncludeDocumentCategoryUserAsync(System.Int64)">
            <summary>
            Include Document , Category and User
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindIncludeDocumentData(System.Int64)">
            <summary>
            Include Document, Category, DocumentForm, DocumentReceiverEntity, SendingEntity, DocumentCarbonCopy, Classification, DocumentType
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindIncludeDocumentDataAsync(System.Int64)">
            <summary>
            Include Document, Category, DocumentForm, DocumentReceiverEntity, SendingEntity, DocumentCarbonCopy, Classification, DocumentType
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.GetTransferInfoById(System.Int64)">
            <summary>
            Include Document ,Priority,Privacy,SendingEntity,DocumentReceiverEntity Structure,CreatedByUser
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.GetTransferInfoByIdAsync(System.Int64)">
            <summary>
            Include Document ,Priority,Privacy,SendingEntity,DocumentReceiverEntity Structure,CreatedByUser
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindActiveIncludeDocument(System.Int64)">
            <summary>
            Active transfers include Document and Category
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.CheckHaveAccess(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Int64)">
            <summary>
            Check access on transfer
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.CheckHaveAccessAsync(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Int64)">
            <summary>
            Check access on transfer
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.CheckActiveHaveAccess(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Int64)">
            <summary>
            Check access on transfer and closedDate not null
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.CheckActiveHaveAccessAsync(System.Int64,System.Int64,System.Collections.Generic.List{System.Int64},System.Boolean,System.Int16,System.Int64,System.Int64)">
            <summary>
            Check access on transfer and closedDate not null
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.DAL.Transfer.FindChildWithDocument(System.Int64)">
            <summary>
            Include Document 
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Intalio.CTS.Core.Interfaces.IEventReceiver">
            <summary>
            The instance implementing the interface will be created once.
            Use UserContextAccessor.UserContext to access the user claims. 
            UserContextAccessor.UserContext is filled only from Portal web app, so if you reference directly Intalio.CTS.Core, it will be null.
            In case of exception, the error Log Level will be "EventReceivers"
            </summary>
        </member>
        <member name="M:Intalio.CTS.Core.Interfaces.IEventReceiver.OnDocumentCreated(Intalio.CTS.Core.Model.DocumentViewModel,Intalio.CTS.Core.Model.FileViewModel)">
            <summary>
            After Document creation. It will not call OnDocumentSaved
            </summary>
            <param name="model"></param>
            <param name="file"></param>
        </member>
        <member name="M:Intalio.CTS.Core.Interfaces.IEventReceiver.OnDocumentSaved(Intalio.CTS.Core.Model.DocumentViewModel)">
            <summary>
            After Saving the Document's metadata
            </summary>
            <param name="model"></param>
        </member>
        <member name="M:Intalio.CTS.Core.Interfaces.IEventReceiver.OnDocumentReferenceNumberGenerated(System.Int64)">
            <summary>
            After generating the Document reference number.
            </summary>
            <param name="documentId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.Interfaces.IEventReceiver.OnTransferLock(System.Int64,System.Nullable{System.Int64})">
            <summary>
            After Transfer lock. 
            </summary>
            <param name="transferId"></param>
            <param name="delegationId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.Interfaces.IEventReceiver.OnTransferUnLock(System.Int64,System.Nullable{System.Int64})">
            <summary>
            After Transfer Unlock.  
            </summary>
            <param name="transferId"></param>
            <param name="delegationId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.Interfaces.IEventReceiver.OnFileUploaded(Intalio.CTS.Core.Model.FileViewModel,System.Int64)">
            <summary>
            After File Uploaded.
            </summary>
            <param name="file"></param>
            <param name="documentId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.Interfaces.IEventReceiver.OnFileReplaced(Intalio.CTS.Core.Model.FileViewModel,System.Int64)">
            <summary>
            After file replaced.
            </summary>
            <param name="file"></param>
            <param name="documentId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.Interfaces.IEventReceiver.OnOriginalDocumentUploaded(Intalio.CTS.Core.Model.FileViewModel,System.Int64)">
            <summary>
            After original document uploaded.
            </summary>
            <param name="file"></param>
            <param name="documentId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.Interfaces.IEventReceiver.OnOriginalDocumentReplaced(Intalio.CTS.Core.Model.FileViewModel,System.Int64)">
            <summary>
            After original document replaced.
            </summary>
            <param name="file"></param>
            <param name="documentId"></param>
        </member>
        <member name="M:Intalio.CTS.Core.Interfaces.IEventReceiver.OnDocumentReplaceBoomark(System.Byte[],System.Int64)">
            <summary>
            After replacing bookmarks
            </summary>
            <param name="bytes"></param>
            <param name="documentId"></param>
            <returns></returns>
        </member>
        <member name="M:Intalio.CTS.Core.Interfaces.IEventReceiver.OnOriginalDocumentSigned(System.Int64)">
            <summary>
            After sign the original document.
            </summary>
            <param name="documentId"></param>
        </member>
        <member name="P:Intalio.CTS.Core.Model.AttachmentModel.Extension">
            <summary>
            Without .
            </summary>
        </member>
        <member name="P:Intalio.CTS.Core.Model.AttachmentVersionModel.Extension">
            <summary>
            Without .
            </summary>
        </member>
        <member name="P:Intalio.CTS.Core.Model.ObjectAttributeListValue.ObjectAttributeValueId">
            <summary>
            UserAttributeValue.Id
            StructureAttributeValue.Id
            </summary>
        </member>
        <member name="P:Intalio.CTS.Core.Model.SearchModel.DocumentId">
            <summary>
            has value from linked document
            </summary>
        </member>
        <member name="P:Intalio.CTS.Core.Model.StorageAttachmentModel.Extension">
            <summary>
            Without .
            </summary>
        </member>
    </members>
</doc>
