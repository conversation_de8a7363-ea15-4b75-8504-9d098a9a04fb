﻿using Intalio.Core;
using Intalio.Core.Interfaces;
using Intalio.Core.Model;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Intalio.CTS.Core.DAL;
using Intalio.Core.DAL;
using System.Reflection.PortableExecutable;

namespace Intalio.CTS.Core.DAL
{
    public partial class Delegation : IDbObject<Delegation>, IDisposable
    {
        #region Private Fields

        private CTSContext _ctx;

        #endregion

        #region Properties

        public long Id { get; set; }
        public long FromUserId { get; set; }
        public long ToUserId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string CategoryIds { get; set; }
        public long? CreatedByUserId { get; set; }
        public short PrivacyId { get; set; }
        public bool ShowOldCorespondence { get; set; }
        public bool AllowSign { get; set; }
        public bool DraftInbox { get; set; }

        public DateTime? StartDate { get; set; }
        public string Note {get; set;}

        public virtual User FromUser { get; set; }
        public virtual User ToUser { get; set; }
        public virtual Privacy Privacy { get; set; }

        #endregion

        #region Private Methods

        private void OpenDbContext()
        {
            if (_ctx == null)
            {
                _ctx = new CTSContext();
            }
        }

        #endregion

        #region Public Methods

        public void Delete(long id)
        {
            using (var ctx = new CTSContext())
            {
                Delegation item = new Delegation { Id = id };
                ctx.Delegation.Attach(item);
                ctx.Delegation.Remove(item);
                ctx.SaveChanges();
            }
        }

        public void Delete()
        {
            Delete(Id);
        }

        public void Delete(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                foreach (var id in ids)
                {
                    Delegation item = new Delegation { Id = id };
                    ctx.Delegation.Attach(item);
                    ctx.Delegation.Remove(item);
                }
                ctx.SaveChanges();
            }
        }

        public Delegation Find(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Delegation.Include(d=>d.Privacy).AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }
        public List<Delegation> FindByUserTo(long userId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Delegation.AsNoTracking()
                    .Where(t => t.ToUserId == userId &&
                                           t.ToDate.Date <= DateTime.Now.Date)?.ToList() ??
                    new List<Delegation>();
            }
        }

        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                CreatedDate = DateTime.Now;
                ctx.Delegation.Add(this);
                ctx.SaveChanges();
            }
        }

        public void Update()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        public List<Delegation> List(int startIdndex, int pageSize, long userId,
            Expression<Func<Delegation, bool>> filterExpression = null,
            List<SortExpression<Delegation>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Delegation> query = ctx.Delegation.AsNoTracking()
                    .Include(t => t.ToUser)
                    .Include(t => t.Privacy)
                    .AsNoTracking().AsNoTracking().Where(t => t.FromUserId == userId);

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return query.Skip(startIdndex).Take(pageSize).ToList();
            }
        }

        public Task<int> GetCount(long userId, Expression<Func<Delegation, bool>> filterExpression = null)
        {
            OpenDbContext();
            IQueryable<Delegation> query = _ctx.Delegation.AsNoTracking().Where(t => t.FromUserId == userId);
            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            return query.CountAsync();
        }

        public (bool, int) CheckUnique(long? id, List<int> documentTypeIds, long userId, long toUserId, DateTime fromDate, DateTime toDate)
        {
            var resultCode = 1;
            var retValue = (false, resultCode);

            using (var ctx = new CTSContext())
            {
                if (fromDate.Date > toDate.Date)
                {
                    resultCode = 2;
                    return (false, resultCode);
                }

                List<Delegation> delegations = id.HasValue
                    ? ctx.Delegation.AsNoTracking().Where(t => t.Id != id.Value && t.FromUserId == userId).ToList()
                    : ctx.Delegation.AsNoTracking().Where(t => t.FromUserId == userId).ToList();

                if (delegations.Count > 0)
                {
                    var duplicatedDelegation = delegations.FindAll(d =>
                        d.ToUserId == toUserId &&
                        ((d.FromDate.Date <= fromDate.Date || d.FromDate.Date >= toDate.Date) ||
                         (d.ToDate.Date >= fromDate.Date || d.ToDate.Date <= toDate.Date)));
                    bool duplicatedDelegationfound = false;
                    if (duplicatedDelegation != null)
                    {
                        duplicatedDelegationfound = duplicatedDelegation.Any(d =>
                        (d.FromDate.Date <= fromDate.Date && d.FromDate.Date >= toDate.Date) ||
                         (d.ToDate.Date >= fromDate.Date && d.ToDate.Date <= toDate.Date));
                    }
                    var lastDelegation = delegations.OrderByDescending(d => d.ToDate).FirstOrDefault();
                    if (lastDelegation != null && fromDate.Date > lastDelegation.ToDate.Date)
                    {
                        retValue = (true, 0); 
                    }
                    else if (!duplicatedDelegationfound)
                    {
                        retValue = (true, 0); 
                    }
                }
                else
                {
                    retValue = (true, 0); 
                }

                return retValue;
            }
        }

        public List<Delegation> List()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Delegation.AsNoTracking().Where(t => t.FromDate.Date <= DateTime.Now.Date && t.ToDate.Date >= DateTime.Now.Date).ToList();
            }
        }

        public string ListDelegationToUserIds(long userId)
        {
            using (var ctx = new CTSContext())
            {
                return string.Join(Intalio.Core.Constants.SPLITTER, ctx.Delegation.AsNoTracking().Where(t => t.ToUserId == userId && t.FromDate.Date <= DateTime.Now.Date && t.ToDate.Date >= DateTime.Now.Date).Select(t => t.FromUserId).ToList());
            }
        }
        public string ListDelegationToUserIdsWithDelegationId(long userId)
        {
            using (var ctx = new CTSContext())
            {
                return string.Join(Intalio.Core.Constants.SPLITTER, ctx.Delegation.AsNoTracking().Where(t => t.ToUserId == userId && t.FromDate.Date <= DateTime.Now.Date && t.ToDate.Date >= DateTime.Now.Date).Select(t => t.Id).ToList());
            }
        }

        public List<Delegation> ListDelegatedToUser(long userId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Delegation.AsNoTracking().Where(t => t.ToUserId == userId && t.FromDate.Date <= DateTime.Now.Date && t.ToDate.Date >= DateTime.Now.Date).ToList();
            }
        }

        public Delegation CheckUserDelegation(long userId, int documentTypeId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Delegation.Where(t => t.FromUserId == userId && t.FromDate.Date <= DateTime.Now.Date && t.ToDate.Date >= DateTime.Now.Date).ToList()
                    .Where(t => t.CategoryIds.CheckStringListContainsValue(documentTypeId.ToString())).FirstOrDefault();
            }
        }

        public Delegation FindWithDocumentTypeNames(long delegationId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Delegation.Where(t => t.Id == delegationId).FirstOrDefault();
            }
        }

        public List<Delegation> ListSystemDelegation(int startIdndex, int pageSize, Expression<Func<Delegation, bool>> filterExpression = null,
            List<SortExpression<Delegation>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Delegation> query = ctx.Delegation.AsNoTracking().
                    Include(t => t.ToUser).AsNoTracking().
                    Include(t => t.Privacy).AsNoTracking()
                    .Include(t => t.FromUser).AsNoTracking();
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return query.Skip(startIdndex).Take(pageSize).ToList();
            }
        }

        public Task<int> GetCountSystemDelegation(Expression<Func<Delegation, bool>> filterExpression = null)
        {
            OpenDbContext();
            IQueryable<Delegation> query = _ctx.Delegation.AsNoTracking();
            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            return query.CountAsync();
        }

        #endregion

        #region Dispose

        public void Dispose()
        {
            if (_ctx != null)
            {
                _ctx.Dispose();
            }
        }

        #endregion

        #region Conversion

        public static implicit operator Model.DelegationListViewModel(Delegation item)
        {
            Model.DelegationListViewModel retValue = null;
            if (item != null)
            {
                var lang = Helper.GetLanguage();
                var privacyName = lang == Language.EN ? item.Privacy?.Name :
                    (lang == Language.AR ? item.Privacy?.NameAr : item.Privacy?.NameFr);
                retValue = new Model.DelegationListViewModel
                {
                    Id = item.Id,
                    ToUser = $"{item.ToUser.Firstname} {item.ToUser.Lastname}",
                    FromDate = item.FromDate.ToString(Constants.DATE_FORMAT),
                    ToDate = item.ToDate.ToString(Constants.DATE_FORMAT),
                    CreatedDate = item.CreatedDate.ToString(Constants.DATE_FORMAT),
                    ModifiedDate = item.ModifiedDate != default ? item.ModifiedDate?.ToString(Constants.DATE_FORMAT) : string.Empty,
                    ToUserValueText = new ValueText { Id = Convert.ToInt32(item.ToUser.Id), Text = $"{item.ToUser.Firstname} {item.ToUser.Lastname}" },
                    CategoryIds = !string.IsNullOrEmpty(item.CategoryIds) ? item.CategoryIds.Split(Constants.SPLITTER).Select(t => Convert.ToInt32(t)).ToList() : new List<int>(),
                    PrivacyName = String.IsNullOrEmpty(privacyName) ? item.Privacy.Name : privacyName,
                    PrivacyId = item.PrivacyId,
                    ShowOldCorrespondecne = item.ShowOldCorespondence,
                    Note = item.Note,
                    AllowSign = item.AllowSign,
                    StartDate = item.StartDate != null ? item.StartDate.Value.ToString(Constants.DATE_FORMAT):null,
                    DraftInbox = item.DraftInbox,

                };
            }
            return retValue;
        }

        public static implicit operator Model.SystemDelegationListViewModel(Delegation item)
        {
            Model.SystemDelegationListViewModel retValue = null;
            if (item != null)
            {
                var lang = Helper.GetLanguage();
                var privacyName = lang == Language.EN ? item.Privacy?.Name :
                    (lang == Language.AR ? item.Privacy?.NameAr : item.Privacy?.NameFr);
                retValue = new Model.SystemDelegationListViewModel
                {
                    Id = item.Id,
                    FromUser = $"{item.FromUser.Firstname} {item.FromUser.Lastname}",
                    ToUser = $"{item.ToUser.Firstname} {item.ToUser.Lastname}",
                    FromDate = item.FromDate.ToString(Constants.DATE_FORMAT),
                    ToDate = item.ToDate.ToString(Constants.DATE_FORMAT),
                    CreatedDate = item.CreatedDate.ToString(Constants.DATE_FORMAT),
                    ModifiedDate = item.ModifiedDate != default ? item.ModifiedDate?.ToString(Constants.DATE_FORMAT) : string.Empty,
                    ToUserValueText = new ValueText { Id = Convert.ToInt32(item.ToUser.Id), Text = $"{item.ToUser.Firstname} {item.ToUser.Lastname}" },
                    FromUserValueText = new ValueText { Id = Convert.ToInt32(item.FromUser.Id), Text = $"{item.FromUser.Firstname} {item.FromUser.Lastname}" },
                    CategoryIds = !string.IsNullOrEmpty(item.CategoryIds) ? item.CategoryIds.Split(Constants.SPLITTER).Select(t => Convert.ToInt32(t)).ToList() : new List<int>(),
                    PrivacyName = String.IsNullOrEmpty(privacyName) ? item.Privacy.Name : privacyName,
                    PrivacyId = item.PrivacyId,
                    ShowOldCorespondence = item.ShowOldCorespondence,
                    AllowSign = item.AllowSign,
                    Note = item.Note,
                    StartDate = item.StartDate != null ? item.StartDate.Value.ToString(Constants.DATE_FORMAT) : null,
                    DraftInbox = item.DraftInbox,
                    
                };
            }
            return retValue;
        }

        public static implicit operator Model.DelegationModel(Delegation item)
        {
            Model.DelegationModel retValue = null;
            if (item != null)
            {
                retValue = new Model.DelegationModel
                {
                    Id = item.Id,
                    FromUserId = item.FromUserId,
                    ToUserId = item.ToUserId,
                    FromDate = item.FromDate,
                    ToDate = item.ToDate,
                    DelegatedPrivacyLevel = item.Privacy.Level,
                    PrivacyLevel = item.Privacy.Level,
                    ShowOldCorespondence = item.ShowOldCorespondence,
                    AllowSign = item.AllowSign,
                    Note=item.Note,
                    StartDate= item.StartDate,
                    CategoryIds = !string.IsNullOrEmpty(item.CategoryIds) ? item.CategoryIds.Split(Constants.SPLITTER).Select(t => Convert.ToInt32(t)).ToList() : new List<int>(),
                    DraftInbox = item.DraftInbox,
                    
                };
            }
            return retValue;
        }

        public static implicit operator Model.DelegationMenuModel(Delegation item)
        {
            CTS.Core.Model.DelegationMenuModel retValue = null;
            if (item != null)
            {
                var fromUser = new Intalio.Core.DAL.User().Find(item.FromUserId);
                retValue = new Model.DelegationMenuModel
                {
                    Id = item.Id,
                    FromUser = fromUser != null ? $"{fromUser.Firstname} {fromUser.Lastname}" : string.Empty,
                    FromUserRoleId = fromUser != null ? fromUser.RoleId : 0,
                    FromUserId = item.FromUserId,
                    DraftInbox = item.DraftInbox
                   
                };
            }
            return retValue;
        }

        #endregion
    }
}
