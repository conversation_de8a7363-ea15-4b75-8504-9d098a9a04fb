﻿window.Paging = 15;
var gCategoryObject = {};


$.ajaxSetup({
    headers: { 'Authorization': 'Bearer ' + window.IdentityAccessToken },
    beforeSend: function (jqXHR, settings) {
        if (settings.url == "/SecurityMatrix/List")
            settings.url = "/CTS/CTSSecurityMatrix/List"
    }
});

if (typeof Common !== 'object')
{
    var Common = (function (E)
    {
        E = {};
        return E;
    }(Common));
}
const targetNode = $('body')[0]; // Get the body element
const config = { attributes: true, attributeFilter: ['class'] }; // Watch for class changes
const callback = function (mutationsList) {
    mutationsList.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            //console.log('Body class changed:', $('body').attr('class')); // Log the new class list
            if ($('.modal.fade.in').length > 0 && !($('body').attr('class').includes('modal-open'))) {
                $('body').addClass('modal-open');
            }
        }
    });
};
const observer = new MutationObserver(callback);
observer.observe(targetNode, config);
var GridCommon = (function (E) {
    E = {};
    E.GridsObject = {};
    E.AddCheckBoxEvents = function (gridname, skipClear) {
        if (!skipClear) {
            GridCommon.Clear(gridname);
            if (window.AllowRowSelection == "True") {
                $('#' + gridname + ' tbody').on('click', 'tr', function (e) {
                    if ($(e.target).is("button") || $(e.target).parent().is("button")) {
                        return;
                    }
                    var input = this.getElementsByTagName('input')[0];
                    if (typeof input !== "undefined") {

                        input.checked = input.checked ? false : true;
                        var id = input.getAttribute("data-id");
                        if (input.checked) {
                            $(e.target).closest('tr')[0].style.backgroundColor = '#eee';
                            GridCommon.Add(gridname, id);
                        } else {
                            if ($(e.target).closest('tr').hasClass('even'))
                                $(e.target).closest('tr')[0].style.backgroundColor = '#ffffff';
                            else
                                $(e.target).closest('tr')[0].style.backgroundColor = '#fafbfc';
                            GridCommon.Remove(gridname, id);
                        }
                    }
                });
            }
                    $('#' + gridname + ' #chkAll').change(function () {
                var checked = $(this).prop('checked');
                $('#' + gridname + ' tbody tr td input[type="checkbox"]').prop('checked', checked);
                $('#' + gridname + ' tbody tr td input[type="checkbox"]').each(function (e) {
                    var id = this.getAttribute("data-id");
                    if (checked) {
                        this.closest('tr').style.backgroundColor = '#eee'
                        GridCommon.Add(gridname, id);
                    } else {
                        if (this.closest('tr').getAttribute("class") == "even")
                            this.closest('tr').style.backgroundColor = '#ffffff';
                        else
                            this.closest('tr').style.backgroundColor = '#fafbfc';
                        GridCommon.Remove(gridname, id);
                    }
                });
            });
                    $('#' + gridname + ' tbody').on('change', 'tr td input[type="checkbox"]', function (e) {
                var checked = $(this).prop('checked');
                var id = this.getAttribute("data-id");
                if (checked) {
                    $(e.target).closest('tr')[0].style.backgroundColor = '#eee';
                    GridCommon.Add(gridname, id);
                } else {
                    if ($(e.target).closest('tr').hasClass('even'))
                        $(e.target).closest('tr')[0].style.backgroundColor = '#ffffff';
                    else
                        $(e.target).closest('tr')[0].style.backgroundColor = '#fafbfc';
                    GridCommon.Remove(gridname, id);
                }
            });
                }
    };
    E.Remove = function (gridname, id) {
        var selectedRows = GridCommon.GetSelectedRows(gridname);
        const index = selectedRows.indexOf(id);
        if (index > -1) {
            selectedRows.splice(index, 1);
            GridCommon.SetSelectedRows(gridname, selectedRows);
        }
    };
    E.Add = function (gridname, id) {
        var selectedRows = GridCommon.GetSelectedRows(gridname);
        const index = selectedRows.indexOf(id);
        if (index === -1) {
            selectedRows.push(id);
            GridCommon.SetSelectedRows(gridname, selectedRows);
        }
    };
    E.SetSelectedRows = function (gridname, selectedRows) {
        GridCommon.GridsObject[gridname] = selectedRows;
    };
    E.GetSelectedRows = function (gridname) {
        if (!GridCommon.GridsObject[gridname]) {
            GridCommon.GridsObject[gridname] = [];
        }
        return GridCommon.GridsObject[gridname];
    };
    E.CheckSelectedRows = function (gridname) {
        var selectedRows = GridCommon.GetSelectedRows(gridname);
        $('#' + gridname + ' tbody').find('input[type="checkbox"]').each(function (index, obj) {
            var id = obj.getAttribute('data-id');
            if (selectedRows.includes(id)) {
                obj.setAttribute("checked", "checked");
            }
        });
    };
    E.Clear = function (gridname) {
        GridCommon.GridsObject[gridname] = [];
    };
    E.Refresh = function (gridname) {
        $("#" + gridname).DataTable().ajax.reload();
        GridCommon.Clear(gridname);
    };
    E.RefreshCurrentPage = function (gridname, triggerBackBtn) {
        $("#" + gridname).DataTable().ajax.reload(null, false);
        GridCommon.Refresh(gridname);
        if (triggerBackBtn) {
            $('.btn-back').click();
        }
    };
    E.Destroy = function () {
        GridCommon.GridsObject = {}
    };
    return E;
}(GridCommon));
Common.showToast = function (message, duration = 3000, container) {

    var toaster = document.createElement('div');
    toaster.textContent = message;
    toaster.style.padding = '3px';
    toaster.style.backgroundColor = '#ff902b';
    toaster.style.color = '#fff';
    toaster.style.borderRadius = '5px';
    toaster.style.marginTop = '10px';
    toaster.style.opacity = '1';
    toaster.style.transition = 'opacity 0.5s ease';
    toaster.style.width = 'fit-content';
    toaster.style.position = 'absolute'
    container.appendChild(toaster);

    setTimeout(function () {
        toaster.style.opacity = '0';
        setTimeout(function () {
            toaster.remove();
        }, 500);
    }, duration);
}
Common.alertConfirmation = function (text, callbackResult)
{
    swal({
        title: "",
        text: text,
        showCancelButton: false,
        confirmButtonClass: "btn-info btn",
        confirmButtonText: Resources.OK,
        closeOnConfirm: true,
        closeOnCancel: true,
        customClass: 'swal-wide'
    },
        function (result)
        {
            if (result)
            {
                callbackResult(true);
            } else
            {
                callbackResult(false);
            }
        });
}
Common.ajaxPostWithHeaders = function (url, params, successHandler, errorHandler, showMask, id, headers, async) //id of the mask location
{
    var newasync = async;
    if (typeof async === "undefined" || async === null)
    {
        newasync = true;
    }
    if (showMask)
    {
        if (id !== undefined && id !== null)
        {
            var element = document.getElementById(id);
            Common.mask(element, id + "-mask");
        }
        else
        {
            Common.mask(document.body, "body-mask");
        }
    }
    $.ajax({
        url: url,
        type: 'POST',
        data: params,
        headers: typeof headers !== "undefined" ? headers : "",
        async: newasync,
    }).done(function (data, textStatus, request)
    {
        if (request.getResponseHeader('LoginPage') !== null)
        {
            location.reload(true);
        }
        if (typeof successHandler === "function") { successHandler(data); }
    }).fail(function (jqXHR, textStatus, errorThrown)
    {
        if (typeof errorHandler === "function") { errorHandler(errorThrown, jqXHR); }
    })
        .always(function ()
        {
            if (showMask)
            {
                if (id !== undefined && id !== null)
                {
                    Common.unmask(id + "-mask");
                }
                else
                {
                    Common.unmask("body-mask");
                }
            }
        });
};
Common.buildModalWithIframe = function (div, src, title, iframeHeight)
{
    var modalDiv = document.createElement('div');
    modalDiv.setAttribute("id", "modal");
    modalDiv.setAttribute("tabindex", "-1");
    modalDiv.setAttribute("role", "dialog");
    modalDiv.setAttribute("aria-hidden", "true");
    modalDiv.setAttribute("class", "modal fade");
    modalDiv.setAttribute("data-backdrop", false);
    modalDiv.setAttribute("data-keyboard", false);

    var modalDialogDiv = document.createElement('div');
    modalDialogDiv.setAttribute("class", "modal-dialog modal-xl");

    var modalContentDiv = document.createElement('div');
    modalContentDiv.setAttribute("class", "modal-content");

    var modalHeaderDiv = document.createElement('div');
    modalHeaderDiv.setAttribute("class", "modal-header");

    var modalHeaderButton = document.createElement('button');
    modalHeaderButton.setAttribute("type", "button");
    modalHeaderButton.setAttribute("class", "close");
    modalHeaderButton.setAttribute("data-dismiss", "modal");
    modalHeaderButton.innerHTML = "&times;";

    var modalHeaderTitle = document.createElement('h4');
    modalHeaderTitle.setAttribute("class", "modal-title");
    modalHeaderTitle.innerHTML = title;

    var modalBodyDiv = document.createElement('div');
    modalBodyDiv.setAttribute("class", "modal-body");
    modalBodyDiv.setAttribute("style", "padding:0px");
    if (iframeHeight == undefined)
    {
        if ($(div)[0].children.length > 0)
            iframeHeight = $(div)[0].children[0].clientHeight + 'px';
    }
    var modalBodyIframe = document.createElement('iframe');
    modalBodyIframe.setAttribute("style", "width:100%;height:" + iframeHeight + ";border:0;");
    modalBodyIframe.src = src;

    modalBodyDiv.appendChild(modalBodyIframe);

    modalHeaderDiv.appendChild(modalHeaderButton);
    if (title)
    {
        modalHeaderDiv.appendChild(modalHeaderTitle);
    }

    modalContentDiv.appendChild(modalHeaderDiv);
    modalContentDiv.appendChild(modalBodyDiv);

    modalDialogDiv.appendChild(modalContentDiv);
    modalDiv.appendChild(modalDialogDiv);

    div.appendChild(modalDiv);
    $('#modal').modal('show');
    $("#modal").off("hidden.bs.modal");
    $("#modal").off("shown.bs.modal");
    $('#modal').on('hidden.bs.modal', function ()
    {
        $('#modal').remove();
        if ($('.modal:visible').length)
        {
            $('body').addClass('modal-open');
        }
    });
}
Common.drawTrackingInfo = function (data)
{
    var isRoot = data["IsRoot"];
    var titleText = '';
    var receiving = data["ReceivingEntity"] !== " " ? data["ReceivingEntity"].replace(/\//g, " - ") : null;
    var sending = data["SendingEntity"] !== " " ? data["SendingEntity"] : null;
    var subject = data["Subject"] !== " " ? data["Subject"] : null;
    var priority = data["Priority"] !== " " ? data["Priority"] : null;
    var privacy = data["Privacy"] !== " " ? data["Privacy"] : null;
    var owner = data["CreatedByFullName"] !== " " ? data["CreatedByFullName"] : null;
    var duedate = data["DueDate"] !== " " ? data["DueDate"] : "----";
    var openedDate = data["OpenedDate"] !== " " ? data["OpenedDate"] : "----";
    var instruction = data["Instruction"] !== " " ? data["Instruction"] : "----";
    var componentId = data["ComponentId"];

    if (isRoot === true)
    {
        var createdDate = data["CreatedDate"] !== " " ? data["CreatedDate"] : "----";
        titleText += Resources.Document;
    } else
    {
        var createdDate = data["TransferDate"] !== " " ? data["TransferDate"] : "----";
        titleText += Resources.Transfer;
    }
    var mainDiv = window.document.createElement('div');
    mainDiv.setAttribute('id', componentId + '_visualTrackingDiv');
    var mask = '<div class="modal-backdrop fade in modal-stack" style="z-index: 9998;"></div>';
    var div = window.document.createElement('div');
    div.setAttribute('id', componentId + '_infoDiv');
    div.setAttribute('class', 'modal-vt');
    div.setAttribute('style', 'border: 1px solid #999;z-index: 9999;position: absolute;background: white;border-radius: 5px;box-shadow: 10px 10px 25px rgb(0 0 0 / 30%);');
    div.setAttribute('data-backdrop', 'static');
    div.setAttribute('data-keyboard', 'false');
    div.setAttribute('role', 'dialog');
    div.setAttribute('aria-hidden', 'true');
    var header = window.document.createElement('div');
    header.setAttribute('class', 'modal-header');
    header.setAttribute('style', 'background: white;border-radius: 10px 10px 0px 0px;');
    var title = window.document.createElement('h4');
    title.setAttribute('class', 'modal-title');
    title.setAttribute('id', componentId + '_title');
    var body = window.document.createElement('div');
    body.setAttribute('class', 'modal-body');
    body.setAttribute('style', 'z-index: 9999;padding: 0px;background: white;border-radius: 10px;height: 300px!important;');

    var vtModal = window.document.createElement('div');
    vtModal.setAttribute('id', componentId + '_modalVisualTrackingInfoIndex');
    vtModal.setAttribute('style', 'padding-top: 25px;');

    var documentDiv = '<div id= "' + componentId + '_document"><div class="col-sm-12 col-xs-12 row" id= "' + componentId + '_senderContainer" style="display:flex;">' +
        '<div class="form-group col-md-4" style="margin:7px 0px; width:40%;"><label class="control-label" style="color: #32bea3;font-size:15px;">' + Resources.SendingEntity + ':</label>' +
        '</div><div class="form-group col-md-8" style="margin:7px 0px; width:60%;"><div id= "' + componentId + '_sendingEntity">' + sending + '</div></div></div>' +
        '<div class="col-sm-12 col-xs-12 row" id= "' + componentId + '_receiverContainer" style="display:flex;"><div class="form-group col-md-4" style="margin:7px 0px; width:40%;">' +
        '<label class="control-label" style="color: #32bea3;font-size:15px;">' + Resources.ReceivingEntity + ':</label>' +
        '</div><div class="form-group col-md-8" style="margin:7px 0px; width:60%;"><div id= "' + componentId + '_receivingEntity">' + receiving + '</div></div></div>' +
        '<div class="col-sm-12 col-xs-12 row" id= "' + componentId + '_subjectContainer" style="display:flex;"> <div class="form-group col-md-4" style="margin:7px 0px; width:40%;">' +
        '<label class="control-label" style="color: #32bea3;font-size:15px;">' + Resources.Subject + ':</label>' +
        '</div><div class="form-group col-md-8" style="margin:7px 0px; width:60%;"><div id= "' + componentId + '_subject">' + subject + '</div></div></div>' +
        '<div class="col-sm-12 col-xs-12 row" id= "' + componentId + '_priorityContainer" style="display:flex;"><div class="form-group col-md-4" style="margin:7px 0px; width:40%;">' +
        '<label class="control-label" style="color: #32bea3;font-size:15px;">' + Resources.Priority + ':</label>' +
        '</div><div class="form-group col-md-8" style="margin:7px 0px; width:60%;"><div id= "' + componentId + '_priority">' + priority + '</div></div></div>' +
        '<div class="col-sm-12 col-xs-12 row" id= "' + componentId + '_privacyContainer" style="display:flex;"><div class="form-group col-md-4" style="margin:7px 0px; width:40%;">' +
        '<label class="control-label" style="color: #32bea3;font-size:15px;">' + Resources.Privacy + ':</label>' +
        '</div><div class="form-group col-md-8" style="margin:7px 0px; width:60%;"><div id= "' + componentId + '_privacy">' + privacy + '</div></div></div></div>';

    var transferDiv = '<div id= "' + componentId + '_transfer"><div class="col-sm-12 col-xs-12 row" id= "' + componentId + '_ownerContainer" style="display:flex;">' +
        '<div class="form-group col-md-4" style="margin:7px 0px; width:40%;"><label class="control-label" style="color: #32bea3;font-size:15px;">' + Resources.CreatedBy + ':</label>' +
        '</div><div class="form-group col-md-8" style="margin:7px 0px; width:60%;"><div id= "' + componentId + '_owner">' + owner + '</div></div></div>' +
        '<div class="col-sm-12 col-xs-12 row" id= "' + componentId + '_duedateContainer" style="display:flex;"><div class="form-group col-md-4" style="margin:7px 0px; width:40%;">' +
        '<label class="control-label" style="color: #32bea3;font-size:15px;">' + Resources.DueDate + ':</label>' +
        '</div><div class="form-group col-md-8" style="margin:7px 0px; width:60%;"><div id= "' + componentId + '_duedate">' + duedate + '</div></div></div>' +
        '<div class="col-sm-12 col-xs-12 row" id= "' + componentId + '_openedDateContainer" style="display:flex;"> <div class="form-group col-md-4" style="margin:7px 0px; width:40%;">' +
        '<label class="control-label" style="color: #32bea3;font-size:15px;">' + Resources.OpenedDate + ':</label>' +
        '</div><div class="form-group col-md-8" style="margin:7px 0px; width:60%;"><div id= "' + componentId + '_openedDate">' + openedDate + '</div></div></div>' +
        '<div class="col-sm-12 col-xs-12 row" id= "' + componentId + '_createdDateContainer" style="display:flex;"><div class="form-group col-md-4" style="margin:7px 0px; width:40%;">' +
        '<label class="control-label" style="color: #32bea3;font-size:15px;">' + Resources.CreatedDate + ':</label>' +
        '</div><div class="form-group col-md-8" style="margin:7px 0px; width:60%;"><div id= "' + componentId + '_createdDate">' + createdDate + '</div></div></div>' +
        '<div class="col-sm-12 col-xs-12 row" id= "' + componentId + '_instructionContainer" style="display:flex;"><div class="form-group col-md-4" style="margin:7px 0px; width:40%;">' +
        '<label class="control-label" style="color: #32bea3;font-size:15px;">' + Resources.Instruction + ':</label>' +
        '</div><div class="form-group col-md-8" style="margin:7px 0px; width:60%;"><div id= "' + componentId + '_instruction" title="' + instruction + '">' + instruction + '</div></div></div></div > ';
    var closeBtn = '<button type="button" class="close" onclick="removeComponent(\'' + componentId + '\')">&times;</button>';
    header.innerHTML = closeBtn;
    header.appendChild(title);
    if (isRoot)
    {
        vtModal.innerHTML = documentDiv;
    } else
    {
        vtModal.innerHTML = transferDiv;
    }
    body.appendChild(vtModal);
    body.style.background = "white";
    div.appendChild(header);
    div.appendChild(body);
    div.style.position = "absolute";
    div.style.left = "430px";
    div.style.top = Math.round($(document).scrollTop()) + "px";
    div.style.background = "white";
    mainDiv.innerHTML = mask;
    mainDiv.appendChild(div);
    window.document.getElementsByTagName('body')[0].appendChild(mainDiv);
    $("#" + componentId + "_title").text(titleText);
}
Common.showConfirmCcedMsg = function (msg, callback, secondCallback, additionalMsg, closeOnConfirm)
{
    if (typeof additionalMsg !== "undefined")
    {
        msg += '<br/>' + additionalMsg;
    }
    swal({
        title: "",
        text: msg,
        allowOutsideClick: true,
        allowEscapeKey: true,
        showCancelButton: true,
        confirmButtonClass: "btn-info btn",
        closeOnConfirm: typeof closeOnConfirm !== "undefined" ? closeOnConfirm : true,
        closeOnCancel: true,
        confirmButtonText: Resources.TransferAndClose,
        cancelButtonText: Resources.Close,
        html: typeof additionalMsg !== "undefined" ? true : false
    },
        function (isConfirm)
        {
            if (isConfirm)
            {
                secondCallback();
            }
            else
            {
                //if (event && event.target && event.screenX !== 0 && event.target.type === "submit")
                //{
                //    if (typeof secondCallback === 'function')
                //    {
                //        secondCallback();
                //    }
                //} else
                //{
                    if (typeof cancelCallback === 'function')
                    {
                        cancelCallback();
                    }
                //}

            }
        });
};
Common.isHijriCalendar = function ()
{
    return window.CalendarType && window.CalendarType !== window.CalendarTypes.None;
}

Common.tryCloseDocumentModal = function (modalComponentId) {
    if (modalComponentId == null || $("div[ref='" + modalComponentId + "']").length <= 0)
        return;

    $("#" + modalComponentId + "_btnClose").trigger("click");
}

var TreeNode = (function (E)
{
    E = {};
    E.DelegationIds = function ()
    {
        var delegationIds = [];
        $('.delegation').each(function (i, obj)
        {
            delegationIds.push($(this).data("id"));
        });
        return delegationIds;
    };
    E.Draft = "Draft";
    E.Inbox = "Inbox";
    E.StructureInbox = "StructureInbox";
    E.StructureSent = "StructureSent";
    E.Completed = "Completed";
    E.MyRequests = "MyRequests";
    E.Sent = "Sent";
    E.Closed = "Closed";
    E.FollowUp = "FollowUp";
    E.Custom = "Custom";
    E.setSingleNodeCount = function (name, total, today, unread)
    {
        $("#countTotal" + name).text(total);
        $("#countToday" + name).text(today);
        $("#countUnread" + name).text(unread);
    };
    E.setNodeCount = function (name, total, today, unread)
    {
        var ids = [];
        $('li[data-inherit="' + name + '"]').each(function (i, obj)
        {
            ids.push($(this).data("id"));
        });
        for (var i = 0; i < ids.length; i++)
        {
            var id = ids[i];
            $("#countTotal" + name + id).text(total);
            $("#countToday" + name + id).text(today);
            $("#countUnread" + name + id).text(unread);
        }
    };
    E.addToNodeCount = function (name, total, today, unread)
    {
        var ids = [];
        $('li[data-inherit="' + name + '"]').each(function (i, obj)
        {
            ids.push($(this).data("id"));
        });
        for (var i = 0; i < ids.length; i++)
        {
            var id = ids[i];
            oldCount = Number($("#countTotal" + name + id).text());
            oldTodayCount = Number($("#countToday" + name + id).text());
            oldUnreadCount = Number($("#countUnread" + name + id).text());
            var newTotal = oldCount + total;
            var newToday = oldTodayCount + today;
            var newUnread = oldUnreadCount + unread;
            $("#countTotal" + name + id).text(newTotal);
            $("#countToday" + name + id).text(newToday);
            $("#countUnread" + name + id).text(newUnread);
        }
    };
    E.refreshTreeNodeCounts = function (name)
    {
        var delegationIds = TreeNode.DelegationIds();
        var id = 0;
        $("#grdInboxItems").DataTable().ajax.reload();
        switch (name)
        {
            case E.Draft:
                var ids = [];
                $('li[data-inherit="' + name + '"]').each(function (i, obj)
                {
                    ids.push($(this).data("id"));
                });
                for (var i = 0; i < ids.length; i++)
                {
                    TreeNode.buildDraftNodeCounts(ids[i]);
                }
                for (var i = 0; i < delegationIds.length; i++) {
                    id = delegationIds[i];
                    TreeNode.buildDelegationNodeCounts('/Document/GetDraftCounts', name, id);
                }
                break;
            case E.Inbox:
                var ids = [];
                $('li[data-inherit="' + name + '"]').each(function (i, obj)
                {
                    ids.push($(this).data("id"));
                });
                for (var i = 0; i < ids.length; i++)
                {
                    TreeNode.buildInboxNodeCounts(ids[i]);
                }
                for (var i = 0; i < delegationIds.length; i++)
                {
                    id = delegationIds[i];
                    TreeNode.buildDelegationNodeCounts('/Transfer/GetInboxCounts', name, id);
                }
                break;
            case E.StructureInbox:
                var ids = [];
                $('li[data-inherit="' + name + '"]').each(function (i, obj)
                {
                    ids.push($(this).data("id"));
                });
                for (var i = 0; i < ids.length; i++)
                {
                    TreeNode.buildStructureInboxNodeCounts(ids[i]);
                }
                for (var i = 0; i < delegationIds.length; i++)
                {
                    id = delegationIds[i];
                    TreeNode.buildDelegationNodeCounts('/Transfer/GetInboxCounts', name, id,true);
                }
                break;
            case E.Completed:
                var ids = [];
                $('li[data-inherit="' + name + '"]').each(function (i, obj)
                {
                    ids.push($(this).data("id"));
                });
                for (var i = 0; i < ids.length; i++)
                {
                    TreeNode.buildCompletedNodeCounts(ids[i]);
                }
                for (var j = 0; j < delegationIds.length; j++)
                {
                    id = delegationIds[j];
                    TreeNode.buildDelegationNodeCounts('/Transfer/GetCompletedCounts', name, id);
                }
                break;
            case E.MyRequests:
                var ids = [];
                $('li[data-inherit="' + name + '"]').each(function (i, obj)
                {
                    ids.push($(this).data("id"));
                });
                for (var i = 0; i < ids.length; i++)
                {
                    TreeNode.buildMyRequestsNodeCounts(ids[i]);
                }
                break;
            case E.Sent:
                var ids = [];
                $('li[data-inherit="' + name + '"]').each(function (i, obj)
                {
                    ids.push($(this).data("id"));
                });
                for (var i = 0; i < ids.length; i++)
                {
                    TreeNode.buildSentNodeCounts(ids[i]);
                }
                for (var i = 0; i < delegationIds.length; i++)
                {
                    id = delegationIds[i];
                    TreeNode.buildDelegationNodeCounts('/Transfer/GetSentCounts', name, id);
                }
                break;
            case E.StructureSent:
                var ids = [];
                $('li[data-inherit="' + name + '"]').each(function (i, obj)
                {
                    ids.push($(this).data("id"));
                });
                for (var i = 0; i < ids.length; i++)
                {
                    TreeNode.buildStructureSentNodeCounts(ids[i]);
                }
                for (var i = 0; i < delegationIds.length; i++)
                {
                    id = delegationIds[i];
                    TreeNode.buildDelegationNodeCounts('/Transfer/GetSentCounts', name, id,true);
                }
                break;
            case E.Closed:
                var ids = [];
                $('li[data-inherit="' + name + '"]').each(function (i, obj)
                {
                    ids.push($(this).data("id"));
                });
                for (var i = 0; i < ids.length; i++)
                {
                    TreeNode.buildClosedNodeCounts(ids[i]);
                }
                break;
            case E.FollowUp:
                var ids = [];
                $('li[data-inherit="' + name + '"]').each(function (i, obj) {
                    ids.push($(this).data("id"));
                });
                for (var i = 0; i < ids.length; i++) {
                    TreeNode.buildFollowUpNodeCounts(ids[i]);
                }
                //for (var i = 0; i < delegationIds.length; i++) {
                //    id = delegationIds[i];
                //    TreeNode.buildDelegationNodeCounts('/Transfer/GetSentCounts', name, id, true);
                //}
                break;
            case E.Custom:
                $("#grdRejectedDocumentsItems").DataTable().ajax.reload();
                $("#grdExportedDocumentsItems").DataTable().ajax.reload();
                $('li[data-inherit="' + name + '"]').each(function (i, obj) {
                    var id = $(this).data("id");
                    TreeNode.buildCustomNodeCounts(id, $(this).data("enabletotalcount"), $(this).data("enabletodaycount"), $(this).data("enableunreadcount"), $(this).data("customfunctions"));
                });
                break;
        }
    };
    E.buildDraftNodeCounts = function (nodeId)
    {
        var name = "Draft";
        Common.ajaxGet('/Document/GetDraftCounts', { nodeId: nodeId, loggedInStructureId: $('#hdLoggedInStructureId').val()}, function (data)
        {
            TreeNode.setSingleNodeCount(name + nodeId, data.total, data.today);
        });
        var delegationIds = TreeNode.DelegationIds();
        for (var i = 0; i < delegationIds.length; i++) {
            var id = delegationIds[i];
            TreeNode.buildDelegationNodeCounts('/Document/GetDraftCounts', name + id, id);
        }
    };
    E.buildInboxNodeCounts = function (nodeId)
    {
        var name = "Inbox";
        Common.ajaxGet('/Transfer/GetInboxCounts', { nodeId: nodeId, loggedInStructureId: $('#hdLoggedInStructureId').val() }, function (data)
        {
            TreeNode.setSingleNodeCount(name + nodeId, data.total, data.today, data.unread);
        });
    };
    E.buildStructureInboxNodeCounts = function (nodeId)
    {
        var name = "StructureInbox";
        Common.ajaxGet('/Transfer/GetInboxCounts', { nodeId: nodeId, loggedInStructureId: $('#hdLoggedInStructureId').val(), fromStructure:true }, function (data)
        {
            TreeNode.setSingleNodeCount(name + nodeId, data.total, data.today, data.unread);
        });
    };
    E.buildCompletedNodeCounts = function (nodeId)
    {
        var name = "Completed";
        Common.ajaxGet('/Transfer/GetCompletedCounts', { nodeId: nodeId, loggedInStructureId: $('#hdLoggedInStructureId').val() }, function (data)
        {
            TreeNode.setSingleNodeCount(name + nodeId, data.total, data.today);
        });

        var delegationIds = TreeNode.DelegationIds();
        for (var i = 0; i < delegationIds.length; i++)
        {
            var id = delegationIds[i];
            TreeNode.buildDelegationNodeCounts('/Transfer/GetCompletedCounts', name + id, id);
        }
    };
    E.buildMyRequestsNodeCounts = function (nodeId)
    {
        var name = "MyRequests";
        Common.ajaxGet('/Document/GetMyRequestsCounts', { nodeId: nodeId, loggedInStructureId: $('#hdLoggedInStructureId').val() }, function (data)
        {
            TreeNode.setSingleNodeCount(name + nodeId, data.total, data.today);
        });
    };
    E.buildSentNodeCounts = function (nodeId)
    {
        var name = "Sent";
        Common.ajaxGet('/Transfer/GetSentCounts', { nodeId: nodeId, loggedInStructureId: $('#hdLoggedInStructureId').val() }, function (data)
        {
            TreeNode.setSingleNodeCount(name + nodeId, data.total, data.today);
        });

        var delegationIds = TreeNode.DelegationIds();
        for (var i = 0; i < delegationIds.length; i++)
        {
            var id = delegationIds[i];
            TreeNode.buildDelegationNodeCounts('/Transfer/GetSentCounts', name + id, id);
        }
    };
    E.buildStructureSentNodeCounts = function (nodeId) {
        var name = "StructureSent";
        Common.ajaxGet('/Transfer/GetSentCounts', { nodeId: nodeId, loggedInStructureId: $('#hdLoggedInStructureId').val(), fromStructure: true }, function (data) {
            TreeNode.setSingleNodeCount(name + nodeId, data.total, data.today);
        });

        var delegationIds = TreeNode.DelegationIds();
        for (var i = 0; i < delegationIds.length; i++) {
            var id = delegationIds[i];
            TreeNode.buildDelegationNodeCounts('/Transfer/GetSentCounts', name + id, id,true);
        }
    };
    E.buildClosedNodeCounts = function (nodeId)
    {
        var name = "Closed";
        Common.ajaxGet('/Document/GetClosedCounts', { nodeId: nodeId, loggedInStructureId: $('#hdLoggedInStructureId').val() }, function (data)
        {
            TreeNode.setSingleNodeCount(name + nodeId, data.total, data.today);
        });
    };
    E.buildCustomNodeCounts = async function (nodeId, enableTotalCount, enableTodayCount, enableUnreadCount, customFunctions) {
        var name = "Custom";
        var total = "0";
        var today = "0";
        var unread = "0";
        if (enableTotalCount === "True") {
            if (customFunctions) {
                var functiontotal = customFunctions.split(window.Splitter)[1];
                if (functiontotal && typeof window[functiontotal.replace("(", "").replace(")", "")] === 'function') {
                    functiontotal = functiontotal
                        .replace("(", "('" + name + nodeId).replace(")", "')");
                    total = await eval(functiontotal);
                }
                else if (typeof window[getFunctionName(functiontotal)] === 'function') {
                    functiontotal = functiontotal
                        .replace("(", "('" + name + nodeId).replace(")", "')")
                    total = await eval(functiontotal);
                }
            }
        }
        if (enableTodayCount === "True") {
            if (customFunctions) {
                var functiontoday = customFunctions.split(window.Splitter)[2];
                if (functiontoday && typeof window[functiontoday.replace("(", "").replace(")", "")] === 'function') {
                    functiontoday = functiontoday
                        .replace("(", "('" + name + nodeId).replace(")", "')")
                    today = await eval(functiontoday);
                    today = await eval(functiontoday);
                }
                else if (typeof window[getFunctionName(functiontoday)] === 'function') {
                    functiontoday = functiontoday
                        .replace("(", "('" + name + nodeId).replace(")", "')")
                    today = await eval(functiontoday);
                }
            }
        } if (enableUnreadCount === "True") {
            if (customFunctions) {
                var functionunread = customFunctions.split(window.Splitter)[3];
                if (functionunread && typeof window[functionunread.replace("(", "").replace(")", "")] === 'function') {
                    functionunread = functionunread
                        .replace("(", "('" + name + nodeId).replace(")", "')")
                    unread = await eval(functionunread);
                }
            }
        }
        TreeNode.setSingleNodeCount(name + nodeId, total, today, unread);
    };
    E.buildDelegationNodeCounts = function (url, name, delegationId, fromStructure=false)
    {
        var ids = [];
        $('.delegation li[data-inherit="' + name + '"]').each(function (i, obj)
        {
            ids.push($(this).data("id"));
        });
        for (var j = 0; j < ids.length; j++)
        {
            var id = ids[j];
            E.GetDelegationNodeCounts(url, name, id, delegationId, fromStructure);
        }
    };
    E.GetDelegationNodeCounts = function (url, name, nodeId, delegationId, fromStructure)
    {
        if (fromStructure) {
            Common.ajaxGet(url, { nodeId: nodeId, delegationId: delegationId, loggedInStructureId: $('#hdLoggedInStructureId').val(), fromStructure: fromStructure }, function (data) {
                TreeNode.setSingleNodeCount(name + nodeId + "-" + delegationId, data.total, data.today);
            });
        } else {

            Common.ajaxGet(url, { nodeId: nodeId, delegationId: delegationId, loggedInStructureId: $('#hdLoggedInStructureId').val() }, function (data) {
                TreeNode.setSingleNodeCount(name + nodeId + "-" + delegationId, data.total, data.today);
            });
        }
    }
    E.buildFollowUpNodeCounts = function (nodeId) {
        var name = "FollowUp";
        Common.ajaxGet('/FollowUp/GetFollowUpCounts', { nodeId: nodeId, loggedInStructureId: Number($('#hdLoggedInStructureId').val()) }, function (data) {
            TreeNode.setSingleNodeCount(name + nodeId, data.total, data.today);
        });
    };

    return E;
}(TreeNode));
var gTasksList = [];
$(window).on('load', function () {
    setTimeout(function () {
        $('li[data-inherit]').each(function (i, obj) {
            var id = $(this).data("id");
            var inherit = $(this).data("inherit");
            switch (inherit) {
                case NodeInherit.Draft:
                    TreeNode.buildDraftNodeCounts(id);
                    break;
                case NodeInherit.Inbox:
                    TreeNode.buildInboxNodeCounts(id);
                    break;
                case NodeInherit.StructureInbox:
                    TreeNode.buildStructureInboxNodeCounts(id);
                    break;
                case NodeInherit.Completed:
                    TreeNode.buildCompletedNodeCounts(id);
                    break;
                case NodeInherit.MyRequests:
                    TreeNode.buildMyRequestsNodeCounts(id);
                    break;
                case NodeInherit.Sent:
                    TreeNode.buildSentNodeCounts(id);
                    break;
                case NodeInherit.StructureSent:
                    TreeNode.buildStructureSentNodeCounts(id);
                    break;
                case NodeInherit.Closed:
                    TreeNode.buildClosedNodeCounts(id);
                    break;
                case NodeInherit.FollowUp:
                    TreeNode.buildFollowUpNodeCounts(id);
                    break;
                case NodeInherit.Custom:
                    TreeNode.buildCustomNodeCounts(id, $(this).data("enabletotalcount"), $(this).data("enabletodaycount"), $(this).data("enableunreadcount"), $(this).data("customfunctions"));
                    break;
            }
        });
        var delegationIds = TreeNode.DelegationIds();
        for (var i = 0; i < delegationIds.length; i++) {
            var id = delegationIds[i];
            TreeNode.buildDelegationNodeCounts('/Transfer/GetInboxCounts', NodeInherit.Inbox, id);
            TreeNode.buildDelegationNodeCounts('/Transfer/GetCompletedCounts', NodeInherit.Completed, id);
            TreeNode.buildDelegationNodeCounts('/Transfer/GetSentCounts', NodeInherit.Sent, id);
            TreeNode.buildDelegationNodeCounts('/Document/GetDraftCounts', NodeInherit.Draft, id);

        }


        new CoreComponents.Lookup.SecurityMatrix().get(window.language);
    }, 500);
});

$(document).ready(function ()
{
    getAllCommittee();
    getAllMeetingLocation();

    if (sessionStorage.getItem("ListCustomAttributeTranslation") == null) {
        GetCustomAttributeTranslation();
    }

    Formio.icons = 'fontawesome';

    sessionStorage.setItem($("#hdUserId").val() + "loggedInStructure", $('#hdLoggedInStructureId').val());
    $("#" + sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure")).addClass('active');
    localStorage.setItem('LoggedInRole', $('#hdLoggedInRoleId').val());
    

    //$('li[data-inherit]').each(function (i, obj)
    //{
    //    var id = $(this).data("id");
    //    var inherit = $(this).data("inherit");
    //    switch (inherit)
    //    {
    //        case NodeInherit.Draft:
    //            TreeNode.buildDraftNodeCounts(id);
    //            break;
    //        case NodeInherit.Inbox:
    //            TreeNode.buildInboxNodeCounts(id);
    //            break;
    //        case NodeInherit.StructureInbox:
    //            TreeNode.buildStructureInboxNodeCounts(id);
    //            break;
    //        case NodeInherit.Completed:
    //            TreeNode.buildCompletedNodeCounts(id);
    //            break;
    //        case NodeInherit.MyRequests:
    //            TreeNode.buildMyRequestsNodeCounts(id);
    //            break;
    //        case NodeInherit.Sent:
    //            TreeNode.buildSentNodeCounts(id);
    //            break;
    //        case NodeInherit.StructureSent:
    //            TreeNode.buildStructureSentNodeCounts(id);
    //            break;
    //        case NodeInherit.Closed:
    //            TreeNode.buildClosedNodeCounts(id);
    //            break;
    //        case NodeInherit.FollowUp:
    //            TreeNode.buildFollowUpNodeCounts(id);
    //            break;
    //        case NodeInherit.Custom:
    //            TreeNode.buildCustomNodeCounts(id, $(this).data("enabletotalcount"), $(this).data("enabletodaycount"), $(this).data("enableunreadcount"), $(this).data("customfunctions"));
    //            break;
    //    }
    //});
    //var delegationIds = TreeNode.DelegationIds();
    //for (var i = 0; i < delegationIds.length; i++)
    //{
    //    var id = delegationIds[i];
    //    TreeNode.buildDelegationNodeCounts('/Transfer/GetInboxCounts', NodeInherit.Inbox, id);
    //    TreeNode.buildDelegationNodeCounts('/Transfer/GetCompletedCounts', NodeInherit.Completed, id);
    //    TreeNode.buildDelegationNodeCounts('/Transfer/GetSentCounts', NodeInherit.Sent, id);
    //    TreeNode.buildDelegationNodeCounts('/Document/GetDraftCounts', NodeInherit.Draft, id);

    //}

    configureNotification();


    $(".hamburger").click(function ()
    {
        $(this).toggleClass("is-active");
        $('.navbar-collapse .nav.navbar-nav').toggleClass('open');
    });
    $(".to_close").click(function ()
    {
        $('.hamburger').toggleClass("is-active");
        $('.navbar-collapse .nav.navbar-nav').toggleClass('open');
    });
    $('.sidebarTrigger').click(function ()
    {
        if ($('.hamburger').hasClass('is-active'))
        {
            $('.hamburger').toggleClass("is-active");
            $('.navbar-collapse .nav.navbar-nav').toggleClass('open');
        }
    });
    if ($("#liMyDocuments").length > 0)
    {
        $.ajax({
            url: window.IdentityUrl + '/api/HasApplicationAccess',
            data: { id: $("#hdUserId").val(), name: window.DMSApplicationName },
            type: 'GET',
            async: false,
            headers: {
                Authorization: 'Bearer ' + window.IdentityAccessToken
            }
        }).done(function (data)
        {
            if (!data)
            {
                $("#liMyDocuments").remove();
            }
        });
    }

    flatpickr.setDefaults({
        typeCalendar: window.CalendarType,
        altInput: true,
        allowInput: true
    });
    if (window.language === 'ar')
    {
        flatpickr.localize(flatpickr.l10ns.ar);
    } else if (window.language === 'fr')
    {
        flatpickr.localize(flatpickr.l10ns.fr);
    } else
    {
        flatpickr.localize(flatpickr.l10ns.en);
    }
    flatpickr(".flatpickr-input");
    $(document).on('show.bs.modal', '.modal', function ()
    {
        var zIndex = 1050 + 10 * $('.modal:visible').length;
        $(this).css('z-index', zIndex);
        setTimeout(function ()
        {
            $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
        }, 0);
    });
    $.fn.modal.Constructor.prototype.enforceFocus = function ()
    {
        modal_this = this
        $(document).on('focusin.modal', function (e)
        {
            if (modal_this.$element[0] !== e.target && !modal_this.$element.has(e.target).length
                && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_select')
                && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_text'))
            {
                modal_this.$element.focus()
            }
        })
    };

    $("#categoriesList").find("li").find("[href*=\\/"+window.MeetingAgendaId+"]").hide();
    $("#categoriesList").find("li").find("[href*=\\/"+window.MeetingResolutionId+"]").hide();
    $("#categoriesList").find("li").find("[href*=\\/" + window.MeetingMinutesId + "]").hide();

    $("body").on("click", ".copyToClipboard", function () {
        //var message = $(this).text();
        //var message = $(this).text().trim(); 
        var message = $(this).children().first().text().trim();
        navigator.clipboard.writeText(message);

        Common.showToast(Resources.CopiedToClipboard, 500, $(this)[0]);
    });

    $(document).on("click", "i.maximize-modal", function () {
        var component = $(this).data("component");
        $("div[ref='" + component + "'] div[ref='modalLinkedCorrespondenceDocument']").length > 0 ? $("div[ref='" + component + "'] div[ref='modalLinkedCorrespondenceDocument']").modal("show") : $("div[ref='" + component + "'] div[ref='modalFollowUpOriginalDocument']").modal("show");
    });
});
function changeLanguage(lang)
{
    let locationUrl = window.location.href;
    $.ajax({
        type: 'POST',
        url: '/Home/ChangeLanguage?lang=' + lang,
        success: function (data)
        {
            window.location.href = locationUrl;
            window.location.reload(true);
        }
    });
}
function ISODate(d) { return d; }

function configureNotification()
{
    var connection = new signalR.HubConnectionBuilder().configureLogging(signalR.LogLevel.Error).withUrl("/notificationhub").build();
    connection.on("ReceiveMessage", function (object)
    {
        if (window.Notification)
        {
            let ctsNotification;
            let options = {
                body: object.message,
                data: object.data,
                icon: '../images/email-32.png',
                direction: window.language === 'ar' ? 'rtl' : 'ltr',
                badge: '../images/nextandprevious.png',
                requireInteraction: true

            };

            if (Notification.permission === 'granted')
            {
                ctsNotification = new Notification(window.ApplicationName, options);
                let audio = new Audio('/Sound/notification.mp3');
                audio.play();
            } else
            {
                Notification.requestPermission().then(function (p)
                {
                    if (p === 'granted')
                    {
                        ctsNotification = new Notification(window.ApplicationName, options);
                        var audio = new Audio('/Sound/notification.mp3');
                        audio.play();
                    }
                }).catch(function (err)
                {
                    console.error(err);
                });
            }

            ctsNotification.onclick = function (event) {
                if (event && event.currentTarget && event.currentTarget.data &&
                    event.currentTarget.data.transferId && event.currentTarget.data.nodeId) {
                }
                window.location.href = "#inbox/" + event.currentTarget.data.nodeId + "/transfer/" + event.currentTarget.data.transferId;
                const interval = setInterval(() => {
                    const liElement = document.querySelector(`li[data-transfer="${event.currentTarget.data.transferId}"]`);
                    if (liElement) {
                        liElement.click();
                        clearInterval(interval);
                    }
                }, 300); 
            };

            setTimeout(() => {
                ctsNotification.close();
            }, 10000);
        }
        else
        {
            Common.showScreenWarningMsg(message);
            let audio = new Audio('/Sound/notification.mp3');
            audio.play();
        }
        TreeNode.refreshTreeNodeCounts("Inbox");
    });
    connection.start().then(function ()
    {
        console.log("Connected");
    }).catch(function (err)
    {
        return console.error(err.toString());
    });
}
var TreeNodes = (function (E)
{
    E = {};
    E.Draft = "1";
    E.Inbox = "2";
    E.Completed = "3";
    E.MyRequests = "4";
    E.Search = "5";
    E.Sent = "6";
    return E;
}(TreeNodes));
var EventReceiver = (function (E)
{
    E = {};
    E.OnDocumentSaved = function (object)
    {
        return new Promise(function (resolve)
        {
            resolve();
        });
    };
    E.OnDocumentRegistered = function (object)
    {
        return new Promise(function (resolve)
        {
            resolve();
        });
    };
    E.MyTransferAfterRender = function (object) {
        return new Promise(function (resolve) {
            resolve();
        });
    };
    E.AttributesAfterRender = function (object) {
        return new Promise(function (resolve) {
            resolve();
        });
    };
    E.AttachmentsAfterRender = function (object) {
        return new Promise(function (resolve) {
            resolve();
        });
    };
    E.NotesAfterRender = function (object) {
        return new Promise(function (resolve) {
            resolve();
        });
    };
    E.LinkedCorrespondencesAfterRender = function (object) {
        return new Promise(function (resolve) {
            resolve();
        });
    };
    E.NonArchivedAttachmentsAfterRender = function (object) {
        return new Promise(function (resolve) {
            resolve();
        });
    };
    E.VisualTrackingAfterRender = function (object) {
        return new Promise(function (resolve) {
            resolve();
        });
    };
    E.ActivityLogAfterRender = function (object) {
        return new Promise(function (resolve) {
            resolve();
        });
    };
    E.TransfersHistoryAfterRender = function (object) {
        return new Promise(function (resolve) {
            resolve();
        });
    };

    E.AfterTransferAddRow = function (self, element) {
        return new Promise(function (resolve) {
            resolve();
        });
    };

    E.AfterTransferDeleteRow = function (self, rowIndex) {
        return new Promise(function (resolve) {
            resolve();
        });
    };

    return E;
}(EventReceiver));
var CustomizationFileType = (function (E)
{
    E = {};
    E.Javascript = "1";
    E.CSS = "2";
    E.EventReceiver = "3";
    E.Barcode = "4";
    return E;
}(CustomizationFileType));
var AuditTrailMode = (function (E)
{
    E = {};
    E.None = "None";
    E.Basic = "Basic";
    E.Full = "Full";
    return E;
}(AuditTrailMode));
var AuditTrailAction = (function (E)
{
    E = {};
    E.PrintReport = "28";
    E.ExportReportToExcel = "29";
    E.ExportReportToPdf = "30";
    return E;
}(AuditTrailAction));
var TransferType = (function (E)
{
    E = {};
    E.Send = 1;
    E.ReplyToUser = 2;
    E.ReplyToStructure = 3;
    E.BroadcastSend = 4;
    E.BroadcastComplete = 5;
    E.SignAndSend = 6;
    E.ReplyToInitiator = 7;
    return E;
}(TransferType));
var NodeInherit = (function (E)
{
    E = {};
    E.Draft = "Draft";
    E.Inbox = "Inbox";
    E.Completed = "Completed";
    E.MyRequests = "MyRequests";
    E.Sent = "Sent";
    E.Closed = "Closed";
    E.Custom = "Custom";
    E.StructureInbox = "StructureInbox";
    E.StructureSent = "StructureSent";
    E.FollowUp = "FollowUp";
    return E;
}(NodeInherit));
var SystemStatus = (function (E)
{
    E = {};
    E.Draft = 1;
    E.InProgress = 2;
    E.Completed = 3;
    return E;
}(SystemStatus));
var CorrespondenceMode = (function (E)
{
    E = {};
    E.Default = "OpenCorrespondenceDefault";
    E.WithViewer = "OpenCorrespondenceWithViewer";
    return E;
}(CorrespondenceMode));
var Type = (function (E)
{
    E = {};
    E.Internal = 1;
    E.External = 2;
    return E;
}(Type));

var FollowUpRoles = (function (E) {
    E = {};
    E.Owner = 1;
    E.Editor = 2;
    E.Reader = 3;
    return E;
}(FollowUpRoles));

var FollowUpStatuses = (function (E) {
    E = {};
    E.InProgress = 1;
    E.Overdued = 2;
    E.Postponed = 3;
    E.Completed = 4;
    E.Canceled = 5;
    return E;
}(FollowUpStatuses));
function removeComponent(componentId)
{
    $("#" + componentId + "_visualTrackingDiv").remove();
}
SecurityMatrix.initContextMenuVip = function (securityMatrix, node)
{
    $.contextMenu('destroy', '.mdl-li');
    $.contextMenu({
        selector: '.mdl-li',
        trigger: isMobile.any() ? 'left' : 'right',
        build: function ($trigger, e)
        {
            let input = $trigger.find('input');
            var categoryId = input.data(SecurityMatrix.CategoryIdPropertyName.toLowerCase());
            if (input && typeof categoryId !== "undefined")
            {
                var options = { items: {} };
                SecurityMatrix.getContextActionsVip(securityMatrix, options, node, categoryId);
                return !jQuery.isEmptyObject(options.items) ? options : false;
            }
            return false;
        },
        rtl: true
    });
};
SecurityMatrix.InitContextMenu = function (securityMatrix, node) {
    $.contextMenu('destroy', '.custom-tab-action');
    $.contextMenu('destroy', '.dataTable td');
    $.contextMenu('destroy', '.row-action');
    $.contextMenu({
        selector: '.dataTable td',
        trigger: isMobile.any() ? 'left' : 'right',
        build: function ($trigger, e) {
            if (!$trigger.hasClass("hasChildren")) {
                let tr = $trigger.closest('tr');
                let table = $trigger.parents("table").attr("id");
                let srow = $('#' + table).DataTable().row(tr);
                var data = srow.data();
                if (data && typeof data[SecurityMatrix.CategoryIdPropertyName] !== "undefined") {
                    var options = { items: {} };
                    SecurityMatrix.getContextActions(securityMatrix, options, node, data[SecurityMatrix.CategoryIdPropertyName],data);
                    return !jQuery.isEmptyObject(options.items) ? options : false;
                }
            }
            return false;
        },
        rtl: true
    });
    $.contextMenu({
        selector: '.row-action',
        trigger: 'left',
        build: function ($trigger, e) {
            let id = $trigger.data("id");
            let tr = $trigger.closest('tr');
            let table = $trigger.parents("table").attr("id");
            let srow = $('#' + table).DataTable().row(tr);
            var data = srow.data();
            if (id) {
                var options = { items: {} };
                SecurityMatrix.getRowSubActions(securityMatrix, options, node, data[SecurityMatrix.CategoryIdPropertyName], id);
                return !jQuery.isEmptyObject(options.items) ? options : false;
            }
            return false;
        }
    });
};
SecurityMatrix.initToolbarMenuVip = function (securityMatrix, node, divId)
{
    var actions = $.grep(securityMatrix.SecurityNodes[Number(node)].Actions, function (element, index)
    {
        return element.Type === Number(TypeAction.Toolbar);
    });
    if (actions.length === 0)
    {
        $(".gridActions").remove();
    } else
    {
        $.contextMenu('destroy', '.gridActions');
        $.contextMenu({
            selector: '.gridActions',
            trigger: 'left',
            build: function ($trigger, e)
            {
                var options = { items: {} };
                SecurityMatrix.getToolbarActionsVip(securityMatrix, options, node, divId);
                return !jQuery.isEmptyObject(options.items) ? options : false;
            },
            rtl: true
        });
    }
};
SecurityMatrix.getContextActionsVip = function (securityMatrix, options, node, categoryId)
{
    var actions = $.grep(securityMatrix.SecurityNodes[Number(node)].SecurityCategories[Number(categoryId)].Actions,
        function (element)
        {
            return element.Type === Number(TypeAction.Context);
        });
    for (var i = 0; i < actions.length; i++)
    {
        var action = actions[i];
        if (action.ParentActionId === null)
        {
            let tooltipAttr = action.Tooltip !== null ? action.Tooltip : '';
            options.items[action.Id] = {
                name: "<span class='customConext' data-function='" + action.JsFunction + "' title='" + tooltipAttr + "'>" + action.Name + "</span>",
                isHtmlName: true,
                order: action.Order,
                icon: action.Icon !== "" && action.Icon !== null ? action.Icon.replace("fa ", "") : "fa-ellipsis-v",
                function: action.JsFunction,
                callback: function (key, options, e)
                {
                    var jsfunction = $(e.target).data("function") ? $(e.target).data("function") : $(e.target).find(".customConext").data("function");
                    var data = $(this).find("input[type='hidden']").val();
                    if (jsfunction)
                    {
                        jsfunction = jsfunction.split("(")[0];
                        eval(jsfunction + "(" + data + ")");
                    }
                }
            };
            getContextSubActionsVip(actions, options.items, action.Id);
        }
    }
    var optionSortedArray = Object.keys(options.items).sort(function (a, b) { return options.items[a].order - options.items[b].order }).map(function (key) { return options.items[key] });
    options.items = {};
    for (var i = 0; i < optionSortedArray.length; i++)
    {
        options.items[i] = optionSortedArray[i];
    }
    return options;
};
SecurityMatrix.getContextActions = function (securityMatrix, options, node, categoryId,data) {
    var actions = $.grep(securityMatrix.SecurityNodes[Number(node)].SecurityCategories[Number(categoryId)].Actions,
        function (element) {
            return element.Type === Number(TypeAction.Context);
        });
  
    for (var i = 0; i < actions.length; i++) {
      
        var action = actions[i];
        if (data.cced === false && action.Name && action.Name.toLowerCase().includes("dismiss")) {
            continue; 
        }
        if (action.ParentActionId === null) {
            let tooltipAttr = action.Tooltip !== null ? action.Tooltip : '';
            options.items[action.Id] = {
                name: "<span class='customConext' data-function='" + action.JsFunction + "' title='" + tooltipAttr + "'>" + action.Name + "</span>",
                isHtmlName: true,
                order: action.Order,
                icon: action.Icon !== "" && action.Icon !== null ? action.Icon.replace("fa ", "") : "fa-ellipsis-v",
                function: action.JsFunction,
                callback: function (key, options, e) {
                    var jsfunction = $(e.target).data("function") ? $(e.target).data("function") : $(e.target).find(".customConext").data("function");
                    let tr = $(this).closest('tr');
                    let table = $(this).parents("table").attr("id");
                    let srow = $('#' + table).DataTable().row(tr);
                    var data = srow.data();
                    if (jsfunction) {
                        jsfunction = jsfunction.split("(")[0];
                        eval(jsfunction + "(" + JSON.stringify(data) + ")");
                    }
                }
            };
            getContextSubActions(actions, options.items, action.Id);
        }
    }
    optionSortedArray = Object.keys(options.items).sort(function (a, b) { return options.items[a].order - options.items[b].order }).map(function (key) { return options.items[key] });
    options.items = {};
    for (var i = 0; i < optionSortedArray.length; i++) {
        options.items[i] = optionSortedArray[i];
    }
    return options;
};
SecurityMatrix.getToolbarActionsVip = function (securityMatrix, options, node, divId)
{
    var actions = $.grep(securityMatrix.SecurityNodes[Number(node)].Actions, function (element, index)
    {
        return element.Type === Number(TypeAction.Toolbar);
    });
    for (var i = 0; i < actions.length; i++)
    {
        var action = actions[i];
        if (action.ParentActionId === null)
        {
            let tooltipAttr = action.Tooltip !== null ? action.Tooltip : '';
            options.items[action.Id] = {
                name: "<span class='customToolbar' data-function='" + action.JsFunction + "' title='" + tooltipAttr + "'>" + action.Name + "</span>",
                isHtmlName: true,
                order: action.Order,
                icon: action.Icon !== "" && action.Icon !== null ? action.Icon.replace("fa ", "") : "fa-ellipsis-v",
                function: action.JsFunction,
                callback: function (key, options, e)
                {
                    var checkedRows = $('#' + divId + ' li').find('input[type="checkbox"]:checked');
                    if (checkedRows.length > 0)
                    {
                        var ids = new Array();
                        checkedRows.each(function (index, obj)
                        {
                            ids.push(obj.getAttribute('data-id'));
                        });
                        var jsfunction = $(e.target).add($(e.target).find('.customToolbar')).filter('.customToolbar').data("function");
                        if (jsfunction)
                        {
                            jsfunction = jsfunction.split("(")[0];
                            eval(jsfunction + "(" + JSON.stringify(ids) + ")");
                        }
                    }
                    else
                    {
                        Common.alertMsg(Resources.NoRowSelected);
                    }
                }
            };
            getToolbarSubActionsVip(actions, options.items, action.Id, divId);
        }
    }
    var optionSortedArray = Object.keys(options.items).sort(function (a, b) { return options.items[a].order - options.items[b].order }).map(function (key) { return options.items[key] });
    options.items = {};
    for (var i = 0; i < optionSortedArray.length; i++)
    {
        options.items[i] = optionSortedArray[i];
    }
    return options;
};
function getContextSubActionsVip(actions, items, id)
{
    var subactions = $.grep(actions, function (element)
    {
        return element.ParentActionId === id;
    });
    if (subactions.length > 0)
    {
        items[id].items = {};
        for (var j = 0; j < subactions.length; j++)
        {
            var subaction = subactions[j];
            let tooltipAttr = subaction.Tooltip !== null ? subaction.Tooltip : '';
            items[id].items[subaction.Id] = {
                name: "<span class='customConext' data-function='" + subaction.JsFunction + "' title='" + tooltipAttr + "'>" + subaction.Name + "</span>",
                isHtmlName: true,
                order: subaction.Order,
                icon: subaction.Icon !== "" && subaction.Icon !== null ? subaction.Icon.replace("fa ", "") : "",
                function: subaction.JsFunction,
                callback: function (key, options, e)
                {
                    var jsfunction = $(e.target).data("function") ? $(e.target).data("function") : $(e.target).find(".customConext").data("function");
                    var data = $(this).find("input[type='hidden']").val();
                    if (jsfunction)
                    {
                        jsfunction = jsfunction.split("(")[0];
                        eval(jsfunction + "(" + data + ")");
                    }
                }
            };
            getContextSubActionsVip(actions, items[id].items, subaction.Id);
            var optionSortedArray = Object.keys(items[id].items).sort(function (a, b) { return items[id].items[a].order - items[id].items[b].order }).map(function (key) { return items[id].items[key] });
            items[id].items = {};
            for (var i = 0; i < optionSortedArray.length; i++)
            {
                items[id].items[i] = optionSortedArray[i];
            }
        }
    }
}
function getContextSubActions(actions, items, id) {
    var subactions = $.grep(actions, function (element) {
        return element.ParentActionId === id;
    });
    if (subactions.length > 0) {
        items[id].items = {};
        for (var j = 0; j < subactions.length; j++) {
            var subaction = subactions[j];
            let tooltipAttr = subaction.Tooltip !== null ? subaction.Tooltip : '';
            items[id].items[subaction.Id] = {
                name: "<span class='customConext' data-function='" + subaction.JsFunction + "' title='" + tooltipAttr + "'>" + subaction.Name + "</span>",
                isHtmlName: true,
                order: subaction.Order,
                icon: subaction.Icon !== "" && subaction.Icon !== null ? subaction.Icon.replace("fa ", "") : "",
                function: subaction.JsFunction,
                callback: function (key, options, e) {
                    var jsfunction = $(e.target).data("function") ? $(e.target).data("function") : $(e.target).find(".customConext").data("function");
                    let tr = $(this).closest('tr');
                    let table = $(this).parents("table").attr("id");
                    let srow = $('#' + table).DataTable().row(tr);
                    var data = srow.data();
                    if (jsfunction) {
                        jsfunction = jsfunction.split("(")[0];
                        eval(jsfunction + "(" + JSON.stringify(data) + ")");
                    }
                }
            };
            getContextSubActions(actions, items[id].items, subaction.Id);
            optionSortedArray = Object.keys(items[id].items).sort(function (a, b) { return items[id].items[a].order - items[id].items[b].order }).map(function (key) { return items[id].items[key] });
            items[id].items = {};
            for (var i = 0; i < optionSortedArray.length; i++) {
                items[id].items[i] = optionSortedArray[i];
            }
        }
    }
}

function getToolbarSubActionsVip(actions, items, id, divId)
{
    var subactions = $.grep(actions, function (element)
    {
        return element.ParentActionId === id;
    });
    if (subactions.length > 0)
    {
        items[id].items = {};
        for (var j = 0; j < subactions.length; j++)
        {
            var subaction = subactions[j];
            let tooltipAttr = subaction.Tooltip !== null ? subaction.Tooltip : '';
            items[id].items[subaction.Id] = {
                name: "<span class='customToolbar' data-function='" + subaction.JsFunction + "' title='" + tooltipAttr + "'>" + subaction.Name + "</span>",
                isHtmlName: true,
                order: subaction.Order,
                icon: subaction.Icon !== "" && subaction.Icon !== null ? subaction.Icon.replace("fa ", "") : "",
                function: subaction.JsFunction,
                callback: function (key, options, e)
                {

                    var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
                    if (checkedRows.length > 0)
                    {
                        var ids = new Array();
                        checkedRows.each(function (index, obj)
                        {
                            ids.push(obj.getAttribute('data-id'));
                        });
                        var jsfunction = $(e.target).closest(".customToolbar").data("function");
                        if (jsfunction)
                        {
                            jsfunction = jsfunction.split("(")[0];
                            eval(jsfunction + "(" + JSON.stringify(ids) + ")");
                        }
                    }
                    else
                    {
                        Common.alertMsg(Resources.NoRowSelected);
                    }
                }
            };
            getToolbarSubActionsVip(actions, items[id].items, subaction.Id, divId);
            var optionSortedArray = Object.keys(items[id].items).sort(function (a, b) { return items[id].items[a].order - items[id].items[b].order }).map(function (key) { return items[id].items[key] });
            items[id].items = {};
            for (var i = 0; i < optionSortedArray.length; i++)
            {
                items[id].items[i] = optionSortedArray[i];
            }
        }
    }
}

function getFullNameByLangauge(userObject) {

    if (typeof userObject != 'undefined' && userObject.attributes != null && userObject.attributes.length > 0) {
        var userFirstName = "";
        var userLastName = "";
        var userMiddleName = "";
        var attrs = userObject.attributes;
        for (var i = 0; i < attrs.length; i++) {
            var attr = attrs[i];
            var attVal = attr.value;
            if (attVal == null)
                attVal = "";
            if (language == 'ar') {
                if (attr.text === window.FirstNameAr)
                    userFirstName = attVal;
                else if (attr.text === window.LastNameAr)
                    userLastName = attVal;
                else if (attr.text === window.MiddleNameAr)
                    userMiddleName = attVal;
            } else if (language=='fr') {
                if (attr.text === window.FirstNameFr)
                    userFirstName = attVal;
                else if (attr.text === window.LastNameFr)
                    userLastName = attVal;
                else if (attr.text === window.MiddleNameFr)
                    userMiddleName = attVal;
            }
        }
        return (userFirstName + " " + userLastName).trim();
        //return userFirstName + " " + userMiddleName + " " + userLastName;
    }
    return "";
}


function getFullNameInAllLangauge(userObject) {
    var fullNameInAllLang = [];
    if (typeof userObject != 'undefined' && userObject.attributes != null && userObject.attributes.length > 0) {
        fullNameInAllLang.push(userObject.fullName)

        var userFirstNameAr = "";
        var userLastNameAr = "";
        var userMiddleNameAr = "";
        var userFirstNameFr = "";
        var userLastNameFr = "";
        var userMiddleNameFr = "";
        var attrs = userObject.attributes;
        for (var i = 0; i < attrs.length; i++) {
            var attr = attrs[i];
            var attVal = attr.value;
            if (attVal == null)
                attVal = "";
            
                if (attr.text === window.FirstNameAr)
                    userFirstNameAr = attVal;
                else if (attr.text === window.LastNameAr)
                    userLastNameAr = attVal;
                else if (attr.text === window.MiddleNameAr)
                    userMiddleNameAr = attVal;

                if (attr.text === window.FirstNameFr)
                    userFirstNameFr = attVal;
                else if (attr.text === window.LastNameFr)
                    userLastNameFr = attVal;
                else if (attr.text === window.MiddleNameFr)
                    userMiddleNameFr = attVal;
        }
        var fullNameAr = (userFirstNameAr + " " + userLastNameAr).trim();
        var fullNameFr = (userFirstNameFr + " " + userLastNameFr).trim();
        if (fullNameAr == "")
            userObject.fullName
        if (fullNameFr == "")
            userObject.fullName
        fullNameInAllLang.push(fullNameFr)
        fullNameInAllLang.push(fullNameAr)

        return fullNameInAllLang;
        
    }
    return [];
}
function getFullUserObj(id)
{
           var retValue = { "id": id, "data": "" };

        var data = new CoreComponents.Lookup.User().getFullUser(id, window.language);
        if (data) {
            retValue = { "id": id, "data": data };
        }
    
    return retValue.data;
}

function GetCustomAttributeTranslation() {
    Common.ajaxGet('/Category/ListCustomAttributeTranslation', null, function (data) {
        var CustomAttributeTranslations = [];
        $.each(data, function (index, value) {
            $.each(JSON.parse(value), function (index, object) {
                CustomAttributeTranslations.push(object);
            });
        });
        sessionStorage.setItem("ListCustomAttributeTranslation", JSON.stringify(CustomAttributeTranslations));
    });
}

function GetCustomAttributeTranslationByLangauge(keyword) {
    var data = JSON.parse(sessionStorage.getItem("ListCustomAttributeTranslation")).find(f => f.Keyword == keyword);

    if (data === undefined)
        return keyword;

    var retval;
    if (window.language === 'ar') {
        retval = data.Ar;
    } else if (window.language === 'fr') {
        retval = data.Fr;
    } else {
        retval =data.En;
    }

    return retval === undefined ? keyword : retval;
}


function CreateMeetingAgenda(selectedRows) {
    sessionStorage.setItem("SelectedTransfers", selectedRows);
    window.location.href = '#createbytemplate/4/Meeteing Agenda';
}

function openAgendaTopicsList(documentId, delegationId, componentId, tabId, readonly, categoryId) {
    CTSCoreComponents.CustomTabs.openAgendaTopicsList(documentId, delegationId, componentId, tabId, readonly, categoryId);
}

function getSimpleSearchForSearchModel() {
    if (window.location.hash.includes('search')){
        let hashArray = window.location.hash.split("/");
        var returnedVal = decodeURIComponent(hashArray[1]);
        
        if (returnedVal == 'undefined') {
            return "";
        }
        return returnedVal;
    }
    
}
document.addEventListener('DOMContentLoaded', function () {
  /*  document.querySelector('.nav-link[data-search-open]').addEventListener('click', function (event) {
   
        event.preventDefault();
        triggerSearch(); 
    });*/

    document.getElementById("txtSimpleSearchFilter").addEventListener('keydown', function (event) {
       
        if (event.key === "Enter") { 
            event.preventDefault();
            triggerSearch(); 
        }
    });
});

function triggerSearch() {

    const searchInput = document.getElementById("txtSimpleSearchFilter");
    const searchValue = searchInput.value.trim();

    if (searchValue) {
        window.location.hash = "#search/" + encodeURIComponent(searchValue);
    }
}


//document.querySelector('.nav-link[data-search-open]').addEventListener('click', function (event) {
//    ;
//    event.preventDefault();
//    console.log("Nav link clicked - plain JavaScript");
//});


////$("#txtSimpleSearchFilter").click(function (event) {


////    if (event.keyCode === 13) {
////        if ($('#txtSimpleSearchFilter').val().trim())
////            window.location.hash = "#search/" + encodeURIComponent($('#txtSimpleSearchFilter').val());
////    }

////});

////$("#txtSimpleSearchFilter").keypress(function (event) {
 
    
////    if (event.keyCode === 13) {
////        if ($('#txtSimpleSearchFilter').val().trim())
////            window.location.hash = "#search/" + encodeURIComponent($('#txtSimpleSearchFilter').val());
////    }

////});

(function (window, document, $, undefined) {
   

    $(function () {
        

        var navSearch = new navbarSearchInput();
        var rightSide = new rightSideInput();

        // Open search input
        var $searchOpen = $('[data-search-open]');

        $searchOpen
            .on('click', function (e) { e.stopPropagation(); })
            .on('click', navSearch.toggle);

        // Close search input
        var $searchDismiss = $('[data-search-dismiss]');
        var inputSelector = '.navbar-form input[type="text"]';

        $(inputSelector)
            .on('click', function (e) { e.stopPropagation(); })
            .on('keyup', function (e) {
                if (e.keyCode == 27) // ESC
                    navSearch.dismiss();
            });

        // click anywhere closes the search
        $(document).on('click', navSearch.dismiss);

        // click anywhere closes the right side bar 

        $(document).on('click', function (event) {
            rightSide.dismiss(event);
        });

        // dismissable options
        $searchDismiss
            .on('click', function (e) { e.stopPropagation(); })
            .on('click', navSearch.dismiss);

    });

    var navbarSearchInput = function () {
        var navbarFormSelector = 'form.navbar-form';
        return {
            toggle: function () {

                var navbarForm = $(navbarFormSelector);

                navbarForm.toggleClass('open');

                var isOpen = navbarForm.hasClass('open');

                navbarForm.find('input')[isOpen ? 'focus' : 'blur']();

            },

            dismiss: function () {
                $(navbarFormSelector)
                    .removeClass('open') // Close control
                    .find('input[type="text"]').blur() // remove focus
                     .val('')                    // Empty input
                    ;
            }
        };

    }

    var rightSideInput = function () {
        return {
            dismiss: function (event) {
                // Check if the clicked element is not inside the 'offsidebar'
                if (!$(event.target).closest('.offsidebar').length) {
                    if ($('body').hasClass('offsidebar-open')) {
                        $('#rightSide').click();
                    }
                }
            }
        };
    };

})(window, document, window.jQuery);

function openSearchDocumentEdit(e) {
    CTSCustomComponents.CustomActions.openSearchDocumentEdit(e, null, false);
}

function isFollowUpNode(nodeId) {
    return nodeId == window.MyFollowUpNode || nodeId == window.AssignedToMeNode || nodeId == window.AssignedToOthersNode || nodeId == window.DepartmentFollowUpsNode;
}

function nodesRoutes(nodeName, nodeId, delegationId) {
    var href = '#' + nodeName + '/' + nodeId;

    if (delegationId)
        href += '/' + delegationId

        window.location.href = href;
}

function addBusinessDays(originalDate, numDaysToAdd) {
    const Sunday = 0;
    const Saturday = 6;
    let daysRemaining = numDaysToAdd - 1;
    const newDate = originalDate;
    while (daysRemaining > 0) {
        newDate.setDate(newDate.getDate() + 1);
        if (newDate.getDay() !== Sunday && newDate.getDay() !== Saturday) {
            daysRemaining--;
        }
    }
    return newDate;
}

function getAllCommittee() {
    Common.ajaxGet('/Committee/GetList', null, function (data) {
        sessionStorage.setItem("AllCommitteeData", JSON.stringify(data));
    });
}

function getAllMeetingLocation() {
    Common.ajaxGet('/ctsold/Lookup/GetLookupItemsByName?name=Locations&language=' + window.language, null, function (data) {
        sessionStorage.setItem("AllMeetingLocation", JSON.stringify(data));
    });
}

function getCommitteeName (Id) {
    if (Id != undefined) {
        var data = JSON.parse(sessionStorage.getItem("AllCommitteeData")).find(f => f.id == Id);
        if (data != undefined)
            return window.language == "ar" ? (data.nameAr == "" ? data.name : data.nameAr) : data.name;
        return "";
    }
}

function getMeetingLocation (Id) {
    if (Id != undefined) {
        var data = JSON.parse(sessionStorage.getItem("AllMeetingLocation")).find(f => f.id == Id);
        return data.text;
    }
}

function tryCloseModal(modalComponentId) {
    if (modalComponentId == null || $("div[ref='" + modalComponentId + "']").length <= 0)
        return;

    $("#" + modalComponentId + "_btnClose").trigger("click");
}

var StructureTypeMode = (function (E) {
    E = {};
    E.Internal = "1";
    E.External = "2";
    E.Both = "3";
    return E;
}(StructureTypeMode));

var PostponeType = (function (E) {
    E = {};
    E.FollowUp = "1";
    E.Transfer = "2";
    E.Task = "3";
    return E;
}(PostponeType));


//Search Menu
const searchInput = document.getElementById('sidebar-menusearch');
const menuItems = document.querySelectorAll(".wrapper aside nav > ul > li");

if (searchInput) {
    searchInput.addEventListener('input', function () {
    const query = this.value.toLowerCase().trim();
    const sidebar = document.querySelector("nav.sidebar");
    const items = sidebar.querySelector("ul.nav").querySelectorAll(":scope > li:not(.search-container):not(.nav-heading)");

    if (query === "") {
        items.forEach(item => {
            item.classList.remove('hidden');
            const subItems = item.querySelectorAll('ul li');
            subItems.forEach(sub => {
                sub.classList.remove('hidden');
            });
        });
        return;
    }

    items.forEach(item => {
        const parentText = (item.firstChild && item.firstChild.textContent) ? item.firstChild.textContent.toLowerCase() : ""; // safer way to get text
        const subItems = item.querySelectorAll('ul li');

        const isParentMatch = parentText.includes(query);
        let hasVisibleChild = false;

        subItems.forEach(sub => {
            const subText = sub.textContent.toLowerCase();
            const isSubMatch = subText.includes(query);

            if (isParentMatch || isSubMatch) {
                sub.classList.remove('hidden');
                hasVisibleChild = true;
            } else {
                sub.classList.add('hidden');
            }
        });

        if (isParentMatch || hasVisibleChild) {
            item.classList.remove('hidden');
        } else {
            item.classList.add('hidden');
        }
    });
});
}

function getExportedDocumentsTotalCount(name) {
    Common.ajaxGet('/Transfer/GetExportedDocumentsTotalCount',null, function (data) {
        if (typeof data == "number")
            $("#countTotal" + name).text(data);
         else
            return 0
    });
}
function getExportedDocumentsTodayCount(name) {
    Common.ajaxGet('/Transfer/GetExportedDocumentsTodayCount', null, function (data) {
        if (typeof data == "number")
            $("#countToday" + name).text(data);
        else
            return 0
    });
}
function getExportedDocumentsUnreadCount(name) {
    Common.ajaxGet('/Transfer/GetExportedDocumentsUnreadCount', null, function (data) {
        if (typeof data == "number")
            $("#countUnread" + name).text(data);
        else
            return 0
    });
}

function getRejectedDocumentsTotalCount(name) {
    Common.ajaxGet('/Transfer/GetRejectedDocumentsTotalCount', null, function (data) {
        if (typeof data == "number")
            $("#countTotal" + name).text(data);
        else
            return 0
    });
}
function getRejectedDocumentsTodayCount(name) {
    Common.ajaxGet('/Transfer/GetRejectedDocumentsTodayCount', null, function (data) {
        if (typeof data == "number")
            $("#countToday" + name).text(data);
        else
            return 0
    });
}
function getRejectedDocumentsUnreadCount(name) {
    Common.ajaxGet('/Transfer/GetRejectedDocumentsUnreadCount', null, function (data) {
        if (typeof data == "number")
            $("#countUnread" + name).text(data);
        else
            return 0
    });
}

function getFunctionName(fullString) {

    let functionName = fullString.split('(')[0];

    return functionName;

}

function refreshG2GgridItem() {
    GridCommon.Refresh('grdg2gItems');
    GetG2GCount('ReceiveOrReject');
    GetG2GCount('IncomingRejected');
}

async function getG2GReciveOrRejectCountToday() {
    return await GetG2GTodayCount("G2G_DocumentInboxReceiveOrReject");
}
async function getG2GReciveOrRejectCountTotal() {
    return await GetG2GTotalCount("G2G_DocumentInboxReceiveOrReject")
}
async function GetG2GTotalCount(NodeName, ViewName) {
    return await GetG2GCount(NodeName);
}

async function GetG2GTodayCount(NodeName, ViewName) {
    return await GetG2GCount(NodeName);
}

function GetG2GCount(NodeName, ViewName) {
    return new Promise(function (resolve, reject) {

        $.ajax({

            method: "GET",

            url: "/G2G/GetG2GCounts?NodeName=" + NodeName,

            success: function (res) {
                $("#countTotalCustom" + res.nodeId).text(res.total);
                $("#countTodayCustom" + res.nodeId).text(res.today);
                //if (countName == "TodayCount") {
                //    resolve();
                //}
                //else {
                //    resolve();
                //}
            },
            error: function (err) {
                reject(err);
            }
        });
    });
}

window.addEventListener('message', function (event) {
    // Make sure the message is from a trusted source
    var url = new URL(window.g2GUrl);
    if (event.origin === url.origin) {
        if (event.data === 'refreshG2GgridItem') {
            refreshG2GgridItem();
        }

        if (event.data === 'callParentFunction') {
            parentFunction();
        }
    }
});

function parentFunction() {
    if ($('#g2gExportModalClose')) {
        $('#g2gExportModalClose').click();
        $(".btn-back").trigger("click");
        refreshG2GgridItem();
    }
}