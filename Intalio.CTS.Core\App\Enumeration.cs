﻿namespace Intalio.CTS.Core
{
    public enum HTTPRequestType
    {
        POST = 1,
        GET = 2
    }

    public enum NotificationTemplateName
    {
        OnTestEmail,
        OnTransfer,
        OnDelegation,
        OnComplete,
        MobileConfiguration,
        OnDueDateTransfer,
        OnFollowUpNoteCreated,
        OnFollowUpNoteUpdated,
        OnFollowUpRequestCompletion,
        OnFollowUpCreated,
        OnFollowUpCompleted,
        OnRecall
    }

    public enum NodeType
    {
        Folder = 1,
        File = 2,
        User = 3,
        Signature = 4
    }

    public enum DocumentStatus
    {
        Draft = 1,
        InProgress = 2,
        Completed = 3,
        RequestToComplete = 4,
        Postponed = 5,
        Canceled = 6,
        ReceiveOrReject = 19
    }

    public enum FilingPlanKeywords
    {
        Year = 1,
        Month = 2,
        Day = 3,
        CategoryName = 4
    }

    public enum ReferenceNumberType
    {
        Year,
        Month,
        String,
        Counter,
        Separator,
        StructureCode,
        Dynamic,
        DynamicProcedure
    }

    public enum ActivityLogs
    {
        View = 1,
        Lock = 2,
        Unlock = 3,
        Save = 4,//to be replaced not used
        DownloadAttachment = 5,
        Recall = 6,
        Complete = 7,
        DeleteAttachment = 8,
        RestoreAttachment = 9,
        PrintAttachment = 10,
        ViewAttachment = 11,
        AddAttachment = 12,
        ReplaceAttachment = 13,
        Transfer = 14,
        ViewTransfer = 15,
        ViewDocument = 16,
        ViewAttachments = 17,
        ViewNotes = 18,
        ViewLinkedDocument = 19,
        ViewNonArchivedAttachments = 20,
        ViewTransfersHistory = 21,
        ViewVisualTracking = 22,
        ViewActivityLog = 23,
        Search = 24,
        DismissCopy = 25,
        RenameAttachment = 26,
        RenameFolder = 27,
        PrintReport = 28,
        ExportReportToExcel = 29,
        ExportReportToPdf = 30,
        AddFolder = 31,
        DeleteFolder = 32,
        ConvertToPdf = 33,
        GenerateLetter = 34,
        ReturnToDraft =35,
        SignDocument = 36,
        AddAnnotation = 37,
        DeleteFollowUpDocument = 39,
        FollowUpTask = 40,
        DeleteFollowUpTask = 41,
        Unarchive = 42,
        Proceed = 43,
        Cancel = 44,
        SignAndProceed = 45,
        SaveEdit = 46,
        AddLinkedDocument = 47,
        AddNote = 48,
        Send = 49,
        Unread = 50,
        ReAssign=51,
        DeleteLinkedDocument=52,
        Accept = 53,
        Reject = 54,
        CreateFollowUp = 55,
        EditFollowup = 56,
        AddFollowUpUser = 57,
        RemoveFollowUpUser = 58,
        CompleteFollowUp = 59,
        CancelFollowUp = 60,
        PostponeFollowUp = 61,
        AddInstruction = 62,
        RemoveInstruction = 63,
        AddEvent = 64,
        RemoveEvent = 65,
        
        PostponeTransfer = 66,
        EditFollowUpUserRole = 67,
        UnsignDocument = 68,
        Exported = 69,

        AddNonArchivedAttachment=70,
        DeleteNonArchivedAttachment=71,
        EditNonArchivedAttachment=72,
        EditAfterExport = 73,
        Reply = 74,

    }

    public enum BarcodeType
    {
        Code128 = 1,
        QR = 2,
        Pdf417 = 3
    }

    public enum TextAlign
    {
        Left = 1,
        Center = 2,
        Right = 3
    }

    public enum LogoutAction
    {
        RedirectToIdentity = 1,
        NoRedirect = 2
    }
    public enum PriorityPrivacyAction
    {
        Priority = 1,
        Privacy = 2
    }
    public enum ViewerMode
    {
        Simple = 1,
        Custome = 2

    }
    public enum CaptionType
    {
        CreatedDate = 1,
        StructureCode = 2,
        ReferenceNumber = 3
    }

    public enum ReplyType
    {
        ReplyToUser = 2,
        ReplyToStructure = 3,
        ReplyToInitiator = 7 // in case workflow
    }

    public enum UserRole
    {
        Administrator = 1,
        User = 2
    }
    public enum NodeInherit
    {
        Draft,
        Inbox,
        Completed,
        MyRequests,
        Sent,
        Closed,
        Custom,
        StructureInbox,
        StructureSent,
        FollowUp
    }
    public enum Role
    {
        Administrator = 1,
        FullControl = 2,
        Contribute = 3,
        Read = 4
    }
    public enum StructureType
    {
        Internal = 1,
        External = 2,
        Both = 3
    }

    public enum AttachmentStatus
    {
        Pending = 1,
        InProgress = 2,
        Completed = 3,
        Failed = 4
    }

    public enum CTSAuditModule
    {
        purpose = 1000,
        Category=2000
    }

    // there is two menu have name [Node]
    // there is two menu have name [Categories]
    public enum CustomMenus
    {
        SystemDelegation = 1,
        Committee,
        CreateMeetingAgenda,
        AddBasket,
        Delegation,
        ToDoList,
        AdvanceSearch,
        AdvanceSearchConfiguration,
        ManageCorrespondence,
        ManageStructureUsersCorrespondences,
        Dashboard,
        SystemDashboard,
        Bookmarks,
        AuditTrail,
        Assemblies,
        Logs,
        Servers,
        OrganizationManagement,
        AutoForward,
        EntityGroup,
        Categories,
        ScanConfiguration,
        Templates,
        AttachmentFolders,
        Nodes,
        Lookup,
        FilingPlan,
        FavoriteStructures,
        DistributionList,
        Reports,
        InProgressTransfers,
        CompletedTransfers,
        InProgressCorrespondences,
        CompletedCorrespondences,
        CorrespondenceDetail,
        StatisticalCorrespondences,
        OperationByUser,
        OperationByCorrespondence,
        Kpi,
        AverageDurationForCorrespondenceCompletion,
        AverageDurationForCorrespondenceDelay,
        AverageDurationForTransferCompletion,
        AverageDurationForTransferDelay,
        Barcode,
        ReferenceNumber,
        ReferenceCounter,
        BarcodeConfiguration,
        LookupMenus,
        Purposes,
        Status,
        Priorities,
        Classifications,
        Importances,
        Privacies,
        DocumentType,
        NonArchivedAttachmentsTypes,
        Security,
        CustomMenus,
        CustomActions,
        CustomTabs,
        Users,
        Roles,
        Settings,
        TranslatorDictionary,
        NotificationTemplates,
        Parameters,
        SchedulerAdministration,
        ApiDocumentation,
        MoveTransfers,
        CategoriesSecurity,
        NodesSecurity,
        ManageDepartmentUsers,
        TransfersSentToStructure,
        OutgoingFromDepartment,
        SecureUserId,
        TemplatesManagement,
    }

    public enum CustomTabs
    {
        MyTransfer = 1,
        VisualTracking,
        Notes,
        Attachments,
        LinkedCorrespondence,
        TransfersHistory,
        ActivityLog,
        Attributes,
        NonArchivedAttachments,
        AgendaTopicsList,
        BasketAttribute,
        Event,
        Instruction,
        ExportedRequests

    }

    public enum CustomNodes
    {
        Draft = 1,
        Inbox,
        Completed,
        MyRequests,
        Search,
        Sent,
        Closed,
        basketnode,
    }

    public enum CustomActions
    {
        Transfer_Transfer = 1,
        Transfer_Send,
        Folder_AddFolder,
        Complete,
        Folder_Upload,
        DismissCopy,
        Transfer_Complete,
        Transfer_ReplyToUser,
        Transfer_ReplyToStructure,
        Transfer_DismissCarbonCopy,
        Transfer_ReplyBy,
        Attribute_Save,
        Transfer,
        LinkedDocument_New,
        NonArchivedAttachments_New,
        NonArchivedAttachments_Delete,
        Note_New,
        Note_Delete,
        LinkedDocument_Delete,
        Folder_Scan,
        Folder_MultipleUpload,
        File_Rename,
        File_Replace,
        File_Download,
        File_Delete,
        File_ViewHistory,
        File_ConvertToPdf,
        File_GenerateLetter,
        Edit,
        Unlock,
        sent_AddCCAndSend,
        sent_SendCopies,
        Event_New,
        Event_Delete,
        Instruction_New,
        Instruction_Delete,
        CreateFollowUp,
        EditAfterSign

    }

    public enum FollowUpStatus
    {
        InProgress = 1,
        Overdued,
        Postponed,
        Completed,
        Canceled,
        
    }
    public enum FollowUpRoles
    {
        Owner = 1,
        Editor,
        Reader,
        
    }

    public enum RequestStatuses
    {
        Pending = 1,
        Accepted = 2,
        Rejected = 3,
        Recalled = 4,
        Dismissed=5
    }
    public enum DocumentLockStatus
    {
        Unlocked = 0,
        DocumentNotLocked = 1,
        DocumentLockedByDifferentUser = 2,
    }
}
