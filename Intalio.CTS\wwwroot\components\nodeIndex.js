import Intalio from './common.js'
import { Categories, FollowUpStatuses, CategoryModel, FollowUpStatusModel } from './lookup.js'
class NodeIndex extends Intalio.Model
{
    constructor()
    {
        super();
        this.roles = [];
    }
}
var NodeInheritProperties = (function (E)
{
    E = {};
    E.Draft = "Draft";
    E.Inbox = "Inbox";
    E.StructureInbox = "StructureInbox";
    E.StructureSent = "StructureSent";
    E.Completed = "Completed";
    E.MyRequests = "MyRequests";
    E<PERSON>Sent = "Sent";
    E.Closed = "Closed";
    E.FollowUp = "FollowUp";
    <PERSON><PERSON>Filters = function (name)
    {
        switch (name)
        {
            case E.Draft:
                return ["FromDate", "ToDate", "Category", "Subject", "User"];
            case E.Inbox:
                return ["ReferenceNumber", "Period", "FromDate", "ToDate", "Category", "Subject", "Purpose", "Priority", "Privacy", "Read", "Locked", "OverDue", "Structure", "User"];
            case E.StructureInbox:
                return ["ReferenceNumber", "FromDate", "ToDate", "Category", "Subject", "Purpose", "Priority", "Privacy", "Read", "Locked", "OverDue", "Structure", "User"];
            case E.Completed:
            case E.Sent:
                return ["ReferenceNumber", "FromDate", "ToDate", "Category", "Subject", "Purpose", "Priority", "Privacy", "Structure", "User"];
            case E.StructureSent:
                return ["ReferenceNumber", "FromDate", "ToDate", "Category", "Subject", "Purpose", "Priority", "Privacy", "Structure", "User"];
            case E.MyRequests:
                return ["ReferenceNumber", "FromDate", "ToDate", "Category", "Subject", "OverDue"];
            case E.Closed:
                return ["ReferenceNumber", "FromDate", "ToDate", "Category", "Subject"];
            case E.FollowUp:
                return ["ReferenceNumber", "ReceivingEntity", "SendingEntity", "Subject", "FollowedUpBy", "Category", "Year", "KeyWord", "Priority","FromDate","ToDate","Note"];
        }
    };
    E.Columns = function (name)
    {
        switch (name)
        {
            case E.Draft:
                return ["Category", "Subject", "ReferenceNumber", "SendingEntity", "ReceivingEntity", "CreatedDate", "ModifiedDate", "User", "DraftStatus", "Custom"];
            case E.MyRequests:
                return ["Category", "Subject", "ReferenceNumber", "SendingEntity", "ReceivingEntity", "CreatedDate", "DueDate", "Custom"];
            case E.Inbox:
                return ["Category", "ReferenceNumber", "Subject", "From", "TransferDate", "DocumentDate", "CreatedDate", "SendingEntity", "ReceivingEntity", "Purpose", "Priority", "Privacy", "CreatedBy", "OpenedDate", "DueDate", "Custom", "TransferNumberOfDays", "ExportedNumberOfDays", "ExportedDate", "ExportedUser", "NumberOfDays", "SignedBy"];
            case E.StructureInbox:
                return ["Category", "ReferenceNumber", "Subject", "To", "From", "TransferDate", "CreatedDate", "SendingEntity", "ReceivingEntity", "Purpose", "Priority", "Privacy", "CreatedBy", "OpenedDate", "DueDate", "Custom", "SignedBy"];
            case E.Completed:
                return ["Category", "ReferenceNumber", "Subject", "From", "TransferDate", "SendingEntity", "ReceivingEntity", "Purpose", "Priority", "Privacy", "CreatedBy", "OpenedDate", "ClosedDate", "Custom"];
            case E.Sent:
                return ["Category", "ReferenceNumber", "Subject", "To", "TransferDate", "Owner", "SendingEntity", "ReceivingEntity", "Purpose", "Priority", "Privacy", "CreatedBy", "OpenedDate", "ClosedDate", "Custom"];
            case E.StructureSent:
                return ["Category", "ReferenceNumber", "Subject", "From", "To", "TransferDate", "Owner", "SendingEntity", "ReceivingEntity", "Purpose", "Priority", "Privacy", "CreatedBy", "OpenedDate", "ClosedDate", "Custom"];
            case E.Closed:
                return ["Category", "Subject", "ReferenceNumber", "SendingEntity", "ReceivingEntity", "CreatedDate", "ClosedDate", "Custom"];
            case E.FollowUp:
                return ["ReferenceNumber", "SendingEntity", "ReceivingEntity", "FollowUpStatus", "Subject","DueDate" ];

        }
    };
    E.Conditions = function (name)
    {
        switch (name)
        {
            case E.Draft:
                return ["Category", "DocumentCreatedDate", "CreatedByStructure", "DraftStatus"];
            case E.MyRequests:
                return ["Category", "DocumentCreatedDate"];
            case E.Inbox:
                return ["Category", "DocumentCreatedDate", "TransferCreatedDate", "Read", "Locked", "OverDue", "IsSigned", "IsExported", "ToUser", "ToStructure","RequestStatus"];
            case E.StructureInbox:
                return ["Category", "DocumentCreatedDate", "TransferCreatedDate", "Read", "Locked", "OverDue", "IsSigned", "ToUser", "ToStructure", "RequestStatus", "IsExported"];
            case E.Completed:
                return ["Category", "DocumentCreatedDate", "TransferClosedDate", "CCed"];
            case E.Sent:
                return ["Category", "DocumentCreatedDate", "TransferClosedDate", "IsClosedTransfer", "IsCompletedFollowUp","RequestStatus"];
            case E.StructureSent:
                return ["Category", "DocumentCreatedDate", "TransferClosedDate", "IsClosedTransfer", "IsCompletedFollowUp"];
            case E.Closed:
                return ["Category", "DocumentCreatedDate", "DocumentCompletedDate"];
            case E.FollowUp:
                return ["Category", "FollowUpStatus", "IsHasNote", "OverDue"];
        }
    };
    E.getFilters = function (name)
    {
        var filterList = E.Filters(name);
        var options = new Array();
        filterList.forEach(function (filter, index)
        {
            options.push({
                id: filter,
                text: Resources[filter] || filter
            });
        });
        return options;
    };
    E.getColumns = function (name)
    {
        var columnList = E.Columns(name);
        var options = new Array();
        columnList.forEach(function (column, index)
        {
            options.push({
                id: column,
                text: Resources[column] || column
            });
        });
        return options;
    };
    E.initColumns = function (name)
    {
        $("#columnDiv").removeClass("col-md-3").addClass("col-md-6");
        if ($("#columnNumberDiv").length > 0)
        {
            if ($("#orderColumnContainer").hasClass("col-md-3"))
            {
                $("#orderColumnContainer").attr("class", "").addClass("col-md-6");
            } else
            {
                $("#orderColumnContainer").attr("class", "").addClass("col-md-3");
            }
        }
        $("#columnNumberDiv").remove();
        $("#columnFunctionDiv").remove();
        $("#tooltipCustom").remove();
        switch (name)
        {
            case "Custom":
                $("#columnDiv").removeClass("col-md-6").addClass("col-md-3");
                $("#ColumnLegend").append(' <label id="tooltipCustom" class="control-label"><i class="fa fa-info-circle font-15 infoDivIcon"></i><div class="infoDiv font-13" style="opacity:0;width: 250px;left: auto!important;right: auto!important;">' +
                    Resources.JsFunctionName + ' ' + Resources.MustReturnValidString + '</div></label>');
                if ($("#orderColumnContainer").hasClass("col-md-3"))
                {
                    $("#orderColumnContainer").removeClass("col-md-3").addClass("col-md-2");
                    $("#columnDiv").after('<div class="col-md-2" id="columnNumberDiv"><div class="form-group"><input type="text" required="required" id="txtColumnName" data-parsley-group="columns" placeholder="' +
                        Resources.Name + '" class="form-control" autocomplete="off" tabindex="14" data-parsley-length="[1, 50]" maxlength="50" aria-hidden="true"></div></div><div class="col-md-2" id="columnFunctionDiv"><div class="form-group"><input type="text" required="required" id="txtColumnFunction" data-parsley-group="columns" placeholder="' +
                        Resources.JsFunctionName + '" class="form-control" autocomplete="off" tabindex="14" data-parsley-length="[1, 50]" maxlength="50" aria-hidden="true"></div></div>');
                } else
                {
                    $("#orderColumnContainer").removeClass("col-md-6").addClass("col-md-3");
                    $("#columnDiv").after('<div class="col-md-3" id="columnNumberDiv"><div class="form-group"><input type="text" required="required" id="txtColumnName" data-parsley-group="columns" placeholder="' +
                        Resources.Name + '" class="form-control" autocomplete="off" tabindex="14" data-parsley-length="[1, 50]" maxlength="50" aria-hidden="true"></div></div><div class="col-md-3" id="columnFunctionDiv"><div class="form-group"><input type="text" required="required" id="txtColumnFunction" data-parsley-group="columns" placeholder="' +
                        Resources.JsFunctionName + '" class="form-control" autocomplete="off" tabindex="14" data-parsley-length="[1, 50]" maxlength="50" aria-hidden="true"></div></div>');
                }
                break;
        }
    };
    E.getConditions = function (name)
    {
        var conditionsList = E.Conditions(name);
        var options = new Array();
        conditionsList.forEach(function (condition, index)
        {
            options.push({
                id: condition,
                text: Resources[condition] || condition
            });
        });
        return options;
    };
    E.initConditionValue = function (name)
    {
        $("#valueContainer").empty();
        var html = "";
        switch (name)
        {
            case "Category":
                html = '<div class="form-group" id="conditionCategoryContainer"><select id="cmbConditionCategory" data-parsley-group="conditions" ' +
                    'tabindex="16" required="required" data-parsley-errors-container="#cmbConditionCategoryError" class="form-control"></select><div id="cmbConditionCategoryError"></div></div>';
                $("#valueContainer").html(html);
                $('#cmbConditionCategory').select2({
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    width: "100%",
                    data: new Categories().get(window.language),
                    allowClear: false,
                    placeholder: Resources.Category,
                    multiple: "multiple",
                    dropdownParent: $('#conditionCategoryContainer')
                });
                $("#cmbConditionCategory").val('').trigger('change');
                break;
            case "DraftStatus":
                html = '<div class="form-group" id="conditionDraftStatusContainer"><select id="cmbConditionDraftStatus" data-parsley-group="conditions" ' +
                    'tabindex="16" required="required" data-parsley-errors-container="#cmbConditionDraftStatusError" class="form-control"></select><div id="cmbConditionDraftStatusError"></div></div>';
                $("#valueContainer").html(html);
                $('#cmbConditionDraftStatus').select2({
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    width: "100%",
                    data: [{ id: 1, text: Resources.Created }, { id: 2, text: Resources.Pending }, { id: 3, text: Resources.Accepted }, { id: 4, text: Resources.Rejected }],
                    allowClear: false,
                    placeholder: Resources.DraftStatus,
                    multiple: "multiple",
                    dropdownParent: $('#conditionDraftStatusContainer')
                });
                $("#cmbConditionDraftStatus").val('').trigger('change');
                break;
            case "RequestStatus":
                html = '<div class="form-group" id="conditionRequestStatusContainer"><select id="cmbConditionRequestStatus" data-parsley-group="conditions" ' +
                    'tabindex="16" required="required" data-parsley-errors-container="#cmbConditionRequestStatusError" class="form-control"></select><div id="cmbConditionRequestStatusError"></div></div>';
                $("#valueContainer").html(html);
                $('#cmbConditionRequestStatus').select2({
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    width: "100%",
                    data: [{ id: 0, text: Resources.Default },{ id: 1, text:  Resources.Pending },{ id: 2, text:  Resources.Accepted },{ id: 3, text:  Resources.Rejected },{ id: 4, text:  Resources.Recalled }],
                    allowClear: false,
                    placeholder: Resources.RequestStatus,
                    multiple: "multiple",
                    dropdownParent: $('#conditionRequestStatusContainer')
                });
                $("#cmbConditionRequestStatus").val('').trigger('change');
                break;
            case "FollowUpStatus":
                html = '<div class="form-group" id="conditionFollowUpStatusContainer"><select id="cmbConditionFollowUpStatus" data-parsley-group="conditions" ' +
                    'tabindex="16" required="required" data-parsley-errors-container="#cmbConditionFollowUpStatusError" class="form-control"></select><div id="cmbConditionFollowUpStatusError"></div></div>';
                $("#valueContainer").html(html);
                $('#cmbConditionFollowUpStatus').select2({
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    width: "100%",
                    data: new FollowUpStatuses().get(window.language),
                    allowClear: false,
                    placeholder: Resources.FollowUpStatus,
                    multiple: "multiple",
                    dropdownParent: $('#conditionFollowUpStatusContainer')
                });
                $("#cmbConditionFollowUpStatus").val('').trigger('change');
                break;
            case "IsHasNote":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionIsHasNote" tabindex="16" type="checkbox" name="IsHasNote" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.IsHasNote + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "DocumentCreatedDate":
            case "DocumentCompletedDate":
            case "TransferCreatedDate":
            case "TransferClosedDate":
                html = '<div class="row"><div class="col-md-4"><div class="form-group" id="conditionDateContainer"><select id="cmbConditionDate" data-parsley-group="conditions" ' +
                    'tabindex="16" required="required" data-parsley-errors-container="#cmbConditionDateError" class="form-control"><option value="Year">' + Resources.Year + '</option>' +
                    '<option value="Month">' + Resources.Month + '</option><option value="Day">' + Resources.Day + '</option></select><div id="cmbConditionDateError"></div></div></div>' +
                    '<div class="col-md-3"><div class="form-group" id="conditionDateSignContainer"><select id="cmbConditionDateSign" data-parsley-group="conditions" ' +
                    'tabindex="17" required="required" data-parsley-errors-container="#cmbConditionDateSignError" class="form-control"><option value="<"><</option><option value="<="><=</option></select><div id="cmbConditionDateSignError"></div></div></div>' +
                    '<div class="col-md-5"><div class="form-group"><input type="number" required="required" min="1" id="txtDateNumber" data-parsley-group="conditions" placeholder="' + Resources.Number + '" class="form-control" max="100" autocomplete="off" tabindex="18" aria-hidden="true"></div></div></div>';
                $("#valueContainer").html(html);
                $('#cmbConditionDate').select2({
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    width: "100%",
                    allowClear: false,
                    dropdownParent: $('#conditionDateContainer')
                });
                $('#cmbConditionDateSign').select2({
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    width: "100%",
                    allowClear: false,
                    dropdownParent: $('#conditionDateSignContainer')
                });
                break;
            case "Read":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionRead" tabindex="16" type="checkbox" name="Read" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.Read + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "IsSigned":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionIsSigned" tabindex="16" type="checkbox" name="IsSigned" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.IsSigned + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "IsExported":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionIsExported" tabindex="16" type="checkbox" name="IsExported" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.IsExported + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "Locked":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionLocked" tabindex="16" type="checkbox" name="Locked" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.Locked + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "OverDue":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionOverdue" tabindex="16" type="checkbox" name="Overdue" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.OverDue + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "ToUser":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionToUser" tabindex="16" type="checkbox" name="ToUser" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.ToUser + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "ToStructure":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionToStructure" tabindex="16" type="checkbox" name="ToStructure" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.ToStructure + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "CreatedByStructure":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionCreatedByStructure" tabindex="16" type="checkbox" name="CreatedByStructure" value="true">' +
                    '<span class="fa fa-check"></span></label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "IsClosedTransfer":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionIsClosedTransfer" tabindex="16" type="checkbox" name="IsClosedTransfer" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.IsClosedTransfer + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "IsCompletedFollowUp":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionIsCompletedFollowUp" tabindex="16" type="checkbox" name="IsCompletedFollowUp" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.IsCompletedFollowUp + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
            case "CCed":
                html = '<div class="form-group"><div class="input-group"><div class="checkbox c-checkbox">' +
                    '<label><input id="chkConditionCCed" tabindex="16" type="checkbox" name="CCed" value="true">' +
                    '<span class="fa fa-check"></span>' + Resources.CCed + '</label></div></div></div>';
                $("#valueContainer").html(html);
                break;
        }
    };
    E.getConditionValue = function (json, name)
    {
        switch (name)
        {
            case "Category":
                var categoryArray = [];
                var dataSelected = $('#cmbConditionCategory').select2("data");
                for (var i = 0; i < dataSelected.length; i++)
                {
                    categoryArray.push({ id: dataSelected[i].id, text: dataSelected[i].text })
                }
                json.value = categoryArray;
                break;
            case "FollowUpStatus":
                var followUpStatusArray = [];
                var dataSelected = $('#cmbConditionFollowUpStatus').select2("data");
                for (var i = 0; i < dataSelected.length; i++)
                {
                    followUpStatusArray.push({ id: dataSelected[i].id, text: dataSelected[i].text })
                }
                json.value = followUpStatusArray;
                break;
            case "IsHasNote":
                json.value = $('#chkConditionIsHasNote').prop("checked");
                break;
            case "RequestStatus":
                var RequestStatusArray = [];
                var dataSelected = $('#cmbConditionRequestStatus').select2("data");
                for (var i = 0; i < dataSelected.length; i++) {
                    RequestStatusArray.push({ id: dataSelected[i].id, text: dataSelected[i].text })
                }
                json.value = RequestStatusArray;
                break;
            case "DraftStatus":
                var DrafStatusArray = [];
                var dataSelected = $('#cmbConditionDraftStatus').select2("data");
                for (var i = 0; i < dataSelected.length; i++) {
                    DrafStatusArray.push({ id: dataSelected[i].id, text: dataSelected[i].text })
                }
                json.value = DrafStatusArray;
                break;
            case "DocumentCreatedDate":
            case "DocumentCompletedDate":
            case "TransferCreatedDate":
            case "TransferClosedDate":
                var datejson = {};
                datejson.date = $('#cmbConditionDate').val();
                datejson.sign = $('#cmbConditionDateSign').val();
                datejson.number = $('#txtDateNumber').val();
                json.value = datejson;
                json.isDate = true;
                break;
            case "Read":
                json.value = $('#chkConditionRead').prop("checked");
                break;
            case "IsSigned":
                json.value = $('#chkConditionIsSigned').prop("checked");
                break;
            case "IsExported":
                json.value = $('#chkConditionIsExported').prop("checked");
                break;
            case "Locked":
                json.value = $('#chkConditionLocked').prop("checked");
                break;
            case "OverDue":
                json.value = $('#chkConditionOverdue').prop("checked");
                break;
            case "ToUser":
                json.value = $('#chkConditionToUser').prop("checked");
                break;
            case "ToStructure":
                json.value = $('#chkConditionToStructure').prop("checked");
                break;
            case "CreatedByStructure":
                json.value = $('#chkConditionCreatedByStructure').prop("checked");
                break;
            case "IsClosedTransfer":
                json.value = $('#chkConditionIsClosedTransfer').prop("checked");
                break;
            case "IsCompletedFollowUp":
                json.value = $('#chkConditionIsCompletedFollowUp').prop("checked");
                break;
            case "CCed":
                json.value = $('#chkConditionCCed').prop("checked");
                break;
        }
    };
    E.setConditionValue = function (data, name)
    {
        switch (name)
        {
            case "Category":
                var categoryIds = [];
                var categories = new CategoryModel().get();
                for (var i = 0; i < data.value.length; i++)
                {
                    var item = $.grep(categories, function (item)
                    {
                        return item.name === data.value[i].text || item.nameFr == data.value[i].text || item.nameAr == data.value[i].text;
                    });
                    categoryIds.push(item[0].id);
                }
                $('#cmbConditionCategory').val(categoryIds).trigger("change");
                break;
            case "FollowUpStatus":
                var followUpStatusIds = [];
                var followUpStatuses = new FollowUpStatusModel().get();
                for (var i = 0; i < data.value.length; i++)
                {
                    var item = $.grep(followUpStatuses, function (item)
                    {
                        return item.name === data.value[i].text || item.nameFr == data.value[i].text || item.nameAr == data.value[i].text;
                    });
                    followUpStatusIds.push(item[0].id);
                }
                $('#cmbConditionFollowUpStatus').val(followUpStatusIds).trigger("change");
                break;
            case "IsHasNote":
                $('#chkConditionIsHasNote').prop("checked", data.value);
            case "RequestStatus":
                var RequestStatusIds = [];
                var RequestStatus = [{ id: 0, text: Resources.Default },{ id: 1, text: Resources.Pending }, { id: 2, text: Resources.Accepted }, { id: 3, text: Resources.Rejected }, { id: 4, text: Resources.Recalled }];

                for (var i = 0; i < data.value.length; i++) {
                    var item = $.grep(RequestStatus, function (item) {
                        return item.text === data.value[i].text || item.nameFr == data.value[i].text || item.nameAr == data.value[i].text;
                    });
                    RequestStatusIds.push(item[0].id);
                }
                $('#cmbConditionRequestStatus').val(RequestStatusIds).trigger("change");
                break;
            case "DraftStatus":
                var DraftStatusIds = [];
                var DraftStatus = [{ id: 1, text: "Created" }, { id: 2, text: "Pending" }, { id: 3, text: "Accepted" }, { id: 4, text: "Rejected" }];

                for (var i = 0; i < data.value.length; i++) {
                    var item = $.grep(DraftStatus, function (item) {
                        return item.text === data.value[i].text || item.nameFr == data.value[i].text || item.nameAr == data.value[i].text;
                    });
                    DraftStatusIds.push(item[0].id);
                }
                $('#cmbConditionDraftStatus').val(DraftStatusIds).trigger("change");
                break;
            case "DocumentCreatedDate":
            case "DocumentCompletedDate":
            case "TransferCreatedDate":
            case "TransferClosedDate":
                $('#cmbConditionDate').val(data.value.date).trigger("change");
                $('#cmbConditionDateSign').val(data.value.sign).trigger("change");
                $('#txtDateNumber').val(data.value.number);
                break;
            case "Read":
                $('#chkConditionRead').prop("checked", data.value);
                break;
            case "IsSigned":
                $('#chkConditionIsSigned').prop("checked", data.value);
                break;
            case "IsExported":
                $('#chkConditionIsExported').prop("checked", data.value);
                break;
            case "Locked":
                $('#chkConditionLocked').prop("checked", data.value);
                break;
            case "OverDue":
                $('#chkConditionOverdue').prop("checked", data.value);
                break;
            case "ToUser":
                $('#chkConditionToUser').prop("checked", data.value);
                break;
            case "ToStructure":
                $('#chkConditionToStructure').prop("checked", data.value);
                break;
            case "CreatedByStructure":
                $('#chkConditionCreatedByStructure').prop("checked", data.value);
                break;
            case "IsClosedTransfer":
                $('#chkConditionIsClosedTransfer').prop("checked", data.value);
                break;
        }
    };
    E.renderConditionValue = function (data, name)
    {
        var html = "";
        switch (name)
        {
            case "Category":
                var categories = new CategoryModel().get();
                html = "";
                for (var i = 0; i < data.value.length; i++)
                {
                    var item = $.grep(categories, function (item)
                    {
                        return item.name === data.value[i].text || item.nameFr == data.value[i].text || item.nameAr == data.value[i].text;
                    });
                    if (item.length)
                    {
                        var itemName = item[0].name;
                        if (window.language === 'ar')
                        {
                            itemName = item[0].nameAr;
                        } else if (window.language === 'fr')
                        {
                            itemName = item[0].nameFr;
                        }
                        if (html === "")
                        {
                            html += itemName;
                        } else
                        {

                            html += "," + itemName;
                        }
                    }
                }
                return html;
            case "FollowUpStatus":
                var followUpStatuses = new FollowUpStatusModel().get();
                html = "";
                for (var i = 0; i < data.value.length; i++)
                {
                    var item = $.grep(followUpStatuses, function (followUp)
                    {
                        return followUp.name === data.value[i].text || followUp.nameFr == data.value[i].text || followUp.nameAr == data.value[i].text;
                    });
                    if (item.length)
                    {
                        var itemName = item[0].name;
                        if (window.language === 'ar')
                        {
                            itemName = item[0].nameAr;
                        } else if (window.language === 'fr')
                        {
                            itemName = item[0].nameFr;
                        }
                        if (html === "")
                        {
                            html += itemName;
                        } else
                        {

                            html += "," + itemName;
                        }
                    }
                }
                return html;
            case "IsHasNote":
                if (data.value === true) {
                    return '<em class="fa success fa-check"></em>';
                }
                return '<em class="fa danger fa-close"></em>';
            case "RequestStatus":
                var RequestStatus = [{ id: 0, text: Resources.Default },{ id: 1, text: Resources.Pending }, { id: 2, text: Resources.Accepted }, { id: 3, text: Resources.Rejected }, { id: 4, text: Resources.Recalled }];
                html = "";
                for (var i = 0; i < data.value.length; i++) {
                    var item = $.grep(RequestStatus, function (item) {
                        return item.text === data.value[i].text || item.nameFr == data.value[i].text || item.nameAr == data.value[i].text;
                    });
                    if (item.length) {
                        var itemName = item[0].text;
                        if (window.language === 'ar') {
                            itemName = item[0].text;
                        } else if (window.language === 'fr') {
                            itemName = item[0].text;
                        }
                        if (html === "") {
                            html += itemName;
                        } else {

                            html += "," + itemName;
                        }
                    }
                }
                return html;
            case "DraftStatus":
                var DraftStatus = [{ id: 1, text: "Created" }, { id: 2, text: "Pending" }, { id: 3, text: "Accepted" }, { id: 4, text: "Rejected" }];
                html = "";
                for (var i = 0; i < data.value.length; i++) {
                    var item = $.grep(DraftStatus, function (item) {
                        return item.text === data.value[i].text || item.nameFr == data.value[i].text || item.nameAr == data.value[i].text;
                    });
                    if (item.length) {
                        var itemName = item[0].text;
                        if (window.language === 'ar') {
                            itemName = item[0].text;
                        } else if (window.language === 'fr') {
                            itemName = item[0].text;
                        }
                        if (html === "") {
                            html += itemName;
                        } else {

                            html += "," + itemName;
                        }
                    }
                }
                return html;
            case "Status":
                var statuses = new Statuses().get(window.language);
                for (var i = 0; i < data.value.length; i++)
                {
                    var item = $.grep(statuses, function (item)
                    {
                        return item.id == data.value[i];
                    });
                    if (item.length)
                    {
                        if (html === "")
                        {
                            html += item[0].text;
                        } else
                        {

                            html += "," + item[0].text;
                        }
                    }
                }
                return html;
            case "DocumentCreatedDate":
            case "TransferCreatedDate":
            case "DocumentCompletedDate":
                var json = data.value;
                return Resources[json.date] + " " + json.sign + " " + json.number;
            case "Read":
            case "IsSigned":
            case "IsExported":
            case "Locked":
            case "OverDue":
            case "ToUser":
            case "ToStructure":
            case "CreatedByStructure":
            case "IsClosedTransfer":
            case "CCed":
                if (data.value === true)
                {
                    return '<em class="fa success fa-check"></em>';
                }
                return '<em class="fa danger fa-close"></em>';
            case "IsCompletedFollowUp":
                if (data.value === true) {
                    return '<em class="fa success fa-check"></em>';
                }
                return '<em class="fa danger fa-close"></em>';
        }
    };
    return E;
}(NodeInheritProperties));
function makeParent()
{
    $("#cmbInherit").removeAttr("required");
    if ($("#cmbInherit").val() === NodeInherit.Custom)
    {
        makeNotCustom();
    }
    $('.tohide').hide();
}
function makeNotParent()
{
    $('.tohide').show();
    $("#cmbInherit").attr("required", "required");
    if ($("#cmbInherit").val() === NodeInherit.Custom)
    {
        makeCustom();
    } else
    {
        makeNotCustom();
    }
}
function makeCustom()
{
    $('.noncustominherit').hide();
    $('.custominherit').show();
    $("#txtJsFunctionName").attr("required", "required");
    $("#txtJsFunctionName").attr("data-parsley-group", "primary");
    $("#txtJsFunctionName").parent().find(".control-label").addClass("field-required");
    if ($("#chkEnableTotalCount").prop('checked'))
    {
        $("#txtTotalCountJsFunctionName").attr("required", "required");
        $("#txtTotalCountJsFunctionName").attr("data-parsley-group", "primary");
        $("#txtTotalCountJsFunctionName").parent().find(".control-label").addClass("field-required");
    } else
    {
        $("#txtTotalCountJsFunctionName").removeAttr("required");
        $("#txtTotalCountJsFunctionName").removeAttr("data-parsley-group");
        $("#txtTotalCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
    }
    if ($("#chkEnableTodayCount").prop('checked'))
    {
        $("#txtTodayCountJsFunctionName").attr("required", "required");
        $("#txtTodayCountJsFunctionName").attr("data-parsley-group", "primary");
        $("#txtTodayCountJsFunctionName").parent().find(".control-label").addClass("field-required");
    } else
    {
        $("#txtTodayCountJsFunctionName").removeAttr("required");
        $("#txtTodayCountJsFunctionName").removeAttr("data-parsley-group");
        $("#txtTodayCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
    }
    if ($("#chkEnableUnreadCount").prop('checked')) {
        $("#txtUnreadCountJsFunctionName").attr("required", "required");
        $("#txtUnreadCountJsFunctionName").attr("data-parsley-group", "primary");
        $("#txtUnreadCountJsFunctionName").parent().find(".control-label").addClass("field-required");
    } else {
        $("#txtUnreadCountJsFunctionName").removeAttr("required");
        $("#txtUnreadCountJsFunctionName").removeAttr("data-parsley-group");
        $("#txtUnreadCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
    }

}
function makeNotCustom()
{
    $('.custominherit').hide();
    $('.noncustominherit').show();
    $("#txtJsFunctionName").removeAttr("required");
    $("#txtJsFunctionName").removeAttr("data-parsley-group");
    $("#txtJsFunctionName").parent().find(".control-label").removeClass("field-required");
    $("#txtTotalCountJsFunctionName").removeAttr("required");
    $("#txtTotalCountJsFunctionName").removeAttr("data-parsley-group");
    $("#txtTotalCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
    $("#txtTodayCountJsFunctionName").removeAttr("required");
    $("#txtTodayCountJsFunctionName").removeAttr("data-parsley-group");
    $("#txtTodayCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
    $("#txtUnreadCountJsFunctionName").removeAttr("required");
    $("#txtUnreadCountJsFunctionName").removeAttr("data-parsley-group");
    $("#txtUnreadCountJsFunctionName").parent().find(".control-label").removeClass("field-required");

}
function changeInheritProperties(value)
{
    var msg = $("#userPostMsg");
    msg.hide();
    if (value)
    {
        if (value !== NodeInherit.Custom)
        {
            $('#columnsTable').DataTable().rows().remove().draw();
            $('#conditionsTable').DataTable().rows().remove().draw();
            makeNotCustom();

            $('#cmbFilters').select2('destroy').select2().empty();
            $('#cmbFilters').select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                data: NodeInheritProperties.getFilters(value),
                allowClear: true,
                placeholder: Resources.SelectFilters,
                multiple: "multiple",
                dropdownParent: $('#filtersContainer')
            }).on("select2:select", function (evt)
            {
                var element = evt.params.data.element;
                var $element = $(element);
                $element.detach();
                $(this).append($element);
                $(this).trigger("change");
            });
            $("#cmbFilters").val('').trigger('change');

            $('#cmbColumn').select2('destroy').select2().empty();
            $('#cmbColumn').select2().off("change");
            $('#cmbColumn').select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                data: NodeInheritProperties.getColumns(value),
                allowClear: true,
                placeholder: Resources.SelectColumn + " *",
                dropdownParent: $('#columnContainer'),
                templateResult: function (data)
                {
                    if (data.id === "Custom")
                    {
                        return $("<span class='text-danger'>" + data.text + "</span>");
                    }
                    return data.text;
                }
            }).on('change', function ()
            {
                $(this).trigger('input');
                NodeInheritProperties.initColumns($(this).val());
            });
            $("#cmbColumn").val('').trigger('change');

            $('#cmbCondition').select2('destroy').select2().empty();
            $('#cmbCondition').select2().off("change");
            $('#cmbCondition').select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                data: NodeInheritProperties.getConditions(value),
                allowClear: true,
                placeholder: Resources.SelectCondition + " *",
                dropdownParent: $('#conditionContainer')
            }).on('change', function ()
            {
                $(this).trigger('input');
                NodeInheritProperties.initConditionValue($(this).val());
            });
            $("#cmbCondition").val('').trigger('change');

            initColumns(true);
            $("#columnDetailContainer").show();
            $("#orderColumnContainer").removeClass("col-md-6").addClass("col-md-3");

            msg.show();
            switch (value)
            {
                case NodeInherit.Draft:
                    msg.html(Resources.DraftInheritMessage);
                    break;
                case NodeInherit.Inbox:
                    msg.html(Resources.InboxInheritMessage);
                    break;
                case NodeInherit.Completed:
                    msg.html(Resources.CompletedInheritMessage);
                    break;
                case NodeInherit.MyRequests:
                    msg.html(Resources.MyRequestsInheritMessage);
                    break;
                case NodeInherit.Sent:
                    msg.html(Resources.SentInheritMessage);
                    break;
                case NodeInherit.Closed:
                    msg.html(Resources.ClosedInheritMessage);
                    break;
            }
        } else
        {
            makeCustom();
        }
    }
}
var gColumnTable;
function initColumns(enableColumnDetail, firstInit)
{
    Common.gridCommon();
    if (!firstInit)
    {
        $('#columnsTable').DataTable().destroy();
    }
    gColumnTable = $('#columnsTable').DataTable({
        dom: 't',
        destroy: true,
        paging: false,
        ordering: false,
        columns: [
            {
                title: Resources.Name,
                "orderable": false,
                "render": function (data, type, row)
                {
                    var name = row.name;
                    var translated = Resources[row.name];
                    if (translated)
                    {
                        name = translated;
                    }
                    if (row.isCustom)
                    {
                        return "<span class='text-danger'>" + Resources.Custom + " : (" + Resources.Name + ": " + name + " , " + Resources.JsFunctionName + ": " + row.customFunctionName + ")</span>";
                    }
                    return name;
                }
            },
            {
                title: Resources.Order,
                'width': '100px',
                "orderable": false,
                "render": function (data, type, row)
                {
                    return row.order;
                }
            },
            {
                title: Resources.ColumnDetail,
                visible: enableColumnDetail,
                'width': '100px',
                "orderable": false,
                "render": function (data, type, row)
                {
                    if (row.isColumnDetail !== "" && row.isColumnDetail !== null && row.isColumnDetail === true)
                    {
                        return '<em class="fa success fa-check"></em>';
                    }
                    return '<em class="fa danger fa-close"></em>';
                }
            },
            {
                "autoWidth": false,
                "bAutoWidth": false,
                'width': '10px',
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta)
                {
                    let btnedit = document.createElement("button");
                    btnedit.setAttribute("class", "btn btn-xs btn-primary");
                    btnedit.setAttribute("title", Resources.Edit);
                    btnedit.setAttribute("type", "button");
                    btnedit.setAttribute("action", "edit");
                    btnedit.innerHTML = "<i class='fa fa-edit fa-white'/>";
                    return btnedit.outerHTML;
                }
            },
            {
                "autoWidth": false,
                "bAutoWidth": false,
                'width': '10px',
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta)
                {
                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-danger");
                    btn.setAttribute("title", Resources.Delete);
                    btn.setAttribute("type", "button");
                    btn.setAttribute("action", "delete");
                    btn.innerHTML = "<i class='fa fa-trash-o'/>";
                    return btn.outerHTML;
                }
            }
        ]
    });
    $('#addColumn').off('click').on('click', function () {
        var $form = $('#formPostNode');
        $form.parsley().reset({ group: 'columns' });
        var isValid = $form.parsley().validate({ group: 'columns' });

        if (isValid) {
            var columns = getColumns(gColumnTable);
            var name = $("#cmbColumn").val();
            var order = $("#txtColumnOrder").val();

            var isNewColumnDetail = $("#chkColumnDetail").prop("checked");

            if (name !== '') {
                if ($(this).attr('action') === 'addRow') {
                    // Check if the name already exists
                    var item = $.grep(columns, function (item) {
                        return item.name === name;
                    });

                    // Check if the order already exists

                    var orderExists = [];
                    if (order && order.trim() !== "") {
                        orderExists = $.grep(columns, function (item) {
                            return item.order == order && item.isColumnDetail === isNewColumnDetail;
                        });
                    }
                    //var orderExists = $.grep(columns, function (item) {
                    //    return item.order === order;
                    //});

                    
                    if (item.length) {
                        Common.alertMsg(Resources.AlreadyExists); 
                    } else if (orderExists.length) {
                        Common.alertMsg(Resources.OrderAlreadyExists); 
                    } else {
                        var json = {};
                        if (name === "Custom") {
                            json.name = $("#txtColumnName").val();
                            json.customFunctionName = $("#txtColumnFunction").val();
                            json.isCustom = true;
                        } else {
                            json.name = name;
                        }
                        json.order = order;
                        json.isColumnDetail = $("#chkColumnDetail").prop("checked");

                        // Add the new row to the table
                        gColumnTable.row.add(json).draw();

                        // Reset form inputs
                        $('#addColumn').attr('action', 'addRow');
                        $('#cmbColumn').attr('required', 'required');
                        $('#cmbColumn').val("").trigger("change");
                        $('#txtColumnOrder').val("");
                        $("#chkColumnDetail").prop("checked", false);
                    } 
                    //else {
                    //    // Show appropriate error messages
                    //    if (item.length) {
                    //        Common.alertMsg(Resources.AlreadyExists); // Name already exists
                    //    }
                    //    if (orderExists.length) {
                    //        Common.alertMsg(Resources.OrderAlreadyExists); // Order already exists
                    //    }
                    //}
                }
            }

            if ($(this).attr('action') === 'confirmEdit') {
                const json = {};
                if (name === "Custom") {
                    json.name = $("#txtColumnName").val();
                    json.customFunctionName = $("#txtColumnFunction").val();
                    json.isCustom = true;
                } else {
                    json.name = name;
                }
                json.order = order;
                json.isColumnDetail = $("#chkColumnDetail").prop("checked");

                // Update the row in the table with the edited data
                gColumnTable.row($(this).attr('rowindex')).data(json).draw();

                // Reset form inputs
                $('#addColumn').attr('action', 'addRow');
                $('#cmbColumn').attr('required', 'required');
                $('#cmbColumn').val("").trigger("change");
                $('#txtColumnOrder').val("");
                $("#chkColumnDetail").prop("checked", false);
            }
        }
    });

    $('#columnsTable').on('click', 'tbody tr button[action="edit"]', function ()
    {
        const row = gColumnTable.row($(event.target).closest('tr'));
        $('#addColumn').attr('rowindex', row.index());
        $('#addColumn').attr('action', 'confirmEdit');
        var data = row.data();
        $('#cmbColumn').attr('required', 'required')
        if (data.isCustom === true)
        {
            $('#cmbColumn').val("Custom").trigger("change");
            $("#txtColumnName").val(data.name);
            $("#txtColumnFunction").val(data.customFunctionName);
        } else
        {
            $('#cmbColumn').val(data.name).trigger("change");
        }
        $('#txtColumnOrder').val(data.order);
        $("#chkColumnDetail").prop("checked", data.isColumnDetail);
    });
}
var gConditionTable;
function initConditions()
{
    Common.gridCommon();
    gConditionTable = $('#conditionsTable').DataTable({
        dom: 't',
        paging: false,
        ordering: false,
        columns: [
            {
                title: Resources.Name,
                "orderable": false,
                "render": function (data, type, row)
                {
                    var translated = Resources[row.name];
                    if (translated)
                    {
                        return translated;
                    }
                    return row.name;
                }
            },
            {
                title: Resources.Value,
                "orderable": false,
                "render": function (data, type, row)
                {
                    return NodeInheritProperties.renderConditionValue(row, row.name);
                }
            },
            {
                "autoWidth": false,
                "bAutoWidth": false,
                'width': '10px',
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta)
                {
                    let btnedit = document.createElement("button");
                    btnedit.setAttribute("class", "btn btn-xs btn-primary");
                    btnedit.setAttribute("title", Resources.Edit);
                    btnedit.setAttribute("type", "button");
                    btnedit.setAttribute("action", "edit");
                    btnedit.innerHTML = "<i class='fa fa-edit fa-white'/>";
                    return btnedit.outerHTML;
                }
            },
            {
                "autoWidth": false,
                "bAutoWidth": false,
                'width': '10px',
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta)
                {
                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-danger");
                    btn.setAttribute("title", Resources.Delete);
                    btn.setAttribute("type", "button");
                    btn.setAttribute("action", "delete");
                    btn.innerHTML = "<i class='fa fa-trash-o'/>";
                    return btn.outerHTML;
                }
            }
        ]
    });
    $('#addCondition').off('click').on('click', function ()
    {
        var $form = $('#formPostNode');
        $form.parsley().reset({ group: 'conditions' });
        var isValid = $form.parsley().validate({ group: 'conditions' });
        if (isValid)
        {
            var conditions = getConditions(gConditionTable);
            var name = $("#cmbCondition").val();
            if (name !== '')
            {
                if ($(this).attr('action') === 'addRow')
                {
                    var item = $.grep(conditions, function (item)
                    {
                        return item.name === name;
                    });

                    if (!item.length)
                    {
                        var json = {};
                        json.name = name;
                        NodeInheritProperties.getConditionValue(json, name);
                        gConditionTable.row.add(json).draw();
                        $('#addCondition').attr('action', 'addRow');
                        $('#cmbCondition').attr('required', 'required')
                        $('#cmbCondition').val("").trigger("change");
                    } else
                    {
                        Common.alertMsg(Resources.AlreadyExists);
                    }
                }
            }
            if ($(this).attr('action') === 'confirmEdit')
            {
                const json = {};
                json.name = name;
                NodeInheritProperties.getConditionValue(json, name);
                gConditionTable.row($(this).attr('rowindex')).data(json).draw();
                $('#addCondition').attr('action', 'addRow');
                $('#cmbCondition').attr('required', 'required')
                $('#cmbCondition').val("").trigger("change");
            }
        }
    });
    $('#conditionsTable').on('click', 'tbody tr button[action="delete"]', function (event)
    {
        gConditionTable.row($(event.target).closest('tr')).remove().draw();
        $('#addCondition').attr('action', 'addRow');
    });
    $('#conditionsTable').on('click', 'tbody tr button[action="edit"]', function ()
    {
        const row = gConditionTable.row($(event.target).closest('tr'));
        $('#addCondition').attr('rowindex', row.index());
        $('#addCondition').attr('action', 'confirmEdit');
        var data = row.data();
        $('#cmbCondition').attr('required', 'required')
        $('#cmbCondition').val(data.name).trigger("change");
        NodeInheritProperties.setConditionValue(data, data.name);
    });
}
function getColumns(datatable)
{
    var data = datatable.rows().data();
    var columns = [];
    data.each(function (value, index)
    {
        var jsonobj = {};
        jsonobj.name = value.name;
        jsonobj.order = value.order;
        jsonobj.isColumnDetail = value.isColumnDetail;
        jsonobj.customFunctionName = value.customFunctionName;
        jsonobj.isCustom = value.isCustom;
        columns.push(jsonobj);
    });
    return columns;
}
function getConditions(datatable)
{
    var data = datatable.rows().data();
    var conditions = [];
    data.each(function (value, index)
    {
        var jsonobj = {};
        jsonobj.name = value.name;
        jsonobj.value = value.value;
        jsonobj.isDate = value.isDate;
        jsonobj.sign = value.sign;
        conditions.push(jsonobj);
    });
    return conditions;
}
var iconsList = ["fa-500px", "fa-address-book", "fa-address-book-o", "fa-address-card", "fa-address-card-o", "fa-adjust", "fa-adn", "fa-align-center", "fa-align-justify", "fa-align-left", "fa-align-right", "fa-amazon", "fa-ambulance", "fa-american-sign-language-interpreting", "fa-anchor", "fa-android", "fa-angellist", "fa-angle-double-down", "fa-angle-double-left", "fa-angle-double-right", "fa-angle-double-up", "fa-angle-down", "fa-angle-left", "fa-angle-right", "fa-angle-up", "fa-apple", "fa-archive", "fa-area-chart", "fa-arrow-circle-down", "fa-arrow-circle-left", "fa-arrow-circle-o-down", "fa-arrow-circle-o-left", "fa-arrow-circle-o-right", "fa-arrow-circle-o-up", "fa-arrow-circle-right", "fa-arrow-circle-up", "fa-arrow-down", "fa-arrow-left", "fa-arrow-right", "fa-arrow-up", "fa-arrows", "fa-arrows-alt", "fa-arrows-h", "fa-arrows-v", "fa-asl-interpreting", "fa-assistive-listening-systems", "fa-asterisk", "fa-at", "fa-audio-description", "fa-automobile", "fa-backward", "fa-balance-scale", "fa-ban", "fa-bandcamp", "fa-bank", "fa-bar-chart", "fa-bar-chart-o", "fa-barcode", "fa-bars", "fa-bath", "fa-bathtub", "fa-battery", "fa-battery-0", "fa-battery-1", "fa-battery-2", "fa-battery-3", "fa-battery-4", "fa-battery-empty", "fa-battery-full", "fa-battery-half", "fa-battery-quarter", "fa-battery-three-quarters", "fa-bed", "fa-beer", "fa-behance", "fa-behance-square", "fa-bell", "fa-bell-o", "fa-bell-slash", "fa-bell-slash-o", "fa-bicycle", "fa-binoculars", "fa-birthday-cake", "fa-bitbucket", "fa-bitbucket-square", "fa-bitcoin", "fa-black-tie", "fa-blind", "fa-bluetooth", "fa-bluetooth-b", "fa-bold", "fa-bolt", "fa-bomb", "fa-book", "fa-bookmark", "fa-bookmark-o", "fa-braille", "fa-briefcase", "fa-btc", "fa-bug", "fa-building", "fa-building-o", "fa-bullhorn", "fa-bullseye", "fa-bus", "fa-buysellads", "fa-cab", "fa-calculator", "fa-calendar", "fa-calendar-check-o", "fa-calendar-minus-o", "fa-calendar-o", "fa-calendar-plus-o", "fa-calendar-times-o", "fa-camera", "fa-camera-retro", "fa-car", "fa-caret-down", "fa-caret-left", "fa-caret-right", "fa-caret-square-o-down", "fa-caret-square-o-left", "fa-caret-square-o-right", "fa-caret-square-o-up", "fa-caret-up", "fa-cart-arrow-down", "fa-cart-plus", "fa-cc", "fa-cc-amex", "fa-cc-diners-club", "fa-cc-discover", "fa-cc-jcb", "fa-cc-mastercard", "fa-cc-paypal", "fa-cc-stripe", "fa-cc-visa", "fa-certificate", "fa-chain", "fa-chain-broken", "fa-check", "fa-check-circle", "fa-check-circle-o", "fa-check-square", "fa-check-square-o", "fa-chevron-circle-down", "fa-chevron-circle-left", "fa-chevron-circle-right", "fa-chevron-circle-up", "fa-chevron-down", "fa-chevron-left", "fa-chevron-right", "fa-chevron-up", "fa-child", "fa-chrome", "fa-circle", "fa-circle-o", "fa-circle-o-notch", "fa-circle-thin", "fa-clipboard", "fa-clock-o", "fa-clone", "fa-close", "fa-cloud", "fa-cloud-download", "fa-cloud-upload", "fa-cny", "fa-code", "fa-code-fork", "fa-codepen", "fa-codiepie", "fa-coffee", "fa-cog", "fa-cogs", "fa-columns", "fa-comment", "fa-comment-o", "fa-commenting", "fa-commenting-o", "fa-comments", "fa-comments-o", "fa-compass", "fa-compress", "fa-connectdevelop", "fa-contao", "fa-copy", "fa-copyright", "fa-creative-commons", "fa-credit-card", "fa-credit-card-alt", "fa-crop", "fa-crosshairs", "fa-css3", "fa-cube", "fa-cubes", "fa-cut", "fa-cutlery", "fa-dashboard", "fa-dashcube", "fa-database", "fa-deaf", "fa-deafness", "fa-dedent", "fa-delicious", "fa-desktop", "fa-deviantart", "fa-diamond", "fa-digg", "fa-dollar", "fa-dot-circle-o", "fa-download", "fa-dribbble", "fa-drivers-license", "fa-drivers-license-o", "fa-dropbox", "fa-drupal", "fa-edge", "fa-edit", "fa-eercast", "fa-eject", "fa-ellipsis-h", "fa-ellipsis-v", "fa-empire", "fa-envelope", "fa-envelope-o", "fa-envelope-open", "fa-envelope-open-o", "fa-envelope-square", "fa-envira", "fa-eraser", "fa-etsy", "fa-eur", "fa-euro", "fa-exchange", "fa-exclamation", "fa-exclamation-circle", "fa-exclamation-triangle", "fa-expand", "fa-expeditedssl", "fa-external-link", "fa-external-link-square", "fa-eye", "fa-eye-slash", "fa-eyedropper", "fa-fa", "fa-facebook", "fa-facebook-f", "fa-facebook-official", "fa-facebook-square", "fa-fast-backward", "fa-fast-forward", "fa-fax", "fa-feed", "fa-female", "fa-fighter-jet", "fa-file", "fa-file-archive-o", "fa-file-audio-o", "fa-file-code-o", "fa-file-excel-o", "fa-file-image-o", "fa-file-movie-o", "fa-file-o", "fa-file-pdf-o", "fa-file-photo-o", "fa-file-picture-o", "fa-file-powerpoint-o", "fa-file-sound-o", "fa-file-text", "fa-file-text-o", "fa-file-video-o", "fa-file-word-o", "fa-file-zip-o", "fa-files-o", "fa-film", "fa-filter", "fa-fire", "fa-fire-extinguisher", "fa-firefox", "fa-first-order", "fa-flag", "fa-flag-checkered", "fa-flag-o", "fa-flash", "fa-flask", "fa-flickr", "fa-floppy-o", "fa-folder", "fa-folder-o", "fa-folder-open", "fa-folder-open-o", "fa-font", "fa-font-awesome", "fa-fonticons", "fa-fort-awesome", "fa-forumbee", "fa-forward", "fa-foursquare", "fa-free-code-camp", "fa-frown-o", "fa-futbol-o", "fa-gamepad", "fa-gavel", "fa-gbp", "fa-ge", "fa-gear", "fa-gears", "fa-genderless", "fa-get-pocket", "fa-gg", "fa-gg-circle", "fa-gift", "fa-git", "fa-git-square", "fa-github", "fa-github-alt", "fa-github-square", "fa-gitlab", "fa-gittip", "fa-glass", "fa-glide", "fa-glide-g", "fa-globe", "fa-google", "fa-google-plus", "fa-google-plus-circle", "fa-google-plus-official", "fa-google-plus-square", "fa-google-wallet", "fa-graduation-cap", "fa-gratipay", "fa-grav", "fa-group", "fa-h-square", "fa-hacker-news", "fa-hand-grab-o", "fa-hand-lizard-o", "fa-hand-o-down", "fa-hand-o-left", "fa-hand-o-right", "fa-hand-o-up", "fa-hand-paper-o", "fa-hand-peace-o", "fa-hand-pointer-o", "fa-hand-rock-o", "fa-hand-scissors-o", "fa-hand-spock-o", "fa-hand-stop-o", "fa-handshake-o", "fa-hard-of-hearing", "fa-hashtag", "fa-hdd-o", "fa-header", "fa-headphones", "fa-heart", "fa-heart-o", "fa-heartbeat", "fa-history", "fa-home", "fa-hospital-o", "fa-hotel", "fa-hourglass", "fa-hourglass-1", "fa-hourglass-2", "fa-hourglass-3", "fa-hourglass-end", "fa-hourglass-half", "fa-hourglass-o", "fa-hourglass-start", "fa-houzz", "fa-html5", "fa-i-cursor", "fa-id-badge", "fa-id-card", "fa-id-card-o", "fa-ils", "fa-image", "fa-imdb", "fa-inbox", "fa-indent", "fa-industry", "fa-info", "fa-info-circle", "fa-inr", "fa-instagram", "fa-institution", "fa-internet-explorer", "fa-ioxhost", "fa-italic", "fa-joomla", "fa-jpy", "fa-jsfiddle", "fa-key", "fa-keyboard-o", "fa-krw", "fa-language", "fa-laptop", "fa-lastfm", "fa-lastfm-square", "fa-leaf", "fa-leanpub", "fa-legal", "fa-lemon-o", "fa-level-down", "fa-level-up", "fa-life-bouy", "fa-life-buoy", "fa-life-ring", "fa-life-saver", "fa-lightbulb-o", "fa-line-chart", "fa-link", "fa-linkedin", "fa-linkedin-square", "fa-linode", "fa-linux", "fa-list", "fa-list-alt", "fa-list-ol", "fa-list-ul", "fa-location-arrow", "fa-lock", "fa-long-arrow-down", "fa-long-arrow-left", "fa-long-arrow-right", "fa-long-arrow-up", "fa-low-vision", "fa-magic", "fa-magnet", "fa-mail-forward", "fa-mail-reply", "fa-mail-reply-all", "fa-male", "fa-map", "fa-map-marker", "fa-map-o", "fa-map-pin", "fa-map-signs", "fa-maxcdn", "fa-meanpath", "fa-medium", "fa-medkit", "fa-meetup", "fa-meh-o", "fa-microchip", "fa-microphone", "fa-microphone-slash", "fa-minus", "fa-minus-circle", "fa-minus-square", "fa-minus-square-o", "fa-mixcloud", "fa-mobile", "fa-mobile-phone", "fa-modx", "fa-money", "fa-moon-o", "fa-mortar-board", "fa-motorcycle", "fa-mouse-pointer", "fa-music", "fa-navicon", "fa-neuter", "fa-newspaper-o", "fa-object-group", "fa-object-ungroup", "fa-odnoklassniki", "fa-odnoklassniki-square", "fa-opencart", "fa-openid", "fa-opera", "fa-optin-monster", "fa-outdent", "fa-pagelines", "fa-paint-brush", "fa-paper-plane", "fa-paper-plane-o", "fa-paperclip", "fa-paragraph", "fa-paste", "fa-pause", "fa-pause-circle", "fa-pause-circle-o", "fa-paw", "fa-paypal", "fa-pencil", "fa-pencil-square", "fa-pencil-square-o", "fa-percent", "fa-phone", "fa-phone-square", "fa-photo", "fa-picture-o", "fa-pie-chart", "fa-pied-piper", "fa-pied-piper-alt", "fa-pied-piper-pp", "fa-pinterest", "fa-pinterest-p", "fa-pinterest-square", "fa-plane", "fa-play", "fa-play-circle", "fa-play-circle-o", "fa-plug", "fa-plus", "fa-plus-circle", "fa-plus-square", "fa-plus-square-o", "fa-podcast", "fa-power-off", "fa-print", "fa-product-hunt", "fa-puzzle-piece", "fa-qq", "fa-qrcode", "fa-question", "fa-question-circle", "fa-question-circle-o", "fa-quora", "fa-quote-left", "fa-quote-right", "fa-ra", "fa-random", "fa-ravelry", "fa-rebel", "fa-recycle", "fa-reddit", "fa-reddit-alien", "fa-reddit-square", "fa-refresh", "fa-registered", "fa-remove", "fa-renren", "fa-reorder", "fa-repeat", "fa-reply", "fa-reply-all", "fa-resistance", "fa-retweet", "fa-rmb", "fa-road", "fa-rocket", "fa-rotate-left", "fa-rotate-right", "fa-rouble", "fa-rss", "fa-rss-square", "fa-rub", "fa-ruble", "fa-rupee", "fa-s15", "fa-safari", "fa-save", "fa-scissors", "fa-scribd", "fa-search", "fa-search-minus", "fa-search-plus", "fa-sellsy", "fa-send", "fa-send-o", "fa-server", "fa-share", "fa-share-alt", "fa-share-alt-square", "fa-share-square", "fa-share-square-o", "fa-shekel", "fa-sheqel", "fa-shield", "fa-ship", "fa-shirtsinbulk", "fa-shopping-bag", "fa-shopping-basket", "fa-shopping-cart", "fa-shower", "fa-sign-in", "fa-sign-language", "fa-sign-out", "fa-signal", "fa-signing", "fa-simplybuilt", "fa-sitemap", "fa-skyatlas", "fa-skype", "fa-slack", "fa-sliders", "fa-slideshare", "fa-smile-o", "fa-snapchat", "fa-snapchat-ghost", "fa-snapchat-square", "fa-snowflake-o", "fa-soccer-ball-o", "fa-sort", "fa-sort-alpha-asc", "fa-sort-alpha-desc", "fa-sort-amount-asc", "fa-sort-amount-desc", "fa-sort-asc", "fa-sort-desc", "fa-sort-down", "fa-sort-numeric-asc", "fa-sort-numeric-desc", "fa-sort-up", "fa-soundcloud", "fa-space-shuttle", "fa-spinner", "fa-spoon", "fa-spotify", "fa-square", "fa-square-o", "fa-stack-exchange", "fa-stack-overflow", "fa-star", "fa-star-half", "fa-star-half-empty", "fa-star-half-full", "fa-star-half-o", "fa-star-o", "fa-steam", "fa-steam-square", "fa-step-backward", "fa-step-forward", "fa-stethoscope", "fa-sticky-note", "fa-sticky-note-o", "fa-stop", "fa-stop-circle", "fa-stop-circle-o", "fa-street-view", "fa-strikethrough", "fa-stumbleupon", "fa-stumbleupon-circle", "fa-subscript", "fa-subway", "fa-suitcase", "fa-sun-o", "fa-superpowers", "fa-superscript", "fa-support", "fa-table", "fa-tablet", "fa-tachometer", "fa-tag", "fa-tags", "fa-tasks", "fa-taxi", "fa-telegram", "fa-television", "fa-tencent-weibo", "fa-terminal", "fa-text-height", "fa-text-width", "fa-th", "fa-th-large", "fa-th-list", "fa-themeisle", "fa-thermometer", "fa-thermometer-0", "fa-thermometer-1", "fa-thermometer-2", "fa-thermometer-3", "fa-thermometer-4", "fa-thermometer-empty", "fa-thermometer-full", "fa-thermometer-half", "fa-thermometer-quarter", "fa-thermometer-three-quarters", "fa-thumb-tack", "fa-thumbs-down", "fa-thumbs-o-down", "fa-thumbs-o-up", "fa-thumbs-up", "fa-ticket", "fa-times", "fa-times-circle", "fa-times-circle-o", "fa-times-rectangle", "fa-times-rectangle-o", "fa-tint", "fa-toggle-down", "fa-toggle-left", "fa-toggle-off", "fa-toggle-on", "fa-toggle-right", "fa-toggle-up", "fa-trademark", "fa-train", "fa-trash", "fa-trash-o", "fa-tree", "fa-trello", "fa-tripadvisor", "fa-trophy", "fa-truck", "fa-try", "fa-tty", "fa-tumblr", "fa-tumblr-square", "fa-turkish-lira", "fa-tv", "fa-twitch", "fa-twitter", "fa-twitter-square", "fa-umbrella", "fa-underline", "fa-undo", "fa-universal-access", "fa-university", "fa-unlink", "fa-unlock", "fa-unlock-alt", "fa-unsorted", "fa-upload", "fa-usb", "fa-usd", "fa-user", "fa-user-circle", "fa-user-circle-o", "fa-user-md", "fa-user-o", "fa-user-plus", "fa-user-secret", "fa-user-times", "fa-users", "fa-vcard", "fa-vcard-o", "fa-viacoin", "fa-viadeo", "fa-viadeo-square", "fa-video-camera", "fa-vimeo", "fa-vimeo-square", "fa-vine", "fa-vk", "fa-volume-control-phone", "fa-volume-down", "fa-volume-off", "fa-volume-up", "fa-warning", "fa-wechat", "fa-weibo", "fa-weixin", "fa-whatsapp", "fa-wheelchair", "fa-wheelchair-alt", "fa-wifi", "fa-wikipedia-w", "fa-window-close", "fa-window-close-o", "fa-window-maximize", "fa-window-minimize", "fa-window-restore", "fa-windows", "fa-won", "fa-wordpress", "fa-wpbeginner", "fa-wpexplorer", "fa-wpforms", "fa-wrench", "fa-xing", "fa-xing-square", "fa-y-combinator", "fa-y-combinator-square", "fa-yahoo", "fa-yc", "fa-yc-square", "fa-yelp", "fa-yen", "fa-yoast", "fa-youtube", "fa-youtube-play", "fa-youtube-square"];
class NodeIndexView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "nodeindex", model);
    }
    render()
    {
        $('#btnSubmit').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#addCondition').focus();
                }
                else
                {
                    $('#txtName').focus();
                }
            }
        });
        $.fn.select2.defaults.set("theme", "bootstrap");
        $('#txtName').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnSubmit').focus();
                }
                else
                {
                    $('#txtOrder').focus();
                }
            }
        });
        $('#formPostNode').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                e.preventDefault();
                $('#btnSubmit').trigger("click");
            }
        });
        var options = new Array();
        iconsList.forEach(function (icon, index)
        {
            options.push({
                id: 'fa ' + icon,
                text: '<i class="fa fa-fw ' + icon + '"></i> fa ' + icon
            });
        });
        $('#cmbIcon').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            data: options,
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#iconContainer'),
            escapeMarkup: function (markup)
            {
                return markup;
            }
        }).on('change', function ()
        {
            $(this).trigger('input');
        });
        $("#cmbIcon").val('').trigger('change');
        $('#cmbFilters').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: Resources.SelectFilters,
            multiple: "multiple",
            dropdownParent: $('#filtersContainer')
        }).on("select2:select", function (evt)
        {
            var element = evt.params.data.element;
            var $element = $(element);
            $element.detach();
            $(this).append($element);
            $(this).trigger("change");
        });
        $("#cmbFilters").val('').trigger('change');
        $('#cmbColumn').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: Resources.SelectColumn + " *",
            dropdownParent: $('#columnContainer'),
            templateResult: function (data)
            {
                if (data.id === "Custom")
                {
                    return $("<span class='text-danger'>" + data.text + "</span>");
                }
                return data.text;
            }
        });
        $("#cmbColumn").val('').trigger('change');
        $('#cmbCondition').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: Resources.SelectCondition + " *",
            dropdownParent: $('#conditionContainer')
        });
        $("#cmbCondition").val('').trigger('change');
        $('#cmbInherit').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#inheritContainer')
        }).on('change', function ()
        {
            if ($("#cmbInherit").val() === NodeInherit.Inbox || $("#cmbInherit").val() == NodeInherit.Custom)
                $('#unread').show();
            else
                $('#unread').hide();
            $(this).trigger('input');
            changeInheritProperties($(this).val());
        });
        $('#cmbInherit').val("").trigger("change");
        $('#cmbRoles').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            placeholder: "",
            allowClear: false,
            multiple: "multiple",
            dropdownParent: $('#rolesContainer')
        });
        $('#cmbRoles').val("").trigger("change");
        $('#btnSubmit').on('click', function ()
        {
            var $form = $('#formPostNode');
            $form.parsley().reset();
            var isValid = $form.parsley().validate({ group: 'primary' });
            if (isValid)
            {
                let name = $("#txtName").val();
                let order = $("#txtOrder").val();
                let parentId = $("#hdParentId").val();
                let roleIds = $("#cmbRoles").val() || [];
                let params = {
                    'Name': name,
                    'Order': order || "1",
                    'ParentNodeId': parentId,
                    'RoleIds': roleIds,
                    'Visible': $("#chkVisible").prop('checked'),
                    'Expand': $("#chkExpand").prop('checked'),
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                };
                if (document.getElementById('hdId').value)
                {
                    params.Id = document.getElementById('hdId').value;
                }
                if (parentId === "")
                {
                    params.Icon = $("#cmbIcon").val();
                }
                var inherit = $("#cmbInherit").val();
                params.Inherit = inherit;
                params.EnableTotalCount = $("#chkEnableTotalCount").prop('checked');
                params.EnableTodayCount = $("#chkEnableTodayCount").prop('checked');
                params.EnableUnreadCount = $("#chkEnableUnreadCount").prop('checked');
                if (inherit !== NodeInherit.Custom)
                {
                    var filters = $("#cmbFilters").val();
                    params.Filters = filters ? JSON.stringify(filters) : "";
                    var columns = getColumns(gColumnTable);
                    params.Columns = columns ? JSON.stringify(columns) : "";
                    var conditions = getConditions(gConditionTable);
                    params.Conditions = conditions ? JSON.stringify(conditions) : "";
                } else
                {
                    params.CustomFunctions = $("#txtJsFunctionName").val() + window.Splitter + $("#txtTotalCountJsFunctionName").val() + window.Splitter + $("#txtTodayCountJsFunctionName").val() + window.Splitter + $("#txtUnreadCountJsFunctionName").val();
                }
                var btn = $('#btnSubmit');
                btn.button('loading');
                var btnClose = $('#btnClose');
                btnClose.attr('disabled', 'disabled');
                Common.ajaxPost('/Node/Save', params, function (data)
                {
                    if (data === "NameAlreadyExist")
                    {
                        Common.alertMsg(Resources.NameAlreadyExists);
                    } else if (data === "OrderAlreadyExists" )
                    { 
                        Common.alertMsg(Resources.OrderAlreadyExists);


                    }
                    else
                    {
                        var children = [];
                        var name = Common.translate(data.name);
                        if (document.getElementById('hdId').value)
                        {
                            var fullNode = $('#jstree').jstree().get_node(data.id);
                            fullNode.name = data.name;
                            fullNode.data = data;
                            $('#jstree').jstree().redraw_node(fullNode);
                            $('#jstree').jstree().rename_node(fullNode, name);
                        } else
                        {
                            var parentNodeId = parentId !== "" ? parentId : "0"
                            let newNode = {
                                'children': children,
                                'data': data,
                                'id': data.id,
                                'parentId': parentNodeId,
                                'state': { opened: true, disabled: false, selected: false },
                                'text': name
                            };
                            $('#jstree').jstree().create_node(parentNodeId, newNode, "last", null);
                        }
                        Common.showScreenSuccessMsg();
                        document.getElementById('hdId').value = data.id;
                        $('#panelNodeTitle').html(data.name + " " + "(" + Resources.Edit + ")");
                    }
                    btn.button('reset');
                    btnClose.removeAttr('disabled');
                }, function () { btn.button('reset'); btnClose.removeAttr('disabled'); Common.showScreenErrorMsg(); }, false);
            }
        });
        $('#chkParent').change(function ()
        {
            if (!this.checked)
            {
                makeNotParent();
            } else
            {
                makeParent();
            }
        });
        $('#chkAllFilters').on('click', function ()
        {
            $("#cmbFilters").select2('destroy').find('option').prop('selected', 'selected').end().select2();
        });
        $('#chkEnableTodayCount').change(function ()
        {
            if ($("#cmbInherit").val() === NodeInherit.Custom)
            {
                if (!this.checked)
                {
                    $("#txtTodayCountJsFunctionName").removeAttr("required");
                    $("#txtTodayCountJsFunctionName").removeAttr("data-parsley-group");
                    $("#txtTodayCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
                } else
                {
                    $("#txtTodayCountJsFunctionName").attr("required", "required");
                    $("#txtTodayCountJsFunctionName").attr("data-parsley-group", "primary");
                    $("#txtTodayCountJsFunctionName").parent().find(".control-label").addClass("field-required");
                }
            }
        });
        $('#chkEnableTotalCount').change(function ()
        {
            if ($("#cmbInherit").val() === NodeInherit.Custom)
            {
                if (!this.checked)
                {
                    $("#txtTotalCountJsFunctionName").removeAttr("required");
                    $("#txtTotalCountJsFunctionName").removeAttr("data-parsley-group");
                    $("#txtTotalCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
                } else
                {
                    $("#txtTotalCountJsFunctionName").attr("required", "required");
                    $("#txtTotalCountJsFunctionName").attr("data-parsley-group", "primary");
                    $("#txtTotalCountJsFunctionName").parent().find(".control-label").addClass("field-required");
                }
            }
        });

        $('#chkEnableUnreadCount').change(function () {
            if ($("#cmbInherit").val() === NodeInherit.Custom) {
                if (!this.checked) {
                    $("#txtUnreadCountJsFunctionName").removeAttr("required");
                    $("#txtUnreadCountJsFunctionName").removeAttr("data-parsley-group");
                    $("#txtUnreadCountJsFunctionName").parent().find(".control-label").removeClass("field-required");
                } else {
                    $("#txtUnreadCountJsFunctionName").attr("required", "required");
                    $("#txtUnreadCountJsFunctionName").attr("data-parsley-group", "primary");
                    $("#txtUnreadCountJsFunctionName").parent().find(".control-label").addClass("field-required");
                }
            }
        });


        $('#chkAllRoles').on('click', function ()
        {
            $("#cmbRoles").select2('destroy').find('option').prop('selected', 'selected').end().select2();
        });

        initColumns(false, true);
        initConditions();

        $('#columnsTable').on('click', 'tbody tr button[action="delete"]', function (event) {
            gColumnTable.row($(event.target).closest('tr')).remove().draw();
            $('#addColumn').attr('action', 'addRow');
        });
    }
}
export default { NodeIndex, NodeIndexView };
