﻿<div id="modalExportOptions" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close ExportOptionsModalClose">&times;</button>
                <h4 id="modalExportOptionsTitle" class="modal-title">
                    {{#if fromResend}} {{Localizer 'ResendOptions'}} {{else}} {{Localizer 'ExportOptions'}} {{/if}}
                </h4>
            </div>
            <div class="modal-body">
                <div class="row div-flex">
                    <div id="versionGridContainer" class="col-lg-12">
                        <div class="row">
                            <div class="col-lg-12 margin-5">
                                <div class="col-md-4 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="checkbox c-checkbox margin-8">
                                                <label>
                                                    <input id="chkAttachments" tabindex="14" type="checkbox" name="Attachments" checked>
                                                    <span class="fa fa-check"></span>{{Localizer 'Attachments'}}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="checkbox c-checkbox margin-8">
                                                <label>
                                                    <input id="chkLinkedCorrespondences" tabindex="14" type="checkbox" name="LinkedCorrespondences" checked>
                                                    <span class="fa fa-check"></span>{{Localizer 'LinkedCorrespondences'}}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="checkbox c-checkbox margin-8">
                                                <label>
                                                    <input id="chkNotes" tabindex="14" type="checkbox" name="Notes" checked>
                                                    <span class="fa fa-check"></span>{{Localizer 'Notes'}}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--<div class="col-md-4 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <div class="checkbox c-checkbox margin-8">
                                                <label>
                                                    <input id="chkNonArchivedAttachments" tabindex="14" type="checkbox" name="NonArchivedAttachments" checked>
                                                    <span class="fa fa-check"></span>{{Localizer 'NonArchivedAttachments'}}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>-->
                            </div>
                        </div>
                        <div class="table-responsive glyphicon-container-color">
                            <table id="grdExportOptions" ref="grdExportOptions" class="table table-striped table-hover" style="width:100%"></table>
                        </div>
                    </div>

                </div>
            </div>
            <div class="modal-footer">
                <button id="_btnExportOptionsSave" type="button" tabindex="2" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin'></i> {{Localizer 'Saving'}}">
                    {{#if fromResend}} {{Localizer 'Resend'}} {{else}} {{Localizer 'Export'}} {{/if}}
                </button>
                <button id="btnExportOptionsClose" type="button" class="btn btn-default">{{Localizer 'Close'}}</button>
            </div>
        </div>
    </div>
</div>
