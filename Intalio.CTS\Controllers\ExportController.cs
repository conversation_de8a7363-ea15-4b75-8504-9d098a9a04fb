﻿using Aspose.Cells;
using Intalio.Core;
using Intalio.Core.DAL;


//using Aspose.Pdf;
using Intalio.Core.Utility;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using MathNet.Numerics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Drawing;
using System.IO;
using System.Linq;

namespace Intalio.CTS.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class ExportController : BaseController

    {

        #region Ajax
        public IActionResult ExportActivityLogGrid([FromBody] ExportActivityLogGridTableViewModel model )
        {
            ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
            if (model.ActivityLogActionId != "0")
            {
                filter.Add("ActivityLogActionId", Convert.ToInt32(model.ActivityLogActionId), Operator.Equals);
            }
            if (model.DocumentId != default)
            {
                filter.Add("Document.id", Convert.ToInt32(model.DocumentId), Operator.Equals);
            }
            List<SortExpression> sorting = new List<SortExpression>();
            string column = model.OrderColumn;
            string sort = model.OrderDir;
            if (!string.IsNullOrEmpty(column))
            {
                SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                
               
                    switch (column)
                    {
                      
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    case "action":
                        sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "ActivityLogAction.Name" });
                        break;
                    default:
                        sorting.Add(new SortExpression { Order = sortDirection, PropertyName = column });
                        break;

                }
                
            }
            else
            {
                //check if Inbox Mode in parameters is Inbox Default With Grouping so not need to set any sorting 
                if (Core.Configuration.InboxMode == "InboxDefaultWithGrouping")
                    sorting = null;
                else
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
            }
            var retValue = ManageActivityLog.ListActivityLogByDocumentId(0, 0, UserId,StructureId, StructureIds, IsStructureReceiver, PrivacyLevel, Convert.ToInt32(model.DocumentId), Convert.ToInt64(model.DelegationId), filter, sorting, Language);

            AsposeLicense asposeLicense = new AsposeLicense();
            Stream licenseStream = asposeLicense.Get();
            if (licenseStream != null)
            {
                License license = new License();
                license.SetLicense(licenseStream);
            }
            else
            {
                return BadRequest("Aspose license is not available.");
            }

            Workbook workbook = new Workbook();
            Worksheet sheet = workbook.Worksheets[0];
            int labelCol = Language == Language.AR ? 2 : 1;
            int valueCol = Language == Language.AR ? 1 : 2;
            // Determine language and set RTL if Arabic


            if (Language == Language.AR)
            {


                 // Align text to the right for Arabic
                 Style style = sheet.Cells.Columns[0].Style;
                style.TextDirection = TextDirectionType.RightToLeft;

                style.HorizontalAlignment = TextAlignmentType.Right;
                sheet.Cells.Columns[0].ApplyStyle(style, new StyleFlag { HorizontalAlignment = true });
            }
            sheet.Cells[0, labelCol].PutValue(TranslationUtility.Translate("ReferenceNumber", Language));
            sheet.Cells[1, labelCol].PutValue(TranslationUtility.Translate("Subject", Language));
            sheet.Cells[2, labelCol].PutValue(TranslationUtility.Translate("User", Language));
            sheet.Cells[3, labelCol].PutValue(TranslationUtility.Translate("Structure", Language));
            sheet.Cells[4, labelCol].PutValue(TranslationUtility.Translate("Date", Language));
        
            Dictionary<string, int> colMap = new Dictionary<string, int>();

            if (Language == Language.AR)
            {
                colMap["Note"] = 0;
                colMap["CreatedDate"] = 1;
                colMap["Action"] = 2;
                colMap["User"] = 3;
            }
            else
            {
                colMap["User"] = 0;
                colMap["Action"] = 1;
                colMap["CreatedDate"] = 2;
                colMap["Note"] = 3;
            }

            // Set headers at row 9 using the mapping
            sheet.Cells[9, colMap["User"]].PutValue(TranslationUtility.Translate("User", Language));
            sheet.Cells[9, colMap["Action"]].PutValue(TranslationUtility.Translate("Action", Language));
            sheet.Cells[9, colMap["CreatedDate"]].PutValue(TranslationUtility.Translate("CreatedDate", Language));
            sheet.Cells[9, colMap["Note"]].PutValue(TranslationUtility.Translate("Note", Language));
            // Style for headers
            Style headerStyle = sheet.Cells[0, 0].GetStyle();
            headerStyle.Font.IsBold = true;
            headerStyle.Font.Size = 25; // Larger font size
            headerStyle.ForegroundColor = Color.Gray;


            headerStyle.Font.Color = Color.White;
            headerStyle.Pattern = BackgroundType.Solid;
            headerStyle.HorizontalAlignment = TextAlignmentType.Center; // Center text horizontally
            headerStyle.VerticalAlignment = TextAlignmentType.Center; // Center text vertically
            
            Style headerStyle2 = sheet.Cells[0, 0].GetStyle();
            headerStyle2.Font.Size = 16; // Larger font size
            headerStyle2.ForegroundColor = Color.White;
            headerStyle2.Font.Color = Color.Black;
            headerStyle2.Pattern = BackgroundType.Solid;
            headerStyle2.HorizontalAlignment = TextAlignmentType.Center; // Center text horizontally
            headerStyle2.VerticalAlignment = TextAlignmentType.Center; // Center text vertically
            headerStyle2.Borders[BorderType.BottomBorder].LineStyle = CellBorderType.Thin;
            headerStyle2.Borders[BorderType.BottomBorder].Color = Color.Gray;
            headerStyle2.Font.Color = Color.Black ;
            headerStyle2.VerticalAlignment = TextAlignmentType.Center; // Center text vertically
            headerStyle2.HorizontalAlignment = TextAlignmentType.Center; // Center text horizontally
            headerStyle2.ForegroundColor = Color.FromArgb(240, 240, 240);
            headerStyle2.Pattern = BackgroundType.Solid;

            Style headerStyle3 = sheet.Cells[0, 0].GetStyle();
            headerStyle3.Font.Size = 16; // Larger font size
            headerStyle3.ForegroundColor = Color.White;
            headerStyle3.Font.Color = Color.Black;
            headerStyle3.Pattern = BackgroundType.Solid;
            headerStyle3.HorizontalAlignment = TextAlignmentType.Center; // Center text horizontally
            headerStyle3.VerticalAlignment = TextAlignmentType.Center; // Center text vertically
            headerStyle3.Borders[BorderType.BottomBorder].LineStyle = CellBorderType.Thin;
            headerStyle3.Borders[BorderType.BottomBorder].Color = Color.Gray;
            headerStyle3.Font.Color = Color.Black;
            headerStyle3.VerticalAlignment = TextAlignmentType.Center; // Center text vertically
            headerStyle3.HorizontalAlignment = TextAlignmentType.Center; // Center text horizontally
            headerStyle3.ForegroundColor = Color.White;
            headerStyle3.Pattern = BackgroundType.Solid;
        

            // Apply header style to all header cells
            sheet.Cells[0, 1].SetStyle(headerStyle2);
            sheet.Cells[0, 2].SetStyle(headerStyle2);
            sheet.Cells[1, 1].SetStyle(headerStyle3);
            sheet.Cells[1, 2].SetStyle(headerStyle3);
            sheet.Cells[2, 1].SetStyle(headerStyle2);
            sheet.Cells[2, 2].SetStyle(headerStyle2);
            sheet.Cells[3, 1].SetStyle(headerStyle3);
            sheet.Cells[3, 2].SetStyle(headerStyle3);
            sheet.Cells[4, 1].SetStyle(headerStyle2);
            sheet.Cells[4, 2].SetStyle(headerStyle2);
            
            sheet.Cells[9, 0].SetStyle(headerStyle);
            sheet.Cells[9, 1].SetStyle(headerStyle);
            sheet.Cells[9, 2].SetStyle(headerStyle);
            sheet.Cells[9, 3].SetStyle(headerStyle);

            // Set column widths
            sheet.Cells.Columns[0].Width = 60; 
            sheet.Cells.Columns[1].Width = 60; 
            sheet.Cells.Columns[2].Width = 60;
            sheet.Cells.Columns[3].Width = 60;
            sheet.Cells.Columns[4].Width = 60;

            sheet.Cells.Columns[5].Width = 80; 
            sheet.Cells.Columns[6].Width = 80;
            sheet.Cells.Columns[7].Width = 80; 
            sheet.Cells.Columns[8].Width = 80; 

            sheet.Cells.Rows[0].Height = 35; 
            sheet.Cells.Rows[1].Height = 100; 
            sheet.Cells.Rows[2].Height = 35; 
            sheet.Cells.Rows[3].Height = 100; 
            sheet.Cells.Rows[4].Height = 35; 

            sheet.Cells.Rows[9].Height = 60; 

            Style dataStyle = workbook.CreateStyle();
            dataStyle.Font.Size = 16; 
            dataStyle.IsTextWrapped = true; 
            dataStyle.Borders[BorderType.LeftBorder].LineStyle = CellBorderType.Thin;
            dataStyle.Borders[BorderType.RightBorder].LineStyle = CellBorderType.Thin;
            dataStyle.Borders[BorderType.TopBorder].LineStyle = CellBorderType.Thin;
            dataStyle.Borders[BorderType.BottomBorder].LineStyle = CellBorderType.Thin;
            dataStyle.Borders[BorderType.BottomBorder].Color = System.Drawing.Color.FromArgb(255, 45, 65, 84);
            dataStyle.Borders[BorderType.TopBorder].Color = System.Drawing.Color.FromArgb(255, 45, 65, 84);
            dataStyle.Borders[BorderType.RightBorder].Color = System.Drawing.Color.FromArgb(255, 45, 65, 84);
            dataStyle.Borders[BorderType.LeftBorder].Color = System.Drawing.Color.FromArgb(255, 45, 65, 84);
            dataStyle.VerticalAlignment = TextAlignmentType.Center; 
            dataStyle.HorizontalAlignment = TextAlignmentType.Center; 

            
            if (retValue.Item2.Count > 0)
            {


                var StructureName = ManageStructure.GetStructureNameById(StructureId);
                var UserName = ManageUser.GetUserName(UserId, Language);
                var userName = UserName.FirstName + " " + UserName.LastName;

                Style refNumberStyle = sheet.Cells[0, valueCol].GetStyle();
                refNumberStyle.Font.Size = 16;
                sheet.Cells[0, valueCol].SetStyle(refNumberStyle);
                sheet.Cells[0, valueCol].PutValue(retValue.Item2[0].ReferenceNumber);

                Style subjectStyle = sheet.Cells[1, valueCol].GetStyle();
                subjectStyle.Font.Size = 16;
                subjectStyle.IsTextWrapped = true;
                sheet.Cells[1, valueCol].SetStyle(subjectStyle);
                sheet.Cells[1, valueCol].PutValue(retValue.Item2[0].Subject);

                Style userStyle = sheet.Cells[2, valueCol].GetStyle();
                userStyle.Font.Size = 16;
                userStyle.IsTextWrapped = true;
                sheet.Cells[2, valueCol].SetStyle(userStyle);
                sheet.Cells[2, valueCol].PutValue(userName);

                Style structureStyle = sheet.Cells[3, valueCol].GetStyle();
                structureStyle.Font.Size = 16;
                structureStyle.IsTextWrapped = true;
                sheet.Cells[3, valueCol].SetStyle(structureStyle);
                sheet.Cells[3, valueCol].PutValue(StructureName);

                Style dateStyle = sheet.Cells[4, valueCol].GetStyle();
                // Optional: customize dateStyle if needed
                sheet.Cells[4, valueCol].PutValue(DateTime.Now);

                // Modify the style for formatting
                dateStyle.Font.Size = 16; 
                dateStyle.HorizontalAlignment = TextAlignmentType.Center;
                dateStyle.VerticalAlignment = TextAlignmentType.Center;

                // Apply custom date format
                dateStyle.Custom = "dd/MM/yyyy HH:mm";

                dateStyle.Pattern = BackgroundType.Solid;
                dateStyle.ForegroundColor = Color.FromArgb(240, 240, 240);

                dateStyle.SetBorder(BorderType.BottomBorder, CellBorderType.Thin, Color.Gray);

                // Apply the style to the correct cell
                sheet.Cells[4, valueCol].SetStyle(dateStyle);

                var i = 9;
                int startRow = 10;

                for (int j = 0; j < retValue.Item2.Count; j++)
                {
                    
                    int rowIndex = startRow + j;

                    // Fill in the values
                    sheet.Cells[rowIndex, colMap["User"]].PutValue(retValue.Item2[j].User);
                    sheet.Cells[rowIndex, colMap["Action"]].PutValue(retValue.Item2[j].Action);
                    sheet.Cells[rowIndex, colMap["CreatedDate"]].PutValue(retValue.Item2[j].CreatedDate);
                    sheet.Cells[rowIndex, colMap["Note"]].PutValue(retValue.Item2[j].Note);

                    Color rowColor = (j % 2 == 0) ? Color.White : Color.FromArgb(240, 240, 240); // #f3f3f3

                    for (int col = 0; col < 4; col++) // 4 columns
                    {
                        dataStyle = sheet.Cells[i + 1, col].GetStyle();
                        dataStyle.Font.Size = 18;
                        dataStyle.IsTextWrapped = true;
                        dataStyle.Borders[BorderType.LeftBorder].LineStyle = CellBorderType.Thin;
                        dataStyle.Borders[BorderType.RightBorder].LineStyle = CellBorderType.Thin;
                        dataStyle.Borders[BorderType.TopBorder].LineStyle = CellBorderType.Thin;
                        dataStyle.Borders[BorderType.BottomBorder].LineStyle = CellBorderType.Thin;
                        dataStyle.Font.Color = Color.Black;
                        dataStyle.VerticalAlignment = TextAlignmentType.Center; // Center text vertically
                        dataStyle.HorizontalAlignment = TextAlignmentType.Center; // Center text horizontally
                        dataStyle.ForegroundColor = rowColor;
                        dataStyle.Pattern = BackgroundType.Solid;
                        sheet.Cells[i + 1, col].SetStyle(dataStyle);
                        sheet.Cells.Columns[col].Width = 45; // Adjusted width for better text wrapping
                    }

                    sheet.Cells.Rows[i + 1].Height = 90; // Adjust row height for multi-line text
                    i++;
                }
            }
 
            // Page setup
            workbook.Worksheets[0].PageSetup.FitToPagesWide = 1;
            workbook.Worksheets[0].PageSetup.FitToPagesTall = 10;
            PageSetup pageSetup = sheet.PageSetup;
            pageSetup.CenterHorizontally = true; 
            pageSetup.CenterVertically = true;   

            pageSetup.LeftMargin = 1.0;   
            pageSetup.RightMargin = 1.0;  
            pageSetup.TopMargin = 1.0;    
            pageSetup.BottomMargin = 1.0; 

            // Save and return the file
            if (model.Format == "excel")
            {
             
                Style columnStyle2 = workbook.CreateStyle();
                columnStyle2.Font.Name = "Calibri";
                columnStyle2.Font.Size = 12;
              
                columnStyle2.Font.Color = Color.Black;

                StyleFlag flag = new StyleFlag { Font = true };

                for (int col = 0; col <= 3; col++)
                {
                    sheet.Cells.Columns[col].ApplyStyle(columnStyle2, flag);
                    sheet.Cells.SetColumnWidth(col, 18); 
                }
                MemoryStream stream = new MemoryStream();
                workbook.Save(stream, SaveFormat.Xlsx);
                stream.Position = 0;
                return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    Convert.ToString((retValue.Item2[0].ReferenceNumber) != null ? (retValue.Item2[0].ReferenceNumber)
                    : (retValue.Item2[0].Subject)) + DateTime.Now);
            }
            else
            {
                MemoryStream stream = new MemoryStream();
                workbook.Save(stream, SaveFormat.Pdf);
                stream.Position = 0;
                return File(stream, "application/pdf", Convert.ToString((retValue.Item2[0].ReferenceNumber) != null ? (retValue.Item2[0].ReferenceNumber) : (retValue.Item2[0].Subject)) + DateTime.Now);
            }
        }
        #endregion
      
    }
}

