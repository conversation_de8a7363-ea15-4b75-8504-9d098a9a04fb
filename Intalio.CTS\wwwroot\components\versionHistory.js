﻿import Intalio from './common.js'
function deleteVersion(versionId, fileId, documentId, transferId, delegationId)
{
    if (!gLocked)
    {
        Common.showConfirmMsg(Resources.DeleteConfirmation, function ()
        {
            gLocked = true;
            $('#btnVersionHistoryClose').attr('disabled', 'disabled');
            $(".versionHistoryModalClose").attr('disabled', 'disabled');
            Common.ajaxDelete('/Attachment/DeleteVersion',
                {
                    'id': versionId,
                    'fileId': fileId,
                    'documentId': documentId,
                    'transferId': transferId,
                    'delegationId': delegationId,
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                function (response)
                {
                    $('#btnVersionHistoryClose').removeAttr('disabled');
                    $(".versionHistoryModalClose").removeAttr('disabled');
                    gLocked = false;
                    if (response.message != undefined && response.message != "")
                    {
                        Common.alertMsg(response.message);
                    }
                    else
                    {
                        Common.showScreenSuccessMsg(Resources.VersionDeletedSuccessfully);
                        $("#grdVersionHistory").DataTable().ajax.reload();
                        $('.attachmentTree').jstree().refresh();
                    }
                }, function ()
            {
                $('#btnVersionHistoryClose').removeAttr('disabled');
                $(".versionHistoryModalClose").removeAttr('disabled');
                gLocked = false;
                Common.showScreenErrorMsg();
            }, false);
        });
    }
}
function restoreVersion(versionId, fileId, documentId, transferId, delegationId, callback)
{
    if (!gLocked)
    {
        Common.showConfirmMsg(Resources.RestoreVersionConfirmation, function ()
        {
            gLocked = true;
            $('#btnVersionHistoryClose').attr('disabled', 'disabled');
            $(".versionHistoryModalClose").attr('disabled', 'disabled');
            Common.ajaxPost('/Attachment/RestoreVersion',
                {
                    'id': versionId,
                    'fileId': fileId,
                    'documentId': documentId,
                    'transferId': transferId,
                    'delegationId': delegationId,
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                function (response)
                {
                    $('#btnVersionHistoryClose').removeAttr('disabled');
                    $(".versionHistoryModalClose").removeAttr('disabled');
                    gLocked = false;
                    if (response != undefined && response != "Success")
                    {
                        setTimeout(function ()
                        {
                            Common.alertMsg(response);
                        }, 500);
                    }
                    else
                    {
                        Common.showScreenSuccessMsg(Resources.VersionRestoredSuccessfully);
                        $("#grdVersionHistory").DataTable().ajax.reload();
                        $('.attachmentTree').jstree().refresh();
                        if (typeof callback === 'function')
                        {
                            callback();
                        }
                        $('*[id*=_btnSignTemplate]').length > 0 ? $('*[id*=_btnSignTemplate]').show() : null;
                        $('.btn-SignOperations').length > 0 ? $('.btn-SignOperations').show() : null;
                    }
                }, function (error, response)
            {
                $('#btnVersionHistoryClose').removeAttr('disabled');
                $(".versionHistoryModalClose").removeAttr('disabled');
                gLocked = false;
                Common.showScreenErrorMsg();
            }, false);
        });
    }
}
function viewVersion(versionNumber, fileId, documentId, transferId, delegationId)
{
    $("#modalVersionHistory > .modal-dialog").removeClass("modal-lg").addClass("modal-xl");
    $("#versionViewerContainer").show();
    $("#versionGridContainer").removeClass("col-lg-12").addClass("col-lg-6");
    var isDraft = transferId === null || typeof transferId === 'undefined' ? true : false;
    $("#versionViewerFrame").attr("src", window.ViewerUrl + "/assets/viewOnly/viewOnly.html?documentId=" + fileId + "&language=" + window.language + "&token=" +
        window.IdentityAccessToken + "&version=" + versionNumber + "&ctsDocumentId=" + documentId +
        "&ctsTransferId=" + transferId + "&delegationId=" + delegationId + "&viewermode=view&isDraft=" + isDraft);

}
var gLocked = false;
var table;
class VersionHistory extends Intalio.Model
{
    constructor()
    {
        super();
        this.fileId = null;
        this.transferId = null;
        this.documentId = null;
        this.delegationId = null;
        this.hasEditAccess = false;
        this.allowRestore = true;
    }
}
class VersionHistoryView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "versionhistory", model);
    }
    render(callback)
    {
        $.fn.dataTable.render.moment = function (from, to, locale)
        {
            // Argument shifting
            if (arguments.length === 1)
            {
                locale = 'en';
                to = from;
                from = 'YYYY-MM-DD';
            }
            else if (arguments.length === 2)
            {
                locale = 'en';
            }

            return function (d, type, row)
            {
                if (!d)
                {
                    return type === 'sort' || type === 'type' ? 0 : d;
                }

                var m = window.moment(new Date(d), from, locale, true);

                // Order and type get a number value from Moment, everything else
                // sees the rendered value
                return m.format(type === 'sort' || type === 'type' ? 'x' : to);
            };
        };
        $("#versionViewerContainer").hide();
        Common.gridCommon();
        var selfModel = this;
        table = $("#grdVersionHistory").on('draw.dt',
            function ()
            {
                $('#grdItems tbody tr td').each(function ()
                {
                    this.setAttribute('title', $(this).text());
                });
            }).DataTable({
                processing: true,
                ordering: false,
                serverSide: true,
                pageLength: 10,
                "drawCallback": function (settings) {
                    if (settings.json.message == 401) {
                        Common.alertMsg(Resources.NoPermission);
                        return;
                    }
                    if (this.api().data().page() == 0) {
                        var node = $(this.api().table().row(':first').node());
                        node.find('button[id*="restoreVersion"]').hide();
                        node.find('button[id*="deleteVersion"]').hide();
                    }
                },
                "ajax": {
                    "url": "/Attachment/ListVersionHistory",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d)
                    {
                        d.fileId = parseInt(selfModel.model.fileId);
                        d.documentId = selfModel.model.documentId;
                        d.transferId = selfModel.model.transferId;
                        d.delegationId = selfModel.model.delegationId;
                        return d;
                    }
                },
                "columns": [
                    { title: "Id", data: "id", visible: false },
                    {
                        "className": 'details-control',
                        "orderable": false,
                        "data": null,
                        "defaultContent": '',
                        width: '16px'
                    },
                    { title: Resources.Version, data: "version", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    { title: Resources.Name, data: "name", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    { title: Resources.Extension, data: "extension", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    {
                        title: Resources.ModifiedDate, data: "createdDate", "autoWidth": true,
                        render: function (data, type, full, meta)
                        {
                            var date = new Date(full.createdDate);
                            var mm = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
                            var hh = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
                            var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
                            var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
                            var year = date.getFullYear();
                            var dateString = day + '/' + month + '/' + year + " " + hh + ':' + mm;
                            return DateConverter.toHijriFormated(dateString, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                        }
                    },
                    { title: Resources.SizeMB, data: "fileSize", render: function (data, type, full, meta) { return Number((Number(data) / 1024 / 1024).toFixed(2)); }, "autoWidth": true },
                    {
                        "className": "text-center",
                        "autoWidth": true,
                        "bAutoWidth": false,
                        'width': '16px',
                        'orderable': false,
                        'sortable': false,
                        'render': function (data, type, full, meta)
                        {
                            let btn = document.createElement("button");
                            btn.setAttribute("class", "btn btn-xs btn-primary");
                            btn.setAttribute("title", Resources.View);
                            btn.setAttribute("clickAttr", "viewVersion(" +"'"+ full.version+"'" + "," + selfModel.model.fileId + "," + selfModel.model.documentId + "," + selfModel.model.transferId + "," + selfModel.model.delegationId + ")");
                            btn.innerHTML = "<i class='fa fa-eye fa-white'/>";
                            return btn.outerHTML;
                        }
                    },
                    {
                        "className": "text-center",
                        "autoWidth": true,
                        "bAutoWidth": false,
                        'width': '16px',
                        'orderable': false,
                        'sortable': false,
                        'render': function (data, type, full, meta)
                        {
                            if (selfModel.model.hasEditAccess)
                            {
                                let btn = document.createElement("button");
                                btn.setAttribute("class", "btn btn-xs btn-danger");
                                btn.setAttribute("id", "deleteVersion" + full.id);
                                btn.setAttribute("title", Resources.Delete);
                                btn.setAttribute("clickAttr", "deleteVersion(" + full.id + "," + selfModel.model.fileId + "," + selfModel.model.documentId + "," + selfModel.model.transferId + "," + selfModel.model.delegationId + ")");
                                btn.innerHTML = "<i class='fa fa-trash fa-white'/>";
                                return btn.outerHTML;
                            } else
                            {
                                return null;
                            }
                        }
                    },
                    {
                        "className": "text-center",
                        "autoWidth": true,
                        "bAutoWidth": false,
                        'width': '16px',
                        'orderable': false,
                        'sortable': false,
                        'render': function (data, type, full, meta)
                        {
                            if (selfModel.model.hasEditAccess && selfModel.model.allowRestore)
                            {
                                let btn = document.createElement("button");
                                btn.setAttribute("class", "btn btn-xs btn-warning");
                                btn.setAttribute("title", Resources.Restore);
                                btn.setAttribute("id", "restoreVersion" + full.id);
                                btn.setAttribute("clickAttr", "restoreVersion(" + full.id + "," + selfModel.model.fileId + "," + selfModel.model.documentId + "," + selfModel.model.transferId + "," + selfModel.model.delegationId + "," + callback + ")");
                                btn.innerHTML = "<i class='fa fa-history fa-white'/>";
                                return btn.outerHTML;
                            } else
                            {
                                return null;
                            }
                        }
                    }
                ],
                dom: 'trpi'
            });
        $('#grdVersionHistory tbody').on('click', 'td.details-control', function ()
        {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown())
            {
                row.child.hide();
                tr.removeClass('shown');
            }
            else
            {
                row.child(format(row)).show();
                tr.addClass('shown');
            }

        });
        $('#grdVersionHistory tbody').on('click', ".btn", function ()
        {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $("#btnVersionHistoryClose, .versionHistoryModalClose").on('click', function ()
        {
            $('#modalVersionHistory').modal('hide');
        });
    }
}
function format(row)
{
    var html = '<table style="width:100%" cellspacing="0" border="0">' +
        '<tr><th style="width: 20%;padding:5px">' + Resources.Comment + ':</th>' +
        '<td style="width: 80%;padding:5px;word-break: break-all;">' + (row.data().comment || '') + '</td>' +
        '</tr>' +
        '</table>';
    return html;
}
export default { VersionHistory, VersionHistoryView };
