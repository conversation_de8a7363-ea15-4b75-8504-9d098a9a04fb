﻿USE [ISFCTS]
GO
--G2G Beshoy script


--GO
--INSERT [dbo].[Action] ( [Name], [Order], [Icon], [Color], [Tooltip], [ParentActionId], [Type], [JsFunction], [ShowInReadMode], [ShowInEditMode], [CreatedByUserId], [CreatedDate], [ModifiedDate], [CategoryIds]) VALUES ( N'Recall', 1, NULL, N'#00AE8D', NULL, NULL, 2, N'Recall()', NULL, NULL, 1, CAST(N'2024-07-18T12:27:43.1994550' AS DateTime2), CAST(N'2024-07-18T12:31:04.5923678' AS DateTime2), N'')
--GO
--INSERT [dbo].[Action] ( [Name], [Order], [Icon], [Color], [Tooltip], [ParentActionId], [Type], [JsFunction], [ShowInReadMode], [ShowInEditMode], [CreatedByUserId], [CreatedDate], [ModifiedDate], [CategoryIds]) VALUES ( N'Resend', 2, NULL, N'#00AE8D', NULL, NULL, 2, N'Resend()', NULL, NULL, 1, CAST(N'2024-07-18T12:30:59.0576638' AS DateTime2), CAST(N'2024-07-18T12:34:59.1012156' AS DateTime2), N'')
--GO
--INSERT [dbo].[Action] ( [Name], [Order], [Icon], [Color], [Tooltip], [ParentActionId], [Type], [JsFunction], [ShowInReadMode], [ShowInEditMode], [CreatedByUserId], [CreatedDate], [ModifiedDate], [CategoryIds]) VALUES ( N'StopSending', 1, NULL, N'#00AE8D', NULL, NULL, 2, N'StopSending', NULL, NULL, 1, CAST(N'2024-07-18T12:32:06.7715549' AS DateTime2), CAST(N'2024-07-18T12:32:16.4660685' AS DateTime2), N'')
--GO
--INSERT [dbo].[Action] ( [Name], [Order], [Icon], [Color], [Tooltip], [ParentActionId], [Type], [JsFunction], [ShowInReadMode], [ShowInEditMode], [CreatedByUserId], [CreatedDate], [ModifiedDate], [CategoryIds]) VALUES ( N'Receive', 1, NULL, N'#00AE8D', NULL, NULL, 2, N'Receive', NULL, NULL, 1, CAST(N'2024-07-18T12:33:28.0546754' AS DateTime2), CAST(N'2024-07-18T12:38:52.9431040' AS DateTime2), N'')
--GO
--INSERT [dbo].[Action] ( [Name], [Order], [Icon], [Color], [Tooltip], [ParentActionId], [Type], [JsFunction], [ShowInReadMode], [ShowInEditMode], [CreatedByUserId], [CreatedDate], [ModifiedDate], [CategoryIds]) VALUES ( N'Reject', 2, NULL, N'#00AE8D', NULL, NULL, 2, N'Reject', NULL, NULL, 1, CAST(N'2024-07-18T12:33:55.4085673' AS DateTime2), CAST(N'2024-07-18T12:35:24.2188510' AS DateTime2), N'')
--GO
--INSERT [dbo].[Action] ( [Name], [Order], [Icon], [Color], [Tooltip], [ParentActionId], [Type], [JsFunction], [ShowInReadMode], [ShowInEditMode], [CreatedByUserId], [CreatedDate], [ModifiedDate], [CategoryIds]) VALUES ( N'Fetch', 1, NULL, N'#00AE8D', NULL, NULL, 2, N'Fetch', NULL, NULL, 1, CAST(N'2024-07-18T12:36:27.1966570' AS DateTime2), CAST(N'2024-07-18T12:36:35.2726386' AS DateTime2), N'')
--GO


--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Recall%'), 1, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Recall%'), 2, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Recall%'), 5, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Resend%'), 1, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Resend%'), 2, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Resend%'), 3, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%StopSending%'), 1, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%StopSending%'), 2, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%StopSending%'), 3, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Receive%'), 1, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Receive%'), 2, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Receive%'), 3, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Reject%'), 1, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Reject%'), 2, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Reject%'), 3, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Fetch%'), 1, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Fetch%'), 2, NULL, NULL)
--GO
--INSERT [dbo].[ActionSecurity] ( [ActionId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Fetch%'), 3, NULL, NULL)


--GO
--INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'New', CAST(N'2024-07-07T12:42:28.2224141' AS DateTime2), NULL, NULL, NULL, 8, N'fa fa-envelope-o', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("G2G_DocumentInbox"),GetG2GTotalCount("G2G_DocumentInbox"),GetG2GTodayCount("G2G_DocumentInbox")', NULL)
GO
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'Queued', CAST(N'2024-07-15T20:32:39.0100070' AS DateTime2), NULL, NULL, NULL, 10, N'fa fa-bitbucket-square', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("G2G_DocumentInboxQueued"),GetG2GTotalCount("G2G_DocumentInboxQueued"),GetG2GTodayCount("G2G_DocumentInboxQueued")', NULL)
GO
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'IncomingRecalled', CAST(N'2024-07-16T13:39:54.1636565' AS DateTime2), NULL, NULL, NULL, 11, N'fa fa-mail-reply-all', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("G2G_DocumentInboxIncomingRecalled"),GetG2GTotalCount("G2G_DocumentInboxIncomingRecalled"),GetG2GTodayCount("G2G_DocumentInboxIncomingRecalled")', NULL)
GO
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'IncomingRejected', CAST(N'2024-07-17T11:05:13.7540813' AS DateTime2), NULL, NULL, NULL, 12, N'fa fa-angle-double-left', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("G2G_DocumentInboxIncomingRejected"),GetG2GTotalCount("G2G_DocumentInboxIncomingRejected"),GetG2GTodayCount("G2G_DocumentInboxIncomingRejected")', NULL)
GO
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'SentFromStructure', CAST(N'2024-07-17T15:03:33.1827023' AS DateTime2), NULL, NULL, NULL, 13, N'fa fa-send', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("G2G_DocumentInboxSent"),GetG2GTotalCount("G2G_DocumentInboxSent"),GetG2GTodayCount("G2G_DocumentInboxSent")', NULL)
GO
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'ReceiveOrReject', CAST(N'2024-07-17T15:38:47.4275289' AS DateTime2), NULL, NULL, NULL, 14, N'fa fa-recycle', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("G2G_DocumentInboxReceiveOrReject"),GetG2GTotalCount("G2G_DocumentInboxReceiveOrReject"),GetG2GTodayCount("G2G_DocumentInboxReceiveOrReject")', NULL)
GO
--INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'PendingToReceive', CAST(N'2024-07-17T16:07:33.5733881' AS DateTime2), NULL, NULL, NULL, 15, N'fa fa-warning', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("G2G_DocumentInboxPendingToReceive"),GetG2GTotalCount("G2G_DocumentInboxPendingToReceive"),GetG2GTodayCount("G2G_DocumentInboxPendingToReceive")', NULL)
GO
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'Recalled', CAST(N'2024-07-17T17:51:24.8732797' AS DateTime2), NULL, NULL, NULL, 16, N'fa fa-repeat', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("G2G_DocumentInboxRecalled"),GetG2GTotalCount("G2G_DocumentInboxRecalled"),GetG2GTodayCount("G2G_DocumentInboxRecalled")', NULL)
GO
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'Rejected', CAST(N'2024-07-17T18:07:12.7360742' AS DateTime2), NULL, NULL, NULL, 16, N'fa fa-close', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("G2G_DocumentInboxRejected"),GetG2GTotalCount("G2G_DocumentInboxRejected"),GetG2GTodayCount("G2G_DocumentInboxRejected")', NULL)

--GO
--INSERT [dbo].[ActionsNodes] ( [ActionId], [NodeId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Recall%'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'))
--GO
--INSERT [dbo].[ActionsNodes] ( [ActionId], [NodeId]) VALUES (  (select id from [dbo].[Action] where [Name] like '%Resend%'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'))
--GO
--INSERT [dbo].[ActionsNodes] ( [ActionId], [NodeId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%StopSending%'), (select id from [dbo].[Node] where [Name] = 'Queued'))
--GO
--INSERT [dbo].[ActionsNodes] ( [ActionId], [NodeId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Receive%'), (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'))
--GO
--INSERT [dbo].[ActionsNodes] ( [ActionId], [NodeId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Reject%'), (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'))
--GO
--INSERT [dbo].[ActionsNodes] ( [ActionId], [NodeId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Resend%'), (select id from [dbo].[Node] where [Name] = 'Rejected'))
--GO
--INSERT [dbo].[ActionsNodes] ( [ActionId], [NodeId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Reject%'), (select id from [dbo].[Node] where [Name] = 'PendingToReceive'))
--GO
--INSERT [dbo].[ActionsNodes] ( [ActionId], [NodeId]) VALUES ( (select id from [dbo].[Action] where [Name] like '%Fetch%'), (select id from [dbo].[Node] where [Name] = 'PendingToReceive'))
--GO


--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'New'))
--GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'New'))
--GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'New'))
--GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'New'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'Queued'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'Queued'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'Queued'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'Queued'))
GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'MyTransfer'), (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'))
--GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] ='IncomingRecalled'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'))
GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'MyTransfer'), (select id from [dbo].[Node] where [Name] = 'IncomingRejected'))
--GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'IncomingRejected'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'IncomingRejected'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'IncomingRejected'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'IncomingRejected'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'MyTransfer'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'))
GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'MyTransfer'), (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'))
--GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'))
GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'MyTransfer'), (select id from [dbo].[Node] where [Name] = 'PendingToReceive'))
--GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'PendingToReceive'))
--GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'PendingToReceive'))
--GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ((select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'PendingToReceive'))
--GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'PendingToReceive'))
GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'MyTransfer'), (select id from [dbo].[Node] where [Name] = 'Recalled'))
--GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'Recalled'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'Recalled'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ((select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'Recalled'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'Recalled'))
GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'MyTransfer'), (select id from [dbo].[Node] where [Name] = 'Rejected'))
--GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ((select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'Rejected'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'Rejected'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'Rejected'))
GO
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ((select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'Rejected'))
GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'MyTransfer'), (select id from [dbo].[Node] where [Name] = 'New'))
--GO
--INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'MyTransfer'), (select id from [dbo].[Node] where [Name] = 'Queued'))
--GO


--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'New'), 1, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'New'), 2, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'New'), 3, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'New'), 4, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'New'), 5, NULL, NULL)
GO
INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Queued'), 1, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Queued'), 2, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Queued'), 3, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Queued'), 4, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Queued'), 5, NULL, NULL)
GO
INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES (  (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'), 1, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES (  (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'), 2, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES (  (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'), 3, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES (  (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'), 4, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES (  (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'), 5, NULL, NULL)
GO
INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'IncomingRejected'), 1, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'IncomingRejected'), 2, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'IncomingRejected'), 3, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'IncomingRejected'), 4, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'IncomingRejected'), 5, NULL, NULL)
GO
INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'SentFromStructure'), 1, NULL, NULL)
GO
INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'SentFromStructure'), 2, NULL, NULL)
GO
INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'SentFromStructure'), 3, NULL, NULL)
GO
INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'SentFromStructure'), 4, NULL, NULL)
GO
INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'SentFromStructure'), 5, NULL, NULL)
GO
INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'), 1, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'), 2, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'), 3, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'), 4, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'), 5, NULL, NULL)
GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES (  (select id from [dbo].[Node] where [Name] = 'PendingToReceive'), 1, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES (  (select id from [dbo].[Node] where [Name] = 'PendingToReceive'), 2, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES (  (select id from [dbo].[Node] where [Name] = 'PendingToReceive'), 3, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES (  (select id from [dbo].[Node] where [Name] = 'PendingToReceive'), 4, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES (  (select id from [dbo].[Node] where [Name] = 'PendingToReceive'), 5, NULL, NULL)
--GO
INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Recalled'), 1, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Recalled'), 2, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Recalled'), 3, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Recalled'), 4, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Recalled'), 5, NULL, NULL)
GO
INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Rejected'), 1, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Rejected'), 2, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Rejected'), 3, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Rejected'), 4, NULL, NULL)
--GO
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Rejected'), 5, NULL, NULL)


GO

INSERT INTO [dbo].[Parameter]
           ([Keyword]
           ,[Description]
           ,[Content]
		   ,[IsSystem])
     VALUES
           ('g2GUrl'
           ,'G2G url to export'
           ,'http://localhost:63842'
		   ,0)
GO


GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'G2GRefNo', N'G2G Ref No', N'G2G Ref No', N'G2G Ref No', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Queued', N'Queued', N'Queued', N'في قائمة الانتظار', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'IncomingRecalled', N'Incoming Recalled', N'Incoming Recalled', N'تم التذكير بالوارد', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'IncomingRejected', N'Incoming Rejected', N'Incoming Rejected', N'الواردة مرفوضة', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'SentFromStructure', N'Sent From Structure', N'Sent From Structure', N'مرسل من الهيكل', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'ReceiveOrReject', N'Receive Or Reject', N'Receive Or Reject', N'تلقي أو رفض', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'PendingToReceive', N'Pending To Receive', N'Pending To Receive', N'في انتظار الاستلام', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Recalled', N'Recalled', N'Recalled', N'استدعى', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Rejected', N'Rejected', N'Rejected', N'مرفوض', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Recall', N'Recall', N'Recall', N'استدعى', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Resend', N'Resend', N'Resend', N'إعادة إرسال', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'StopSending', N'Stop Sending', N'Stop Sending', N'توقف عن الإرسال', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Receive', N'Receive', N'Receive', N'تسلم', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Reject', N'Reject', N'Reject', N'رفض', 0)
GO
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Fetch', N'Fetch', N'Fetch', N'أحضر', 0)
GO
