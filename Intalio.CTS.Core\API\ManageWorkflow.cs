﻿using Intalio.Core;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Microsoft.AspNetCore.SignalR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.API
{
    public class ManageWorkflow
    {
        public static async Task<APIResponseViewModel> StartWorkflow(WorkflowViewModel model, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language lang = Language.EN)
        {
            APIResponseViewModel retValue = new APIResponseViewModel();
            retValue.Success = false;
            try
            {
                Transfer transfer = new Transfer().Find(transferId ?? 0);
                Document document = new Document().Find(documentId);
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer == null ? null : transfer.CreatedDate) : null;
                if (delegationId != null)
                {
                    if(delegation!=null)
                    {
                        userId = delegation.FromUserId;
                        structureIds = delegation.StructureIds;
                        isStructureReceiver = delegation.IsStructureReceiver;
                        privacyLevel = delegation.PrivacyLevel;
                    }
                   
                }

                if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    if (Configuration.AttachmentEditable)
                    {
                        if (new Document().CheckHasLockedAttachmentsByUser(documentId, userId))
                        {
                            retValue.Success = false;
                            retValue.Message = "FileInUse";
                            return retValue;
                        }
                    }
                    else
                    {
                        if (new Document().CheckOriginalDocumentLockedByUser(documentId, userId))
                        {
                            retValue.Success = false;
                            retValue.Message = "OriginalFileInUse";
                            return retValue;
                        }
                    }

                    if (model.WorkflowUsers == null || model.WorkflowUsers.Count == 0)
                    {
                        retValue.Success = false;
                        retValue.Message = "NoWorkflowStepsAdded";
                        return retValue;
                    }

                    if (!(document.ByTemplate ?? false) && !CheckHasSignatureRegion(model.WorkflowUsers, documentId))
                    {
                        retValue.Success = false;
                        retValue.Message = "NotAllUserHasSignatureRegion";
                        return retValue;
                    }

                    List<WorkflowStep> workflowSteps = new List<WorkflowStep>();
                    foreach (var workflowUser in model.WorkflowUsers)
                    {
                        if (workflowUser.UserId == userId)
                        {
                            retValue.Success = false;
                            retValue.Message = "NotAllowedToAddYourSelf";
                            return retValue;
                        }
                        var userworkflow = new User().ListByIds(new List<long> { workflowUser.UserId });
                        if (userworkflow == null || userworkflow.Count == 0)
                        {
                            retValue.Success = false;
                            retValue.Message = "SynchronizUsersData";
                            return retValue;
                        }
                        WorkflowStep user = new WorkflowStep();
                        user.CreatedBy = userId;
                        user.CreatedDate = DateTime.Now;
                        user.IsCompleted = false;
                        user.order = workflowUser.order;
                        user.PurposeId = workflowUser.PurposeId;
                        user.UserId = workflowUser.UserId;
                        user.StructureId = workflowUser.StructureId;
                        workflowSteps.Add(user);
                    }

                    Workflow workflow = new Workflow();
                    workflow.InitiatorUserId = userId;
                    workflow.InitiatorStructureId = transfer != null ? transfer.ToStructureId.Value : document.CreatedByStructureId;
                    workflow.CreatedDate = DateTime.Now;
                    workflow.WorkflowStep = workflowSteps;
                    workflow.TransferId = transferId;
                    workflow.DocumentId = documentId;
                    workflow.Insert();

                    APIResponseViewModel transferResult = await BeginFirstStep(workflow.Id, documentId, transferId, userId, transfer != null ? transfer.ToStructureId.Value : document.CreatedByStructureId, structureIds, isStructureSender, isStructureReceiver, privacyLevel, delegationId, lang);
                    if (!transferResult.Success)
                    {
                        retValue.Success = false;
                        retValue.Message = transferResult.Message;
                        return retValue;
                    }
                    retValue.Success = true;
                    //retValue.Data= workflow;
                }
                else
                {
                    retValue.Message = "NoAccess";
                }
            }
            catch (Exception ex)
            {
                retValue.Success = false;
                retValue.Message = ex.Message;
                return retValue;
            }

            return retValue;
        }
        public static async Task<APIResponseViewModel> UpdateWorkflow(WorkflowViewModel model, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long? delegationId = null)
        {
            APIResponseViewModel retValue = new APIResponseViewModel();
            retValue.Success = false;
            try
            {
                Transfer transfer = new Transfer().Find(transferId ?? 0);
                Document document = new Document().Find(documentId);
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer == null ? null : transfer.CreatedDate) : null;
                if (delegationId != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                }

                if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
                {

                    if (model.WorkflowUsers == null || model.WorkflowUsers.Count == 0)
                    {
                        retValue.Success = false;
                        retValue.Message = "NoWorkflowStepsAdded";
                        return retValue;
                    }

                    if (!CheckHasSignatureRegion(model.WorkflowUsers, documentId))
                    {
                        retValue.Success = false;
                        retValue.Message = "NotAllUserHasSignatureRegion";
                        return retValue;
                    }
                    Workflow workflow = new Workflow().Find(model.WorkflowId);
                    List<long> list = model.WorkflowUsers.Where(w=>w.Id != null && w.Id != 0).Select(w=>w.Id.Value).ToList();
                    List<WorkflowStep> workflowStepstoDelete = new List<WorkflowStep>();
                    workflowStepstoDelete = workflow.WorkflowStep.Where(w => !list.Contains(w.Id)).ToList();

                    List<WorkflowStep> workflowSteps = new List<WorkflowStep>();
                    var isSelfExist = model.WorkflowUsers.Any(w => w.UserId == userId);
                    if(isSelfExist)
                    {
                        retValue.Success = false;
                        retValue.Message = "NotAllowedToAddYourSelf";
                        return retValue;
                    }
                    foreach(var item in workflowStepstoDelete)
                    {
                        item.Delete();
                    }
                    foreach (var workflowUser in model.WorkflowUsers)
                    {
                        WorkflowStep user = new WorkflowStep();
                        if (workflowUser.Id != null && workflowUser.Id.Value != 0)
                        {
                            user = new WorkflowStep().Find(workflowUser.Id.Value);
                            user.order = workflowUser.order;
                            user.PurposeId = workflowUser.PurposeId;
                            user.UserId = workflowUser.UserId;
                            user.StructureId = workflowUser.StructureId;
                            user.Update();
                        }
                        else
                        {
                            user.CreatedBy = userId;
                            user.CreatedDate = DateTime.Now;
                            user.IsCompleted = false;
                            user.order = workflowUser.order;
                            user.PurposeId = workflowUser.PurposeId;
                            user.UserId = workflowUser.UserId;
                            user.StructureId = workflowUser.StructureId;
                            user.WorkflowId = model.WorkflowId;
                            user.Insert();
                            //workflowSteps.Add(user);
                        }

                    }

                    workflow.InitiatorUserId = userId;
                    workflow.InitiatorStructureId = transfer != null ? transfer.ToStructureId.Value : document.CreatedByStructureId;
                    workflow.CreatedDate = DateTime.Now;
                    workflow.WorkflowStep = workflowSteps;
                    workflow.TransferId = transferId;
                    workflow.DocumentId = documentId;
                    workflow.Update();

                    retValue.Success = true;
                    //retValue.Data= workflow;
                }
                else
                {
                    retValue.Message = "NoAccess";
                }
            }
            catch (Exception ex)
            {
                retValue.Success = false;
                retValue.Message = ex.Message;
                return retValue;
            }

            return retValue;
        }
        public static async Task<APIResponseViewModel> Proceed(long workflowStepId, long documentId, long transferId, long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, bool forSignature = false, long? signatureTemplateId = null, long? delegationId = null, Language lang = Language.EN)
        {
            APIResponseViewModel retValue = new APIResponseViewModel();
            retValue.Success = false;
            try
            {
                WorkflowStep currrentStep = new WorkflowStep().Find(workflowStepId);
                if(currrentStep.UserId != userId)
                {
                    retValue.Success = false;
                    retValue.Message = "HasNoAccess";
                    return retValue;
                }
                Transfer transfer = new Transfer().Find(transferId);
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
                if (delegationId != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                }

                if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    if (Configuration.AttachmentEditable)
                    {
                        if (new Document().CheckHasLockedAttachmentsByUser(documentId, userId))
                        {
                            retValue.Success = false;
                            retValue.Message = "FileInUse";
                            return retValue;
                        }
                    }
                    else
                    {
                        if (new Document().CheckOriginalDocumentLockedByUser(documentId, userId))
                        {
                            retValue.Success = false;
                            retValue.Message = "OriginalFileInUse";
                            return retValue;
                        }
                    }
                    Document document = new Document().Find(documentId);
                    var hasSignatureRegion = new AttachmentSignUser().FindByDocumentAndUser(documentId, userId).Any();
                    if ((forSignature || hasSignatureRegion) && ((document.ByTemplate ?? false) && !(document.IsSigned ?? false)))
                    {
                        var signatureResult = await ManageTransfer.SignDocument(documentId, transferId, userId, structureIds, isStructureReceiver, isStructureSender, privacyLevel, signatureTemplateId.Value, delegationId, lang);
                        if (!signatureResult.updated)
                        {
                            retValue.Success = false;
                            retValue.Message = "SignatureFailed";
                            return retValue;
                        }
                    }

                    


                    WorkflowStep nextStep = new WorkflowStep().FindNextStep(currrentStep.WorkflowId, currrentStep.order + 1);
                    List<TransferModel> transfers = new List<TransferModel>();
                    TransferModel nextTransfer = new TransferModel();
                    if (nextStep != null)
                    {
                        Purpose nextPurpose = new Purpose().Find(nextStep.PurposeId);
                        nextTransfer.ParentTransferId = transferId;
                        nextTransfer.ToUserId = nextStep.UserId;
                        nextTransfer.ToStructureId = nextStep.StructureId;
                        nextTransfer.FromStructureId = transfer.ToStructureId;
                        nextTransfer.PurposeId = nextStep.PurposeId;
                        nextTransfer.CCed = nextPurpose.Cced;
                        nextTransfer.DocumentId = documentId;
                        nextTransfer.WorkflowStepId = nextStep.Id;
                        transfers.Add(nextTransfer);

                        var result = await ManageTransfer.Transfer(userId, transfer.ToStructureId.Value, structureIds, isStructureSender, isStructureReceiver, privacyLevel, transfers, false, delegationId, language: lang);
                        if (result != null)
                        {
                            retValue.Success = result[0].Updated;
                            retValue.Message = result[0].Message;
                        }

                    }
                    else
                    {
                        nextTransfer.ParentTransferId = transferId;
                        nextTransfer.ToUserId = currrentStep.Workflow.InitiatorUserId;
                        nextTransfer.ToStructureId = currrentStep.Workflow.InitiatorStructureId;
                        nextTransfer.FromStructureId = transfer.ToStructureId;
                        nextTransfer.PurposeId = currrentStep.PurposeId;
                        nextTransfer.CCed = false;
                        nextTransfer.DocumentId = documentId;
                        transfers.Add(nextTransfer);

                        var result = await ManageTransfer.Transfer(userId, transfer.ToStructureId.Value, structureIds, isStructureSender, isStructureReceiver, privacyLevel, transfers, false, delegationId, language: lang);
                        if (result != null)
                        {
                            retValue.Success = result[0].Updated;
                            retValue.Message = result[0].Message;
                        }
                    }

                    if (!retValue.Success)
                        return retValue;

                    currrentStep.IsCompleted = true;
                    currrentStep.Update();

                    retValue.Success = true;
                    retValue.Message = "";
                }
                if (forSignature)
                    ManageActivityLog.AddActivityLog(documentId, transferId, (int)ActivityLogs.SignAndProceed, userId);
                else
                    ManageActivityLog.AddActivityLog(documentId, transferId, (int)ActivityLogs.Proceed, userId);
            }
            catch (Exception ex)
            {
                retValue.Success = false;
                retValue.Message = ex.Message;
                return retValue;
            }


            return retValue;
        }
        public static APIResponseViewModel CancelWorkflow(long WorkflowStepId, long documentId, long transferId, long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language lang = Language.EN)
        {
            var retValue = new APIResponseViewModel();
            retValue.Success = false;
            try
            {
                Transfer transfer = new Transfer().Find(transferId);
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
                if (delegationId != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                }

                if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    if (Configuration.AttachmentEditable)
                    {
                        if (new Document().CheckHasLockedAttachmentsByUser(documentId, userId))
                        {
                            retValue.Success = false;
                            retValue.Message = "FileInUse";
                            return retValue;
                        }
                    }
                    else
                    {
                        if (new Document().CheckOriginalDocumentLockedByUser(documentId, userId))
                        {
                            retValue.Success = false;
                            retValue.Message = "OriginalFileInUse";
                            return retValue;
                        }
                    }
                    transfer.WorkflowStepId = null;
                    transfer.Update();
                    retValue.Success = true;

                }
                ManageActivityLog.AddActivityLog(documentId, transferId, (int)ActivityLogs.Cancel, userId);
            }
            catch (Exception ex)
            {
                retValue.Success = false;
                retValue.Message = ex.Message;
                return retValue;
            }
            return retValue;
        }
        public static WorkflowViewModel FindAllWorkflowSteps(long workflowStepId, long documentId, long transferId, long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language lang = Language.EN)
        {
            WorkflowViewModel retValue = new WorkflowViewModel();
            retValue.WorkflowUsers = new List<WorkflowUserViewModel>();
            retValue.DocumentId = documentId;

            try
            {
                Transfer transfer = new Transfer().Find(transferId);
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
                if (delegationId != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                }

                if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    WorkflowStep currrentStep = new WorkflowStep().Find(workflowStepId);
                    Workflow workflow = new Workflow().Find(currrentStep.WorkflowId);
                    //retValue = (WorkflowViewModel)workflow;
                    foreach (var workflowStep in workflow.WorkflowStep)
                    {
                        WorkflowUserViewModel step = new WorkflowUserViewModel();
                        step.Id = workflowStep.Id;
                        step.StructureId = workflowStep.StructureId;
                        step.UserId = workflowStep.UserId;
                        step.PurposeId = workflowStep.PurposeId;
                        step.order = workflowStep.order;
                        step.IsCompleted = workflowStep.IsCompleted;
                        //step.Username = ManageUser.GetFullNameByUser(workflowStep.User, lang);
                        retValue.WorkflowUsers.Add(step);
                    }
                    retValue.WorkflowId = workflow.Id;
                    retValue.TransferId = workflow.TransferId;
                    retValue.InitiatorUserId = workflow.InitiatorUserId;
                    retValue.InitiatorStructureId = workflow.InitiatorStructureId;
                }
            }
            catch (Exception ex)
            {

            }
            return retValue;
        }

        public static async Task<APIResponseViewModel> ResumeWorkflow(long workflowStepId, long documentId, long transferId, long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language lang = Language.EN)
        {
            var retValue = new APIResponseViewModel();
            retValue.Success = false;
            try
            {
                Transfer transfer = new Transfer().Find(transferId);
                WorkflowStep currrentStep = new WorkflowStep().Find(workflowStepId);

                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
                if (delegationId != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                }

                if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    if (Configuration.AttachmentEditable)
                    {
                        if (new Document().CheckHasLockedAttachmentsByUser(documentId, userId))
                        {
                            retValue.Success = false;
                            retValue.Message = "FileInUse";
                            return retValue;
                        }
                    }
                    else
                    {
                        if (new Document().CheckOriginalDocumentLockedByUser(documentId, userId))
                        {
                            retValue.Success = false;
                            retValue.Message = "OriginalFileInUse";
                            return retValue;
                        }
                    }
                    List<TransferModel> transfers = new List<TransferModel>();
                    TransferModel nextTransfer = new TransferModel();
                    if (currrentStep != null)
                    {
                        Purpose currentPurpose = new Purpose().Find(currrentStep.PurposeId);
                        nextTransfer.ParentTransferId = transferId;
                        nextTransfer.ToUserId = currrentStep.UserId;
                        nextTransfer.ToStructureId = currrentStep.StructureId;
                        nextTransfer.FromStructureId = transfer.ToStructureId;
                        nextTransfer.PurposeId = currrentStep.PurposeId;
                        nextTransfer.CCed = currentPurpose.Cced;
                        nextTransfer.DocumentId = documentId;
                        nextTransfer.WorkflowStepId = currrentStep.Id;
                        transfers.Add(nextTransfer);

                        var result = await ManageTransfer.Transfer(userId, transfer.ToStructureId.Value, structureIds, isStructureSender, isStructureReceiver, privacyLevel, transfers, false, delegationId, language: lang);
                        if (result != null)
                        {
                            retValue.Success = result[0].Updated;
                            retValue.Message = result[0].Message;
                        }

                        if (!retValue.Success)
                            return retValue;
                    }
                }
            }
            catch (Exception ex)
            {
                retValue.Success = false;
                retValue.Message = ex.Message;
                return retValue;
            }

            return retValue;
        }
        public static async Task<APIResponseViewModel> SaveWorkflow(WorkflowViewModel model, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long? delegationId = null)
        {
            APIResponseViewModel retValue = new APIResponseViewModel();
            retValue.Success = false;
            try
            {
                Transfer transfer = new Transfer().Find(transferId ?? 0);
                Document document = new Document().Find(documentId);
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer == null ? null : transfer.CreatedDate) : null;
                if (delegationId != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                }

                if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
                {


                    if (model.WorkflowUsers == null || model.WorkflowUsers.Count == 0)
                    {
                        retValue.Success = false;
                        retValue.Message = "NoWorkflowStepsAdded";
                        return retValue;
                    }



                    List<WorkflowStep> workflowSteps = new List<WorkflowStep>();
                    foreach (var workflowUser in model.WorkflowUsers)
                    {
                        if (workflowUser.UserId == userId)
                        {
                            retValue.Success = false;
                            retValue.Message = "NotAllowedToAddYourSelf";
                            return retValue;
                        }
                        var userworkflow = new User().ListByIds(new List<long> { workflowUser.UserId });
                        if (userworkflow == null || userworkflow.Count == 0)
                        {
                            retValue.Success = false;
                            retValue.Message = "SynchronizUsersData";
                            return retValue;
                        }
                        WorkflowStep user = new WorkflowStep();
                        user.CreatedBy = userId;
                        user.CreatedDate = DateTime.Now;
                        user.IsCompleted = false;
                        user.order = workflowUser.order;
                        user.PurposeId = workflowUser.PurposeId;
                        user.UserId = workflowUser.UserId;
                        user.StructureId = workflowUser.StructureId;
                        workflowSteps.Add(user);
                    }

                    Workflow workflow = new Workflow();
                    workflow.InitiatorUserId = userId;
                    workflow.InitiatorStructureId = transfer != null ? transfer.ToStructureId.Value : document.CreatedByStructureId;
                    workflow.CreatedDate = DateTime.Now;
                    workflow.WorkflowStep = workflowSteps;
                    workflow.TransferId = transferId;
                    workflow.DocumentId = documentId;
                    workflow.Insert();

                    retValue.Success = true;
                    //retValue.Data= workflow;
                }
                else
                {
                    retValue.Message = "NoAccess";
                }
            }
            catch (Exception ex)
            {
                retValue.Success = false;
                retValue.Message = ex.Message;
                return retValue;
            }

            return retValue;
        }
        public static WorkflowViewModel GetWorkflowForStartWorkflow(long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language lang = Language.EN)
        {
            WorkflowViewModel retValue = new WorkflowViewModel();
            retValue.WorkflowUsers = new List<WorkflowUserViewModel>();
            retValue.DocumentId = documentId;

            try
            {

                if (ManageUserAccess.HaveAccess(documentId, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    Workflow workflow = new Workflow().FindByDocumentAndTransfer(documentId, transferId);
                    if(workflow!= null)
                    {
                        foreach (var workflowStep in workflow.WorkflowStep)
                        {
                            WorkflowUserViewModel step = new WorkflowUserViewModel();
                            step.Id = workflowStep.Id;
                            step.StructureId = workflowStep.StructureId;
                            step.UserId = workflowStep.UserId;
                            step.PurposeId = workflowStep.PurposeId;
                            step.order = workflowStep.order;
                            step.IsCompleted = workflowStep.IsCompleted;
                            //step.Username = ManageUser.GetFullNameByUser(workflowStep.User, lang);
                            retValue.WorkflowUsers.Add(step);
                        }
                        retValue.WorkflowId = workflow.Id;
                        retValue.TransferId = workflow.TransferId;
                        retValue.InitiatorUserId = workflow.InitiatorUserId;
                        retValue.InitiatorStructureId = workflow.InitiatorStructureId;
                    }
                    //retValue = (WorkflowViewModel)workflow;
                    
                }
            }
            catch (Exception ex)
            {

            }
            return retValue;
        }
        public static WorkflowViewModel GetWorkflow(long documentId,long workflowId, long? transferId, long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language lang = Language.EN)
        {
            WorkflowViewModel retValue = new WorkflowViewModel();
            retValue.WorkflowUsers = new List<WorkflowUserViewModel>();
            retValue.DocumentId = documentId;

            try
            {

                if (ManageUserAccess.HaveAccess(documentId, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    Workflow workflow = new Workflow().Find(workflowId);
                    if (workflow != null)
                    {
                        foreach (var workflowStep in workflow.WorkflowStep)
                        {
                            WorkflowUserViewModel step = new WorkflowUserViewModel();
                            step.Id = workflowStep.Id;
                            step.StructureId = workflowStep.StructureId;
                            step.UserId = workflowStep.UserId;
                            step.PurposeId = workflowStep.PurposeId;
                            step.order = workflowStep.order;
                            step.IsCompleted = workflowStep.IsCompleted;
                            //step.Username = ManageUser.GetFullNameByUser(workflowStep.User, lang);
                            retValue.WorkflowUsers.Add(step);
                        }
                        retValue.WorkflowId = workflow.Id;
                        retValue.TransferId = workflow.TransferId;
                        retValue.InitiatorUserId = workflow.InitiatorUserId;
                        retValue.InitiatorStructureId = workflow.InitiatorStructureId;
                    }
                    //retValue = (WorkflowViewModel)workflow;

                }
            }
            catch (Exception ex)
            {

            }
            return retValue;
        }
        public static async Task<APIResponseViewModel> CreateWorkflow(WorkflowViewModel model,long documentId,long? transferId,long userId,List<long> structureIds,bool isStructureReceiver, short privacyLevel,long?delegationId = null)
        {
            APIResponseViewModel retValue = new APIResponseViewModel();
            retValue.Success = false;
            try
            {
                Transfer transfer = new Transfer().Find(transferId ?? 0);
                Document document = new Document().Find(documentId);
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer == null ? null : transfer.CreatedDate) : null;
                if (delegationId != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                }

                if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
                {                                  
                    Workflow workflow = new Workflow();
                    workflow.InitiatorUserId = userId;
                    workflow.InitiatorStructureId = transfer != null ? transfer.ToStructureId.Value : document.CreatedByStructureId;
                    workflow.CreatedDate = DateTime.Now;                    
                    workflow.TransferId = transferId;
                    workflow.DocumentId = documentId;
                    workflow.Insert();

                    retValue.Success = true;
                    retValue.Data= workflow.Id;
                }
                else
                {
                    retValue.Message = "NoAccess";
                }
            }
            catch (Exception ex)
            {
                retValue.Success = false;
                retValue.Message = ex.Message;
                return retValue;
            }

            return retValue;
        }
        private static async Task<APIResponseViewModel> BeginFirstStep(long workflowId, long documentId, long? transferId, long userId, long sendingStructureId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language language = Language.EN)
        {
            APIResponseViewModel retValue = new APIResponseViewModel();
            retValue.Success = false;
            try
            {
                Transfer transfer = new Transfer().Find(transferId ?? 0);
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer?.CreatedDate) : null;
                if (delegationId != null)
                {
                    if(delegation !=null)
                    {
                        userId = delegation.FromUserId;
                        structureIds = delegation.StructureIds;
                        isStructureReceiver = delegation.IsStructureReceiver;
                        privacyLevel = delegation.PrivacyLevel;
                    }
                    
                }

                if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    Workflow workflow = new Workflow().Find(workflowId);
                    WorkflowStep firstStep = workflow.WorkflowStep.Where(ws => ws.order == 1).FirstOrDefault();
                    if (firstStep != null)
                    {
                        List<TransferModel> transfers = new List<TransferModel>();
                        TransferModel firstTransfer = new TransferModel();
                        Purpose firstPurpose = new Purpose().Find(firstStep.PurposeId);
                        firstTransfer.ParentTransferId = transferId;
                        firstTransfer.ToUserId = firstStep.UserId;
                        firstTransfer.ToStructureId = firstStep.StructureId;
                        firstTransfer.FromStructureId = sendingStructureId;
                        firstTransfer.PurposeId = firstStep.PurposeId;
                        firstTransfer.CCed = firstPurpose.Cced;
                        firstTransfer.DocumentId = documentId;
                        firstTransfer.WorkflowStepId = firstStep.Id;
                        transfers.Add(firstTransfer);

                        var result = await ManageTransfer.Transfer(userId, sendingStructureId, structureIds, isStructureSender, isStructureReceiver, privacyLevel, transfers, false, delegationId,language: language);
                        if (result != null)
                        {
                            retValue.Success = result[0].Updated;
                            retValue.Message = result[0].Message;
                            retValue.Data = result[0];
                        }

                    }

                }
            }
            catch (Exception ex)
            {
                retValue.Success = false;
                retValue.Message = ex.Message;
                return retValue;
            }

            return retValue;
        }

        private static bool CheckHasSignatureRegion(List<WorkflowUserViewModel> workflowUsers, long documentId)
        {
            bool retValue = true;
            foreach (var user in workflowUsers)
            {
                if (user.PurposeForSignature)
                {
                    var signtureRegion = new AttachmentSignUser().FindByDocumentAndUser(documentId, user.UserId);
                    if (signtureRegion == null || signtureRegion.Count == 0) { return false; }
                }
            }
            return retValue;
        }
    }
}
