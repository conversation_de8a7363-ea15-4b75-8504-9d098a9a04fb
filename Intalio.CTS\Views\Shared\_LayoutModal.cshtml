﻿@using System.Globalization
@using  Microsoft.AspNetCore.Authentication
<!DOCTYPE html>
<html @(CultureInfo.CurrentUICulture.Name.Equals("ar") ? "dir=rtl" : "")>
@*Linked*@
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>@Intalio.CTS.Core.Configuration.ApplicationName</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    @if (IsSectionDefined("Styles"))
    {@RenderSection("Styles", required: false)}
    <environment names="Development,Production">
        <link href="~/lib/combobox/comboBox.min.css" rel="stylesheet" />
        <link href="~/lib/combobox/comboBoxEN.min.css" rel="stylesheet" />
        <link href="~/lib/jquery-te/jquery-te.css" rel="stylesheet" />
        @{
            if (CultureInfo.CurrentUICulture.Name.Equals("ar"))
            {
                <link href="~/css/core-css-rtl.min.css" rel="stylesheet" type="text/css" />
            }
            else
            {
                <link href="~/css/core-css.min.css" rel="stylesheet" />
            }
        }
        <link href="~/lib/dropzone/dropzone.min.css" rel="stylesheet" />
        <link href="~/css/style.css" rel="stylesheet" />
        @{
            if (CultureInfo.CurrentUICulture.Name.Equals("ar"))
            {
                <link href="~/css/style-rtl.css" rel="stylesheet" />
            }
        }
        <link href="~/lib/orgchart/getorgchart.min.css" rel="stylesheet" />

    </environment>
    <environment names="Staging">
        @{
            if (CultureInfo.CurrentUICulture.Name.Equals("ar"))
            {
                <link href="~/css/main-rtl.min.css?v=@Intalio.CTS.Core.Configuration.Version" rel="stylesheet" type="text/css" />
            }
            else
            {
                <link href="~/css/main.min.css?v=@Intalio.CTS.Core.Configuration.Version" rel="stylesheet" type="text/css" />
            }
        }
    </environment>
    @{
        var customcss = Intalio.CTS.Core.API.ManageCustomizationFile.FindByTypeId((byte)Intalio.Core.CustomizationFileType.CSS);
        if (customcss != null)
        {
            <style>
        @Html.Raw(customcss.Data)
            </style>
        }
    }
<script type="text/javascript">
         @{
                        var language = CultureInfo.CurrentUICulture.Name.Replace("en-GB", "en");
                        var currentLanguage = Intalio.Core.Language.EN;
                        if (language == "ar")
                        {
                            currentLanguage = Intalio.Core.Language.AR;
                        }
                        else if (language == "fr")
                        {
                            currentLanguage = Intalio.Core.Language.FR;
                        }
                       var user = Convert.ToInt64(User.Claims.First(t => t.Type == "Id").Value);
           }

        window.language = "@CultureInfo.CurrentUICulture.Name.Replace("en-GB", "en")";
        window.IdentityUrl = "@Intalio.CTS.Core.Configuration.IdentityAuthorityUrl";
        window.IdentityAccessToken = "@Context.GetTokenAsync("access_token").Result";
        window.HideAuditTrail = "@(Intalio.CTS.Core.Configuration.AuditTrailMode == Intalio.Core.AuditTrailMode.None)";
        window.EnableConfirmationMessage = "@Intalio.CTS.Core.Configuration.EnableConfirmationMessage";
        window.InboxMode = "@Intalio.CTS.Core.Utility.IdentityHelperExtension.GetUserInboxMode("InboxMode",user)";
        window.OpenCorrespondenceMode = "@Intalio.CTS.Core.Configuration.OpenCorrespondenceMode";
        window.ByFileMode = "@Intalio.CTS.Core.Configuration.ByFileMode";
        window.LinkedDeleteForCreatorOnly = "@Intalio.CTS.Core.Configuration.LinkedDeleteForCreatorOnly";
        window.StructureNameAr = "@Intalio.CTS.Core.Configuration.StructureNameAr";
        window.StructureNameFr = "@Intalio.CTS.Core.Configuration.StructureNameFr";
        window.ByFileExtensions = "@Intalio.CTS.Core.Configuration.ByFileExtensions";
        window.UserStructureReceiver = "@Intalio.CTS.Core.Configuration.UserStructureReceiver";
        window.UserPrivacy = "@Intalio.CTS.Core.Configuration.UserPrivacy";
        window.SendWithoutStructureReceiverOrPrivacyLevel = "@Intalio.CTS.Core.Configuration.SendWithoutStructureReceiverOrPrivacyLevel";
        window.IsStructureSender = "@(Intalio.CTS.Core.Configuration.EnablePerStructure ? Intalio.CTS.Core.Utility.IdentityHelperExtension.GetUserStrucurePermission(user, currentLanguage).IsStrucureSender : User.Claims.Any(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender) && !string.IsNullOrEmpty(User.Claims.First(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender).Value) ? Convert.ToBoolean(User.Claims.First(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender).Value) : false)"; //"@(User.Claims.Any(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender) && !string.IsNullOrEmpty(User.Claims.First(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender).Value) ? Convert.ToBoolean(User.Claims.First(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender).Value) : false)";
        window.EnableSendingRules = "@Intalio.CTS.Core.Configuration.EnableSendingRules";
        window.MaxFileSize = "@Intalio.CTS.Core.Configuration.MaxFileSize";
        window.AttachmentEditable = "@Intalio.CTS.Core.Configuration.AttachmentEditable";
        window.ViewerUrl = "@Intalio.CTS.Core.Configuration.ViewerUrl";
        window.PriorityPrivacyAction = "@Intalio.CTS.Core.Configuration.PriorityPrivacyAction";
        window.ViewerMode ="@Intalio.CTS.Core.Configuration.ViewerMode";


        @foreach (var par in Intalio.CTS.Core.API.ManageParameter.CustomListParameters())
            {
           @:window["@par.Text"] = "@par.Value";
        }
</script>
</head>
<body>
  
    <input id="hdUserId" type="hidden" value="@User.Claims.First(t => t.Type == "Id").Value" />
    <input id="hdStructureIds" type="hidden" value="@(Intalio.CTS.Core.Configuration.EnablePerStructure ? @Intalio.CTS.Core.Utility.IdentityHelperExtension.getCurrentLoggedInStructure(Convert.ToInt64( User.Claims.First(t => t.Type == "Id").Value)).ToString() : @User.Claims.First(t => t.Type == "StructureIds").Value)"/>
    <input id="hdGroupIds" type="hidden" value="@User.Claims.First(t => t.Type == "GroupIds").Value" />
    <input id="hdHasManager" type="hidden" value="@User.Claims.First(t => t.Type == "ManagerId").Value" />
    <input id="hdStructureId" type="hidden" value="@User.Claims.First(t => t.Type == "StructureId").Value" />
    <input id="hdLoggedInStructureId" type="hidden" value="@Intalio.CTS.Core.Utility.IdentityHelperExtension.getCurrentLoggedInStructure(Convert.ToInt64( User.Claims.First(t => t.Type == "Id").Value))"/>
    <input id="hdLoggedInRoleId" type="hidden" value="" />
    @{
        var userId = Convert.ToInt64(User.Claims.First(t => t.Type == "Id").Value);
        var editDesignatedPerson = Intalio.CTS.Core.Utility.IdentityHelperExtension.GetUserStrucurePermission(userId, Intalio.Core.Language.EN).EditDesignatedPerson;}
    <input id="hdEditDesignatedPerson" type="hidden" value="@($"{editDesignatedPerson}")" />

    @{
        var delegationList = Intalio.CTS.Core.API.ManageDelegation.ListDelegationToUserIds(userId);
        <input id="hdDelegatedUserIds" type="hidden" value="@delegationList" />
    }
    <div class="wrapper">
        <section class="margin-0">
            <div class="content-wrapper">
                @RenderBody()
            </div>
        </section>
    </div>
    <div class="modal-window"></div>
    @if (IsSectionDefined("BodyArea"))
    {
        @RenderSection("BodyArea", required: false)
    }
    @Html.AntiForgeryToken()
    <script src="/CoreJS?@<EMAIL>("en-GB","en")"></script>
    <script src="/JS?@<EMAIL>("en-GB","en")"></script>
    @RenderSection("scripts", required: false)
    <script asp-append-version="true" src="~/js/core-js.min.js?v=@Intalio.CTS.Core.Configuration.Version"></script>
    <script asp-append-version="true" src="~/js/templates.min.js?v=@Intalio.CTS.Core.Configuration.Version"></script>
    <script asp-append-version="true" src="~/js/core-components.min.js?v=@Intalio.CTS.Core.Configuration.Version"></script>
    <environment names="Development,Production">
        <script asp-append-version="true" src="~/js/app.js"></script>
        <script src="~/lib/combobox/Combo.js"></script>
        <script src="~/lib/datatableEditor/dataTables.editor.min.js"></script>
        <script src="~/lib/orgchart/getorgchart.js"></script>
        <script type="text/javascript" src="~/lib/ckeditor/ckeditor.js"></script>
        <script type="text/javascript" src="~/lib/DynamicWebTWAIN/dynamsoft.webtwain.initiate.js"></script>
        <script type="text/javascript" src="~/lib/DynamicWebTWAIN/dynamsoft.webtwain.config.js"></script>
        <script src="~/lib/dropzone/dropzone.min.js"></script>
        <script src="~/lib/jquery-te/jquery-te.js"></script>
        <script asp-append-version="true" src="~/components/appComponent.js" type="module"></script>
    </environment>
    <environment names="Staging">
        <script asp-append-version="true" src="~/js/main.min.js?v=@Intalio.CTS.Core.Configuration.Version"></script>
        <script asp-append-version="true" type="text/javascript" src="~/lib/ckeditor/ckeditor.js"></script>
        <script asp-append-version="true" src="~/js/bundle.min.js?v=@Intalio.CTS.Core.Configuration.Version" type="text/javascript"></script>
        @RenderSection("scripts", required: false)
    </environment>
    @{
        var customjs = Intalio.CTS.Core.API.ManageCustomizationFile.FindByTypeId((byte)Intalio.Core.CustomizationFileType.Javascript);
        if (customjs != null)
        {
            <script>
            @Html.Raw(customjs.Data)
            </script>
        }
    }
</body>
</html>
