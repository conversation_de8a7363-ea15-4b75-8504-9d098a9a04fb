﻿import Intalio from './common.js'
import DocumentDetails from './documentDetails.js'
import { Categories, Nodes } from './lookup.js'
import ReportCorrespondenceDetailExport from './reportCorrespondenceDetailExport.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import VipDocumentMyRequests from './vipMyRequestsList.js'
import CustomizeNodeColomns from '../components/customizeNodeColomns.js'

class DocumentMyRequests extends Intalio.Model
{
    constructor()
    {
        super();
        this.title = null;
        this.categories = null;
        this.statuses = null;
    }
}
var gArabic = /[\u0600-\u06FF]/;
var gfirstTime = true;
var gDataChanged = true;
var gAction = "";
var gFilterChanged = false;
var gFilterStructure = "";
var gFilterFromDate = "";
var gFilterToDate = "";
var gFilterUser = "";
var gFilterOverdue = "";
var gOverdue = false;
var clear = false;
var gReportName = "MyRequests Report";
function format(row, nodeId)
{
    return '<table style="width:100%" cellspacing="0" border="0">' + buildColumnsDetails(row, nodeId) + '</table>';
}

function openDocument(id, nodeId)
{
    var params = { id: id };
    Common.ajaxGet('/Document/GetDocument', params, function (response)
    {
        gLocked = false;
        Common.setActiveSidebarMenu("liMyRequests" + nodeId);
        $(".delegation").removeClass("active");
        //$("#gridContainerDiv").hide();

        var wrapper = $(".modal-documents");
        var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
        linkedCorrespondenceModel.reference = response.referenceNumber;
        linkedCorrespondenceModel.subject = response.subject;
        linkedCorrespondenceModel.documentId = response.id;
        //linkedCorrespondenceModel.subject = $("#" + model.ComponentId + "_subjectTaskPanel").html();
        //linkedCorrespondenceModel.from = $("#" + model.ComponentId + "_fromTaskPanel").html();
        //linkedCorrespondenceModel.to = $("#" + model.ComponentId + "_toTaskPanel").html();
        //linkedCorrespondenceModel.transferDate = $("#" + model.ComponentId + "_transferDateTaskPanel").html();
        //linkedCorrespondenceModel.registerDate = $("#" + model.ComponentId + "_registerDateTaskPanel").html();
        //linkedCorrespondenceModel.registeredBy = $("#" + model.ComponentId + "_registerByTaskPanel").html();
        var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
        linkedCorrespondenceDocument.render();

         var model = new DocumentDetails.DocumentDetails();
        model.categoryId = response.categoryId;
        model.documentId = response.id;
        model.senderPerson = response.senderPerson;
        model.receiverPerson = response.receiverPerson;
        model.isExternalReceiver = response.isExternalReceiver;
        model.isExternalSender = response.isExternalSender;
        model.referenceNumber = response.referenceNumber;
        model.categoryName = response.categoryName;
        model.statusId = response.status;
        model.documentModel = response;
        model.readonly = true;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.showMyTransfer = false;
        model.attachmentId = response.attachmentId;
        model.formData = (response.formData || "") !== "" ? eval("(" + response.formData + ")") : null;
        model.formDesigner = (response.formDesigner || "") !== "" ? JSON.parse(response.formDesigner) : null;
        model.formDesignerTranslation = (response.formDesignerTranslation || "") !== "" ? JSON.parse(response.formDesignerTranslation) : null;
        model.fullControl = response.fullControl;
        model.isTaskCreator = response.isTaskCreator;
        model.createdByUser = response.createdByUser;
        model.attachmentVersion = response.attachmentVersion;
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
        model.tabs = $.grep(tabs, function (element, index)
        {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
        });
        model.tabsWithStatic = tabs;
        model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;

        if (response.categoryId == window.FollowUpCategory) {
            model.readonly = false;
        }

        model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
        model.isModal = true;
        model.showBackButton = false;
        wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

        var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
        view.render();

        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
            $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');

        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
            if ($(this).data("remove") != true)
                return;
            $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
            swal.close();
            if ($(".modal-documents").children().length > 0 || $(".modal-window").children().length > 0) {
                $('body').addClass('modal-open');
            }

        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

        $(document).off('click', '.btn-back');
        $(document).on('click', '.btn-back', function ()
        {
            $("#gridContainerDiv").show();
            view.remove();
            $(".toRemove").remove();
        });
        $(document).off('click', '.btn-export');
        $(document).on('click', '.btn-export', function ()
        {
            var wrapper = $(".modal-window");
            var model = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExport();
            model.documentId = response.id;
            var reportCorrespondenceDetailExportView = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExportView(wrapper, model);
            reportCorrespondenceDetailExportView.render();

            $("#modalReportCorrespondenceDetailExport").off("hidden.bs.modal");
            $("#modalReportCorrespondenceDetailExport").off("shown.bs.modal");
            $("#modalReportCorrespondenceDetailExport").on('shown.bs.modal', function ()
            { });
            $("#modalReportCorrespondenceDetailExport").on('hidden.bs.modal', function ()
            { });
            $("#modalReportCorrespondenceDetailExport").modal("show");
        });
    }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
}
function buildFilters(nodeId)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var filters = node.filters !== "" && node.filters !== null ? JSON.parse(node.filters) : [];

    if (filters.length > 0)
    {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++)
        {
            var filter = filters[i];
            switch (filter)
            {
                case "ReferenceNumber":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
                        '<input type="text" id="txtFilterMyRequestsReferenceNumber" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "FromDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label><div class="input-group date">' +
                        '<input id="filterMyRequestsFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterMyRequestsFromDateError">' +
                        '<span class="input-group-addon" id="filterMyRequestsFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterMyRequestsFromDateError"></div></div></div></div>'
                    break;
                case "ToDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label><div class="input-group date">' +
                        '<input id="filterMyRequestsToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterMyRequestsToDateError">' +
                        '<span class="input-group-addon" id="filterMyRequestsToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterMyRequestsToDateError"></div></div></div></div>';
                    break;
                case "Subject":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterMyRequestsSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Category":
                    clearFix += 3;
                    var categories = new Categories().get(window.language);
                    var followUpCategoryIndex = categories.findIndex(item => item.id == window.FollowUpCategory);
                    categories.splice(followUpCategoryIndex, 1);

                    var div = '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="categoryFilterMyRequestsContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterMyRequestsCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterMyRequestsCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++)
                    {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterMyRequestsCategoryError"></div></div></div>';
                    html += div;
                    break;
                case "OverDue":
                    clearFix += 2;
                    html += '<div class="col-lg-2 col-md-2 col-sm-6 col-xs-6"><div class="form-group"><label class="control-label"></label><div class="checkbox c-checkbox">' +
                        '<label><input tabindex="' + i + '" type="checkbox" id="chkFilterMyRequestsOverdue" name="Overdue"><span class="fa fa-check"></span>' + Resources.OverDue + '</label></div></div></div>';
                    break;
            }
            if (clearFix === 12 || clearFix === 11)
            {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 list-action-filter"><button id="btnFilterMyRequestsSearch" tabindex="5" type="button" class="btn btn-primary">' + Resources.Search + '</button>' +
            ' <button id="btnFilterMyRequestsClear" tabindex="6" type="button" class="btn btn-clear">' + Resources.Clear + '</button></div>';
        html += '</div>';
        $('#filtersContainer').html(html);

        //var clickedSearch = false;
        //$('#collapseMyRequestsIcon').click(function ()
        //{
        //    $('#collapseMyRequestsIcon').empty();
        //    if (clickedSearch)
        //    {
        //        $('#collapseMyRequestsIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
        //        $('#collapseMyRequestsPanel').attr('class', '');
        //        $('#collapseMyRequestsPanel').addClass('panel-body panel-collapse collapse in');
        //        clickedSearch = false;
        //    } else
        //    {
        //        $('#collapseMyRequestsIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
        //        $('#collapseMyRequestsPanel').attr('class', '');
        //        $('#collapseMyRequestsPanel').addClass('panel-body panel-collapse collapse');
        //        clickedSearch = true;
        //    }
        //});
        $("#btnFilterMyRequestsSearch").on('click', function ()
        {
            GridCommon.Refresh(gTableName);
        });
        $("#btnFilterMyRequestsClear").on('click', function ()
        {
            $("#cmbFilterMyRequestsCategory").val('').trigger('change');
            $("#cmbFilterMyRequestsStatus").val('').trigger('change');
            $("#txtFilterMyRequestsReferenceNumber").val('');
            $("#txtFilterMyRequestsSubject").val('');
            $("#cmbFilterMyRequestsStatus").val('').trigger('change');
            $("#chkFilterMyRequestsOverdue").prop("checked", false);
            fromDate.clear();
            toDate.clear();
            GridCommon.Refresh(gTableName);
        });
        $('#cmbFilterMyRequestsCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterMyRequestsContainer')
        });
        $("#cmbFilterMyRequestsCategory").val('').trigger('change');
        $('#cmbFilterMyRequestsStatus').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#statusFilterMyRequestsContainer')
        });
        $("#cmbFilterMyRequestsStatus").val('').trigger('change');
        var fromDate = $('#filterMyRequestsFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    maxDate: jQuery('#filterMyRequestsToDate').val() && jQuery('#filterMyRequestsToDate').val() !== "" ? jQuery('#filterMyRequestsToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterMyRequestsFromDate_img").click(function ()
        {
            fromDate.toggle();
        });
        var toDate = $('#filterMyRequestsToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    minDate: jQuery('#filterMyRequestsFromDate').val() && jQuery('#filterMyRequestsFromDate').val() !== "" ? jQuery('#filterMyRequestsFromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterMyRequestsToDate_img").click(function ()
        {
            toDate.toggle();
        });
        $('#txtFilterMyRequestsReferenceNumber').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterMyRequestsSearch').focus();
                }
                else
                {
                    $('#filterMyRequestsFromDate').focus();
                }
            }
        });
        $('#btnFilterMyRequestsClear').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterMyRequestsSearch').focus();
                }
                else
                {
                    $('#txtFilterMyRequestsReferenceNumber').focus();
                }
            }
        });
    } else
    {
        $('.searchToRemove > div:first-child').hide();
        $("#gridPanel").removeAttr("style");
        $("#gridPanel").addClass("panel-default");
    }
}
function buildColumns(gridcolumns, nodeId)
{
    gridcolumns.push({
        visible: false,
        title: Resources.Notes,
        data: "notes",
        render: function (data, type, full, meta) {
            if (Array.isArray(full.note) && full.note.length > 0) {
                // Add a bullet point before each note and join them with double new lines
                return full.note.map(n => `\u2022 ${n.notes}`).join('\n\n');
            }

            return "";
        }
    });


    //gridcolumns.push({
    //    visible: false,
    //    title: Resources.Notes,
    //    data: "notes",
    //    render: function (data, type, full, meta) {
    //        if (Array.isArray(full.note) && full.note.length > 0) {
    //            // Add a bullet point before each note and join with new lines
    //            return full.note.map(n => `\u2022 ${n.notes} \n`).join('\n');
    //        }

    //        return "";
    //    }
    //});

    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var newColomns = new Nodes().getCustomcolumns(nodeId);


    if (newColomns) {
        node.columns = newColomns.content;
    }
    var columns = node.columns !== "" && node.columns !== null ? JSON.parse(node.columns) : [];
    var columnDetails = $.grep(columns, function (element, index)
    {
        return element.isColumnDetail === true;
    });
    if (columnDetails.length > 0)
    {
        gridcolumns.push({
            "className": 'details-control',
            "orderable": false,
            "data": null,
            "defaultContent": '',
            width: '16px'
        });
    }
    gridcolumns.push(
        {
            "className": "text-left",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            data: "importanceId",
            'render': function (data, type, full, meta)
            {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var i = 0; i < importances.length; i++)
                {
                    if (importances[i].id === data)
                    {
                        let btnView = document.createElement("i");
                        btnView.setAttribute("class", "fa fa-exclamation fa-lg");
                        btnView.setAttribute("style", "color:" + importances[i].color);
                        btnView.setAttribute("title", importances[i].text);
                        return btnView.outerHTML;
                    }
                }
                return "";
            }
        });
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var customColumn = [];
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (!column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                customColumn.push({ columnName: column.name, functionName: column.customFunctionName });
                var customColumnKeyword = column.customFunctionName;
                var escapedVariable = column.customFunctionName.replace(/"/g, '&quot;');

                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                gridcolumns.push({
                    title: GetCustomAttributeTranslationByLangauge(customColumnKeyword), "orderable": false, 'defaultContent': '<div data-function="' + escapedVariable + '"></div>',
                    "createdCell": function (td, cellData, rowData, row, col)
                    {
                        var htmlCell = "";
                        var div = td.children[0];
                        var customFunctionName = div !== null && typeof div !== "undefined" ? $(div).attr("data-function") : "";
                        //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                        //{
                        //    var functionName = customFunctionName.split("(")[0];
                        //    htmlCell = eval(functionName + "(" + JSON.stringify(rowData) + ")");
                        //}

                        var customColumns = JSON.parse(rowData.documentForm.form);
                        var columnName = customColumn.find(f => f.functionName == customFunctionName).columnName;

                        var customData = customColumns[columnName]

                        if (customFunctionName.includes("()")) {
                            if (customFunctionName == "getCommitteeName()") {
                                customData = getCommitteeName(customData);
                            }

                            if (customFunctionName == "getMeetingLocation()") {
                                customData = getMeetingLocation(customData);
                            }
                        }

                        htmlCell = htmlCell == "" && customColumns != null ? (customData == undefined ? "" : customData) : htmlCell;

                        $(td).html(htmlCell);
                    }
                });
            } else
            {
                switch (column.name)
                {
                    case "Note":
                        gridcolumns.push(
                            {
                                title: Resources.Notes,
                                data: "notes",
                                render: function (data, type, full, meta) {

                                    if (Array.isArray(full.note) && full.note.length > 0) {
                                        return full.note.map(n => n.notes).join(',');
                                    }

                                    return "";
                                }
                            }

                        )
                        break;
                    case "Category":
                        gridcolumns.push({
                            title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"],
                            render: function (data, type, full, meta)
                            {
                                var categories = new Categories().get(window.language);
                                for (var i = 0; i < categories.length; i++)
                                {
                                    if (categories[i].id === data)
                                    {
                                        return categories[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "ReferenceNumber":
                        gridcolumns.push({
                            title: Resources.ReferenceNumber, data: "referenceNumber",
                            render: function (data, type, full, meta) {
                                if (full.referenceNumber != undefined && full.referenceNumber != "" && full.referenceNumber != null) {
                                    var linkedCorresRefNumList = full.referenceNumber.split(',');
                                    var html = '';
                                    $(linkedCorresRefNumList).each(function () {
                                        html += "<div>" + this + "</div>";
                                    });
                                    return html;
                                }
                                return "";
                            },
                            "orderable": false, "className": "min-max-width-50-150 copyToClipboard"
                        });
                        break;
                    case "Subject":
                        gridcolumns.push({ title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"], "className": "min-max-width-50-150" });
                        break;
                    case "SendingEntity":
                        gridcolumns.push({ title: Resources.SendingEntity, data: "sendingEntity", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"], "className": "min-max-width-50-150" });
                        break;
                    case "ReceivingEntity":
                        gridcolumns.push({ title: Resources.ReceivingEntity, data: "receivingEntity", render: $.fn.dataTable.render.text(), "orderable": false, "className": "min-max-width-50-150" });
                        break;
                    case "CreatedDate":
                        gridcolumns.push({
                            title: Resources.CreatedDate, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
                            }
                        });
                        break;
                    case "ModifiedDate":
                        gridcolumns.push({
                            title: Resources.ModifiedDate, data: "modifiedDate", "orderable": false, width: "100px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.modifiedDate, null, window.CalendarType);
                            }
                        });
                        break;
                    case "DueDate":
                        gridcolumns.push({
                            title: Resources.DueDate, data: "dueDate", "orderable": false, width: "100px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.dueDate, null, window.CalendarType);
                            }
                        });
                        break;
                    case "Purpose":
                        gridcolumns.push({
                            title: Resources.Purpose, data: "purpose", "orderable": false, width: "150px",
                            render: function (data, type, full, meta) {
                                var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                                for (let i = 0; i < purposes.length; i++) {
                                    if (purposes[i].id === full.purposeId) {
                                        return purposes[i].text;
                                    }
                                }
                                return "";
                            },
                            "orderable": false, "className": "min-max-width-150-250"
                        });
                        break;
                    case "Priority":
                        gridcolumns.push({
                            title: Resources.Priority, data: "priority", "orderable": false, width: "150px",
                            render: function (data, type, full, meta) {
                                var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                                for (let i = 0; i < priorities.length; i++) {
                                    if (priorities[i].id === full.priorityId) {
                                        return priorities[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "Privacy":
                        gridcolumns.push({
                            title: Resources.Privacy, data: "privacy", "orderable": false, width: "150px",
                            render: function (data, type, full, meta) {
                                var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                                for (let i = 0; i < privacies.length; i++) {
                                    if (privacies[i].id === full.privacyId) {
                                        return privacies[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "From":
                        gridcolumns.push({
                            title: Resources.From, "orderable": false,
                            "render": function (data, type, full, meta) {
                                var retValue = "";
                                if (full.fromStructure) {
                                    retValue += full.fromStructure;
                                }
                                if (full.fromUser) {
                                    var user = full.fromUser;
                                    retValue += retValue !== "" ? "/" + user : user;
                                }
                                return retValue;
                            }
                        });
                        break;
                    case "CreatedBy":
                        gridcolumns.push({ title: Resources.CreatedBy, data: "createdByUser", "orderable": false, width: "150px", "className": "min-max-width-150-250" });
                        break;
                    case "OpenedDate":
                        gridcolumns.push({
                            title: Resources.OpenedDate, data: "openedDate", "orderable": false, width: "150px",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.openedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                            },
                            "className": "min-max-width-150-250"
                        });
                        break;
                    case "TransferDate":
                        gridcolumns.push({
                            title: Resources.TransferDate, data: "transferDate", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.transferDate, null, window.CalendarType);
                            }
                        });
                        break;
                }
            }
        }
    }

}
function buildColumnsDetails(row, nodeId)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var columns = node.columns !== "" && node.columns !== null ? JSON.parse(node.columns) : [];
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var html = "";
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                var customColumn = column.name;
                var customColumnKeyword = column.customFunctionName;

                var htmlCell = "";
                var customFunctionName = column.customFunctionName;
                //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                //{
                //    var functionName = customFunctionName.split("(")[0];
                //    htmlCell = eval(functionName + "(" + JSON.stringify(row.data()) + ")");
                //}

                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                var customColumns = JSON.parse(row.data().documentForm.form);

                customColumns = customColumns[customColumn];

                if (customFunctionName.includes("()")) {
                    if (customFunctionName == "getCommitteeName()") {
                        customColumns = getCommitteeName(customColumns);
                    }

                    if (customFunctionName == "getMeetingLocation()") {
                        customColumns = getMeetingLocation(customColumns);
                    }
                }

                htmlCell = htmlCell == "" && customColumns != null ? (customColumns == undefined ? "" : customColumns) : htmlCell;

                html += '<tr><th style="width: 10%;padding:5px">' + (GetCustomAttributeTranslationByLangauge(customColumnKeyword)) + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + htmlCell + '</td></tr>';

            } else
            {
                switch (column.name)
                {
               
                    case "Category":
                        var category = "";
                        var categories = new Categories().get(window.language);
                        if (categories && Array.isArray(categories)) {
                            var matchedCategory = categories.find(c => c.id === row.data().categoryId);
                            category = matchedCategory ? matchedCategory.text : "";
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Category + ':</th>' +
                            '<td style="width: 85%;padding:5px;word-break: break-all;">' + category + '</td></tr>';
                        break;
                    case "ReferenceNumber":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReferenceNumber + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().referenceNumber || '') + '</td></tr>';
                        break;
                    case "Subject":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Subject + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().subject || '') + '</td></tr>';
                        break;
                    case "SendingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.SendingEntity + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().sendingEntity || '') + '</td></tr>';
                        break;
                    case "ReceivingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReceivingEntity + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().receivingEntity || '') + '</td></tr>';
                        break;
                    case "CreatedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.CreatedDate + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().createdDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "ModifiedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ModifiedDate + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().modifiedDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "DueDate":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.DueDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().dueDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "Purpose":
                        var purpose = "";
                        var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                        for (let i = 0; i < purposes.length; i++) {
                            if (purposes[i].id === row.data().purposeId) {
                                purpose = purposes[i].text;
                            }
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Purpose + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + purpose + '</td></tr>';
                        break;
                    case "Priority":
                        var priority = "";
                        var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                        for (let i = 0; i < priorities.length; i++) {
                            if (priorities[i].id === row.data().priorityId) {
                                priority = priorities[i].text;
                            }
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Priority + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + priority + '</td></tr>';
                        break;
                    case "Privacy":
                        var privacy = "";
                        var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                        for (let i = 0; i < privacies.length; i++) {
                            if (privacies[i].id === row.data().privacyId) {
                                privacy = privacies[i].text;
                            }
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Privacy + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + privacy + '</td></tr>';
                        break;
                    case "CreatedBy":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.CreatedBy + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().createdByUser || '') + '</td></tr>';
                        break;
                    case "From":
                        var from = "";
                        if (row.data().fromStructure) {
                            from += row.data().fromStructure;
                        }
                        if (row.data().fromUser) {
                            var user = row.data().fromUser;
                            from += from !== "" ? "/" + user : user;
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.From + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + from + '</td></tr>';
                        break;
                    case "TransferDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.TransferDate + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().transferDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                }
            }
        }
    }
    return html;
}
var gTableName = "grdMyRequestsItems";
var gLocked = false;
var wrapperParent;
class DocumentMyRequestsView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "myrequests", model);
        wrapperParent = model;
    }
    render()
    {
        var model = this.model;
        var self = this;
        gfirstTime = true;
        gDataChanged = true;
        gAction = "";
        gFilterChanged = false;
        gFilterStructure = "";
        gFilterFromDate = "";
        gFilterToDate = "";
        gFilterUser = "";
        gFilterOverdue = "";
        gOverdue = false;
        clear = false;
        $.fn.select2.defaults.set("theme", "bootstrap");
        $('#' + model.ComponentId + '_customizeMyRequestsNodeColomns').on('click', (function () {

            var wrapper = $(".modal-window");
            var customeModel = new CustomizeNodeColomns.CustomizeNodeColomns();
            customeModel.nodeId = model.nodeId;
            customeModel.text = "MyRequests";
            var CustomizeNodeColomnsViews = new CustomizeNodeColomns.CustomizeNodeColomnsView(wrapper, customeModel);
            CustomizeNodeColomnsViews.render();

            $("#nodeColomnsModal").parsley().reset();

            $('#nodeColomnsModal').modal('show');

            $("#nodeColomnsModal").off("hidden.bs.modal");

            $("#nodeColomnsModal").off("shown.bs.modal");

            $('#nodeColomnsModal').on('shown.bs.modal', function () {

                // document.getElementById('_cmbAttachmentUserToSign').focus();

            });

            $('#nodeColomnsModal').on('hidden.bs.modal', function () {

                $('#formPostNode').parsley().reset();

                $('#nodeColomnsModal').remove();

            });




        }));
        buildFilters(self.model.nodeId);
        Common.gridCommon();
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        //var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName);
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName).map(button => {
            button.className = (button.className || '') + ' hidden conditional-buttons';
            return button;
        });
        var exportButton = [{
            className: 'btn-sm btn-primary',

            extend: 'print',
            text: Resources.Print,
            title: function () {
                return Resources.MyRequestReport;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.MyRequestReport + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [':visible', 2]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportmyrequest.print
        },
            {
                className: 'btn-sm btn-primary',
                extend: 'excelHtml5',
                title: function () {
                    return Resources.MyRequestReport;
                },
                filename: function () {
                    var currentDate = new Date();
                    var formattedDate = currentDate.toISOString().split('T')[0];
                    var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');
                    return Resources.MyRequestReport + '_' + formattedDate + '_' + formattedTime;
                },
                exportOptions: {
                    columns: [':visible']
                },
                customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportmyrequest.excelHTML5

            },

        {
            className: 'btn-sm btn-primary',

            extend: 'pdfHtml5',
            title: function () {
                return Resources.MyRequestReport;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.MyRequestReport + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [':visible', 2]
            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportmyrequest.pdfHTML5
        }, {
            className: 'btn-sm btn-primary',
            text: Resources.CustomizeColumns,
            action: function (e, dt, node, config) {

                var wrapper = $(".modal-window");
                var customeModel = new CustomizeNodeColomns.CustomizeNodeColomns();
                customeModel.nodeId = model.nodeId;
                customeModel.text = "Inbox";
                var CustomizeNodeColomnsViews = new CustomizeNodeColomns.CustomizeNodeColomnsView(wrapper, customeModel);
                CustomizeNodeColomnsViews.render();

                $("#nodeColomnsModal").parsley().reset();

                $('#nodeColomnsModal').modal('show');

                $("#nodeColomnsModal").off("hidden.bs.modal");
                $("#nodeColomnsModal").off("shown.bs.modal");

                $('#nodeColomnsModal').on('shown.bs.modal', function () {
                });

                $('#nodeColomnsModal').on('hidden.bs.modal', function () {
                    $('#formPostNode').parsley().reset();
                    $('#nodeColomnsModal').remove();
                });
            }
        }
        ];

        var allButtons = [exportButton, ...buttons];
        var columns = [{ visible: buttons.length > 0, title: '<input id="chkAll" type="checkbox" />', width: '16px', "orderable": false, "render": function (data, type, row) { return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />"; } },
        { title: "Id", data: "id", visible: false, "orderable": false }];
        //var allButtons = buttons.concat(customButtons);
        buildColumns(columns, self.model.nodeId);
        columns.push({
            "className": "text-center",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = "";
                var text = "";
                var color = "";


                var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                for (var i = 0; i < privacies.length; i++) {
                    if (privacies[i].id === full.privacyId) {
                        color = privacies[i].color;
                        text = Resources.Privacy + ": " + privacies[i].text;
                        html += "<div class='mr-sm' title='" + text + "' style='height: 24px'>" +
                            "<i class='fa fa-shield fa-lg' style='color: " + color + "'></i>&nbsp;" +
                            "</div>";
                        break;
                    }
                }

                return html;
            }
        });
        columns.push({
            "className": "text-center",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = '';
                var categories = new Categories().get(window.language);
                var matchedCategory, categoryName;
                matchedCategory = categories.find(c => c.id === full.categoryId);

                categoryName = matchedCategory ? matchedCategory.text : "";
                if (matchedCategory.text == "Incoming") {


                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                        "<i class=' fa fa-arrow-down '></i>&nbsp;" +
                        "</div>";
                }
                else if (matchedCategory.text == "Outgoing") {


                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                        "<i class=' fa fa-arrow-up '></i>&nbsp;" +
                        "</div>";
                } else if (matchedCategory.text == "Internal") {


                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                        "<i class=' fa fa-arrows-h '></i>&nbsp;" +
                        "</div>";
                }



                return html;
            }
        });

        
      
        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta)
            {
                var html = "";
                if (full.isOverDue)
                {
                    html += "<div class='mr-sm' title='" + Resources.OverDue + "'><i class='fa fa-clock-o fa-lg text-danger'></i></div>&nbsp;";
                }
                return "<div id='divLock" + full.id + "' style='display: inline-flex;'>" + html + "</div>";
            }
        });
        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta)
            {
                let btnView = document.createElement("button");
                btnView.setAttribute("class", "btn btn-xs btn-warning view");
                btnView.setAttribute("title", Resources.View);
                btnView.setAttribute("clickattr", "openDocument(" + full.id + "," + self.model.nodeId + ")");
                btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                return btnView.outerHTML;
            }
        });
        SecurityMatrix.getRowActions(securityMatrix, columns, self.model.nodeId);
        var table = $("#" + gTableName)
            .on('draw.dt', function ()
            {
                $('#' + gTableName + " td input[type='checkbox']").on('click', function () {
                    if ($(this).is(":checked"))
                    //    $(".html5buttons").removeClass("hidden");
                        $(".conditional-buttons").removeClass("hidden");

                    else if (GridCommon.GetSelectedRows(gTableName).length == 1)
                    //    $(".html5buttons").addClass("hidden");
                    $(".conditional-buttons").addClass("hidden");

                });

                $('#' + gTableName + ' tbody tr td').each(function ()
                {
                    this.setAttribute('title', $(this).text());
                });
                GridCommon.CheckSelectedRows(gTableName);
            })
            .DataTable({
                "createdRow": function (row, data, dataIndex)
                {
                    var color = "";
                    var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                    for (var i = 0; i < priorities.length; i++)
                    {
                        if (priorities[i].id === data.priorityId)
                        {
                            color = priorities[i].color;
                        }
                    }
                    if (color !== "")
                    {
                        $(row).attr('style', "color:" + color);
                    }
                },
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/Document/ListMyRequests",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d)
                    {
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.NodeId = self.model.nodeId;
                        d.CategoryId = $("#cmbFilterMyRequestsCategory").val() !== null && typeof $("#cmbFilterMyRequestsCategory").val() !== "undefined" ? $("#cmbFilterMyRequestsCategory").val() : "0";
                        d.StatusId = $("#cmbFilterMyRequestsStatus").val() !== null && typeof $("#cmbFilterMyRequestsStatus").val() !== "undefined" ? $("#cmbFilterMyRequestsStatus").val() : "0";
                        d.ReferenceNumber = $("#txtFilterMyRequestsReferenceNumber").val() !== "" && typeof $("#txtFilterMyRequestsReferenceNumber").val() !== "undefined" ? $("#txtFilterMyRequestsReferenceNumber").val() : "";
                        d.Subject = $("#txtFilterMyRequestsSubject").val() !== "" && typeof $("#txtFilterMyRequestsSubject").val() !== "undefined" ? $("#txtFilterMyRequestsSubject").val() : "";
                        d.FromDate = $("#filterMyRequestsFromDate").val() !== "" && typeof $("#filterMyRequestsFromDate").val() !== "undefined" ? $("#filterMyRequestsFromDate").val() : "";
                        d.ToDate = $("#filterMyRequestsToDate").val() !== "" && typeof $("#filterMyRequestsToDate").val() !== "undefined" ? $("#filterMyRequestsToDate").val() : "";
                        d.Overdue = $("#chkFilterMyRequestsOverdue").is(':checked');
                        d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
                        return d;
                    }
                },
                "order": [],
                "columns": columns,
                "fnInitComplete": function (settings, json)
                {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons "B>ltrpi',
                buttons: allButtons
            });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        GridCommon.AddCheckBoxEvents(gTableName);
        $('#' + gTableName + ' tbody').on('click', ".view", function ()
        {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#' + gTableName + ' tbody').on('dblclick', 'tr', function ()
        {
            if (!gLocked)
            {
                gLocked = true;
                try
                {
                    var onclick = $(this).find(".view").attr("clickattr");
                    eval(onclick);
                } catch (e)
                {
                    gLocked = false;
                }
            }
        });
        $('#' + gTableName + ' tbody').on('click', 'td.details-control', function ()
        {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown())
            {
                row.child.hide();
                tr.removeClass('shown');
            }
            else
            {
                row.child(format(row, self.model.nodeId)).show();
                tr.addClass('shown');
            }
        });
        SecurityMatrix.InitToolbarColor();
        SecurityMatrix.InitContextMenu(securityMatrix, self.model.nodeId);
        $('#filtersContainer input').off('keydown', function (e) { });
        $('#filtersContainer input').on('keydown', function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                $("#btnFilterMyRequestsSearch").trigger('click');
            }
        });

        $('#' + gTableName + ' #chkAll').on('click', function () {
            if ($(this).is(":checked"))
                $(".conditional-buttons").removeClass("hidden");

            //    $(".html5buttons").removeClass("hidden");
            else
                $(".conditional-buttons").addClass("hidden");

        //        $(".html5buttons").addClass("hidden");
        });
        $('.toggleVIP').on('click', function () {
            if (window.InboxMode === "InboxDefault") {
                window.InboxMode = "LocalVIPView";
            } else if (window.InboxMode === "LocalInboxDefaultView") {
                window.InboxMode = "InboxVIPView";
            }
            let wrapper = $(".content-wrapper");
            let VIPmodel = new VipDocumentMyRequests.VipDocumentMyRequests();
            VIPmodel.nodeId = wrapperParent.nodeId;
            VIPmodel.delegationId = wrapperParent.delegationId;
            VIPmodel.categories = new Categories().get(window.language, wrapperParent.delegationId);
            VIPmodel.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            VIPmodel.title = $('.content-heading').text();
            let documentView = new VipDocumentMyRequests.VipDocumentMyRequestsView(wrapper, VIPmodel);
            documentView.render();

        })
    }
}
export default { DocumentMyRequests, DocumentMyRequestsView };