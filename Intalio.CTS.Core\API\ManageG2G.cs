using Intalio.Core;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using NPOI.POIFS.Crypt.Dsig;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Microsoft.Data.SqlClient;

namespace Intalio.CTS.Core.API
{
    public static class ManageG2G
    {

        #region Public Methods

        /// <summary>
        /// List G2G transfers. 
        /// Must be sent to the user or the user structures 
        /// </summary>
        /// <param name="ViewName"></param>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static (int, List<G2GListViewModel>) List(string NodeName, int startIndex, int pageSize, long userId, long structureId,
            bool isStructureReceiver, short privacyLevel, long? delegationId = null, ExpressionBuilderFilters filter = null, List<SortExpression> sortExpression = null,
            Language language = Language.EN)
        {
            string ViewName = GetViewName(NodeName);

            //  var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            var categoryIds = new List<short>();
            var filterExp = filter != null ? ExpressionBuilder.GetExpression<G2GDocumentModel>(filter, ExpressionBuilderOperator.And) : null;
            var itemList = new G2G().List(ViewName, startIndex, pageSize, userId, structureId, isStructureReceiver, privacyLevel, filterExp, sortExpression?.OrderByExpression<Intalio.CTS.Core.Model.G2GDocumentModel>());
            var g2gTotal = G2G.GetG2GCount(ViewName, userId, structureId, isStructureReceiver, privacyLevel, filterExp);

            return (g2gTotal, itemList.Select(t =>
            {
                var fromStructure = string.IsNullOrEmpty(t.TSF_FROM_STRUCT_STC_NAME) ? string.Empty : t.TSF_FROM_STRUCT_STC_NAME;
                if (language == Language.AR && !string.IsNullOrEmpty(t.TSF_FROM_STRUCT_STC_NAME_AR))
                {
                    fromStructure = t.TSF_FROM_STRUCT_STC_NAME_AR;
                }

                var toStructure = string.IsNullOrEmpty(t.TSF_TO_STRUCT_STC_NAME) ? string.Empty : t.TSF_TO_STRUCT_STC_NAME;
                if (language == Language.AR && !string.IsNullOrEmpty(t.TSF_TO_STRUCT_STC_NAME_AR))
                {
                    toStructure = t.TSF_TO_STRUCT_STC_NAME_AR;
                }

                var isOverdue = false;
                if (t.TSF_DUE_DATE.HasValue)
                {
                    if (t.TSF_CLOSURE_DATE.HasValue)
                    {
                        isOverdue = t.TSF_CLOSURE_DATE.Value > t.TSF_DUE_DATE.Value;
                    }
                    else
                    {
                        isOverdue = DateTime.Now > t.TSF_DUE_DATE.Value;
                    }
                }

                var sendingEntity = string.IsNullOrEmpty(t.DOC_SENDER_FULL_NAME) ? string.Empty : t.DOC_SENDER_FULL_NAME;
                if (language == Language.AR && !string.IsNullOrEmpty(t.DOC_SENDER_FULL_NAME_AR))
                {
                    sendingEntity = t.DOC_SENDER_FULL_NAME_AR;
                }

                var receivers = new List<ReceivingEntityModel>();
                var receivingEntity = string.Empty;
                var structureText = string.Empty;
                var entityGroupText = string.Empty;
                var docRecipientIds = t.DOC_RECIPIENT_ID?.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries).Select(item => item.Trim()).ToList() ?? new List<string>();
                var docRecipients = t.DOC_RECIPIENT?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(item => item.Trim()).ToList() ?? new List<string>();
                var docRecipientFullNamesAR = t.DOC_RECIPIENT_FULL_NAME_AR?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(item => item.Trim()).ToList() ?? new List<string>();

                for (int i = 0; i < docRecipientIds.Count; i++)
                {
                    long targetId = long.Parse(docRecipientIds[i]);
                    bool isEntityGroup = false;
                    var text = string.Empty;
                    if (language == Language.EN && i < docRecipients.Count)
                    {
                        text = docRecipients[i];
                    }
                    else if (language == Language.AR && i < docRecipientFullNamesAR.Count)
                    {
                        text = docRecipientFullNamesAR[i];
                    }

                    if (!string.IsNullOrEmpty(text))
                    {
                        structureText += text + Constants.SEPARATOR;
                    }

                    receivers.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        Text = text,
                        IsEntityGroup = isEntityGroup
                    });
                }
                receivingEntity = $"{structureText}{entityGroupText}";
                if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.EndsWith(Constants.SEPARATOR))
                {
                    receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                }

                if (ViewName.Contains("DocumentInboxPendingToReceive"))
                {
                    return new G2GListViewModel
                    {
                        Id = t.TSF_ID.Value,
                        DocumentId = t.DOC_ID,
                        Subject = t.SUBJECT,
                        CategoryId = t.DOC_CATEGORY,
                        ReferenceNumber = t.REFERENCE,
                        G2G_REF_NO = t.G2G_REF_NO ?? string.Empty,
                        IsRead = false,
                        PrivacyId = t.Id,
                        SendingEntity = sendingEntity,
                        G2GInternalId = t.G2G_Internal_ID
                    };

                }
                else
                {
                    return new G2GListViewModel
                    {
                        Id = t.TSF_ID.Value,
                        DocumentId = t.DOC_ID,
                        Subject = t.SUBJECT,
                        CategoryId = t.DOC_CATEGORY,
                        ReferenceNumber = t.REFERENCE,
                        G2G_REF_NO = t.G2G_REF_NO ?? string.Empty,
                        TransferDate = t.TSF_DATE?.ToString(Constants.DATE_FORMAT) ?? string.Empty,
                        CreatedDate = t.DOC_REGISTERDATE != null ? t.DOC_REGISTERDATE.Value.ToString(Constants.DATE_FORMAT) : null,
                        DueDate = t.TSF_DUE_DATE?.ToString(Constants.DATETIME_FORMAT24) ?? string.Empty,
                        Status = t.DOC_STATUS_ID.Value,
                        OwnerUserId = t.TSF_FROM_UserId,
                        OwnerDelegatedUserId = t.DELEGATE_FROM,
                        IsOverDue = isOverdue,
                        IsRead = false,
                        IsLocked = t.TSF_FROM_UserId.HasValue,
                        SentToUser = t.TOUSER != null ? true : false,
                        SentToStructure = !t.TSF_TO_UserId.HasValue && t.TSF_TO_STRUCTURE != null ? true : false,
                        FromUserId = t.TSF_FROM_UserId,
                        ToUserId = t.TSF_TO_UserId,
                        FromStructureId = t.TSF_FROM_STRUCTURE,
                        ToStructureId = t.TSF_TO_STRUCTURE,
                        FromUser = t.TSF_FROM_UserId.HasValue ? IdentityHelperExtension.GetFullName(t.TSF_FROM_UserId.Value, language) : string.Empty,
                        ToUser = t.TSF_TO_UserId.HasValue ? IdentityHelperExtension.GetFullName(t.TSF_TO_UserId.Value, language) : string.Empty,
                        FromStructure = fromStructure,
                        ToStructure = toStructure,
                        LockedBy = t.TSF_FROM_UserId.HasValue ? IdentityHelperExtension.GetFullName(t.TSF_FROM_UserId.Value, language) : string.Empty,
                        LockedByDelegatedUser = t.DELEGATE_FROM.HasValue ? IdentityHelperExtension.GetFullName(t.DELEGATE_FROM.Value, language) : string.Empty,
                        LockedDate = t.TSF_LOCKDATE?.ToString(Constants.DATETIME_FORMAT24) ?? string.Empty,
                        ClosedDate = t.TSF_CLOSURE_DATE?.ToString(Constants.DATETIME_FORMAT24) ?? string.Empty,
                        CreatedByUser = t.TSF_FROM_UserId.HasValue ? IdentityHelperExtension.GetFullName(t.TSF_FROM_UserId.Value, language) : string.Empty,
                        CreatedByUserId = t.TSF_FROM_UserId ?? 0,
                        Instruction = t.TSF_DESCRIPTION,
                        PurposeId = t.TSF_PURPOSE_ID ?? 0,
                        PriorityId = t.PRIORITY,
                        PrivacyId = t.Id,
                        SendingEntity = sendingEntity,
                        CreatedByStructureId = t.DOC_REGISTERBY_STRUCTURE.Value,
                        ReceivingEntityId = receivers,
                        ReceivingEntity = receivingEntity,
                        G2GInternalId = t.G2G_Internal_ID
                    };
                }
            }).ToList());

        }


        /// <summary>
        /// Get g2g transfers count. 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public static (int Total, int Today, int NodeId) GetG2GCounts(string NodeName, long userId, long structureId, long? loggedInStructureId, bool isStructureReceiver, short privacyLevel, long? delegationId = null, ExpressionBuilderFilters filter = null)
        {
            //  var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            //  var categoryIds = new List<short>();
            //if (delegation != null)
            //{
            //    userId = delegation.FromUserId;
            //    structureIds = delegation.StructureIds;
            //    categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
            //    isStructureReceiver = delegation.IsStructureReceiver;
            //    privacyLevel = delegation.PrivacyLevel;
            //    if (filter == null)
            //        filter = new ExpressionBuilderFilters();

            //    if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
            //        filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
            //    else if (!delegation.ShowOldCorespondence)
            //        filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

            //    filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
            //    filter.Add("Document.Privacy.Level", privacyLevel, Operator.LessThanOrEqualTo);
            //}

            string ViewName = GetViewName(NodeName);

            if (Core.Configuration.EnablePerStructure && loggedInStructureId != null)
                filter.Add("TSF_TO_STRUCTURE", loggedInStructureId, Operator.Equals);

            var filterExp = filter != null ? ExpressionBuilder.GetExpression<G2GDocumentModel>(filter, ExpressionBuilderOperator.And) : null;
            var g2gTotal = G2G.GetG2GCount(ViewName, userId, structureId, isStructureReceiver, privacyLevel, filterExp);
            var g2gToday = G2G.GetG2GTodayCount(ViewName, userId, structureId, isStructureReceiver, privacyLevel, filterExp);
            var g2gNodeId = ManageNode.findByName(NodeName).Id;
            return (g2gTotal, g2gToday, g2gNodeId);
        }

        public static string GenerateGuidBulkActions(string viewName, string ids, string type, long userId)
        {

            try
            {
                var connStr = Configuration.G2GConnectionString;
                string result = Guid.NewGuid().ToString();
                using (var conn = new SqlConnection(connStr))
                using (var cmd = new SqlCommand("usp_PrepareBulkActionsWithDocuments", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;

                    // Add parameters
                    cmd.Parameters.AddWithValue("@type", type);
                    cmd.Parameters.AddWithValue("@userId", userId);
                    cmd.Parameters.AddWithValue("@viewName", viewName);
                    cmd.Parameters.AddWithValue("@g2gIds", ids);
                    cmd.Parameters.AddWithValue("@guid", result);
                    cmd.Parameters.AddWithValue("@Comments", "");

                    conn.Open();

                    cmd.ExecuteNonQuery();
                }
                return result;
            }
            catch (Exception ex)
            {
                ExceptionLogger.LogException(ex.Message, null, null, "GenerateGuidBulkActions Error");
                return "";
            }

        }

        private static string GetViewName(string NodeName)
        {
            string ViewName = "";
            switch (NodeName)
            {
                case "Queued":
                    ViewName = "G2G_DocumentInboxQueued";
                    break;
                case "IncomingRecalled":
                    ViewName = "G2G_DocumentInboxIncomingRecalled";
                    break;
                case "IncomingRejected":
                    ViewName = "G2G_DocumentInboxIncomingRejected";
                    break;
                case "SentFromStructure":
                    ViewName = "G2G_DocumentInboxSent";
                    break;
                case "ReceiveOrReject":
                    ViewName = "G2G_DocumentInboxReceiveOrReject";
                    break;
                case "Recalled":
                    ViewName = "G2G_DocumentInboxRecalled";
                    break;
                case "Rejected":
                    ViewName = "G2G_DocumentInboxRejected";
                    break;
            }

            return ViewName;
        }

        #endregion

    }
}
