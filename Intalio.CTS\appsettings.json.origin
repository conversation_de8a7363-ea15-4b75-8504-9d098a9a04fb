{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Warning"
    }
  },
  "ConnectionStrings": {
    "DbConnection": "Server=D**KTOP-JVC9NJI\\SQLEXPR**S;Database=***_QDB2;MultipleActiveResultSets=true;Integrated Security=False;User=**;Password=**;",
    "IAMDbConnection": "Server=D**KTOP-JVC9NJI\\SQLEXPR**S;Database=IAM;MultipleActiveResultSets=true;Integrated Security=False;User=**;Password=**;"
    //"DbConnection": "Server=.;Database=***;MultipleActiveResultSets=true;Integrated Security=False;User=**;Password=**;"
    //"DbConnection": "User Id=***;Password=***;Data Source=localhost:1521/orcl12.intalio.com"
    //"DbConnection": "User ID=postgres;Password=P@$$w0rd;Server=localhost;Port=5432;Database=***;Integrated Security=true;Pooling=true;"
  },
  "DatabaseType": "MSSQL", //"PostgreSQL,MSSQL,Oracle"
  "IdentityServer": {
    "Url": "http://localhost:2525", //IAM url
    "ClientId": "5d2c8fa5-9f58-430c-bcf2-5f4366d425dc", // ClientId for *** App
    "ClientSecret": "d85a0d00-a065-4d8e-b001-f39d69951555" // ClientSecret for *** App
  },
  "Crawler": {
    "Urls": "", //"http://localhost:9200/,http://localhost:9300/,http://localhost:9400/", //Elasticsearch urls seperated by ,
    //"UserName": "elastic",
    //"Password": "P@$$w0rd",
    "DefaultIndexName": "intalio_cts",
    //"IsPasswordEncrypted": false, //true,false
    "ExecuteServiceEveryTimeInSeconds": 100,
    "BulkSize": 100,
    "CrawlerMaxResultWindow": 1000,
    "NumberOfReplicas": 1,
    "NumberOfShards": 1,
    "JobName": "CrawlerServiceIndexing"
  },
  "Scheduler": {
    "SchedulePollingInterval": 100, //in seconds
    "StatsPollingInterval": 10 //in seconds
  },
  "OverDue": {
    "CronExpression": "0 12 */1 * *",
    "type": "timerTrigger",
    "useMonitor": true,
    "runOnStartup": true,
    "name": "myTimer"
  },
  "SummarizeAI": {
    "CronExpression": "*/2 * * * *",
    "type": "timerTrigger",
    "useMonitor": true,
    "runOnStartup": true,
    "name": "myTimer"
  },
  "TranslationService": {
    "CronExpression": "*/2 * * * *"
  },
  "StorageServerUrl": "http://localhost:2023/", //Intalio storage server url
  "ViewerUrl": "http://localhost:8081/VIEWER",
  "DSUrl": "http://localhost:8081/DS",
  "DownloadWithAnnotation": false,
  "AiServerUrl": "https://services.ebs-gs.com/", //Intalio Ai server url
  //Intalio Ai token 
  "AIToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6Ijc2QThDNzYxNzBENEJGN0U5RTk3QzVCQUI4QzZEOUM1IiwidHlwIjoiYXQrand0In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ButRU3uK3-GAdxAMY8CxgvM2ITK5qvs5diQ5RTc7vyWwv0jEDF0EAB7HA4U_XeopJi3388YzvikUoM4ZifZ0a0iifXzkmE1wNsjhK-oqj0UnukazN_Lae4RNUqhfyRpVEZkQcS8UToERnhHUiAayONC9rrkf2wu34qaY7i2s8KivArJ7oPb8Pkog2TNGXaAC6CO5fE1RGk8uUpWxdI6FrgIpM0b64EUizs0q1fkRjnw9twMhkAeqr2VQHSBVwXPWTHZxwuchlqP1ujC7I3ny9y0JSb4dJY0sZYhd7bfPCQYO6S2Pm6NiCsriwAC7V8pLuwSZwa6h9RU9PYcyzh-qSg",
  "ChatbotServerUrl": "chatbot/api/",
  "SummarizerServerUrl": "summarizer/api/",
  "TextLabellingServerUrl": "text-labeling/api/",
  "CorrespondenceServerUrl": "correspondence/api/",
  "TranslatorServerUrl": "translator/api/",
  "ViewerClientId": "5dcd6505-0692-41fd-9a55-30bfc16d5d99",
  "AllowedOrigins": "http://corstest.mycts.com;http://localhost:8093",
  "DMSURL": "http://localhost:2021/"

}