﻿
using Aspose.Words.Layout;
using Intalio.Core;
using Intalio.Core.API;
using Intalio.Core.Utility;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using LinqKit;
using Newtonsoft.Json;
using NPOI.POIFS.Crypt.Dsig;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Threading.Tasks;
using Serilog;

namespace Intalio.CTS.Core.API
{
    public static class ManageAttachment
    {
        #region Public Methods

        public static async Task<List<AttachmentG2GModel>> getAttachmentForDocument(long documentId)
        {
            List<Attachment> attachments = new Attachment().ListByDocumentId(documentId);
            List<AttachmentG2GModel> attachmentG2G = new();
            foreach (var item in attachments)
            {
                var storageAttachment = await GetStorageAttachmentModel(item.StorageAttachmentId);
                AttachmentG2GModel attachment = new AttachmentG2GModel();
                attachment.Id = item.Id;
                attachment.Extention = item.Extension;
                attachment.IsOriginalMail = CheckIsAttachmentOriginal(item.Id);
                attachment.FolderId = item.FolderId;
                attachment.FileSize = storageAttachment.FileSize;
                attachment.Content = storageAttachment.Data;
                attachment.FileGUID = Guid.NewGuid().ToString();
                attachment.IsFile = item.FolderId.HasValue ? false : true;
                attachment.Name = item.Name + "." + item.Extension;
                //attachment.FullPath = storageAttachment.
                attachmentG2G.Add(attachment);
            }
            return attachmentG2G;
        }

        public static async Task<object> DownloadFromViewerWithAllAnnotations(long id, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId, bool logAction)
        {
            var item = new Attachment().Find(id);
            if (item != null)
            {
                var documentId = item.DocumentId.Value;
                long storageAttachmentId = item.StorageAttachmentId;
                if (ManageUserAccess.HaveAccess(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
                {
                    if (storageAttachmentId > 0)
                    {
                        string version = await GetCurrentVersionNumber(storageAttachmentId);
                        //withAnnotations=false will return only forced annotations, withAnnotations=true will return all annotations
                        string url = $"{Configuration.ViewerUrl}/api/document/{id}/version/{version}/file?withAnnotations=true&withName=true&token={Configuration.IdentityAccessToken}";
                        var fileData = await Intalio.Core.Helper.HttpGetFileAsync(url);
                        if (fileData.Success)
                        {
                            if (logAction)
                            {
                                ManageActivityLog.AddActivityLog(documentId, transferId, (int)ActivityLogs.DownloadAttachment, userId, note: fileData.Result.GetType().GetProperty("ContentDisposition").GetValue(fileData.Result).ToString());
                                return fileData.Result;
                            }
                        }
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// Add file
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="folderId"></param>
        /// <param name="categoryId"></param>
        /// <param name="file"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="structureId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        public static async Task<long?> Upload(long documentId, long? transferId, long? folderId, short categoryId, FileViewModel file, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, long? structureId = null, int roleId = 0, bool fromExport = false)
        {
            long? retvalue = null;
            var ifHaveManageCorrespondenceAccess = (structureId != null && roleId != 0) ? Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageCorrespondence") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageCorrespondence") : false;
            if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId) || ifHaveManageCorrespondenceAccess)
            {
                List<string> paths = null;
                if (folderId != null)
                {
                    paths = new List<string>();
                    paths.AddRange(ManageFolder.GetFolderPath((long)folderId));
                }
                List<string> filingPlan = ManageFilingPlan.ListFilingPlanByCategoryId(categoryId);
                var data = SetFileFormData(file, paths, documentId.ToString(), !filingPlan.IsNullOrEmpty() ? filingPlan : null);
                var retValue = await Intalio.Core.Helper.HttpMultipartPostAsync($"{Configuration.StorageServerUrl}/storage/Upload", Configuration.IdentityAccessToken, data);
                if (!retValue.Success)
                {
                    ExceptionLogger.LogException($"ManageAttachment.Upload: document: {documentId}, transfer: {(transferId != null ? transferId.Value : String.Empty)}, result: {(string)retValue.Result}", userId, null, "Storage Error");
                }
                else
                {
                    long atachmentId = Convert.ToInt64(retValue.Result);
                    Attachment attachment = new Attachment()
                    {
                        Name = file.Name,
                        Extension = file.Extension,
                        StorageAttachmentId = atachmentId,
                        FolderId = folderId,
                        DocumentId = documentId,
                        TransferId = transferId,
                        CreatedByUserId = userId
                    };
                    attachment.Insert();
                    retvalue = attachment.Id;
                    if(!fromExport)
                        ManageActivityLog.AddActivityLog(documentId, transferId, (int)ActivityLogs.AddAttachment, userId, note: $"{attachment.Name}.{attachment.Extension}");

                }
            }
            return retvalue;
        }

        /// <summary>
        /// Add original mail file
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="categoryId"></param>
        /// <param name="file"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="structureId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        public static async Task<long?> UploadOriginalMail(long documentId, long? transferId, short categoryId, FileViewModel file, long userId, List<long> structureIds,
            bool isStructureReceiver, short privacyLevel, long? delegationId, long? structureId = null, int roleId = 0)
        {
            long? itemId = null;
            var ifHaveManageCorrespondenceAccess = (structureId != null && roleId != 0) ? Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageCorrespondence") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageCorrespondence") : false;
            if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId) || ifHaveManageCorrespondenceAccess)
            {
                var attachmentId = await Upload(documentId, transferId, null, categoryId, file, userId, structureIds, isStructureReceiver, privacyLevel, delegationId, structureId, roleId);
                if (!attachmentId.IsNull())
                {
                    Document item = new Document().Find(documentId);
                    item.AttachmentId = attachmentId;
                    item.UpdateAttachmentId();
                    itemId = attachmentId;
                }
                new EventReceivers().OnOriginalDocumentUploaded(file, documentId);
            }
            return itemId;
        }

        /// <summary>
        /// Add file
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="folderId"></param>
        /// <param name="categoryId"></param>
        /// <param name="file"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="structureId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        public static async Task<long?> UploadAttachment(long documentId, long? transferId, long? folderId, short categoryId, FileViewModel file, long userId, List<long> structureIds,
            bool isStructureReceiver, short privacyLevel, long? delegationId, long? structureId = null, int roleId = 0,bool fromRejectedDocument=false)
        {
            long? itemId = null;
            var ifHaveManageCorrespondenceAccess = (structureId != null && roleId != 0) ? Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageCorrespondence") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageCorrespondence") : false;
            if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId, fromRejectedDocument) || ifHaveManageCorrespondenceAccess)
            {
                var attachmentId = await Upload(documentId, transferId, folderId, categoryId, file, userId, structureIds, isStructureReceiver, privacyLevel, delegationId, structureId, roleId);
                itemId = attachmentId;
                new EventReceivers().OnFileUploaded(file, documentId);
            }
            return itemId;
        }

        /// <summary>
        /// List files
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="language"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<List<TreeNode>> List(long documentId, long? transferId, long userId, int roleId, long? structureId, List<long> structureIds, bool isStructureReceiver,
            short privacyLevel, long? delegationId = null, long? parentDocumentId = null, Language language = Language.EN)
        {
            List<TreeNode> treeNodes = new List<TreeNode>();
            Document document = new Document().FindIncludeAll(documentId);
            var ifHaveManageCorrespondenceAccess = Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageCorrespondence") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageCorrespondence");
            var ifHaveFollowUpOnEmployeesAccess = Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageStructureUsersCorrespondences") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageStructureUsersCorrespondences");

            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId)
                || (parentDocumentId != null && document.LinkedDocumentDocument.Count > 0 &&
                ManageUserAccess.HaveAccess(parentDocumentId.Value, userId, structureIds, isStructureReceiver, privacyLevel, delegationId) || ifHaveManageCorrespondenceAccess|| ifHaveFollowUpOnEmployeesAccess)
                )
            {
                List<Folder> folders = (await new Folder().ListAsync(documentId)).Where(e => e.ParentId == null).ToList();
                treeNodes = ManageFolder.GetFolderTreeNodes(folders, transferId, userId, delegationId, structureIds);
                List<Attachment> rootAttachments = await new Attachment().ListTreeRootAttachmentsAsync(documentId, userId, structureIds);
                List<TreeNode> attachmentNodes = GetAttachmentTreeNodes(rootAttachments, transferId, userId, delegationId, document.IsSigned);

                TreeNode originalMail = document.AttachmentId == null ? null : attachmentNodes.FirstOrDefault(t => t.Id == $"file_{document.AttachmentId}");
                if (originalMail != null)
                {
                    originalMail.ParentId = "folder_originalMail";
                    originalMail.State.Selected = Configuration.OpenCorrespondenceMode == "OpenCorrespondenceDefault";
                    attachmentNodes.Remove(originalMail);
                }
                treeNodes.Add(new TreeNode
                {
                    Id = "folder_originalMail",
                    Text = TranslationUtility.Translate("OriginalDocument", language),
                    Title = TranslationUtility.Translate("OriginalDocument", language),
                    Type = ((int)NodeType.Folder).ToString(),
                    ParentId = "#",
                    Icon = Helper.GetIcon("folder"),
                    Children = originalMail != null ? new List<TreeNode>().Append(originalMail) : null,
                    State = new TreeNodeState()
                    {
                        Disabled = false,
                        Opened = true,
                        Selected = false
                    }
                });
                treeNodes.AddRange(attachmentNodes);
                ManageActivityLog.AddFullActivityLog(documentId, transferId, (int)ActivityLogs.ViewAttachments, userId, "", "");
            }
            return treeNodes;
        }

        public static async Task<(int, List<TreeNode>)> ListAttachments(int draw, int start, int length, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver,
            short privacyLevel, long? delegationId = null, long? parentDocumentId = null, Language language = Language.EN)
        {
            List<TreeNode> attachmentNodes = new List<TreeNode>();
            Document document = new Document().FindIncludeAll(documentId);
            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId)
                || (parentDocumentId != null && document.LinkedDocumentDocument.Count > 0 && ManageUserAccess.HaveAccess(parentDocumentId.Value, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
                )
            {
                List<Attachment> rootAttachments = await new Attachment().ListTreeRootAttachmentsAsync(documentId, userId, structureIds);
                attachmentNodes = GetAttachmentTreeNodes(rootAttachments, transferId, userId, delegationId, document.IsSigned);

                ManageActivityLog.AddFullActivityLog(documentId, transferId, (int)ActivityLogs.ViewAttachments, userId, "", "");
            }
            return (attachmentNodes.Count, attachmentNodes);
        }

        /// <summary>
        /// Rename file
        /// </summary>
        /// <param name="model"></param>
        /// <param name="language"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <returns></returns>
        public static async Task<string> Rename(AttachmentNodeModel model, Language language, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel)
        {
            string retValue = string.Empty;
            if (ManageUserAccess.HaveAccess(model.DocumentId, model.TransferId, userId, structureIds, isStructureReceiver, privacyLevel, model.DelegationId))
            {
                if (model.Id != null)
                {
                    var item = new Attachment().Find(model.Id.Value);

                    if (item != null && ManageUserAccess.HaveEditAccess(item.CreatedByUserId, item.CreatedByDelegatedUserId, item.TransferId, model.TransferId, userId, model.DelegationId))
                    {
                        var isUnique = CheckUnique(model.Id, model.ParentId, model.DocumentId, model.Name, item.Extension);
                        if (!isUnique.Item1)
                        {
                            if (item.IsLocked.HasValue && item.IsLocked.Value)
                            {
                                retValue = TranslationUtility.Translate("FileIsCheckedout", language);

                            }
                            var storageretValue = await Intalio.Core.Helper.HttpPostAsync($"{Configuration.StorageServerUrl}/storage/RenameFile?fileId={item.StorageAttachmentId}&newName={model.Name}", Configuration.IdentityAccessToken);
                            if (!storageretValue.Success)
                            {
                                ExceptionLogger.LogException($"ManageAttachment.Rename: document: {model.DocumentId}, transfer: {(model.TransferId != null ? model.TransferId.Value : String.Empty)}, result: {(string)storageretValue.message}", userId, null, "Storage Error");
                                //ExceptionLogger.LogException(storageretValue.message, userId, null, "Storage Error");
                            }
                            else
                            {
                                var originalFileValue = JsonConvert.SerializeObject((AttachmentModel)item);
                                item.Name = model.Name;
                                var NeworiginalFileValue = JsonConvert.SerializeObject((AttachmentModel)item);
                                item.Update();
                                ManageActivityLog.AddActivityLog(model.DocumentId, model.TransferId, (int)ActivityLogs.RenameAttachment, userId, originalValue: originalFileValue, newOriginalValue: NeworiginalFileValue, note: $"{model.Name}.{item.Extension}");
                            }
                        }
                        else
                        {
                            retValue = TranslationUtility.Translate("SameFileNameExists", language);
                        }
                    }
                    else
                    {
                        retValue = TranslationUtility.Translate("NoPermission", language);
                    }
                }
            }
            else
            {
                retValue = TranslationUtility.Translate("NoPermission", language);
            }
            return retValue;
        }

        /// <summary>
        /// Check unique file
        /// </summary>
        /// <param name="id"></param>
        /// <param name="folderId"></param>
        /// <param name="documentId"></param>
        /// <param name="name"></param>
        /// <param name="extension"></param>
        /// <returns></returns>
        public static (bool,long) CheckUnique(long? id, long? folderId, long documentId, string name, string extension)
        {
            return new Attachment().CheckUnique(id, folderId, documentId, name, extension);
        }

        /// <summary>
        /// Delete file
        /// </summary>
        /// <param name="id"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<bool> Delete(long id, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver,
            short privacyLevel, long? delegationId)
        {
            bool retValue = false;
            if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                var item = new Attachment().Find(id);
                if(item.DocumentLockId.HasValue)
                {
                    return false;
                }
                if (item != null && ManageUserAccess.HaveEditAccess(item.CreatedByUserId, item.CreatedByDelegatedUserId, item.TransferId, transferId, userId, delegationId))
                {
                    var storageretValue = await Intalio.Core.Helper.HttpPostAsync($"{Configuration.StorageServerUrl}/storage/DeleteFile?fileId={item.StorageAttachmentId}", Configuration.IdentityAccessToken);
                    if (!storageretValue.Success)
                    {
                        ExceptionLogger.LogException($"ManageAttachment.Delete: document: {documentId}, transfer: {(transferId != null ? transferId.Value : String.Empty)}, message: {storageretValue.message}", userId, null, "Storage Error");
                        //ExceptionLogger.LogException(storageretValue.message, userId, null, "Storage Error");
                    }
                    else
                    {
                        item.Delete();
                        retValue = true;
                        ManageActivityLog.AddActivityLog(documentId, transferId, (int)ActivityLogs.DeleteAttachment, userId, originalValue: JsonConvert.SerializeObject((AttachmentModel)item), note: $"{item.Name}.{item.Extension}");
                    }
                }
            }
            return retValue;
        }

        /// <summary>
        /// Replace file
        /// </summary>
        /// <param name="id"></param>
        /// <param name="file"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="isOriginalMail"></param>
        /// <param name="structureId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        public static async Task<(long? Id, string Message)> Replace(long id, FileViewModel file, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver,
            short privacyLevel, long? delegationId, bool isOriginalMail, long? structureId = null, int roleId = 0)
        {
            long itemId = 0;
            var ifHaveManageCorrespondenceAccess = (structureId != null && roleId != 0) ? Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageCorrespondence") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageCorrespondence") : false;
            if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId) || ifHaveManageCorrespondenceAccess)
            {
                var item = new Attachment().FindIncludeDocument(id);
                if (item != null && item.Document != null && (ManageUserAccess.HaveEditAccess(item.CreatedByUserId, item.CreatedByDelegatedUserId, item.TransferId, transferId, userId, delegationId) || ifHaveManageCorrespondenceAccess))
                {

                    if (item.Document.AttachmentId == id && (item.Document.IsSigned.HasValue ? item.Document.IsSigned.Value : false))
                    {
                        return (itemId, "CantReplaceSignedDocument");
                    }

                    if (item.IsLocked.HasValue && item.IsLocked.Value)
                    {
                        return (itemId, "FileIsCheckedout");
                    }

                    if (item.DocumentLockId.HasValue)
                    {
                        return (itemId, "CorrespondenceIsLocked");
                    }
                    var urlReplace = "Replace";
                    if (new Document().CheckDraft(documentId) && !Configuration.EnableDraftVersioning)
                    {
                        urlReplace = "ReplaceNoVersioning";
                    }
                    var data = SetFileFormData(file);
                    var retValue = await Intalio.Core.Helper.HttpMultipartPostAsync($"{Configuration.StorageServerUrl}/storage/{urlReplace}?fileId={item.StorageAttachmentId}", Configuration.IdentityAccessToken, data);
                    if (!retValue.Success)
                    {
                        ExceptionLogger.LogException($"ManageAttachment.Replace: document: {documentId}, transfer: {(transferId != null ? transferId.Value : String.Empty)}, AttachmentId: {id}, StorageId: {item.StorageAttachmentId}  message: {(string)retValue.Result}", userId, null, "Storage Error");
                        //ExceptionLogger.LogException((string)retValue.Result, userId, null, "Storage Error");
                    }
                    else
                    {
                        var oldFile = (AttachmentModel)item;
                        if (item.Name.ToLower() != file.Name.ToLower() || item.Extension.ToLower() != file.Extension.ToLower())
                        {
                            item.Name = file.Name;
                            item.Extension = file.Extension;
                        }
                        item.Update();
                        ManageActivityLog.AddActivityLog(documentId, transferId, (int)ActivityLogs.ReplaceAttachment, userId, originalValue: JsonConvert.SerializeObject(oldFile), newOriginalValue: JsonConvert.SerializeObject((AttachmentModel)item));
                    }
                    itemId = item.Id;
                    if (isOriginalMail)
                    {
                        new EventReceivers().OnOriginalDocumentReplaced(file, documentId);
                    }
                    else
                    {
                        new EventReceivers().OnFileReplaced(file, documentId);
                    }
                }
            }
            return (itemId, string.Empty);
        }

        /// <summary>
        /// Get file
        /// From storage
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static async Task<Intalio.Core.Model.StorageAttachmentModel> GetStorageAttachmentModel(long id)
        {
            return await Intalio.Core.Helper.GetFileFromStorageAsync($"{Configuration.StorageServerUrl}/storage/GetFile?fileId={id}", Configuration.IdentityAccessToken);
        }

        /// <summary>
        /// Download file
        /// </summary>
        /// <param name="id"></param>
        /// <param name="transferId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="logAction"></param>
        /// <returns></returns>
        public static async Task<Intalio.Core.Model.AttachmentViewModel> Download(long id, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId, bool logAction)
        {
            Intalio.Core.Model.AttachmentViewModel retValue = null;
            var item = new Attachment().Find(id);
            if (item != null)
            {
                var documentId = item.DocumentId.Value;
                if (ManageUserAccess.HaveAccess(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
                {
                    var storageAttachment = await GetStorageAttachmentModel(item.StorageAttachmentId);
                    if (storageAttachment != null)
                    {
                        retValue = new Intalio.Core.Model.AttachmentViewModel();
                        retValue.Name = item.Name;
                        retValue.Extension = item.Extension;
                        retValue.ContentType = storageAttachment.ContentType;
                        retValue.Data = storageAttachment.Data;
                        if (logAction)
                        {
                            ManageActivityLog.AddActivityLog(documentId, transferId, (int)ActivityLogs.DownloadAttachment, userId, originalValue: JsonConvert.SerializeObject((AttachmentModel)item), note: $"{item.Name}.{item.Extension}");
                        }
                    }
                }
            }
            return retValue;
        }

        /// <summary>
        /// Download file from the Viewer
        /// </summary>
        /// <param name="id"></param>
        /// <param name="transferId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="logAction"></param>
        /// <param name="withAnnotations"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        public static async Task<object> DownloadFromViewer(long id, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId, bool logAction, bool withAnnotations = false, string version = null)
        {
            var item = new Attachment().Find(id);
            if (item != null)
            {
                var documentId = item.DocumentId.Value;
                long storageAttachmentId = item.StorageAttachmentId;
                if (ManageUserAccess.HaveAccess(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
                {
                    if (storageAttachmentId > 0)
                    {
                        version = version ?? await GetCurrentVersionNumber(storageAttachmentId);
                        bool viewResult = await OpenFileInViewer(id, documentId, transferId, version, delegationId, false);
                        //withAnnotations=false will return only forced annotations, withAnnotations=true will return all annotations
                        string url = $"{Configuration.ViewerUrl}/api/document/{id}/version/{version}/file?withAnnotations={(withAnnotations ? "true" : "false")}&withName=true&token={Configuration.IdentityAccessToken}";
                        var fileData = await Intalio.Core.Helper.HttpGetFileAsync(url);
                        if (fileData.Success)
                        {
                            if (logAction)
                            {
                                ManageActivityLog.AddActivityLog(documentId, transferId, (int)ActivityLogs.DownloadAttachment, userId, note: fileData.Result.GetType().GetProperty("ContentDisposition").GetValue(fileData.Result).ToString());
                            }
                            return fileData.Result;
                        }
                    }
                }
            }
            return null;
        }
        public static async Task<bool> OpenFileInViewer(long fileId, long documentId, long? transferId, string version, long? delgationId = null, bool isDraft = false)
        {
            string viewerUrl = $"{Configuration.ViewerUrl}/api/document/{fileId}/version/{version}/details?&ctsDocumentId={documentId}&ctsTransferId={transferId}&delegationId={(delgationId == null ? "null" : delgationId)}&isDraft={(isDraft ? "true" : "false")}&isCustomMode=false";
            var result = await Intalio.Core.Helper.HttpGetAsync<object>(viewerUrl, Configuration.IdentityAccessToken);
            if (result != null && ((Newtonsoft.Json.Linq.JContainer)result)?.Count > 0)
                return true;
            return false;
        }

        /// <summary>
        /// Check if transfer has locked attachments
        /// </summary>
        /// <param name="transferId"></param>
        /// <returns></returns>
        public static bool TransferHasLockedAttachments(long transferId)
        {
            return new Transfer().CheckHasLockedAttachments(transferId);
        }

        /// <summary>
        /// Check if document has locked attachments
        /// </summary>
        /// <param name="documentId"></param>
        /// <returns></returns>
        public static bool DocumentHasLockedAttachments(long documentId)
        {
            return new Document().CheckHasLockedAttachments(documentId);
        }

        /// <summary>
        /// Checkout attachment
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="attachmentId"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="delegationId"></param>
        /// <param name="isDraft"></param>
        /// <returns></returns>
        public static bool Checkout(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long attachmentId, long documentId, long? transferId, long? delegationId, bool isDraft)
        {
            Log.Information($"test checkout {userId} {structureIds[0]} {isStructureReceiver} {privacyLevel} {attachmentId} {documentId} {(transferId != null ? transferId.Value : "null")}");
            var retValue = false;
            bool hasAccess = false;
            Document document = null;
            if (transferId.IsNull())
            {
                if(delegationId != null && isDraft)
                {
                    var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
                    if(delegation!= null)
                    {
                        userId = delegation.FromUserId;
                    }
                }
                //if (isDraft && new Document().HasDraftAccess(documentId, userId))
                if (isDraft && new Document().HasDraftAccess(documentId, userId, structureIds[0]))
                {
                    hasAccess = true;
                }
            }
            else
            {
                Log.Information($"test checkout: transfer not null");
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
                Transfer transfer = new Transfer().FindIncludeDocumentAndPrivacy(transferId.Value);

                if (delegation != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                    var delegationStartDate = delegation.FromDate;
                    if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                    {
                        delegationStartDate = delegation.StartDate.Value;
                    }
                    if (delegation.ShowOldCorespondence && delegation.StartDate == null)
                    {
                        if (transfer != null && transfer.CreatedDate >= delegation.ToDate)
                        {
                            transfer = null;
                            document = null;
                            hasAccess = false;
                        }
                    }
                    else
                    {
                        if (!(transfer != null && transfer.CreatedDate >= delegationStartDate && transfer.CreatedDate <= delegation.ToDate))
                        {
                            transfer = null;
                            document = null;
                            hasAccess = false;
                        }
                    }
                }

                //Log.Information($"{TranslationUtility.Translate("Testcheckout:beforecheckaccesscondition", Language)}: " +
                //                $"{(transfer != null ? TranslationUtility.Translate("notnull", Language) : TranslationUtility.Translate("null", Language))}, " +
                //                $"{(!transfer.ClosedDate.HasValue ? TranslationUtility.Translate("notransfercloseddate", Language) : TranslationUtility.Translate("transferhascloseddate", Language))}, " +
                //                $"{(transfer.ToUserId.HasValue ? TranslationUtility.Translate("transfertouser", Language) + ": " + transfer.ToUserId.ToString() : TranslationUtility.Translate("nottransfertouser", Language))}, " +
                //                $"{(structureIds.Contains(transfer.ToStructureId.Value) ? TranslationUtility.Translate("structureIdscontainstoStructure", Language) : TranslationUtility.Translate("structureidsdoesntcontaintoStructure", Language))}");
                Log.Information($"test checkout: before check access condition: {(transfer != null ? "not null" : "null")}, {(!transfer.ClosedDate.HasValue ? "no transfer closeddate" : "transfer has closed date")}, {(transfer.ToUserId.HasValue ? "transfer to user: " + transfer.ToUserId.ToString() : "not transfer to user")}, {(structureIds.Contains(transfer.ToStructureId.Value) ? "structureIds contains toStructure" : "structureids doesnt contain toStructure")} ");
                if (transfer != null && (!transfer.ClosedDate.HasValue || (transfer.ClosedDate.HasValue && transfer.IsSigning)) && 
                        (
                        transfer.ToUserId == userId 
                        || (!transfer.ToUserId.HasValue && structureIds.Contains(transfer.ToStructureId.Value) && isStructureReceiver && transfer.Document.Privacy.Level <= privacyLevel)
                        )
                    )
                {
                    Log.Information($"test checkout: access condition success");
                    document = transfer.Document;
                    hasAccess = true;
                }
                else
                {
                    Log.Information($"test checkout: access condition fail");
                }
            }
            if (hasAccess)
            {
                var attachment = new Attachment().Find(attachmentId);
                if (attachment.DocumentLockId.HasValue)
                {
                    Log.Information($"test checkout: fail attachment.DocumentLockId.HasValue");
                    return false;
                }
                if (attachment != null && attachment.IsLocked != true)
                {
                    document = document == null ? new Document().Find(documentId) : document;
                    if (document.AttachmentId.HasValue && document.AttachmentId.Value == attachment.Id)
                    {
                        Log.Information($"test checkout: document.AttachmentId.HasValue && document.AttachmentId.Value == attachment.Id condition success");
                        attachment.IsLocked = true;
                        attachment.LockedByUserId = userId;
                        attachment.UpdateLockAndLockedByUserId();
                        if (document.IsLocked != true)
                        {
                            document.IsLocked = true;
                            document.UpdateLock();
                        }
                        retValue = true;
                    }
                    else if (Configuration.AttachmentEditable)
                    {
                        Log.Information($"test checkout: Configuration.AttachmentEditable condition success");
                        attachment.IsLocked = true;
                        attachment.LockedByUserId = userId;
                        attachment.UpdateLockAndLockedByUserId();
                        if (document.IsLocked != true)
                        {
                            document.IsLocked = true;
                            document.UpdateLock();
                        }
                        retValue = true;
                    }
                    else
                    {
                        Log.Information($"test checkout: fail document.AttachmentId.HasValue && document.AttachmentId.Value == attachment.Id");
                    }
                }
                else
                {
                    Log.Information($"test checkout: fail attachment != null && attachment.IsLocked != true");
                }
            }
            return retValue;
        }

        /// <summary>
        /// CheckIn attachment
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="attachmentId"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="delegationId"></param>
        /// <param name="isDraft"></param>
        /// <returns></returns>
        public static bool CheckIn(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long attachmentId, long documentId, long? transferId, long? delegationId, bool isDraft)
        {
            var retValue = false;
            bool hasAccess = false;
            Document document = null;
            if (transferId.IsNull())
            {
                if (delegationId != null && isDraft)
                {
                    var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
                    if (delegation != null)
                    {
                        userId = delegation.FromUserId;
                    }
                }

                //if (isDraft && new Document().HasDraftAccess(documentId, userId))
                if (isDraft && new Document().HasDraftAccess(documentId, userId, structureIds[0]))
                {
                    hasAccess = true;
                }
            }
            else
            {
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
                Transfer transfer = new Transfer().FindIncludeDocumentAndPrivacy(transferId.Value);
                if (delegation != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                    var delegationStartDate = delegation.FromDate;

                    if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                    {
                        delegationStartDate = delegation.StartDate.Value;
                    }
                    if (delegation.ShowOldCorespondence && delegation.StartDate == null)
                    {
                        if (transfer != null && transfer.CreatedDate >= delegation.ToDate)
                        {
                            transfer = null;
                            document = null;
                            hasAccess = false;
                        }
                    }
                    else
                    {
                        if (transfer != null && transfer.CreatedDate <= delegationStartDate && transfer.CreatedDate >= delegation.ToDate)
                        {
                            transfer = null;
                            document = null;
                            hasAccess = false;
                        }
                    }

                }
                if (transfer != null && (!transfer.ClosedDate.HasValue || (transfer.ClosedDate.HasValue && transfer.IsSigning)) && (transfer.ToUserId == userId || (!transfer.ToUserId.HasValue && structureIds.Contains(transfer.ToStructureId.Value) && isStructureReceiver && transfer.Document.Privacy.Level <= privacyLevel)))
                {
                    document = transfer.Document;
                    hasAccess = true;
                }
            }
            if (hasAccess)
            {
                var attachment = new Attachment().Find(attachmentId);
                document = document == null ? new Document().Find(documentId) : document;
                if (attachment != null && attachment.IsLocked == true)
                {
                    if (document.AttachmentId.HasValue && document.AttachmentId.Value == attachment.Id)
                    {
                        attachment.IsLocked = false;
                        attachment.LockedByUserId = null;
                        attachment.UpdateLockAndLockedByUserId();
                        if (!Configuration.AttachmentEditable || (Configuration.AttachmentEditable && !new Attachment().CheckDocumentAttachmentsIsLocked(documentId)))
                        {
                            document.IsLocked = false;
                            document.UpdateLock();
                        }
                        retValue = true;
                    }
                    else if (Configuration.AttachmentEditable)
                    {
                        attachment.IsLocked = false;
                        attachment.LockedByUserId = null;
                        attachment.UpdateLockAndLockedByUserId();
                        if (!new Attachment().CheckDocumentAttachmentsIsLocked(documentId))
                        {
                            document.IsLocked = false;
                            document.UpdateLock();
                        }
                        retValue = true;
                    }
                }
                else if (attachment != null && (document.ReferenceNumber == null || document.ReferenceNumber == ""))
                {
                    var documentWithCategory = new Document().FindIncludeCategory(documentId);
                    if (documentWithCategory.Category.CategoryReferenceNumberTypeId == 2)
                    {
                        retValue = true;
                    }
                }
            }
            return retValue;
        }

        /// <summary>
        /// Get attachment data
        /// From storage
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="version"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<byte[]> GetAttachmentData(long fileId, string version, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, int roleId = 0)
        {
            if (ManageUserAccess.HaveAccess(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId) || ManageUserAccess.hasManageCorrespondenceAccess(userId, structureIds[0], roleId))
            {
                var item = new Attachment().FindIncludeAttachmentSecurity(fileId);
                if (item != null && ManageAttachmentSecurity.ChackAttachmentSecurity(item, userId, structureIds))
                {
                    return await GetFileData(item.StorageAttachmentId, version);
                }
            }
            return null;
        }

        /// <summary>
        /// Get attachment metdata
        /// From storage
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="version"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<FileViewModel> GetAttachmentInfo(long fileId, string version, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, int roleId,long structureId, long? delegationId = null)
        {
            if (documentId == 0)
            {
                documentId = new Attachment().FindIncludeDocument(fileId).DocumentId.GetValueOrDefault();
            }
            var ifHaveManageCorrespondenceAccess = ManageUserAccess.hasManageCorrespondenceAccess(userId, structureIds[0], roleId);
            var ifHaveFollowUpOnEmployeesAccess = Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageStructureUsersCorrespondences") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageStructureUsersCorrespondences");

            var hasAccess = ManageUserAccess.HaveAccess(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId);

            if (!hasAccess)
            {
                Document document = new Document().FindIncludeAll(documentId);
                foreach (var link in document.LinkedDocumentDocument)
                {
                    hasAccess = hasAccess || ManageUserAccess.HaveAccess(link.LinkedDocumentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId);
                }
            }

            if (hasAccess|| ifHaveManageCorrespondenceAccess|| ifHaveFollowUpOnEmployeesAccess)
            {
                var item = new Attachment().FindIncludeAttachmentSecurity(fileId);
                if (item != null && ManageAttachmentSecurity.ChackAttachmentSecurity(item, userId, structureIds))
                {
                    return await GetFileInfo(item.StorageAttachmentId, version);
                }
            }
            return null;
        }

        /// <summary>
        /// Convert original attachment to pdf
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <remarks>
        /// A new version of file will be create even when the status of the document is draft.
        /// </remarks>
        /// <returns></returns>
        public static async Task<(bool Converted, string Message)> ConvertToPdf(long id, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null)
        {
            bool retValue = false;
            var item = new Attachment().Find(id);
            if (item != default(Attachment) && Intalio.Core.Helper.IsWord($"{item.Name}.{item.Extension}") && ManageUserAccess.HaveAccess(item.DocumentId.Value, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                if (ManageDocument.CheckOriginalDocumentLocked(item.DocumentId.Value))
                {
                    return (false, "OriginalFileInUse");
                }
                else
                {
                    var result = await new Document().GetOriginalAttachmentIdAndCategoryId(item.DocumentId.Value);
                    if (!result.IsCompleted && result.AttachmentId == item.Id && ManageCategory.CheckCategoryByFileOrTemplate(result.CategoryId))
                    {
                        var originalFileValue = $"{item.Name}.{item.Extension}";
                        await ManageBookmark.FindReplaceBookmarkAndAttachment(item.Id, item.StorageAttachmentId, item.DocumentId.Value, userId, convertToPdf: true, replaceVersion: true);
                        ManageActivityLog.AddActivityLog(item.DocumentId.Value, null, (int)ActivityLogs.ConvertToPdf, userId, originalValue: originalFileValue, newOriginalValue: $"{item.Name}.pdf");
                        retValue = true;
                    }
                    else
                    {
                        return (false, "GenerateLetterNotComplete");
                    }
                }
            }
            return (retValue, string.Empty);
        }

        /// <summary>
        /// validate generate letter
        /// </summary>
        /// <param name="id"></param>
        /// <param name="generateReceivingEntity"></param>
        /// <param name="generateCarbonCopy"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<(bool IsValid, string Message)> GenerateLetterValidation(long id, bool generateReceivingEntity, bool generateCarbonCopy, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null)
        {
            if (!generateReceivingEntity && !generateCarbonCopy)
            {
                return (false, "SelectReceivingEntityOrCarbonCopy");
            }
            var item = new Attachment().Find(id);
            return await GenerateLetterValidater(item, generateReceivingEntity, generateCarbonCopy, userId, structureIds, isStructureReceiver, privacyLevel, delegationId);
        }



        /// <summary>
        /// Generate letter for each receiving entity and carbon copy
        /// </summary>
        /// <param name="id"></param>
        /// <param name="generateReceivingEntity"></param>
        /// <param name="generateCarbonCopy"></param>
        /// <param name="convertToPdf"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="isPreview"></param>
        /// <remarks>
        /// Generate word document for each receiving entity and carbon copy.<br/><br/>
        /// Document status: <br/>
        /// <li><b>Draft</b> : replace all bookmarks</li>
        /// <li><b>In Progress</b> : replace receiving entity and carbon copy bookmarks</li>
        /// Merge all documents generated into one word document
        /// Convert the document to pdf in case <b>convertToPdf</b> is true and replace the file in storage in case <b>isPreview</b> is false
        /// A new version of file will be create even when the status of the document is draft.
        /// </remarks>
        /// <returns></returns>
        public static async Task<(bool Generated, FileViewModel FileAttachment, string Message)> GenerateLetter(long id, bool generateReceivingEntity, bool generateCarbonCopy, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, bool convertToPdf = true, bool isPreview = false)
        {
            if (!generateReceivingEntity && !generateCarbonCopy)
            {
                return (false, null, "SelectReceivingEntityOrCarbonCopy");
            }
            var item = new Attachment().Find(id);
            var result = await GenerateLetterValidater(item, generateReceivingEntity, generateCarbonCopy, userId, structureIds, isStructureReceiver, privacyLevel, delegationId);
            if (result.IsValid)
            {
                var storageAttachmentModel = await GetStorageAttachmentModel(item.StorageAttachmentId);
                if (storageAttachmentModel.Data?.Length > 0)
                {
                    FileViewModel fileAttachment = new FileViewModel();
                    fileAttachment.Name = Intalio.Core.Helper.GetFileName(storageAttachmentModel.Name);
                    fileAttachment.ContentType = convertToPdf == true ? "application/pdf" : storageAttachmentModel.ContentType;
                    fileAttachment.Extension = convertToPdf == true ? "pdf" : storageAttachmentModel.Extension;
                    var bytes = ManageBookmark.GenerateLetter(storageAttachmentModel.Data, item.DocumentId.Value, generateReceivingEntity, generateCarbonCopy, convertToPdf);
                    fileAttachment.Data = bytes;
                    fileAttachment.FileSize = bytes.Length;
                    if (!isPreview)
                    {
                        await Replace(item.Id, fileAttachment, item.DocumentId.Value, null, userId, true);
                        ManageActivityLog.AddActivityLog(item.DocumentId.Value, null, (int)ActivityLogs.GenerateLetter, userId, originalValue: $"{item.Name}.{item.Extension}", newOriginalValue: $"{item.Name}.pdf");
                        return (true, null, result.Message);
                    }
                    return (true, fileAttachment, result.Message);
                }
            }
            return (false, null, result.Message);
        }
        public static async Task<(bool Generated, FileViewModel FileAttachment, string Message)> PreviewAttachment(long attachmentId, long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId, long signature, bool GenerateReference)
        {
            var item = new Attachment().FindIncludeDocument(attachmentId);
            string reference = null;
            if (GenerateReference)
            {
                reference = new CategoryReferenceCounter().Generate(item.Document.CategoryId, userId, item.Document.CreatedByStructureId, item.Id, item.TransferId, Language.EN,true).Reference;
            }

            //var result = await PreviewAttachmentValidator(item, userId, structureIds, isStructureReceiver,privacyLevel, delegationId);
            //if (result.IsValid)
            //{
            bool convertToPdf = true;
            byte[] bytes = null;
            var storageAttachmentModel = await GetStorageAttachmentModel(item.StorageAttachmentId);
            if (storageAttachmentModel.Data?.Length > 0)
            {
                FileViewModel fileAttachment = new FileViewModel();
                fileAttachment.Name = Intalio.Core.Helper.GetFileName(storageAttachmentModel.Name);
                fileAttachment.ContentType = convertToPdf == true ? "application/pdf" : storageAttachmentModel.ContentType;
                fileAttachment.Extension = convertToPdf == true ? "pdf" : storageAttachmentModel.Extension;
                //string version = await GetCurrentVersionNumber(item.StorageAttachmentId);
                //string url = $"{Configuration.ViewerUrl}/api/document/{attachmentId}/version/{version}/file?withAnnotations=true&withName=true&token={Configuration.IdentityAccessToken}";
                //var fileData = await Intalio.Core.Helper.HttpGetFileAsync(url);
                //var file = fileData.Result;
                //if (file != null && file.ToString() != "")
                //{
                //    byte[] data = (byte[])file.GetType().GetProperty("Data").GetValue(file);
                //    using (MemoryStream pdfStream = new MemoryStream(data))
                //    {
                //        Aspose.Pdf.Document pdfDocument = new Aspose.Pdf.Document(pdfStream);

                //        // Save the PDF as a Word document into a MemoryStream
                //        using (MemoryStream wordStream = new MemoryStream())
                //        {
                //            pdfDocument.Save(wordStream, Aspose.Pdf.SaveFormat.DocX);
                //            bytes = wordStream.ToArray(); // Return the Word document as a byte array
                //            string fileSignature = BitConverter.ToString(bytes, 0, 4).Replace("-", string.Empty);
                //            if (fileSignature.StartsWith("504B0304")) // Hex for "PK" (ZIP file signature)
                //            {
                //                string w = "w";
                //            }
                //        }
                //    }
                //    bytes = await ReplaceSignPlaceholder(data, "signature",signature);
                //    bytes = ManageBookmark.ReplaceBookmark(bytes, documentId, convertToPdf);
                //}
                //else
                //{

                    bytes = await ReplaceSignPlaceholder(storageAttachmentModel.Data, "signature", signature);
                    bytes = ManageBookmark.ReplaceBookmark(bytes, documentId, convertToPdf, reference);

                //}

                AddDiagonalWatermark(bytes, "Draft Only", out byte[] watermarkedPdfBytes);
                fileAttachment.Data = watermarkedPdfBytes;
                fileAttachment.FileSize = bytes.Length;
                return (true, fileAttachment, "success");
            }
            return (false, null, "can't find the attachment");

            // }
            //return (false, null, result.Message);
        }


        //private static async Task<byte[]> ReplaceSignPlaceholder(byte[] wordFile, string imageAlt, long signature)
        //{
        //    double imageWidth = 0.0;
        //    double imageHeight = 0.0;
        //    MemoryStream pdfms = new MemoryStream();
        //    using (MemoryStream ms = new MemoryStream(wordFile))
        //    {
        //        Aspose.Words.Document doc = new Aspose.Words.Document(ms);

        //        LayoutCollector layoutCollector = new LayoutCollector(doc);
        //        LayoutEnumerator layoutEnumerator = new LayoutEnumerator(doc);

        //        Aspose.Words.NodeCollection shapes = doc.GetChildNodes(Aspose.Words.NodeType.Shape, true);
        //        // Iterate over all shapes in the document
        //        foreach (Aspose.Words.Drawing.Shape shape in shapes)
        //        {
        //            // Check if the shape is an image and has alt text "signature"
        //            if (shape.HasImage && shape.AlternativeText.ToLower() == imageAlt.ToLower())
        //            {
        //                // Get the dimensions of the image
        //                imageWidth = shape.SizeInPoints.Width;
        //                imageHeight = shape.SizeInPoints.Height;

        //                byte[] templateImage = await GetSignature(signature);
        //                MemoryStream sign = new MemoryStream(templateImage);
        //                shape.ImageData.SetImage(sign);
        //                //shapes.Remove(shape);
        //                //MemoryStream memoryStream = new MemoryStream();

        //            }
        //        }
        //        doc.Save(ms, Aspose.Words.SaveFormat.Docx);
        //        return ms.ToArray();//new Intalio.Core.WordUtility().ConvertToPDF( 
        //    }

        //}

        private static async Task<byte[]> ReplaceSignPlaceholder(byte[] wordFile, string imageAlt, long signature)
        {
            AsposeLicense asposeLicense = new AsposeLicense();
            Stream licenseStream = asposeLicense.Get();
            if (licenseStream != null)
            {
                Aspose.Words.License license = new Aspose.Words.License();
                license.SetLicense(licenseStream);
            }
            // Create a memory stream for the input Word file
            using (MemoryStream inputStream = new MemoryStream(wordFile))
            {
                // Load the document
                Aspose.Words.Document doc = new Aspose.Words.Document(inputStream);

                // Get all shapes in the document
                Aspose.Words.NodeCollection shapes = doc.GetChildNodes(Aspose.Words.NodeType.Shape, true);

                foreach (Aspose.Words.Drawing.Shape shape in shapes)
                {
                    // Check if the shape is an image and matches the specified alt text
                    if (shape.HasImage && shape.AlternativeText.Equals(imageAlt, StringComparison.OrdinalIgnoreCase))
                    {
                        // Get the dimensions of the current shape (optional)
                        double imageWidth = shape.Width;
                        double imageHeight = shape.Height;

                        // Retrieve the new image asynchronously
                        byte[] templateImage = await GetSignature(signature);

                        using (MemoryStream imageStream = new MemoryStream(templateImage))
                        {
                            // Set the new image for the shape
                            shape.ImageData.SetImage(imageStream);

                            // Optional: Adjust the dimensions to match the original shape
                            shape.Width = imageWidth;
                            shape.Height = imageHeight;
                        }
                    }
                }

                // Save the updated document into a new memory stream
                using (MemoryStream outputStream = new MemoryStream())
                {
                    doc.Save(outputStream, Aspose.Words.SaveFormat.Docx);
                    return outputStream.ToArray(); // Return the updated document as a byte array
                }
            }
        }

        private static async Task<byte[]> GetSignature(long templateId)
        {
            var client = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Get, Configuration.DSURL + "/api/signature/template/" + templateId + "/image?token=" + Configuration.IdentityAccessToken);
            request.Headers.Add("Authorization", "Bearer " + Configuration.IdentityAccessToken);
            var response = await client.SendAsync(request);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsByteArrayAsync();
        }
        public static async Task<(bool IsValid, string Message)> PreviewAttachmentValidation(long id, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null)
        {
            var item = new Attachment().Find(id);
            return await PreviewAttachmentValidator(item, userId, structureIds, isStructureReceiver, privacyLevel, delegationId);
        }
        public static void AddDiagonalWatermark(byte[] pdfBytes, string watermarkText, out byte[] watermarkedPdfBytes)
        {
            AsposeLicense asposeLicense = new AsposeLicense();
            Stream licenseStream = asposeLicense.Get();
            if (licenseStream != null)
            {
                Aspose.Pdf.License license = new Aspose.Pdf.License();
                license.SetLicense(licenseStream);
            }
            using (MemoryStream inputStream = new MemoryStream(pdfBytes))
            using (MemoryStream outputStream = new MemoryStream())
            {
                Aspose.Pdf.Document pdfDocument = new Aspose.Pdf.Document(inputStream);

                Aspose.Pdf.TextStamp textStamp = new Aspose.Pdf.TextStamp(watermarkText)
                {
                    HorizontalAlignment = Aspose.Pdf.HorizontalAlignment.Center,
                    VerticalAlignment = Aspose.Pdf.VerticalAlignment.Center,
                    RotateAngle = 45,
                    Opacity = 0.5
                };
                textStamp.TextState.Font = Aspose.Pdf.Text.FontRepository.FindFont("Arial");
                textStamp.TextState.FontSize = 72;
                textStamp.TextState.ForegroundColor = Aspose.Pdf.Color.FromRgb(System.Drawing.Color.Gray);


                foreach (Aspose.Pdf.Page page in pdfDocument.Pages)
                {
                    page.AddStamp(textStamp);
                }

                // Flatten the annotations and content layers to embed the watermark securely
                foreach (Aspose.Pdf.Page page in pdfDocument.Pages)
                {
                    page.Flatten();
                }

                pdfDocument.Save(outputStream);
                watermarkedPdfBytes = outputStream.ToArray();
            }
        }

        public static async Task<bool> RestoreDocumentBeforeSignAsync(long documentId)
        {
            var document = new Document().FindIncludeAttachments(documentId);
            if(document is null)
            {
                return false;
            }
            if(document.IsSigned.IsNull() || !document.IsSigned.HasValue)
            {
                return false;
            }
            //Document is signed, so we have to unsign it and revert back to the previous version while deleting the current one
            new Document().UpdateDocumentIsSigned(documentId, false);   

            //As discussed, we have 2 scenarios
                //if first version was pdf, then we fall back to the last none-signed version
                //otherwise, if it was a word document, then we fall back to the last word version
            
            var allVersions = await Intalio.Core.Helper.HttpGetAsync<List<AttachmentVersionModel>>($"{Configuration.StorageServerUrl}/storage/ListAllVersionsIncludeCurrent?fileId={document.AttachmentNavigation.StorageAttachmentId}", Configuration.IdentityAccessToken);
            var versionToRestore = allVersions.FirstOrDefault(e => !e.Extension.Equals("pdf", StringComparison.InvariantCultureIgnoreCase));
            if(versionToRestore is null)
            {
                versionToRestore = allVersions[1];
            }
            //Restore `versionToRestore` and delete everything from > Index(versionToRestore)
            var storageRetValue = await Intalio.Core.Helper.HttpJsonPostAsync<(string, AttachmentVersionModel)>($"{Configuration.StorageServerUrl}/storage/RestoreVersion?id={versionToRestore.Id}", Configuration.IdentityAccessToken, null);
            if (!storageRetValue.Success)
            {
                ExceptionLogger.LogException("ManageAttachment.RestoreDocumentBeforeSignAsync: documentId: " + documentId + " - " + (string)storageRetValue.Result, UserContextAccessor.UserContext!.Id, null, "Storage Error");
                return false;
            }

            (string, AttachmentVersionModel) restoredVersion = ((string, AttachmentVersionModel))storageRetValue.Result;
            if (!await CloneVersionInViewer(document.AttachmentNavigation, restoredVersion.Item2.Version, restoredVersion.Item1))
            {
                await Intalio.Core.Helper.HttpJsonPostAsync<bool>($"{Configuration.StorageServerUrl}/storage/UndoRestoreFileVersion?fileId={document.AttachmentNavigation.StorageAttachmentId}", Configuration.IdentityAccessToken, null);
                return false;
            }
            if (
                document.AttachmentNavigation.Name.Equals(restoredVersion.Item2.Name, StringComparison.CurrentCultureIgnoreCase)
                || document.AttachmentNavigation.Extension.Equals(restoredVersion.Item2.Extension, StringComparison.CurrentCultureIgnoreCase)
                )
            {
                document.AttachmentNavigation.Name = restoredVersion.Item2.Name;
                document.AttachmentNavigation.Extension = restoredVersion.Item2.Extension;
            }
            document.AttachmentNavigation.Update();

            allVersions = await Intalio.Core.Helper.HttpGetAsync<List<AttachmentVersionModel>>($"{Configuration.StorageServerUrl}/storage/ListAllVersions?fileId={document.AttachmentNavigation.StorageAttachmentId}", Configuration.IdentityAccessToken);
            var test = allVersions
            .TakeWhile(e => e != versionToRestore).ToArray();

            var versionsToDelete = allVersions
            .TakeWhile(e => e.Id != versionToRestore.Id)
            .Select(
                e => Intalio.Core.Helper.HttpPostAsync($"{Configuration.StorageServerUrl}/storage/DeleteVersion?id={e.Id}", Configuration.IdentityAccessToken)
            ).ToArray();

            var tasks = await Task.WhenAll(versionsToDelete);

            tasks.ForEach(e => Console.WriteLine($"{e.Item1}, {e.Item2}"));

            return true;
        }


        public static async Task<bool> RestoreWordDocument(long documentId)
        {
            var document = new Document().FindIncludeAttachments(documentId);
            var allVersions = await Intalio.Core.Helper.HttpGetAsync<List<AttachmentVersionModel>>(
                $"{Configuration.StorageServerUrl}/storage/ListAllVersionsIncludeCurrent?fileId={document.AttachmentNavigation.StorageAttachmentId}",
                Configuration.IdentityAccessToken
            );

            var versionToRestore = allVersions.FirstOrDefault(e =>
                !e.Extension.Equals("pdf", StringComparison.InvariantCultureIgnoreCase)
            );

            if (versionToRestore is null && allVersions.Count > 1)
            {
                versionToRestore = allVersions[1]; // fallback
            }

            if (versionToRestore is null)
            {
                // Still nothing found to restore
                return false;
            }

            var storageRetValue = await Intalio.Core.Helper.HttpJsonPostAsync<(string, AttachmentVersionModel)>(
                $"{Configuration.StorageServerUrl}/storage/RestoreVersion?id={versionToRestore.Id}",
                Configuration.IdentityAccessToken,
                null
            );

            if (!storageRetValue.Success)
            {
                ExceptionLogger.LogException("ManageAttachment.RestoreWordDocument: documentId: " + documentId + " - " + (string)storageRetValue.Result, UserContextAccessor.UserContext!.Id, null, "Storage Error");
                return false;
            }

            (string, AttachmentVersionModel) restoredVersion = ((string, AttachmentVersionModel))storageRetValue.Result;

            await CloneVersionInViewer(document.AttachmentNavigation, restoredVersion.Item2.Version, restoredVersion.Item1);

            document.AttachmentNavigation.Name = restoredVersion.Item2.Name;
            document.AttachmentNavigation.Extension = restoredVersion.Item2.Extension;
            document.AttachmentNavigation.Update();

            return true;
        }
        #region Versions

        /// <summary>
        /// List file versions
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="forCopy"></param>
        /// <returns></returns>
        public static async Task<(long, List<AttachmentVersionModel>)> ListVersionHistory(long fileId, int startIndex, int pageSize, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver,
            short privacyLevel, long? delegationId, bool forCopy)
        {
            if (ManageUserAccess.HaveAccess(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                var item = new Attachment().FindIncludeDocument(fileId);
                if (item != null)
                {
                    var versions = await Intalio.Core.Helper.HttpGetAsync<List<AttachmentVersionModel>>($"{Configuration.StorageServerUrl}/Storage/ListAllVersionsIncludeCurrent?fileId={item.StorageAttachmentId}", Configuration.IdentityAccessToken);
                    if (forCopy && (item.Document.IsSigned ?? false) && item.Document.SignedVersion != null)
                    {
                        //var signedVersion = versions.FirstOrDefault(x => x.Version == item.Document.SignedVersion);
                        Version signedVersion = new Version(item.Document.SignedVersion + ".0");
                        //versions = versions.Where(x => x.Extension == "docx" || x.Extension == "doc").ToList().Count() > 0 ? versions.Where(x => x.Extension == "docx" || x.Extension == "doc").ToList() : new List<AttachmentVersionModel> { versions.LastOrDefault() };
                        versions = versions.Where(x => new Version(x.Version + ".0") <= signedVersion).ToList();
                    }
                    int allVersionsCount = versions?.Count ?? 0;
                    versions = versions?.Skip(startIndex)?.Take(pageSize)?.ToList() ?? new List<AttachmentVersionModel>();
                    return (allVersionsCount, versions);

                }
                else
                {
                    return (0, new List<AttachmentVersionModel>());
                }
            }
            else
            {
                return (0, new List<AttachmentVersionModel>());
            }
        }

        /// <summary>
        /// Delete version
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileId"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<bool> DeleteVersion(long id, long fileId, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver,
            short privacyLevel, long? delegationId)
        {
            bool retValue = false;
            if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                var item = new Attachment().Find(fileId);
                if (item != null && ManageUserAccess.HaveEditAccess(item.CreatedByUserId, item.CreatedByDelegatedUserId, item.TransferId, transferId, userId, delegationId))
                {
                    var storageretValue = await Intalio.Core.Helper.HttpPostAsync($"{Configuration.StorageServerUrl}/storage/DeleteVersion?id={id}", Configuration.IdentityAccessToken);
                    if (!storageretValue.Success)
                    {
                        ExceptionLogger.LogException($"ManageAttachment.DeleteVersion: {documentId}, transfer: {(transferId != null ? transferId.Value : String.Empty)}, AttachmentId: {fileId}, StorageId: {item.StorageAttachmentId}  message: {(string)storageretValue.message}", userId, null, "Storage Error");
                        //ExceptionLogger.LogException(storageretValue.message, userId, null, "Storage Error");
                    }
                    else
                    {
                        retValue = true;
                    }
                }
            }
            return retValue;
        }

        /// <summary>
        /// Restore version
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileId"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<string> RestoreVersion(long id, long fileId, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver,
            short privacyLevel, long? delegationId, Intalio.Core.Language Language = Intalio.Core.Language.EN)
        {
            string retValue = null;
            bool nameIsDifferent = false;
            bool canRestore = true;
            if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                var item = new Attachment().FindIncludeDocument(fileId);
                if (item != null && item.Document != null)
                {
                    long documnetAttachemntId = item?.Document?.AttachmentId ?? 0;
                    if (documnetAttachemntId == fileId &&
                        ManageDocument.CheckOriginalDocumentLocked(item.DocumentId.Value))
                    {
                        retValue = "OriginalFileInUse";
                    }
                    else if (item.IsLocked ?? false)
                    {
                        retValue = "FileInUse";
                    }
                    else
                    {
                        if (item.Document.AttachmentId == fileId && (item.Document.IsSigned.HasValue ? item.Document.IsSigned.Value : false))
                        {
                            return "CantRestoreVersionOfSignedDocument";
                        }

                        List<Intalio.Core.Model.TextValue> versionName = await Intalio.Core.Helper.HttpGetAsync<List<Intalio.Core.Model.TextValue>>($"{Configuration.StorageServerUrl}/Storage/GetVersionFileName?id={id}", Configuration.IdentityAccessToken);
                        if (versionName != null)
                        {
                            string name = versionName.Find(t => t.Text == "Name").Value;
                            string extension = versionName.Find(t => t.Text == "Extension").Value;
                            nameIsDifferent = item.Name.ToLower() != name.ToLower() || item.Extension.ToLower() != extension.ToLower();
                            if (nameIsDifferent)
                            {
                                var isUnique = CheckUnique(item.Id, item.FolderId, (long)item.DocumentId, name, extension);
                                canRestore = !isUnique.Item1;
                            }

                            if (canRestore)
                            {
                                var storageRetValue = await Intalio.Core.Helper.HttpJsonPostAsync<(string, AttachmentVersionModel)>($"{Configuration.StorageServerUrl}/storage/RestoreVersion?id={id}", Configuration.IdentityAccessToken, null);
                                if (!storageRetValue.Success)
                                {
                                    ExceptionLogger.LogException($"ManageAttachment.RestoreVersion: document: {documentId}, transfer: {(transferId != null ? transferId.Value : String.Empty)}, AttachmentId: {fileId}, StorageId: {item.StorageAttachmentId}  message: {(string)storageRetValue.Result}", userId, null, "Storage Error");
                                    //ExceptionLogger.LogException((string)storageRetValue.Result, userId, null, "Storage Error");
                                    retValue = "StorageError";
                                }
                                else
                                {
                                    (string, AttachmentVersionModel) restoredVersion = ((string, AttachmentVersionModel))storageRetValue.Result;
                                    if (await CloneVersionInViewer(item, restoredVersion.Item2.Version, restoredVersion.Item1))
                                    {
                                        if (nameIsDifferent)
                                        {
                                            item.Name = restoredVersion.Item2.Name;
                                            item.Extension = restoredVersion.Item2.Extension;
                                        }
                                        item.Update();
                                        retValue = "Success";
                                        var versionNumberText = TranslationUtility.Translate("versionnumber", Language);
                                        ManageActivityLog.AddActivityLog(documentId, transferId, (int)ActivityLogs.RestoreAttachment, userId, note: $"{versionNumberText}: {restoredVersion.Item2.Version}");
                                    }
                                    else
                                    {
                                        await Intalio.Core.Helper.HttpJsonPostAsync<bool>($"{Configuration.StorageServerUrl}/storage/UndoRestoreFileVersion?fileId={item.StorageAttachmentId}", Configuration.IdentityAccessToken, null);
                                        retValue = "NoPermission";
                                    }
                                }
                            }
                            else
                            {
                                retValue = "FileSameNameExists";
                            }
                        }
                        else
                        {
                            retValue = "VersionNotFound";
                        }
                    }
                }
                else
                {
                    retValue = "FileNotFound";
                }
            }
            else
            {
                retValue = "NoPermission";
            }
            return retValue;
        }

        /// <summary>
        /// List all versions
        /// </summary>
        /// <param name="fileId"></param>
        /// <returns></returns>
        public static async Task<List<AttachmentVersionModel>> ListAllVersions(long fileId)
        {
            var item = new Attachment().Find(fileId);
            if (item != null)
            {
                var versions = await Intalio.Core.Helper.HttpGetAsync<List<AttachmentVersionModel>>($"{Configuration.StorageServerUrl}/Storage/ListAllVersions?fileId={item.StorageAttachmentId}", Configuration.IdentityAccessToken);
                return versions;
            }
            return null;
        }

        /// <summary>
        /// List all versions
        /// Including the current version
        /// </summary>
        /// <param name="fileId"></param>
        /// <returns></returns>
        public static async Task<List<AttachmentVersionModel>> ListAllVersionsIncludeCurrent(long fileId)
        {
            var item = new Attachment().Find(fileId);
            if (item != null)
            {
                return await Intalio.Core.Helper.HttpGetAsync<List<AttachmentVersionModel>>($"{Configuration.StorageServerUrl}/Storage/ListAllVersionsIncludeCurrent?fileId={item.StorageAttachmentId}", Configuration.IdentityAccessToken);
            }
            return null;
        }

        /// <summary>
        /// Check if attachment is original document by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static bool CheckIsAttachmentOriginal(long id)
        {
            return new Attachment().CheckIsAttachmentOriginal(id);
        }

        /// <summary>
        /// Download file from the Viewer
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static async Task<object> DownloadFromViewertoUploadOnDMS(long id)
        {
            var item = new Attachment().Find(id);
            if (item != null)
            {
                var documentId = item.DocumentId.Value;
                long storageAttachmentId = item.StorageAttachmentId;
                if (storageAttachmentId > 0)
                {
                    string version = await GetCurrentVersionNumber(storageAttachmentId);
                    //withAnnotations=false will return only forced annotations, withAnnotations=true will return all annotations
                    string url = $"{Configuration.ViewerUrl}/api/document/{id}/version/{version}/file?withAnnotations=true&withName=true&token={Configuration.IdentityAccessToken}";
                    var fileData = await Intalio.Core.Helper.HttpGetFileAsync(url);
                    if (fileData.Success)
                    {
                        return fileData.Result;
                    }
                }
            }
            return null;
        }

        public static async Task<List<TreeNode>> ListForExport(long documentId, long? transferId, long userId, int roleId, long? structureId, List<long> structureIds, bool isStructureReceiver,
    short privacyLevel, long? delegationId = null, long? parentDocumentId = null, Language language = Language.EN)
        {
            List<TreeNode> treeNodes = new List<TreeNode>();
            Document document = new Document().FindIncludeAll(documentId);
            var ifHaveManageCorrespondenceAccess = Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageCorrespondence") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageCorrespondence");
            var ifHaveFollowUpOnEmployeesAccess = Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageStructureUsersCorrespondences") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageStructureUsersCorrespondences");

            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId)
                || (parentDocumentId != null && document.LinkedDocumentDocument.Count > 0 &&
                ManageUserAccess.HaveAccess(parentDocumentId.Value, userId, structureIds, isStructureReceiver, privacyLevel, delegationId) || ifHaveManageCorrespondenceAccess || ifHaveFollowUpOnEmployeesAccess)
                )
            {
                List<Attachment> AllAttachments = await new Attachment().ListAllAttachmentsByDocumentIdAsync(documentId, userId, structureIds);
                AllAttachments.ForEach(x => x.FolderId = null);
                List<TreeNode> attachmentNodes = GetAttachmentTreeNodes(AllAttachments, transferId, userId, delegationId, document.IsSigned);

                TreeNode originalMail = document.AttachmentId == null ? null : attachmentNodes.FirstOrDefault(t => t.Id == $"file_{document.AttachmentId}");
                if (originalMail != null)
                {
                    attachmentNodes.Remove(originalMail);
                }
                treeNodes.AddRange(attachmentNodes);
                //ManageActivityLog.AddFullActivityLog(documentId, transferId, (int)ActivityLogs.ViewAttachments, userId, "", "");
            }
            return treeNodes;
        }

        #endregion

        #endregion

        #region Internal Methods

        internal static List<TreeNode> GetAttachmentTreeNodes(List<Attachment> attachments, long? transferId, long userId, long? delegationId, bool? isSigned = false)
        {
            List<TreeNode> nodes = new List<TreeNode>();
            foreach (Attachment attachment in attachments)
            {
                TreeNode node = null;
                if (attachment != null)
                {
                    node = new TreeNode
                    {
                        Id = $"file_{attachment.Id}",
                        Text = $"{attachment.Name}.{attachment.Extension}",
                        Title = $"{attachment.Name}.{attachment.Extension}",
                        Type = ((int)NodeType.File).ToString(),
                        Icon = Helper.GetIcon(attachment.Extension),
                        ParentId = attachment.FolderId.HasValue ? $"folder_{attachment.FolderId}" : "#",
                        Data = new TreeNodeData()
                        {
                            HasEditAccess = ManageUserAccess.HaveEditAccess(attachment.CreatedByUserId, attachment.CreatedByDelegatedUserId, attachment.TransferId, transferId, userId, delegationId),
                            IsWord = Intalio.Core.Helper.IsWord($"{attachment.Name}.{attachment.Extension}"),
                            IsSigned = isSigned,
                            isUserCreated = attachment.CreatedByUserId == userId,
                            IsLocked = attachment.DocumentLockId.HasValue
                        },
                        State = new TreeNodeState()
                        {
                            Disabled = false,
                            Opened = true,
                            Selected = false
                        }
                        ,
                        IsLocked = attachment.IsLocked,
                        SecurityCount = attachment.AttachmentSecurities.Count()

                    };
                    nodes.Add(node);
                }
            }
            return nodes;
        }

        internal static async Task<byte[]> GetFileData(long id, string version)
        {
            return await Intalio.Core.Helper.HttpGetBytesAsync($"{Configuration.StorageServerUrl}/storage/DownloadSpecificVersion?id={id}&number={version}", Configuration.IdentityAccessToken);
        }

        internal static async Task<FileViewModel> GetFileInfo(long id, string version)
        {
            FileViewModel retValue = null;
            var attachmentVersionModel = await Intalio.Core.Helper.HttpGetAsync<AttachmentVersionModel>($"{Configuration.StorageServerUrl}/storage/GetSpecificVersionInfo?id={id}&number={version}", Configuration.IdentityAccessToken);
            if (attachmentVersionModel != null)
            {
                retValue = new FileViewModel
                {
                    ContentType = attachmentVersionModel.ContentType,
                    Name = $"{attachmentVersionModel.Name}.{attachmentVersionModel.Extension}",
                    FileName = $"{attachmentVersionModel.Name}.{attachmentVersionModel.Extension}",
                    Data = attachmentVersionModel.Data,
                    MD5Checksum = attachmentVersionModel.MD5Checksum,
                };
            }
            return retValue;
        }

        internal static async Task<double> Replace(long id, long documentId, byte[] data, long userId, string comment = "")
        {
            double retValue = 0;
            var urlReplace = "Replace";
            var isDraft = new Document().CheckDraft(documentId);
            if (isDraft && !Configuration.EnableDraftVersioning)
            {
                urlReplace = "ReplaceNoVersioning";
            }
            var item = new Attachment().Find(id);
            if (item != null)
            {
                var content = SetFileFormData(new FileViewModel
                {
                    Name = item.Name,
                    FileSize = data.Length,
                    Extension = item.Extension,
                    Comment = comment,
                    Data = data
                });
                var result = await Intalio.Core.Helper.HttpMultipartPostAsync($"{Configuration.StorageServerUrl}/storage/{urlReplace}?fileId={item.StorageAttachmentId}", Configuration.IdentityAccessToken, content);
                if (!result.Success)
                {
                    ExceptionLogger.LogException($"ManageAttachment.Replace: document: {documentId},  AttachmentId: {id}, StorageId: {item.StorageAttachmentId}  message: {(string)result.Result}", userId, null, "Storage Error");
                    //ExceptionLogger.LogException(result.Result, userId: userId, level: "Storage Error");
                }
                else if (!isDraft)
                {
                    retValue = Convert.ToDouble(result.Result);
                }
            }
            return retValue;
        }

        internal static async Task<string> ReplaceFile(long id, long documentId, byte[] data, long userId, string comment = "", bool isMajor = true)
        {
            string retValue = "-1";
            var urlReplace = "Replace";
            var isDraft = new Document().CheckDraft(documentId);
            if (isDraft && !Configuration.EnableDraftVersioning)
            {
                urlReplace = "ReplaceNoVersioning";
            }
            var item = new Attachment().Find(id);
            if (item != null)
            {
                var content = SetFileFormData(new FileViewModel
                {
                    Name = item.Name,
                    FileSize = data.Length,
                    Extension = item.Extension,
                    Comment = comment,
                    Data = data
                });
                var result = await Intalio.Core.Helper.HttpMultipartPostAsync($"{Configuration.StorageServerUrl}/storage/{urlReplace}?fileId={item.StorageAttachmentId}&isMajor={isMajor}", Configuration.IdentityAccessToken, content);
                if (!result.Success)
                {
                    ExceptionLogger.LogException($"ManageAttachment.ReplaceFile: document: {documentId}, AttachmentId: {id}, StorageId: {item.StorageAttachmentId}  message: {(string)result.Result}", userId, null, "Storage Error");
                    //ExceptionLogger.LogException(result.Result, userId: userId, level: "Storage Error");
                }
                else if (!isDraft || isDraft && Configuration.EnableDraftVersioning)
                {
                    retValue = result.Result;
                }
            }
            return retValue;
        }

        internal static async Task<double> Replace(long id, FileViewModel file, long documentId, long? transferId, long userId, bool replaceVersion = false)
        {
            long retValue = 0;
            var item = new Attachment().Find(id);
            if (item != null)
            {
                var urlReplace = "Replace";
                if (!replaceVersion && new Document().CheckDraft(documentId))
                {
                    urlReplace = "ReplaceNoVersioning";
                }
                var data = SetFileFormData(file);
                var result = await Intalio.Core.Helper.HttpMultipartPostAsync($"{Configuration.StorageServerUrl}/storage/{urlReplace}?fileId={item.StorageAttachmentId}", Configuration.IdentityAccessToken, data);
                if (!result.Success)
                {
                    ExceptionLogger.LogException($"ManageAttachment.Replace: document: {documentId}, transfer: {(transferId != null ? transferId.Value : String.Empty)}, AttachmentId: {id}, StorageId: {item.StorageAttachmentId}  message: {(string)result.Result}", userId, null, "Storage Error");
                    //ExceptionLogger.LogException((string)result.Result, userId, null, "Storage Error");
                }
                else
                {
                    var oldFile = (AttachmentModel)item;
                    if (item.Name.ToLower() != file.Name.ToLower() || item.Extension.ToLower() != file.Extension.ToLower())
                    {
                        item.Name = file.Name;
                        item.Extension = file.Extension;
                    }
                    item.Update();
                    ManageActivityLog.AddActivityLog(documentId, transferId, (int)ActivityLogs.ReplaceAttachment, userId, originalValue: JsonConvert.SerializeObject(oldFile), newOriginalValue: JsonConvert.SerializeObject((AttachmentModel)item));
                }
                retValue = item.Id;
            }
            return retValue;
        }

        internal static async Task<string> GetCurrentVersionNumber(long id)
        {
            return await Intalio.Core.Helper.HttpGetAsync<string>($"{Configuration.StorageServerUrl}/Storage/GetCurrentVersionNumber?fileId={id}", Configuration.IdentityAccessToken);
        }

        /// <summary>
        /// Check if attachment is original document and not signed
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        internal static bool CheckIsAttachmentOriginalNotSigned(long id)
        {
            return new Attachment().CheckIsAttachmentOriginalNotSigned(id);
        }
        internal static bool CheckIsAttachmentOriginalSigned(long id)
        {
            return new Attachment().CheckIsAttachmentOriginalSigned(id);
        }

        public static bool LockForEditing(long userId, long attachmentId)
        {
            Attachment attachment = new Attachment().Find(attachmentId);
            attachment.IsEditLocked = true;
            attachment.EditLockedByUserId = userId;
            attachment.Update();
            return true;
        }

        public static bool UnlockForEditing(long userId, long attachmentId)
        {
            Attachment attachment = new Attachment().Find(attachmentId);
            attachment.IsEditLocked = false;
            attachment.EditLockedByUserId = null;
            attachment.Update();
            return true;
        }
        #endregion

        #region Private Methods

        /// <summary>
        /// Clone annotations from old file version to new file version
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="fromVersion"></param>
        /// <param name="toVersion"></param>
        /// <returns></returns>
        private static async Task<bool> CloneVersionInViewer(long fileId, string fromVersion, string toVersion)
        {
            try
            {
                string viewerUrl = $"{Configuration.ViewerUrl}/api/document/{fileId}/version/{fromVersion}/clone";
                var content = new StringContent(Intalio.Core.Helper.SerializeJson(new
                {
                    version = toVersion,
                    checkedOut = false
                }).ToString(), Encoding.UTF8, "application/json");
                var retValue = await HttpJsonPostAsync<string>(viewerUrl, Configuration.IdentityAccessToken, content);
                return retValue.Success;
            }
            catch (Exception ex)
            {
                ExceptionLogger.WriteEntry(ex.Message, "Viewer error");
                return false;
            }
        }
        private static async Task<bool> CloneVersionInViewer(Attachment attachment, string fromVersion, string toVersion)
        {
            try
            {
                long fileId = attachment.Id;
                long ctsSocumentID = attachment.Document.Id;
                string viewerUrl = $"{Configuration.ViewerUrl}/api/document/{fileId}/version/{fromVersion}/clone?ctsDocumentId={ctsSocumentID}";
                var content = new StringContent(Intalio.Core.Helper.SerializeJson(new
                {
                    version = toVersion,
                    checkedOut = false,
                }).ToString(), Encoding.UTF8, "application/json");
                var retValue = await HttpJsonPostAsync<string>(viewerUrl, Configuration.IdentityAccessToken, content);
                return retValue.Success;
            }
            catch (Exception ex)
            {
                ExceptionLogger.WriteEntry(ex.Message, "Viewer error");
                return false;
            }
        }
        /// <summary>
        /// Used for viewer only
        /// Authentication Basic not Bearor
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="url"></param>
        /// <param name="token"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        private static async Task<(bool Success, object Result)> HttpJsonPostAsync<T>(string url, string token, StringContent data)
        {
            HttpClient client = new HttpClient();
            client.BaseAddress = new Uri(url);
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));//ACCEPT header
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", token);
            HttpResponseMessage response = await client.PostAsync(url, data);
            if (response.IsSuccessStatusCode)
            {
                string responseBody = await response.Content.ReadAsStringAsync();
                return (true, JsonConvert.DeserializeObject<T>(responseBody));
            }
            return (false, response.ReasonPhrase);
        }

        /// <summary>
        /// Used to set file data for storage 
        /// </summary>
        /// <returns></returns>
        private static MultipartFormDataContent SetFileFormData(FileViewModel file, List<string> paths = null, string recordId = "", List<string> filingPlans = null)
        {
            var retValue = new MultipartFormDataContent();
            string fileName = Intalio.Core.Helper.GetFileName(file.Name);
            retValue.Add(new StringContent(fileName), "Name");
            retValue.Add(new StringContent(file.FileSize.ToString()), "FileSize");
            retValue.Add(new StringContent(file.Data.MD5Hash()), "MD5Checksum");
            retValue.Add(new StringContent(file.Extension), "Extension");
            if (!string.IsNullOrEmpty(file.Comment))
            {
                retValue.Add(new StringContent(file.Comment), "Comment");
            }
            if (!string.IsNullOrEmpty(file.ContentType))
            {
                retValue.Add(new StringContent(file.ContentType), "ContentType");
            }
            var fileContent = new ByteArrayContent(file.Data);
            //fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse(file.ContentType);
            retValue.Add(fileContent, "Data", file.Name);
            if (!paths.IsNull())
            {
                foreach (var path in paths)
                {
                    retValue.Add(new StringContent(path), "Paths");
                }
            }
            if (!string.IsNullOrEmpty(recordId))
            {
                retValue.Add(new StringContent(recordId), "RecordId");
            }
            if (!filingPlans.IsNull())
            {
                foreach (var filingPlan in filingPlans)
                {
                    retValue.Add(new StringContent(filingPlan), "FilingPlan");
                }
            }
            return retValue;
        }

        private static async Task<(bool IsValid, string Message)> GenerateLetterValidater(Attachment item, bool generateReceivingEntity, bool generateCarbonCopy, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null)
        {
            var retValue = false;
            if (item != default(Attachment) && Intalio.Core.Helper.IsWord($"{item.Name}.{item.Extension}") && ManageUserAccess.HaveAccess(item.DocumentId.Value, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                if (ManageDocument.CheckOriginalDocumentLocked(item.DocumentId.Value))
                {
                    return (retValue, "OriginalFileInUse");
                }
                else
                {
                    var document = new Document().FindIncludeReceiversAndCarbonCopyAndCategory(item.DocumentId.Value);
                    if (document != null && !string.IsNullOrEmpty(document.ReferenceNumber))
                    {
                        if ((!document.DocumentReceiverEntity.Any() && !document.DocumentCarbonCopy.Any())
                            || (!document.DocumentReceiverEntity.Any() && generateReceivingEntity && !generateCarbonCopy)
                            || (!document.DocumentCarbonCopy.Any() && generateCarbonCopy && !generateReceivingEntity))
                        {
                            return (retValue, "AtLeastOneReceivingEntityOrCarbonCopy");
                        }
                        bool isBroadcast = false;
                        bool isCarbonCopy = false;
                        bool isMultipleReceivingEntity = false;
                        dynamic categoryBasicAttribute = JsonConvert.DeserializeObject<dynamic>(document.Category.BasicAttribute);
                        if (!ReferenceEquals(null, categoryBasicAttribute))
                        {
                            foreach (var attr in categoryBasicAttribute.Root)
                            {
                                if (attr.Name.Value == "ReceivingEntity")
                                {
                                    if (!ReferenceEquals(null, attr.BroadcastReceivingEntity) && attr.BroadcastReceivingEntity.Value == true)
                                    {
                                        isBroadcast = true;
                                        break;
                                    }
                                    if (!ReferenceEquals(null, attr.MultipleReceivingEntity) && attr.MultipleReceivingEntity.Value == true)
                                    {
                                        isMultipleReceivingEntity = true;
                                    }
                                }
                                if (attr.Name.Value == "CarbonCopy")
                                {
                                    if (!ReferenceEquals(null, attr.Enabled) && attr.Enabled.Value == true)
                                    {
                                        isCarbonCopy = true;
                                        break;
                                    }
                                }
                            }
                        }
                        if (!isBroadcast && (isCarbonCopy || isMultipleReceivingEntity))
                        {
                            var result = await new Document().GetOriginalAttachmentIdAndCategoryId(item.DocumentId.Value);
                            if (!result.IsCompleted && result.AttachmentId == item.Id && ManageCategory.CheckCategoryByFileOrTemplate(result.CategoryId))
                            {
                                retValue = true;
                            }
                            else
                            {
                                return (retValue, "GenerateLetterNotComplete");
                            }
                        }
                    }
                    else
                    {
                        return (retValue, "DocumentNotRegistered");
                    }
                }
            }
            return (retValue, string.Empty);
        }

        private static async Task<(bool IsValid, string Message)> PreviewAttachmentValidator(Attachment item, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null)
        {
            var retValue = false;
            if (item != default(Attachment) && ManageUserAccess.HaveAccess(item.DocumentId.Value, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                if (ManageDocument.CheckOriginalDocumentLocked(item.DocumentId.Value))
                {
                    return (retValue, "OriginalFileInUse");
                }
                else
                {
                    var document = new Document().FindIncludeReceiversAndCarbonCopyAndCategory(item.DocumentId.Value);
                    if (document != null && !string.IsNullOrEmpty(document.ReferenceNumber))
                    {
                        var result = await new Document().GetOriginalAttachmentIdAndCategoryId(item.DocumentId.Value);
                        if (!result.IsCompleted && result.AttachmentId == item.Id && ManageCategory.CheckCategoryByFileOrTemplate(result.CategoryId))
                        {
                            retValue = true;
                        }
                        else
                        {
                            return (retValue, "AttachmentNotComplete");
                        }

                    }
                    else
                    {
                        return (retValue, "DocumentNotRegistered");
                    }
                }
            }
            return (retValue, string.Empty);
        }
        public static async Task<(int, List<TreeNode>)> ListFollowUpAttachments(int draw, int start, int length, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver,
    short privacyLevel, long? delegationId = null, long? parentDocumentId = null, Language language = Language.EN)
        {
            List<TreeNode> attachmentNodes = new List<TreeNode>();
            Document document = new Document().FindIncludeAll(documentId);
            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId)
                || (parentDocumentId != null && document.LinkedDocumentDocument.Count > 0 && ManageUserAccess.HaveAccess(parentDocumentId.Value, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
                )
            {
                List<long?> LinkedDocumentDocumentIds = new List<long?>();
                foreach (var item in document.LinkedDocumentDocument)
                {
                    LinkedDocumentDocumentIds.Add(item.LinkedDocumentId);
                }

                List<Attachment> rootAttachments = await new Attachment().ListTreeRootFollowUpAttachmentsAsync(documentId, userId, structureIds, LinkedDocumentDocumentIds);
                attachmentNodes = GetAttachmentTreeNodes(rootAttachments, transferId, userId, delegationId, document.IsSigned);

                ManageActivityLog.AddFullActivityLog(documentId, transferId, (int)ActivityLogs.ViewAttachments, userId, "", "");
            }
            return (attachmentNodes.Count, attachmentNodes);
        }
        public static async Task<AttachmentPropertiesDetails> GetAttachmentProperties(long attachmentId, long userId, List<long> structureIds, bool isStructureReceiver,short privacyLevel, long? delegationId = null, Language language = Language.EN)
        {
            AttachmentPropertiesDetails retvalue = null;
            var attachment = new Attachment().FindIncludeFormIncludeDocumentThenIncludeCategory(attachmentId);
            if (await ManageUserAccess.HaveAccessAsync(attachment.DocumentId ?? 0, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                retvalue = new AttachmentPropertiesDetails()
                {
                    attachmentProperties = attachment.Document.Category.AttachmentProperties,
                    attachmentPropertiesTranslation = attachment.Document.Category.AttachmentPropertiesTranslation,
                    Form = attachment.AttachmentForm.Form
                };
            }
            return retvalue;
        }
        public static async Task<bool> SaveAttachmentProperties(long attachmentId, string attachmentForm, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language language = Language.EN)
        {
            bool retvalue = false;
            try
            {
                var attachment = new Attachment().FindIncludeAttachmentForm(attachmentId);
                if (await ManageUserAccess.HaveAccessAsync(attachment.DocumentId ?? 0, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
                {
                    attachment.AttachmentForm.Form = attachmentForm;
                    attachment.AttachmentForm.Id = attachment.Id;
                    attachment.AttachmentForm.Body = "";
                    attachment.AttachmentForm.Keyword = "";
                    attachment.UpdateIncludeAttachmentForm();
                    retvalue = true;
                }
            }
            catch (Exception ex)
            {
                retvalue = false;
            }
            return retvalue;
        }
        #endregion
    }
}
