﻿IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'FailedToGetCertificate')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'FailedToGetCertificate', 
            N'Failed to get certificate.', 
            N'Échec de l''obtention du certificat.', 
            N'فشل في الحصول على الشهادة.', 
            1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'NoCertificate')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'NoCertificate', 
            N'No certificate found.', 
            N'Aucun certificat trouvé.', 
            N'لم يتم العثور على شهادة.', 
            1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'CertificateExpired')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'CertificateExpired', 
            N'The certificate has expired.', 
            N'Le certificat a expiré.', 
            N'انتهت صلاحية الشهادة.', 
            1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'CertificateNotActivated')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'CertificateNotActivated', 
            N'The certificate is not activated.', 
            N'Le certificat n''est pas activé.', 
            N'الشهادة غير مفعّلة.', 
            1)
END