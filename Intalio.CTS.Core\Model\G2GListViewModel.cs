﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.Model
{
    public class G2GListViewModel : TransferGridFormatModel
    {
        public long Id { get; set; }
        public int CategoryId { get; set; }
        public string ReferenceNumber { get; set; }
        public string G2G_REF_NO { get; set; }
        public string TransferDate { get; set; }
        public int Status { get; set; }
        public long? OwnerDelegatedUserId { get; set; }
        public bool IsOverDue { get; set; }
        public bool IsRead { get; set; }
        public bool IsLocked { get; set; }
        public string LockedBy { get; set; }
        public string LockedByDelegatedUser { get; set; }
        public string LockedDate { get; set; }
        public long? FromUserId { get; set; }
        public long? ToUserId { get; set; }
        public long? ToStructureId { get; set; }
        public bool SentToStructure { get; set; }
        public long CreatedByUserId { get; set; }
        public bool? HasAttachment { get; set; }
        public long? G2GInternalId { get; set; }

    }
}