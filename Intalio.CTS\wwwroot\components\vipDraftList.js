﻿import Intalio from './common.js'
import VipDocumentDetails from './vipDocumentDetails.js'
import DocumentDraft from './draftList.js'
import { Categories, DraftStatuses } from './lookup.js'
class VipDocumentDraft extends Intalio.Model
{
    constructor()
    {
        super();
        this.title = null;
        this.nodeId = null;
        this.categories = null;
    }
}
function buildFilters(nodeId, categories)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var filters = node.filters !== "" && node.filters !== null ? JSON.parse(node.filters) : [];
    if (filters.length > 0)
    {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++)
        {
            var filter = filters[i];
            switch (filter)
            {
                case "FromDate":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label><div class="input-group date">' +
                        '<input id="filterDraftFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterDraftFromDateError">' +
                        '<span class="input-group-addon" id="filterDraftFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterDraftFromDateError"></div></div></div></div>'
                    break;
                case "ToDate":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label><div class="input-group date">' +
                        '<input id="filterDraftToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterDraftToDateError">' +
                        '<span class="input-group-addon" id="filterDraftToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterDraftToDateError"></div></div></div></div>';
                    break;
                case "Subject":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterDraftSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Category":
                    clearFix += 6;
                    var div = '<div class="col-md-6" id="categoryFilterDraftContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterDraftCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterDraftCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++)
                    {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterDraftCategoryError"></div></div></div>';
                    html += div;
                    break;
            }
            if (clearFix === 12 || clearFix === 11)
            {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '</div>';
        $('#filtersContainer').html(html);

        var clickedSearch = false;
        $('#collapseDraftIcon').click(function ()
        {
            $('#collapseDraftIcon').empty();
            if (clickedSearch)
            {
                $('#collapseDraftIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
                $('#collapseDraftPanel').attr('class', '');
                $('#collapseDraftPanel').addClass('panel-body panel-collapse collapse in');
                clickedSearch = false;
            } else
            {
                $('#collapseDraftIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
                $('#collapseDraftPanel').attr('class', '');
                $('#collapseDraftPanel').addClass('panel-body panel-collapse collapse');
                clickedSearch = true;
            }
        });
        $("#btnFilterDraftSearch").on('click', function ()
        {
            gFromSearch = true;
            gNoMoreData = false;
            gPageIndex = 0;
            if (!$(".filterInfoDiv").hasClass("hidden"))
            {
                $(".filterInfoDiv").addClass("hidden");
            }
            loadDraftList();
        });
        $("#btnFilterDraftClear").on('click', function ()
        {
            $("#cmbFilterDraftCategory").val('').trigger('change');
            fromDate.clear();
            toDate.clear();
            $("#txtFilterDraftSubject").val('');
            gFromSearch = true;
            gNoMoreData = false;
            gPageIndex = 0;
            if (!$(".filterInfoDiv").hasClass("hidden"))
            {
                $(".filterInfoDiv").addClass("hidden");
            }
            loadDraftList();
        });
        $('#cmbFilterDraftCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterDraftContainer')
        });
        $("#cmbFilterDraftCategory").val('').trigger('change');
        var fromDate = $('#filterDraftFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    maxDate: jQuery('#filterDraftToDate').val() && jQuery('#filterDraftToDate').val() !== "" ? jQuery('#filterDraftToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterDraftFromDate_img").click(function ()
        {
            fromDate.toggle();
        });
        var toDate = $('#filterDraftToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    minDate: jQuery('#filterDraftFromDate').val() && jQuery('#filterDraftFromDate').val() !== "" ? jQuery('#filterDraftFromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterDraftToDate_img").click(function ()
        {
            toDate.toggle();
        });
        $('#filterDraftFromDate').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterDraftSearch').focus();
                }
                else
                {
                    $('#filterDraftToDate').focus();
                }
            }
        });
        $('#btnFilterDraftClear').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterDraftSearch').focus();
                }
                else
                {
                    $('#filterDraftFromDate').focus();
                }
            }
        });
    } else
    {
        $("#btnOpenSearchDraftModal").remove();
        $("#divSearchDraft").remove();
    }
}
function openDocument(id, nodeId)
{
    Common.ajaxGet('/Document/Get', { id: id }, function (data)
    {
        $('.card-max-width').addClass('card-min-width');
        $('.card-max-width').removeClass('card-max-width');
        $('input:checkbox').removeAttr('checked');
        $('#draftListContainer li').removeClass("active");
        $("input[data-id='" + id + "']").parent().parent().parent().addClass("active");
        $("input[data-id='" + id + "']").prop('checked', true);
        gSelectedRowId = id;
        gLocked = false;
        Common.setActiveSidebarMenu("liDraft" + nodeId);
        $(".delegation").removeClass("active");
        var model = new VipDocumentDetails.VipDocumentDetails();
        model.documentId = id;
        model.statusId = data.status;
        model.categoryId = data.categoryId;
        model.categoryName = data.categoryName;
        model.documentModel = data;
        model.referenceNumber = data.referenceNumber;
        model.showMyTransfer = false;
        model.readonly = false;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));
        var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[data.categoryId].Tabs;
        model.tabs = $.grep(tabs, function (element, index)
        {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
        });
        model.tabsWithStatic = tabs;
        model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[data.categoryId].SecurityTabs;
        model.attachmentId = data.attachmentId;
        model.fromDraft = true;
        model.showBackButton = false;
        model.nodeId = nodeId;
        model.showPreview = window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer" && data.attachmentId !== null;
        model.attachmentCount = data.attachmentCount;
        model.noteCount = data.notesCount;
        model.linkedCount = data.linkedCorrespondanceCount;
        model.attachmentVersion = data.attachmentVersion;
        var wrapper = $("#draftDocumentDetailsContainer");
        wrapper.empty();
        $(".modal-window").empty();
        var view = new VipDocumentDetails.VipDocumentDetailsView(wrapper, model, refreshDraftList);
        view.render();
        $(".documentHeader").hide();
        $(".waitingBackground").removeClass("waitingBackground");
        $(".vipDetailsPanel").show();
        $(".vipCorrLeftPanel").removeClass("col-lg-12 col-md-12 col-sm-12 col-xs-12");
        $(".vipCorrLeftPanel").addClass("col-lg-3 col-md-4 col-sm-4 col-xs-12");
        $(".mdl-ul").removeAttr("style")

    }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
}
function loadDraftList()
{
    if (!gNoMoreData)
    {
        Common.mask(document.getElementById('draftListContainer'), "draftListContainer-mask");
        var params = {};
        addFilters(params);
        params.NodeId = gSelf.model.nodeId;
        params.DelegationId = gSelf.model.delegationId;
        params.start = gPageIndex;
        params.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
        Common.ajaxPost('/Document/ListDraftVip', params, function (response)
        {
            if (response.length > 0)
            {
                gPageIndex += window.Paging;
                if (response.length < window.Paging)
                {
                    gNoMoreData = true;
                }
            } else
            {
                gNoMoreData = true;
            }
            createListData(response);
            gLocked = false;
            Common.unmask("draftListContainer-mask");
            if (gFromSearch)
            {
                $("#divSearchDraft").fadeOut();
            }
        }, function () { gLocked = false; Common.showScreenErrorMsg(); });
    } else
    {
        gLocked = false;
    }
}
function refreshDraftList(ids)
{
    for (var i = 0; i < ids.length; i++)
    {
        var canDelete = false;
        var li = $($("input[data-id='" + ids[i] + "']").parents("li")[0]);
        var spans = li.find("#middlebox span");
        if (spans.length === 3)
        {
            canDelete = true;
        }
        if (canDelete)
        {
            li.fadeOut().remove();
            if (Number(gSelectedRowId) === Number(ids[i]))
            {
                $(".withBorders-o").addClass("waitingBackground");
                $("#draftDocumentDetailsContainer").empty();
            }
        }
    }
    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
}
function createListData(data)
{
    var html = '';
    if (data.length === 0 && gPageIndex === 0)
    {
        html = '<ul class="mdl-ul"><div class="nodata dataTables_empty pt-lg">' + Resources.NoDataToDisplay + '</div></ul>';
        $('#draftListContainer').html(html);
    } else if (data.length > 0)
    {
        var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        html = '<ul class="mdl-ul" style=" margin: 0px 5px 0 0;">';
        var htmlLi = '';
        var color = "";
        var draftstatuses = new DraftStatuses().get();
        for (var i = 0; i < data.length; i++)
        {
            var document = data[i];
            var draftstatus;
            for (var j = 0; j < draftstatuses.length; j++) {
                if (draftstatuses[j].id === document.draftStatusId) {
                    draftstatus = draftstatuses[j].text;
                }
            }
            var htmlIcons = "";
            if (document.importanceId)
            {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var j = 0; j < importances.length; j++)
                {
                    if (importances[j].id === document.importanceId)
                    {
                        htmlIcons += "<i class='fa fa-exclamation fa-lg mr-sm' title='" + importances[j].text + "' style='color:" + importances[j].color + "'></i>";
                    }
                }
            }

            if (window.PriorityPrivacyAction == "2") {
                for (var j = 0; j < privacies.length; j++) {
                    if (privacies[j].id === document.privacyId) {
                        color = privacies[j].color;
                        break;
                    }
                }
            } else {
                for (var j = 0; j < priorities.length; j++) {
                    if (priorities[j].id === document.priorityId) {
                        color = priorities[j].color;
                        break;
                    }
                }
            }
            document.referenceNumber = document.referenceNumber ?? "";
            
         
            //var to = document.sendingEntity !== "" ? document.toStructure + (document.toUser !== "" ? '/' + document.toUser : document.toUser) : document.toUser;
            htmlLi += '<li class="mdl-li" data-transfer="' + document.id + '">';
            htmlLi += '<div class="mdl-container-document">';
            htmlLi += '<div id="leftbox" class="pull-left">';
            htmlLi += '<input data-id=' + document.id + ' data-categoryid=' + document.categoryId + ' type="checkbox" class="pointer m-sm ml-10px" onclick="event.stopPropagation();">';
            htmlLi += "<input type='hidden' data-id=" + document.id + " value='" + JSON.stringify(document) + "'/>";
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-circle icon-primary'></i></span>"
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-arrow-left'></i></span>"
            htmlLi += '</div>';
            htmlLi += '<div id="middlebox" class="mdl-text-msg pull-left pl-sm card-max-width">';
            //htmlLi += '<span class="dot"></span>';
            htmlLi += '<span class="mdl-span light_grey_color" data-field="referenceNumber" style="color:' + color + '" title="' + (document.referenceNumber || "") + '">' + (document.referenceNumber || "") + '</span>';
            htmlLi += '<span class="mdl-span text-primary bold" data-field="subject" style="color:' + color + '" title="' + (document.subject || "") + '">' + (document.subject || "") + '</span>';
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + (document.sendingEntity || "") + '">' + (document.sendingEntity || "") + '</span>';
            htmlLi += '<span class="mdl-span light_grey_color" data-field="draftstatus" style="color:' + color + '" title="' + (draftstatus || "") + '">' + (draftstatus || "") + '</span>';
            htmlLi += '</div>';
            htmlLi += '<div id="rightbox" class="pull-right text-right"><div class="mdl-time mr-sm" title="' + Resources.CreatedDate + '">' + dateFormat(document.createdDate) + '</div>';

            if (htmlIcons !== "")
            {
                htmlLi += '<div class="mdl-action">' + htmlIcons + '</div>';
            }
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</li>';
        }
        html += htmlLi;
        html += '</ul>';
        if (gPageIndex === 15)
        {
            $('#draftListContainer').html(html);
        } else
        {
            $('#draftListContainer ul').append(htmlLi);
        }
    }
}
function addFilters(d)
{
    d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
    if (gFromSearch)
    {
        d.CategoryId = $("#cmbFilterDraftCategory").val() !== null && typeof $("#cmbFilterDraftCategory").val() !== "undefined" ? $("#cmbFilterDraftCategory").val() : "0";
        d.FromDate = $("#filterDraftFromDate").val() !== "" && typeof $("#filterDraftFromDate").val() !== "undefined" ? $("#filterDraftFromDate").val() : "";
        d.ToDate = $("#filterDraftToDate").val() !== "" && typeof $("#filterDraftToDate").val() !== "undefined" ? $("#filterDraftToDate").val() : "";
        d.Subject = $("#txtFilterDraftSubject").val() !== "" && typeof $("#txtFilterDraftSubject").val() !== "undefined" ? $("#txtFilterDraftSubject").val() : "";
    }
}
function dateFormat(dateText)
{
    var dateFull = dateText.split(" ")[0].split("/");
    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth() + 1; //January is 0!
    var yy = today.getFullYear();
    var time = DateConverter.toHijriFormated(dateText.split(" ")[0], null, window.CalendarType);
    if (parseInt(dateFull[0]) === dd && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy)
    {
        time = "";
        var timeSeparator = ":";
        var hours = parseInt(dateText.split(" ")[1].split(":")[0]);
        var amPm = Resources.AM;
        if (hours > 12)
        {
            time += (hours - 12) + timeSeparator;
            amPm = Resources.PM;
        } else if (hours === 12)
        {
            time += "12" + timeSeparator;
            amPm = Resources.PM;
        } else
        {
            time += (hours < 10 ? '0' : '') + hours + timeSeparator;
            amPm = Resources.AM;
        }
        var minutes = parseInt(dateText.split(" ")[1].split(":")[1]);
        minutes = (minutes < 10 ? '0' : '') + minutes;
        time += minutes + " " + amPm;
    } else if (parseInt(dateFull[0]) === (dd - 1) && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy)
    {
        time = Resources.Yesterday;
    }
    return time;
}
var gLocked = false;
var gNoMoreData = false;
var gFromSearch = false;
var gPageIndex = 0;
var gSelectedRowId, gSelf;
var wrapperParent;
class VipDocumentDraftView extends Intalio.View
{
    constructor(element, model)
    {
        wrapperParent = model;
        super(element, "vipdraft", model);
    }
    render()
    {
        $.fn.select2.defaults.set("theme", "bootstrap");
        gSelf = this;        
        gLocked = false;
        gPageIndex = 0;
        gNoMoreData = false;
        gFromSearch = false;
        gSelectedRowId = null;
        var followUpCategoryIndex = gSelf.model.categories.findIndex(item => item.id == window.FollowUpCategory);
        gSelf.model.categories.splice(followUpCategoryIndex, 1);
        buildFilters(gSelf.model.nodeId, gSelf.model.categories);
        loadDraftList();
        var lastScrollTop = 0;
        $('#draftListContainer').on('scroll', function ()
        {
            var div = $(this);
            var st = div.scrollTop();
            if (st > lastScrollTop && st + div.innerHeight() + 5 >= div[0].scrollHeight)
            {
                if (!gLocked)
                {
                    gLocked = true;
                    try
                    {
                        loadDraftList();
                    } catch (e)
                    {
                        gLocked = false;
                    }
                }
            }
            lastScrollTop = st;
        });
        $('#draftListContainer').on('click', 'li', function (e)
        {
            if (!gLocked)
            {
                gLocked = true;
                try
                {
                    var input = this.getElementsByTagName('input')[0];
                    if (typeof input !== "undefined")
                    {
                        if (!$(this).hasClass("active"))
                        {
                            var id = input.getAttribute("data-id");
                            if (Number(gSelectedRowId) !== Number(id))
                            {
                                if (!$(".filterInfoDiv").hasClass("hidden"))
                                {
                                    $(".filterInfoDiv").addClass("hidden");
                                }
                                openDocument(id, gSelf.model.nodeId);
                            } else
                            {
                                gLocked = false;
                            }
                        } else
                        {
                            gLocked = false;
                        }
                    } else
                    {
                        gLocked = false;
                    }
                } catch (e)
                {
                    gLocked = false;
                }
            }
        });
        $('#draftListContainer').on('click', 'input', function ()
        {
            var input = this;
            if (typeof input !== "undefined")
            {
                input.checked = input.checked ? false : true;
            }
        });
        $('#chkAll').change(function ()
        {
            var checked = $(this).prop('checked');
            $('#draftListContainer input[type="checkbox"]').prop('checked', checked);
        });
        $('#draftListContainer').on('change', 'input[type="checkbox"]', function () {
            var total = $('#draftListContainer input[type="checkbox"]').length;
            var checked = $('#draftListContainer input[type="checkbox"]:checked').length;

            $('#chkAll').prop('checked', total === checked);
        });
        $("#btnDraftDelete").on('click', function ()
        {
            if (!$(".filterInfoDiv").hasClass("hidden"))
            {
                $(".filterInfoDiv").addClass("hidden");
            }
            var checkedRows = $('#draftListContainer li').find('input[type="checkbox"]:checked');
            if (checkedRows.length > 0)
            {
                var ids = new Array();
                checkedRows.each(function (index, obj)
                {
                    ids.push(obj.getAttribute('data-id'));
                });
                Common.showConfirmMsg(Resources.DeleteConfirmationCorrespondence, function ()
                {
                    Common.mask(document.getElementById('draftRow'), "draftRow-mask");
                    Common.ajaxDelete('/Document/Delete',
                        {
                            'ids': ids, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                        },
                        function (result)
                        {
                            if (result != null/* && result.length > 0*/)
                            {
                                swal.close()
                                let msg = "";
                                for (var i = 0; i < result.length; i++)
                                {
                                    if (!result[i].updated)
                                    {
                                        msg += "\n ○ " + result[i];
                                    }
                                }
                                if (msg !== "")
                                {
                                    setTimeout(function ()
                                    {
                                        Common.alertMsg(Resources.CannotDeleteRegisteredDocumentsWarning + msg);
                                    }, 500);
                                    Common.unmask("draftRow-mask");
                                }
                                for (var i = 0; i < ids.length; i++)
                                {
                                    var canDelete = true;
                                    var li = $($("input[data-id='" + ids[i] + "']").parents("li")[0]);
                                    if (result.length > 0)
                                    {
                                        var spans = li.find("#middlebox span");
                                        if (spans.length === 3)
                                        {
                                            if ($.grep(result, function (element, index)
                                            {
                                                return element === spans[1].textContent;
                                            }).length > 0)
                                            {
                                                canDelete = false;
                                            }
                                        }
                                    }
                                    if (canDelete && msg == "")
                                    {
                                        li.fadeOut().remove();
                                        if (Number(gSelectedRowId) === Number(ids[i]))
                                        {
                                            $(".withBorders-o").addClass("waitingBackground");
                                            $("#draftDocumentDetailsContainer").empty();
                                        }
                                    }
                                }
                                Common.unmask("draftRow-mask");
                                TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                            } else
                            {
                                Common.unmask("draftRow-mask");
                                Common.showScreenErrorMsg();
                            }

                        }, null, false);
                });
            }
            else
            {
                Common.alertMsg(Resources.NoRowSelected);
            }
        });
        $(document).click(function (e)
        {
            if ($(e.target).hasClass("select2-selection__choice__remove") || e.target.tagName.toLowerCase() === "body")
            {
                return;
            }
        });
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        SecurityMatrix.initContextMenuVip(securityMatrix, gSelf.model.nodeId);
        SecurityMatrix.initToolbarMenuVip(securityMatrix, gSelf.model.nodeId, 'draftListContainer');

        $('.filterInfoDiv').draggable({ containment: 'window', cancel: '.cancelDrag' });
        $("#btnOpenSearchDraftModal").on("click", function ()
        {
            if (window.language == "ar")
            {
                if ($(".vipDetailsPanel").css('display') == 'none') {
                    var position = $("#btnOpenSearchDraftModal").position().left + $("#btnOpenSearchDraftModal").width() + 970;
                    $(".filterInfoDiv").attr('style', 'right:' + position + 'px;top:170px;');
                }
                else
                    $(".filterInfoDiv").attr('style', 'right:' + $("#btnOpenSearchDraftModal").position().right + 'px;top:170px;');
            }
            else
            {
                if ($(".vipDetailsPanel").css('display') == 'none') {
                    var position = $("#btnOpenSearchDraftModal").position().left - 220;
                    $(".filterInfoDiv").attr('style', 'left:' + position + 'px;top:170px;');

                }
                else
                    $(".filterInfoDiv").attr('style', 'left:' + $("#btnOpenSearchDraftModal").position().left + 'px;top:170px;');
            }
            if ($(".filterInfoDiv").hasClass("hidden"))
            {
                $(".filterInfoDiv").removeClass("hidden");
            } else
            {
                $(".filterInfoDiv").addClass("hidden");
            }
        });

        $("#btnFilterCloseIcon").on("click", function ()
        {
            $(".filterInfoDiv").addClass("hidden");
        });
        $('#filtersContainer input').off('keydown');
        $('#filtersContainer input').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                $("#btnFilterDraftSearch").trigger('click');
            }
        });
        $('.toggleVIP').on('click', function () {
            if (window.InboxMode === "LocalVIPView") {
                window.InboxMode = "InboxDefault";
            } else if (window.InboxMode !== "InboxDefault") {
                window.InboxMode = "LocalInboxDefaultView";
            }

            let wrapper = $(".content-wrapper");
            let defaultmodel = new DocumentDraft.DocumentDraft();
            defaultmodel.nodeId = wrapperParent.nodeId;
            defaultmodel.delegationId = wrapperParent.delegationId;
            defaultmodel.categories = new Categories().get(window.language, wrapperParent.delegationId);
            defaultmodel.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            defaultmodel.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            defaultmodel.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            defaultmodel.title = $('.content-heading').text();
            let documentView = new DocumentDraft.DocumentDraftView(wrapper, defaultmodel);
            documentView.render();
        })
    }
}
export default { VipDocumentDraft, VipDocumentDraftView };