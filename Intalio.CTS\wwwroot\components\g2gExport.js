import Intalio from './common.js'


class G2GExport extends Intalio.Model {
    constructor() {
        super();

        this.documentId;
        this.g2gInternalId;
    }
}
class G2GExportView extends Intalio.View {
    constructor(element, model) {
        super(element, "g2gexport", model);
    }

    render() {
        self = this;
        document.cookie = "Token="+window.IdentityAccessToken;
        //sessionStorage.setItem("Token", window.IdentityAccessToken);

        $(self.refs['exportg2gFrame']).attr("src", window.g2GUrl + "/G2GExport.aspx?DocumentId=" + self.model.documentId + "&G2G_Internal_ID=" + self.model.g2gInternalId + "&lang=" + window.language + "&ExportedByGctId=" + $('#hdUserId').val() + "&ExportedByStructureGctId=" + $('#hdLoggedInStructureId').val());

        $("#btnCloseG2GExport, .g2gExportModalClose").on('click', function () {
            $('#modalg2gExport').modal('hide');
        });
    }
}

export default { G2GExport, G2GExportView };