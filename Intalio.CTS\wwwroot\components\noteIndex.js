import Intalio from './common.js'
var gIsLocked = false;
class NoteIndex extends Intalio.Model
{
    constructor()
    {
        super();
        this.transferId = null;
        this.documentId = null;
        this.noteId = null;
        this.delegationId = null;
    }
}
function submitNote(self, closeModal)
{
    var $form = $(self.refs['formIndexPost']);
    $form.parsley().reset();
    var note = jQuery(CKEDITOR.instances[self.model.ComponentId + '_txtAreaNote'].getData()).text().replaceAll("\n", "").replaceAll("\t", "");
    if (note.trim().length < 10)
    {
        $(self.refs['formIndexPost'])[0][0].value = note;
    }
    var isValid = $form.parsley().validate();
    if (isValid)
    {
        gIsLocked = true;
        let params = {
            'Notes': CKEDITOR.instances[self.model.ComponentId + '_txtAreaNote'].getData(),
            'DocumentId': self.model.documentId,
            'TransferId': self.model.transferId,
            'IsPrivate': $(self.refs['chkIsPrivate'])[0].checked,
            'delegationId': self.model.delegationId,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        };
        if (self.model.noteId)
        {
            params.Id = self.model.noteId;
        }
        var btn = $(self.refs['btnSubmitNote']);
        btn.button('loading');
        var btnSubmitClose = $(self.refs['btnSubmitCloseNote']);
        var btnClose = $(self.refs['btnCloseNote']);
        var btnCloseX = $(self.refs['noteClose']);
        btnClose.attr('disabled', 'disabled');
        btnCloseX.attr('disabled', 'disabled');
        btnSubmitClose.attr('disabled', 'disabled');
        Common.ajaxPost('/Note/Index', params, function (data)
        {
            gIsLocked = false;
            if (data.message === Resources.NoPermission)
            {
                if (closeModal)
                {
                    setTimeout(function ()
                    {
                        Common.alertMsg(Resources.NoPermission);
                    }, 800);
                } else
                {
                    Common.alertMsg(Resources.NoPermission);
                }
            }
            else
            {
                Common.showScreenSuccessMsg();
                self.model.noteId = data.id;
                $(self.refs['modalNoteTitle']).html(Resources.Edit);
                $(".grdNoteItems").DataTable().ajax.reload();
                GridCommon.Refresh("grdFollowUpItems");
                TreeNode.refreshTreeNodeCounts(TreeNode.FollowUp);
            }
            btn.button('reset');
            btnClose.removeAttr('disabled');
            btnCloseX.removeAttr('disabled');
            btnSubmitClose.removeAttr('disabled');
            if (closeModal)
            {
                $(self.refs['btnCloseNote']).trigger("click");
            }
        }, function () { gIsLocked = false; btn.button('reset'); btnClose.removeAttr('disabled'); btnCloseX.removeAttr('disabled'); btnSubmitClose.removeAttr('disabled'); Common.showScreenErrorMsg(); }, false);
    }
}
class NoteIndexView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "noteindex", model);
    }
    render()
    {
        var self = this;
        $(self.refs['btnCloseNote']).keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $(self.refs['btnSubmitCloseNote']).focus();
                }
                else
                {
                    setTimeout(function ()
                    {
                        CKEDITOR.instances[model.ComponentId + '_txtAreaNote'].focus();
                    }, 100);
                }
            }
        });
        var model = this.model;
        if (typeof CKEDITOR.instances[model.ComponentId + '_txtAreaNote'] !== 'undefined')
        {
            CKEDITOR.instances[model.ComponentId + '_txtAreaNote'].destroy(true);
        }
        CKEDITOR.on('instanceReady', function ()
        {
            $('#' + model.ComponentId + '_txtAreaNote').attr('required', '');
            $.each(CKEDITOR.instances, function (instance)
            {
                CKEDITOR.instances[instance].on("change", function (e)
                {
                    for (instance in CKEDITOR.instances)
                    {
                        CKEDITOR.instances[instance].updateElement();
                    }
                });
            });
        });
        $('#cke_1_contents').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $(self.refs['btnCloseNote']).focus();
                } else
                {
                    $(self.refs['btnSubmitNote']).focus();
                }
            }
        });
        if (document.getElementById(model.ComponentId + '_txtAreaNote') !== null)
        {
            var textarea = document.getElementById(model.ComponentId + '_txtAreaNote');
            CKEDITOR.replace(textarea, {
                customConfig: "/lib/ckeditor/custom-config.js", language: window.languageVal, resize_enabled: false, wordcount: {
                    showWordCount: false,
                    showCharCount: true,
                    maxWordCount: 4,
                    maxCharCount: 1000
                }
            });
        }
        $.fn.select2.defaults.set("theme", "bootstrap");
        $(self.refs['formIndexPost']).keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                e.preventDefault();
                $(self.refs['btnSubmitNote']).trigger("click");
            }
        });
        $(self.refs['btnSubmitNote']).on('click', function (event, closeModal)
        {
            try
            {
                if (gIsLocked === false)
                {
                    if (window.EnableConfirmationMessage === "True")
                    {
                        Common.showConfirmMsg(Resources.SubmitNoteConfirmation, function ()
                        {
                            submitNote(self, closeModal);
                        });
                    } else
                    {
                        submitNote(self, closeModal);
                    }
                }
            } catch (ex)
            {
                gIsLocked = false;
            }
        });
        $(self.refs['btnSubmitCloseNote']).on('click', function ()
        {
            $(self.refs['btnSubmitNote']).trigger("click", [true]);
        });
    };
    setData(data)
    {
        CKEDITOR.instances[this.model.ComponentId + '_txtAreaNote'].setData(data.notes);
        CKEDITOR.instances[this.model.ComponentId + '_txtAreaNote'].updateElement();
        $(this.refs['chkIsPrivate'])[0].checked = data.isPrivate;
    }
}
export default { NoteIndex, NoteIndexView };
