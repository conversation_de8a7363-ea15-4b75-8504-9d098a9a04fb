﻿using Intalio.Core;
using Intalio.Core.API;
using Intalio.Core.DAL;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.API
{
    public static class ManageNonArchivedAttachments
    {
        /// <summary>
        /// User must have access on transfer or on document
        /// </summary>
        public static bool Create(NonArchivedAttachmentsViewModel model, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId, Language language)
        {
            bool retValue = false;
            if (ManageUserAccess.HaveAccess(model.DocumentId, model.TransferId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                var createdByDelegatedUserId = !delegationId.IsNull() ? ManageDelegation.GetByDelegationId(userId, delegationId.Value)?.FromUserId : default(long?);
                var item = new NonArchivedAttachments
                {
                    Description = model.Description,
                    CreatedByUserId = userId,
                    CreatedByDelegatedUserId = createdByDelegatedUserId,
                    DocumentId = model.DocumentId,
                    Quantity = model.Quantity,
                    TypeId = model.TypeId,
                    CreatedDate = DateTime.Now,
                    TransferId = model.TransferId
                };
                item.Insert();
                model.Id = item.Id;
                retValue = true;
                var user = new Intalio.Core.DAL.User().Find(userId);
                ManageActivityLog.AddActivityLog(model.DocumentId, model.TransferId.HasValue ? model.TransferId.Value : null, (int)Core.ActivityLogs.AddNonArchivedAttachment, userId, "", $"{model.Id}", $"{TranslationUtility.Translate("AddedBy", language)} {user.Firstname} {user.Lastname}");
            }
        
            return retValue;
        }

    /// <summary>
    /// Case 1: CreatedByUserId equals current user id
    /// Case 2: if delegationId have value return to case 1
    /// Case 3: if delegationId don't have value and CreatedByUserId equals current user id 
    /// and transferId if have value must equal to nonArchivedAttachments transferId 
    /// </summary>
        public static bool Edit(NonArchivedAttachmentsViewModel model, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId, Language language)
        {
            bool retValue = false;
            if (ManageUserAccess.HaveAccess(model.DocumentId, model.TransferId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                NonArchivedAttachments item = new NonArchivedAttachments().Find(model.Id.Value);
                if(item.DocumentLockId.HasValue)
                {
                    return retValue;
                }
                if (item != null && ManageUserAccess.HaveEditAccess(item.CreatedByUserId, item.CreatedByDelegatedUserId, item.TransferId, model.TransferId, userId, delegationId))
                {
                    var createdByDelegatedUserId = !delegationId.IsNull() ? ManageDelegation.GetByDelegationId(userId, delegationId.Value)?.FromUserId : default(long?);
                    item.CreatedByDelegatedUserId = createdByDelegatedUserId;
                    item.Description = model.Description;
                    item.Quantity = model.Quantity;
                    item.TypeId = model.TypeId;
                    item.Update();
                    retValue = true;
                    var user = new Intalio.Core.DAL.User().Find(userId);
                    ManageActivityLog.AddActivityLog(model.DocumentId, model.TransferId.HasValue ? model.TransferId.Value : null, (int)Core.ActivityLogs.EditNonArchivedAttachment, userId, "", $"{model.Id}", $"{TranslationUtility.Translate("EditedBy", language)} {user.Firstname} {user.Lastname}");
                }
            }

            return retValue;
        }

        /// <summary>
        /// User must have access on transfer or on document
        /// </summary>
        public static async Task<(int, List<NonArchivedAttachmentsListViewModel>)> List(int startIndex, int pageSize, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language language = Language.EN)
        {
            var retValue = (0, new List<NonArchivedAttachmentsListViewModel>());
            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                NonArchivedAttachments item = new NonArchivedAttachments();
                var countResult = item.GetCount(documentId);
                var itemList = await item.ListAsync(startIndex, pageSize, documentId);
                retValue = (await countResult, itemList.Select(t => new NonArchivedAttachmentsListViewModel
                {
                    Id = t.Id,
                    CreatedBy = language == Language.EN ? $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.CreatedByUser.Id, language)}",
                    CreatedByDelegatedUser = t.CreatedByDelegatedUser != null ?
                  (language == Language.EN ? $"{t.CreatedByDelegatedUser.Firstname} {t.CreatedByDelegatedUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.CreatedByDelegatedUser.Id, language)}")
                    : string.Empty,
                    CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                    Quantity = t.Quantity,
                    Description = t.Description,
                    TypeId = t.TypeId,
                    Type = t.Type != null ? language == Language.EN ? t.Type.Name : language == Language.FR ? !string.IsNullOrEmpty(t.Type.NameFr) ? t.Type.NameFr : t.Type.Name : language == Language.AR ? !string.IsNullOrEmpty(t.Type.NameAr) ? t.Type.NameAr : t.Type.Name : t.Type.Name : string.Empty,
                    IsEditable = t.DocumentLockId.IsNull() && ManageUserAccess.HaveEditAccess(t.CreatedByUserId, t.CreatedByDelegatedUserId, t.TransferId, transferId, userId, delegationId),
                    IsCreatedByDelegator = t.CreatedByDelegatedUserId.HasValue
                }).ToList());
                ManageActivityLog.AddFullActivityLog(documentId, transferId, (int)ActivityLogs.ViewNonArchivedAttachments, userId, "", "");
            }
            return retValue;
        }

        /// <summary>
        /// Case 1: CreatedByUserId equals current user id
        /// Case 2: if delegationId have value return to case 1
        /// Case 3: if delegationId don't have value and CreatedByUserId equals current user id 
        /// and transferId if have value must equal to nonArchivedAttachments transferId 
        /// </summary>
        public static bool Delete(long id, long documentId, long? transferId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId, Language language)
        {
            bool retValue = false;
            if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                var item = new NonArchivedAttachments().Find(id);
                if(item.DocumentLockId.HasValue)
                {
                    return retValue;
                }
                if (item != null && ManageUserAccess.HaveEditAccess(item.CreatedByUserId, item.CreatedByDelegatedUserId, item.TransferId, transferId, userId, delegationId))
                {
                    item.Delete(id);
                    retValue = true;
                    var user = new Intalio.Core.DAL.User().Find(userId);
                    ManageActivityLog.AddActivityLog(documentId, transferId.HasValue ? transferId.Value : null, (int)Core.ActivityLogs.DeleteNonArchivedAttachment, userId, "", $"{id}", $"{TranslationUtility.Translate("DeletedBy", language)} {user.Firstname} {user.Lastname}");
                }
            }
            return retValue;
        }

        public static List<short> CheckTypeInUse(List<short> ids)
        {
            List<short> retValue = new List<short>();
            foreach (var id in ids)
            {
                if (new NonArchivedAttachments().CheckTypeInUse(id))
                {
                    retValue.Add(id);
                }
            }
            return retValue;
        }
    }
}
