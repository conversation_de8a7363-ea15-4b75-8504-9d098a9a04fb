﻿import { Categories, CategoryModel, DelegationUsers, Types, IdentityService, FollowUpStatuses, FollowUpStatusModel } from './lookup.js'
import Parameter from './parameter.js'
import ParameterIndex from './parameterindex.js'
import ScanConfiguration from './scanConfigurationList.js'
import ScanConfigurationIndex from './scanConfigurationIndex.js'
import Template from './templateList.js'
import TemplateIndex from './templateIndex.js'
import TreeNodeActions from './treeNodeActions.js'
import Category from './categoryList.js'
import CategoryIndex from './categoryIndex.js'
import CategoryReferenceNumber from './categoryReferenceNumberList.js'
import CategoryReferenceNumberIndex from './categoryReferenceNumberIndex.js'
import CategoryReferenceCounter from './categoryReferenceCounterList.js'
import CategoryReferenceCounterIndex from './categoryReferenceCounterIndex.js'
import EventReceiverIndex from './eventReceiverIndex.js'
import ActivityLog from './activityLogList.js'
import AttachmentFolder from './attachmentFolderList.js'
import AttachmentFolderIndex from './attachmentFolderIndex.js'
import DocumentDraft from './draftList.js'
import DocumentInbox from './inboxList.js'
import DocumentCompleted from './completedList.js'
import DocumentMyRequests from './myRequestsList.js'
import DocumentBasket from './basketList.js'
import CategoryManagementView from './categoryManagement.js'
import DocumentSearch from './search.js'
import DocumentAdvanceSearch from './advanceSearch.js'
import DocumentAdvanceSearchConfiguration from './advanceSearchConfiguration.js'
import DocumentAdvanceSearchConfigurationColumns from './advanceSearchConfigurationColumns.js'
import CreateByTemplate from './createByTemplate.js'
import CreateByFile from './createByFile.js'
import NonArchivedAttachmentsTypes from './nonArchivedAttachmentsTypesList.js'
import NonArchivedAttachmentsTypesIndex from './nonArchivedAttachmentsTypesIndex.js'
import DocumentDetails from './documentDetails.js'
import BarcodeConfiguration from './barcodeConfigurationList.js'
import BarcodeConfigurationIndex from './barcodeConfigurationIndex.js'
import FilingPlan from './filingPlanList.js'
import FilingPlanIndex from './filingPlanIndex.js'
import BarcodeIndex from './barcodeIndex.js'
import DocumentSent from './sentList.js'
import Home from './home.js'
import CategoryImport from './categoryImport.js'
import AuditTrailValues from './auditTrailValues.js'
import NodeList from './nodeList.js'
import NodeIndex from './nodeIndex.js'
import Bookmark from './bookmark.js'
import DocumentClosed from './closedList.js'
import Export from './export.js'
import Import from './import.js'
import SystemDashboard from './dashboardSystem.js'
import UserDashboard from './dashboardUser.js'
import KPI from './kpi.js'
import ReportInProgressTransfers from './reportInProgressTransfers.js'
import ReportCompletedTransfers from './reportCompletedTransfers.js'
import ReportOperationByUser from './reportOperationByUser.js'
import ReportOperationByCorrespondence from './reportOperationByCorrespondence.js'
import ReportStatisticalCorrespondences from './reportStatisticalCorrespondences.js'
import ReportCorrespondenceDetailFilter from './reportCorrespondenceDetailFilter.js'
import ReportInProgressCorrespondences from './reportInProgressCorrespondences.js'
import ReportCompletedCorrespondences from './reportCompletedCorrespondences.js'
import VipDocumentInbox from './vipInboxList.js'
import VipDocumentCompleted from './vipCompletedList.js'
import VipDocumentSent from './vipSentList.js'
import VipDocumentMyRequests from './vipMyRequestsList.js'
import VipDocumentClosed from './vipClosedList.js'
import VipDocumentDraft from './vipDraftList.js'
import EntityGroup from './entityGroupList.js'
import DocumentManageCorrespondence from './manageCorrespondence.js'
import Purpose from './purpose.js'
import CTSDelegation from './delegation.js'
import CTSSystemDelegation from './systemdelegation.js'
import movetransfers from './movetransfers.js'
import AutoForward from './autoForward.js'
import Role from './role.js'
import DocumentManageStructureUsersCorrespondences from './manageStructureUsersCorrespondences.js'
import FollowUpDetails from '../components/FollowUpDetails.js'


// Used for export
import Widget from './widget.js'
import MyTransfer from './myTransfer.js'
import DocumentMetadata from './document.js'
import DocumentAttachment from './attachment.js'
import DocumentNote from './noteList.js'
import DocumentLinkCorrespondence from './linkedCorrespondenceList.js'
import DocumentNonArchivedAttachment from './nonArchivedAttachmentsList.js'
import VisualTracking from './visualTracking.js'
import TransferHistory from './transferHistoryList.js'
import ActivityLogs from './activityLogTimeline.js'
import FavoriteStructures from './favoriteStructures.js'
import DistributionList from './distributionList.js'
import DistributionIndex from './distributionIndex.js'
import ActivityLogTimeline from './activityLogTimeline.js'
import SendTransferModal from './sendTransfer.js'
import Transfer from './transfer.js'
import searchLinkedDocumnet from './searchLinkedDocumnet.js'

import Committee from './committeeList.js'


import User from './user.js'
import OrganizationManagement from './organizationManagement.js'
import AgendaTopicsList from './AgendaTopicsList.js'
import AgendaCorrespondenceDocument from './AgendaCorrespondenceDocument.js'
import AgendaCorrespondenceDocumentView from './AgendaCorrespondenceDocument.js'
import MeetingAgendaIndex from './meetingAgendaIndex.js'
//meetingAgendaIndex

import OrganiztionManagementDepartmentUser from './OrganiztionManagementDepartmentUser.js'
//end
import Assignee from './assigneeList.js'
import AssigneeIndex from './assigneeIndex.js'
import ManageFollowUp from './manageFollowUp.js'
import TaskSearch from './taskSearch.js'
import TemplatesManagement from './templatesManagement.js'
import UserNodeIndex from '../components/userNodeIndex.js'
import UserNodeList from '../components/userNodeList.js'
import UserNodeCorrespondance from '../components/userNodeListCorrespondance.js'
import VipUserNodeCorrespondance from '../components/vipUserNodeListCorrespondance.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import Profile from '../components/profile.js'
import FollowUpList from './followUpList.js'
import followUpIndex from '../components/followUpIndex.js'



// This is a global file that is read by all other js files
$(document).ready(function () {

        // Set global defaults for all DataTables
    if ($.fn.dataTable.defaults.paging !== false) {
        $.fn.dataTable.defaults.lengthMenu = [10, 25, 50, 100, 200]; // Default lengthMenu
        }
        //$.fn.dataTable.defaults.pageLength = 10; // Default page length
        //$.fn.dataTable.defaults.processing = true; // Enable processing indicator
        //$.fn.dataTable.defaults.ordering = true; // Enable ordering
        //$.fn.dataTable.defaults.serverSide = true; // Enable server-side processing
    //$.extend(true, $.fn.dataTable.defaults, {
    //    pageLength: 10, // Default page length
    //    lengthMenu: [10, 25, 50, 100, 200], // Add 200 rows option
    //    processing: true,
    //    ordering: true,
    //    serverSide: true
    //});
    new CoreComponents.Common.ViewConfiguration(".content-wrapper");
    CoreComponents.Common.parameters = { structureNameAr: window.StructureNameAr, structureNameFr: window.StructureNameFr, calendarType: window.CalendarType };
    Handlebars.registerHelper("setVar", function (varName, varValue, options) {
        options.data.root[varName] = varValue;
    });
    Handlebars.registerHelper("addVar", function (varName, varValue, options) {
        //for number incrementation
        if (options.data.root[varName]) {
            options.data.root[varName] += varValue;
        } else {
            options.data.root[varName] = varValue;
        }
    });
    Handlebars.registerHelper('switch', function (value, options) {
        this.switch_value = value;
        this.switch_break = false;
        return options.fn(this);
    });
    Handlebars.registerHelper('case', function (value, options) {
        if (value == this.switch_value) {
            this.switch_break = true;
            return options.fn(this);
        }
    });
    Handlebars.registerHelper('default', function (value, options) {
        if (this.switch_break == false) {
            return options.fn(this);
        }
    });
    Handlebars.registerHelper('tabIndex', function (value, options) {
        return (parseInt(value) + 13);
    });
    Handlebars.registerHelper('ifNotNull', function (value, options) {
        if (value != null)
            return options.fn(this);
        else
            return options.inverse(this);
    });
    Handlebars.registerHelper('ifindexPerThree', function (value, options) {
        if (parseInt(value) % 3 === 0)
            return options.fn(this);
    });
    Handlebars.registerHelper('ifindexPerThreePlusone', function (value, options) {
        if ((parseInt(value) + 1) % 3 === 0)
            return options.fn(this);
    });
    Handlebars.registerHelper("setMinMAx", function (minvalue, maxvalue, dataType, ismin, options) {
        switch (dataType) {
            case "String":
                return ismin ? minvalue == null ? 0 : minvalue : maxvalue == null ? 32767 : maxvalue;
            case "Text":
                return ismin ? minvalue == null ? 0 : minvalue : maxvalue == null ? 32767 : maxvalue;
            case "Short":
                return ismin ? minvalue == null ? 0 : minvalue : maxvalue == null ? 32767 : maxvalue;
            case "Integer":
                return ismin ? minvalue == null ? 0 : minvalue : maxvalue == null ? 2147483647 : maxvalue;
            case "Long":
                return ismin ? minvalue == null ? 0 : minvalue : maxvalue == null ? 9223372036854775807 : maxvalue;
            case "Date":
                break;
            case "Datetime":
                break;
            case "Time":
                break;
            case "List":
                break;
            case "MultiList":
                break;
            case "Binary":
                break;
        }

    });

    var appRouter = Backbone.Router.extend({
        routes: {
            //'manageStructureUsersCorrespondences':'manageStructureUsersCorrespondencesRoute',
            //'exceptionlog': 'exceptionLogRoute',
            //'purpose': 'purposeRoute',
            //'status': 'statusRoute',
            //'priority': 'priorityRoute',
            //'applicationserver': 'applicationServerRoute',
            //'parameter': 'parameterRoute',
            //'translatordictionary': 'translatorDictionaryRoute',
            //'scanconfiguration': 'scanConfigurationRoute',
            //'notificationtemplate': 'notificationTemplateRoute',
            //'organizationManagement': 'organizationManagementRoute',
            //'manageTemplate': 'manageTemplateRoute',
            //'manageCategory': 'manageCategoryRoute',
            //'categoryreferencenumber': 'categoryReferenceNumberRoute',
            //'categoryreferencecounter': 'categoryReferenceCounterRoute',
            //'classification': 'classificationRoute',
            //'importance': 'importanceRoute',
            //'privacy': 'privacyRoute',
            //'attachmentfolder': 'attachmentfolderRoute',
            //'todolist': 'toDoListRoute',
            //'assembly': 'assemblyRoute',
            //'activitylog': 'activityLogRoute',
            //'menu': 'menuRoute',
            //'action': 'actionRoute',
            //'tab': 'tabRoute',
            //'user': 'userRoute',
            //'role': 'roleRoute',
            //'node': 'nodeRoute',
            //'category': 'categoryRoute',
            //'lookup': 'lookupRoute',
            'draft/:nodeId': 'draftRoute',
            'draft/:nodeId/:delegationId': 'draftRoute',
            'inbox/:nodeId': 'inboxRoute',
            'Custom/:nodeId': 'customRoute',
            'StructureInbox/:nodeId': 'StructureInboxRoute',
            'inbox/:nodeId/:delegationId': 'inboxRoute',
            'inbox/:nodeId/transfer/:transferId': 'inboxRouteWithTransfer',
            'StructureInbox/:nodeId/:delegationId': 'StructureInboxRoute',
            'completed/:nodeId': 'completedRoute',
            'completed/:nodeId/:delegationId': 'completedRoute',
            'myrequests/:nodeId': 'myRequestsRoute',
            'basket/:basketId': 'basketRoute',
            'sent/:nodeId': 'sentRoute',
            'sent/:nodeId/:delegationId': 'sentRoute',
            'StructureSent/:nodeId': 'StructureSentRoute',
            'StructureSent/:nodeId/:delegationId': 'StructureSentRoute',
            'closed/:nodeId': 'closedListRoute',
            'userNode': 'userNodeRoute',

            'userNode/:nodeId': 'userNodeCorrespondanceRoute',
            'userNode/:nodeId/:delegationId': 'userNodeCorrespondanceRoute',
            //'delegation': 'delegationRoute',
            //'documenttype': 'documenttypeRoute',
            'search': 'searchRoute',
            'search/:term': 'searchRoute',
            //'advanceSearch': 'advanceSearchRoute',
            //'advanceSearch/:delegationId': 'advanceSearchRoute',
            //'advanceSearchConfiguration': 'advanceSearchConfigurationRoute',
            'document/:id': 'documentRoute',
            'document/:id/:delegationId': 'documentRoute',

            'create/:id/:name': 'createDocumentRoute',
            'createbytemplate/:id/:name': 'createDocumentByTemplateRoute',
            'createbyfile/:id/:name': 'createDocumentByFileRoute',
            /*'nonarchivedattachmentstypes': 'nonArchivedAttachmentsTypesRoute',*/
            'inboxdocumentdetails/:id': 'inboxDocumentDetailsRoute',
            'inboxdocumentdetails/:id/:delegationId': 'inboxDocumentDetailsRoute',
            'completeddocumentdetails/:id': 'completedDocumentDetailsRoute',
            'completeddocumentdetails/:id/:delegationId': 'completedDocumentDetailsRoute',
            'myrequestsdocumentdetails/:id': 'myRequestsDocumentDetailsRoute',
            //'barcodeconfiguration': 'barcodeConfigurationRoute',
            //'filingPlan': 'filingPlanRoute',
            'linkeddocumentdetails/:id': 'linkedDocumentDetailsRoute',
            'linkeddocumentdetails/:id/:delegationId': 'linkedDocumentDetailsRoute',
            //'nodelist': 'nodeListRoute',
            //'bookmarks': 'bookmarksRoute',
            //'systemdashboard': 'systemDashboardRoute',
            //'userdashboard': 'userDashboardRoute',
            //'systeminprogresstransfers': 'systemInProgressTransfersRoute',
            //'averagedurationforcorrespondencecompletion': 'averageDurationForCorrespondenceCompletionRoute',
            //'averagedurationforcorrespondencedelay': 'averageDurationForCorrespondenceDelayRoute',
            //'averagedurationfortransfercompletion': 'averageDurationForTransferCompletionRoute',
            //'averagedurationfortransferdelay': 'averageDurationForTransferDelayRoute',
            //'reportinprogresstransfers': 'reportInProgressTransfersRoute',
            //'reportcompletedtransfers': 'reportCompletedTransfersRoute',
            //'reportOperationByUser': 'reportOperationByUserRoute',
            //'reportOperationByCorrespondence': 'reportOperationByCorrespondenceRoute',
            //'reportstatisticalcorrespondences': 'reportStatisticalCorrespondencesRoute',
            //'reportcorrespondencedetailfilter': 'reportCorrespondenceDetailFilterRoute',
            //'reportinprogresscorrespondences': 'reportInProgressCorrespondencesRoute',
            //'reportcompletedcorrespondences': 'reportCompletedCorrespondencesRoute',
            //'entitygroup': 'entityGroupRoute',
            //'manageCorrespondence': 'manageCorrespondenceRoute',
            ////'newautoforward': 'newAutoForwardRoute',
            //'autoForward': 'autoForwardRoute',
            //'favoriteStructures': 'favoriteStructuresRoute',
            //'distributionList': 'distributionListRoute',
            'OrganiztionManagementDepartmentUser': 'OrganiztionManagementDepartmentUserRoute',
            'tasksearch': 'taskSearchRoute',
            'profile':'profileRoute',
            'templatesManagement': 'templatesManagementRoute',
            'FollowUp/:nodeId': 'FollowUpRoute',
            'FollowUpDocument/:id': 'openFollowUpRoute',
            '': 'rootRoute',

        },
        execute: function (callback, args) {
            if (window.InboxMode === 'LocalVIPView') {
                window.InboxMode = 'InboxDefault';

            } else if (window.InboxMode === 'LocalInboxDefaultView') {
                window.InboxMode = 'InboxVIPView';
            }

            if (callback) callback.apply(this, args);
        },
        rootRoute: function () {
            rootRoute();
        },
        applicationServerRoute: function () {
            applicationServerRoute();
        },
        parameterRoute: function () {
            parameterRoute();
        },
        exceptionLogRoute: function () {
            exceptionLogRoute();
        },
        purposeRoute: function () {
            purposeRoute();
        },
        statusRoute: function () {
            statusRoute();
        },
        priorityRoute: function () {
            priorityRoute();
        },
        translatorDictionaryRoute: function () {
            translatorDictionaryRoute();
        },
        scanConfigurationRoute: function () {
            scanConfigurationRoute();
        },
        notificationTemplateRoute: function () {
            notificationTemplateRoute();
        },
        organizationManagementRoute: function () {
            organizationManagementRoute();
        },
        manageTemplateRoute: function () {
            manageTemplateRoute();
        },
        manageCategoryRoute: function () {
            manageCategoryRoute();
        },
        categoryReferenceNumberRoute: function () {
            categoryReferenceNumberRoute();
        },
        categoryReferenceCounterRoute: function () {
            categoryReferenceCounterRoute();
        },
        classificationRoute: function () {
            classificationRoute();
        },
        importanceRoute: function () {
            importanceRoute();
        },
        privacyRoute: function () {
            privacyRoute();
        },
        toDoListRoute: function () {
            toDoListRoute();
        },
        assemblyRoute: function () {
            assemblyRoute();
        },
        activityLogRoute: function () {
            activityLogRoute();
        },
        menuRoute: function () {
            menuRoute();
        },
        actionRoute: function () {
            actionRoute();
        },
        tabRoute: function () {
            tabRoute();
        },
        userRoute: function () {
            userRoute();
        },
        roleRoute: function () {
            roleRoute();
        },
        //nodeRoute: function () {
        //    nodeRoute();
        //},
        categoryRoute: function () {
            categoryRoute();
        },
        attachmentfolderRoute: function () {
            attachmentfolderRoute();
        },
        lookupRoute: function () {
            lookupRoute();
        },
        draftRoute: function (nodeId, delegationId) {
            draftListRoute(nodeId, delegationId);
        },
        inboxRoute: function (nodeId, delegationId) {
            inboxListRoute(nodeId, delegationId);
        }
       , customRoute: function (nodeId) {
            customListRoute(nodeId);
        },
        inboxRouteWithTransfer: function (nodeId, transferId) {
            inboxListRoute(nodeId, null, false, transferId);
        },
        StructureInboxRoute: function (nodeId, delegationId) {
            inboxListRoute(nodeId, delegationId, true);
        },
        completedRoute: function (nodeId, delegationId) {
            completedListRoute(nodeId, delegationId);
        },
        myRequestsRoute: function (nodeId) {
            myRequestsListRoute(nodeId);
        },
        basketRoute: function (basketId) {
            basketListRoute(basketId);
        },
        sentRoute: function (nodeId, delegationId) {
            sentListRoute(nodeId, delegationId);
        },
        StructureSentRoute: function (nodeId, delegationId) {
            sentListRoute(nodeId, delegationId, true);
        },
        closedListRoute: function (nodeId) {
            closedListRoute(nodeId);
        },
        delegationRoute: function () {
            delegationRoute();
        },
        documenttypeRoute: function () {
            documenttypeRoute();
        },
        searchRoute: function (term) {
            searchRoute(term);
        },
        advanceSearchRoute: function (delegationId) {
            advanceSearchRoute(delegationId);
        },
        advanceSearchConfigurationRoute: function () {
            advanceSearchConfigurationRoute();
        },
        noteRoute: function () {
            noteRoute();
        },
        barcodeConfigurationRoute: function () {
            barcodeConfigurationRoute();
        },
        createDocumentRoute: function (id) {
            createDocumentRoute(id);
        },
        createDocumentByTemplateRoute: function (id, name) {
            createDocumentByTemplateRoute(id, name);
        },
        createDocumentByFileRoute: function (id, name) {
            createDocumentByFileRoute(id, name);
        },
        documentRoute: function (id, delegationId) {
            documentRoute(id, delegationId);
        },
        openFollowUpRoute: function (id) {
            openFollowUpRoute(id);
        },
        filingPlanRoute: function () {
            filingPlanRoute();
        },
        linkedDocumentDetailsRoute: function (id, delegationId) {
            if (typeof isModal !== "undefined" && isModal !== null && isModal.includes("true")) {
                $(".topnavbar-wrapper").remove();
                $(".aside").remove();
                $(".offsidebar").remove();
                $("section").attr("style", "margin:0;");
            }
            linkedDocumentDetailsRoute(id, delegationId);
        },
        nonArchivedAttachmentsTypesRoute: function () {
            nonArchivedAttachmentsTypesRoute();
        },
        inboxDocumentDetailsRoute: function (id, delegationId) {
            inboxDocumentDetailsRoute(id, delegationId);
        },
        completedDocumentDetailsRoute: function (id, delegationId) {
            completedDocumentDetailsRoute(id, delegationId);
        },
        myRequestsDocumentDetailsRoute: function (id, delegationId) {
            myRequestsDocumentDetailsRoute(id, delegationId);
        },
        nodeListRoute: function () {
            nodeListRoute();
        },
        bookmarksRoute: function () {
            bookmarksRoute();
        },
        systemDashboardRoute: function () {
            systemDashboardRoute();
        },
        userDashboardRoute: function () {
            userDashboardRoute();
        },
        averageDurationForCorrespondenceCompletionRoute: function () {
            averageDurationForCorrespondenceCompletionRoute();
        },
        averageDurationForCorrespondenceDelayRoute: function () {
            averageDurationForCorrespondenceDelayRoute();
        },
        averageDurationForTransferCompletionRoute: function () {
            averageDurationForTransferCompletionRoute();
        },
        averageDurationForTransferDelayRoute: function () {
            averageDurationForTransferDelayRoute();
        },
        reportStatisticalCorrespondencesRoute: function () {
            reportStatisticalCorrespondencesRoute();
        },
        reportCorrespondenceDetailFilterRoute: function () {
            reportCorrespondenceDetailFilterRoute();
        },
        reportInProgressTransfersRoute: function () {
            reportInProgressTransfersRoute();
        },
        reportCompletedTransfersRoute: function () {
            reportCompletedTransfersRoute();
        },
        reportOperationByUserRoute: function () {
            reportOperationByUserRoute();
        },
        reportOperationByCorrespondenceRoute: function () {
            reportOperationByCorrespondenceRoute();
        },
        reportInProgressCorrespondencesRoute: function () {
            reportInProgressCorrespondencesRoute();
        },
        reportCompletedCorrespondencesRoute: function () {
            reportCompletedCorrespondencesRoute();
        },
        entityGroupRoute: function () {
            entityGroupRoute();
        },
        favoriteStructuresRoute: function () {
            favoriteStructuresRoute();
        },
        distributionListRoute: function () {
            distributionListRoute();
        },
        //newAutoForwardRoute: function () {
        //    newAutoForwardRoute();
        //},
        manageCorrespondenceRoute: function () {
            manageCorrespondenceRoute();
        },
        manageStructureUsersCorrespondencesRoute: function () {
            manageStructureUsersCorrespondencesRoute();
        },
        OrganiztionManagementDepartmentUserRoute: function () {
            OrganiztionManagementDepartmentUserRoute();
        },
        taskSearchRoute: function () {
            taskSearchRoute();
        },
        templatesManagementRoute: function () {
            templatesManagementRoute();
        },
        _routeToRegExp: function (route) {
            route = Backbone.Router.prototype._routeToRegExp.call(this, route);
            return new RegExp(route.source, 'i'); // Just add the 'i'
        }
        , autoForwardRoute: function () {
            autoForwardRoute();
        },
        userNodeRoute: function () {
            userNodeRoute();
        },
        userNodeCorrespondanceRoute: function () {
            userNodeCorrespondanceRoute();
        },
        profileRoute: function () {
            profileRoute();
        },
        FollowUpRoute: function (nodeId) {
            followUpRoute(nodeId);
        }
    });
    var app = new appRouter();
    app.on('route', function () {
        Common.removeUnUsedDomElementsOnRoute();
    });
    Backbone.history.start();

    //Common.ajaxGet('/UserParameter/Get', { "Keyword": "InboxMode"},
    //    function (data) {
    //        console.log(data);
    //        if (data != undefined && data != null) {
    //            $("input[type=radio][value=" + data.content + "]").prop('checked', true);
    //            window.InboxMode = data.content;
    //        }

    //    }, null, false);

    $("input[type=radio][value=" + window.InboxMode + "]").prop('checked', true);
    $('#radioInboxDefault').click(function () {
        window.InboxMode = "InboxDefault";
        $("input[type=radio][value=" + window.InboxMode + "]").prop('checked', true);
        $('#rightSide').click();
        var paramData = [];
        var parameter = {};
        parameter.Id = 0;
        parameter.Keyword = "InboxMode";
        parameter.Content = "InboxDefault";
        parameter.Description = "Inbox Default Mode";
        paramData.push(parameter);
       
        Common.ajaxPost('/UserParameter/UpdateCustomParameters', { parameters: paramData }, function (data) {
            if (window.location.href.indexOf("Inbox") > -1 || window.location.href.indexOf("Draft") > -1
                || window.location.href.indexOf("Completed") > -1 || window.location.href.indexOf("Sent") > -1
                || window.location.href.indexOf("MyRequests") > -1 || window.location.href.indexOf("Closed") > -1) {
                window.location.reload();
            }
        }, function () { }, false);

       
    });
    //$('#radioInboxDefaultWithGrouping').click(function () {
    //    window.InboxMode = "InboxDefaultWithGrouping";
    //    $("input[type=radio][value=" + window.InboxMode + "]").prop('checked', true);
    //    $('#rightSide').click();
    //    var paramData = [];
    //    var parameter = {};
    //    parameter.Id = 0;
    //    parameter.Keyword = "InboxMode";
    //    parameter.Content = "InboxDefaultWithGrouping";
    //    parameter.Description = "Inbox Default With Grouping Mode";
    //    paramData.push(parameter);

    //    Common.ajaxPost('/UserParameter/UpdateCustomParameters', { parameters: paramData }, function (data) {
    //        if (window.location.href.indexOf("Inbox") > -1 || window.location.href.indexOf("Draft") > -1
    //            || window.location.href.indexOf("Completed") > -1 || window.location.href.indexOf("Sent") > -1
    //            || window.location.href.indexOf("MyRequests") > -1 || window.location.href.indexOf("Closed") > -1) {
    //            window.location.reload();
    //        }
    //    }, function () { }, false);

       
    //});
    $('#radioInboxVIPView').click(function () {
        window.InboxMode = "InboxVIPView";
        $("input[type=radio][value=" + window.InboxMode + "]").prop('checked', true);
        $('#rightSide').click();
        var paramData = [];
        var parameter = {};
        parameter.Id = 0;
        parameter.Keyword = "InboxMode";
        parameter.Content = "InboxVIPView";
        parameter.Description = "Inbox VIP Mode";
        paramData.push(parameter);

        Common.ajaxPost('/UserParameter/UpdateCustomParameters', { parameters: paramData }, function (data) {
            if (window.location.href.indexOf("Inbox") > -1 || window.location.href.indexOf("Draft") > -1
                || window.location.href.indexOf("Completed") > -1 || window.location.href.indexOf("Sent") > -1
                || window.location.href.indexOf("MyRequests") > -1 || window.location.href.indexOf("Closed") > -1) {
                window.location.reload();
            }
        }, function () { }, false);

       
    });

    $('.sidebarStructure').click(function () {
        
        var ref = this;
        Common.showConfirmMsg(Resources.ConfirmChangeStructure, function () {
            
            $("#" + $('#hdLoggedInStructureId').val()).removeClass('active');
            var oldStructure = $('#hdLoggedInStructureId').val();
            $('#hdLoggedInStructureId').val($(ref).parent()[0].id);
            sessionStorage.setItem($("#hdUserId").val() + "loggedInStructure", $('#hdLoggedInStructureId').val());

            let params = {
                'structureId': $('#hdLoggedInStructureId').val(),
                'oldStrutureId': oldStructure
            };

            Common.ajaxPost('CTS/Structure/UpdateLoggedInStrucure', params, function (data) {
                $('#hdLoggedInRoleId').val(data);
                localStorage.setItem('LoggedInRole', data);

                $("#" + $(ref).parent()[0].id).addClass('active');
                window.location.hash = "";
                window.location.reload();
            }, function () { }, false);
        })
    })

    $('#templatesManagement').click(function () { $('#rightSide').click(); });
    $('#profilePage').click(function () { $('#rightSide').click(); });
    
});

function Session() { }

Session.prototype.init = function () {
    console.log('inside Session.init()');

    //capturing all click, touch and keypress events
    window.addEventListener('touchstart', Timeout, false);
    window.addEventListener('click', Timeout, false);
    window.addEventListener('keypress', Timeout, false);
    window.addEventListener('scroll', Timeout, false);

    function _timeout() {
        return function () {
            window.location.href = $("a[title='Logout']").attr("href");
        }
    }
    function Timeout() {
        if (typeof (timer) !== 'undefined') {
            timer = clearTimeout(timer); //reset as soon as something is clicked
        }

        if (typeof (window.sessionTimeout) !== 'undefined' && window.sessionTimeout > 0) {
            timer = setTimeout(_timeout(), window.sessionTimeout /*test tiemout period in millisec*/);
        }
    }
}

var sessionTimeout = new Session();
var timer;
sessionTimeout.init();

function rootRoute() {
    window.onload = function () {
        let view = new Home.HomeView($(".content-wrapper"));
        view.render();
    };
}
function exceptionLogRoute() {
    Common.setActiveSidebarMenu("liExceptionLog");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.ExceptionLogComponent();
    view.render();
}
function purposeRoute() {
    Common.setActiveSidebarMenu("liPurpose");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new Purpose.Purpose();
    model.showCced = true;
    let view = new Purpose.PurposeView(wrapper, model);
    //let view = new CoreComponents.PurposeComponent(true);//showCced param pass to show cced column default is true
    view.render();
}
function statusRoute() {
    Common.setActiveSidebarMenu("liStatus");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.StatusComponent();
    view.render();
}
function priorityRoute() {
    Common.setActiveSidebarMenu("liPriority");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.PriorityComponent();
    view.render();
}
function applicationServerRoute() {
    Common.setActiveSidebarMenu("liApplicationServer");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.ApplicationServerComponent();
    view.render();
}
function parameterRoute() {
    Common.setActiveSidebarMenu("liParameters");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let documentView = new Parameter.ParameterView(wrapper, null);
    documentView.render();
    let modalWrapper = $(".modal-window");
    modalWrapper.empty();

    let modelIndex = new ParameterIndex.ParameterIndex();
    let parameterIndexView = new ParameterIndex.ParameterIndexView(modalWrapper, modelIndex);
    parameterIndexView.render();

    let modelEventIndex = new EventReceiverIndex.EventReceiverIndex();
    let eventIndexView = new EventReceiverIndex.EventReceiverIndexView(modalWrapper, modelEventIndex);
    eventIndexView.render();

    var exportModel = new Export.Export();
    var viewexport = new Export.ExportView(modalWrapper, exportModel);
    viewexport.render();

    var importModel = new Import.Import();
    var viewimport = new Import.ImportView(modalWrapper, importModel);
    viewimport.render();
}
function translatorDictionaryRoute() {
    Common.setActiveSidebarMenu("liTranslatorDictionary");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.DictionaryComponent();
    view.render();
}
function notificationTemplateRoute() {
    Common.setActiveSidebarMenu("liNotificationTemplate");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.NotificationTemplateComponent();
    view.render();
}
function scanConfigurationRoute() {
    Common.setActiveSidebarMenu("liScanConfiguration");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let documentView = new ScanConfiguration.ScanConfigurationView(wrapper, null);
    documentView.render();
    let modalWrapper = $(".modal-window"); modalWrapper.empty();
    let model = new ScanConfigurationIndex.ScanConfigurationIndex();
    model.categories = new ScanConfigurationIndex.ScanConfigurationIndex().get();
    let scanConfigurationIndexView = new ScanConfigurationIndex.ScanConfigurationIndexView(modalWrapper, model);
    scanConfigurationIndexView.render();
}
function organizationManagementRoute() {
    Common.setActiveSidebarMenu("liOrganizationManagement");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    var isSendingRulesEnabled = window.EnableSendingRules === "True";
    var searchAssignedSecurity = { enabled: true, showStructures: true, showUsers: false, showGroups: false };
    //params: isSendingRulesEnabled default false,showSendingRules default false, searchAssignedSecurity default {enabled:false,showStructures:false,showUsers:false,showGroups:false}
    //let view = new CoreComponents.OrganizationManagementComponent(isSendingRulesEnabled, true, searchAssignedSecurity);
    let view = new OrganizationManagement.OrganizationManagementView(wrapper, isSendingRulesEnabled, true, searchAssignedSecurity);
    view.render();
}
function manageTemplateRoute() {
    Common.setActiveSidebarMenu("liManageTemplate");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let documentView = new Template.TemplateView(wrapper, null);
    documentView.render();
    let modalWrapper = $("#contentDiv");


    let modelIndex = new TemplateIndex.TemplateIndex();
    let categoriesObj = new CategoryModel().get();
    let categoriesByTemplates = [];
    for (var i = 0; i < categoriesObj.length; i++) {
        if (categoriesObj[i].byTemplate) {
            var catName = window.language === "en" ? categoriesObj[i].name : window.language === "ar" ? categoriesObj[i].nameAr : categoriesObj[i].nameFr;
            categoriesByTemplates.push({ id: categoriesObj[i].id, text: catName });
        }
    }
    modelIndex.categories = categoriesByTemplates;
    let templateIndexView = new TemplateIndex.TemplateIndexView(modalWrapper, modelIndex);
    templateIndexView.render();

    let modalTreeWrapper = $(".modal-window"); modalTreeWrapper.empty();
    let treeNodeActionModel = new TreeNodeActions.TreeNodeActionsModel();
    treeNodeActionModel.structureTreeId = 'structureTree';
    treeNodeActionModel.templateTreeId = 'templateTree';

    let treeNodeActionsView = new TreeNodeActions.TreeNodeActions(modalTreeWrapper, treeNodeActionModel);
    treeNodeActionsView.render();
}
function manageCategoryRoute() {
    Common.setActiveSidebarMenu("liManageCategories");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");

    let model = new Category.Category();
    model.categories = new Categories().get(window.language);
    let documentView = new Category.CategoryView(wrapper, model);
    documentView.render();

    let modalWrapper = $(".modal-window");
    modalWrapper.empty();

    let categoryindexIndexView = new CategoryIndex.CategoryIndexView(modalWrapper, null);
    categoryindexIndexView.render();
    let categoryManagement = new CategoryManagementView.CategoryManagementView(modalWrapper, null);
    categoryManagement.render();
    let categoryindexImportView = new CategoryImport.CategoryImportView(modalWrapper, null);
    categoryindexImportView.render();

    //let modelSecurityMatrix = new SecurityMatrix.SecurityMatrix();
    //modelSecurityMatrix.isCategory = true;
    //let modalView = new SecurityMatrix.SecurityMatrixView(modalWrapper, modelSecurityMatrix);
    //modalView.render();
}
function categoryReferenceNumberRoute() {
    Common.setActiveSidebarMenu("liManageReferenceNumber");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let documentView = new CategoryReferenceNumber.CategoryReferenceNumberView(wrapper, null);
    documentView.render();
    let modalWrapper = $(".modal-window"); modalWrapper.empty();
    let model = new CategoryReferenceNumberIndex.CategoryReferenceNumberIndex();
    model.categories = new CategoryReferenceNumberIndex.CategoryReferenceNumberIndex().get();
    let categoryReferenceNumberIndexView = new CategoryReferenceNumberIndex.CategoryReferenceNumberIndexView(modalWrapper, model);
    categoryReferenceNumberIndexView.render();
}
function categoryReferenceCounterRoute() {
    Common.setActiveSidebarMenu("liManageReferenceCounter");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let view = new CategoryReferenceCounter.CategoryReferenceCounterView(wrapper, null);
    view.render();
    let modalWrapper = $(".modal-window"); modalWrapper.empty();
    let referenceCounterView = new CategoryReferenceCounterIndex.CategoryReferenceCounterIndexView(modalWrapper, null);
    referenceCounterView.render();
}
function classificationRoute() {
    Common.setActiveSidebarMenu("liClassification");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.ClassificationComponent();
    view.render();
}
function importanceRoute() {
    Common.setActiveSidebarMenu("liImportance");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.ImportanceComponent();
    view.render();
}

function privacyRoute() {
    Common.setActiveSidebarMenu("liPrivacy");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.PrivacyComponent();
    view.render();
}
function toDoListRoute() {
    Common.setActiveSidebarMenu("liToDoList");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.ToDoListComponent();
    view.render();
}
function assemblyRoute() {
    Common.setActiveSidebarMenu("liAssembly");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.AssemblyComponent();
    view.render();
}
function activityLogRoute() {
    Common.setActiveSidebarMenu("liActivityLog");
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");

    let model = new ActivityLog.ActivityLog();
    model.categories = new Categories().get(window.language);
    model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    if (model.statuses.length > 0) {
        model.statuses = model.statuses.filter(function (obj) {
            return obj.id !== SystemStatus.Draft;
        });
    }
    let documentView = new ActivityLog.ActivityLogView(wrapper, model);
    documentView.render();

    let modalWrapper = $(".modal-window");
    modalWrapper.empty();
    let auditTrailValuesView = new AuditTrailValues.AuditTrailValuesView(modalWrapper, null);
    auditTrailValuesView.render();

}
function menuRoute() {
    Common.setActiveSidebarMenu("liMenu");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.MenuComponent();
    view.render();
}
function actionRoute() {
    Common.setActiveSidebarMenu("liAction");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.ActionComponent();//"DocumentTypes" pass as param to change categories label, default is "Categories"
    view.render();
}
function tabRoute() {
    Common.setActiveSidebarMenu("liTab");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.TabComponent();//"DocumentTypes" pass as param to change categories label, default is "Categories"
    view.render();
}
function userRoute() {
    Common.setActiveSidebarMenu("liUser");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");

    let view = new User.UserView(wrapper);

    //let view = new CoreComponents.UserComponent();
    view.render();
}
function roleRoute() {
    Common.setActiveSidebarMenu("liRole");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");

    let view = new Role.RoleView(wrapper);

    //let view = new CoreComponents.RoleComponent();
    view.render();
}
//function nodeRoute() {
//    Common.setActiveSidebarMenu("liNode");
//    $(".delegation").removeClass("active");

//    let view = new CoreComponents.NodeComponent();
//    view.render();
//}
function categoryRoute() {
    Common.setActiveSidebarMenu("liCategory");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.CategoryComponent();//"DocumentTypes" pass as param to change categories label, default is "Categories"
    view.render();
}
function attachmentfolderRoute() {
    Common.setActiveSidebarMenu("liManageAttachmentFolder");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new AttachmentFolder.AttachmentFolder();
    model.categories = new Categories().get(window.language);
    let documentView = new AttachmentFolder.AttachmentFolderView(wrapper, model);
    documentView.render();
    let modalWrapper = $(".modal-window");
    modalWrapper.empty();
    let modelIndex = new AttachmentFolderIndex.AttachmentFolderIndex();
    let attachmentFolderIndexView = new AttachmentFolderIndex.AttachmentFolderIndexView(modalWrapper, modelIndex);
    attachmentFolderIndexView.render();
}
function lookupRoute() {
    Common.setActiveSidebarMenu("liLookup");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.LookupComponent();
    view.render();
}
function draftListRoute(nodeId, delegationId) {
    let item = "liDraft" + nodeId;
    if (delegationId !== null) {
        item = "draft-" + nodeId;
    }
    Common.setActiveSidebarMenu(item);
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");
    const nodes = new CoreComponents.Lookup.Nodes().get();

    if (window.InboxMode === "InboxVIPView" && (nodeId != window.MyFollowUpNode || nodeId != window.AssignedToMeNode || nodeId != window.AssignedToOthersNode || nodeId != window.DepartmentFollowUpsNode)) {
        let model = new VipDocumentDraft.VipDocumentDraft();
        model.title = nodes.find(e => e.id == nodeId).text ?? "Draft";
        model.nodeId = nodeId;
        model.categories = new Categories().get(window.language);
        model.delegationId = delegationId;

        let documentView = new VipDocumentDraft.VipDocumentDraftView(wrapper, model);
        documentView.render();
    } else {
        let model = new DocumentDraft.DocumentDraft();
        model.title = nodes.find(e => e.id == nodeId).text ?? "Draft";
        model.nodeId = nodeId;
        model.categories = new Categories().get(window.language);
        model.delegationId = delegationId;

        let documentView = new DocumentDraft.DocumentDraftView(wrapper, model);
        documentView.render();
    }

}
function inboxListRoute(nodeId, delegationId, fromStructureInbox = false, transferId) {
    let item = (fromStructureInbox ? "liStructureInbox": "liInbox") + nodeId;
    if (delegationId !== null) {
        item = "index-" + nodeId;
    }
    Common.setActiveSidebarMenu(item);
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");
    const nodes = new CoreComponents.Lookup.Nodes().get();

    if (window.InboxMode === "InboxVIPView" && (nodeId != window.MyFollowUpNode || nodeId != window.AssignedToMeNode || nodeId != window.AssignedToOthersNode || nodeId != window.DepartmentMyFollowNode)) {
        let model = new VipDocumentInbox.VipDocumentInbox();
        model.title = nodes.find(e => e.id == nodeId).text ?? "Inbox";
        model.nodeId = nodeId;
        model.delegationId = delegationId;
        model.categories = new Categories().get(window.language, delegationId);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.fromStructureInbox = fromStructureInbox;
        model.transferId = transferId;
        model.isExported = nodes.find(e => e.id == nodeId).isExported ?? false;
        let documentView = new VipDocumentInbox.VipDocumentInboxView(wrapper, model);
        documentView.render();

    } else {
        let model = new DocumentInbox.DocumentInbox();
        model.title = nodes.find(e => e.id == nodeId).text ?? "Inbox";
        model.nodeId = nodeId;
        model.delegationId = delegationId;
        model.categories = new Categories().get(window.language, delegationId);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.fromStructureInbox = fromStructureInbox;
        model.transferId = transferId;
        model.isExported = nodes.find(e => e.id == nodeId).isExported ?? false;
        
        let documentView = new DocumentInbox.DocumentInboxView(wrapper, model);
        documentView.render();
    }
}
function customListRoute(nodeId) {
    let item = "liCustom" + nodeId;
    Common.setActiveSidebarMenu(item);
    $(".delegation").removeClass("active");
    var customfunctions = $('#' + item).data("customfunctions");
    var customfunction = customfunctions.split(window.Splitter)[0];
    customfunction = customfunction
        .replace("$nodeId", nodeId)
    eval(customfunction);
}
function completedListRoute(nodeId, delegationId) {
    let item = "liCompleted" + nodeId;
    if (delegationId !== null) {
        item = "completed-" + nodeId;
    }
    Common.setActiveSidebarMenu(item);
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");
    const nodes = new CoreComponents.Lookup.Nodes().get();

    if (window.InboxMode === "InboxVIPView" && (nodeId != window.MyFollowUpNode || nodeId != window.AssignedToMeNode || nodeId != window.AssignedToOthersNode || nodeId != window.DepartmentMyFollowNode)) {
        let model = new VipDocumentCompleted.VipDocumentCompleted();
        model.title = nodes.find(e => e.id == nodeId).text ?? "Completed";
        model.nodeId = nodeId;
        model.delegationId = delegationId;
        model.categories = new Categories().get(window.language, delegationId);
        let documentView = new VipDocumentCompleted.VipDocumentCompletedView(wrapper, model);
        documentView.render();

    } else {
        let model = new DocumentCompleted.DocumentCompleted();
        model.title = nodes.find(e => e.id == nodeId).text ?? "Completed";
        model.nodeId = nodeId;
        model.delegationId = delegationId;
        model.categories = new Categories().get(window.language, delegationId);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);

        let documentView = new DocumentCompleted.DocumentCompletedView(wrapper, model);
        documentView.render();
    }

}
function myRequestsListRoute(nodeId) {
    TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);

    Common.setActiveSidebarMenu("liMyRequests" + nodeId);
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");
    const nodes = new CoreComponents.Lookup.Nodes().get();

    if (window.InboxMode === "InboxVIPView" && (nodeId != window.MyFollowUpNode || nodeId != window.AssignedToMeNode || nodeId != window.AssignedToOthersNode || nodeId != window.DepartmentMyFollowNode)) {
        let model = new VipDocumentMyRequests.VipDocumentMyRequests();
        model.title = nodes.find(e => e.id == nodeId).text ?? "MyRequests";
        model.nodeId = nodeId;
        model.categories = new Categories().get(window.language);
        let documentView = new VipDocumentMyRequests.VipDocumentMyRequestsView(wrapper, model);
        documentView.render();

    } else {
        let model = new DocumentMyRequests.DocumentMyRequests();
        model.title = nodes.find(e => e.id == nodeId).text ?? "MyRequests";
        model.nodeId = nodeId;
        model.categories = new Categories().get(window.language);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);

        let documentView = new DocumentMyRequests.DocumentMyRequestsView(wrapper, model);
        documentView.render();
    }

}
function basketListRoute(basketId) {
    Common.ajaxGet('/Basket/GetNameById', { basketId: basketId, language: window.language }, function (data) {
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new DocumentBasket.DocumentBasket();
        model.basketId = basketId;
        model.basketName = data;
        model.categories = new Categories().get(window.language);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);

        let documentView = new DocumentBasket.DocumentBasketView(wrapper, model);
        documentView.render();
    }, function () {
        Common.showScreenErrorMsg();
    });
}
function sentListRoute(nodeId, delegationId, fromStructureSent = false) {
    let item = (fromStructureSent ? "liStructureSent" : "liSent") + nodeId;
    if (delegationId !== null) {
        item = "sent-" + nodeId;
    }
    Common.setActiveSidebarMenu(item);
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");
    const nodes = new CoreComponents.Lookup.Nodes().get();

    if (window.InboxMode === "InboxVIPView" && (nodeId != window.MyFollowUpNode || nodeId != window.AssignedToMeNode || nodeId != window.AssignedToOthersNode || nodeId != window.DepartmentMyFollowNode)) {
        let model = new VipDocumentSent.VipDocumentSent();
        model.title = nodes.find(e => e.id == nodeId).text ?? "Sent";
        model.nodeId = nodeId;
        model.delegationId = delegationId;
        model.categories = new Categories().get(window.language, delegationId);
        model.fromStructureSent = fromStructureSent
        let documentView = new VipDocumentSent.VipDocumentSentView(wrapper, model);
        documentView.render();

    } else {
        let model = new DocumentSent.DocumentSent();
        model.title = nodes.find(e => e.id == nodeId).text ?? "Sent";
        model.nodeId = nodeId;
        model.delegationId = delegationId;
        model.categories = new Categories().get(window.language, delegationId);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.fromStructureSent = fromStructureSent
        let documentView = new DocumentSent.DocumentSentView(wrapper, model);
        documentView.render();
    }

}
function closedListRoute(nodeId) {
    let item = "liClosed" + nodeId;
    Common.setActiveSidebarMenu(item);
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");
    const nodes = new CoreComponents.Lookup.Nodes().get();

    if (window.InboxMode === "InboxVIPView" && (nodeId != window.MyFollowUpNode || nodeId != window.AssignedToMeNode || nodeId != window.AssignedToOthersNode || nodeId != window.DepartmentMyFollowNode)) {
        let model = new VipDocumentClosed.VipDocumentClosed();
        model.title = nodes.find(e => e.id == nodeId).text ?? "Closed";
        model.nodeId = nodeId;
        model.categories = new Categories().get(window.language);

        let documentView = new VipDocumentClosed.VipDocumentClosedView(wrapper, model);
        documentView.render();
    } else {
        let model = new DocumentClosed.DocumentClosed();
        model.title = nodes.find(e => e.id == nodeId).text ?? "Closed";
        model.nodeId = nodeId;
        model.categories = new Categories().get(window.language);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);

        let documentView = new DocumentClosed.DocumentClosedView(wrapper, model);
        documentView.render();
    }
}
function delegationRoute() {
    Common.setActiveSidebarMenu("liDelegation");
    $(".delegation").removeClass("active");

    var userId = $("#hdUserId").val();
    var structureIds = $("#hdStructureIds").val().split(window.Seperator);
    var hdHasManager = $("#hdHasManager").val();
    let wrapper = $(".content-wrapper");
    let view = new CTSDelegation.DelegationView(wrapper, userId, structureIds, hdHasManager);//params userId,structureIds,hasManager,"DocumentTypes" pass as param to change categories label, default is "Categories"
    view.render();

}
function documenttypeRoute() {
    Common.setActiveSidebarMenu("liDocumentType");
    $(".delegation").removeClass("active");

    let view = new CoreComponents.DocumentTypeComponent();
    view.render();
}
function searchRoute(term) {
    Common.setActiveSidebarMenu("liSearch");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new DocumentSearch.DocumentSearch();
    model.categories = new Categories().get(window.language).filter(item => item.id != window.FollowUpCategory);
    var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    if (statuses.length > 0) {
        statuses = statuses.filter(function (obj) {
            return obj.id !== SystemStatus.Draft;
        });
    }
    model.statuses = statuses;
    model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
    model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
    model.importances = new CoreComponents.Lookup.Importances().get(window.language);
    model.delegationUsers = new DelegationUsers().get(window.language);
    let view = new DocumentSearch.DocumentSearchView(wrapper, model, term != null ? term : '');
    view.render();
}
function documentRoute(id, delegationId) {
    Common.ajaxGet('/Document/Get', { id: id, delegationId: delegationId }, function (data) {
        if (!data.id) {
            return;
        }
        Common.setActiveSidebarMenu("liDraft");
        $(".delegation").removeClass("active");

        var wrapper = $(".modal-documents");
        var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
        linkedCorrespondenceModel.reference = data.referenceNumber;
        linkedCorrespondenceModel.subject = data.subject;
        linkedCorrespondenceModel.documentId = id;
        //linkedCorrespondenceModel.subject = $("#" + model.ComponentId + "_subjectTaskPanel").html();
        //linkedCorrespondenceModel.from = $("#" + model.ComponentId + "_fromTaskPanel").html();
        //linkedCorrespondenceModel.to = $("#" + model.ComponentId + "_toTaskPanel").html();
        //linkedCorrespondenceModel.transferDate = $("#" + model.ComponentId + "_transferDateTaskPanel").html();
        //linkedCorrespondenceModel.registerDate = $("#" + model.ComponentId + "_registerDateTaskPanel").html();
        //linkedCorrespondenceModel.registeredBy = $("#" + model.ComponentId + "_registerByTaskPanel").html();
        var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
        linkedCorrespondenceDocument.render();
        var model = new DocumentDetails.DocumentDetails();
        model.documentId = id;
        model.statusId = data.status;
        model.categoryId = data.categoryId;
        model.categoryName = data.categoryName;
        model.referenceNumber = data.referenceNumber;
        model.documentModel = data;
        model.attachmentVersion = data.attachmentVersion;
        model.showMyTransfer = false;
        model.readonly = false;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language, delegationId));
        var tabs = [];
        var nodeId = data.categoryId == window.FollowUpCategory ? window.MyFollowUpNode : $('[data-inherit="' + TreeNode.Draft + '"]').first().data("id");
        if (nodeId !== undefined && $.isNumeric(nodeId)) {
            tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[data.categoryId].Tabs;
            model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[data.categoryId].SecurityTabs;
        }
        model.tabs = $.grep(tabs, function (element, index) {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("notes") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory");
        });
        model.tabsWithStatic = tabs;
        model.showBackButton = false;
        model.fromDraft = true;
        model.attachmentId = data.attachmentId;
        model.attachmentVersion = data.attachmentVersion;
        model.keyword = data.keyword;
        model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
        model.isModal = true;
        model.showBackButton = false;
        model.delegationId = delegationId;

        wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

        var documentView = new DocumentDetails.DocumentDetailsView(wrapper, model);
        documentView.render();

        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
            $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');

        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
            if ($(this).data("remove") != true)
                return;
            $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
            swal.close();
            //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
            //    $('body').addClass('modal-open');
            //}
        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");


        if (!id) {
            TreeNode.addToNodeCount(TreeNode.Draft, 1, 1);
        }
    }, function () { Common.showScreenErrorMsg(); }, true);
}
function openFollowUpRoute(id) {

    Common.ajaxGet('/FollowUp/Get', { id: id }, function (result) {
        if (result.status) {
            if (result.model) {
                var data = result.model;
                Common.setActiveSidebarMenu("liFollowUp");
                $(".delegation").removeClass("active");

                var wrapper = $(".modal-documents");
                var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
                linkedCorrespondenceModel.reference = data.originalDocument.referenceNumber;
                linkedCorrespondenceModel.subject = data.originalDocument.subject;
                var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
                linkedCorrespondenceDocument.render();

                var model = new DocumentDetails.DocumentDetails();
                model.followupId = id;
                model.originalDocumentId = data.originalDocument.id;
                model.documentId = data.followUpDocument.id;
                model.categoryId = window.FollowUpCategory;
                model.showMyTransfer = false;
                model.readonly = null;
                var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));
                var tabs = [];
                var nodeId = $('[data-inherit="' + TreeNode.FollowUp + '"]').first().data("id");
                if (nodeId !== undefined && $.isNumeric(nodeId)) {
                    tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[window.FollowUpCategory].Tabs;
                    model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[window.FollowUpCategory].SecurityTabs;
                }
                model.tabs = $.grep(tabs, function (element, index) {
                    return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                        !element.Name.includes("notes") && !element.Name.includes("visualTracking") &&
                        !element.Name.includes("activityLog") &&
                        !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory");
                });
                //model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[window.FollowUpCategory].SecurityTabs;
                model.tabsWithStatic = tabs;
                model.showBackButton = false;
                model.fromDraft = false;
                model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
                model.isModal = true;
                model.teamId = data.teamId
                model.isPrivateFollowUp = data.isPrivate
                wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

                var documentView = new DocumentDetails.DocumentDetailsView(wrapper, model);
                documentView.render();

                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                    $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
                });
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                    if ($(this).data("remove") != true)
                        return;
                    $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                    swal.close();
                    //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
                    //    $('body').addClass('modal-open');
                    //}
                });
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");
            }
        } else {
            if (result.message) {
                Common.showScreenErrorMsg(result.message);
            }
        }
    }, function () { Common.showScreenErrorMsg(); }, true);
}
function createDocumentRoute(id) {
    //if ($('[data-inherit="' + TreeNode.Draft + '"]').length > 0) {
    //    Common.setActiveSidebarMenu($('[data-inherit="' + TreeNode.Draft + '"]')[0].id);
    //}
    $(".delegation").removeClass("active");
    if (window.EnablePerStructure) {
        var createdByStructureId = $("#hdLoggedInStructureId").val();

    } else {
        var createdByStructureId = $("#hdStructureId").val();
        if (sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture") !== null) {
            createdByStructureId = sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture");
        }

    }
    var saveParams = {
        'CategoryId': id,
        'CreatedByStructureId': createdByStructureId,
        '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
    };
    Common.ajaxPost('/Document/Save', saveParams, function (data) {
        TreeNode.addToNodeCount(TreeNode.Draft, 1, 1);
        window.location.href = "/#document/" + data;
        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
        if (id == window.FollowUpCategory) {
            TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
        }
    }, function () {
        Common.showScreenErrorMsg();
    });
}
function createDocumentByTemplateRoute(id, name) {
    Common.setActiveSidebarMenu("liDraft");
    $(".delegation").removeClass("active");
    $(".modal-window").empty();
    var wrapper = $(".content-wrapper");
    var model = new CreateByTemplate.CreateByTemplate();
    model.categoryId = id;
    model.categoryName = name;
    model.isModal = false;
    var view = new CreateByTemplate.CreateByTemplateView(wrapper, model);
    view.render();
}
function createDocumentByFileRoute(id, name) {
    Common.setActiveSidebarMenu("liDraft");
    $(".delegation").removeClass("active");

    var wrapper = $(".content-wrapper");
    var model = new CreateByFile.CreateByFile();
    model.categoryId = id;
    model.categoryName = name;
    model.byFileMode = window.ByFileMode;
    var view = new CreateByFile.CreateByFileView(wrapper, model);
    view.render();
}
function advanceSearchRoute(delegationId) {
    if ($("#liAdvanceSearch").length == 0) {
        window.location.href = "/";
    } else {
        Common.ajaxGet('/AdvanceSearchConfiguration/GetConfiguration', null, function (response) {
            Common.setActiveSidebarMenu("liAdvanceSearch");
            $(".delegation").removeClass("active");
            let wrapper = $(".content-wrapper");
            let model = new DocumentAdvanceSearch.DocumentAdvanceSearchModel();
            model.delegationId = delegationId;
            model.configuration = response;
            model.delegationUsers = new DelegationUsers().get(window.language);
            if (response && response.content != null && response.content != "") {
                if (JSON.parse(response.content).components.length > 0) {
                    model.drawContentBuilder = true;
                }
            }
            let view = new DocumentAdvanceSearch.DocumentAdvanceSearchView(wrapper, model);
            view.render();
        });
    }
}
function advanceSearchConfigurationRoute() {
    if ($("#liAdvanceSearchConfiguration").length == 0) {
        window.location.href = "/";
    } else {
        Common.ajaxGet('/AdvanceSearchConfiguration/GetConfiguration', null, function (response) {
            Common.setActiveSidebarMenu("liAdvanceSearchConfiguration");
            $(".delegation").removeClass("active");

            let wrapper = $(".content-wrapper");

            let model = new DocumentAdvanceSearchConfiguration.DocumentAdvanceSearchConfiguration();
            model.configuration = response;
            let view = new DocumentAdvanceSearchConfiguration.DocumentAdvanceSearchConfigurationView(wrapper, model);
            view.render();

            let modalWrapper = $(".modal-window");
            modalWrapper.empty();
            let documentAdvanceSearchConfigurationColumnsView = new DocumentAdvanceSearchConfigurationColumns.AdvanceSearchConfigurationColumnsView(modalWrapper, null);
            documentAdvanceSearchConfigurationColumnsView.render();
        });
    }
}
function nonArchivedAttachmentsTypesRoute() {
    Common.setActiveSidebarMenu("liNonArchivedAttachmentsTypes");
    let wrapper = $(".content-wrapper");
    let model = new NonArchivedAttachmentsTypes.NonArchivedAttachmentsTypes();
    model.types = new Types().get(window.language);
    let documentView = new NonArchivedAttachmentsTypes.NonArchivedAttachmentsTypesView(wrapper, model);
    documentView.render();
    let modalWrapper = $(".modal-window");
    modalWrapper.empty();
    let documenttypeIndexView = new NonArchivedAttachmentsTypesIndex.NonArchivedAttachmentsTypesIndexView(modalWrapper, null);
    documenttypeIndexView.render();
}
function inboxDocumentDetailsRoute(id, delegationId) {
    var params = { id: id };
    if (delegationId !== null) {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetDocumentBasicInfoByTransferId', params, function (response) {
        if (!response.id) {
            return;
        }
        let item = "liInbox";
        if (delegationId !== null) {
            item = "index-" + delegationId;
        }
        Common.setActiveSidebarMenu(item);
        $(".delegation").removeClass("active");
        var model = new DocumentDetails.DocumentDetails();
        model.readonly = false;
        model.delegationId = delegationId;
        model.id = id;
        model.documentId = response.id;
        model.referenceNumber = response.referenceNumber;
        model.categoryName = response.categoryName;
        model.categoryId = response.categoryId;
        model.statusId = response.status;
        model.createdByUser = response.createdByUser;
        model.attachmentId = response.attachmentId;
        model.fromInbox = true;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.showMyTransfer = true;
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));

        var tabs = [];
        var nodeId = $('[data-inherit="' + TreeNode.Inbox + '"]').first().data("id");
        if (nodeId !== undefined && $.isNumeric(nodeId)) {
            tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
            model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
        }
        model.tabs = $.grep(tabs, function (element, index) {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("notes") &&
                !element.Name.includes("visualTracking") && !element.Name.includes("activityLog") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory");
        });
        model.tabsWithStatic = tabs;
        model.showBackButton = false;

        var wrapper = $(".content-wrapper");
        wrapper.empty();
        var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
        view.render();

    }, function () { Common.showScreenErrorMsg(); }, true);
}
function completedDocumentDetailsRoute(id, delegationId) {
    var params = { id: id };
    if (delegationId !== null) {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetDocumentBasicInfoByTransferId', params, function (response) {
        if (!response.id) {
            return;
        }
        let item = "liCompleted";
        if (delegationId !== null) {
            item = "completed-" + delegationId;
        }
        Common.setActiveSidebarMenu(item);
        $(".delegation").removeClass("active");

        var model = new DocumentDetails.DocumentDetails();
        model.readonly = true;
        model.delegationId = delegationId;
        model.id = id;
        model.documentId = response.id;
        model.referenceNumber = response.referenceNumber;
        model.categoryName = response.categoryName;
        model.categoryId = response.categoryId;
        model.statusId = response.status;
        model.createdByUser = response.createdByUser;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.showMyTransfer = true;
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var tabs = [];
        var nodeId = $('[data-inherit="' + TreeNode.Completed + '"]').first().data("id");
        if (nodeId !== undefined && $.isNumeric(nodeId)) {
            tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
            model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
        }
        model.tabs = $.grep(tabs, function (element, index) {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("notes") &&
                !element.Name.includes("visualTracking") && !element.Name.includes("activityLog") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory");
        });
        model.tabsWithStatic = tabs;
        model.showBackButton = false;
        model.attachmentId = response.attachmentId;

        var wrapper = $(".content-wrapper");
        wrapper.empty();
        var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
        view.render();

    }, function () { Common.showScreenErrorMsg(); }, true);
}
function myRequestsDocumentDetailsRoute(id) {
    var params = { id: id };
    Common.ajaxGet('/Document/GetDocument', params, function (response) {
        if (!response.id) {
            return;
        }
        Common.setActiveSidebarMenu("liMyRequests");
        $(".delegation").removeClass("active");

        var model = new DocumentDetails.DocumentDetails();
        model.documentId = response.id;
        model.referenceNumber = response.referenceNumber;
        model.categoryName = response.categoryName;
        model.categoryId = response.categoryId;
        model.documentModel = response;
        model.statusId = response.status;
        model.readonly = true;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.showMyTransfer = false;
        model.formData = (response.formData || "") !== "" ? eval("(" + response.formData + ")") : null;
        model.formDesigner = (response.formDesigner || "") !== "" ? JSON.parse(response.formDesigner) : null;
        model.formDesignerTranslation = (response.formDesignerTranslation || "") !== "" ? JSON.parse(response.formDesignerTranslation) : null;
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var nodeId = $('[data-inherit="' + TreeNode.MyRequests + '"]').first().data("id");
        if (nodeId !== undefined && $.isNumeric(nodeId)) {
            tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
            model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
        }
        model.tabs = $.grep(tabs, function (element, index) {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("notes") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") && !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory");
        });
        model.tabsWithStatic = tabs;
        model.showBackButton = false;
        model.attachmentId = response.attachmentId;
        var wrapper = $(".content-wrapper");
        wrapper.empty();

        var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
        view.render();
    }, function () { Common.showScreenErrorMsg(); }, true);
}
function barcodeConfigurationRoute() {
    Common.setActiveSidebarMenu("liBarcodeConfiguration");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let modelIndex = new BarcodeConfigurationIndex.BarcodeConfigurationIndex();
    modelIndex.categories = new Categories().get(window.language);
    let documentView = new BarcodeConfiguration.BarcodeConfigurationView(wrapper, modelIndex);
    documentView.render();
    let modalWrapper = $(".modal-window");
    modalWrapper.empty();
    let model = new BarcodeConfigurationIndex.BarcodeConfigurationIndex();
    model.categories = new BarcodeConfigurationIndex.BarcodeConfigurationIndex().get();
    let barcodeConfigurationIndexView = new BarcodeConfigurationIndex.BarcodeConfigurationIndexView(modalWrapper, model);
    barcodeConfigurationIndexView.render();

    let modelBarcodeIndex = new BarcodeIndex.BarcodeIndex();
    let barcodeIndexView = new BarcodeIndex.BarcodeIndexView(modalWrapper, modelBarcodeIndex);
    barcodeIndexView.render();
}
function filingPlanRoute() {
    Common.setActiveSidebarMenu("liFilingPlan");
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");

    let model = new FilingPlan.FilingPlan();

    let view = new FilingPlan.FilingPlanView(wrapper, model);
    view.render();

    let modalWrapper = $("#addEditPanel");

    let modelIndex = new FilingPlanIndex.FilingPlanIndex();
    let menuIndexView = new FilingPlanIndex.FilingPlanIndexView(modalWrapper, modelIndex);
    menuIndexView.render();
}
function linkedDocumentDetailsRoute(id, delegationId) {
    var params = { id: id };
    if (delegationId !== null) {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetSearchDocument', params, function (response) {
        if (response && response === "NoAccess") {
            Common.alertMsg(Resources.NoPermission);
        } else {
            if (!response.id) {
                return;
            }
            var model = new DocumentDetails.DocumentDetails();
            model.documentModel = response;
            model.readonly = true;
            model.delegationId = delegationId;
            model.documentId = response.id;
            model.referenceNumber = response.referenceNumber;
            model.categoryName = response.categoryName;
            model.statusId = response.status;
            model.createdByUser = response.createdByUser;
            model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            model.showMyTransfer = false;
            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
            var tabs = [];
            var nodeId = /*$('[data-inherit="' + TreeNode.Inbox + '"]').first().data("id")*/ TreeNodes.Search;
            if (nodeId !== undefined && $.isNumeric(nodeId)) {
                tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
                model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
            }
            model.tabs = $.grep(tabs, function (element, index) {
                return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                    !element.Name.includes("notes") &&
                    !element.Name.includes("visualTracking") && !element.Name.includes("activityLog") &&
                    !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory");
            });
            model.tabsWithStatic = tabs;
            model.showBackButton = false;
            model.isModal = true;
            model.attachmentId = response.attachmentId;
            var wrapper = $(".content-wrapper");
            wrapper.empty();
            var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
            view.render();
        }
    }, function () { Common.showScreenErrorMsg(); }, true);
}
function nodeListRoute() {
    Common.setActiveSidebarMenu("liNodeList");
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");

    let model = new NodeList.NodeList();

    let view = new NodeList.NodeListView(wrapper, model);
    view.render();

    let modalWrapper = $("#addEditPanel");
    modalWrapper.empty();

    let modelIndex = new NodeIndex.NodeIndex();
    modelIndex.roles = new IdentityService().getRoles();
    let menuIndexView = new NodeIndex.NodeIndexView(modalWrapper, modelIndex);
    menuIndexView.render();
}
function bookmarksRoute() {
    Common.setActiveSidebarMenu("liBookmarks");
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");

    let model = new Bookmark.Bookmark();
    model.categories = new Categories().get(window.language);
    let bookmarkView = new Bookmark.BookmarkView(wrapper, model);
    bookmarkView.render();
}
function systemDashboardRoute() {
    Common.setActiveSidebarMenu("liSystemDashboard");
    $(".delegation").removeClass("active");

    $(".modal-window").empty();

    let wrapper = $(".content-wrapper");
    let model = new SystemDashboard.SystemDashboard();
    model.purposes = new CoreComponents.Lookup.Purposes().get(window.language);
    model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    model.categories = new Categories().get(window.language);

    let view = new SystemDashboard.SystemDashboardView(wrapper, model);
    view.render();
}
function userDashboardRoute() {
    Common.setActiveSidebarMenu("liUserDashboard");
    $(".delegation").removeClass("active");

    $(".modal-window").empty();

    let wrapper = $(".content-wrapper");
    let view = new UserDashboard.UserDashboardView(wrapper);
    view.render();
}
function averageDurationForCorrespondenceCompletionRoute() {
    Common.setActiveSidebarMenu("liAverageDurationForCorrespondenceCompletion");
    $(".delegation").removeClass("active");

    $(".modal-window").empty();

    let wrapper = $(".content-wrapper");

    let view = new KPI.KPIView(wrapper, "AverageDurationForCorrespondenceCompletion", true);
    view.render();
}
function averageDurationForCorrespondenceDelayRoute() {
    Common.setActiveSidebarMenu("liAverageDurationForCorrespondenceDelay");
    $(".delegation").removeClass("active");

    $(".modal-window").empty();

    let wrapper = $(".content-wrapper");

    let view = new KPI.KPIView(wrapper, "AverageDurationForCorrespondenceDelay", true);
    view.render();
}
function averageDurationForTransferCompletionRoute() {
    Common.setActiveSidebarMenu("liAverageDurationForTransferCompletion");
    $(".delegation").removeClass("active");

    $(".modal-window").empty();

    let wrapper = $(".content-wrapper");

    let view = new KPI.KPIView(wrapper, "AverageDurationForTransferCompletion");
    view.render();
}
function averageDurationForTransferDelayRoute() {
    Common.setActiveSidebarMenu("liAverageDurationForTransferDelay");
    $(".delegation").removeClass("active");

    $(".modal-window").empty();

    let wrapper = $(".content-wrapper");

    let view = new KPI.KPIView(wrapper, "AverageDurationForTransferDelay");
    view.render();
}

function reportInProgressTransfersRoute() {
    Common.setActiveSidebarMenu("liReportInProgressTransfers");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let documentView = new ReportInProgressTransfers.ReportInProgressTransfersView(wrapper, null);
    documentView.render();
}
function reportCompletedTransfersRoute() {
    Common.setActiveSidebarMenu("liReportCompletedTransfers");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let documentView = new ReportCompletedTransfers.ReportCompletedTransfersView(wrapper, null);
    documentView.render();
}
function reportOperationByUserRoute() {
    Common.setActiveSidebarMenu("liReportOperationByUser");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new ReportOperationByUser.ReportOperationByUser();
    model.categories = new Categories().get(window.language);
    let documentView = new ReportOperationByUser.ReportOperationByUserView(wrapper, model);
    documentView.render();
}
function reportOperationByCorrespondenceRoute() {
    Common.setActiveSidebarMenu("liReportOperationByCorrespondence");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new ReportOperationByCorrespondence.ReportOperationByCorrespondence();
    let documentView = new ReportOperationByCorrespondence.ReportOperationByCorrespondenceView(wrapper, null);
    documentView.render();
}
function reportStatisticalCorrespondencesRoute() {
    Common.setActiveSidebarMenu("liReportStatisticalCorrespondences");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new ReportStatisticalCorrespondences.ReportStatisticalCorrespondences();
    model.categories = new Categories().get(window.language);
    let documentView = new ReportStatisticalCorrespondences.ReportStatisticalCorrespondencesView(wrapper, model);
    documentView.render();
}
function reportCorrespondenceDetailFilterRoute() {
    Common.setActiveSidebarMenu("liReportCorrespondenceDetailFilter");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new ReportCorrespondenceDetailFilter.ReportCorrespondenceDetailFilter();
    let documentView = new ReportCorrespondenceDetailFilter.ReportCorrespondenceDetailFilterView(wrapper, model);
    documentView.render();
}
function reportInProgressCorrespondencesRoute() {
    Common.setActiveSidebarMenu("liReportInProgressCorrespondences");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let documentView = new ReportInProgressCorrespondences.ReportInProgressCorrespondencesView(wrapper, null);
    documentView.render();
}
function reportCompletedCorrespondencesRoute() {
    Common.setActiveSidebarMenu("liReportCompletedCorrespondences");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let documentView = new ReportCompletedCorrespondences.ReportCompletedCorrespondencesView(wrapper, null);
    documentView.render();
}
function entityGroupRoute() {
    Common.setActiveSidebarMenu("liEntityGroup");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let documentView = new EntityGroup.EntityGroupView(wrapper, null);
    documentView.render();
    let modalWrapper = $(".modal-window");
    modalWrapper.empty();

    //let modelIndex = new EntityGroupIndex.EntityGroupIndex();
    //let entityGroupIndexView = new EntityGroupIndex.EntityGroupIndexView(modalWrapper, modelIndex);
    //entityGroupIndexView.render();
}


function autoForwardRoute() {
    Common.setActiveSidebarMenu("liAutoForward");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new AutoForward.AutoForward();
    model.enableTransferToUsers = window.EnableTransferToUsers;
    model.itemsNames = [];
    let documentView = new AutoForward.AutoForwardView(wrapper, model);
    documentView.render();
    let modalWrapper = $(".modal-window");
    modalWrapper.empty();

    //let modelIndex = new EntityGroupIndex.EntityGroupIndex();
    //let entityGroupIndexView = new EntityGroupIndex.EntityGroupIndexView(modalWrapper, modelIndex);
    //entityGroupIndexView.render();
}

function favoriteStructuresRoute() {
    Common.setActiveSidebarMenu("liFavoriteStructures");
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");


    let favoriteStructuresView = new FavoriteStructures.FavoriteStructuresView(wrapper, null);
    favoriteStructuresView.render();
}

function distributionListRoute() {
    Common.setActiveSidebarMenu("liDistributionList");
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");

    let model = new DistributionList.DistributionList();

    let distributionListView = new DistributionList.DistributionListView(wrapper, model);
    distributionListView.render();

    let modalWrapper = $(".modal-window");
    modalWrapper.empty();

    let distributionIndexIndexView = new DistributionIndex.DistributionIndexView(modalWrapper, null);
    distributionIndexIndexView.render();

}


function AutoForwardRoute() {
    Common.setActiveSidebarMenu("liAutoForward");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new AutoForward.AutoForward();
    model.enableTransferToUsers = window.EnableTransferToUsers;
    let view = new AutoForward.AutoForwardView(wrapper, model);
    view.render();
    let modalWrapper = $(".modal-window");
    modalWrapper.empty();

}

function manageCorrespondenceRoute() {

    Common.setActiveSidebarMenu("liManageCorrespondence");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new DocumentManageCorrespondence.DocumentManageCorrespondence();

    model.categories = new Categories().get(window.language);
    model.delegationUsers = new DelegationUsers().get(window.language);
    model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);

    model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
    model.importances = new CoreComponents.Lookup.Importances().get(window.language);
    var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    if (statuses.length > 0) {
        statuses = statuses.filter(function (obj) {
            return obj.id !== SystemStatus.Draft;
        });
    }
    model.statuses = statuses;
    let view = new DocumentManageCorrespondence.DocumentManageCorrespondenceView(wrapper, model);
    view.render();
}

function manageStructureUsersCorrespondencesRoute() {

    Common.setActiveSidebarMenu("liManageStructureUsersCorrespondences");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new DocumentManageStructureUsersCorrespondences.DocumentManageStructureUsersCorrespondences();
    let view = new DocumentManageStructureUsersCorrespondences.DocumentManageStructureUsersCorrespondencesView(wrapper, model);
    view.render();
}

function OrganiztionManagementDepartmentUserRoute() {
    Common.setActiveSidebarMenu("liOrganizationManagement");
    $(".delegation").removeClass("active");
    let wrapper = $('[ref="contentDiv"]');
    let view = new OrganiztionManagementDepartmentUser.OrganiztionManagementDepartmentUserView(wrapper, null);
    view.render();
}

function taskSearchRoute() {
    Common.setActiveSidebarMenu("liTaskSearch");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new TaskSearch.TaskSearch();
    model.categories = new Categories().get(window.language);

    var statuses = new FollowUpStatuses().get(window.language);
    //if (statuses.length > 0) {
    //    statuses = statuses.filter(function (obj) {
    //        return obj.id !== FollowUpStatuses.;
    //    });
    //}
    model.statuses = statuses;
    model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
    model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
    model.importances = new CoreComponents.Lookup.Importances().get(window.language);
    model.delegationUsers = new DelegationUsers().get(window.language);
    let view = new TaskSearch.TaskSearchView(wrapper, model);
    view.render();
}
function templatesManagementRoute() {
    Common.setActiveSidebarMenu("liTemplatesManagement");
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    let model = new TemplatesManagement.TemplatesManagement();
    model.categories = new Categories().get(window.language);
    model.EnablePersonalTemplates =  window.EnablePersonalTemplates=="True"?true:false;
    model.EnableStructureTemplates = window.EnableStructureTemplates == "True" ? true : false;
    let view = new TemplatesManagement.TemplatesManagementView(wrapper, model);
    view.render();
}

function userNodeRoute() {


    let wrapper = $(".content-wrapper");

    let model = new UserNodeList.UserNodeList();

    let view = new UserNodeList.UserNodeListView(wrapper, model);
    view.render();

    let modalWrapper = $("#addEditPanel");
    modalWrapper.empty();

    let modelIndex = new UserNodeIndex.UserNodeIndex();

    let menuIndexView = new UserNodeIndex.UserNodeIndexView(modalWrapper, modelIndex);
    menuIndexView.render();
};
function userNodeCorrespondanceRoute(nodeId, delegationId) {
    nodeId = window.location.href.slice(window.location.href.indexOf("?") + 1).split("/")[4];
    let item = "liUserNode" + nodeId;
    if (delegationId !== null) {
        item = "index-" + nodeId;
    }
    if (delegationId == undefined)
        delegationId = null;
    Common.setActiveSidebarMenu(item);
    $(".delegation").removeClass("active");

    let wrapper = $(".content-wrapper");

    //if (window.InboxMode === "InboxVIPView" && (nodeId != window.MyFollowUpNode || nodeId != window.AssignedToMeNode || nodeId != window.AssignedToOthersNode || nodeId != window.DepartmentMyFollowNode)) {
    //    let model = new VipUserNodeCorrespondance.VipUserNodeListCorrespondance();
    //    model.nodeId = nodeId;
    //    model.delegationId = delegationId;
    //    model.categories = new Categories().get(window.language);
    //    model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
    //    let documentView = new VipUserNodeCorrespondance.VipUserNodeListCorrespondanceView(wrapper, model);
    //    documentView.render();

    //} else {
    let model = new UserNodeCorrespondance.UserNodeDocument();
    model.nodeId = nodeId;
    model.delegationId = delegationId;
    model.categories = /*new Categories().get(window.language, delegationId);*/ new Categories().get(window.language);
    model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
    model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
    let documentView = new UserNodeCorrespondance.UserNodeDocumentView(wrapper, model);
    documentView.render();
    //  }
}

function openUserNodeRoute() {
    Common.ajaxGet('/Node/ListTreeUserNode', null, function (data) {

        for (var i = 0; i < data.length; i++) {
            openNodeNode(data[i])

        }
    }, function () { Common.showScreenErrorMsg(); }, false);
}


function profileRoute() {
    let wrapper = $(".content-wrapper");

    let model = new Profile.Profile();
    model.userId = $("#hdUserId").val();
    let view = new Profile.ProfileView(wrapper, model);
    view.render();
}

function followUpRoute(nodeId) {
    let item = "liFollowUp" + nodeId;
    Common.setActiveSidebarMenu(item);
    $(".delegation").removeClass("active");
    let wrapper = $(".content-wrapper");
    const nodes = new CoreComponents.Lookup.Nodes().get();

    let model = new FollowUpList.FollowUpListModel();
    model.title = nodes.find(e => e.id == nodeId).text ?? "FollowUp";
    model.nodeId = nodeId;
    model.categories = new Categories().get(window.language);
    
    model.followUpStatuses = new FollowUpStatusModel().get(window.language);

    let View = new FollowUpList.FollowUpListView(wrapper, model);
    View.render();

}


export {
    Widget, Home, MyTransfer, DocumentMetadata, DocumentAttachment, DocumentNote, DocumentLinkCorrespondence, DocumentNonArchivedAttachment, VisualTracking,
    TransferHistory, ActivityLogs, Parameter, ParameterIndex, ScanConfiguration, ScanConfigurationIndex, Template, TemplateIndex, TreeNodeActions, Category, CategoryIndex,
    CategoryReferenceNumber, CategoryReferenceNumberIndex, CategoryReferenceCounter, CategoryReferenceCounterIndex, EventReceiverIndex, ActivityLog, AttachmentFolder,
    AttachmentFolderIndex, DocumentDraft, DocumentInbox, DocumentCompleted, DocumentMyRequests, CategoryManagementView, DocumentSearch, DocumentAdvanceSearch,
    DocumentAdvanceSearchConfiguration, DocumentAdvanceSearchConfigurationColumns, CreateByTemplate, DocumentBasket, CreateByFile, NonArchivedAttachmentsTypes, NonArchivedAttachmentsTypesIndex,
    DocumentDetails, BarcodeConfiguration, BarcodeConfigurationIndex, FilingPlan, FilingPlanIndex, BarcodeIndex, DocumentSent, CategoryImport, AuditTrailValues, NodeList, NodeIndex, Bookmark,
    DocumentClosed, Export, Import, SystemDashboard, UserDashboard, KPI, ReportInProgressTransfers, ReportCompletedTransfers, ReportOperationByUser, ReportOperationByCorrespondence,
    ReportStatisticalCorrespondences, ReportCorrespondenceDetailFilter, ReportInProgressCorrespondences, ReportCompletedCorrespondences, VipDocumentInbox, VipDocumentCompleted,
    VipDocumentSent, VipDocumentMyRequests, VipDocumentClosed, VipDocumentDraft, EntityGroup, DocumentManageCorrespondence, CTSDelegation, CTSSystemDelegation
    , Purpose, AutoForward, ActivityLogTimeline, CategoryModel, IdentityService, DelegationUsers, Categories, SendTransferModal, Transfer, Types, FavoriteStructures, DistributionList,
    OrganizationManagement, movetransfers, Role, User, AgendaTopicsList, AgendaCorrespondenceDocumentView, AgendaCorrespondenceDocument, Committee, MeetingAgendaIndex,
    searchLinkedDocumnet, OrganiztionManagementDepartmentUser, Assignee, AssigneeIndex, ManageFollowUp, TaskSearch, UserNodeIndex, UserNodeList, UserNodeCorrespondance, VipUserNodeCorrespondance, DocumentManageStructureUsersCorrespondences,
    FollowUpList, followUpIndex
};

