﻿import Intalio from './common.js'
import { RequestStatuses, Categories, IdentityService, CategoryModel, DelegationUsers, Helper } from './lookup.js'
import CustomizeNodeColomns from '../components/customizeNodeColomns.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import DocumentDetails from './documentDetails.js'
import SearchLinkedDocumnet from './searchLinkedDocumnet.js'
import Document from './document.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import ExportOptions from '../components/exportOptions.js'


class RejectedDocuments extends Intalio.Model
{
    constructor()
    {
        super();
        this.title = null;
        this.nodeId = null;
        this.delegationId = null;
        this.categories = null;
        this.statuses = null;
        this.priorities = null;
        this.privacies = null;
        this.transferId = null;
        this.isExported = false;
    }
}
var gTableName = "grdRejectedDocumentsItems";
var gLocked = false;
function dismiss(id, delegationId) {
    Common.showConfirmMsg(Resources.DismissConfirmation, function () {
        Common.ajaxPost('/Transfer/Dismiss',
            {
                'id': id, 'delegationId': delegationId, 'fromExported': true, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                GridCommon.RefreshCurrentPage(gTableName, false);
            }, function () { Common.showScreenErrorMsg(); }, false
        );
    }, function () {

    });
}
function resend(data, delegationId) {
    Common.showConfirmMsg(Resources.ResendConfirmation, function () {

        var broadcastModel = {};
        var exportedCategoryModel = new CategoryModel().findFullById(data.exportedDocumentCategoryId);
        if (typeof exportedCategoryModel !== 'undefined' && exportedCategoryModel !== "" && exportedCategoryModel !== null) {
            if (exportedCategoryModel.basicAttribute !== "" && exportedCategoryModel.basicAttribute !== null) {
                let basicAttributes = JSON.parse(exportedCategoryModel.basicAttribute);
                if (basicAttributes.length > 0) {
                    let receivingEntityObj = $.grep(basicAttributes, function (e) {
                        return e.Name === "ReceivingEntity";
                    });
                    if (receivingEntityObj[0].BroadcastReceivingEntity && receivingEntityObj[0].Type === "internal") {
                        broadcastModel.isInternalBroadcast = true;
                        broadcastModel.isBroadcast = true;
                    } else if (receivingEntityObj[0].BroadcastReceivingEntity) {
                        broadcastModel.isBroadcast = true;
                    }
                }
            }
        }
        var receivingEntities = data.receivingEntityId;
        var delegationId = null;
        var transferToType = broadcastModel.isInternalBroadcast ? TransferType.BroadcastSend : TransferType.Send;
        var transferToTxt = data.fromUserId;
        //    , , , self.model.fromUser, self, null, false, actionArray);
        //receivingEntities, delegationId, transferToType, transferToTxt, self, signatureTemplate, withSign, actionArray

    let modalWrapper = $(".modal-window");
    var modelIndex = new ExportOptions.ExportOptions();
    //let allPurposes = new Helper().get();
    var defaultPurposeForExport = window.ToViewPurpose /*allPurposes.filter(p => p.id === 13 || p.text.includes("To View") || p.text.includes("للعرض"))*/;
    modelIndex.purposes = new Helper().getPurpose();
    modelIndex.receivingEntities = receivingEntities;
    modelIndex.transferToType = transferToType;
    modelIndex.transferToUser = transferToTxt;
        modelIndex.transferToStructure = data.toStructureId;
    modelIndex.customAttributeDueDate = data.dueDate;
        modelIndex.documentId = data.exportedDocumentId;
        modelIndex.transferId = data.signedTransferId;
    modelIndex.fromVip = false;
    modelIndex.enableSendingRules = window.EnableSendingRules === "True";
    modelIndex.enableTransferToUsers = window.EnableTransferToUsers === "True";
    modelIndex.isStructureSender = window.IsStructureSender === "True";
    modelIndex.delegationId = delegationId;
    modelIndex.structureIds = $("#hdStructureIds").val().split(window.Seperator);
    modelIndex.fromStructureId = data.fromStructureId;
    modelIndex.signatureTemplate = null
    modelIndex.withSign = false;
    modelIndex.fromExport = false;
        modelIndex.fromResend = true;
        modelIndex.incomingDocumentId = data.documentId;
    modelIndex.isSpecific = false;
    if (transferToType == TransferType.BroadcastSend || transferToType == TransferType.BroadcastComplete) {
        modelIndex.isBroadcast = true;
        modelIndex.broadcastIds = data.SignedTransferId;
    }
    modelIndex.action = "Attribute.Export";
        var ExportOptionsView = new ExportOptions.ExportOptionsView(modalWrapper, modelIndex, function () {
            setTimeout(() => {
                GridCommon.Refresh(gTableName);
                TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
            },100);
        });
        modelIndex.defaultPurposeForExport = defaultPurposeForExport ? defaultPurposeForExport[0].id : null;
        ExportOptionsView.render();
        $('#modalExportOptions').modal('show');
        $("#modalExportOptions").off("hidden.bs.modal");
        $("#modalExportOptions").off("shown.bs.modal");
        $('#modalExportOptions').on('hidden.bs.modal', function () {
            $('#modalExportOptions').remove();
        });
    }, function () {
       
    });
}
function openSearchDocument(id, delegationId, fromLink) {
    var params = { id: id };
    if (delegationId !== null) {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetSearchDocument', params, function (response) {
        if (response && response === "NoAccess") {
            Common.alertMsg(Resources.NoPermission);
        } else {
            if (!response.id) {
                return;
            }
            var wrapper = $(".modal-documents");
            var model = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocument();
            model.reference = response.referenceNumber;
            model.subject = response.subject;
            model.documentId = response.id;
            var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, model);
            linkedCorrespondenceDocument.render();

            model = new DocumentDetails.DocumentDetails();
            model.documentModel = response;
            model.readonly = true;
            model.delegationId = delegationId;
            model.documentId = response.id;
            model.referenceNumber = response.referenceNumber;
            model.categoryName = response.categoryName;
            model.statusId = response.status;
            model.createdByUser = response.createdByUser;
            model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            model.showMyTransfer = false;
            model.showVisualTrackingOnly = false;
            model.attachmentVersion = response.attachmentVersion;
            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
            var tabs = [];
            var nodeId = /*$('[data-inherit="' + TreeNode.Inbox+ '"]').first().data("id");*/ TreeNodes.Search;
            if (nodeId !== undefined && $.isNumeric(nodeId)) {
                tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
                model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
            }
            model.tabs = $.grep(tabs, function (element, index) {
                return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                    !element.Name.includes("notes") &&
                    !element.Name.includes("visualTracking") && !element.Name.includes("activityLog") &&
                    !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory") && !element.Name.includes("attachments");
            });
            model.tabsWithStatic = tabs;
            model.showBackButton = false;
            model.isModal = true;
            model.fromRejectedDocument = true;
            model.attachmentId = response.attachmentId;
            wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);
            var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
            view.render();
            $($(view.refs['documentDetailsContainerDiv']).find('.div-flex')[0]).addClass('width100pc-ml0');

            var title = response.categoryName;
            if (response.referenceNumber) {
                title += ' - ' + response.referenceNumber;
            }
            if (response.createdByUser) {
                title += ' - ' + response.createdByUser;
            }
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocumentTitle']).html(title);
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                if ($(this).data("remove") != true)
                    return;
                $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                swal.close();
                if ($(".modal-documents").children().length > 0) {
                    $('body').addClass('modal-open');
                }
            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

        }
    }, function () { Common.showScreenErrorMsg(); }, true);

}

function UpdateButtonVisibility(tableId) {
    var selectedRows = GridCommon.GetSelectedRows(tableId).length;

    // Show buttons if at least one row is selected
    if (selectedRows > 0 && !self.model.isExported) {
        //$(".html5buttons").removeClass("hidden");
        $(".conditional-buttons").removeClass("hidden");

        //table.buttons.add(allButtons);

    } else if (selectedRows == 0) {
        $(".conditional-buttons").addClass("hidden");

        //    $(".html5buttons").addClass("hidden");
    }
}
function buildColumns(gridcolumns) {

    gridcolumns.push({
        title: Resources.ReferenceNumber, data: "referenceNumber",
        render: function (data, type, full, meta) {
            if (full.referenceNumber != undefined && full.referenceNumber != "" && full.referenceNumber != null) {
                var linkedCorresRefNumList = full.referenceNumber.split(',');
                var html = '';
                $(linkedCorresRefNumList).each(function () {
                    html += "<div>" + this + "</div>";
                });
                return html;
            }
            return "";
        },
        "orderable": false, "className": "min-max-width-150-250 copyToClipboard"
    });

    gridcolumns.push({
        title: Resources.Subject, data: "subject",
        render: function (data, type, full, meta) {
            if (full.subject != undefined && full.subject != "" && full.subject != null) {
                var html = '';
                html += "<div>" + full.subject + "</div>";
                return html;
            }
            return "";
        },
        "orderable": false, "className": "min-max-width-150-250 copyToClipboard"
    });

    gridcolumns.push({ title: Resources.ReceivingEntity, data: "toStructure", "orderable": false, width: "150px" });

    gridcolumns.push({
        title: Resources.ReturnedDate, data: "rejectedDate",
        render: function (data, type, full, meta) {
            return DateConverter.toHijriFormated(data, null, window.CalendarType);
        }, width: "150px",
        "orderable": false, "className": "min-max-width-150-250"
    });
    gridcolumns.push({
        title: Resources.Status, data: "requestStatus", "orderable": false, width: "50px",
        "render": function (data, type, full, meta) {
            var statuses = new RequestStatuses().get();
            for (var i = 0; i < statuses.length; i++) {
                if (statuses[i].Name === data) {

                    var color = "#27c24c";
                    if (statuses[i].color !== undefined && statuses[i].color !== null) {
                        color = statuses[i].color;
                    }
                    return "<div class='label' style='background-color:" + color + "'>" + statuses[i].text + "</div>";
                }
            }
            return "";
        }
    });
        
    gridcolumns.push({ title: Resources.ReturnReason, data: "rejectionReason", "orderable": false, width: "150px" });
    gridcolumns.push({
        data: "cced", "orderable": false, width: "50px",
        "render": function (data, type, full, meta) {
            if (full.document.documentReceiverEntity[0].exportIsOriginal == false || full.document.documentReceiverEntity[0].exportIsOriginal == null) {
                return "<div class='mr-sm' title='" + Resources.CarbonCopy + "'><i class='fa fa-cc text-warning'></i></div>";
            }
            return "";
        }
    });

}
function buildFilters() {
    
        var html = '<div class="row">';
        html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
            '<input type="text" id="txtFilterRejectedDocumentsReferenceNumber" class="form-control" autocomplete="off" tabindex="1" aria-hidden="true"></div></div>';
        html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 list-action-filter"><button id="btnFilterRejectedDocumentsSearch" tabindex="9" type="button" class="btn btn-primary">' + Resources.Search + '</button>' +
            ' <button id="btnFilterRejectedDocumentsClear" tabindex="10" type="button" class="btn btn-clear">' + Resources.Clear + '</button></div>';
        html += '</div>';
        $('#filtersContainer').html(html);

        $('#txtFilterRejectedDocumentsReferenceNumber').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterRejectedDocumentsSearch').focus();
                }
            }
        });
        $('#btnFilterRejectedDocumentsClear').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterRejectedDocumentsSearch').focus();
                }
                else {
                    $('#txtFilterRejectedDocumentsReferenceNumber').focus();
                }
            }
        });
        $("#btnFilterRejectedDocumentsSearch").on('click', function () {
            GridCommon.Refresh(gTableName);
        });
        $("#btnFilterRejectedDocumentsClear").on('click', function () {
            
            $("#txtFilterRejectedDocumentsReferenceNumber").val('');
            GridCommon.Refresh(gTableName);
        });
}
function openDocument(id, transferId, nodeId, delegationId, resendModel) {
        Common.ajaxPost('/Document/Reopen',
            {
                'documentIds': id, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                swal.close()
                if (!result) {
                    Common.alertMsg(Resources.ErrorOccured);
                } else {
                    Common.ajaxGet('/Document/Get', { id: id, delegationId: delegationId, fromRejectedDocument: true }, function (data) {
                        gLocked = false;
                        //Common.setActiveSidebarMenu("liDraft");
                        $(".delegation").removeClass("active");
                        //$("#gridContainerDiv").hide();

                        var wrapper = $(".modal-documents");
                        var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
                        linkedCorrespondenceModel.reference = data.referenceNumber;
                        linkedCorrespondenceModel.subject = data.subject;
                        linkedCorrespondenceModel.documentId = id;
                        //linkedCorrespondenceModel.subject = $("#" + model.ComponentId + "_subjectTaskPanel").html();
                        //linkedCorrespondenceModel.from = $("#" + model.ComponentId + "_fromTaskPanel").html();
                        //linkedCorrespondenceModel.to = $("#" + model.ComponentId + "_toTaskPanel").html();
                        //linkedCorrespondenceModel.transferDate = $("#" + model.ComponentId + "_transferDateTaskPanel").html();
                        //linkedCorrespondenceModel.registerDate = $("#" + model.ComponentId + "_registerDateTaskPanel").html();
                        //linkedCorrespondenceModel.registeredBy = $("#" + model.ComponentId + "_registerByTaskPanel").html();
                        var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
                        linkedCorrespondenceDocument.render();

                        var model = new DocumentDetails.DocumentDetails();
                        model.documentId = id;
                        model.statusId = data.status;
                        model.categoryId = data.categoryId;
                        model.categoryName = data.categoryName;
                        model.documentModel = data;
                        model.senderPerson = data.senderPerson;
                        model.receiverPerson = data.receiverPerson;
                        model.isExternalReceiver = data.isExternalReceiver;
                        model.isExternalSender = data.isExternalSender;
                        model.referenceNumber = data.referenceNumber;
                        model.showMyTransfer = false;
                        model.readonly = false;
                        model.hasAttachments = data.attachmentCount > 0;
                        model.templateHasSignature = data.TemplateHasSignature;
                        model.isSigned = data.IsSigned;
                        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
                        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language, delegationId));
                        var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[data.categoryId].Tabs;
                        model.tabs = $.grep(tabs, function (element, index) {
                            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                                !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                                !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                                !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
                        });
                        model.tabsWithStatic = tabs;
                        model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[data.categoryId].SecurityTabs;
                        model.showBackButton = true;
                        model.attachmentId = data.attachmentId;

                        model.fromDraft = false;
                        model.fromRejectedDocument = true;
                        model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
                        model.isModal = true;
                        model.showBackButton = false;
                        model.delegationId = delegationId;
                        model.nodeId = nodeId;
                        model.resendData = resendModel;
                        wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

                        var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
                        $(view.refs['hdId']).val(transferId);
                        view.render();

                        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
                        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
                        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                            $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');

                        });
                        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                            if ($(this).data("remove") != true)
                                return;
                            $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                            swal.close();
                            $(".modal-window").empty();
                            if ($(".modal-documents").children().length > 0 || $(".modal-window").children().length > 0) {
                                $('body').addClass('modal-open');
                            }

                        });
                        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

                        $(document).off('click', '.btn-back');
                        $(document).on('click', '.btn-back', function () {
                            $("#gridContainerDiv").show();
                            documentView.remove();
                            $(".toRemove").remove();
                        });
                        $(document).off('click', '.btn-export');
                    }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);

                }
            }, null, false);
    
}
function EditAfterExport(id, transferId, nodeId, delegationId, resendModel) {
    Common.showConfirmMsg(Resources.EditAfterSignConfirmation, function () {
        Common.mask(document.body, "body-mask");
        Common.ajaxPost('/DocumentLock/EditAfterExport',
            {
                'documentId': id, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                //swal.close();
                if (result.success) {
                    GridCommon.Refresh(gTableName);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                    Common.showScreenSuccessMsg();
                } else {
                    if (result.message == "")
                        Common.showScreenErrorMsg();
                    else {
                        setTimeout(() => {
                            Common.alertMsg(result.message);
                        }, 100);
                    }
                }

                Common.unmask("body-mask");
            }, function () {
                swal.close();
                Common.unmask("body-mask");
                Common.showScreenErrorMsg();
            }, false);
    });
}


class RejectedDocumentsView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "rejectedDocuments", model);
    }
    render()
{
        var self = this;
        var model = this.model;
        $.fn.select2.defaults.set("theme", "bootstrap");
        var categoryModels = new CategoryModel().getFullCategories();
        if (categoryModels.length > 0)
        {
            for (var i = 0; i < categoryModels.length; i++)
            {
                if (categoryModels[i].basicAttribute !== "" && categoryModels[i].basicAttribute !== null)
                {
                    let basicAttributes = JSON.parse(categoryModels[i].basicAttribute);
                    if (basicAttributes.length > 0)
                    {
                        let receivingEntityObj = $.grep(basicAttributes, function (e)
                        {
                            return e.Name === "ReceivingEntity";
                        });
                        if (receivingEntityObj[0].BroadcastReceivingEntity)
                        {
                            for (var j = 0; j < self.model.categories.length; j++)
                            {
                                if (self.model.categories[j].id == categoryModels[i].id)
                                {
                                    self.model.categories[j].isBroadcast = true;
                                    self.model.categories[j].isInternalBroadcast = receivingEntityObj[0].Type === "internal";
                                }
                            }
                        }
                    }
                }
            }
        }

        buildFilters();
        Common.gridCommon();
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName).map(button => {
            button.className = (button.className || '') + ' hidden conditional-buttons';
            return button;
        });
        var exportButton = [{
            className: 'btn-sm btn-primary',

            extend: 'print',
            text: Resources.Print,
            title: function () {
                return Resources.reportRejectedDocuments;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.reportRejectedDocuments + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [':visible', 2, 3]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportRejectedDocuments.print
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'excelHtml5',
            title: function () {
                return Resources.reportRejectedDocuments;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.reportRejectedDocuments + '_' + formattedDate + '_' + formattedTime;
            },


            exportOptions: {
                columns: [':visible', 2, 3]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportRejectedDocuments.excelHTML5
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'pdfHtml5',
            title: function () {
                return Resources.reportRejectedDocuments;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.reportRejectedDocuments + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [':visible', 2, 3]
            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportRejectedDocuments.pdfHTML5
        }
        ];

        var allButtons = [exportButton, ...buttons];

        var columns = [
            {
                title: "", visible: true, width: '0px', "orderable": false,
                "render": function (data, type, row) {
                    return "<div class='hidden'  data-id=" + row.id + " />";

                }
            },
            {
                title: '<input id="chkAll" type="checkbox" />',
                width: '16px',
                orderable: false,
                render: function (data, type, row) {
                    return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " data-subject='" + row.subject + "' data-toStructure='" + row.toStructure + "' />";
                }
            },
        { title: "Id", data: "id", visible: false, "orderable": false }];


        buildColumns(columns, self.model.nodeId, false);
        columns.push({

            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = '';

                if (/*full.requestStatus == "Rejected"*/true) {
                    var resendModelJSON = {
                        documentId: full.documentId,
                        fromStructureId: full.fromStructureId,
                        signedTransferId: full.signedTransferId,
                        exportedDocumentId: full.exportedDocumentId,
                        dueDate: full.dueDate,
                        toStructureId: full.toStructureId,
                        fromUserId: full.fromUserId,
                        receivingEntityId: full.receivingEntityId,
                        exportedDocumentCategoryId: full.exportedDocument.categoryId
                    }
                    var resendModel = JSON.stringify(resendModelJSON)

                    let btnDismiss = document.createElement("button");
                    btnDismiss.setAttribute("class", "btn btn-xs btn-danger mr-sm dismiss");
                    btnDismiss.setAttribute("title", Resources.Dismiss);
                    btnDismiss.setAttribute("clickattr", "dismiss(" + full.id + ", " + self.model.delegationId + ")");
                    btnDismiss.innerHTML = "<i class='fa fa-times fa-white'/>";
                    html += btnDismiss.outerHTML;

                    let btnResend = document.createElement("button");
                    btnResend.setAttribute("class", "btn btn-xs btn-info mr-sm resend")
                    btnResend.setAttribute("title", Resources.Resend);
                    btnResend.setAttribute("clickattr", "resend(" + resendModel + "," + self.model.delegationId + ")");
                    btnResend.innerHTML = "<i class='fa fa-paper-plane fa-white'/>";
                    html += btnResend.outerHTML;

                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-primary mr-sm edit");
                    btn.setAttribute("title", Resources.Edit);
                    btn.setAttribute("clickattr", "openDocument(" + full.exportedDocumentId + ", " + full.signedTransferId + ", " + window.ReadyForExportNodeId + ", " + null + "," + resendModel + ")");
                    btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                    html += btn.outerHTML;

                    let EditAfterExportbtn = document.createElement("button");
                    EditAfterExportbtn.setAttribute("class", "btn btn-xs btn-warning edit");
                    EditAfterExportbtn.setAttribute("title", Resources.EditAfterExport);
                    EditAfterExportbtn.setAttribute("clickattr", "EditAfterExport(" + full.exportedDocumentId + ", " + full.signedTransferId + ", " + window.ReadyForExportNodeId + ", " + null + "," + resendModel + ")");
                    EditAfterExportbtn.innerHTML = "<i class='fa fa-external-link-square fa-white'/>";
                    html += EditAfterExportbtn.outerHTML;
                } else {

                    let btnView = document.createElement("button");
                    btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view")
                    btnView.setAttribute("title", Resources.View);
                    btnView.setAttribute("clickattr", "openSearchDocument(" + full.exportedDocumentId + "," + self.model.delegationId + "," + false + ")");
                    btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                    html += btnView.outerHTML;
                }

                return "<div style='display: inline-flex;'>" + html + "</div>";


            }
        });
        

        let table = $("#" + gTableName)
            .DataTable({
                "createdRow": function (row, data, dataIndex , textColor)
                {
                    var color = "";
                    var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
              
                        for (var i = 0; i < priorities.length; i++) {
                            if (priorities[i].id === data.priorityId) {
                                color = priorities[i].color;
                            }
                        }

                    if (color !== "")
                    {
                        $(row).attr('style', "color:" + color);
                    }
                    $(row).css('cursor', 'pointer');

                },
                processing: true,
                ordering: true,
                serverSide: true,
                // The attributes data of this datatable dropdown comes by default from the appComponent file from the ($.fn.dataTable.defaults >> values) and we can override any datatable attribute value here
                "paging": true, // Enable pagination
                pageLength: 10,  // The default number of transfer rows per page
                //lengthMenu: [10, 25, 50, 100, 200],  // Dropdown options coming by default from the appComponent for rows per page
                "ajax": {
                    "url": "/Transfer/ListRejectedDocumentsGrid",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d)
                    {
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.ReferenceNumber = $("#txtFilterRejectedDocumentsReferenceNumber").val() !== "" && typeof $("#txtFilterRejectedDocumentsReferenceNumber").val() !== "undefined" ? $("#txtFilterRejectedDocumentsReferenceNumber").val() : "";
                        d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
                        return d;
                    }, "dataSrc": function (response) {
                        return response.data;
                    }
                },
                "order": [],
                "columns": columns,
                dom: '<"html5buttons "B>ltr<"pull-left"p><"pull-right"i>',

                "fnInitComplete": function (settings, json)
                {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                buttons: allButtons,

            });

        table.on('draw.dt', function () {
           
            $('#grdItems tbody tr td').each(function () {
                this.setAttribute('title', $(this).text());
            });
            GridCommon.CheckSelectedRows(gTableName);

            if ($('#expandAll')[0] != undefined) {
                $('#expandAll')[0].classList.add("expand");
                $('#expandAll')[0].classList.remove("colllapse");
            }

            $('#' + gTableName + " td input[type='checkbox']").on('click', function () {

                if ($(this).is(":checked"))
                    $(".conditional-buttons").removeClass("hidden");

                else if (GridCommon.GetSelectedRows(gTableName).length == 1)
                    $(".conditional-buttons").addClass("hidden");

            });

        })
            
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        GridCommon.AddCheckBoxEvents(gTableName);
        if (window.AllowRowSelection == "True") {
            $('#' + gTableName + ' tbody').on('click', 'tr', function () {

                UpdateButtonVisibility(gTableName);
            });
        }
        $('#' + gTableName + ' tbody').on('click', ".resend,.dismiss,.recall,.edit,.view", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#' + gTableName + ' tbody').on('dblclick', 'tr', function () {
                
                if (!gLocked) {
                    gLocked = true;
                    try {
                        var onclick = $(this).find(".edit").attr("clickattr");
                        if (!onclick) {
                            onclick = $(this).find(".view").attr("clickattr");
                        }
                        eval(onclick);
                    } catch (e) {
                        gLocked = false;
                    }
                }
            });
        $('#' + gTableName + ' tbody').on('click', 'td.parentCheckAll', function (event) {
            event.stopPropagation();
            let tr = $(this).closest('tr');
            var data = table.row(tr).data();
            $('[parent=' + data.id + ']').prop('checked', $('[data-id=' + data.id + ']').prop('checked'));
            $('[parent=' + data.id + ']').trigger("change");
        });
        $('#filtersContainer input').off('keydown', function (e) { });
        $('#filtersContainer input').on('keydown', function (e)
        {
            var code = e.keyCode || e.which;            
            if (code === 13)
            {
                $("#btnFilterRejectedDocumentsSearch").trigger('click');
            }
        });


        $('#' + gTableName).on('click', '#chkAll', function () {
            let isChecked = $(this).is(":checked");

            if (isChecked) {
                $(".html5buttons .btn-danger").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".html5buttons .btn-danger").addClass("hidden");
                $(".conditional-buttons").addClass("hidden");
            }

            let table = $('#' + gTableName).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', isChecked);
        });

        $('#' + gTableName).on('change', 'input[type="checkbox"]', function () {
            let table = $('#' + gTableName).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();

            let totalCheckboxes = $('input[type="checkbox"]', pageNodes).length;
            let checkedCheckboxes = $('input[type="checkbox"]:checked', pageNodes).length;

            $('#chkAll').prop('checked', totalCheckboxes === checkedCheckboxes);

            if (checkedCheckboxes > 0) {
                $(".html5buttons .btn-danger").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".html5buttons .btn-danger").addClass("hidden");
                $(".conditional-buttons").addClass("hidden");
            }
        });

        table.on('draw', function () {
            $('#chkAll').prop('checked', false);

            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', false);

            $(".html5buttons .btn-danger").addClass("hidden");
            $(".conditional-buttons").addClass("hidden");
        });

       
        SecurityMatrix.InitContextMenu(securityMatrix, self.model.nodeId);



    }

    
}

export default { RejectedDocuments, RejectedDocumentsView };