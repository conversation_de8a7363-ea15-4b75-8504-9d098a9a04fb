{{#if isModal}}
<div id="modalCategoryReferenceNumber" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade">
    <div class="modal-dialog modal-sm" id="modalDiv">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" id="catRefNumberClose" class="close" data-dismiss="modal">&times;</button>
                <h4 id="modalCategoryReferenceNumberTitle" class="modal-title"></h4>
            </div>
            <div class="modal-body">
                <input type="button" style="display:none" id="btnClearForm" />
                <form id="formPost" method="post" data-parsley-validate="" novalidate="">
                    <div class="row" id="categoryDiv">
                        <div class="col-md-12" id="indexCategoryContainer">
                            <div class="form-group">
                                <label class="control-label">{{Localizer 'Category'}} </label>
                                <select id="cmbReferenceCategories" tabindex="1" class="form-control">
                                    {{#each categories}}
                                    <option value="{{id}}">{{text}}</option>
                                    {{/each}}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row" id="referenceConfigDiv">
                        <input type="hidden" id="hddId" />
                        <input type="hidden" id="rowId" />
                        <input type="hidden" id="hdReferenceId" />
                        <div style="margin-left:0px" class="row">
                            <div class="col-md-3" id="GenerateRefrenceNoOnSign_OnTransfer">
                                <label class="control-label field-required">{{Localizer 'GenerateReferenceNumber'}} </label>
                                <select id="ReferenceNumberOption" class="form-control" required="required" data-parsley-errors-container="#ReferenceNumberOptionError" tabindex="0">
                                    <option value="{{id}}">{{text}}</option>
                                </select>
                                <div id="ReferenceNumberOptionError"></div>
                                <br />
                            </div>
                        </div>

                        <div class="col-md-3" id="cmbTypeContainer">
                            <label class="control-label field-required">{{Localizer 'Type'}} </label>
                            <select id="cmbType" class="form-control" required="required" data-parsley-errors-container="#cmbTypeError" tabindex="2">
                                <option value="Year" selected="selected">{{Localizer 'Year'}}</option>
                                <option value="Month">{{Localizer 'Month'}}</option>
                                <option value="String">{{Localizer 'String'}}</option>
                                <option value="Counter">{{Localizer 'Counter'}}</option>
                                <option value="Separator">{{Localizer 'Separator'}}</option>
                                <option value="StructureCode">{{Localizer 'StructureCode'}}</option>
                                <option value="Dynamic">{{Localizer 'Dynamic'}}</option>
                                <option value="DynamicProcedure">{{Localizer 'DynamicProcedure'}}</option>


                            </select>
                            <div id="cmbTypeError"></div>
                        </div>
                        <div class="col-md-2 form-group" id="cmbOrderContainer">
                            <label class="control-label field-required">{{Localizer 'Order'}} </label>
                            <select id="cmbOrder" class="form-control" required="required" data-parsley-errors-container="#cmbOrderError" tabindex="3">
                            </select>
                            <div id="cmbOrderError"></div>
                        </div>
                        <div id="stringParent" class="col-md-5 form-group">
                            <label class="control-label field-required">{{Localizer 'String'}} </label>
                            <input type="text" class="form-control " id="contentString" tabindex="4" />
                        </div>
                        <div id="stringFunctionName" class="col-md-3 form-group">
                            <label class="control-label field-required">{{Localizer 'JsFunctionName'}} </label>
                            <input type="text" class="form-control " id="contentFunctionName" tabindex="5" />
                        </div>
                        <div id="dynamicStoredProcedureName" class="col-md-4 form-group">
                            <label class="control-label field-required">{{Localizer 'dynamicStoredProcedureName'}} </label>
                            <input type="text" class="form-control " id="contentStoredProcedureName" tabindex="6" />
                        </div>
                        <div class="col-md-3" id="cmbCounterContentContainer">
                            <label class="control-label">{{Localizer 'Content'}}</label>
                        </div>
                        <div class="col-md-2 mt-26px" id="divResetByYear">
                            <div class="form-group">
                                <div class="checkbox c-checkbox">
                                    <label>
                                        <input tabindex="6" type="checkbox" id="chkResetByYear" name="ResetByYear">
                                        <span class="fa fa-check"></span>{{Localizer 'ResetByYear'}}
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3" id="cmbSeparatorContainer">
                            <label class="control-label field-required">{{Localizer 'Separator'}} </label>
                            <select id="cmbSeparator" class="form-control" data-parsley-errors-container="#cmbSeparatorError" tabindex="7">
                                <option value="/" name="slash">/</option>
                                <option value="-" name="dash">-</option>
                                <option value="." name="dot">.</option>
                            </select>
                            <div id="cmbSeparatorError"></div>
                        </div>
                        <div class="col-md-2 pull-right">
                            <div class="btn-group pull-right" style="margin-top:22px !important">
                                <button type="button" class="btn btn-warning btn-download arabic-btn-group-right" id="btnAdd" title="{{Localizer 'Add'}}" tabindex="8"><span class="fa fa-arrow-down"></span></button>
                                <button type="button" class="btn btn-danger arabic-btn-group-left" id="btnReferenceClear" title="{{Localizer 'Clear'}}" tabindex="9"><span class="fa fa-times-circle"></span></button>
                            </div>
                        </div>
                    </div>
                    <div class="row" id="referenceTableDiv">
                        <div class="col-md-12 pull-left" style="margin-top: 8px">
                            <table id="referencesTable" class="table table-striped table-hover" cellspacing="0" width="100%">
                            </table>
                        </div>
                    </div>
                </form>

                <div class="required mt-15px" id="requiredDiv"><span class="text-danger">*</span> {{Localizer 'RequiredFields'}}</div>
            </div>
            <div class="modal-footer">
                <button tabindex="10" id="btnNext" type="button" class="btn btn-primary">{{Localizer 'Next'}}</button>
                <button tabindex="11" id="btnReferenceSubmit" type="button" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin'></i> {{Localizer 'Saving'}}">{{Localizer 'Submit'}}</button>
                <button tabindex="12" id="btnClose" type="button" data-dismiss="modal" class="btn btn-default">{{Localizer 'Close'}}</button>
            </div>
        </div>
    </div>
</div>
{{else}}
<div class="panel panel-default">
    <div class="panel-body" oncontextmenu="return false;">
        <form id="formPost" method="post" data-parsley-validate="" novalidate="">
            <div class="row" id="categoryDiv">
                <div class="col-md-12" id="indexCategoryContainer">
                    <div class="form-group">
                        <label class="control-label">{{Localizer 'Category'}} </label>
                        <select id="cmbReferenceCategories" tabindex="1" class="form-control">
                            {{#each categories}}
                            <option value="{{id}}">{{text}}</option>
                            {{/each}}
                        </select>
                    </div>
                </div>
            </div>
            <div class="row" id="referenceConfigDiv">
                <input type="hidden" id="hddId" />
                <input type="hidden" id="rowId" />
                <input type="hidden" id="hdReferenceId" />
                <div style="margin-left:0px" class="row">
                    <div class="col-md-3" id="GenerateRefrenceNoOnSign_OnTransfer">
                        <label class="control-label field-required">{{Localizer 'GenerateReferenceNumber'}} </label>
                        <select id="ReferenceNumberOption" class="form-control" required="required" data-parsley-errors-container="#ReferenceNumberOptionError" tabindex="0">
                            <option value="{{id}}">{{text}}</option>
                        </select>
                        <div id="ReferenceNumberOptionError"></div>
                        <br />
                    </div>
                </div>

                <div class="col-md-3" id="cmbTypeContainer">
                    <label class="control-label field-required">{{Localizer 'Type'}} </label>
                    <select id="cmbType" class="form-control" required="required" data-parsley-errors-container="#cmbTypeError" tabindex="2">
                        <option value="Year" selected="selected">{{Localizer 'Year'}}</option>
                        <option value="Month">{{Localizer 'Month'}}</option>
                        <option value="String">{{Localizer 'String'}}</option>
                        <option value="Counter">{{Localizer 'Counter'}}</option>
                        <option value="Separator">{{Localizer 'Separator'}}</option>
                        <option value="StructureCode">{{Localizer 'StructureCode'}}</option>
                        <option value="Dynamic">{{Localizer 'Dynamic'}}</option>
                        <option value="DynamicProcedure">{{Localizer 'DynamicProcedure'}}</option>

                    </select>
                    <div id="cmbTypeError"></div>
                </div>
                <div class="col-md-2 form-group" id="cmbOrderContainer">
                    <label class="control-label field-required">{{Localizer 'Order'}} </label>
                    <select id="cmbOrder" class="form-control" required="required" data-parsley-errors-container="#cmbOrderError" tabindex="3">
                    </select>
                    <div id="cmbOrderError"></div>
                </div>
                <div id="stringParent" class="col-md-5 form-group">
                    <label class="control-label field-required">{{Localizer 'String'}} </label>
                    <input type="text" class="form-control " data-parsley-errors-container="#cmbContentStringError" id="contentString" data-parsley-noarabic="true" tabindex="4" />
                    <div id="cmbContentStringError"></div>
                </div>
                <div id="stringFunctionName" class="col-md-3 form-group">
                    <label class="control-label field-required">{{Localizer 'JsFunctionName'}} </label>
                    <input type="text" class="form-control " id="contentFunctionName" tabindex="5" />
                </div>
                <div id="dynamicStoredProcedureName" class="col-md-3 form-group">
                    <label class="control-label field-required">{{Localizer 'DynamicProcedureName'}} </label>
                    <input type="text" class="form-control " id="contentStoredProcedureName" tabindex="6" />
                </div>
                <div class="col-md-3" id="cmbCounterContentContainer">
                    <label class="control-label">{{Localizer 'Content'}}</label>
                </div>
                <div class="col-md-2 mt-26px" id="divResetByYear">
                    <div class="form-group">
                        <div class="checkbox c-checkbox">
                            <label>
                                <input tabindex="6" type="checkbox" id="chkResetByYear" name="ResetByYear">
                                <span class="fa fa-check"></span>{{Localizer 'ResetByYear'}}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-3" id="cmbSeparatorContainer">
                    <label class="control-label field-required">{{Localizer 'Separator'}} </label>
                    <select id="cmbSeparator" class="form-control" data-parsley-errors-container="#cmbSeparatorError" tabindex="7">
                        <option value="/" name="slash">/</option>
                        <option value="-" name="dash">-</option>
                        <option value="." name="dot">.</option>
                    </select>
                    <div id="cmbSeparatorError"></div>
                </div>
                <div class="col-md-2 pull-right">
                    <div class="btn-group pull-right" style="margin-top:22px !important">
                        <button type="button" class="btn btn-warning btn-download arabic-btn-group-right" id="btnAdd" title="{{Localizer 'Add'}}" tabindex="8"><span class="fa fa-arrow-down"></span></button>
                        <button type="button" class="btn btn-danger arabic-btn-group-left" id="btnReferenceClear" title="{{Localizer 'Clear'}}" tabindex="9"><span class="fa fa-times-circle"></span></button>
                    </div>
                </div>
            </div>
            <div class="row" id="referenceTableDiv">
                <div class="col-md-12 pull-left" style="margin-top: 8px">
                    <table id="referencesTable" class="table table-striped table-hover" cellspacing="0" width="100%">
                    </table>
                </div>
            </div>
        </form>
        <div class="required mt-15px" id="requiredDiv"><span class="text-danger">*</span> {{Localizer 'RequiredFields'}}</div>
    </div>
    <div class="panel-footer text-right">
        <button tabindex="5" id="btnReferenceSubmit" type="button" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin'></i> {{Localizer 'Saving'}}">{{Localizer 'Submit'}}</button>
    </div>
</div>
{{/if}}
