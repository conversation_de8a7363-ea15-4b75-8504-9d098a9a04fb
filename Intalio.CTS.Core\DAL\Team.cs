﻿using Intalio.Core.Interfaces;
using Intalio.CTS.Core.Model;
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Intalio.Core.Interfaces;
using LinqKit;


namespace Intalio.CTS.Core.DAL
{
    public partial class Team : IDbObject<Team>
    {
        public Team()
        {
        }

        private CTSContext _ctx;

        #region Properties

        public long Id { get; set; }
        public string Name { get; set; }
        public long CreatedByUserId { get; set; }
        public long StructureId { get; set; }

        public DateTime CreatedDate { get; set; }
        public virtual Structure Structure { get; set; }
        public virtual User CreatedByUser { get; set; }
        public virtual ICollection<TeamUsers> TeamUsers { get; set; }

        #endregion

        private void OpenDbContext()
        {
            if (_ctx == null)
            {
                _ctx = new CTSContext();
            }
        }

        #region Public Methods
        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                this.CreatedDate = DateTime.Now;
                ctx.Team.Add(this);
                ctx.SaveChanges();
            }
        }

        public Team Find(long id)
        {
            using (var ctx = new CTSContext())
            {
                
                return ctx.Team.AsNoTracking().FirstOrDefault(x => x.Id == id);
            }
        }
        public Team FindWithInclude(long id)
        {
            using (var ctx = new CTSContext())
            {
                
                return ctx.Team
                    .Include(s=>s.Structure)
                    .Include(s=>s.CreatedByUser)
                    .Include(d=>d.TeamUsers)
                    .Include(d=>d.TeamUsers).ThenInclude(d=>d.User)
                    .Include(d=>d.TeamUsers).ThenInclude(d=>d.Structure)
                    .Include(d=>d.TeamUsers).ThenInclude(d=>d.CreatedByUser)
                    .AsNoTracking().FirstOrDefault(x => x.Id == id);
            }
        }

        public void Update()
        {
            using (var ctx = new CTSContext())
            {
                
                ctx.Entry(this).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        public void Delete(long id)
        {
            using (var ctx = new CTSContext())
            {
                
                Team item = new Team { Id = id };

                ctx.Team.Attach(item);
                ctx.Team.Remove(item);
                ctx.SaveChanges();
            }
        }


        public List<Team> GetAll()
        {
            using (var ctx = new CTSContext())
            {
                
                return ctx.Team.ToList();
            }
        }
        public List<Team> GetListByUserAndStructure(long userId,long structureId)
        {
            using (var ctx = new CTSContext())
            {

                return ctx.Team.Where(s=>s.CreatedByUserId==userId && s.StructureId==structureId).ToList();
            }
        }
        public List<Team> GetListStructureId(long structureId)
        {
            using (var ctx = new CTSContext())
            {

                return ctx.Team.Where(s => s.StructureId == structureId).ToList();
            }
        }
        public void Delete()
        {
            Delete(Id);
        }
        public bool CheckUnique(long? id, string name,  long UserId)
        {
            using (var ctx = new CTSContext())
            {
                return (id.HasValue ? ctx.Team.Where(t => t.Id != id.Value).AsNoTracking() : ctx.Team.AsNoTracking())
                    .Any(t => t.CreatedByUserId == UserId &&
                              (t.Name.ToLower() == name.ToLower() ));
            }
        }
        public Task<int> GetCount(long userId)
        {
            OpenDbContext();
            IQueryable<Team> query = _ctx.Team.Where(x => x.CreatedByUserId == userId ).AsNoTracking();

            return Task.FromResult(query.Count());
        }

        public async Task<List<Team>> ListAsync(int startIndex, int pageSize, long userId,long structureId)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Team> query = ctx.Team.Include(x => x.CreatedByUser).Include(x => x.Structure).Where(x => x.CreatedByUserId == userId&&x.StructureId==structureId);

                return await query.Skip(startIndex).Take(pageSize).ToListAsync();

            }
        }
        public async Task<List<Team>> ListAsyncByStructureId(long structureId)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Team> query = ctx.Team.Include(x => x.CreatedByUser).Include(x => x.Structure)
                                                  .Where(x => x.StructureId == structureId);

                return await query.ToListAsync();
            }
        }

        public bool CheckHaveFollowUp(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FollowUp.Any(t => t.TeamId == id);
            }
        }

        #endregion
        #region Conversion

        public static implicit operator TeamModelView(Team item)
        {
            TeamModelView retValue = null;
            if (item != null)
            {
                retValue.Id = item.Id;
                retValue.Name = item.Name;
                retValue.StructureId = item.StructureId;
                retValue.UserTeams = item.TeamUsers.Select(d => (TeamUsersModelView)d).ToList();
            }

            return retValue;
        }

        #endregion

    }
}
