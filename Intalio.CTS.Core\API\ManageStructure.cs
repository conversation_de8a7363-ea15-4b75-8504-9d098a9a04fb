﻿using Intalio.Core;
using Intalio.Core.API;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Http;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.API
{
    public static class ManageStructure
    {
        #region Public Methods

        public static async void Provision(List<long> ids, string nodeId)
        {
            if (ids != null && ids.Count > 0)
            {
                string url = $"{Configuration.IdentityAuthorityUrl}/api/ListStructuresAndParents";
                MultipartFormDataContent data = new MultipartFormDataContent();

                foreach (long id in ids)
                {
                    data.Add(new StringContent(id.ToString()), "ids");
                }
                if (nodeId.StartsWith("Structure"))
                {
                    data.Add(new StringContent(nodeId.Replace("Structure", "")), "ids");
                }
                if (!string.IsNullOrEmpty(Configuration.StructureNameAr))
                {
                    data.Add(new StringContent(Configuration.StructureNameAr), "attributes");
                }
                if (!string.IsNullOrEmpty(Configuration.StructureNameFr))
                {
                    data.Add(new StringContent(Configuration.StructureNameFr), "attributes");
                }
                List<StructureModel> result = await Intalio.Core.Helper.HttpPostAsync<List<StructureModel>>(url, Configuration.IdentityAccessToken, data);

                if (!result.IsNullOrEmpty())
                {
                    foreach (StructureModel structureModel in result)
                    {
                        Intalio.Core.Model.StringValueText nameAr = !string.IsNullOrEmpty(Configuration.StructureNameAr) ? structureModel.Attributes.FirstOrDefault(a => a.Text.ToLower() == Configuration.StructureNameAr.ToLower()) : null;
                        Intalio.Core.Model.StringValueText nameFr = !string.IsNullOrEmpty(Configuration.StructureNameFr) ? structureModel.Attributes.FirstOrDefault(a => a.Text.ToLower() == Configuration.StructureNameFr.ToLower()) : null;

                        var structure = new Structure().Find(structureModel.Id);
                        if (structure == null)
                        {
                            structure = new Structure();
                            structure.Id = structureModel.Id;
                            structure.Code = !string.IsNullOrEmpty(structureModel.Code) ? structureModel.Code : null;
                            structure.Name = structureModel.Name;
                            structure.NameAr = nameAr != null ? nameAr.Value : null;
                            structure.NameFr = nameFr != null ? nameFr.Value : null;
                            structure.Insert();
                        }
                        else
                        {
                            structure.Id = structureModel.Id;
                            structure.Code = !string.IsNullOrEmpty(structureModel.Code) ? structureModel.Code : null;
                            structure.Name = structureModel.Name;
                            structure.NameAr = nameAr != null ? nameAr.Value : null;
                            structure.NameFr = nameFr != null ? nameFr.Value : null;
                            structure.Update();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Mapping structures from IAM
        /// </summary>
        /// <param name="ids"></param>
        public static async void Provision(List<long> ids)
        {
            var structureIds = new Structure().FindNotExistent(ids);
            if (structureIds.Count > 0)
            {
                var result = GetStructures(structureIds);
                if (!result.IsNullOrEmpty())
                {
                    foreach (StructureModel structureModel in result)
                    {
                        Intalio.Core.Model.StringValueText nameAr = !string.IsNullOrEmpty(Configuration.StructureNameAr) ? structureModel.Attributes.FirstOrDefault(a => a.Text.ToLower() == Configuration.StructureNameAr.ToLower()) : null;
                        Intalio.Core.Model.StringValueText nameFr = !string.IsNullOrEmpty(Configuration.StructureNameFr) ? structureModel.Attributes.FirstOrDefault(a => a.Text.ToLower() == Configuration.StructureNameFr.ToLower()) : null;
                        new Structure
                        {
                            Id = structureModel.Id,
                            Code = !string.IsNullOrEmpty(structureModel.Code) ? structureModel.Code : null,
                            Name = structureModel.Name,
                            NameAr = nameAr?.Value,
                            NameFr = nameFr?.Value
                        }.Insert();
                    }
                }
            }
        }

        public static List<StructureModel> GetStructures(List<long> structureIds)
        {
            string url = $"{Configuration.IdentityAuthorityUrl}/api/ListAllStructuresByIds";
            MultipartFormDataContent data = new MultipartFormDataContent();
            foreach (long id in structureIds)
            {
                data.Add(new StringContent(id.ToString()), "ids");
            }
            return Intalio.Core.Helper.HttpPost<List<StructureModel>>(url, Configuration.IdentityAccessToken, data);
        }
        public static StructureModel GetStructure(long structureId, Language lang)
        {
            string url = $"{Configuration.IdentityAuthorityUrl}/Api/GetStructure?id={structureId}&language={lang}";

            return Intalio.Core.Helper.HttpGet<StructureModel>(url,
                Configuration.IdentityAccessToken);
        }
        public static string GetStructureName(StructureModel structureModel, Language language)
        {

            var nameEn = structureModel.Name;
            var structureAttributes = structureModel.Attributes;
            var nameAr = !string.IsNullOrEmpty(Configuration.StructureNameAr) ?
                (structureAttributes.FirstOrDefault(a => a.Text.ToLower() == Configuration.StructureNameAr.ToLower()) != null ? structureAttributes.FirstOrDefault(a => a.Text.ToLower() == Configuration.StructureNameAr.ToLower()).Value : null) : nameEn;
            var nameFr = !string.IsNullOrEmpty(Configuration.StructureNameFr) ?
                (structureAttributes.FirstOrDefault(a => a.Text.ToLower() == Configuration.StructureNameFr.ToLower()) != null ? structureAttributes.FirstOrDefault(a => a.Text.ToLower() == Configuration.StructureNameFr.ToLower()).Value : null) : nameEn;
            var structureName = language == Language.EN ? nameEn : language == Language.AR ? nameAr : nameFr;
            structureName = string.IsNullOrEmpty(structureName) ? nameEn : structureName;
            return structureName;
        }

        public static List<long?> GetSendingRulesStructures(long? userStructureId, List<long> userStructureIds,
            long userId)
        {
            List<long?> structureIdsSendingRules = new List<long?>();
            if (userStructureId != null)
            {// Transfer
                structureIdsSendingRules.AddRange(ManageSendingRule
                        .ListStructureIdsBySendingRules(userId, userStructureId.Value));
            }
            else
            {// When View All as Favorite Structures and Distribution list
                foreach (var structureId in userStructureIds)
                {
                    structureIdsSendingRules.AddRange(ManageSendingRule
                        .ListStructureIdsBySendingRules(userId, structureId));
                }
            }

            return structureIdsSendingRules;
        }

        public static List<Structure> GetUsersAndStructures(string searchText, bool enableTransferToUsers, long userId, long structureId, List<long> structureIds, StructureType structureType, string searchStructure, string searchUser, bool isDepartment = false,bool AllowTransferToMe=false,long filterByLoggedInStructure=default)
        {
            List<Structure> structures = new List<Structure>();

            if (enableTransferToUsers && (structureType == StructureType.Internal || structureType == StructureType.Both))
            {
                if (structureType == StructureType.Internal)
                { if (AllowTransferToMe)
                    {
                        structures = new DAL.Structure().GetIncludeUserStructure(s => !s.IsExternal).Select(s => new Structure()
                        {
                            Id = s.Id,
                            ParentId = s.ParentId,
                            Name = s.Name,
                            Code = s.Code,
                            NameAr = s.NameAr,
                            NameFr = s.NameFr,
                            IsExternal = s.IsExternal,
                            Parent = s.Parent,
                            UserStructure = s.UserStructure./*Where(us => us.UserId != userId || us.StructureId != structureId).*/ToList()
                        }).ToList();
                    }
                    else
                    {
                        structures = new DAL.Structure().GetIncludeUserStructure(s => !s.IsExternal).Select(s => new Structure()
                        {
                            Id = s.Id,
                            ParentId = s.ParentId,
                            Name = s.Name,
                            Code = s.Code,
                            NameAr = s.NameAr,
                            NameFr = s.NameFr,
                            IsExternal = s.IsExternal,
                            Parent = s.Parent,
                            UserStructure = s.UserStructure.Where(us => us.UserId != userId || us.StructureId != structureId).ToList()
                        }).ToList();
                    }
                }
                else
                {
                    structures = new DAL.Structure().GetIncludeUserStructure(s => s.IsExternal || !s.IsExternal).Select(s => new Structure()
                    {
                        Id = s.Id,
                        Name = s.Name,
                        Code = s.Code,
                        NameAr = s.NameAr,
                        NameFr = s.NameFr,
                        IsExternal = s.IsExternal,
                        ParentId = s.ParentId,
                        Parent = s.Parent,
                        UserStructure = s.UserStructure.Where(us => us.UserId != userId || us.StructureId != structureId).ToList()
                    }).ToList();
                }
            }
            else
            {
                if (structureType == StructureType.Internal)
                {
                    structures = new DAL.Structure().GetIncludeUserStructure(s => (isDepartment ? (!s.IsExternal && s.IsDepartment) : !s.IsExternal)).Select(s => new Structure()
                    {
                        Id = s.Id,
                        ParentId = s.ParentId,
                        Name = s.Name,
                        Code = s.Code,
                        NameAr = s.NameAr,
                        NameFr = s.NameFr,
                        IsExternal = s.IsExternal,
                        Parent = s.Parent,
                        UserStructure = new List<UserStructure>()
                    }).ToList();
                }
                else if (structureType == StructureType.External)
                {
                    structures = new DAL.Structure().GetIncludeUserStructure(s => s.IsExternal).Select(s => new Structure()
                    {
                        Id = s.Id,
                        ParentId = s.ParentId,
                        Name = s.Name,
                        Code = s.Code,
                        NameAr = s.NameAr,
                        NameFr = s.NameFr,
                        IsExternal = s.IsExternal,
                        Parent = s.Parent,
                        UserStructure = new List<UserStructure>()
                    }).ToList();
                }
                else
                {
                    if (isDepartment)
                    {
                        structures = new DAL.Structure().GetIncludeUserStructure(s => s.IsDepartment || s.IsExternal).Select(s => new Structure()
                        {
                            Id = s.Id,
                            ParentId = s.ParentId,
                            Name = s.Name,
                            Code = s.Code,
                            NameAr = s.NameAr,
                            NameFr = s.NameFr,
                            IsExternal = s.IsExternal,
                            Parent = s.Parent,
                            UserStructure = new List<UserStructure>()
                        }).ToList();
                    }
                    else
                    {
                        structures = new DAL.Structure().GetIncludeUserStructure(s => s.IsExternal || !s.IsExternal).Select(s => new Structure()
                        {
                            Id = s.Id,
                            ParentId = s.ParentId,
                            Name = s.Name,
                            Code = s.Code,
                            NameAr = s.NameAr,
                            NameFr = s.NameFr,
                            Parent = s.Parent,
                            IsExternal = s.IsExternal,
                            UserStructure = new List<UserStructure>()
                        }).ToList();
                    }
                }
            }

            if (filterByLoggedInStructure != default)
            {
                structures = structures.Where(d => d.Id == filterByLoggedInStructure).ToList();
            }
            if (structureIds.Count != 0)
            {
                structures = structures.Where(s => structureIds.Contains(s.Id) || s.IsExternal).ToList();
            }

            if (!string.IsNullOrEmpty(searchText))
            {
                var structureFiltered = new List<DAL.Structure>();

                var structuresData = structures.Where(s => s.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (!string.IsNullOrEmpty(s.NameAr) && s.NameAr.Contains(searchText, StringComparison.OrdinalIgnoreCase)) ||
                (!string.IsNullOrEmpty(s.NameFr) && s.NameFr.Contains(searchText, StringComparison.OrdinalIgnoreCase))).Select(s => new Structure()
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    NameAr = s.NameAr,
                    NameFr = s.NameFr,
                    IsExternal = s.IsExternal,
                    Parent = s.Parent,
                    ParentId = s.ParentId,
                }).ToList();
                structureFiltered.AddRange(structuresData);

                var UsersData = structures.Select(s => new Structure()
                {
                    UserStructure = s.UserStructure.Where(us => (us.User.Firstname + " " + us.User.Lastname).Contains(searchText, StringComparison.OrdinalIgnoreCase)).ToList()
                }).Where(s => s.UserStructure.Any()).ToList();
                structureFiltered.AddRange(UsersData);

                return structureFiltered;
            }

            if (!string.IsNullOrEmpty(searchStructure) || !string.IsNullOrEmpty(searchUser))
            {
                var structureFiltered = new List<DAL.Structure>();

                if (!string.IsNullOrEmpty(searchStructure) && string.IsNullOrEmpty(searchUser))
                {
                    var structuresData = structures.Where(s => s.Name.Contains(searchStructure, StringComparison.OrdinalIgnoreCase) ||
                    (!string.IsNullOrEmpty(s.NameAr) && s.NameAr.Contains(searchStructure, StringComparison.OrdinalIgnoreCase)) ||
                    (!string.IsNullOrEmpty(s.NameFr) && s.NameFr.Contains(searchStructure, StringComparison.OrdinalIgnoreCase))).ToList();
                    structureFiltered.AddRange(structuresData);
                }
                else
                {
                    if (!string.IsNullOrEmpty(searchStructure))
                    {
                        structures = structures.Where(s => s.Name.Contains(searchStructure, StringComparison.OrdinalIgnoreCase) ||
                        (!string.IsNullOrEmpty(s.NameAr) && s.NameAr.Contains(searchStructure, StringComparison.OrdinalIgnoreCase)) ||
                        (!string.IsNullOrEmpty(s.NameFr) && s.NameFr.Contains(searchStructure, StringComparison.OrdinalIgnoreCase))).ToList();
                    }

                    var UsersData = structures.Select(s => new Structure()
                    {
                        UserStructure = s.UserStructure.Where(us => (us.User.Firstname + " " + us.User.Lastname).Contains(searchUser, StringComparison.OrdinalIgnoreCase)).ToList()
                    }).Where(s => s.UserStructure.Any()).ToList();
                    structureFiltered.AddRange(UsersData);
                }
                return structureFiltered;
            }

            return structures;
        }
        public static string GetStructureNameById(long? StructureId)
        {
            var name = string.Empty;
            var lang = Helper.GetLanguage();
            using (var ctx = new CTSContext())
            {
                if (StructureId.HasValue)
                {
      
                    name = (lang == Intalio.Core.Language.EN ? ctx.Structure.FirstOrDefault(s => s.Id == StructureId).Name :
                         lang == Intalio.Core.Language.AR ?
                         ctx.Structure.FirstOrDefault(s => s.Id == StructureId).NameAr : ctx.Structure.FirstOrDefault(s => s.Id == StructureId).NameFr);
                    if (String.IsNullOrEmpty(name))
                    {
                        name = ctx.Structure.FirstOrDefault(s => s.Id == StructureId).Name;
                    }

                }
                return name;
            }
        }


        public static Structure GetClosestPrivateStructure(long structureId)
        {
            var stc = (new Structure()).Find(structureId);
            while (stc != null)
            {
                if (stc.IsPrivate)
                    return stc;

                if (stc.ParentId == null)
                    break;

                stc = (new Structure()).Find(stc.ParentId.Value);
            }
            return null;
        }

        public static bool IsParentToStructure(long parentId, long childId)
        {
            if (parentId == childId)
                return true;

            var stc = (new Structure()).Find(childId);

            if (stc.ParentId == parentId) return true;

            while (stc.ParentId != null)
            {
                if (stc.ParentId.Value == parentId)
                    return true;

                stc = (new Structure()).Find(stc.ParentId.Value);
            }

            return false;
        }
        #endregion

        #region Private Methods



        #endregion
    }
}
