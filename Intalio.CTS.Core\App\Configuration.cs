﻿using Intalio.Core;
using Intalio.CTS.Core.App;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;

namespace Intalio.CTS.Core
{
    public static class Configuration
    {
        #region Fields

        private static CoreParameters Parametre { get { return Singleton<CoreParameters>.Instance; } }
        private static string _dbConnectionString;
        private static string _dbConnectionStringDecrypted;
        private static string _crawlerPassword;
        private static string _crawlerPasswordDecrypted;

        public static long userId;
        #endregion

        #region Properties

        public static string DbConnectionString
        {
            get { if (string.IsNullOrEmpty(_dbConnectionStringDecrypted)) { _dbConnectionStringDecrypted = _dbConnectionString; if (IsConnectionStringEncrypted) { var decryptedDbConnectionString = new EncryptionUtility().Decrypt(_dbConnectionString); if (!string.IsNullOrEmpty(decryptedDbConnectionString)) { _dbConnectionStringDecrypted = decryptedDbConnectionString; } } } return _dbConnectionStringDecrypted; }
            set { _dbConnectionString = value; }
        }
        public static string IdentityAuthorityUrl { get; set; }
        public static string IdentityClientId { get; set; }
        public static string IdentityClientSecret { get; set; }
        public static bool SameDomain { get; set; }
        public static string SmtpServer { get { return Parametre["SmtpServer"]; } }
        public static string SmtpSystemEmail { get { return Parametre["SmtpSystemEmail"]; } }
        public static string SmtpUserName { get { return Parametre["SmtpUserName"]; } }
        public static string SmtpPassword { get { return !string.IsNullOrEmpty(Parametre["SmtpPassword"]) ? new EncryptionUtility().Decrypt(Parametre["SmtpPassword"]) : null; } }
        public static int SmtpPort { get { return string.IsNullOrEmpty(Parametre["SmtpPort"]) ? 25 : Convert.ToInt32(Parametre["SmtpPort"]); } }
        public static bool SmtpEnableSSL { get { return string.IsNullOrEmpty(Parametre["SmtpEnableSSL"]) ? true : Convert.ToBoolean(Parametre["SmtpEnableSSL"]); } }
        public static DatabaseType DatabaseType { get; set; }
        public static string Version { get { return Parametre["Version"]; } }
        public static bool EnableCaching { get { return string.IsNullOrEmpty(Parametre["EnableCaching"]) ? true : Convert.ToBoolean(Parametre["EnableCaching"]); } }
        public static bool IsConnectionStringEncrypted { get; set; }
        public static bool EnableConfirmationMessage { get { return string.IsNullOrEmpty(Parametre["EnableConfirmationMessage"]) ? true : Convert.ToBoolean(Parametre["EnableConfirmationMessage"]); } }
        public static bool EnableSendingRules { get { return string.IsNullOrEmpty(Parametre["EnableSendingRules"]) ? true : Convert.ToBoolean(Parametre["EnableSendingRules"]); } }
        public static bool AllowTransferToMe { get { return string.IsNullOrEmpty(Parametre["AllowTransferToMe"]) ? true : Convert.ToBoolean(Parametre["AllowTransferToMe"]); } }

        public static string ApiScopeName { get; set; }
        public static string IdentityAccessToken { get; set; }
        public static string AdditionalAssembliesPath { get { return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AdditionalAssembliesPath"); } }
        public static bool EnableEmailNotification { get { return string.IsNullOrEmpty(Parametre["EnableEmailNotification"]) ? true : Convert.ToBoolean(Parametre["EnableEmailNotification"]); } }
        public static string InboxMode { get { return Utility.IdentityHelperExtension.GetUserInboxMode("InboxMode", userId); } }
        public static string OpenCorrespondenceMode { get { return Parametre["OpenCorrespondenceMode"]; } }
        public static bool LinkedDeleteForCreatorOnly { get { return string.IsNullOrEmpty(Parametre["LinkedDeleteForCreatorOnly"]) ? true : Convert.ToBoolean(Parametre["LinkedDeleteForCreatorOnly"]); } }
        public static LogoutAction LogoutAction { get { return string.IsNullOrEmpty(Parametre["LogoutAction"]) ? LogoutAction.RedirectToIdentity : Enum.Parse<LogoutAction>(Parametre["LogoutAction"]); } }

        public static string PriorityPrivacyAction { get { return Parametre["PriorityPrivacyAction"]; } }
        public static string ViewerMode { get { return Parametre["ViewerMode"]; } }

        //public static PriorityPrivacyAction PriorityPrivacyAction { get { return string.IsNullOrEmpty(Parametre[""]) ? PriorityPrivacyAction.Priority : Enum.Parse<PriorityPrivacyAction>(Parametre["PriorityPrivacyAction"]); } }
        public static string StructureNameAr { get { return Parametre["StructureNameAr"]; } }
        public static string StructureNameFr { get { return Parametre["StructureNameFr"]; } }
        public static string HideStructureFromTransfer { get { return Parametre["HideStructureFromTransfer"]; } }
        public static string UserStructureReceiver { get { return Parametre["UserStructureReceiver"]; } }
        public static string UserStructureSender { get { return Parametre["UserStructureSender"]; } }
        public static string UserPrivacy { get { return Parametre["UserPrivacy"]; } }
        public static string UserPosition { get { return Parametre["UserPosition"]; } }
        public static bool EnableTransferToUsers { get { return string.IsNullOrEmpty(Parametre["EnableTransferToUsers"]) ? true : Convert.ToBoolean(Parametre["EnableTransferToUsers"]); } }
        public static bool SearchAssignedStructureSearchUsersDocuments { get { return string.IsNullOrEmpty(Parametre["SearchAssignedStructureSearchUsersDocuments"]) ? true : Convert.ToBoolean(Parametre["SearchAssignedStructureSearchUsersDocuments"]); } }
        public static string ByFileMode { get { return Parametre["ByFileMode"]; } }
        public static string StorageServerUrl { get; set; }
        public static string ByFileExtensions { get { return Parametre["ByFileExtensions"]; } }
        public static int ReferenceCounterLength { get { return !string.IsNullOrEmpty(Parametre["ReferenceCounterLength"]) ? Convert.ToInt32(Parametre["ReferenceCounterLength"]) : 0; } }
        public static bool SendWithoutStructureReceiverOrPrivacyLevel { get { return string.IsNullOrEmpty(Parametre["SendWithoutStructureReceiverOrPrivacyLevel"]) ? false : Convert.ToBoolean(Parametre["SendWithoutStructureReceiverOrPrivacyLevel"]); } }
        public static double MaxFileSize { get { return !string.IsNullOrEmpty(Parametre["MaxFileSize"]) ? Convert.ToDouble(Parametre["MaxFileSize"]) : 0; } }
        public static string ViewerUrl { get; set; }
        public static string WebsiteUrl { get; set; }
        public static AuditTrailMode AuditTrailMode { get { return string.IsNullOrEmpty(Parametre["AuditTrailMode"]) ? AuditTrailMode.Basic : Parametre["AuditTrailMode"] == AuditTrailMode.None.ToString() ? AuditTrailMode.None : Parametre["AuditTrailMode"] == AuditTrailMode.Basic.ToString() ? AuditTrailMode.Basic : AuditTrailMode.Full; } }
        public static bool AttachmentEditable { get { return string.IsNullOrEmpty(Parametre["AttachmentEditable"]) ? false : Convert.ToBoolean(Parametre["AttachmentEditable"]); } }

        public static List<string> CrawlerServerUrls { get; set; }
        public static string CrawlerDefaultIndexName { get; set; }
        public static string CrawlerUserName { get; set; }
        public static string CrawlerPassword
        {
            get { if (string.IsNullOrEmpty(_crawlerPasswordDecrypted)) { _crawlerPasswordDecrypted = _crawlerPassword; if (IsCrawlerPasswordEncrypted) { var decryptedPassword = new EncryptionUtility().Decrypt(_crawlerPassword); if (!string.IsNullOrEmpty(decryptedPassword)) { _crawlerPasswordDecrypted = decryptedPassword; } } } return _crawlerPasswordDecrypted; }
            set { _crawlerPassword = value; }
        }
        public static bool IsCrawlerPasswordEncrypted { get; set; }
        public static int CrawlerBulkSize { get; set; }
        public static int CrawlerMaxResultWindow { get; set; }

        public static bool FixedLayout { get { return string.IsNullOrEmpty(Parametre["FixedLayout"]) ? false : Convert.ToBoolean(Parametre["FixedLayout"]); } }
        public static int HomePageMaximumWidget { get { return string.IsNullOrEmpty(Parametre["HomePageMaximumWidget"]) ? 4 : Convert.ToInt32(Parametre["HomePageMaximumWidget"]); } }
        public static int LinkedCorrespondenceLevels { get { return string.IsNullOrEmpty(Parametre["LinkedCorrespondenceLevels"]) ? 0 : Convert.ToInt32(Parametre["LinkedCorrespondenceLevels"]); } }

        public static string ApplicationName { get { return Parametre["ApplicationName"]; } }

        public static bool UsePrivacyColorInBookmark { get { return Parametre["UsePrivacyColorInBookmark"] == "true"; } }
        
        public static bool AllowRowSelection { get { return Parametre["AllowRowSelection"] == "true"; }  }

        public static CalendarType CalendarType
        {
            get
            {
                object calendarType;
                if (string.IsNullOrEmpty(Parametre["CalendarType"]))
                {
                    return CalendarType.None;
                }
                else
                {
                    Enum.TryParse(typeof(CalendarType), Parametre["CalendarType"], true, out calendarType);
                    return calendarType.IsNull() ? CalendarType.None : ((CalendarType)calendarType);
                }
            }
        }
        public static Language DefaultApplicationLanguage
        {
            get
            {
                if (string.IsNullOrEmpty(Parametre["DefaultApplicationLanguage"]))
                {
                    return Language.EN;
                }
                else
                {
                    Enum.TryParse(typeof(Language), Parametre["DefaultApplicationLanguage"], true, out object lang);
                    return lang.IsNull() ? Language.EN : ((Language)lang);
                }
            }
        }
        public static bool EnableOCR { get { return string.IsNullOrEmpty(Parametre["EnableOCR"]) ? false : Convert.ToBoolean(Parametre["EnableOCR"]); } }
        public static bool EnableAttributeEditSign { get { return string.IsNullOrEmpty(Parametre["EnableAttributeEditSign"]) ? false : Convert.ToBoolean(Parametre["EnableAttributeEditSign"]); } }

       // public static bool AllowEditSigned { get { return string.IsNullOrEmpty(Parametre["AllowEditSigned"]) ? false : Convert.ToBoolean(Parametre["AllowEditSigned"]); } }

        public static bool EnableAttributeEdit { get {
                return string.IsNullOrEmpty(Parametre["EnableAttributeEdit"]) ? false : Convert.ToBoolean(Parametre["EnableAttributeEdit"]);
            } }

        public static bool EnablePerStructure { get { return string.IsNullOrEmpty(Parametre["EnablePerStructure"]) ? false : Convert.ToBoolean(Parametre["EnablePerStructure"]); } }
        public static string FirstNameAr { get { return Parametre["FirstNameAr"]; } }
        public static string MiddleNameAr { get { return Parametre["MiddleNameAr"]; } }
        public static string LastNameAr { get { return Parametre["LastNameAr"]; } }

        public static string FirstNameFr { get { return Parametre["FirstNameFr"]; } }
        public static string MiddleNameFr { get { return Parametre["MiddleNameFr"]; } }
        public static string LastNameFr { get { return Parametre["LastNameFr"]; } }

        public static string DSURL { get; set; }
        public static string DMSURL { get; set; }
        public static string EnableNotifications { get { return Parametre["EnableNotifications"]; } }
        public static string AllowEditSigned { get { return Parametre["AllowEditSigned"]; } }
        public static bool EnableDraftVersioning { get { return string.IsNullOrEmpty(Parametre["EnableDraftVersioning"]) ? false : Convert.ToBoolean(Parametre["EnableDraftVersioning"]); } }
        public static int FollowUpCategory { get { return !string.IsNullOrEmpty(Parametre["FollowUpCategory"]) ? Convert.ToInt32(Parametre["FollowUpCategory"]) : 0; } }
        public static string DepartmentFollowUpNode { get { return Parametre["DepartmentFollowUpNode"]; } }
        public static string AssignedToMeNode { get { return Parametre["AssignedToMeNode"]; } }
        public static int FollowUpPurpose { get { return string.IsNullOrEmpty(Parametre["FollowUpPurpose"]) ? 3 : Convert.ToInt32(Parametre["FollowUpPurpose"]); } }
        public static string ViewerClientId { get; set;  }
        public static int ViwerRoleId { get; set;  }
        public static string PdfConvertorClientUrl { get; set; }
        public static int ForInfoPurpose { get { return string.IsNullOrEmpty(Parametre["ForInfoPurposeId"]) ? 4 : Convert.ToInt32(Parametre["ForInfoPurposeId"]); } }
        public static int RecallPurpose { get { return string.IsNullOrEmpty(Parametre["RecallPurpose"]) ? 14 : Convert.ToInt32(Parametre["RecallPurpose"]); } }

        public static string G2GURL { get { return Parametre["g2GUrl"]; } }
        public static string G2GConnectionString { get; set; }
        public static string G2GService { get { return Parametre["g2GService"]; } }

        #endregion

        #region Public Methods

        public static void ConfigureSystem()
        {
            new DAL.CTSContext().Database.Migrate();
        }

        #endregion
    }
}
