import Intalio from './common.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import DocumentSearch from './search.js'
import DocumentDetails from './documentDetails.js'
import { Categories, DelegationUsers } from './lookup.js'
var gIsLocked = false;
var gPaddingTop = 0;
var gWidth = 100;
function deleteLink(id, documentId, transferId, self)
{
    try
    {
        Common.showConfirmMsg(Resources.DeleteConfirmation, function ()
        {
            if (gIsLocked === false)
            {
                gIsLocked = true;
                Common.ajaxDelete('/LinkedDocument/Delete', {
                    'id': id, documentId: documentId, transferId: transferId, delegationId: self.model.delegationId,
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                }, function (data)
                {
                    gIsLocked = false;
                    if (!data)
                    {
                        setTimeout(function ()
                        {
                            Common.alertMsg(Resources.NoPermission);
                        }, 300);
                    } else
                    {
                        $(self.refs['grdLinkedCorrespondences']).DataTable().ajax.reload();
                    }

                }, function () { gIsLocked = false; }, false);
            }
        });
    } catch (ex)
    {
        gIsLocked = false;
    }
}
function openLinkWindow(self)
{
    if (!self.model.readOnly)
    {
        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        var wrapper = $(".modal-window");
        var modelIndex = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
        modelIndex.transferId = self.model.transferId;
        modelIndex.documentId = self.model.documentId;
        modelIndex.delegationId = self.model.delegationId;
        var linkedCorrespondenceIndex = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndexView(wrapper, modelIndex);
        linkedCorrespondenceIndex.render();
        var wrapper = $("#linkSearchDiv");
        let model = new DocumentSearch.DocumentSearch();
        model.categories = new Categories().get(window.language).filter(item => item.id != window.FollowUpCategory);
        model.statuses = statuses.filter(function (el) { return el.text !== Resources.Draft; });
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.delegationUsers = new DelegationUsers().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.importances = new CoreComponents.Lookup.Importances().get(window.language);
        model.documentId = self.model.documentId;
        model.fromLink = true;
        let view = new DocumentSearch.DocumentSearchView(wrapper, model);
        view.render();
        $('#modalLinkedCorrespondenceTitle').html(Resources.New);
        $('#modalLinkedCorrespondence').modal("show");
        $('#modalLinkedCorrespondence').off("hidden.bs.modal");
        $('#modalLinkedCorrespondence').off("shown.bs.modal");
        $('#modalLinkedCorrespondence').on('shown.bs.modal', function ()
        {
            $('#searchContainerDiv').find("h3").hide();
            setTimeout(function () { $('#cmbSearchFilterCategory').focus(); }, 200);
        });
        $('#modalLinkedCorrespondence').on('hidden.bs.modal', function ()
        {
            $('.search').fadeIn();
            $('.gridResult').hide();
            $('.btn-scroll').hide();
            GridCommon.Clear("grdSearchItems");
            $("#btnSearchFilterClear").trigger('click');
            $('#modalLinkedCorrespondence').remove();
            swal.close();
            //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0)
            //{
            //    $('body').addClass('modal-open');
            //}
        });
    }

}
function openLinkedDocument(documentId, delegationId, mainDocumentId,fromManageCorrespondance = false)
{
   
    var params = { id: documentId };
    if (fromManageCorrespondance) {
        if (mainDocumentId !== null) {
            params.parentDocumentId = mainDocumentId;
        }
    }
    
    if (delegationId !== null)
    {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetSearchDocument', params, function (response)
    {
        if (response && response === "NoAccess")
        {
            Common.alertMsg(Resources.NoPermission);
        } else
        {
            if (!response.id)
            {
                return;
            }
            var wrapper = $(".modal-window");
            var model = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
            var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, model);
            linkedCorrespondenceDocument.render();

            model = new DocumentDetails.DocumentDetails();
            model.documentModel = response;
            model.readonly = true;
            model.delegationId = delegationId;
            model.documentId = response.id;
            model.referenceNumber = response.referenceNumber;
            model.categoryName = response.categoryName;
            model.statusId = response.status;
            model.createdByUser = response.createdByUser;
            model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            model.showMyTransfer = false;
            model.fromSearch = true;
            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
            var tabs = [];
            var nodeId = /*$('[data-inherit="' + TreeNode.Inbox + '"]').first().data("id")*/ TreeNodes.Search;
            if (nodeId !== undefined && $.isNumeric(nodeId))
            {
                tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
                model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
            }
            model.tabs = $.grep(tabs, function (element, index)
            {
                return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                    !element.Name.includes("notes") &&
                    !element.Name.includes("visualTracking") && !element.Name.includes("activityLog") &&
                    !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory")
                    && !element.Name.includes("attachments") && !element.Name.includes("nonArchivedAttachments");
            });
            model.tabsWithStatic = tabs;
            model.showBackButton = false;
            model.isModal = true;
            model.attachmentId = response.attachmentId;
            model.parentLinkedDocumentId = mainDocumentId;
            wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);
            var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
            view.render();
            $($(view.refs['documentDetailsContainerDiv']).find('.div-flex')[0]).addClass('width100pc-ml0');
            var title = response.categoryName;
            if (response.referenceNumber)
            {
                title += ' - ' + response.referenceNumber;
            }
            if (response.createdByUser)
            {
                title += ' - ' + response.createdByUser;
            }
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocumentTitle']).html(title);
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).removeAttr("style");
            $(linkedCorrespondenceDocument.refs['modal-dialog']).attr("style", "width:" + gWidth + "% !important;display: block;padding-left: 9px !important;padding-right: 9px !important;padding-top:" + gPaddingTop + "px;");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function ()
            {
                gPaddingTop += 30;
                gWidth -= 5;
                $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function ()
            {
                gPaddingTop -= 30;
                gWidth += 5;
                if ($(this).data("remove") != true)
                    return;
                $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                swal.close();
                //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0)
                //{
                //    $('body').addClass('modal-open');
                //}
            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");
        }
    }, function () { Common.showScreenErrorMsg(); }, true);
}

function format(row) {

    return '<table style="width:100%" cellspacing="0" border="0">' + buildColumnsDetails(row) + '</table>';
}

function buildColumnsDetails(row) {
    var html = "";
    var columns = ["Subject", "Priority", "Privacy"];
    for (var i = 0; i < columns.length; i++) {
        var column = columns[i];
        switch (column) {
            case "Subject":
                html += '<tr><th style="width: 15%;padding:5px">' +
                    Resources.Subject +
                    ':</th><td style="width: 85%;padding:5px;word-break: break-all;">' +
                    (row.subject || '') +
                    '</td></tr>';
                break;
            case "Priority":
                var priority = "";
                var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                for (let j = 0; j < priorities.length; j++) {
                    if (priorities[j].id === row.priorityId) {
                        priority = priorities[j].text;
                        break;
                    }
                }
                html += '<tr><th style="width: 15%;padding:5px">' +
                    Resources.Priority +
                    ':</th><td style="width: 85%;padding:5px;word-break: break-all;">' +
                    priority +
                    '</td></tr>';
                break;
            case "Privacy":
                var privacy = "";
                var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                for (let j = 0; j < privacies.length; j++) {
                    if (privacies[j].id === row.privacyId) {
                        privacy = privacies[j].text;
                        break;
                    }
                }
                html += '<tr><th style="width: 15%;padding:5px">' +
                    Resources.Privacy +
                    ':</th><td style="width: 85%;padding:5px;word-break: break-all;">' +
                    privacy +
                    '</td></tr>';
                break;
        }
            
        
    }

    return html;
}
class LinkedCorrespondence extends Intalio.Model
{
    constructor()
    {
        super();
        this.transferId = null;
        this.documentId = null;
        this.statuses = [];
        this.categories = [];
        this.delegationId = null;
        this.readOnly = null;
    }
}
class LinkedCorrespondenceView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "linkedcorrespondence", model);
    }
    render()
    {
        

        var self = this;
        $(self.refs['btnNewLinkedDocument']).hide();

        if (this.model.actionName != undefined) {
            var actionArray = this.model.actionName.split("_");
            if ((actionArray.includes("LinkedDocument.New"))) {
                if (!this.model.readOnly) {
                    $(self.refs['btnNewLinkedDocument']).show();
                }
            }
        }
        $.fn.select2.defaults.set("theme", "bootstrap");
        var initialLoad = true;
        Common.gridCommon();
        let table = $(self.refs['grdLinkedCorrespondences']).on('draw.dt',
            function ()
            {
                $(self.refs['grdLinkedCorrespondences']).find('tbody tr td').each(function (index)
                {
                    if ($(this).hasClass('sortableDateInDatatable'))
                    {
                        if ($(this)[0].lastElementChild)
                        {
                            this.setAttribute('title', $(this)[0].lastElementChild.textContent);
                        }
                    } else
                    {
                        this.setAttribute('title', $(this).text());
                    }
                });
            }).DataTable({
                processing: false,
                ordering: true,
                serverSide: false,
                pageLength: 10,
                "ajax": {
                    "url": "/LinkedDocument/List",
                    "type": "GET",
                    "datatype": "json",
                    data: function (d)
                    {
                        d.documentId = self.model.documentId;
                        d.transferId = self.model.transferId;
                        d.delegationId = self.model.delegationId;
                        return d;
                    }
                },
                "order": [],
                "columns": [
                    { title: "Id", data: "id", visible: false, "orderable": false },
                    {
                        className: 'details-control',
                        orderable: false,
                        data: null,
                        defaultContent: '',
                        width: '16px'
                    },
                    {
                        title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"],
                        render: function (data, type, full, meta)
                        {
                            var categories = self.model.categories;
                            for (var i = 0; i < categories.length; i++)
                            {
                                if (categories[i].id === data)
                                {
                                    return categories[i].text;
                                }
                            }
                            return "";
                        }
                    },
                    { title: Resources.ReferenceNumber, data: "linkedDocumentReferenceNumber", "orderable": true, orderSequence: ["asc", "desc"], width: "120px" },
                    { title: Resources.AddedBy, data: "linkedBy", "orderable": true, orderSequence: ["asc", "desc"], width: "100px", visible: !self.model.isFollowUp },
                    {
                        title: Resources.CreatedDate, 'render': function (data, type, full, meta)
                        {
                            return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
                        }, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px", visible: !self.model.isFollowUp
                    },
                
                    {
                        "autoWidth": false,
                        "bAutoWidth": false,
                        'orderable': false,
                        'sortable': false,
                        width: "16px",
                        'render': function (data, type, row)
                        {
                            var html = "";

                            let btn = document.createElement("button");
                            btn.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                            btn.setAttribute("title", Resources.View);
                            btn.setAttribute("type", "button");
                            btn.setAttribute("clickAttr", "openLinkedDocument(" + row.linkedDocumentId + "," + self.model.delegationId + "," + self.model.documentId + "," + self.model.fromManageCorrespondance + ")");
                            btn.innerHTML = "<i class='fa fa-eye fa-white'/>";
                            html += btn.outerHTML;
                            if (self.model.readOnly !== true && row.allowDelete)
                            {
                                let btn = document.createElement("button");
                                btn.setAttribute("class", "btn btn-xs btn-danger edit");

                                var actionArray;
                                var createLinkedCorrespondenceActionBtns = false;

                                if (self.model.actionName != undefined) {
                                    actionArray = self.model.actionName.split("_");
                                    if ((actionArray.includes("LinkedDocument.Delete"))) {
                                        createLinkedCorrespondenceActionBtns = true;
                                    }
                                }
                                if (self.model.isFollowUp == true)
                                    createLinkedCorrespondenceActionBtns = true;
                                if (createLinkedCorrespondenceActionBtns == true) {
                                    btn.setAttribute("title", Resources.Delete);
                                    btn.setAttribute("clickAttr", "deleteLink(" + row.id + "," + self.model.documentId + ", " + self.model.transferId + ")");
                                    btn.innerHTML = "<i class='fa fa-trash'/>";
                                    html += btn.outerHTML;
                                }
                            }
                            return "<div style='display: inline-flex;'>" + html + "</div>";
                        }
                    }
                ],
                "fnInitComplete": function (settings, json)
                {
                    $('[data-toggle="tooltip"]').tooltip();

                    if (initialLoad) {
                        initialLoad = false;
                        EventReceiver.LinkedCorrespondencesAfterRender(self);
                    }
                },
                "createdRow": function (row, data, dataIndex)
                {
                    var color = data.isDirectLink ? '#656565' : '#ffbf00';
                    $(row).attr('style', "color:" + color);
                },
                dom: 'trpi',
                buttons: []
            });
        $(self.refs['grdLinkedCorrespondences']).on('click', 'tr', function ()
        {
            var input = this.getElementsByTagName('input')[0];
            if (typeof input !== "undefined")
                input.checked = input.checked ? false : true;
        });
        $(self.refs['grdLinkedCorrespondences']).on('dblclick', 'tr', function (e)
        {
            var onclick = $(this).find(".view").attr("clickattr");
            eval(onclick);
        });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        $('#chkAll').change(function ()
        {
            $('tbody tr td input[type="checkbox"]').prop('checked', $(this).prop('checked'));
        });
        $(self.refs['grdLinkedCorrespondences']).on('click', ".edit", function ()
        {
            var onclick = $(this).attr("clickattr").slice(0, -1) + ",self)";
            eval(onclick);
        });
        $(self.refs['grdLinkedCorrespondences']).on('click', ".view", function ()
        {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });

        $(self.refs['grdLinkedCorrespondences']).on('click', 'td.details-control', function (event) {
            event.stopPropagation();
            var tr = $(this).closest('tr');
            var row = $(self.refs['grdLinkedCorrespondences']).DataTable().row(tr);

            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            } else {
                row.child(format(row.data())).show();
                tr.addClass('shown');
            }
        });

        if (!self.model.readOnly)
        {
            $(self.refs['btnNewLinkedDocument']).click(function ()
            {
                openLinkWindow(self);
            });
        } 
    }
}
export default { LinkedCorrespondence, LinkedCorrespondenceView };
