﻿IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'InvalidSubject')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'InvalidSubject', 
            N'Please provide a subject.', 
            N'Veuillez saisir un objet.', 
            N'يرجى إدخال الموضوع.', 
            1)
END
---------------------------------------------------------------------------------
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'FailedToGetCertificate')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'FailedToGetCertificate', 
            N'Failed to get certificate.', 
            N'Échec de l''obtention du certificat.', 
            N'فشل في الحصول على الشهادة.', 
            1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'NoCertificate')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'NoCertificate', 
            N'No certificate found.', 
            N'Aucun certificat trouvé.', 
            N'لم يتم العثور على شهادة.', 
            1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'CertificateExpired')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'CertificateExpired', 
            N'The certificate has expired.', 
            N'Le certificat a expiré.', 
            N'انتهت صلاحية الشهادة.', 
            1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'CertificateNotActivated')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'CertificateNotActivated', 
            N'The certificate is not activated.', 
            N'Le certificat n''est pas activé.', 
            N'الشهادة غير مفعّلة.', 
            1)
END
-----------------------------------------------------------------------------------------------
UPDATE [ISFCTS].[dbo].[Category]
SET 
    [NameAr] = N'تَعْمِيم',      
    [ModifiedDate] = GETDATE()
WHERE 
    [Name] = 'circular';
------------------------------------------
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'IsSigned')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'IsSigned', 
            N'Is Signed', 
            N'Est signé', 
            N'تم التوقيع', 
            1)
END
----------------------------------------------------------------------------------------------
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'reportInbox')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('reportInbox', N'Report Inbox', N'تقرير البريد الوارد', N'boîte de réception des rapports', 1)
END
-------------------------------------------------------------------------------------------------
-- No
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'No')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (
        N'No',
        N'No',
        N'Non',
        N'كلا',
        1
    )
END

-- Yes
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'Yes')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (
        N'Yes',
        N'Yes',
        N'Oui',
        N'نعم',
        1
    )
END
-----------------------------------------------------------------------------------
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'CannotBeRecalled')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'CannotBeRecalled', 
            N'Cannot be recalled', 
            N'Ne peut pas être rappelé', 
            N'لا يمكن الاسترجاع', 
            1)
END
--------------------------------------------------------------------------------
IF NOT EXISTS (
    SELECT 1 FROM [dbo].[TranslatorDictionary] WHERE Keyword = N'FavoriteStructureAddedSuccessfully'
)
BEGIN
    INSERT INTO [dbo].[TranslatorDictionary] (Keyword, EN, FR, AR, IsSystem)
    VALUES (
        N'FavoriteStructureAddedSuccessfully',N'Favorite Structure Added Successfully', N'Structure favorite ajoutée avec succès',    
        N'تمت إضافة الهيكل المفضل بنجاح',         
        0                             
    );
END;

IF NOT EXISTS (
    SELECT 1 FROM [dbo].[TranslatorDictionary] WHERE Keyword = N'UnexpectedErrorOccurred'
)
BEGIN
    INSERT INTO [dbo].[TranslatorDictionary] (Keyword, EN, FR, AR, IsSystem)
    VALUES (
        N'UnexpectedErrorOccurred',N'Unexpected Error Occurred', N'Unexpected Error Occurred',    
        N'حدث خطأ غير متوقع',         
        0                             
    );
END;
------------------------------------------------------------------------------------------------
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'OrderAlreadyExists')
Begin
INSERT INTO TranslatorDictionary
([Keyword],[EN],[FR],[AR],[IsSystem]) VALUES (N'OrderAlreadyExists', N'Order Already Exists',N'Ordre sportif Xistes',N'الترتيب موجود بالفعل',1)
END
------------------------------------------------------------------------------------------
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'SentReport')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('SentReport', N'Sent Report', N'تقرير البريد المرسل', N'Rapports envoyés', 1)
END
 
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'DraftReport')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('DraftReport', N'Draft Report', N'تقرير المسودة', N'Projet de rapport', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'MyRequestReport')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('MyRequestReport', N'My Request Report', N'طلباتي', N'MaRequêteRapport', 1)
END
 
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'CompletedReport')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('CompletedReport', N'Completed Report', N'تقرير البريد المكتمل', N'Rapport terminé', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'ClosedReport')

BEGIN

	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)

	VALUES ('ClosedReport', N'Closed Report', N'تقرير البريد المغلق', N'Rapport fermé', 1)

END
 --------------------------------------------------------------------------------
IF NOT EXISTS (SELECT 1 FROM Parameter WHERE Keyword=N'RCVersion')
BEGIN
	INSERT INTO Parameter (Keyword,[Description], Content, IsSystem)
	VALUES (N'RCVersion','RC version of CTS', 'RC28', 0)
END

IF EXISTS (SELECT 1 FROM Parameter WHERE Keyword=N'RCVersion')
BEGIN
update Parameter set Content='RC28' where Keyword='RCVersion'

END