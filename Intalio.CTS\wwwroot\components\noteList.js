﻿import Intalio from './common.js'
import NoteIndex from './noteIndex.js'
var gIsLocked = false;
function drawTable(model, self) {

    var initialLoad = true;
    var table = $(self.refs["grdNoteItems"]);
    return $(self.refs["grdNoteItems"]).on('draw.dt', function () {

        $(self.refs['grdNoteItems']).find('tbody tr td').each(function (index) {
            if ($(this).hasClass('sortableDateInDatatable')) {
                if ($(this)[0].lastElementChild) {
                    this.setAttribute('title', $(this)[0].lastElementChild.textContent);
                }
            } else {
                this.setAttribute('title', $(this).text());
            }
        });
    }).DataTable({
        processing: true,
        ordering: true,
        serverSide: true,
        pageLength: 10,
        "ajax": {
            "url": "/Note/List",
            "type": "GET",
            "datatype": "json",
            data: function (d) {
                d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                d.TransferId = model.transferId;
                d.DocumentId = model.documentId;
                d.delegationId = model.delegationId;
                return d;
            },

        },
        "order": [],
        "columns": [
            {
                "className": 'details-control width20',
                "orderable": false,
                "data": null,
                "defaultContent": '',
                width: '16px'
            },
            { title: "Id", data: "id", visible: false, "orderable": false },
            {
                title: Resources.CreatedBy, "orderable": false, orderSequence: ["desc", "asc"], "autoWidth": false, "className": "min-max-width-50-250",
                render: function (data, type, full, meta) {
                    return full.isCreatedByDelegator ? full.createdBy + " " + Resources.OnBehalfOf + " " + full.createdByDelegatedUser : full.createdBy
                }
            },
            {
                title: Resources.CreatedDate, data: "createdDate",
                render: function (data, type, full, meta) {
                    return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
                }, "orderable": false, orderSequence: ["desc", "asc"], "autoWidth": false, "className": "min-max-width-50-250"
            },

            {
                "className": "text-right width20",
                "autoWidth": false,
                "bAutoWidth": false,
                "width": "16px",
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta) {
                    var lblType = '';
                    if (full.isPrivate) {
                        lblType = '<div style="display: inline-flex;"><div class="mr-sm" title="' + Resources.Private + '"><i class="fa fa-eye-slash fa-lg text-warning"></i></div></div>';
                    }
                    return lblType;
                }
            },
            {
                "className": "text-right width20 minwidth55",
                "autoWidth": false,
                "bAutoWidth": false,
                "width": "16px",
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta) {
                    var html = "";
                    if (model.readOnly !== true && full.isEditable) {
                        let btn = document.createElement("button");
                        btn.setAttribute("class", "btn btn-xs mr-sm btn-warning edit");
                        btn.setAttribute("title", Resources.Edit);
                        btn.setAttribute("clickAttr", "openNoteWindow(this)");
                        btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                        html += btn.outerHTML;
                        if (model.actionName != undefined) {
                            var actionArray = model.actionName.split("_");
                            if ((actionArray.includes("Note.Delete"))) {
                                btn = document.createElement("button");
                                btn.setAttribute("class", "btn btn-xs btn-danger delete");
                                btn.setAttribute("title", Resources.Delete);
                                btn.setAttribute("clickAttr", "deleteNote(" + full.id + "," + model.documentId + "," + model.transferId + ")");
                                btn.innerHTML = "<i class='fa fa-trash fa-white'/>";
                                html += btn.outerHTML;
                            }

                        }

                    }
                    return html;
                }
            }
        ],
        "fnInitComplete": function (settings, json) {
            $('[data-toggle="tooltip"]').tooltip();

            if (initialLoad) {
                initialLoad = false;
                EventReceiver.NotesAfterRender(self);
            }
        },
        dom: '<"html5buttons"B>trpi',
        buttons: [
            {

                className: 'btn-sm btn-primary ',
                text: "Excel",
                action: function (e, dt, node, config) {

                    gAction = "excel";


                    exportTable(model);
                },
                init: function (api, node, config) {

                }
            },
            {
                className: 'btn-sm btn-primary btn-default btnNewNote',
                text: `<span class="fa fa-plus-circle mr-sm"></span><span>${Resources.New}</span>`
            }
        ]

    });



}
function exportTable(model) {
    if (gfirstTime || clear || gOverdue) {
        if ($.fn.dataTable.isDataTable("#grdNotes")) {
            if (clear) {
                clear = false;
            }
            $("#grdNotesHidden").DataTable().clear().destroy();
        }
        $("#grdNotesHidden").on('draw.dt',
            function () {

                $('#grdNote tbody tr td').each(function () {
                    this.setAttribute('title', $(this).text());
                });

                if (gAction === "excel") {
                    $("#grdNotesHidden_wrapper .buttons-excel").click();
                }
                gAction = "";
                gfirstTime = false;
            }).DataTable({
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/Note/List",
                    "type": "GEt",
                    "datatype": "json",
                    data: function (d) {
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.TransferId = model.transferId;
                        d.DocumentId = model.documentId;
                        d.delegationId = model.delegationId;
                        return d;
                    }
                },



                "order": [],

                "columns": [
                    {
                        "className": 'details-control width20',
                        "orderable": false,
                        "data": null,
                        "defaultContent": '',
                        width: '16px'
                    },
                    {
                        title: Resources.CreatedBy, "orderable": false, orderSequence: ["desc", "asc"], "autoWidth": false, "className": "min-max-width-50-250",
                        render: function (data, type, full, meta) {
                            return full.isCreatedByDelegator ? full.createdBy + " " + Resources.OnBehalfOf + " " + full.createdByDelegatedUser : full.createdBy
                        }
                    },
                    {
                        title: Resources.CreatedDate, data: "createdDate",
                        render: function (data, type, full, meta) {

                            return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
                        }, "orderable": false, orderSequence: ["desc", "asc"], "autoWidth": false, "className": "min-max-width-50-250"
                    },
                    {
                        title: Resources.Notes, data: "notes",
                        render: function (data, type, full, meta) {
                            return full.notes ? full.notes : "";
                        }

                    },

                ],


                "fnInitComplete": function (settings, json) {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons"B>trpi',

                buttons: [
                    {
                        extend: 'excelHtml5',
                        title: function () {
                            return Resources.reportsnotesfollowup;
                        },
                        filename: function () {
                            var currentDate = new Date();

                            var formattedDate = currentDate.toISOString().split('T')[0];

                            var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                            return Resources.reportsnotesfollowup + '_' + formattedDate + '_' + formattedTime;
                        },
                        exportOptions: {
                            columns: [0, 1, 2, 3]
                        },
                        customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportsnotesfollowup.excelHTML5
                    }
                ]

            });

    } else {
        if (gDataChanged) {
            $("#grdNotesHidden").DataTable().ajax.reload();
        } else {
            if (gAction === "excel") {
                $("#grdNotesHidden_wrapper .buttons-excel").click();
            }
        }
    }



    gDataChanged = false;
}

function buildColumns() {
    var gridcolumns = [];


    gridcolumns.push({
        title: Resources.CreatedBy,
        data: "CreatedBy",
        "orderable": false,
        orderSequence: ["desc", "asc"],
        "autoWidth": false,
        "className": "min-max-width-50-250",
        render: function (data, type, full, meta) {

            return full.isCreatedByDelegator
                ? full.createdBy + " " + Resources.OnBehalfOf + " " + full.createdByDelegatedUser
                : full.createdBy;
        }
    });
    gridcolumns.push({

        title: Resources.CreatedDate, data: "createdDate",
        render: function (data, type, full, meta) {
            return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
        }, "orderable": false, orderSequence: ["desc", "asc"], "autoWidth": false, "className": "min-max-width-50-250"

    })


    return gridcolumns;
}




function openNoteWindow(row, self) {
    if (!self.model.readOnly) {
        var wrapper = $(".modal-window");
        var modelIndex = new NoteIndex.NoteIndex();
        modelIndex.transferId = self.model.transferId;
        modelIndex.documentId = self.model.documentId;
        modelIndex.delegationId = self.model.delegationId;
        var noteIndex = new NoteIndex.NoteIndexView(wrapper, modelIndex);
        noteIndex.render();

        let tr = $(row).closest('tr');
        let srow = $(self.refs['grdNoteItems']).DataTable().row(tr);
        var note = srow.data();
        if (note) {
            $(noteIndex.refs['modalNoteTitle']).html(Resources.Edit);
            noteIndex.setData(note);
            noteIndex.model.noteId = note.id;
        } else {
            $(noteIndex.refs['modalNoteTitle']).html(Resources.New);
        }
        $(noteIndex.refs['modalNote']).modal("show");
        $(noteIndex.refs['modalNote']).off("hidden.bs.modal");
        $(noteIndex.refs['modalNote']).off("shown.bs.modal");
        $(noteIndex.refs['modalNote']).on('shown.bs.modal', function () {
            CKEDITOR.instances[noteIndex.model.ComponentId + '_txtAreaNote'].focus();
        });
        $(noteIndex.refs['modalNote']).on('hidden.bs.modal', function () {
            noteIndex.model.noteId = '';
            if (typeof CKEDITOR.instances[noteIndex.model.ComponentId + '_txtAreaNote'] !== 'undefined') {
                CKEDITOR.instances[noteIndex.model.ComponentId + '_txtAreaNote'].destroy(true);
            }
            $(noteIndex.refs['formIndexPost']).parsley().reset();
            $(noteIndex.refs['userPostMessage']).html('');
            $(noteIndex.refs[noteIndex.model.ComponentId]).remove();
            swal.close();
        });
    }
}
function deleteNote(id, documentId, transferId, self) {
    try {
        Common.showConfirmMsg(Resources.DeleteNoteConfirmation, function () {
            if (gIsLocked === false) {
                gIsLocked = true;
                Common.ajaxDelete('/Note/Delete', { 'id': id, documentId: documentId, transferId: transferId, delegationId: self.model.delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val() }, function (data) {
                    gIsLocked = false;
                    if (!data) {
                        setTimeout(function () {
                            Common.alertMsg(Resources.NoPermission);
                        }, 300);
                    } else {
                        $(self.refs['grdNoteItems']).DataTable().ajax.reload();
                        GridCommon.Refresh("grdFollowUpItems");
                        TreeNode.refreshTreeNodeCounts(TreeNode.FollowUp);
                    }

                }, function () { gIsLocked = false; }, false);
            }
        });
    } catch (ex) {
        gIsLocked = false;
    }

}
function format(row) {
    return '<table style="width:100%" cellspacing="0" border="0">' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Note + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().notes || '') + '</td>' +
        '</tr>' +
        '</table>';
}



class Note extends Intalio.Model {
    constructor() {
        super();
        this.transferId = null;
        this.documentId = null;
        this.readOnly = null;
        this.delegationId = null;
        this.isFollowUpExcel;
        this.nodeId = null;

    }
}

var gfirstTime = true;
var gDataChanged = true;
var gAction = "";
var gFilterChanged = false;
var gFilterStructure = "";
var gFilterFromDate = "";
var gFilterToDate = "";
var gFilterUser = "";
var gFilterOverdue = "";
var gOverdue = false;
var clear = false;
var gReportName = "Reports Notes Followup";

class NoteView extends Intalio.View {
    constructor(element, model) {
        super(element, "note", model);
    }
    render() {

        var self = this;
        gfirstTime = true;
        gDataChanged = true;
        gAction = "";
        gFilterChanged = false;
        gFilterStructure = "";
        gFilterFromDate = "";
        gFilterToDate = "";
        gFilterUser = "";
        gFilterOverdue = "";
        gOverdue = false;
        clear = false;
        var _isFollowUpNode = false;


        gReportName = "Transfers InProgress Report";
        $.fn.select2.defaults.set("theme", "bootstrap");
        var clickedSearch = false;
        const nodes = new CoreComponents.Lookup.Nodes().get();

        _isFollowUpNode = isFollowUpNode(self.model.nodeId);



        var self = this;
        var model = this.model;

        //if (window.location.hash.includes("#MyRequests/10")) {
        //    ;
        //    this.model.nodeId = 10;
        //    model.isFollowUpExcel = true;

        //    if (model.isFollowUpE`xcel) {
        //        ;
        //        const exportButton = document.getElementById("btnExcelAllNotesDetails");

        //        exportButton.addEventListener("click", function () {
        //            // Call the exportTable function when the button is clicked
        //            exportTable();
        //        });
        //    }

        //}

        //const exportButton = document.getElementById("btnExcelAllNotesDetails");

        //if (exportButton) {
        //    exportButton.addEventListener("click", function () {
        //        exportTable();
        //    });
        //} else {
        //    console.warn("Export button not found in the DOM.");
        //}
        //if (window.location.hash.includes("#MyRequests/10")) {
        //    ;
        //    this.model.nodeId = 10;
        //    model.isFollowUpExcel = true;

        //    //if (model.isFollowUpExcel) {
        //        ;

        //        const exportButton = document.getElementById("btnExcelAllNotesDetails");

        //        if (exportButton) {
        //            exportButton.addEventListener("click", function () {
        //                exportTable();
        //            });
        //        } else {
        //            console.warn("Export button not found in the DOM.");
        //        }
        ////    }
        //}



        //$(self.refs['btnNewNote']).hide();
        //if (this.model.actionName != undefined) {
        //    var actionArray = this.model.actionName.split("_");
        //    if ((actionArray.includes("Note.New"))) {
        //        $(self.refs['btnNewNote']).show();
        //    }

        //}

        $(self.refs['btnNewNote']).hide();

        setTimeout(() => {
            $('.btnNewNote').hide();
            if (this.model.actionName != undefined) {
                var actionArray = this.model.actionName.split("_");
                if ((actionArray.includes("Note.New"))) {
                    if (!model.readOnly) {
                        $('.btnNewNote').show();
                    }
                }
            }
        }, 1);


        $.fn.select2.defaults.set("theme", "bootstrap");
        Common.gridCommon();
        let table = drawTable(model, self);
        $(self.refs['grdNoteItems']).on('click', 'tr', function () {
            var input = this.getElementsByTagName('input')[0];
            if (typeof input !== "undefined")
                input.checked = input.checked ? false : true;
        });
        $(self.refs['grdNoteItems']).on('click', ".edit", function () {
            var onclick = $(this).attr("clickattr").slice(0, -1) + ",self)";
            eval(onclick);
        });



        $(self.refs['grdNoteItems']).on('click', ".delete", function () {
            var onclick = $(this).attr("clickattr").slice(0, -1) + ",self)";
            eval(onclick);
        });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        $(self.refs['grdNoteItems']).on('click', 'td.details-control', function () {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            }
            else {
                row.child(format(row)).show();
                tr.addClass('shown');
            }
        });

        if (!model.readOnly) {
            //$(self.refs['btnNewNote']).click(function ()
            //{
            //    openNoteWindow(null, self);
            //});
            $('.btnNewNote').click(function () {
                openNoteWindow(null, self);
            });
        }






    }
}
export default { Note, NoteView };
