﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Aspose.Pdf.Operators;
using Intalio.Core.Interfaces;
using Microsoft.EntityFrameworkCore;


namespace Intalio.CTS.Core.DAL
{
    public partial class Event : IDbObject<Event>
    {
        #region Ctor
        public Event()
        {

        }
        #endregion

        #region Private Fields

        private CTSContext _ctx;

        #endregion

        #region Properties
        public long Id { get; set; }
        public string Name { get; set; }
        public string Location { get; set; }
        public string Description { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public long CreatedByUserId { get; set; }
        public DateTime CreatedDate { get; set; }
        public long DocumentId { get; set; }
        public long? TransferId { get; set; }
 

        public virtual Transfer Transfer { get; set; }

        public virtual Document Document { get; set; }
        public virtual User CreatedByUser { get; set; }

        #endregion
        #region Private Methods

        private void OpenDbContext()
        {
            if (_ctx == null)
            {
                _ctx = new CTSContext();
            }
        }
        #endregion
        #region Public Methods
        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                this.CreatedDate = DateTime.Now;
                ctx.Event.Add(this);
                ctx.SaveChanges();
            }
        }


        public Event Find(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Event.AsNoTracking().FirstOrDefault(x => x.Id == id);
            }
        }

   
        public void Update()
        {
            using (var ctx = new CTSContext())
            {
                ctx.Entry(this).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                ctx.SaveChanges();
            }
        }


        public void Delete(long id)
        {
            using (var ctx = new CTSContext())
            {
                Event item = new Event { Id = id };
                ctx.Event.Attach(item);
                ctx.Event.Remove(item);
                ctx.SaveChanges();
            }
        }
        public Task<int> GetCount(long documentId, long userId)
        {
            try
            {
                OpenDbContext();
                IQueryable<Event> query = _ctx.Event.Where(x => x.DocumentId == documentId &&
                ((x.CreatedByUserId == userId))).AsNoTracking();

                return Task.FromResult(query.Count());
            }

            catch (Exception ex)
            {
                
                Console.WriteLine($"Error in GetCount: {ex.Message}");
                throw;  
            }
        }
        public async Task<List<Event>> ListAsync(int startIndex, int pageSize, long documentId, long userId)
        {
            try
            {
                using (var ctx = new CTSContext())
                {
                    IQueryable<Event> query = ctx.Event.Include(x => x.CreatedByUser).Include(i => i.Document)
                        .Where(x => x.DocumentId == documentId);

                    return await query.Skip(startIndex).Take(pageSize).ToListAsync();

                }
            }
            catch (Exception ex)
            {
           
                Console.WriteLine($"Error in ListAsync: {ex.Message}");
                throw;  
            }
        }      
        public List<Event> GetAll()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Event.ToList();
            }
        }


        public void Delete()
        {
            Delete(Id);
        }

      

        #endregion
    }
}
