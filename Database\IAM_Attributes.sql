Declare @StructureRoleId int;
Declare @AllowEditSigned int;
Declar<PERSON> @EnableNotification int;
Declar<PERSON> @EditDesignatedPerson int;
Declare @StructureIsDepartmentId int;
Declare @EnablePersonalTemplates int;
Declare @EnableStructureTemplates int;
Declare @SecureUserIdAttributeId int;

Declare @StructureRoleIdUserAttribute int;
Declare @AllowEditSignedUserAttribute int;
Declare @EnableNotificationUserAttribute int;
Declare @EditDesignatedPersonUserAttribute int;
Declare @StructureIsDepartmentIdStructureAttribute int;
Declare @EnablePersonalTemplatesUserAttribute int;
Declare @EnableStructureTemplatesUserAttribute int;
Declare @SecureUserIdAttributeIdUserAttribute int;

Declare @ctsAppId bigint
set @ctsAppId=(select Id from Application where name='CTS'); -- be sure of cts application name in you IAM database

IF NOT EXISTS (SELECT 1 FROM [Attribute] WHERE [Name] = N'Roles')
Begin
INSERT INTO [dbo].[Attribute]
           ([Name], [AttributeTypeId], [UniqueConstraint], [Mandatory], [ListValue], [CreatedByUserId], [CreatedDate], [Hidden])
     VALUES ('Roles', 10, 0, 1, '[{"Id": "1","Text": "Administrator"},{"Id": "2","Text": "Full Control"},{"Id": "3","Text": "Contribute"},{"Id": "4","Text": "Read"}]', 1, GetDate(), 0)
select @StructureRoleId = @@IDENTITY

INSERT INTO [dbo].[UserAttribute]([AttributeId], [ByStructure], [IsProfile], [CreatedByUserId], [CreatedDate])
     VALUES (@StructureRoleId, 1, 0, 1, GetDate())
select @StructureRoleIdUserAttribute = @@IDENTITY

INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @StructureRoleIdUserAttribute, 'StructureRole', 1, 1, GETDATE())

INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @StructureRoleIdUserAttribute, 'StructureRole', 2, 1, GETDATE())

INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @StructureRoleIdUserAttribute, 'StructureRole', 3, 1, GETDATE())
END


IF NOT EXISTS (SELECT 1 FROM [Attribute] WHERE [Name] = N'AllowEditSigned')
Begin
INSERT INTO [dbo].[Attribute]
           ([Name], [AttributeTypeId], [UniqueConstraint], [Mandatory], [CreatedByUserId], [CreatedDate], [Hidden])
     VALUES ('AllowEditSigned', 9, 0, 0,  1, GetDate(), 0)
 

select @AllowEditSigned = @@IDENTITY

INSERT INTO [dbo].[UserAttribute]
           ([AttributeId], [ByStructure], [IsProfile], [CreatedByUserId], [CreatedDate])
     VALUES (@AllowEditSigned, 1, 0, 1, GetDate())
select @AllowEditSignedUserAttribute = @@IDENTITY


INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @AllowEditSignedUserAttribute, 'AllowEditSigned', 1, 1, GETDATE())

INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @AllowEditSignedUserAttribute, 'AllowEditSigned', 2, 1, GETDATE())

INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @AllowEditSignedUserAttribute, 'AllowEditSigned', 3, 1, GETDATE())
END


IF NOT EXISTS (SELECT 1 FROM [Attribute] WHERE [Name] = N'EnableNotifications')
Begin
INSERT INTO [dbo].[Attribute]
           ([Name], [AttributeTypeId], [UniqueConstraint], [Mandatory], [CreatedByUserId], [CreatedDate], [Hidden])
     VALUES ('EnableNotifications', 9, 0, 0,  1, GetDate(), 0)
select @EnableNotification = @@IDENTITY


INSERT INTO [dbo].[UserAttribute]
           ([AttributeId], [ByStructure], [IsProfile], [CreatedByUserId], [CreatedDate])
     VALUES (@EnableNotification, 0, 0, 1, GetDate())
select @EnableNotificationUserAttribute = @@IDENTITY


INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EnableNotificationUserAttribute, 'EnableNotifications', 1, 1, GETDATE())

INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EnableNotificationUserAttribute, 'EnableNotifications', 2, 1, GETDATE())

INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EnableNotificationUserAttribute, 'EnableNotifications', 3, 1, GETDATE())
END

IF NOT EXISTS (SELECT 1 FROM [Attribute] WHERE [Name] = N'EditDesignatedPerson')
Begin
INSERT INTO [dbo].[Attribute]
           ([Name], [AttributeTypeId], [UniqueConstraint], [Mandatory], [CreatedByUserId], [CreatedDate], [Hidden])
     VALUES ('EditDesignatedPerson', 9, 0, 0,  1, GetDate(), 0)
select @EditDesignatedPerson = @@IDENTITY


INSERT INTO [dbo].[UserAttribute]
           ([AttributeId], [ByStructure], [IsProfile], [CreatedByUserId], [CreatedDate])
     VALUES (@EditDesignatedPerson, 1, 0, 1, GetDate())
select @EditDesignatedPersonUserAttribute = @@IDENTITY


INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EditDesignatedPersonUserAttribute, 'EditDesignatedPerson', 1, 1, GETDATE())
	 
INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EditDesignatedPersonUserAttribute, 'EditDesignatedPerson', 2, 1, GETDATE())

INSERT INTO [dbo].[ApplicationUserAttributeMapping]
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EditDesignatedPersonUserAttribute, 'EditDesignatedPerson', 3, 1, GETDATE())

END


update UserAttribute
Set ByStructure = 1
where UserAttribute.AttributeId in (1, 2, 3)


IF NOT EXISTS (SELECT 1 FROM [Attribute] WHERE [Name] = N'IsDepartment')
Begin
INSERT INTO [dbo].[Attribute] ([Name], [AttributeTypeId], [UniqueConstraint], [Mandatory], [CreatedByUserId], [CreatedDate], [Hidden]) 
VALUES ('IsDepartment', 9, 0, 0, 1, GetDate(), 0)
select @StructureIsDepartmentId = @@IDENTITY

INSERT INTO [dbo].[StructureAttribute]([AttributeId], [CreatedByUserId], [CreatedDate]) VALUES (@StructureIsDepartmentId, 1, GetDate())

select @StructureIsDepartmentIdStructureAttribute = @@IDENTITY

INSERT INTO [dbo].[ApplicationStructureAttributeMapping] 
([ApplicationId], [StructureAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate]) VALUES (@ctsAppId, @StructureIsDepartmentIdStructureAttribute, 'IsDepartment', 1, 1, GETDATE())

INSERT INTO [dbo].[ApplicationStructureAttributeMapping] 
([ApplicationId], [StructureAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate]) VALUES (@ctsAppId, @StructureIsDepartmentIdStructureAttribute, 'IsDepartment', 2, 1, GETDATE())

INSERT INTO [dbo].[ApplicationStructureAttributeMapping] 
([ApplicationId], [StructureAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate]) VALUES (@ctsAppId, @StructureIsDepartmentIdStructureAttribute, 'IsDepartment', 3, 1, GETDATE())

END

IF NOT EXISTS (SELECT 1 FROM [Attribute] WHERE [Name] = N'EnablePersonalTemplates')
Begin
INSERT INTO [dbo].[Attribute]
           ([Name], [AttributeTypeId], [UniqueConstraint], [Mandatory], [CreatedByUserId], [CreatedDate], [Hidden]) 
		   VALUES ('EnablePersonalTemplates', 9, 0, 0,  1, GetDate(), 0)
select @EnablePersonalTemplates = @@IDENTITY

INSERT INTO [dbo].[UserAttribute]
           ([AttributeId], [ByStructure], [IsProfile], [CreatedByUserId], [CreatedDate])
     VALUES (@EnablePersonalTemplates, 0, 0, 1, GetDate())
select @EnablePersonalTemplatesUserAttribute = @@IDENTITY


INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EnablePersonalTemplatesUserAttribute, 'EnablePersonalTemplates', 1, 1, GETDATE())
	 
INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EnablePersonalTemplatesUserAttribute, 'EnablePersonalTemplates', 2, 1, GETDATE())

INSERT INTO [dbo].[ApplicationUserAttributeMapping]
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EnablePersonalTemplatesUserAttribute, 'EnablePersonalTemplates', 3, 1, GETDATE())
END

IF NOT EXISTS (SELECT 1 FROM [Attribute] WHERE [Name] = N'EnableStructureTemplates')
Begin
	 INSERT INTO [dbo].[Attribute]
           ([Name], [AttributeTypeId], [UniqueConstraint], [Mandatory], [CreatedByUserId], [CreatedDate], [Hidden]) 
		   VALUES ('EnableStructureTemplates', 9, 0, 0,  1, GetDate(), 0)


select @EnableStructureTemplates = @@IDENTITY

INSERT INTO [dbo].[UserAttribute]
           ([AttributeId], [ByStructure], [IsProfile], [CreatedByUserId], [CreatedDate])
     VALUES (@EnableStructureTemplates, 1, 0, 1, GetDate())
select @EnableStructureTemplatesUserAttribute = @@IDENTITY

INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EnableStructureTemplatesUserAttribute, 'EnableStructureTemplates', 1, 1, GETDATE())
	 
INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EnableStructureTemplatesUserAttribute, 'EnableStructureTemplates', 2, 1, GETDATE())

INSERT INTO [dbo].[ApplicationUserAttributeMapping]
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
     VALUES (@ctsAppId, @EnableStructureTemplatesUserAttribute, 'EnableStructureTemplates', 3, 1, GETDATE())

END

IF NOT EXISTS (SELECT 1 FROM [Attribute] WHERE [Name] = N'SecureUserId')
Begin
INSERT INTO [Attribute] ( [Name],  AttributeTypeId,   Min,   Max,    UniqueConstraint,    Mandatory,    ListValue,    [Group],    CreatedByUserId,  CreatedDate,   ModifiedDate,   Hidden
)
VALUES (
    'SecureUserId',   1,    NULL,  NULL,  1,  0,   NULL,  NULL,   1,   '2025-03-05 13:14:49.2170680',   NULL,    0
);
select @SecureUserIdAttributeId = @@IDENTITY

INSERT INTO [UserAttribute]
(AttributeId,[ByStructure], IsProfile, CreatedByUserId, CreatedDate)
VALUES(@SecureUserIdAttributeId,0, 1, 1, '2025-03-05 13:15:03.0458836');

select @SecureUserIdAttributeIdUserAttribute = @@IDENTITY

INSERT INTO UserAttributeValue( UserId,  AttributeId, Value)(SELECT  Id,   @SecureUserIdAttributeId, NULL FROM [User]);

INSERT INTO [ApplicationUserAttributeMapping]
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
VALUES(@ctsAppId, @SecureUserIdAttributeIdUserAttribute, 'SecureUserId', 1, 1, '2025-03-06 09:42:29.9668210')

INSERT INTO [ApplicationUserAttributeMapping]
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
Values (@ctsAppId, @SecureUserIdAttributeIdUserAttribute, 'SecureUserId', 2, 1, '2025-03-06 09:42:29.9668210')

INSERT INTO [ApplicationUserAttributeMapping]
([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
Values (@ctsAppId, @SecureUserIdAttributeIdUserAttribute, 'SecureUserId', 3, 1, '2025-03-06 09:42:29.9668210');
ENd

update UserAttribute set ByStructure = 1 where AttributeId in (select Id from Attribute where Name in ( 'Privacy', 'StructureReceiver' , 'StructureSender'))