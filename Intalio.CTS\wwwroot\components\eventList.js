﻿import Intalio from './common.js';
import EventIndex from './eventIndex.js';

class Event extends Intalio.Model {
    constructor() {
        super();
        this.delegationId = null;
        this.documentId = null;
    }
}

var gIsLocked = false;

function openEventWindow(row, self) {
    if (!self.model.readOnly) {
        var wrapper = $(".modal-window");
        var modelIndex = new EventIndex.EventIndex();
        modelIndex.documentId = self.model.documentId;
        var eventIndex = new EventIndex.EventIndexView(wrapper, modelIndex);
        eventIndex.render();
        let tr = $(row).closest('tr');
        let srow = $(self.refs['grdEventItems']).DataTable().row(tr);
        var event = srow.data();
        if (event) {
            $(eventIndex.refs['modalEventTitle']).html(Resources.Edit);
            eventIndex.setData(event);
            eventIndex.model.eventId = event.id;
        } else {
            $(eventIndex.refs['modalEventTitle']).html(Resources.New);
        }
        $(eventIndex.refs['modalEvent']).modal("show");
        $(eventIndex.refs['modalEvent']).off("hidden.bs.modal");
        $(eventIndex.refs['modalEvent']).off("shown.bs.modal");

        $(eventIndex.refs['modalEvent']).on('hidden.bs.modal', function () {
            eventIndex.model.eventId = '';
            $(eventIndex.refs['formIndexPost']).parsley().reset();
            $(eventIndex.refs['userPostMessage']).html('');
        });
    }
}

function deleteEvent(id, self) {
    try {
        Common.showConfirmMsg(Resources.DeleteEventConfirmation, function () {
            if (gIsLocked === false) {
                gIsLocked = true;
                Common.ajaxDelete('/Events/Delete', { 'id': id, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val() }, function (data) {
                    gIsLocked = false;
                    //if (!data) {
                    //    setTimeout(function () {
                    //        Common.alertMsg("No Permission");
                    //    }, 300);
                    //} else {
                        $(self.refs['grdEventItems']).DataTable().ajax.reload();
                    /*}*/
                }, function () { gIsLocked = false; }, false);
            }
        });
    } catch (ex) {
        gIsLocked = false;
    }
}

function format(row) {
    return '<table style="width:100%" cellspacing="0" border="0">' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">Description:</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;">' + (row.data().description || '') + '</td>' +
        '</tr>' +
        '</table>';
}

class EventView extends Intalio.View {
    constructor(element, model) {
        super(element, "eventList", model);
    }

    render() {
        var self = this;
        var model = this.model;
        let buttons = [];
        $(self.refs['btnNewEvent']).hide();
        if (this.model.actionName !== undefined) {
            var actionArray = this.model.actionName.split("_");
            if (actionArray.includes("Event.New") && !model.readOnly)
            {
                buttons = [
                    {
                        className: 'btn-sm btn-primary btnNewEvent',
                        text: `<span class="fa fa-plus-circle mr-sm"></span><span>${Resources.New}</span>`
                    }
                ]
            }
        }

        
        var table = $(self.refs["grdEventItems"]).DataTable({
            processing: true,
            ordering: true,
            serverSide: true,
            pageLength: 10,
            ajax: {
                url: "/Events/List",
                type: "GET",
                datatype: "json",
                data: function (d) {
                    return {
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val(),
                        draw: d.draw,
                        start: d.start,
                        length: d.length,
                        documentId: model.documentId
                    };
                }
            },
            order: [],
            columns: [
                {
                    className: 'details-control width20',
                    orderable: false,
                    data: null,
                    defaultContent: '',
                    width: '16px'
                },
                { title: "Id", data: "id", visible: false, orderable: false },
                {
                    title: Resources.Name, data: "name", orderable: false, className: "min-max-width-50-250"
                },
                {
                    title: Resources.Location, data: "location", orderable: false, className: "min-max-width-50-250"
                },
                {
                    title: Resources.FromDate, data: "fromDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                    render: function (data, type, full, meta) {
                        return DateConverter.toHijriFormated(full.fromDate, null, window.CalendarType);
                    }
                },
                {
                    title: Resources.ToDate, data: "toDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                    render: function (data, type, full, meta) {
                        return DateConverter.toHijriFormated(full.toDate, null, window.CalendarType);
                    }
                },
                {
                    title: Resources.CreatedBy, "orderable": false, orderSequence: ["desc", "asc"], "autoWidth": false, "className": "min-max-width-50-250",
                    render: function (data, type, full, meta) {
                        return full.createdBy;
                    }
                },
                {
                    className: "text-right width20 minwidth55",
                    autoWidth: false,
                    bAutoWidth: false,
                    width: "16px",
                    orderable: false,
                    sortable: false,
                    render: function (data, type, full, meta) {
                        var html = "";
                        if (!self.model.readOnly && full.createdByUserId == $("#hdUserId").val()) {
                            let btn = document.createElement("button");
                            btn.setAttribute("class", "btn btn-xs mr-sm btn-warning edit");
                            btn.setAttribute("title", "Edit");
                            btn.setAttribute("clickAttr", "openEventWindow(this)");
                            btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                            html += btn.outerHTML;

                            if (model.actionName != undefined) {
                                var actionArray = model.actionName.split("_");
                                if (true) { 
                                    btn = document.createElement("button");
                                    btn.setAttribute("class", "btn btn-xs btn-danger delete");
                                    btn.setAttribute("title", "Delete");
                                     btn.setAttribute("clickAttr", "deleteEvent(" + full.id + ")");
                                    btn.innerHTML = "<i class='fa fa-trash fa-white'/>";
                                    html += btn.outerHTML;
                                }
                            }
                        }
                        return html;
                    }
                }
            ],
            fnInitComplete: function (settings, json) {
                $('[data-toggle="tooltip"]').tooltip();
            },
            dom: '<"html5buttons "B>ltrpi',
            buttons: buttons            
        });

     
        $(self.refs["grdEventItems"]).find('td').tooltip({
            delay: 0,
            track: true,
            fade: 250
        });

        $(self.refs['btnNewEvent']).hide();
        $(self.refs['grdEventItems']).on('click', ".edit", function () {
            var onclick = $(this).attr("clickattr").slice(0, -1) + ",self)";
            eval(onclick);
        });

       
        $(self.refs['grdEventItems']).on('click', ".delete", function () {
            var onclick = $(this).attr("clickattr").slice(0, -1) + ",self)";
            eval(onclick);
        });
        
        $('.btnNewEvent').click(function () {
            openEventWindow(null, self);
        });
        $(self.refs['grdEventItems']).on('click', 'td.details-control', function () {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            } else {
                row.child(format(row)).show();
                tr.addClass('shown');
            }
        });
    }
}

export default { Event, EventView }
