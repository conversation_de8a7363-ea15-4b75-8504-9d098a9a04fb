﻿using Intalio.Core.DAL;
using Intalio.Core.Model;
using Intalio.CTS.Core.Model;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;

namespace Intalio.Core.API
{
    public static class ManageNode
    {
        #region Public Methods
        
        public static (bool Success, string Message) Create(long userId, NodeViewModel model ,long? structureId)
        {
            if (new Node().CheckUniqueUnderParent(model.Id, model.ParentNodeId, model.Name , userId , structureId))
            {
                return (false, "NameAlreadyExist");
            }
            var OrderExists = new Node().CheckUniqueOrderUnderParent(model.Id, model.ParentNodeId, model.Order, userId, model.StructureId);
            if (OrderExists)
            {
                return (false, "OrderAlreadyExists");
            }
            if (model.ParentNodeId.HasValue)
            {
                var parent = Configuration.AdminAudit ? new Node().FindWithInclude(model.ParentNodeId.Value) : new Node().Find(model.ParentNodeId.Value);
                var originalValue = JsonConvert.SerializeObject(new AuditNodeModel
                {
                    Name = parent.Name,
                    Icon = parent.Icon,
                    ParentNodeId = parent.Id,
                    Visible = parent.Visible,
                    Expand = parent.Expand,
                    Inherit = parent.Inherit,
                    EnableTodayCount = parent.EnableTodayCount,
                    EnableTotalCount = parent.EnableTotalCount,
                    EnableUnreadCount= parent.EnableUnreadCount,
                    CustomFunctions = parent.CustomFunctions,
                    RoleIds = parent.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, parent.NodeSecurity.Where(t => !t.UserId.HasValue).Select(t => t.RoleId).ToList()),
                    UserIds = parent.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, parent.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList()),
                    IsUserNode = parent.IsUserNode,
                    StructureId = structureId,
                    UserId = userId
                });
                //delete last child of node (make the parent is null) -- comment till End 
                parent.Inherit = null;
                parent.Filters = null;
                parent.Columns = null;
                parent.Conditions = null;
                parent.EnableTodayCount = false;
                parent.EnableTotalCount = false;
                parent.EnableUnreadCount = false;
                parent.Update();
                //End
                ManageAudit.Add(userId, AuditModule.Node, AuditAction.Edit, originalValue, JsonConvert.SerializeObject(new AuditNodeModel
                {
                    Name = parent.Name,
                    Icon = parent.Icon,
                    ParentNodeId = parent.Id,
                    Visible = parent.Visible,
                    Expand = parent.Expand,
                    Inherit = parent.Inherit,
                    EnableTodayCount = parent.EnableTodayCount,
                    EnableTotalCount = parent.EnableTotalCount,
                    EnableUnreadCount= parent.EnableUnreadCount,
                    CustomFunctions = parent.CustomFunctions,
                    RoleIds = parent.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, parent.NodeSecurity.Where(t => !t.UserId.HasValue).Select(t => t.RoleId).ToList()),
                    UserIds = parent.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, parent.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList()),
                    IsUserNode = parent.IsUserNode,
                    StructureId = structureId,
                    UserId = userId
                }));
            }
            Node item = new Node
            {
                Name = model.Name,
                Order = model.Order,
                Icon = model.Icon,
                Inherit = model.Inherit,
                CustomFunctions = model.CustomFunctions,
                Filters = model.Filters,
                Columns = model.Columns,
                Conditions = model.Conditions,
                ParentNodeId = model.ParentNodeId,
                EnableTodayCount = model.EnableTodayCount,
                EnableTotalCount = model.EnableTotalCount,
                EnableUnreadCount = model.EnableUnreadCount,
                Visible = model.Visible,
                Expand = model.Expand,
                IsUserNode = model.IsUserNode,
                StructureId = structureId,
                UserId= userId,
                IsExported = model.IsExported
            };
            if (!model.RoleIds.IsNullOrEmpty())
            {
                model.RoleIds.ForEach(t => item.NodeSecurity.Add(new NodeSecurity
                {
                    RoleId = t
                }));
            }
            item.Insert();
            model.Id = item.Id;
            ManageAudit.Add(userId, AuditModule.Node, AuditAction.Add, originalValue: JsonConvert.SerializeObject(new AuditNodeModel
            {
                Name = item.Name,
                Icon = item.Icon,
                ParentNodeId = item.Id,
                Visible = item.Visible,
                Expand = item.Expand,
                Inherit = item.Inherit,
                EnableTodayCount = item.EnableTodayCount,
                EnableTotalCount = item.EnableTotalCount,
                EnableUnreadCount = item.EnableUnreadCount,
                CustomFunctions = item.CustomFunctions,
                RoleIds = model.RoleIds.IsNullOrEmpty() ? string.Empty : string.Join(Constants.SPLITTER, model.RoleIds),
                UserIds = string.Empty,
                IsUserNode = model.IsUserNode,
                StructureId = structureId,
                UserId = userId
            }));
            return (true, "");
        }

        public static (bool Success, string Message) Edit(long userId, NodeViewModel model ,long? StructureId)
        {
            bool retValue = false;
            if (model.Id != null)
            {
                if (new Node().CheckUniqueUnderParent(model.Id, model.ParentNodeId, model.Name , userId , StructureId))
                {
                    return (false, "NameAlreadyExist");
                }
                var OrderExists = new Node().CheckUniqueOrderUnderParent(model.Id, model.ParentNodeId, model.Order, userId, model.StructureId);
                if (OrderExists)
                {
                    return (false, "OrderAlreadyExists");
                }
                Node item = new Node().FindWithInclude(model.Id.Value);
                if (item != null)
                {
                    var originalValue = JsonConvert.SerializeObject(new AuditNodeModel
                    {
                        Name = item.Name,
                        Icon = item.Icon,
                        ParentNodeId = item.Id,
                        Visible = item.Visible,
                        Expand = item.Expand,
                        Inherit = item.Inherit,
                        EnableTodayCount = item.EnableTodayCount,
                        EnableTotalCount = item.EnableTotalCount,
                        EnableUnreadCount = item.EnableUnreadCount,
                        CustomFunctions = item.CustomFunctions,
                        RoleIds = item.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, item.NodeSecurity.Where(t => !t.UserId.HasValue).Select(t => t.RoleId).ToList()),
                        UserIds = item.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, item.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList()),
                        IsUserNode = item.IsUserNode,
                        UserId =  userId ,
                        StructureId = StructureId,
                    
                    });
                    item.Name = model.Name;
                    item.Order = model.Order;
                    item.Icon = model.Icon;
                    item.Inherit = model.Inherit;
                    item.Filters = model.Filters;
                    item.Columns = model.Columns;
                    item.Conditions = model.Conditions;
                    item.CustomFunctions = model.CustomFunctions;
                    item.ParentNodeId = model.ParentNodeId;
                    item.EnableTodayCount = model.EnableTodayCount;
                    item.EnableTotalCount = model.EnableTotalCount;
                    item.EnableUnreadCount = model.EnableUnreadCount;
                    item.Visible = model.Visible;
                    item.Expand = model.Expand;
                    item.IsUserNode = model.IsUserNode;
                    item.UserId = userId;
                    item.StructureId = StructureId;
                    item.IsExported = item.IsExported;
                    if (!model.RoleIds.IsNullOrEmpty())
                    {
                        var tobeDeleted = item.NodeSecurity.Where(t => !model.RoleIds.Any(n => n == t.RoleId) && t.RoleId != null).Select(t => t.Id).ToList();
                        if (!tobeDeleted.IsNullOrEmpty())
                        {
                            new NodeSecurity().Delete(tobeDeleted);
                            foreach (var removedItem in tobeDeleted)
                            {
                                var property = item.NodeSecurity.FirstOrDefault(t => t.Id == removedItem);
                                if (property != null)
                                {
                                    item.NodeSecurity.Remove(property);
                                }
                            }
                        }

                        //insert  
                        model.RoleIds.Where(t => !item.NodeSecurity.Any(n => n.RoleId == t)).Select(t => new NodeSecurity
                        {
                            RoleId = t
                        }).ToList().ForEach(t => item.NodeSecurity.Add(t));
                    }
                    else
                    {
                        var tobeDeleted = item.NodeSecurity.Select(t => t.Id).ToList();
                        if (!tobeDeleted.IsNullOrEmpty())
                        {
                            new NodeSecurity().Delete(tobeDeleted);
                        }
                        foreach (var removedItem in tobeDeleted)
                        {
                            var property = item.NodeSecurity.FirstOrDefault(t => t.Id == removedItem);
                            if (property != null)
                            {
                                item.NodeSecurity.Remove(property);
                            }
                        }
                    }

                    item.Update();
                    retValue = true;
                    ManageAudit.Add(userId, AuditModule.Node, AuditAction.Edit, originalValue, JsonConvert.SerializeObject(new AuditNodeModel
                    {
                        Name = item.Name,
                        Icon = item.Icon,
                        ParentNodeId = item.Id,
                        Visible = item.Visible,
                        Expand = item.Expand,
                        Inherit = item.Inherit,
                        EnableTodayCount = item.EnableTodayCount,
                        EnableTotalCount = item.EnableTotalCount,
                        EnableUnreadCount= item.EnableUnreadCount,
                        CustomFunctions = item.CustomFunctions,
                        RoleIds = model.RoleIds.IsNullOrEmpty() ? string.Empty : string.Join(Constants.SPLITTER, model.RoleIds),
                        UserIds = item.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, item.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList()),
                        IsUserNode = item.IsUserNode,
                        StructureId = StructureId,
                        UserId = userId
                    }));
                }
            }
            return (retValue, "");
        }

        public static void Delete(long userId, short id)
        {
            new Node().DeleteWithChildren(id, userId);
        }

        public static List<ValueText> ListNodes(string name, Language language = Language.EN)
        {
            var xx = new Node().List(name).ToList();
            var nodes = new Node().List(name) 
           .Select(t => new ValueText
           {
               Id = t.Id,
               Text = t.Name,
               ParentNodeId = t.ParentNodeId,
               ParentName = t.ParentNodeId != null
                   ? new Node().List().FirstOrDefault(p => p.Id == t.ParentNodeId)?.Name
                   : null
           })
           .ToList();
            nodes.ForEach(t =>
            {
                t.Text = TranslationUtility.Translate(t.Text, language);
            });
            return nodes;
        }

        public static void UpdateSecurity(List<short> nodeIds, long currentUserId, int? roleId, long? userId)
        {
            var nodes = new List<Node>();
            nodes = !userId.HasValue ? new Node().ListByRoleId(roleId.Value) : new Node().ListByUserId(userId.Value);
            if (!nodeIds.IsNullOrEmpty())
            {
                var tobeDeleted = nodes.Where(t => !nodeIds.Any(n => n == t.Id)).Select(t => t.Id).ToList();
                if (!tobeDeleted.IsNullOrEmpty())
                {
                    new NodeSecurity().DeleteByNodeIdRoleIdUserId(tobeDeleted, roleId, userId);
                }
                //insert
                nodeIds.Where(t => !nodes.Any(n => n.Id == t))
                    .Select(t => !userId.HasValue ? new NodeSecurity
                    {
                        RoleId = roleId,
                        NodeId = t,
                    } : new NodeSecurity
                    {
                        UserId = userId,
                        NodeId = t,
                    }).ToList().ForEach(t => t.Insert());
            }
            else
            {
                var tobeDeleted = nodes.Select(t => t.Id).ToList();
                if (!tobeDeleted.IsNullOrEmpty())
                {
                    new NodeSecurity().DeleteByNodeIdRoleIdUserId(tobeDeleted, roleId, userId);
                }
            }
            //Add admin audit
            AddAudit(nodes, currentUserId, roleId, userId);
        }

        public static void BreakInheritance(long currentUserId, int roleId, long userId)
        {
            var nodes = new Node().ListByRoleId(roleId);
            //insert 
            nodes.Select(t => new NodeSecurity
            {
                UserId = userId,
                NodeId = t.Id,
            }).ToList().ForEach(t => t.Insert());

            var user = new User().Find(userId);
            user.SecurityBreakedInheritance = true;
            user.Update();
            //Add admin audit
            AddUserAudit(nodes, currentUserId, user, true);
        }

        public static void RevertBack(long currentUserId, long userId)
        {
            var nodes = new Node().ListByUserId(userId);
            if (!nodes.IsNullOrEmpty())
            {
                var tobeDeleted = nodes.Select(t => t.Id).ToList();
                if (!tobeDeleted.IsNullOrEmpty())
                {
                    new NodeSecurity().DeleteByNodeIdUserId(userId, tobeDeleted);
                }
            }
            var user = new User().Find(userId);
            user.SecurityBreakedInheritance = false;
            user.Update();
            //Add admin audit
            AddUserAudit(nodes, currentUserId, user);
        }

        public static void CopyFromRole(long currentUserId, int fromRole, int toRole)
        {
            var fromNodes = new Node().ListByRoleId(fromRole);
            var toNodes = new Node().ListByRoleId(toRole);
            var originalNodes = new List<Node>(toNodes);
            if (!fromNodes.IsNullOrEmpty())
            {
                var tobeDeleted = toNodes.Where(t => !fromNodes.Any(n => n.Id == t.Id)).Select(t => t.Id).ToList();
                if (!tobeDeleted.IsNullOrEmpty())
                {
                    new NodeSecurity().DeleteByNodeIdRoleId(toRole, tobeDeleted);
                    foreach (var removedItem in tobeDeleted)
                    {
                        var property = toNodes.FirstOrDefault(t => t.Id == removedItem);
                        if (property != null)
                        {
                            toNodes.Remove(property);
                        }
                    }
                }
                //insert  
                fromNodes.Where(t => !toNodes.Any(n => n.Id == t.Id))
                    .Select(t => new NodeSecurity
                    {
                        RoleId = toRole,
                        NodeId = t.Id,
                    }).ToList().ForEach(t => t.Insert());
            }
            else
            {
                var tobeDeleted = toNodes.Select(t => t.Id).ToList();
                if (!tobeDeleted.IsNullOrEmpty())
                {
                    new NodeSecurity().DeleteByNodeIdRoleId(toRole, tobeDeleted);
                }
            }
            //Add admin audit
            AddRoleAudit(originalNodes, currentUserId, toRole);
        }

        /// <summary>
        /// Security list Nodes
        /// </summary>
        /// <returns></returns>
        public static List<NodeSecurityModel> ListSecurity()
        {
            return new Node().ListSecurity().Select(t => (NodeSecurityModel)t).ToList();
        }

        public static List<NodeListViewModel> List(string name)
        {
            return new Node().List(name).Where(t => t.InverseParentNode.Count == 0).Select(t => (NodeListViewModel)t).ToList();
        }

        public static List<ValueText> List(Language language = Language.EN)
        {
            var nodes = new Node().List().Select(t => new ValueText { Id = t.Id,Name=t.Name, Text = t.Name, ParentNodeId = t.ParentNodeId ,IsExported = t.IsExported}).ToList();
            nodes.ForEach(t =>
            {
                t.Text = TranslationUtility.Translate(t.Text, language);
            });
            nodes.ForEach(t =>
            {
                t.ParentName = nodes.FirstOrDefault(x => x.Id == t.ParentNodeId)?.Text;
            });
            return nodes;
        }

        public static List<NodeTreeListViewModel> ListNodesByRoleIdUserId(int roleId, long userId, Language language = Language.EN, long? delegationId = null)
        {
            var delegation = delegationId.HasValue ? CoreManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                roleId = delegation.RoleId;
            }
          
            var user = ManageUser.Find(userId);
            var securityBreakedInheritance = user != null ? user.SecurityBreakedInheritance : false;
            var nodes = (Configuration.EnableCaching ? new Node().ListWithCaching().Where(t =>
            (securityBreakedInheritance ? t.NodeSecurity.Any(t => t.UserId == userId) : t.NodeSecurity.Any(t => t.RoleId == roleId))).ToList() :
                new Node().ListByRoleIdUserId(roleId, userId, securityBreakedInheritance)).Select(t => (NodeTreeListViewModel)t).ToList();
            nodes.ForEach(t =>
            {
                t.Name = TranslationUtility.Translate(t.Name, language);
            });
            return nodes;
        }

        public static List<NodeTreeListViewModel> ListNodesByRoleIdUserIdStructureId(int roleId, long userId, long structureId, bool securityBreakedInheritance, Language language = Language.EN, long? delegationId = null)
        {
            var delegation = delegationId.HasValue ? CoreManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                roleId = delegation.RoleId;
            }

            var nodes = (Configuration.EnableCaching ? new Node().ListWithCaching().Where(t =>
           (securityBreakedInheritance ? t.NodeSecurity.Any(t => t.UserId == userId && t.StructureId == structureId) : t.NodeSecurity.Any(t => t.RoleId == roleId))).ToList() :
               securityBreakedInheritance ? new Node().ListByUserIdStructureId(userId, structureId) : new
               Node().ListByRoleId(roleId)).Select(t => (NodeTreeListViewModel)t).ToList();
            nodes.ForEach(t =>
            {
                t.Name = TranslationUtility.Translate(t.Name, language);
            });
            return nodes;
        }



        public static List<NodeTreeListViewModel> ListUserNodesByUserIdandStructureId(int roleId, long userId,long? structureId, Language language = Language.EN, long? delegationId = null)
        {
            var delegation = delegationId.HasValue ? CoreManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                roleId = delegation.RoleId;
            }
            var user = ManageUser.Find(userId);
            var securityBreakedInheritance = user != null ? user.SecurityBreakedInheritance : false;
            var nodes = (Configuration.EnableCaching ? new Node().ListWithCaching().Where(t =>
            (securityBreakedInheritance ? t.NodeSecurity.Any(t => t.UserId == userId && t.StructureId == structureId && t.Node.IsUserNode == true)
            : t.NodeSecurity.Any(t => t.RoleId == roleId && t.Node.UserId == userId && t.Node.StructureId == structureId && t.Node.IsUserNode == true)) && t.Visible == true).ToList() :
                new Node().ListByRoleIdUserIdStructureId(roleId, userId, structureId,securityBreakedInheritance).Where(t => t.Visible == true)).Select(t => (NodeTreeListViewModel)t).ToList();
            nodes.ForEach(t =>
            {
                t.Name = TranslationUtility.Translate(t.Name, language);
            });
            return nodes;
        }



        public static List<short> ListNodeIds()
        {
            return new Node().List().Select(t => t.Id).ToList();
        }

        /// <summary>
        /// Tree list node
        /// </summary>
        /// <returns></returns>
        public static TreeNode ListTreeNode()
        {
            var nodes = new Node().ListParent();
            TreeNode retValue = new TreeNode
            {
                Id = "0",
                Text = "/",
                State = new TreeNodeState()
                {
                    Disabled = false,
                    Opened = true,
                    Selected = false
                },
                Children = nodes.Select(t => (TreeNode)t).ToList()
            };
            return retValue;
        }

        public static TreeNode ListTreeUserNode(long userId , long structureId)
        {
            var nodes = new Node().ListParentUserNode(userId , structureId);
            TreeNode retValue = new TreeNode
            {
                Id = "0",
                Text = "/",
                State = new TreeNodeState()
                {
                    Disabled = false,
                    Opened = true,
                    Selected = false
                },
                Children = nodes.Select(t => (TreeNode)t).ToList()
            };
            return retValue;
        }

        /// <summary>
        /// List all nodes
        /// </summary>
        /// <returns></returns>
        public static List<ExportNodeViewModel> Export()
        {
            return new Node().Export().Select(t => (ExportNodeViewModel)t).ToList();
        }

        /// <summary>
        /// Import nodes
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="models"></param>
        public static void Import(long userId, List<ExportNodeViewModel> models)
        {
            var roots = models.ToList().Where(t => !t.ParentNodeId.HasValue).ToList();
            var childs = models.ToList().Where(t => t.ParentNodeId.HasValue).ToList();
            //insert roots
            foreach (var model in roots)
            {
                List<ExportNodeViewModel> newChilds = new List<ExportNodeViewModel>();
                var childrens = childs.Clone();
                newChilds.AddRange(childrens.Where(t => t.ParentNodeId == model.Id).ToList());
                var node = new Node().FindByNameAndParent(model.Name, roots.Where(x => x.Id == model.ParentNodeId).FirstOrDefault()?.Name);
                string nodeName = string.Empty;
                if (node.IsNull())
                {
                    Node item = new Node
                    {
                        Name = model.Name,
                        Order = model.Order,
                        Icon = model.Icon,
                        Inherit = model.Inherit,
                        CustomFunctions = model.CustomFunctions,
                        Filters = model.Filters,
                        Columns = model.Columns,
                        Conditions = model.Conditions,
                        ParentNodeId = model.ParentNodeId,
                        EnableTodayCount = model.EnableTodayCount,
                        EnableTotalCount = model.EnableTotalCount,
                        EnableUnreadCount = model.EnableUnreadCount,
                        Visible = model.Visible,
                        Expand = model.Expand
                    };
                    item.Insert();
                    models.Where(x => x.Id == model.Id).FirstOrDefault().Id = item.Id;
                    newChilds.ForEach(t => t.ParentNodeId = item.Id);
                    nodeName = item.Name;
                }
                else
                {
                    models.Where(x => x.Id == model.Id).FirstOrDefault().Id = node.Id;
                    newChilds.ForEach(t => t.ParentNodeId = node.Id);
                    nodeName = node.Name;
                }
                InsertChildrenRecursively(userId, childs, newChilds, nodeName);
            }
        }

        public static List<NodeBreakInheritanceModel> GetNodeBreakInheritanceByUserId(long userId)
        {
            List<NodeBreakInheritanceModel> nodeBreakInheritanceModel = new List<NodeBreakInheritanceModel>();
            var userNodeSecurity = new NodeSecurity().ListByUserId(userId);
            if (userNodeSecurity.Count > 0)
            {
                nodeBreakInheritanceModel = userNodeSecurity.Select(t => (NodeBreakInheritanceModel)t).ToList();
            }
            return nodeBreakInheritanceModel;
        }
        #endregion

        #region Private Methods

        private static void InsertChildrenRecursively(long userId, List<ExportNodeViewModel> childs, List<ExportNodeViewModel> newChilds, string parentName)
        {
            //insert childs
            foreach (var model in newChilds)
            {
                List<ExportNodeViewModel> newChildren = new List<ExportNodeViewModel>();
                var childrens = childs.Clone();
                newChildren.AddRange(childrens.Where(t => t.ParentNodeId == model.Id).ToList());
                var childNode = new Node().FindByNameAndParent(model.Name, parentName);
                string nodeName = string.Empty;
                if (childNode.IsNull())
                {
                    if (model.ParentNodeId.HasValue)
                    {
                        var parent = new Node().Find(model.ParentNodeId.Value);
                        parent.Inherit = null;
                        parent.Filters = null;
                        parent.Columns = null;
                        parent.Conditions = null;
                        parent.EnableTodayCount = false;
                        parent.EnableTotalCount = false;
                        parent.EnableUnreadCount = false;
                        parent.Update();
                    }
                    Node item = new Node
                    {
                        Name = model.Name,
                        Order = model.Order,
                        Icon = model.Icon,
                        Inherit = model.Inherit,
                        CustomFunctions = model.CustomFunctions,
                        Filters = model.Filters,
                        Columns = model.Columns,
                        Conditions = model.Conditions,
                        ParentNodeId = model.ParentNodeId,
                        EnableTodayCount = model.EnableTodayCount,
                        EnableTotalCount = model.EnableTotalCount,
                        EnableUnreadCount = model.EnableUnreadCount,
                        Visible = model.Visible,
                        Expand = model.Expand
                    };
                    item.Insert();
                    newChildren.ForEach(t => t.ParentNodeId = item.Id);
                    nodeName = item.Name;
                }
                else
                {
                    newChildren.ForEach(t => t.ParentNodeId = childNode.Id);
                    nodeName = childNode.Name;
                }
                InsertChildrenRecursively(userId, childs, newChildren, nodeName);
            }
        }

        public static List<ExportNodeViewModel> Clone(this List<ExportNodeViewModel> items)
        {
            List<ExportNodeViewModel> properties = new List<ExportNodeViewModel>();
            foreach (var item in items)
            {
                properties.Add(new ExportNodeViewModel
                {
                    Id = item.Id,
                    Name = item.Name,
                    Order = item.Order,
                    ParentNodeId = item.ParentNodeId,
                    Filters = item.Filters,
                    Columns = item.Columns,
                    Conditions = item.Conditions,
                    CustomFunctions = item.CustomFunctions,
                    Icon = item.Icon,
                    Inherit = item.Inherit,
                    Visible = item.Visible,
                    EnableTodayCount = item.EnableTodayCount,
                    EnableTotalCount = item.EnableTotalCount,
                    EnableUnreadCount= item.EnableUnreadCount
                });
            }
            return properties;
        }

        private static void AddAudit(List<Node> oldNodes, long currentUserId, int? roleId, long? userId)
        {
            if (Configuration.AdminAudit)
            {
                var newNodes = !userId.HasValue ? new Node().ListByRoleId(roleId.Value) : new Node().ListByUserId(userId.Value);
                AdminAudit(oldNodes, newNodes, currentUserId, roleId, userId);
            }
        }

        private static void AdminAudit(List<Node> oldNodes, List<Node> newNodes, long currentUserId, int? roleId = null, long? userId = null)
        {
            foreach (var oldNode in oldNodes)
            {
                var node = newNodes.FirstOrDefault(t => t.Id == oldNode.Id);
                string originalValue = JsonConvert.SerializeObject(new AuditNodeModel
                {
                    Name = oldNode.Name,
                    Icon = oldNode.Icon,
                    ParentNodeId = oldNode.Id,
                    Visible = oldNode.Visible,
                    Expand = oldNode.Expand,
                    Inherit = oldNode.Inherit,
                    EnableTodayCount = oldNode.EnableTodayCount,
                    EnableTotalCount = oldNode.EnableTotalCount,
                    EnableUnreadCount=oldNode.EnableUnreadCount,
                    CustomFunctions = oldNode.CustomFunctions,
                    RoleIds = oldNode.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, oldNode.NodeSecurity.Where(t => !t.UserId.HasValue).Select(t => t.RoleId).ToList()),
                    UserIds = oldNode.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, oldNode.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList())
                });
                string newValue = string.Empty;
                if (node.IsNull())
                {
                    if (userId.HasValue)
                    {
                        oldNode.NodeSecurity.Remove(oldNode.NodeSecurity.First(t => t.UserId == userId.Value));
                    }
                    else if (roleId.HasValue)
                    {
                        oldNode.NodeSecurity.Remove(oldNode.NodeSecurity.First(t => t.RoleId == roleId.Value));
                    }
                    newValue = JsonConvert.SerializeObject(new AuditNodeModel
                    {
                        Name = oldNode.Name,
                        Icon = oldNode.Icon,
                        ParentNodeId = oldNode.Id,
                        Visible = oldNode.Visible,
                        Expand = oldNode.Expand,
                        Inherit = oldNode.Inherit,
                        EnableTodayCount = oldNode.EnableTodayCount,
                        EnableTotalCount = oldNode.EnableTotalCount,
                        EnableUnreadCount= oldNode.EnableUnreadCount,
                        CustomFunctions = oldNode.CustomFunctions,
                        RoleIds = oldNode.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, oldNode.NodeSecurity.Where(t => !t.UserId.HasValue).Select(t => t.RoleId).ToList()),
                        UserIds = oldNode.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, oldNode.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList())
                    });
                }
                else
                {
                    newValue = JsonConvert.SerializeObject(new AuditNodeModel
                    {
                        Name = node.Name,
                        Icon = node.Icon,
                        ParentNodeId = node.Id,
                        Visible = node.Visible,
                        Expand = node.Expand,
                        Inherit = node.Inherit,
                        EnableTodayCount = node.EnableTodayCount,
                        EnableTotalCount = node.EnableTotalCount,
                        EnableUnreadCount = node.EnableUnreadCount,
                        CustomFunctions = node.CustomFunctions,
                        RoleIds = node.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, node.NodeSecurity.Where(t => !t.UserId.HasValue).Select(t => t.RoleId).ToList()),
                        UserIds = node.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, node.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList())
                    });
                }
                ManageAudit.Add(currentUserId, AuditModule.Node, AuditAction.Edit, originalValue, newValue);
            }
            foreach (var newNode in newNodes)
            {
                var node = oldNodes.FirstOrDefault(t => t.Id == newNode.Id);
                string originalValue = string.Empty;
                string newValue = JsonConvert.SerializeObject(new AuditNodeModel
                {
                    Name = newNode.Name,
                    Icon = newNode.Icon,
                    ParentNodeId = newNode.Id,
                    Visible = newNode.Visible,
                    Expand = newNode.Expand,
                    Inherit = newNode.Inherit,
                    EnableTodayCount = newNode.EnableTodayCount,
                    EnableTotalCount = newNode.EnableTotalCount,
                    EnableUnreadCount= newNode.EnableUnreadCount,
                    CustomFunctions = newNode.CustomFunctions,
                    RoleIds = newNode.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, newNode.NodeSecurity.Where(t => !t.UserId.HasValue).Select(t => t.RoleId).ToList()),
                    UserIds = newNode.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, newNode.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList())
                });
                if (node.IsNull())
                {
                    if (userId.HasValue)
                    {
                        newNode.NodeSecurity.Remove(newNode.NodeSecurity.First(t => t.UserId == userId.Value));
                    }
                    else if (roleId.HasValue)
                    {
                        newNode.NodeSecurity.Remove(newNode.NodeSecurity.First(t => t.RoleId == roleId.Value));
                    }
                    originalValue = JsonConvert.SerializeObject(new AuditNodeModel
                    {
                        Name = newNode.Name,
                        Icon = newNode.Icon,
                        ParentNodeId = newNode.Id,
                        Visible = newNode.Visible,
                        Expand = newNode.Expand,
                        Inherit = newNode.Inherit,
                        EnableTodayCount = newNode.EnableTodayCount,
                        EnableTotalCount = newNode.EnableTotalCount,
                        EnableUnreadCount= newNode.EnableUnreadCount,
                        CustomFunctions = newNode.CustomFunctions,
                        RoleIds = newNode.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, newNode.NodeSecurity.Where(t => !t.UserId.HasValue).Select(t => t.RoleId).ToList()),
                        UserIds = newNode.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, newNode.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList())
                    });
                }
                else
                {
                    originalValue = JsonConvert.SerializeObject(new AuditNodeModel
                    {
                        Name = node.Name,
                        Icon = node.Icon,
                        ParentNodeId = node.Id,
                        Visible = node.Visible,
                        Expand = node.Expand,
                        Inherit = node.Inherit,
                        EnableTodayCount = node.EnableTodayCount,
                        EnableTotalCount = node.EnableTotalCount,
                        EnableUnreadCount = node.EnableUnreadCount,
                        CustomFunctions = node.CustomFunctions,
                        RoleIds = node.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, node.NodeSecurity.Where(t => !t.UserId.HasValue).Select(t => t.RoleId).ToList()),
                        UserIds = node.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, node.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList())
                    });
                }
                ManageAudit.Add(currentUserId, AuditModule.Node, AuditAction.Edit, originalValue, newValue);
            }
        }

        private static void AddUserAudit(List<Node> nodes, long currentUserId, User user, bool isBreakInheritance = false)
        {
            foreach (var node in nodes)
            {
                var originalValue = JsonConvert.SerializeObject(new AuditNodeModel
                {
                    Name = node.Name,
                    Icon = node.Icon,
                    ParentNodeId = node.Id,
                    Visible = node.Visible,
                    Expand = node.Expand,
                    Inherit = node.Inherit,
                    EnableTodayCount = node.EnableTodayCount,
                    EnableTotalCount = node.EnableTotalCount,
                    EnableUnreadCount= node.EnableUnreadCount,
                    CustomFunctions = node.CustomFunctions,
                    RoleIds = node.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, node.NodeSecurity.Where(t => !t.UserId.HasValue).Select(t => t.RoleId).ToList()),
                    UserIds = node.NodeSecurity.IsNull() ? string.Empty : isBreakInheritance ? string.Join(Constants.SPLITTER, node.NodeSecurity.Where(t => !t.RoleId.HasValue && t.UserId != user.Id).Select(t => t.UserId).ToList())
                    : string.Join(Constants.SPLITTER, node.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList())
                });
                if (isBreakInheritance)
                {
                    node.NodeSecurity.Add(new NodeSecurity
                    {
                        NodeId = node.Id,
                        UserId = user.Id
                    });
                }
                else
                {
                    node.NodeSecurity.Remove(node.NodeSecurity.First(t => t.UserId == user.Id));
                }
                var newValue = JsonConvert.SerializeObject(new AuditNodeModel
                {
                    Name = node.Name,
                    Icon = node.Icon,
                    ParentNodeId = node.Id,
                    Visible = node.Visible,
                    Expand = node.Expand,
                    Inherit = node.Inherit,
                    EnableTodayCount = node.EnableTodayCount,
                    EnableTotalCount = node.EnableTotalCount,
                    EnableUnreadCount= node.EnableUnreadCount,
                    CustomFunctions = node.CustomFunctions,
                    RoleIds = node.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, node.NodeSecurity.Where(t => !t.UserId.HasValue).Select(t => t.RoleId).ToList()),
                    UserIds = node.NodeSecurity.IsNull() ? string.Empty : string.Join(Constants.SPLITTER, node.NodeSecurity.Where(t => !t.RoleId.HasValue).Select(t => t.UserId).ToList())
                });
                ManageAudit.Add(currentUserId, AuditModule.Node, AuditAction.Edit, originalValue, newValue);
            }
            if ((!user.SecurityBreakedInheritance && isBreakInheritance) || (user.SecurityBreakedInheritance && !isBreakInheritance))
            {
                ManageAudit.Add(currentUserId, AuditModule.User, AuditAction.Edit, JsonConvert.SerializeObject(new AuditUserModel
                {
                    Firstname = user.Firstname,
                    Lastname = user.Lastname,
                    RoleId = user.RoleId,
                    SecurityBreakedInheritance = isBreakInheritance ? false : true
                }), JsonConvert.SerializeObject(new AuditUserModel
                {
                    Firstname = user.Firstname,
                    Lastname = user.Lastname,
                    RoleId = user.RoleId,
                    SecurityBreakedInheritance = user.SecurityBreakedInheritance
                }));
            }
        }

        private static void AddRoleAudit(List<Node> nodes, long currentUserId, int roleId)
        {
            if (Configuration.AdminAudit)
            {
                List<Node> newNodes = new Node().ListByRoleId(roleId);
                AdminAudit(nodes, newNodes, currentUserId, roleId);
            }
        }
        public static string GetNameById(long id)
        {
            return new Node().GetNameById(id);
        }
        #endregion
    }
}
