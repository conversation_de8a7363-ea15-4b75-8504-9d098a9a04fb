﻿import Intalio from './common.js'
import AddressBook from './ctsaddressBook.js'
import { DelegationUsers, Helper } from './lookup.js'
import FavoriteStructure from './favoriteStructures.js'
import Distribution from './distributionList.js'
import favoriteStructures from './favoriteStructures.js'
import FollowUpIndex from './followUpIndex.js'
class TransferItem {
    constructor() {
        this.toStructureId;
        this.toUserId;
        this.name;
        this.dueDate;
        this.purposeId;
        this.instruction;
        this.PrivateInstruction;
        this.cced;
    }
}
class Transfer extends Intalio.Model {
    constructor() {
        super();
        this.documentId = null;
        this.structureIds = [];
        this.structureId = null;
        this.userId = null;
        this.enableTransferToUsers = false;
        this.isStructureSender = false;
        this.enableSendingRules = false;
        this.maxDueDate = null;
        this.gTableName = null;
        this.callBack = null;
        this.delegationId = null;
        this.favoriteStructures = [];
        this.selectedReceivers = [];
        this.categoryId = null;
        this.gIsDueDateRelatedToPriority = false;
    }
}
var self, instructionId;
var lastRowId = 2;
var lastFavouriteRowId = 2;
var transferRows = [], transferFavouriteRows = [], purposes = [], priorities = [];
var grdFavoriteTransferItems;
let clearRowClicked = false;
let hiddenFavouriteRowId = '2';
var fromFavorites = false;
var listFavoriteStructuresData = [];


// A flag to check if Structures/Users are coming from Favorites or from the url that gets all Structures and Users from the IAM, This url is called by the select2 dropdown list.
// So the Select2 dropdown of the ToTransfer dropdown list does not have any data in it, It is empty, Where It gets all structures and their assigned users from the IAM API URL

// This is the URL where the Select2 gets its data options from >> var url = window.IdentityUrl + '/api/GetUsersAndStructuresWithSearchAttributes';
function updateSelect2FavoriteData(selectComponent, newDataArray) {
    selectComponent.select2('destroy');

    // Reinitialize Select2 with the new data
    selectComponent.select2({
        minimumInputLength: 0,
        allowClear: false,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: selectComponent.parent(),
        width: "100%",
        templateResult: function (option) {
            // Custom rendering function
            return renderTextInSelectStructures(option);
        },
        data: newDataArray, // Set the new data
        sorter: function (data) {
            return data.sort((a, b) =>
                a.order.toString().localeCompare(b.order.toString(), undefined, { numeric: true })
            );
        }
    });

    // Trigger an event or refresh to apply changes
}
function createCKEDITOR(textarea) {
    if (textarea && textarea != null) {


        CKEDITOR.replace(textarea, {
            customConfig: "/lib/ckeditor/custom-config.js", language: window.languageVal, resize_enabled: false, wordcount: {
                showWordCount: false,
                showCharCount: true,
                maxWordCount: 4,
                maxCharCount: 220
            },
        });
    }

}
function showHideInstruction(id, isFavorite = false) {
    var data = $('#' + self.model.ComponentId + '_hidden_Area_' + id).val();
    var txtArea = $('#' + self.model.ComponentId + '_txtAreaTransferInstruction')

    if (isFavorite)
        data = $('#' + self.model.ComponentId + '_hidden_Area_Favorite_' + id).val();


    if (txtArea !== null) {
        if (isFavorite)
            txtArea.attr("fromElement", self.model.ComponentId + '_hidden_Area_Favorite_' + id)
        else
            txtArea.attr("fromElement", self.model.ComponentId + '_hidden_Area_' + id)
    }

    if (typeof CKEDITOR.instances[self.model.ComponentId + '_txtAreaTransferInstruction'] === 'undefined') {

        if (txtArea !== null) {
            createCKEDITOR(document.getElementById(self.model.ComponentId + '_txtAreaTransferInstruction'));
        }
    }

    CKEDITOR.instances[self.model.ComponentId + "_txtAreaTransferInstruction"].setData(data);

    $(self.refs['modalTransferInstruction']).modal('show');
    $(self.refs['modalTransferInstruction']).off("hidden.bs.modal");
    $(self.refs['modalTransferInstruction']).off("shown.bs.modal");
    $(self.refs['modalTransferInstruction']).on('shown.bs.modal', function () {
        $('.cke_wysiwyg_frame').contents().find('.cke_editable').focus();
    });
    $(self.refs['modalTransferInstruction']).on('hidden.bs.modal', function () {
        $('body').addClass('modal-open');
    });
}
function createTransferToSelect(id) {
    var headers = {};
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    var structureSendingRulesIds = [];
    let delegationId = self.model.delegationId;
    var idNumberValue = id.replaceAll(self.model.ComponentId + "_cmbTransferTo_", "");
    var url = location.origin + '/User/GetUsersStructuresFromCTS';
    var selectComponent = $("#" + id);
    var selectComponentParent = $("#" + self.model.ComponentId + "_transferToContainer_" + idNumberValue);

    selectComponent.select2({
        minimumInputLength: 0,
        allowClear: false,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: selectComponentParent,
        width: "100%",
        templateResult: function (option) {

            let $option = renderTextInSelectStructures(option)
            return $option;
        },
        ajax: {
            delay: 400,
            url: url,
            type: "POST",
            dataType: 'json',

            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                return {
                    "searchText": term.term ? term.term : "", "delegationId": delegationId, "structureType": StructureTypeMode.Internal, 'fromSendingandReceiving': false
                };
            },
            processResults: function (data, term) {
                var listitemsMultiList = [];
                $.each(data, function (key, val) {
                    if (val.id != 0) {
                        var item = {};
                        item.id = "Structure" + val.id;
                        var structureName = val.name;
                        item.text = structureName;
                        if (!val.isExternal) {
                            item.icon = "fa fa-building-o";
                        }
                        else {
                            item.icon = "fa fa-university";
                        }
                        item.isStructure = true;
                        item.dataId = val.id;
                        listitemsMultiList.push(item);
                    }

                    if (val.userStructure != null && val.userStructure.length > 0) {
                        $.each(val.userStructure, function (key, userStructureVal) {
                            var item = {};
                            item.id = "User_" + userStructureVal.structureId + "_" + userStructureVal.userId;
       
                            //var structureName = window.language === "ar" ? userStructureVal.structure.nameAr : window.language === "fr" ? userStructureVal.structure.nameFr : userStructureVal.structure.name;
                            var structureName = window.language === "ar"
                                ? (userStructureVal.structure.nameAr || userStructureVal.structure.name)
                                : window.language === "fr"
                                    ? (userStructureVal.structure.nameFr || userStructureVal.structure.name)
                                    : userStructureVal.structure.name;

                            var userName = userStructureVal.user.firstname + " " + userStructureVal.user.lastname
                            item.text = structureName + " / " + userName;
                            item.icon = "fa fa-user-o";
                            item.isStructure = false;
                            item.dataId = userStructureVal.userId;
                            item.structureId = userStructureVal.structureId;
                            listitemsMultiList.push(item);
                        });
                    }

                });
                return {
                    results: listitemsMultiList
                };
            }
        },
        sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
    }).on('change', function () {
        $(this).trigger('input');
        let idCharacters = $(this).attr("id").split('_');
        let id = idCharacters[idCharacters.length - 1];

        var attRequired = $('#' + self.model.ComponentId + '_cmbTransferPurpose_' + id).attr('required');
        if (typeof attRequired === typeof undefined) {
            $('#' + self.model.ComponentId + '_cmbTransferPurpose_' + id).attr('required', 'required');
        }
        if (!clearRowClicked && !fromFavorites)
            addRow(this);
    });
}
function createTransferToFavoriteSelect(id) {
    var idNumberValue = id.replaceAll(self.model.ComponentId + "_cmbTransferTo_Favorite_", "");
    var selectComponent = $("#" + id);
    var selectComponentParent = $("#" + self.model.ComponentId + "_transferToContainer_Favorite_" + idNumberValue);
    selectComponent.select2({
        minimumInputLength: 0,
        allowClear: false,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: selectComponentParent,
        width: "100%",
        templateResult: function (option) {

            let $option = renderTextInSelectStructures(option)
            return $option;
        },
        data: renderTextInFavoriteSelect(self.model.favoriteStructures),
        //sorter: data => data.sort((a, b) =>

        //    a.order.toString().localeCompare(b.order.toString(), undefined, { 'numeric': true })

        //)
    }).on('change', function () {
        $(this).trigger('input');
        let idCharacters = $(this).attr("id").split('_');
        let id = idCharacters[idCharacters.length - 1];

        if (!clearRowClicked && $(this).val() != '0') {
            var attRequired = $('#' + self.model.ComponentId + '_cmbTransferPurpose_Favorite_' + id).attr('required');
            if (typeof attRequired === typeof undefined) {
                $('#' + self.model.ComponentId + '_cmbTransferPurpose_Favorite_' + id).attr('required', 'required');
            }
            //addFavoriteRow(this);
        }
        else {

            $('#' + self.model.ComponentId + '_cmbTransferPurpose_Favorite_' + id).removeAttr('required');

        }

        var allData = $(self.refs['grdFavoriteTransferItems']).find('*[id*="_cmbTransferTo_Favorite_"]:not([id*="-container"]):not([id*="-results"])').map(function () {
            return $(this).select2('data');
        }).get();

        if (allData && allData.length > 0) {
            var hasNonZeroValue = Object.values(allData).some(function (value) {
                return value.id !== '0';
            });

            if (hasNonZeroValue) {
                $(self.refs['grdTransferItems']).find('*[id*="_cmbTransferTo_"]:not([id*="-container"])').each(function () {

                    $(this).removeAttr('required');

                });

                $(self.refs['grdTransferItems']).find('*[id*="_cmbTransferPurpose_"]:not([id*="-container"])').each(function () {

                    let idCharacters = $(this).attr("id").split('_');
                    let id = idCharacters[idCharacters.length - 1];

                    var transferTo = $("#" + self.model.ComponentId + "_cmbTransferTo_" + id).val();
                    if (!transferTo) {
                        $(this).removeAttr('required');
                    }

                });


            } else {

                var transferToElements = $(self.refs['grdTransferItems']).find('*[id*="_cmbTransferTo_"]:not([id*="-container"])');
                transferToElements.each(function () {
                    if ($(this).attr("id") != transferToElements.last().attr("id")) {

                        var attRequired = $(this).attr('required');
                        if (typeof attRequired === typeof undefined) {
                            $(this).attr('required', 'required');
                        }
                    }
                });

                var purposeElements = $(self.refs['grdTransferItems']).find('*[id*="_cmbTransferPurpose_"]:not([id*="-container"])');
                purposeElements.each(function () {

                    if ($(this).attr("id") != purposeElements.last().attr("id")) {

                        var attRequired = $(this).attr('required');
                        if (typeof attRequired === typeof undefined) {
                            $(this).attr('required', 'required');
                        }
                    }
                });
            }

        }


    });


}
function renderTextInFavoriteSelect(favoriteStructures) {
    var listitemsMultiList = [];
    var item = {};
    item.id = "0"
    item.text = "  ";
    item.selected = true;
    item.order = "0";

    listitemsMultiList.push(item);

    $.each(favoriteStructures, function (key, val) {
        item = {};
        item.id = val.toUserId > 0 ? "User" + val.toUserId.toString() : "Structure" + val.toStructureId.toString();
        item.text = val.name;
        item.selected = false;
        item.icon = "fa fa-building-o";
        item.isStructure = val.toUserId > 0 ? false : true;
        item.dataId = val.id;
        item.order = val.order;
        item.toUserId = val.toUserId.toString();
        item.toStructureId = val.toStructureId.toString();
        item.distributionId = val.distributionId.toString();
        listitemsMultiList.push(item);
    });
    return listitemsMultiList;
}
function renderTextInSelectStructures(option) {
    var $option;
    if (typeof option.id !== "undefined") {
        $option = $(
            '<div><i style="font-size:12px;" class="' + option.icon + '"></i> ' + option.text + '</div>'
        );
    } else {
        $option = option;
    }
    return $option;
}
function getStructuresUsersOptions(data, term) {
    var listitemsMultiList = [];
    var allDistribution = new Distribution.DistributionList().getMyDistributionList(term, self.model.delegationId, self.model.structureId);
    listitemsMultiList.push(...allDistribution);

    var allUSersWithStructures = getUsersWithStructureOptions(data);
    listitemsMultiList.push(...allUSersWithStructures);

    var allStructures = getStructuresOptions(data.structures);
    listitemsMultiList.push(...allStructures);

    return listitemsMultiList;
}
function getStructuresOptions(data) {
    var listitemsMultiList = [];

    $.each(data, function (key, val) {
        var item = {};
        item.id = "Structure" + val.id;
        var structureName = val.name;
        if (val.attributes != null && val.attributes.length > 0) {
            var attributeLang = $.grep(val.attributes, function (e) {
                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
            });
            if (attributeLang.length > 0) {
                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
            }
        }
        item.text = structureName;
        item.icon = "fa fa-building-o";
        item.isStructure = true;
        item.dataId = val.id;
        listitemsMultiList.push(item);
    });
    return listitemsMultiList;

}
function getUsersWithStructureOptions(data) {
    var listitemsMultiList = [];

    $.each(data.users, function (key, val) {

        var delegatedUser = (typeof self.model.delegationId !== 'undefined' && self.model.delegationId !== null) ? new DelegationUsers().getById(Number(self.model.delegationId)) : null;
        var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
        if ((delegatedUserId != 0 && val.id !== delegatedUserId) || (delegatedUserId == 0 && val.id !== Number(self.model.userId))) {
            var item = {};
            item.id = "User_" + val.structureIds[0] + "_" + val.id;
            var currentStructure = new CoreComponents.Lookup.Structure().getWithCaching(val.structureIds[0], window.language);
            var structureName = currentStructure;
            if (currentStructure.attributes != null && currentStructure.attributes.length > 0) {
                var attributeLang = $.grep(currentStructure.attributes, function (e) {
                    return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                });
                if (attributeLang.length > 0) {
                    structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                }
            }
            item.text = structureName + " / " + val.fullName;
            item.icon = "fa fa-user-o";
            item.isStructure = false;
            item.dataId = val.id;
            item.structureId = val.structureIds.length > 0 ? val.structureIds[0] : val.defaultStructureId;
            item.structureIds = val.structureIds;
            item.defaultStructureId = val.defaultStructureId;
            listitemsMultiList.push(item);
        }
    });
    return listitemsMultiList;

}
function createPurposeSelect(id) {
    var idNumberValue = id.replaceAll(self.model.ComponentId + "_cmbTransferPurpose_", "");
    var selectComponent = $("#" + id);
    var selectComponentParent = $("#" + self.model.ComponentId + "_transferPurposeContainer_" + idNumberValue);

    if (id.indexOf("_Favorite_") !== -1) {
        idNumberValue = id.replaceAll(self.model.ComponentId + '_cmbTransferPurpose_Favorite_', "");
        selectComponentParent = $("#" + self.model.ComponentId + "_transferPurposeContainer_Favorite_" + idNumberValue);
    }

    selectComponent.select2({
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        width: "100%",
        dropdownParent: selectComponentParent,
        data: purposes,
    }).on('change', function () {

        var selectedPurpose = $(this).val();
        if (selectedPurpose !== "" && selectedPurpose !== null) {
            var targetedPurpose = $.grep(purposes, function (e) {
                return e.id.toString() === selectedPurpose;
            });

            var cCedInput = $(this).parent().parent().children().find('[name="Cced"]')[0];

            if (cCedInput !== undefined && targetedPurpose[0].cCed) {
                cCedInput.checked = true;
            } else if (cCedInput !== undefined) {
                cCedInput.checked = false;
                $(this).prop('disabled', false);
            }
            $(this).trigger('input');

            let idCharacters = $(this).attr("id").split('_');
            let id = idCharacters[idCharacters.length - 1];

            // Removed calls to addRow() and addFavoriteRow()
            // The following block is removed:

            if ($(this).attr("id").indexOf("_Favorite_") === -1) {
                var attRequired = $('#' + self.model.ComponentId + '_cmbTransferTo_' + id).attr('required');
                if (typeof attRequired === typeof undefined) {
                    $('#' + self.model.ComponentId + '_cmbTransferTo_' + id).attr('required', 'required');
                }
                if (!fromFavorites && !fromFavorites)
                    addRow(this);
            } else {
                // Prevent adding multiple rows in Favorites
                const favoriteRowExists = $("#" + self.model.ComponentId + "_transferPurposeContainer_Favorite_" + idNumberValue).length > 0;
                if (!favoriteRowExists) {
                    //addFavoriteRow(this);
                }
            }
        }
    });
    selectComponent.val("").trigger("change");
    // This Line ensures that the last row added to the Transfer rows is to be empty
    // When a new row is added after choosing an option value from the dropdown list options >> the addRow(this) >> meaning that a new row is added with (this) >> representing the purpose option choosen.

}

function createPrioritySelect(id, fromMeeting = false) {
    var idNumberValue = id.replaceAll(self.model.ComponentId + "_cmbTransferPriority_", "");
    var selectComponent = $("#" + id);
    var selectComponentParent = $("#" + self.model.ComponentId + "_transferPriorityContainer_" + idNumberValue);
    if (id.indexOf("_Favorite_") !== -1) {
        idNumberValue = id.replaceAll(self.model.ComponentId + '_cmbTransferPriority_Favorite_', "");
        selectComponentParent = $("#" + self.model.ComponentId + "_transferPriorityContainer_Favorite_" + idNumberValue);
    }
    selectComponent.select2({
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        width: "100%",
        dropdownParent: selectComponentParent,
        data: priorities
    }).on('change', function () {
        var selectedPriority = $(this).val();
        if (selectedPriority !== "" && selectedPriority !== null) {
            var targetedPriority = $.grep(priorities, function (e) {
                return e.id.toString() === selectedPriority;
            });
            if (targetedPriority[0].cCed) {
                $(this).parent().parent().children().last().children('.fa-cc').removeClass('hidden');
            } else {
                $(this).parent().parent().children().last().children('.fa-cc').addClass('hidden');
            }

            var customAttributeDueDate;
            if (self.model.gIsDueDateRelatedToPriority) {
                var dueDays = targetedPriority[0].numberOfDueDays;
                var id = fromMeeting == true ? (parseInt(idNumberValue)).toString() : idNumberValue
                if (typeof dueDays !== "undefined") {

                    if ($(this).attr("id").indexOf("_Favorite_") == -1) {
                        customAttributeDueDate = $("#" + self.model.ComponentId + '_transferDueDate_' + id).flatpickr({
                            noCalendar: false,
                            enableTime: false,
                            dateFormat: 'd/m/Y',
                            minDate: "today",
                            maxDate: self.model.maxDueDate
                        });
                    } else {
                        customAttributeDueDate = $("#" + self.model.ComponentId + '_transferDueDate_Favorite_' + id).flatpickr({
                            noCalendar: false,
                            enableTime: false,
                            dateFormat: 'd/m/Y',
                            minDate: "today"
                        });

                    }
                    customAttributeDueDate.clear();
                    customAttributeDueDate.setDate(addBusinessDays(new Date(), dueDays));
                }
            }

            $(this).trigger('input');

        }
    });
    selectComponent.val("1");
}

function createDueDate(id, imgId, withMax) {
    var dateComponent = $("#" + id);
    var iconComponent = $("#" + imgId);
    var transferDueDate = dateComponent.flatpickr({
        noCalendar: false,
        enableTime: false,
        dateFormat: 'd/m/Y',
        minDate: "today",
        maxDate: withMax ? self.model.maxDueDate : ""
    });

    // Check if the ID belongs to a favorite structure
    var idNumberValue = id.indexOf("_Favorite_") !== -1
        ? id.replaceAll(self.model.ComponentId + '_transferDueDate_Favorite_', "")
        : id.replaceAll(self.model.ComponentId + '_transferDueDate_', "");

    // Remove references to priority since it's no longer in the template
    if (self.model.gIsDueDateRelatedToPriority) {
        console.warn("Due date related to priority logic has been skipped because priorities are no longer in the template.");
    }

    // Attach toggle behavior to the calendar icon
    iconComponent.click(function () {
        transferDueDate.toggle();
    });
}
function addRow(element) {
    var table = $(self.refs['grdTransferItems'])[0];
    var rowCnt = table.rows.length;
    if ($(element).closest("tr").is(":last-child") && !$(element).hasClass('checkPurpose')) {
        var tr = table.insertRow(rowCnt);
        lastRowId += 1;
        transferRows.push(lastRowId);
        var colCount = table.rows[0].cells.length;
        for (var c = 0; c < colCount; c++) {
            var td = document.createElement('td');
            td = tr.insertCell(c);
            if (c === 0) {
                var div = document.createElement('div');
                div.setAttribute('class', 'input-group');
                td.setAttribute('id', self.model.ComponentId + '_transferToContainer_' + lastRowId);
                var select = document.createElement('select');
                select.setAttribute('id', self.model.ComponentId + '_cmbTransferTo_' + lastRowId);

                select.setAttribute('data-parsley-errors-container', '#' + self.model.ComponentId + '_cmbTransferToError_' + lastRowId);
                var span = document.createElement('span');
                span.setAttribute('class', 'input-group-addon triggerAddressBook');
                span.setAttribute('id', self.model.ComponentId + '_transferToAddressBook_' + lastRowId);
                span.setAttribute('clickAttr', 'openTransferAddressBook(' + lastRowId + ')');
                span.setAttribute('style', 'cursor:pointer');
                var i = document.createElement('i');
                i.setAttribute('class', 'fa fa-address-book-o');
                i.setAttribute('aria-hidden', 'true');
                var errorContainer = document.createElement('div');
                errorContainer.setAttribute('id', self.model.ComponentId + '_cmbTransferToError_' + lastRowId);
                span.appendChild(i);
                div.appendChild(select);
                div.appendChild(span);
                td.appendChild(div);
                td.appendChild(errorContainer);
                createTransferToSelect(self.model.ComponentId + '_cmbTransferTo_' + lastRowId);
            }
            else if (c === 1) {
                td.setAttribute('id', self.model.ComponentId + '_transferPurposeContainer_' + lastRowId);
                var select = document.createElement('select');
                select.setAttribute('id', self.model.ComponentId + '_cmbTransferPurpose_' + lastRowId);
                select.setAttribute('class', 'form-control purposeAll');

                select.setAttribute('data-parsley-errors-container', '#' + self.model.ComponentId + '_cmbTransferPurposeError_' + lastRowId);
                var errorContainer = document.createElement('div');
                errorContainer.setAttribute('id', self.model.ComponentId + '_cmbTransferPurposeError_' + lastRowId);
                td.appendChild(select);
                td.appendChild(errorContainer);
                createPurposeSelect(self.model.ComponentId + '_cmbTransferPurpose_' + lastRowId);
            }
            else if (c === 2) {
                td.setAttribute('id', self.model.ComponentId + '_transferPriorityContainer_' + lastRowId);
                var select = document.createElement('select');
                select.setAttribute('id', self.model.ComponentId + '_cmbTransferPriority_' + lastRowId);
                select.setAttribute('class', 'form-control priorityAll');
                var attRequired = $("#" + self.model.ComponentId + "_cmbTransferPriority_" + (lastRowId - 1)).attr('required');
                if (typeof attRequired === typeof undefined) {
                    $("#" + self.model.ComponentId + "_cmbTransferPriority_" + (lastRowId - 1)).attr('required', 'required');
                }
                select.setAttribute('data-parsley-errors-container', '#' + self.model.ComponentId + '_cmbTransferPriorityError_' + lastRowId);
                var errorContainer = document.createElement('div');
                errorContainer.setAttribute('id', self.model.ComponentId + '_cmbTransferPriorityError_' + lastRowId);
                td.appendChild(select);
                td.appendChild(errorContainer);
                createPrioritySelect(self.model.ComponentId + '_cmbTransferPriority_' + lastRowId);
            }
            else if (c === 3) {
                var divParent = document.createElement('div');
                divParent.setAttribute("class", "input-group date staticFlatPickrDate");
                var dueDateInput = document.createElement('input');
                dueDateInput.setAttribute('type', 'text');
                dueDateInput.setAttribute('autocomplete', 'off');
                dueDateInput.setAttribute('id', self.model.ComponentId + '_transferDueDate_' + lastRowId);
                dueDateInput.setAttribute('class', 'form-control inputChange');
                dueDateInput.setAttribute('name', 'Date');
                var dueDateInputImg = document.createElement('span');
                dueDateInputImg.setAttribute('class', 'input-group-addon');
                dueDateInputImg.setAttribute('id', self.model.ComponentId + '_transferDueDate_img_' + lastRowId);
                dueDateInputImg.setAttribute('style', 'cursor:pointer');
                var dueDateInputImgIcon = document.createElement('i');
                dueDateInputImgIcon.setAttribute('class', 'fa fa-calendar');
                dueDateInputImgIcon.setAttribute('aria-hidden', 'true');
                dueDateInputImg.appendChild(dueDateInputImgIcon);
                divParent.appendChild(dueDateInput);
                divParent.appendChild(dueDateInputImg);
                td.appendChild(divParent);
                createDueDate(self.model.ComponentId + '_transferDueDate_' + lastRowId, self.model.ComponentId + '_transferDueDate_img_' + lastRowId, true);
            }
            else if (c === 4) {
                var divParent = document.createElement('div');
                divParent.setAttribute("class", "input-group");

                var textInput = document.createElement('textarea');
                textInput.setAttribute('id', self.model.ComponentId + '_transferInstruction_' + lastRowId);
                textInput.setAttribute('rows', '2');
                textInput.setAttribute('class', 'form-control inputChange InstructionTextArea');
                textInput.setAttribute('style', 'flex: 1; border-radius: 4px 0 0 4px;');
                divParent.appendChild(textInput);


                var instructionSpan = document.createElement('span');
                instructionSpan.setAttribute('id', self.model.ComponentId + '_transferInstruction_img_' + lastRowId);
                instructionSpan.setAttribute('class', 'input-group-addon triggerInstruction');
                instructionSpan.setAttribute('clickAttr', 'showHideInstruction(' + lastRowId + ')');
                instructionSpan.setAttribute('title', Resources.Instruction);
                instructionSpan.setAttribute('style', 'cursor:pointer; padding:revert-layer;');

                var instructionSpanIcon = document.createElement('i');
                instructionSpanIcon.setAttribute('class', 'fa fa-ellipsis-h');
                instructionSpanIcon.setAttribute('aria-hidden', 'true');
                instructionSpan.appendChild(instructionSpanIcon);
                divParent.appendChild(instructionSpan);

                var hiddenArea = '<textarea name="textarea" tabindex="-1" value="" class="hidden" id="' + self.model.ComponentId + '_hidden_Area_' + lastRowId + '"></textarea>'
                td.appendChild(divParent);
                td.appendChild($(hiddenArea)[0]);
            }
            else if (c === 5) {
                var checkbox = '<input id="' + self.model.ComponentId + '_checkInstructionPrivate_' + lastRowId + '" type="checkbox">';
                td.setAttribute('class', 'text-center');
                td.appendChild($(checkbox)[0]);
            }
            else if (c === 6) {
                td.setAttribute('id', self.model.ComponentId + '_transferCcedContainer_' + lastRowId);
                td.setAttribute('class', 'text-center');
                var ccedInput = document.createElement('input');
                ccedInput.setAttribute('type', 'checkbox');
                ccedInput.setAttribute('id', self.model.ComponentId + '_TransferCced_' + lastRowId);
                ccedInput.setAttribute('class', 'checkbox inputChange transferCcedCheckbox');
                ccedInput.setAttribute('name', 'Cced');
                td.appendChild(ccedInput);
                disableCcedCheckBox()

            }
            //else if (c === 7) {
            //    td.setAttribute('id', self.model.ComponentId + '_transferFollowUpContainer_' + lastRowId);
            //    td.setAttribute('class', 'text-center');
            //    var followUpInput = document.createElement('input');
            //    followUpInput.setAttribute('type', 'checkbox');
            //    followUpInput.setAttribute('id', self.model.ComponentId + '_TransferFollowUp_' + lastRowId);
            //    followUpInput.setAttribute('class', 'checkbox inputChange');
            //    followUpInput.setAttribute('name', 'FollowUp');
            //    td.appendChild(followUpInput);
            //}
            else if (c === 7) {
                td.setAttribute('class', 'text-center');
                var deleteButton = document.createElement('a');
                deleteButton.setAttribute('id', self.model.ComponentId + '_btnDeleteParam_' + lastRowId);
                deleteButton.setAttribute('class', 'btn btn-danger delete-row');
                deleteButton.setAttribute('title', Resources.Delete);
                deleteButton.setAttribute('clickattr', 'removeRow(this)');
                var deleteButtonSpan = document.createElement('span');
                deleteButtonSpan.setAttribute('class', 'fa fa-trash fa-fw');
                deleteButton.appendChild(deleteButtonSpan);
                td.appendChild(deleteButton);
            }

        }
        $(".inputChange").off().on("change", function (e) {
            let idCharacters = $(this).attr("id").split('_');
            let id = idCharacters[idCharacters.length - 1];

            if ($(this).attr("id").indexOf("_transferInstruction_") != -1) {
                if (typeof CKEDITOR.instances[self.model.ComponentId + '_txtAreaTransferInstruction'] === 'undefined') {

                    if (document.getElementById(self.model.ComponentId + '_txtAreaTransferInstruction') !== null) {
                        createCKEDITOR(document.getElementById(self.model.ComponentId + '_txtAreaTransferInstruction'));
                    }
                }

                var instructionInputVal = $(this).val();
                CKEDITOR.instances[self.model.ComponentId + "_txtAreaTransferInstruction"].setData(instructionInputVal);

                if ($(this).attr("id").indexOf("_Favorite_") == -1) {

                    $('#' + self.model.ComponentId + '_hidden_Area_' + id).val(instructionInputVal);
                    addRow(this);
                } else {
                    $('#' + self.model.ComponentId + '_hidden_Area_Favorite_' + id).val(instructionInputVal);
                    //addFavoriteRow(this);
                }
            }

            if ($(this).attr("name") && $(this).attr("name").indexOf('Cced') != -1) {

                updatePurposeDDL($(this));
            }

            //if ($(this).attr("name") && $(this).attr("name").indexOf('FollowUp') != -1) {

            //    if (this.checked) {
            //        if ($(this).attr("id").indexOf("_Favorite_") == -1) {
            //            $('#' + self.model.ComponentId + '_transferInstruction_' + id).attr('required', 'required');
            //            $('#' + self.model.ComponentId + '_InstructionTextDanger_' + id).removeClass('hidden').removeAttr("hidden");
            //        } else {
            //            $('#' + self.model.ComponentId + '_transferInstruction_Favorite_' + id).attr('required', 'required');
            //            $('#' + self.model.ComponentId + '_InstructionTextDanger_Favorite_' + id).removeClass('hidden').removeAttr("hidden");
            //        }



            //    } else {

            //        if ($(this).attr("id").indexOf("_Favorite_") == -1) {
            //            $('#' + self.model.ComponentId + '_transferInstruction_' + id).removeAttr('required');
            //            $('#' + self.model.ComponentId + '_InstructionTextDanger_' + id).addClass('hidden').attr('hidden', "hidden");


            //        } else {
            //            $('#' + self.model.ComponentId + '_transferInstruction_Favorite_' + id).removeAttr('required');
            //            $('#' + self.model.ComponentId + '_InstructionTextDanger_Favorite_' + id).addClass('hidden').attr('hidden', "hidden");

            //        }

            //    }
            //}
        });
        $("tr:last-child .purposeAll").removeClass("checkPurpose");

        if (transferRows.length > 1) {
            for (var i = 0; i < transferRows.length; i++) {
                if (transferRows[i] !== transferRows[transferRows.length - 1]) {
                    $('#' + self.model.ComponentId + '_btnDeleteParam_' + transferRows[i]).removeClass('hidden').removeAttr("hidden");
                } else {
                    $('#' + self.model.ComponentId + '_btnDeleteParam_' + transferRows[i]).addClass('hidden').attr('hidden', "hidden");
                }
            }
        }
        EventReceiver.AfterTransferAddRow(self,element);
    }
    else if ($(element).closest("tr").is(":last-child")) {
        $("tr:last-child .purposeAll").removeClass("checkPurpose");
    }
}
//function addFavoriteRow(element) {
//    var table = $(self.refs['grdFavoriteTransferItems'])[0];
//    var rowCnt = table.rows.length;
//    if ($(element).closest("tr").is(":last-child") && !$(element).hasClass('checkPurpose')) {
//        var tr = table.insertRow(rowCnt);
//        lastFavouriteRowId += 1;
//        transferFavouriteRows.push(lastFavouriteRowId);
//        var colCount = table.rows[0].cells.length;
//        for (var c = 0; c < colCount; c++) {
//            var td = document.createElement('td');
//            td = tr.insertCell(c);
//            if (c === 0) {
//                var div = document.createElement('div');
//                div.setAttribute('class', 'input-group');
//                td.setAttribute('id', self.model.ComponentId + '_transferToContainer_Favorite_' + lastFavouriteRowId);
//                var select = document.createElement('select');
//                select.setAttribute('id', self.model.ComponentId + '_cmbTransferTo_Favorite_' + lastFavouriteRowId);
//                var attRequired = $("#" + self.model.ComponentId + "_cmbTransferTo_Favorite_" + (lastFavouriteRowId - 1)).attr('required');
//                if (typeof attRequired === typeof undefined) {
//                    $("#" + self.model.ComponentId + "_cmbTransferTo_Favorite_" + (lastFavouriteRowId - 1)).attr('required', 'required');
//                }
//                select.setAttribute('data-parsley-errors-container', '#' + self.model.ComponentId + '_cmbTransferToError_Favorite_' + lastFavouriteRowId);
//                var errorContainer = document.createElement('div');
//                errorContainer.setAttribute('id', self.model.ComponentId + '_cmbTransferToError_Favorite_' + lastFavouriteRowId);

//                var toUserIdInput = '<input hidden class="hidden" id="' + self.model.ComponentId + '_hidden_toUserId_Favorite_' + lastFavouriteRowId + '">';
//                var toStructureIdInput = '<input hidden class="hidden" id="' + self.model.ComponentId + '_hidden_toStructureId_Favorite_' + lastFavouriteRowId + '">';
//                var distributionIdInput = '<input hidden class="hidden" id="' + self.model.ComponentId + '_hidden_distributionId_Favorite_' + lastFavouriteRowId + '">';


//                div.appendChild(select);
//                td.appendChild(div);
//                td.appendChild(errorContainer);
//                td.appendChild($(toUserIdInput)[0]);
//                td.appendChild($(toStructureIdInput)[0]);
//                td.appendChild($(distributionIdInput)[0]);
//                createTransferToFavoriteSelect(self.model.ComponentId + '_cmbTransferTo_Favorite_' + lastFavouriteRowId);
//            }
//            else if (c === 1) {
//                td.setAttribute('id', self.model.ComponentId + '_transferPurposeContainer_Favorite_' + lastFavouriteRowId);
//                var select = document.createElement('select');
//                select.setAttribute('id', self.model.ComponentId + '_cmbTransferPurpose_Favorite_' + lastFavouriteRowId);
//                select.setAttribute('class', 'form-control purposeAll');
//                var attRequired = $("#" + self.model.ComponentId + "_cmbTransferPurpose_Favorite_" + (lastFavouriteRowId - 1)).attr('required');
//                if (typeof attRequired === typeof undefined) {
//                    $("#" + self.model.ComponentId + "_cmbTransferPurpose_Favorite_" + (lastFavouriteRowId - 1)).attr('required', 'required');
//                }
//                select.setAttribute('data-parsley-errors-container', '#' + self.model.ComponentId + '_cmbTransferPurposeError_Favorite_' + lastFavouriteRowId);
//                var errorContainer = document.createElement('div');
//                errorContainer.setAttribute('id', self.model.ComponentId + '_cmbTransferPurposeError_Favorite_' + lastFavouriteRowId);
//                td.appendChild(select);
//                td.appendChild(errorContainer);
//                createPurposeSelect(self.model.ComponentId + '_cmbTransferPurpose_Favorite_' + lastFavouriteRowId);
//            }
//            else if (c === 2) {
//                td.setAttribute('id', self.model.ComponentId + '_transferPriorityContainer_Favorite_' + lastFavouriteRowId);
//                var select = document.createElement('select');
//                select.setAttribute('id', self.model.ComponentId + '_cmbTransferPriority_Favorite_' + lastFavouriteRowId);
//                select.setAttribute('class', 'form-control priorityAll');
//                var attRequired = $("#" + self.model.ComponentId + "_cmbTransferPriority_Favorite_" + (lastFavouriteRowId - 1)).attr('required');
//                if (typeof attRequired === typeof undefined) {
//                    $("#" + self.model.ComponentId + "_cmbTransferPriority_Favorite_" + (lastFavouriteRowId - 1)).attr('required', 'required');
//                }
//                select.setAttribute('data-parsley-errors-container', '#' + self.model.ComponentId + '_cmbTransferPriorityError_Favorite_' + lastFavouriteRowId);
//                var errorContainer = document.createElement('div');
//                errorContainer.setAttribute('id', self.model.ComponentId + '_cmbTransferPriorityError_Favorite_' + lastFavouriteRowId);
//                td.appendChild(select);
//                td.appendChild(errorContainer);
//                createPrioritySelect(self.model.ComponentId + '_cmbTransferPriority_Favorite_' + lastFavouriteRowId);
//            }
//            else if (c === 3) {
//                var divParent = document.createElement('div');
//                divParent.setAttribute("class", "input-group date staticFlatPickrDate");
//                var dueDateInput = document.createElement('input');
//                dueDateInput.setAttribute('type', 'text');
//                dueDateInput.setAttribute('autocomplete', 'off');
//                dueDateInput.setAttribute('id', self.model.ComponentId + '_transferDueDate_Favorite_' + lastFavouriteRowId);
//                dueDateInput.setAttribute('class', 'form-control inputChange');
//                dueDateInput.setAttribute('name', 'Date');
//                var dueDateInputImg = document.createElement('span');
//                dueDateInputImg.setAttribute('class', 'input-group-addon');
//                dueDateInputImg.setAttribute('id', self.model.ComponentId + '_transferDueDate_Favorite_img_' + lastFavouriteRowId);
//                dueDateInputImg.setAttribute('style', 'cursor:pointer');
//                var dueDateInputImgIcon = document.createElement('i');
//                dueDateInputImgIcon.setAttribute('class', 'fa fa-calendar');
//                dueDateInputImgIcon.setAttribute('aria-hidden', 'true');
//                dueDateInputImg.appendChild(dueDateInputImgIcon);
//                divParent.appendChild(dueDateInput);
//                divParent.appendChild(dueDateInputImg);
//                td.appendChild(divParent);
//                createDueDate(self.model.ComponentId + '_transferDueDate_Favorite_' + lastFavouriteRowId, self.model.ComponentId + '_transferDueDate_Favorite_img_' + lastFavouriteRowId);
//            }
//            else if (c === 4) {
//                var divParent = document.createElement('div');
//                divParent.setAttribute("class", "input-group");

//                var textInput = document.createElement('textarea');
//                textInput.setAttribute('id', self.model.ComponentId + '_transferInstruction_Favorite_' + lastFavouriteRowId);
//                textInput.setAttribute('rows', '2');
//                textInput.setAttribute('class', 'form-control inputChange InstructionTextArea');
//                textInput.setAttribute('style', 'flex: 1; border-radius: 4px 0 0 4px;');
//                divParent.appendChild(textInput);


//                var instructionSpan = document.createElement('span');
//                instructionSpan.setAttribute('id', self.model.ComponentId + '_transferInstruction_Favorite_img_' + lastFavouriteRowId);
//                instructionSpan.setAttribute('class', 'input-group-addon triggerInstruction');
//                instructionSpan.setAttribute('clickAttr', 'showHideInstruction(' + lastFavouriteRowId + ',true)');
//                instructionSpan.setAttribute('title', Resources.Instruction);
//                instructionSpan.setAttribute('style', 'cursor:pointer; padding:revert-layer;');

//                var instructionSpanIcon = document.createElement('i');
//                instructionSpanIcon.setAttribute('class', 'fa fa-ellipsis-h');
//                instructionSpanIcon.setAttribute('aria-hidden', 'true');
//                instructionSpan.appendChild(instructionSpanIcon);
//                divParent.appendChild(instructionSpan);

//                var hiddenArea = '<textarea name="textarea" tabindex="-1" value="" class="hidden" id="' + self.model.ComponentId + '_hidden_Area_Favorite_' + lastFavouriteRowId + '"></textarea>'
//                td.appendChild(divParent);
//                td.appendChild($(hiddenArea)[0]);
//            }
//            else if (c === 5) {
//                var checkbox = '<input id="' + self.model.ComponentId + '_checkInstructionPrivate_Favorite_' + lastFavouriteRowId + '" type="checkbox">';
//                td.setAttribute('class', 'text-center');
//                td.appendChild($(checkbox)[0]);
//            }
//            else if (c === 6) {
//                td.setAttribute('id', self.model.ComponentId + '_transferCcedContainer_Favorite_' + lastFavouriteRowId);
//                td.setAttribute('class', 'text-center');
//                var ccedInput = document.createElement('input');
//                ccedInput.setAttribute('type', 'checkbox');
//                ccedInput.setAttribute('id', self.model.ComponentId + '_TransferCced_Favorite_' + lastFavouriteRowId);
//                ccedInput.setAttribute('class', 'checkbox inputChange');
//                ccedInput.setAttribute('name', 'Cced');
//                td.appendChild(ccedInput);
//                disableCcedCheckBox()

//            }
//            //else if (c === 7) {
//            //    td.setAttribute('id', self.model.ComponentId + '_transferFollowUpContainer_Favorite_' + lastFavouriteRowId);
//            //    td.setAttribute('class', 'text-center');
//            //    var followUpInput = document.createElement('input');
//            //    followUpInput.setAttribute('type', 'checkbox');
//            //    followUpInput.setAttribute('id', self.model.ComponentId + '_TransferFollowUp_Favorite_' + lastFavouriteRowId);
//            //    followUpInput.setAttribute('class', 'checkbox inputChange');
//            //    followUpInput.setAttribute('name', 'FollowUp');
//            //    td.appendChild(followUpInput);
//            //}
//            else if (c === 7) {
//                td.setAttribute('class', 'text-center');
//                var deleteButton = document.createElement('a');
//                deleteButton.setAttribute('id', self.model.ComponentId + '_btnDeleteParam_Favorite_' + lastFavouriteRowId);
//                deleteButton.setAttribute('class', 'btn btn-danger delete-row');
//                deleteButton.setAttribute('title', Resources.Delete);
//                deleteButton.setAttribute('clickattr', 'removeFavoriteRow(this)');
//                var deleteButtonSpan = document.createElement('span');
//                deleteButtonSpan.setAttribute('class', 'fa fa-trash fa-fw');
//                deleteButton.appendChild(deleteButtonSpan);
//                td.appendChild(deleteButton);
//            }
//        }
//       //checkShowHideCcColumn();
//        $(".inputChange").off().on("change", function (e) {
//            let idCharacters = $(this).attr("id").split('_');
//            let id = idCharacters[idCharacters.length - 1];
//            if ($(this).attr("id").indexOf("_transferInstruction_") != -1) {
//                if (typeof CKEDITOR.instances[self.model.ComponentId + '_txtAreaTransferInstruction'] === 'undefined') {

//                    if (document.getElementById(self.model.ComponentId + '_txtAreaTransferInstruction') !== null) {
//                        createCKEDITOR(document.getElementById(self.model.ComponentId + '_txtAreaTransferInstruction'));
//                    }
//                }

//                var instructionInputVal = $(this).val();
//                CKEDITOR.instances[self.model.ComponentId + "_txtAreaTransferInstruction"].setData(instructionInputVal);

//                if ($(this).attr("id").indexOf("_Favorite_") == -1) {

//                    $('#' + self.model.ComponentId + '_hidden_Area_' + id).val(instructionInputVal);
//                    addRow(this);
//                } else {
//                    $('#' + self.model.ComponentId + '_hidden_Area_Favorite_' + id).val(instructionInputVal);
//                    addFavoriteRow(this);
//                }
//            }

//            if ($(this).attr("name") && $(this).attr("name").indexOf('Cced') != -1) {

//                updatePurposeDDL($(this)); 2
//            }

//            //if ($(this).attr("name") && $(this).attr("name").indexOf('FollowUp') != -1) {

//            //    if (this.checked) {
//            //        if ($(this).attr("id").indexOf("_Favorite_") == -1) {
//            //            $('#' + self.model.ComponentId + '_transferInstruction_' + id).attr('required', 'required');
//            //            $('#' + self.model.ComponentId + '_InstructionTextDanger_' + id).removeClass('hidden').removeAttr("hidden");
//            //        } else {
//            //            $('#' + self.model.ComponentId + '_transferInstruction_Favorite_' + id).attr('required', 'required');
//            //            $('#' + self.model.ComponentId + '_InstructionTextDanger_Favorite_' + id).removeClass('hidden').removeAttr("hidden");
//            //        }



//            //    } else {

//            //        if ($(this).attr("id").indexOf("_Favorite_") == -1) {
//            //            $('#' + self.model.ComponentId + '_transferInstruction_' + id).removeAttr('required');
//            //            $('#' + self.model.ComponentId + '_InstructionTextDanger_' + id).addClass('hidden').attr('hidden', "hidden");


//            //        } else {
//            //            $('#' + self.model.ComponentId + '_transferInstruction_Favorite_' + id).removeAttr('required');
//            //            $('#' + self.model.ComponentId + '_InstructionTextDanger_Favorite_' + id).addClass('hidden').attr('hidden', "hidden");

//            //        }

//            //    }
//            //}

//        });
//        $("tr:last-child .purposeAll").removeClass("checkPurpose");

//        if (transferFavouriteRows.length > 1) {
//            $('#' + self.model.ComponentId + '_btnDeleteParam_Favorite_' + hiddenFavouriteRowId).removeClass('hidden').removeAttr("hidden");
//            hiddenFavouriteRowId = '';
//        }

//    } else if ($(element).closest("tr").is(":last-child")) {
//        $("tr:last-child .purposeAll").removeClass("checkPurpose");
//    }
//}
function addRowForMeeting(element, userStructureId, name) {
    var table = $(self.refs['grdTransferItems'])[0];
    if ($(element).closest("tr").is(":last-child") && !$(element).hasClass('checkPurpose')) {
        table.deleteRow(lastRowId - 1);
        var rowCnt = table.rows.length;
        var tr = table.insertRow(rowCnt);
        // lastRowId += 1;
        //  transferRows.push(lastRowId);
        var colCount = table.rows[0].cells.length;
        for (var c = 0; c < colCount; c++) {
            var td = document.createElement('td');
            td = tr.insertCell(c);
            if (c === 0) {
                var div = document.createElement('div');
                div.setAttribute('class', 'input-group');
                td.setAttribute('id', self.model.ComponentId + '_transferToContainer_' + lastRowId);
                var select = document.createElement('select');
                select.setAttribute('id', self.model.ComponentId + '_cmbTransferTo_' + lastRowId)
                var attRequired = $("#" + self.model.ComponentId + "_cmbTransferTo_" + (lastRowId)).attr('required'); //-1
                if (typeof attRequired === typeof undefined) {
                    $("#" + self.model.ComponentId + "_cmbTransferTo_" + (lastRowId)).attr('required', 'required');//-1
                }
                select.setAttribute('data-parsley-errors-container', '#' + self.model.ComponentId + '_cmbTransferToError_' + lastRowId);
                var span = document.createElement('span');
                span.setAttribute('class', 'input-group-addon triggerAddressBook');
                span.setAttribute('id', self.model.ComponentId + '_transferToAddressBook_' + lastRowId);
                span.setAttribute('clickAttr', 'openTransferAddressBook(' + lastRowId + ')');
                span.setAttribute('style', 'cursor:pointer');
                var i = document.createElement('i');
                i.setAttribute('class', 'fa fa-address-book-o');
                i.setAttribute('aria-hidden', 'true');
                var errorContainer = document.createElement('div');
                errorContainer.setAttribute('id', self.model.ComponentId + '_cmbTransferToError_' + lastRowId);
                span.appendChild(i);
                div.appendChild(select);
                div.appendChild(span);
                td.appendChild(div);
                td.appendChild(errorContainer);


                let userIdValue = userStructureId;  //'User_' + self.model.structureId + '_' + self.model.userId;
                var option = document.createElement('option');
                option.value = userIdValue;
                option.text = name;
                option.setAttribute('data-dataId', userIdValue);
                option.setAttribute('data-isStructure', true);
                select.add(option);
                $("#" + self.model.ComponentId + "_cmbTransferTo_" + (lastRowId)).val(userStructureId).change();

                createTransferToSelect(self.model.ComponentId + '_cmbTransferTo_' + lastRowId);


            }
            else if (c === 1) {
                td.setAttribute('id', self.model.ComponentId + '_transferPurposeContainer_' + lastRowId);
                var select = document.createElement('select');
                select.setAttribute('id', self.model.ComponentId + '_cmbTransferPurpose_' + lastRowId);
                select.setAttribute('class', 'form-control purposeAll');
                var attRequired = $("#" + self.model.ComponentId + "_cmbTransferPurpose_" + (lastRowId)).attr('required');//-1
                if (typeof attRequired === typeof undefined) {
                    $("#" + self.model.ComponentId + "_cmbTransferPurpose_" + (lastRowId)).attr('required', 'required');//-1
                }
                select.setAttribute('data-parsley-errors-container', '#' + self.model.ComponentId + '_cmbTransferPurposeError_' + lastRowId);
                var errorContainer = document.createElement('div');
                errorContainer.setAttribute('id', self.model.ComponentId + '_cmbTransferPurposeError_' + lastRowId);
                td.appendChild(select);
                td.appendChild(errorContainer);
                createPurposeSelect(self.model.ComponentId + '_cmbTransferPurpose_' + lastRowId);

                $("#" + self.model.ComponentId + "_cmbTransferPurpose_" + (lastRowId)).val(window.CommentsPurposeId).change();

            }
            else if (c === 2) {
                td.setAttribute('id', self.model.ComponentId + '_transferPriorityContainer_' + (lastRowId - 1));
                var select = document.createElement('select');
                select.setAttribute('id', self.model.ComponentId + '_cmbTransferPriority_' + (lastRowId - 1));
                select.setAttribute('class', 'form-control priorityAll');
                var attRequired = $("#" + self.model.ComponentId + "_cmbTransferPriority_" + (lastRowId)).attr('required');//-1
                if (typeof attRequired === typeof undefined) {
                    $("#" + self.model.ComponentId + "_cmbTransferPriority_" + ((lastRowId - 1))).attr('required', 'required');//-1
                }
                select.setAttribute('data-parsley-errors-container', '#' + self.model.ComponentId + '_cmbTransferPriorityError_' + (lastRowId - 1));
                var errorContainer = document.createElement('div');
                errorContainer.setAttribute('id', self.model.ComponentId + '_cmbTransferPriorityError_' + (lastRowId - 1));
                td.appendChild(select);
                td.appendChild(errorContainer);
                createPrioritySelect(self.model.ComponentId + '_cmbTransferPriority_' + (lastRowId - 1), true);

            }
            else if (c === 3) {
                var divParent = document.createElement('div');
                divParent.setAttribute("class", "input-group date staticFlatPickrDate");
                var dueDateInput = document.createElement('input');
                dueDateInput.setAttribute('type', 'text');
                dueDateInput.setAttribute('autocomplete', 'off');
                dueDateInput.setAttribute('id', self.model.ComponentId + '_transferDueDate_' + (lastRowId - 1));
                dueDateInput.setAttribute('class', 'form-control inputChange');
                dueDateInput.setAttribute('name', 'Date');
                var dueDateInputImg = document.createElement('span');
                dueDateInputImg.setAttribute('class', 'input-group-addon');
                dueDateInputImg.setAttribute('id', self.model.ComponentId + '_transferDueDate_img_' + (lastRowId - 1));
                dueDateInputImg.setAttribute('style', 'cursor:pointer');
                var dueDateInputImgIcon = document.createElement('i');
                dueDateInputImgIcon.setAttribute('class', 'fa fa-calendar');
                dueDateInputImgIcon.setAttribute('aria-hidden', 'true');
                dueDateInputImg.appendChild(dueDateInputImgIcon);
                divParent.appendChild(dueDateInput);
                divParent.appendChild(dueDateInputImg);
                td.appendChild(divParent);
                createDueDate(self.model.ComponentId + '_transferDueDate_' + (lastRowId - 1), self.model.ComponentId + '_transferDueDate_img_' + (lastRowId - 1), true);
            } else if (c === 4) {


                var divParent = document.createElement('div');
                divParent.setAttribute("class", "input-group");

                var textInput = document.createElement('textarea');
                textInput.setAttribute('id', self.model.ComponentId + '_transferInstruction_' + (lastRowId - 1));
                textInput.setAttribute('rows', '2');
                textInput.setAttribute('class', 'form-control inputChange InstructionTextArea');
                textInput.setAttribute('style', 'flex: 1; border-radius: 4px 0 0 4px;');
                divParent.appendChild(textInput);


                var instructionSpan = document.createElement('span');
                instructionSpan.setAttribute('title', Resources.Instruction);
                instructionSpan.setAttribute('id', self.model.ComponentId + '_transferInstruction_img_' + (lastRowId - 1));

                instructionSpan.setAttribute('class', 'input-group-addon triggerInstruction');
                instructionSpan.setAttribute('clickAttr', 'showHideInstruction(' + (lastRowId - 1) + ')');
                instructionSpan.setAttribute('style', 'cursor:pointer; padding: revert-layer;');

                var instructionSpanIcon = document.createElement('i');
                instructionSpanIcon.setAttribute('class', 'fa fa-ellipsis-h');
                instructionSpanIcon.setAttribute('aria-hidden', 'true');
                instructionSpan.appendChild(instructionSpanIcon);
                divParent.appendChild(instructionSpan);

                var hiddenArea = '<textarea name="textarea" tabindex="-1" value="" class="hidden" id="' + self.model.ComponentId + '_hidden_Area_' + (lastRowId - 1) + '"></textarea>'
                td.appendChild(divParent);
                td.appendChild($(hiddenArea)[0]);
            }
            else if (c === 5) {
                var checkbox = '<input id="' + self.model.ComponentId + '_checkInstructionPrivate_' + lastRowId + '" type="checkbox">';
                td.setAttribute('class', 'text-center');
                td.appendChild($(checkbox)[0]);
            }
            else if (c === 6) {
                td.setAttribute('id', self.model.ComponentId + '_transferCcedContainer_' + (lastRowId - 1));
                td.setAttribute('class', 'text-center');

                var ccedInput = document.createElement('input');
                ccedInput.setAttribute('type', 'checkbox');
                ccedInput.setAttribute('id', self.model.ComponentId + '_TransferCced_' + (lastRowId - 1));
                ccedInput.setAttribute('class', 'checkbox inputChange');
                ccedInput.setAttribute('name', 'Cced');
                td.appendChild(ccedInput);
                disableCcedCheckBox()
            }
            //else if (c === 7) {
            //    td.setAttribute('id', self.model.ComponentId + '_transferFollowUpContainer_' + (lastRowId - 1));
            //    td.setAttribute('class', 'text-center');

            //    var followUpInput = document.createElement('input');
            //    followUpInput.setAttribute('type', 'checkbox');
            //    followUpInput.setAttribute('id', self.model.ComponentId + '_TransferFollowUp_' + (lastRowId - 1));
            //    followUpInput.setAttribute('class', 'checkbox inputChange');
            //    followUpInput.setAttribute('name', 'FollowUp');
            //    td.appendChild(followUpInput);
            //}
            else if (c === 7 && rowCnt > 1) {
                td.setAttribute('class', 'text-center');
                var deleteButton = document.createElement('a');
                deleteButton.setAttribute('id', self.model.ComponentId + '_btnDeleteParam_' + (lastRowId - 1));
                deleteButton.setAttribute('class', 'btn btn-danger delete-row');
                deleteButton.setAttribute('title', Resources.Delete);
                deleteButton.setAttribute('clickattr', 'removeRow(this)');
                var deleteButtonSpan = document.createElement('span');
                deleteButtonSpan.setAttribute('class', 'fa fa-trash fa-fw');
                deleteButton.appendChild(deleteButtonSpan);
                td.appendChild(deleteButton);
            }
            else {
                //td.setAttribute('class', 'text-center');
                //var ccIcon = document.createElement('i');
                //ccIcon.setAttribute('class', 'fa fa-cc font-16 hidden');
                //td.appendChild(ccIcon);
            }
        }
        //checkShowHideCcColumn();
        $(".inputChange").off().on("change", function (e) {
            let idCharacters = $(this).attr("id").split('_');
            let id = idCharacters[idCharacters.length - 1];
            if ($(this).attr("id").indexOf("_transferInstruction_") != -1) {
                if (typeof CKEDITOR.instances[self.model.ComponentId + '_txtAreaTransferInstruction'] === 'undefined') {

                    if (document.getElementById(self.model.ComponentId + '_txtAreaTransferInstruction') !== null) {
                        createCKEDITOR(document.getElementById(self.model.ComponentId + '_txtAreaTransferInstruction'));
                    }
                }

                var instructionInputVal = $(this).val();

                CKEDITOR.instances[self.model.ComponentId + "_txtAreaTransferInstruction"].setData(instructionInputVal);

                if ($(this).attr("id").indexOf("_Favorite_") == -1) {

                    $('#' + self.model.ComponentId + '_hidden_Area_' + id).val(instructionInputVal);
                    addRow(this);
                } else {
                    $('#' + self.model.ComponentId + '_hidden_Area_Favorite_' + id).val(instructionInputVal);
                    //addFavoriteRow(this);
                }
            }

            if ($(this).attr("name") && $(this).attr("name").indexOf('Cced') != -1) {
                updatePurposeDDL($(this));
            }

            if ($(this).attr("name") && $(this).attr("name").indexOf('FollowUp') != -1) {

                if (this.checked) {
                    if ($(this).attr("id").indexOf("_Favorite_") == -1) {
                        $('#' + self.model.ComponentId + '_transferInstruction_' + id).attr('required', 'required');
                        $('#' + self.model.ComponentId + '_InstructionTextDanger_' + id).removeClass('hidden').removeAttr("hidden");
                    } else {
                        $('#' + self.model.ComponentId + '_transferInstruction_Favorite_' + id).attr('required', 'required');
                        $('#' + self.model.ComponentId + '_InstructionTextDanger_Favorite_' + id).removeClass('hidden').removeAttr("hidden");
                    }



                } else {

                    if ($(this).attr("id").indexOf("_Favorite_") == -1) {
                        $('#' + self.model.ComponentId + '_transferInstruction_' + id).removeAttr('required');
                        $('#' + self.model.ComponentId + '_InstructionTextDanger_' + id).addClass('hidden').attr('hidden', "hidden");


                    } else {
                        $('#' + self.model.ComponentId + '_transferInstruction_Favorite_' + id).removeAttr('required');
                        $('#' + self.model.ComponentId + '_InstructionTextDanger_Favorite_' + id).addClass('hidden').attr('hidden', "hidden");

                    }

                }
            }


        });
        $("tr:last-child .purposeAll").removeClass("checkPurpose");
    }
    else if ($(element).closest("tr").is(":last-child")) {
        $("tr:last-child .purposeAll").removeClass("checkPurpose");
    }
}
function removeRow(oButton) {
    var table = $(self.refs['grdTransferItems'])[0];

    let rowIndex = oButton.parentNode.parentNode.rowIndex;
    table.deleteRow(rowIndex);
    var index = transferRows.indexOf(parseInt(oButton.id.split("_")[3]));
    transferRows.splice(index, 1);
    var elems = $("tr:last-child .purposeAll");
    $("tr:last-child .purposeAll").removeClass("checkPurpose");
    EventReceiver.AfterTransferDeleteRow(self, rowIndex);
}
function removeFavoriteRow(oButton) {
    var table = $(self.refs['grdFavoriteTransferItems'])[0];
    table.deleteRow(oButton.parentNode.parentNode.rowIndex);
    var index = transferFavouriteRows.indexOf(parseInt(oButton.id.split("_")[4]));
    transferFavouriteRows.splice(index, 1);
    var elems = $("tr:last-child .purposeAll");
    $("tr:last-child .purposeAll").removeClass("checkPurpose");

    if (transferFavouriteRows.length == 1) {
        lastFavouriteRowId = transferFavouriteRows[0];
        hiddenFavouriteRowId = lastFavouriteRowId.toString();
        $('#' + self.model.ComponentId + '_btnDeleteParam_Favorite_' + hiddenFavouriteRowId).addClass('hidden').attr('hidden', "hidden");
    }
}
function clearRow(clearRow) {
    clearRowClicked = true;
    $(`#${self.model.ComponentId}_cmbTransferTo_2`).val(null).trigger('change');
    $(`#${self.model.ComponentId}_cmbTransferPurpose_2`).val(null).trigger('change');
    $(`#${self.model.ComponentId}_cmbTransferPriority_2`).val(null).trigger('change');
    $(`#${self.model.ComponentId}_transferDueDate_2`).flatpickr({
        noCalendar: false,
        enableTime: false,
        dateFormat: 'd/m/Y',
        minDate: "today",
        maxDate: self.model.maxDueDate
    }).clear();
    $(`#${self.model.ComponentId}_hidden_Area_2`).val('');
    $(`#${self.model.ComponentId}_transferInstruction_2`).val('');
    clearRowClicked = false;
}
function checkShowHideCcColumn(isFavorite = false) {
    var tableGrid = $(self.refs['grdTransferItems']);
    var rows = tableGrid.children().children();
    var noCced = true;
    for (var i = 1; i < rows.length; i++) {
        noCced = $(rows[i]).children().last().children('.hidden').length > 0;
        if (!noCced) {
            break;
        }
    }
    if (noCced) {
        for (var i = 0; i < rows.length; i++) {
            $(rows[i]).children().last().addClass('hidden');
        }
    } else {
        for (var i = 0; i < rows.length; i++) {
            $(rows[i]).children().last().removeClass('hidden');
        }
    }
}
function openTransferAddressBook(id) {
    var callback = function (data) {
        var item = {};
        if (data.userId !== null) {
            //if (Number(data.userId) !== Number(self.model.userId)) {
                item.id = "User_" + data.structureId + "_" + data.userId;
                var currentStructure = new CoreComponents.Lookup.Structure().getWithCaching(data.structureId, window.language);
                var structureName = currentStructure;
                if (currentStructure.attributes != null && currentStructure.attributes.length > 0) {
                    var attributeLang = $.grep(currentStructure.attributes, function (e) {
                        return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                    });
                    if (attributeLang.length > 0) {
                        structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                    }
                }
                item.text = structureName + " / " + data.name;
                item.icon = "fa fa-user-o";
                item.isStructure = false;
                item.dataId = data.userId;
                item.structureId = data.structureId;
            
        } else {
            item.id = "Structure" + data.structureId;
            item.text = data.name;
            item.icon = "fa fa-building-o";
            item.isStructure = true;
            item.dataId = data.structureId;
        }
        if (typeof item.id !== 'undefined') {
            $("#" + self.model.ComponentId + "_cmbTransferTo_" + id).append($('<option />')
                .val(item.id).text(item.text).prop('selected', true).data(item)).trigger("change");
        }
        $('.modalAddressBook').modal('hide');
    };
    let modalWrapper = $(".modal-window");
    let view = new AddressBook(self.model.structureId, self.model.enableSendingRules, self.model.isStructureSender,
        self.model.enableTransferToUsers, AddressBookMode.InternalWithUsers, AddressBookSelectionMode.Single, self.model.delegationId, StructureTypeMode.Internal, false, callback, modalWrapper);
    view.render();
    $('.modalAddressBook').modal('show');
    $(".modalAddressBook").off("hidden.bs.modal");
    $(".modalAddressBook").off("shown.bs.modal");
    $('.modalAddressBook').on('hidden.bs.modal', function () {
        swal.close();
        $(".modalAddressBook").parent().remove();
        $('body').addClass('modal-open');
    });
}
function checkIfExist(transferArray, structureId, userId) {
    for (var i = 0; i < transferArray.length; i++) {
        if ((transferArray[i].toUserId === null && structureId === transferArray[i].toStructureId && userId === null)
            || (transferArray[i].toUserId !== null && userId === transferArray[i].toUserId && structureId === transferArray[i].toStructureId)) {
            return true;
        }
    }
    return false;
}
function disableCcedCheckBox() {
    let hasCced = purposes.some(item => item.cCed === true);
    let elements = document.querySelectorAll('[name*="Cced');
    if (!hasCced)
        elements.forEach(element => { element.disabled = true; });
    else
        elements.forEach(element => { element.disabled = false; });
}
function updatePurposeDDL(checkboxElment) {
    if (checkboxElment.attr("name").indexOf('Cced') != -1) {
        var trPurposeDDL = checkboxElment[0].closest("tr");
        var purposeDDL = trPurposeDDL.querySelectorAll('td .purposeAll')[0];

        if (checkboxElment[0].checked) {

            var allRows = Array.from(trPurposeDDL.parentElement.children);
            var isLastChild = allRows[allRows.length - 1] === trPurposeDDL;

            if (isLastChild) {
                purposeDDL.classList.add('checkPurpose');
            }
            var ccedPurpose = $.grep(purposes, function (e) {
                return e.cCed === true;
            });

            purposeDDL.value = ccedPurpose[0].id;
            purposeDDL.disabled = true;
            $('.purposeAll').trigger('change');

        } else {
            purposeDDL.value = '';
            purposeDDL.disabled = false;
            $('.purposeAll').trigger('change');
            //trPurposeDDL.children[trPurposeDDL.children.length - 1].children[0].classList.add('hidden');
        }
    }
}
function cacheStructures() {
    var currentStructures = (new CoreComponents.Lookup.Structure()).structures;
    if (currentStructures.length > 0)
        return;

    var url = window.IdentityUrl + '/api/GetUsersAndStructuresWithSearchAttributes';
    var headers = {};
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    var params = {
        "text": "",
        "language": window.language,
        "attributes": [window.StructureNameAr, window.StructureNameFr], "showOnlyActiveUsers": true
    }
    Common.ajaxPostWithHeaders(url, null, function (data) {
        var allUSersWithStructures = getUsersWithStructureOptions(data);
    }, function (msg) {
        console.log(msg);
    }, false, "", headers);
}
class TransferView extends Intalio.View {
    constructor(structureId, userId, structureIds, isStructureSender, enableSendingRules,
        enableTransferToUsers, maxDueDate, delegationId, callBack, element,
        allowMaintainTransfer = true, selectedReceivers, gIsDueDateRelatedToPriority, prioritiesList, documentId,fromSignAndTransfer,fromSignAndReply,isSigned) {

        priorities = prioritiesList;

        var model = new Transfer();   // model here is an object of type Transfer and considered as a propertry in TransferView >> meaning that: 
        //Transfer class properties can be called in TransferView class using the model variable

        if (enableTransferToUsers === true) {
            model.enableTransferToUsers = enableTransferToUsers;
        }
        if (isStructureSender === true) {
            model.isStructureSender = isStructureSender;
        }
        if (enableSendingRules === true) {
            model.enableSendingRules = enableSendingRules;
        }
        if (gIsDueDateRelatedToPriority === true) {
            model.gIsDueDateRelatedToPriority = gIsDueDateRelatedToPriority;
        }
        model.structureIds = structureIds;
        model.structureId = structureId;
        model.userId = userId;
        model.maxDueDate = maxDueDate;
        model.callBack = callBack;
        model.delegationId = delegationId;
        model.allowMaintainTransfer = allowMaintainTransfer;
        model.selectedReceivers = selectedReceivers;
        model.documentId = documentId;
        model.favoriteStructures = new FavoriteStructure.FavoriteStructures()
            .getFavoritesStructuresPerUser(structureId, delegationId);
        model.fromSignAndTransfer = fromSignAndTransfer;
        model.fromSignAndReply = fromSignAndReply;
        model.isSigned = isSigned;

        super(element, "transfer", model);
    }

    render() {

        if (this.model.allowMaintainTransfer == false) {
            $("#maintainTransferSection").hide()
        }
        if (this.model.allowMaintainTransfer == true) {
            $("#maintainTransferSection").show()
        }
        if (this.model.isSigned) { 
                $("#maintainTransferSection").show()
                $('#chkMaintainTransfer').prop('checked', true);
            }

        if (this.model.fromSignAndTransfer) {
            $("#maintainTransferSection").show()
            $('#chkMaintainTransfer').prop('checked', true);
        }
        
        self = this;  // self is a TransferView object having model)
      
        lastRowId = 2;
        lastFavouriteRowId = 2;
        hiddenFavouriteRowId = '2';

        purposes = new Helper().getPurpose();
        cacheStructures();
        disableCcedCheckBox();
        //priorities = new CoreComponents.Lookup.Priorities().get(window.language);

        disableCcedCheckBox()
        ////////////////////////
        //Common.ajaxGet('CTS/Purpose/ListUserPurposes', null, function (data) {
        //    purposes = data;
        //}, function () { Common.showScreenErrorMsg(); }, null, null, false);

        ////////////////////////
        $.fn.select2.defaults.set("theme", "bootstrap");
        $(self.refs['selectPurpose']).select2(
            {
                placeholder: Resources.Purpose,
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                dropdownParent: $(self.refs['purposeContainer']),
                data: purposes,
            });

        $(self.refs['selectPurpose']).val("").trigger("change");
        $('#grdFavoriteTransferContainer').dataTable({
            searching: false,
            info: false,
            pageLength: 5,
            ordering: false,
            "drawCallback": function () {
                $.each($('[ref="favoritesPurpose"]'), function (key, value) {
                    $(value).select2(
                        {
                            placeholder: Resources.Purpose,
                            dir: window.language === "ar" ? "rtl" : "ltr",
                            language: window.language,
                            width: "100%",
                            dropdownParent: $(self.refs['purposeContainer']),
                            data: purposes,
                        });
                    $(value).val("").trigger("change");
                });
                $("[ref='AddFavorites']").on("click", function () {
                    fromFavorites = true;
                    var selectedCheckboxes = $("#" + self.model.ComponentId + "_favoritesGrid").find("input[type='checkbox']:checked");
                    var itemId = $(this).attr('data-id');
                    var item = listFavoriteStructuresData.filter(x => x.dataId == itemId)[0];

                    var selectedFavPurpose = $("#_cmbTransferPurpose_Favorite_" + itemId).val();
                    //this;
                    //if (selectedCheckboxes.length === 0) {
                    //    Common.alertMsg(Resources.NoItemsSelected);
                    //    return;
                    //} else {

                    var instructionData = $('#' + self.model.ComponentId + '_hidden_Area_Favorite_' + itemId).val();

                    if (instructionData) {
                        const instructionFavoriteRawText = $('#' + self.model.ComponentId + '_transferInstruction_Favorite_' + itemId).val();
                        $('#' + self.model.ComponentId + '_hidden_Area_' + lastRowId).val(instructionFavoriteRawText);
                        CKEDITOR.instances[self.model.ComponentId + "_txtAreaTransferInstruction"].setData(instructionData);
                        $('#' + self.model.ComponentId + '_transferInstruction_' + lastRowId).val(instructionFavoriteRawText);
                    }
                    if (!selectedFavPurpose) {
                        Common.alertMsg(Resources.SelectPurpose);
                        return;
                    }
                    var transferToObj;
                    for (var i = 0; i < transferRows.length; i++) {
                        var transferToObj = $('#' + self.model.ComponentId + '_cmbTransferTo_' + transferRows[i]).select2('data');
                        if (transferToObj.length > 0 && transferToObj[0].toStructureId == item.toStructureId && transferToObj[0].toUserId == item.toUserId) {
                            Common.alertMsg(Resources.AlreadyExists);
                            return;
                        }
                    }
                    //}

                    //selectedCheckboxes.each(function (index) {

                    item.selected = true;
                    var newarray = [];
                    newarray.push(item);

                    var newSelectToId = self.model.ComponentId + "_cmbTransferTo_" + lastRowId;  // lastRowId starts with 2 in the Transfer class
                    var transferToDropdown = $("#" + newSelectToId);

                    var newaddrressbookId = self.model.ComponentId + "_transferToAddressBook_" + lastRowId;
                    // This represents the address book span with the TansferTo
                    var transferAddressBook = $("#" + newaddrressbookId);

                    if (transferToDropdown.find(`option[value='${itemId}']`).length === 0) {

                        updateSelect2FavoriteData(transferToDropdown, newarray)
                        transferToDropdown.attr('disabled', 'disabled');
                        // transferAddressBook.prop('disabled', true);
                        transferAddressBook.css({
                            cursor: 'default',        // Makes the cursor look inactive, This disables the pointor cursor on the addressBook span element
                            'pointer-events': 'none'  // Disables all mouse interactions like clicks
                        });
                    }


                    if (selectedFavPurpose) {

                        var newPurposeId = self.model.ComponentId + "_cmbTransferPurpose_" + lastRowId;

                        $("#" + newPurposeId).val(selectedFavPurpose).trigger("change");
                    }
                    //addRow(this.nextSibling);   // Adding a new row increments lastRowId by 1 in the new row
                    // So We add the first Favorite element selected of index 0 in the already existing row, then We add a new row to add the rest of the checked elements.

                    //});

                    fromFavorites = false;

                    selectedCheckboxes.prop("checked", false);
                    transferToDropdown.trigger('change');

                    $("#" + self.model.ComponentId + "_cmbTransferPurpose_Favorite_" + itemId).val("").trigger("change");
                });

            }
        });
        //$.each($('[ref="favoritesPurpose"]'), function (key, value) {
        //    $(value).select2(
        //        {
        //            placeholder: Resources.Purpose,
        //            dir: window.language === "ar" ? "rtl" : "ltr",
        //            language: window.language,
        //            width: "100%",
        //            dropdownParent: $(self.refs['purposeContainer']),
        //            data: purposes,
        //            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        //        });
        //    $(value).val("").trigger("change");
        //});
        createTransferToSelect(self.model.ComponentId + "_cmbTransferTo_2");
        createPurposeSelect(self.model.ComponentId + "_cmbTransferPurpose_2");
        createPrioritySelect(self.model.ComponentId + "_cmbTransferPriority_2");
        createDueDate(self.model.ComponentId + "_transferDueDate_2", self.model.ComponentId + "_transferDueDate_img", true);

        if (self.model.favoriteStructures.length > 0) {

            $.each(self.model.favoriteStructures, function (key, val) {
                if (val.distributionId > 0) {
                    let distributionData = new Distribution.DistributionList().getMyDistribution(val.distributionId, self.model.delegationId, self.model.structureId);
                    distributionData.dataId = val.id;
                    distributionData.order = val.order;
                    distributionData.toUserId = val.toUserId.toString();
                    distributionData.toStructureId = val.toStructureId.toString();
                    distributionData.structureId = val.toStructureId;
                    listFavoriteStructuresData.push(distributionData);

                } else {
                    var item = {};
                    item.id = val.toUserId > 0 ? "User_" + val.toStructureId.toString() + "_" + val.toUserId.toString() : "Structure" + val.toStructureId.toString();
                    item.text = val.name;
                    item.selected = false;
                    item.icon = "fa fa-building-o";
                    item.isStructure = val.toUserId > 0 ? false : true;
                    item.dataId = val.id;
                    item.order = val.order;
                    item.toUserId = val.toUserId.toString();
                    item.toStructureId = val.toStructureId.toString();
                    item.structureId = val.toStructureId;
                    item.distributionId = val.distributionId.toString();
                    listFavoriteStructuresData.push(item);
                }
            });

            createPurposeSelect(self.model.ComponentId + "_cmbTransferPurpose_Favorite_2");
        }

        transferRows = [];
        transferRows.push(2);

        transferFavouriteRows = [];
        transferFavouriteRows.push(2);
        $(self.refs['formTransferPost']).keydown(function (e, elemnt) {
      
            var code = e.keyCode || e.which;
            if (code === 13 && e.target.classList.contains('purposeAll') &&((e && e.target && e.target.classList) ? e.target.classList[0] !== "jqte_editor" : true)) {

                e.preventDefault();

                if (e.target.classList.contains('InstructionTextArea')) {
                    e.target.value += '\n';
                } else
                    $(self.refs['btnTransfer']).trigger("click");
            }
        });
        $(".inputChange").on("change", function (e) {
            let idCharacters = $(this).attr("id").split('_');
            let id = idCharacters[idCharacters.length - 1];
            if ($(this).attr("id").indexOf("_transferInstruction_") != -1) {
                if (typeof CKEDITOR.instances[self.model.ComponentId + '_txtAreaTransferInstruction'] === 'undefined') {

                    if (document.getElementById(self.model.ComponentId + '_txtAreaTransferInstruction') !== null) {
                        createCKEDITOR(document.getElementById(self.model.ComponentId + '_txtAreaTransferInstruction'));
                    }
                }

                var instructionInputVal = $(this).val();
                CKEDITOR.instances[self.model.ComponentId + "_txtAreaTransferInstruction"].setData(instructionInputVal);

                if ($(this).attr("id").indexOf("_Favorite_") == -1) {

                    $('#' + self.model.ComponentId + '_hidden_Area_' + id).val(instructionInputVal);
                    addRow(this);
                } else {
                    $('#' + self.model.ComponentId + '_hidden_Area_Favorite_' + id).val(instructionInputVal);
                    //addFavoriteRow(this);
                }
            }
            if ($(this).attr("name") && $(this).attr("name").indexOf('Cced') != -1) {
                updatePurposeDDL($(this));
            }

            if ($(this).attr("name") && $(this).attr("name").indexOf('FollowUp') != -1) {

                if (this.checked) {
                    if ($(this).attr("id").indexOf("_Favorite_") == -1) {
                        $('#' + self.model.ComponentId + '_transferInstruction_' + id).attr('required', 'required');
                        $('#' + self.model.ComponentId + '_InstructionTextDanger_' + id).removeClass('hidden').removeAttr("hidden");
                    } else {
                        $('#' + self.model.ComponentId + '_transferInstruction_Favorite_' + id).attr('required', 'required');
                        $('#' + self.model.ComponentId + '_InstructionTextDanger_Favorite_' + id).removeClass('hidden').removeAttr("hidden");
                    }



                } else {

                    if ($(this).attr("id").indexOf("_Favorite_") == -1) {
                        $('#' + self.model.ComponentId + '_transferInstruction_' + id).removeAttr('required');
                        $('#' + self.model.ComponentId + '_InstructionTextDanger_' + id).addClass('hidden').attr('hidden', "hidden");


                    } else {
                        $('#' + self.model.ComponentId + '_transferInstruction_Favorite_' + id).removeAttr('required');
                        $('#' + self.model.ComponentId + '_InstructionTextDanger_Favorite_' + id).addClass('hidden').attr('hidden', "hidden");

                    }

                }
            }
        });
        $(self.refs[self.model.ComponentId]).on('click', ".delete-row", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
            //checkShowHideCcColumn();
        });

        $(self.refs[self.model.ComponentId]).on('click', ".triggerInstruction", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });

        $(self.refs['grdTransferItems']).on('click', ".triggerAddressBook", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $(self.refs['multipleTransfer']).on('click', function () {
            var callback = function (result) {
                if (typeof ($("#" + self.model.ComponentId + "_cmbTransferTo_" + lastRowId).val()) === "undefined") {
                    addRow($("#" + self.model.ComponentId + "_cmbTransferTo_" + (lastRowId - 1)));
                }
                for (var i = 0; i < result.length; i++) {
                    var data = result[i];
                    var item = {};
                    if (data.userId !== null) {
                        if (Number(data.userId) !== Number(self.model.userId)) {
                            item.id = "User_" + data.structureId + "_" + data.userId;
                            var currentStructure = new CoreComponents.Lookup.Structure().getWithCaching(data.structureId, window.language);
                            var structureName = currentStructure;
                            if (currentStructure.attributes != null && currentStructure.attributes.length > 0) {
                                var attributeLang = $.grep(currentStructure.attributes, function (e) {
                                    return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                                });
                                if (attributeLang.length > 0) {
                                    structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                                }
                            }
                            item.text = structureName + " / " + data.name;
                            item.icon = "fa fa-user-o";
                            item.isStructure = false;
                            item.dataId = data.userId;
                            item.structureId = data.structureId;
                        }
                    } else {
                        item.id = "Structure" + data.structureId;
                        item.text = data.name;
                        item.icon = "fa fa-building-o";
                        item.isStructure = true;
                        item.dataId = data.structureId;
                    }
                    if (typeof item.id !== 'undefined') {
                        $("#" + self.model.ComponentId + "_cmbTransferTo_" + lastRowId).append($('<option />')
                            .val(item.id).text(item.text).prop('selected', true).data(item)).trigger("change");
                    }
                }
                $('.modalAddressBook').modal('hide');

            };
            let modalWrapper = $(".modal-window");
            let view = new AddressBook(self.model.structureId, self.model.enableSendingRules, self.model.isStructureSender,
                self.model.enableTransferToUsers, AddressBookMode.InternalWithUsers, AddressBookSelectionMode.Multiple, self.model.delegationId, StructureTypeMode.Internal, false, callback, modalWrapper);
            view.render();
            $('.modalAddressBook').modal('show');
            $(".modalAddressBook").off("hidden.bs.modal");
            $(".modalAddressBook").off("shown.bs.modal");
            $('.modalAddressBook').on('hidden.bs.modal', function () {
                swal.close();
                $(".modalAddressBook").parent().remove();
                $('body').addClass('modal-open');
            });
        });
        $(self.refs['btnCloseTransfer']).on('click', function () {
            lastRowId = 2;
        });
        $('.close:not(.closeTransferInstruction)').on('click', function () {
            lastRowId = 2;
            lastFavouriteRowId = 2;
        });
        $('.close.closeTransferInstruction').on('click', function () {
            $(self.refs['btnCloseTransferInstruction']).trigger('click');
        });
        $(self.refs['btnTransfer']).on('click', function () {
            let isFollowUpTransfer = false;
            if ($('#chkCreateFollowUpTransfer').is(':checked')) {
                let wrapper = $(".modal-window");
                let modelFollowUp = new FollowUpIndex.FollowUpIndexIndex();
                isFollowUpTransfer = true;
                modelFollowUp.createdFromTransfer = true;
                modelFollowUp.documentId = self.model.documentId;
                modelFollowUp.dueDate = self.model.maxDueDate;
                let viewFollowUp = new FollowUpIndex.FollowUpIndexView(wrapper, modelFollowUp);
                viewFollowUp.render();
                $('#followUpIndexModal').modal('show');
                $("#followUpIndexModal").off("hidden.bs.modal");
                $("#followUpIndexModal").off("shown.bs.modal");
                //let isFollowUpSubmitted = false; 
                //$('#followUpIndexSubmit').off('click').on('click', function () { 
                //        isFollowUpSubmitted = true; 
                //        $('#followUpIndexModal').modal('hide');
                //});
                $('#followUpIndexModal').on('hidden.bs.modal', function () {
                    $("#followUpIndexModal").parent().remove();
                    swal.close()
                    //if (isFollowUpSubmitted) {
                    if (self.model.fromSignAndTransfer == true) {
                        self.model.fromSignAndTransfer = false;
                    }
                        excuteTransfer(isFollowUpTransfer); // Proceed with transfer only if form was submitted
                    //}
                });
            } else {
                if (self.model.fromSignAndTransfer == true) {
                    self.model.fromSignAndTransfer = false;
                }
                excuteTransfer(isFollowUpTransfer);
            }        
        });
        function excuteTransfer(isFollowUpTransfer) {
            if (typeof self.model.callBack === 'function') {
                var $form = $(self.refs['formTransferPost']);
                $form.parsley().reset();
                var isValid = $form.parsley().validate();
                if (!isValid) return;

                var duplicatedNames = [], transferArray = [], selectedFavoriteDistributionIds = [];
                let duplicatedDistributionNames = [];

                let grdFavoriteTransferItems = $(self.refs['grdFavoriteTransferItems']);
                if (grdFavoriteTransferItems.length > 0) {
                    for (var i = 0; i < transferFavouriteRows.length; i++) {
                        var favTransferToObj = $('#' + self.model.ComponentId + '_cmbTransferTo_Favorite_' + transferFavouriteRows[i]).select2('data');
                        if (favTransferToObj && favTransferToObj.length > 0 && favTransferToObj[0].id != '0') {
                            favTransferToObj = favTransferToObj[0];
                            var distributionId = favTransferToObj.distributionId;

                            if (typeof distributionId !== 'undefined' && distributionId != -1) {
                                let distributionData = new Distribution.DistributionList().get(distributionId, self.model.structureId);
                                selectedFavoriteDistributionIds.push({
                                    id: "Distribution_" + distributionId,
                                    rowId: "Favorite_" + transferFavouriteRows[i],
                                    distributionStructureList: distributionData ? distributionData.distributionStructureList : []
                                });
                                continue;
                            } else {
                                let rowId = "Favorite_" + transferFavouriteRows[i];
                                var selectedOption = $(favTransferToObj.element);
                                var isStructure = typeof favTransferToObj.isStructure === 'undefined' ?
                                    selectedOption.data('isStructure') : favTransferToObj.isStructure;
                                var toUserId = isStructure ? null : parseInt(favTransferToObj.toUserId);
                                var toStructureId = favTransferToObj.toStructureId === "-1" ? null : parseInt(favTransferToObj.toStructureId);
                                let transferItem = self.createTransferItem(toStructureId, toUserId, favTransferToObj.text, rowId);

                                self.updateTransferArray(
                                    transferItem,
                                    duplicatedNames,
                                    transferArray,
                                    transferItem.name,
                                    transferItem.toStructureId,
                                    transferItem.toUserId
                                );
                            }
                        }
                    }
                }

                for (var i = 0; i < transferRows.length; i++) {
                    var transferToObj = $('#' + self.model.ComponentId + '_cmbTransferTo_' + transferRows[i]).select2('data');
                    if (transferToObj && transferToObj.length > 0) {
                        transferToObj = transferToObj[0];
                        var isDistributionItem = typeof transferToObj.isDistributionItem === 'boolean' && transferToObj.isDistributionItem;

                        if (!isDistributionItem) {
                            var selectedOption = $(transferToObj.element);
                            var isStructure = typeof transferToObj.isStructure === 'undefined' ?
                                selectedOption.data('isStructure') : transferToObj.isStructure;

                            var toStructureId;
                            if (transferToObj.structureId !== undefined) {
                                var toUserStructureId = typeof transferToObj.structureId === 'undefined' ?
                                    selectedOption.data('structureId') : transferToObj.structureId;
                                toStructureId = isStructure ? parseInt(transferToObj.id.split("Structure")[1]) : parseInt(toUserStructureId);
                            } else {
                                if (transferToObj.id.includes('Structure')) {
                                    toStructureId = parseInt(transferToObj.id.split("Structure")[1]);
                                } else {
                                    toStructureId = parseInt(transferToObj.id.split("_")[1]);
                                }
                            }

                            var toUserId = isStructure ? null : parseInt(transferToObj.id.split("_")[2]);

                            let transferItem = self.createTransferItem(toStructureId, toUserId, transferToObj.text, transferRows[i]);

                            self.updateTransferArray(
                                transferItem,
                                duplicatedNames,
                                transferArray,
                                transferItem.name,
                                transferItem.toStructureId,
                                transferItem.toUserId
                            );
                        } else {
                            selectedFavoriteDistributionIds.push({
                                id: transferToObj.id,
                                rowId: transferRows[i],
                                distributionStructureList: transferToObj.distributionStructureList
                            });
                        }
                    }
                }

                for (var i = 0; i < selectedFavoriteDistributionIds.length; i++) {
                    let selectedFavoriteDistributionObj = selectedFavoriteDistributionIds[i];
                    duplicatedDistributionNames.push(selectedFavoriteDistributionObj.id);
                    let distributionStructureList = selectedFavoriteDistributionObj.distributionStructureList;

                    if (!Array.isArray(distributionStructureList) || distributionStructureList.length === 0) {
                        continue;
                    }

                    for (var j = 0; j < distributionStructureList.length; j++) {
                        let transferItem = self.createTransferItem(
                            distributionStructureList[j].toStructureId,
                            distributionStructureList[j].toUserId,
                            distributionStructureList[j].structure,
                            selectedFavoriteDistributionObj.rowId
                        );

                        var items = transferArray.filter(x =>
                            parseInt(x.toUserId || -1) === parseInt(transferItem.toUserId || -1) &&
                            parseInt(x.toStructureId || -1) === parseInt(transferItem.toStructureId || -1)
                        );

                        if (items.length === 0) {
                            self.updateTransferArray(
                                transferItem,
                                duplicatedNames,
                                transferArray,
                                transferItem.name,
                                transferItem.toStructureId,
                                transferItem.toUserId
                            );
                        }
                    }
                }

                if (duplicatedNames.length > 0 || duplicatedDistributionNames.length !== new Set(duplicatedDistributionNames).size) {
                    Common.alertMsg(Resources.TransferDuplication + duplicatedNames.toString() + ']');
                    return;
                }

                self.model.callBack(transferArray, isFollowUpTransfer);
            }
        }

        window.removeEventListener('popstate', function (event) {
            $(self.refs['modalTransfer']).modal('hide');
            $(self.refs['modalTransferInstruction']).modal('hide');
        });
        window.addEventListener('popstate', function (event) {
            $(self.refs['modalTransfer']).modal('hide');
            $(self.refs['modalTransferInstruction']).modal('hide');
            $('body').attr("style", "");
        });

        //$(document).on("click", ".favoriteStructure-item", function (e) {
        //    // Ignore clicks on the checkbox itself to prevent double toggling
        //    if (!$(e.target).is("input[type='checkbox']")) {
        //        const checkbox = $(this).find("input[type='checkbox']");
        //        checkbox.prop("checked", !checkbox.prop("checked")); // Toggle the checkbox
        //    }
        //});

        $(document).ready(function () {
            // Event handler for clicking anywhere inside the FavoriteItems div Checkboxes
            $(document).on("click", ".favoriteStructure-item", function (e) {
                // Allow the natural behavior of the label or checkbox to take place
                if ($(e.target).is("label") || $(e.target).is("input[type='checkbox']")) {
                    return;
                }

                // Get the checkbox ID from the data attribute inside the div
                var checkboxId = $(this).data("checkbox-id");

                var checkbox = $("#" + checkboxId);
                checkbox.prop("checked", !checkbox.prop("checked"));


                checkbox.trigger("change");
            });
        });

        //$("[ref='AddFavorites']").on("click", function () {
        //    fromFavorites = true;
        //    var selectedCheckboxes = $("#" + self.model.ComponentId + "_favoritesGrid").find("input[type='checkbox']:checked");
        //    var itemId = $(this).attr('data-id');
        //    var item = listFavoriteStructuresData.filter(x => x.dataId == itemId)[0];

        //    var selectedFavPurpose = $("#_cmbTransferPurpose_Favorite_" + itemId).val();
        //    //this;
        //    //if (selectedCheckboxes.length === 0) {
        //    //    Common.alertMsg(Resources.NoItemsSelected);
        //    //    return;
        //    //} else {
        //    if (!selectedFavPurpose) {
        //        Common.alertMsg(Resources.SelectPurpose);
        //        return;
        //    }
        //    var transferToObj;
        //    for (var i = 0; i < transferRows.length; i++) {
        //        var transferToObj = $('#' + self.model.ComponentId + '_cmbTransferTo_' + transferRows[i]).select2('data');
        //        if (transferToObj.length > 0 && transferToObj[0].toStructureId == item.toStructureId && transferToObj[0].toUserId == item.toUserId) {
        //            Common.alertMsg(Resources.AlreadyExists);
        //            return;
        //        }
        //    }
        //    //}

        //    //selectedCheckboxes.each(function (index) {

        //    item.selected = true;
        //    var newarray = [];
        //    newarray.push(item);

        //    var newSelectToId = self.model.ComponentId + "_cmbTransferTo_" + lastRowId;  // lastRowId starts with 2 in the Transfer class
        //    var transferToDropdown = $("#" + newSelectToId);

        //    var newaddrressbookId = self.model.ComponentId + "_transferToAddressBook_" + lastRowId;
        //    // This represents the address book span with the TansferTo
        //    var transferAddressBook = $("#" + newaddrressbookId);

        //    if (transferToDropdown.find(`option[value='${itemId}']`).length === 0) {

        //        updateSelect2FavoriteData(transferToDropdown, newarray)
        //        transferToDropdown.attr('disabled', 'disabled');
        //        // transferAddressBook.prop('disabled', true);
        //        transferAddressBook.css({
        //            cursor: 'default',        // Makes the cursor look inactive, This disables the pointor cursor on the addressBook span element
        //            'pointer-events': 'none'  // Disables all mouse interactions like clicks
        //        });
        //    }


        //    if (selectedFavPurpose) {

        //        var newPurposeId = self.model.ComponentId + "_cmbTransferPurpose_" + lastRowId;

        //        $("#" + newPurposeId).val(selectedFavPurpose).trigger("change");
        //    }
        //    //addRow(this.nextSibling);   // Adding a new row increments lastRowId by 1 in the new row
        //    // So We add the first Favorite element selected of index 0 in the already existing row, then We add a new row to add the rest of the checked elements.

        //    //});

        //    fromFavorites = false;

        //    selectedCheckboxes.prop("checked", false);
        //    transferToDropdown.trigger('change');

        //    $("#" + self.model.ComponentId + "_cmbTransferPurpose_Favorite_" + itemId).val("").trigger("change");
        //});
        $(self.refs['setPurposeToAll']).click(function () {                 // calling the Localizer 'SetAllPurposes' button ref at the header of the transfer window

            if ($(self.refs['selectPurpose']).val() !== null) {             // Then checking that the purpose dropdown list at the header has a value (not null) with its ref

                // These 2 conditions are satisfied as class "purposeAll" is inside the select tag inside the latest row<tr> in the table class 
                if ($(".purposeAll").closest("tr").is(":last-child")) {
                    // Checks if there is a class "purposeAll" that exists inside an html element [like (select) of Purpose dropdown] that is inside a <tr> (table row) and if that <tr> that has an element with class "purposeAll" is the last row in the <table> element then :

                    $('.purposeAll').addClass('checkPurpose');
                    // The class checkPurpose is added to all elements with the class "purposeAll".
                }

                $('.purposeAll').each(function () {

                    let idCharacters = $(this).attr("id").split('_');
                    let id = idCharacters[idCharacters.length - 1];
                    if ($(this).attr("id").indexOf("_Favorite_") != -1) {

                        var transferTo = $("#" + self.model.ComponentId + "_cmbTransferTo_Favorite_" + id).val();
                        if (transferTo && transferTo != '0') {
                            $(this).val($(self.refs['selectPurpose']).val()).trigger('change');
                        }
                    } else {
                        var transferTo = $("#" + self.model.ComponentId + "_cmbTransferTo_" + id).val();
                        if (transferTo && transferTo != null) {
                            $(this).val($(self.refs['selectPurpose']).val()).trigger('change');
                        }

                    }
                });

            }
        });

        $(self.refs['btnTransferInstruction']).on('click', function () {
            var isFavorite = false;
            var txtArea = $('#' + self.model.ComponentId + '_txtAreaTransferInstruction')
            var fromelemnt = txtArea.attr("fromElement")

            let idCharacters = fromelemnt.split('_');
            let instructionId = idCharacters[idCharacters.length - 1];

            if (fromelemnt.indexOf("_Favorite_") != -1)
                isFavorite = true;


            var instructionData = CKEDITOR.instances[self.model.ComponentId + "_txtAreaTransferInstruction"].getData();
            var instructionRawText = CKEDITOR.instances[self.model.ComponentId + "_txtAreaTransferInstruction"].document.getBody().getText();

            if (isFavorite) {
                $('#' + self.model.ComponentId + '_hidden_Area_Favorite_' + instructionId).val(instructionData);
                $('#' + self.model.ComponentId + '_transferInstruction_Favorite_' + instructionId).val(instructionRawText);
                if (instructionData !== "" && typeof instructionId != "string") {
                    //addFavoriteRow($('#' + self.model.ComponentId + '_hidden_Area_Favorite_' + instructionId));
                }
            }
            else {
                $('#' + self.model.ComponentId + '_hidden_Area_' + instructionId).val(instructionData);
                $('#' + self.model.ComponentId + '_transferInstruction_' + instructionId).val(instructionRawText);

                if (instructionData !== "" && typeof instructionId != "string") {
                    addRow($('#' + self.model.ComponentId + '_hidden_Area_' + instructionId));
                }
            }

            $(self.refs['btnCloseTransferInstruction']).trigger('click');
        });

        var headers = {};
        headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
        let meetingTypeVal = $('[id*=meetingType]').val();
        if (meetingTypeVal != null || meetingTypeVal != undefined) {
            Common.ajaxGet('/Committee/GetCommitteeUsers?Id=' + $('[id*=meetingType]').val(), null, function (data) {
                for (i = 0; i < data.length; i++) {

                    var notExists = false;

                    Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/GetUser', { "id": data[i].id }, function (response) {

                        var structureId = data[i].userStructureId.split("_")[1];
                        var structure = response.structures.filter(x => x.id == structureId);
                        if (structure.length > 0) {
                            addRowForMeeting($(".inputChange"), data[i].userStructureId, data[i].name);
                        }

                    }, function (msg) {
                        console.log(msg);
                    }, false, "", headers, false);

                }
                var table = $(self.refs['grdTransferItems'])[0];
                var rowCnt = table.rows.length;
            });
        }

        $('#chkShowFavoriteTransfers').on('click', function () {

            // #chkShowFavoriteTransfers: Selects the checkbox element with the id chkShowFavoriteTransfers when clicked
            // self.refs , calls an html element by its ref name  refrencing an html element, making the ref property act like an alternative to id
            var grdFavoriteTransferContainer = $(self.refs["grdFavoriteTransferContainer"]);
            // "grdFavoriteTransferContainer" is a ref attribute in the table element in the transfer.handlebars file, references an HTML element using its ref property.
            // self is a javascript object from the Transfer class
            if ($(this).is(':checked')) {
                grdFavoriteTransferContainer.removeAttr('hidden');
                grdFavoriteTransferContainer.removeClass("hidden");
            } else {
                grdFavoriteTransferContainer.attr('hidden', "hidden");
                grdFavoriteTransferContainer.addClass("hidden");
            }
        });

    }

    updateTransferArray(transferItem, duplicatedNames, transferArray, strcutureName, toStructureId, toUserId) {
        if (checkIfExist(transferArray, toStructureId, toUserId)) {
            duplicatedNames.push(strcutureName);
        }
        transferArray.push(transferItem);
    }

    createTransferItem(toStructureId, toUserId, structureName, rowId) {
        let transferItem = new TransferItem();

        transferItem.toStructureId = toStructureId;
        transferItem.toUserId = toUserId;
        transferItem.name = structureName;
        transferItem.dueDate = $('#' + self.model.ComponentId + '_transferDueDate_' + rowId).val()
        transferItem.purposeId = $('#' + self.model.ComponentId + '_cmbTransferPurpose_' + rowId).val()
        transferItem.priorityId = $('#' + self.model.ComponentId + '_cmbTransferPriority_' + rowId).val()
        transferItem.instruction = $("#" + self.model.ComponentId + "_hidden_Area_" + rowId).val()
        transferItem.cced = $('#' + self.model.ComponentId + '_TransferCced_' + rowId).is(':checked');
        transferItem.followUp = $('#' + self.model.ComponentId + '_TransferFollowUp_' + rowId).is(':checked');
        transferItem.PrivateInstruction = $('#' + self.model.ComponentId + '_checkInstructionPrivate_' + rowId).prop("checked");
        return transferItem;

    }

    lockButonsOnTransfer() {
        var btn = $(self.refs['btnTransfer']);
        btn.button('loading');
        var btnClose = $(self.refs['btnCloseTransfer']);
        btnClose.attr('disabled', 'disabled');
    }

    unLockButonsAfterTransfer() {
        var btn = $(self.refs['btnTransfer']);
        var btnClose = $(self.refs['btnCloseTransfer']);
        btn.button('reset');
        btnClose.removeAttr('disabled');
    }

    clear() {
        $("#" + self.model.ComponentId + "_cmbTransferTo_2").val('').trigger('change');
        $("#" + self.model.ComponentId + "_cmbTransferPurpose_2").val(1).trigger('change');
        $("#" + self.model.ComponentId + "_cmbTransferPriority_2").val(1).trigger('change');
        $("#" + self.model.ComponentId + "_transferDueDate_2").val('').trigger('change');
        $("#" + self.model.ComponentId + "_transferDueDate_2").val('').trigger('change');
        $("#" + self.model.ComponentId + "_privateCheck_2").prop("checked", false)
        $(self.refs['grdTransferItems']).find("tr:gt(1)").remove();
        transferRows = [];
        transferRows.push(2);
    }

    close() {
        $('.modalTransfer').modal('hide');
    }
}



export default TransferView;
