﻿import Intalio from './common.js'
import { CategoriesReferenceType } from './lookup.js'
var gReferencedObject = [];
let gOrderData = [];
var gFromEditCounter = false;
var notUsedCategoriesArray = new Array();
function UpdateOrderDropDown()
{
    gOrderData = [];
    if (gReferencedObject)
    {
        if (gReferencedObject.length > 0)
        {
            for (var i = 0; i <= gReferencedObject.length; i++)
            {
                gOrderData.push(i + 1);
            }
        } else
        {
            gOrderData.push(1);
        }
    } else
    {
        gOrderData.push(1);
    }

    $('#cmbOrder').select2('destroy').empty().select2({
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        minimumResultsForSearch: -1,
        placeholder: Resources.SelectOrder,
        width: "100%",
        allowClear: false,
        data: gOrderData,
        dropdownParent: $('#cmbOrderContainer')
    }).on('select2:select', function ()
    {
        $('#cmbOrder').parsley().validate();
    });
    $('#cmbOrder').val('').trigger('change');
}
function InsertReferenceRow(type, content, order, resetByYear, category)
{
    var referencesTable = $('#referencesTable').DataTable();
    var refRows = referencesTable.rows();
    for (var i = 0; i < gReferencedObject.length; i++)
    {
        if (gReferencedObject[i].order >= order)
        {
            gReferencedObject[i].order++;
        }
    }

    var refRowsIds = [];
    for (var i = 0; i < refRows.data().length; i++)
    {
        refRowsIds.push(parseInt(referencesTable.row(i).data().rowId));
    }

    for (var i = 0; i < gReferencedObject.length; i++)
    {
        for (var j = 0; j < refRowsIds.length; j++)
        {
            if (gReferencedObject[i].rowId === refRowsIds[j])
            {
                referencesTable.row('#' + refRowsIds[j]).data({ 'id': gReferencedObject[i].id, 'rowId': gReferencedObject[i].rowId, "type": gReferencedObject[i].type, "displayType": Resources[gReferencedObject[i].type], "content": gReferencedObject[i].content, "order": gReferencedObject[i].order, "resetByYear": gReferencedObject[i].resetByYear, "category": gReferencedObject[i].category }).draw();
                break;
            }
        }
    }
    
    var lastItem = gReferencedObject[gReferencedObject.length - 1];
    if (lastItem)
    {
        var maxid = 0;
        for (var i = 0; i < gReferencedObject.length; i++)
        {
            if (maxid < gReferencedObject[i].rowId)
            {
                maxid = gReferencedObject[i].rowId;
            }
        }

        referencesTable.row.add({ 'id': null, 'rowId': maxid + 1, "type": type, "displayType": Resources[type], "content": content, "order": order, "resetByYear": resetByYear, "category": category }).draw();
        //gReferencedObject.push({ 'id': maxid + 1, 'type': type, 'content': content, "order": order, "resetByYear": resetByYear, "category": category });
    } else
    {
        referencesTable.row.add({ 'id': null, 'rowId': 1, "type": type, "displayType": Resources[type], "content": content, "order": order, "resetByYear": resetByYear, "category": category }).draw();
        //gReferencedObject.push({ 'id': 1, 'type': type, 'content': content, "order": order, "resetByYear": resetByYear, "category": category });
    }

    UpdateSessionStorage();
    UpdateOrderDropDown();
}
function UpdateReferenceRow(type, content, order, resetByYear, category)
{
    var referencesTable = $('#referencesTable').DataTable();
    var targetbtn = $("#edit-" + $("#rowId").val());
    var currentOrder = referencesTable.row(targetbtn.parents("tr")).data().order;
    var refRows = referencesTable.rows();

    for (var i = 0; i < gReferencedObject.length; i++)
    {
        if (gReferencedObject[i].order >= order && gReferencedObject[i].order < currentOrder)
        {
            gReferencedObject[i].order++;
        } else if (gReferencedObject[i].order <= order && gReferencedObject[i].order >= currentOrder)
        {
            gReferencedObject[i].order--;
        }
    }
    var refRowsIds = [];
    for (var i = 0; i < refRows.data().length; i++)
    {
        refRowsIds.push(parseInt(referencesTable.row(i).data().rowId));
    }
    for (var i = 0; i < gReferencedObject.length; i++)
    {
        for (var j = 0; j < refRowsIds.length; j++)
        {
            if (gReferencedObject[i].rowId === refRowsIds[j] && gReferencedObject[i].rowId !== parseInt($("#rowId").val()))
            {
                referencesTable.row('#' + refRowsIds[j]).data({ 'id': referencesTable.row('#' + refRowsIds[j]).data().id != "" ? parseInt(referencesTable.row('#' + refRowsIds[j]).data().id) : "", 'rowId': parseInt(referencesTable.row('#' + refRowsIds[j]).data().rowId), "type": referencesTable.row('#' + refRowsIds[j]).data().type, "displayType": Resources[referencesTable.row('#' + refRowsIds[j]).data().type], "content": referencesTable.row('#' + refRowsIds[j]).data().content, "order": gReferencedObject[i].order, "resetByYear": gReferencedObject[i].resetByYear, "category": gReferencedObject[i].category }).draw();
                break;
            }
        }
    }
    referencesTable.row(targetbtn.parents("tr")).data({ 'id': $("#hddId").val() != "" ? parseInt($("#hddId").val()) : "", 'rowId': parseInt($("#rowId").val()), "type": type, "displayType": Resources[type], "content": content, "order": order, "resetByYear": resetByYear, "category": category }).draw();
    for (var i = 0; i < gReferencedObject.length; i++)
    {
        if (gReferencedObject[i].rowId === parseInt($("#rowId").val()))
        {
            gReferencedObject[i].type = type;
            gReferencedObject[i].displayType = Resources[type];
            gReferencedObject[i].content = content;
            gReferencedObject[i].order = order;
            gReferencedObject[i].resetByYear = resetByYear;
            gReferencedObject[i].category = category;
            break;
        }
    }
    UpdateSessionStorage();
    UpdateOrderDropDown();
}
function UpdateSessionStorage()
{
    gReferencedObject.sort(function (a, b)
    {
        return parseInt(a.order) - parseInt(b.order);
    });
    sessionStorage.setItem('gReferences', JSON.stringify(gReferencedObject));
}
function UpdateOrderDropDownOnEdit()
{
    gOrderData = [];
    if (gReferencedObject)
    {
        if (gReferencedObject.length > 0)
        {
            for (var i = 0; i < gReferencedObject.length; i++)
            {
                gOrderData.push(i + 1);
            }
        } else
        {
            gOrderData.push(1);
        }
    } else
    {
        gOrderData.push(1);
    }

    $('#cmbOrder').select2('destroy').empty().select2({
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        minimumResultsForSearch: -1,
        placeholder: Resources.SelectOrder,
        width: "100%",
        allowClear: false,
        data: gOrderData,
        dropdownParent: $('#cmbOrderContainer')
    }).on('select2:select', function ()
    {
        $('#cmbOrder').parsley().validate();
    });
    $('#cmbOrder').val('').trigger('change');
}
function editReference(row)
{
    ClearForm();
    UpdateOrderDropDownOnEdit();
    $("#hddId").val(row.id);
    $("#rowId").val(row.rowId);
    var rowType = row.type;
    $('#cmbType').val(rowType).trigger('change');
    $('#cmbOrder').val(row.order).trigger('change');
    if (rowType.toLowerCase() === "string")
    {
        $("#contentString").val(row.content);
    }
    if (rowType.toLowerCase() === "dynamic")
    {
        $("#contentFunctionName").val(row.content);
    }
    if (rowType.toLowerCase() === "DynamicProcedure") {
        $("#contentStoredProcedureName").val(row.content);
    }
    else if (rowType.toLowerCase() === "counter")
    {
        $("#chkResetByYear").prop("checked", row.resetByYear);
        $('#cmbCounterContent input[type=text]').val(row.content);
        gFromEditCounter = true;
    }
    else if (rowType.toLowerCase() === "separator")
    {
        $("#cmbSeparator").val(row.content).trigger('change');
    }
    return false;
}
function counterExists()
{
    if ($('#cmbType').val() === "counter")
    {
        var referencesTable = $('#referencesTable').DataTable();
        var refRows = referencesTable.rows().data();
        for (var i = 0; i < refRows.length; i++)
        {
            if (refRows[i].type === "counter")
            {
                return true;
            }
        }
    }
    return false;
}
function ClearForm()
{
    UpdateOrderDropDown();
    $("#formPost").parsley().reset();
    $("#contentString").val('');
    $("#contentFunctionName").val('');
    $("#contentStoredProcedureName").val('');
    $("#chkResetByYear").prop("checked", false);
    $("#cmbSeparator").val('').trigger('change');
    $('#cmbType').val('').trigger('change');
    $('#cmbOrder').val('').trigger('change');
    $('#cmbCounterContent input[type=text]').val('');
    $('#hddId').val('');
    $('#rowId').val('');
    gFromEditCounter = false;
    $("#stringParent").hide();
    $("#stringFunctionName").hide();
    $("#divResetByYear").hide();
    $("#cmbCounterContentContainer").hide();
    $("#cmbSeparatorContainer").hide();
    $("#dynamicStoredProcedureName").hide();

}
function DrawTable()
{
    $('#referencesTable').on('draw.dt',
        function ()
        {
            $('#referencesTable tbody tr td').each(function ()
            {
                this.setAttribute('title', $(this).text());
            });
        }).DataTable({
            "order": [[5, "asc"]],
            rowId: 'rowId',
            "bPaginate": false,
            "bInfo": false,
            "language": {
                "lengthMenu": Resources.DatatableLengthMenu,
                "zeroRecords": Resources.DatatableZeroRecords,
                "infoEmpty": Resources.DatatableInfoEmpty,
                "infoFiltered": Resources.DatatableinfoFiltered,
                "paginate": {
                    "previous": Resources.Previous,
                    "next": Resources.Next,
                }
            },
            columns: [
                { data: "id", visible: false, 'orderable': false, 'sortable': false },
                { title: Resources.Type, visible: false, data: "type", 'orderable': false, 'sortable': false },
                { data: "rowId", visible: false, 'orderable': false, 'sortable': false },
                { title: Resources.Type, data: "displayType", 'orderable': false, 'sortable': false },
                { title: Resources.Content, data: "content", 'orderable': false, 'sortable': false },
                { title: Resources.Order, data: "order", className: "width100" },
                { data: "resetByYear", visible: false, 'orderable': false, 'sortable': false },
                {
                    data: "",
                    className: "dt-center width20",
                    'orderable': false,
                    'sortable': false,
                    render: function (data, type, row, meta)
                    {
                        var btn = document.createElement('button');
                        btn.setAttribute('class', 'btn btn-xs btn-primary');
                        btn.setAttribute('id', 'edit-' + row.rowId);
                        btn.setAttribute("type", "button");
                        btn.setAttribute('clickattr', 'editReference(' + JSON.stringify(row) + ')');
                        btn.innerHTML = "<i class='fa fa-pencil-square-o'/>";
                        return btn.outerHTML;
                    },
                    width: "10px"
                },
                {
                    data: "",
                    className: "dt-center width20",
                    'orderable': false,
                    'sortable': false,
                    render: function (data, type, row)
                    {
                        var btn = document.createElement('button');
                        btn.setAttribute('class', 'btn btn-xs btn-danger js-delete');
                        btn.setAttribute('id', 'delete-' + row.rowId);
                        btn.setAttribute("type", "button");
                        btn.setAttribute('clickattr', 'DeleteReference(' + JSON.stringify(row) + ',this' + ')');
                        btn.innerHTML = "<i class='fa fa-trash-o'/>";
                        return btn.outerHTML;
                    },
                    width: "10px"
                }
            ],
            dom: 'trpi',
            "createdRow": function (row, data, index)
            {
                gReferencedObject.push(data);
            }
        });
    $('#referencesTable tbody').on('click', ".btn", function ()
    {
        var onclick = $(this).attr("clickattr");
        eval(onclick);
    });
}
function DeleteReference(row, rowdata)
{
    if ($("#rowId").val() !== "")
    {
        ClearForm();
    }
    var referencesTable = $('#referencesTable').DataTable();
    referencesTable.row($(rowdata).closest("tr")).remove().draw();
    for (var i = 0; i < gReferencedObject.length; i++)
    {
        if (gReferencedObject[i].rowId === row.rowId)
        {
            gReferencedObject.splice(i, 1);
            break;
        }
    }
    for (var i = 0; i < gReferencedObject.length; i++)
    {
        if (gReferencedObject[i].order > row.order)
        {
            gReferencedObject[i].order--;
        }
    }
    var refRows = referencesTable.rows();
    var refRowsIds = [];
    for (var i = 0; i < refRows.data().length; i++)
    {
        refRowsIds.push(parseInt(referencesTable.row(i).data().rowId));
    }

    for (var i = 0; i < gReferencedObject.length; i++)
    {
        for (var j = 0; j < refRowsIds.length; j++)
        {
            if (gReferencedObject[i].rowId === refRowsIds[j]) {
                referencesTable.row('#' + refRowsIds[j]).data({
                    'id': parseInt(referencesTable.row('#' + refRowsIds[j]).data().id), 'rowId': referencesTable.row('#' + refRowsIds[j]).data().rowId, "type": referencesTable.row('#' + refRowsIds[j]).data().type, "displayType": Resources[referencesTable.row('#' + refRowsIds[j]).data().type], "content": referencesTable.row('#' + refRowsIds[j]).data().content, "order": gReferencedObject[i].order, "resetByYear": gReferencedObject[i].resetByYear,"category": gReferencedObject[i].category
                }).draw();
                break;
            }
        }
    }

    UpdateSessionStorage();
    UpdateOrderDropDown();
}
function getData()
{
    var referencesTable = $('#referencesTable').DataTable();
    let data = referencesTable.rows().data();
    var params = [];
    data.each(function (value, index)
    {
        var jsonobj = {};
        jsonobj.id = isNaN(value.id) ? null : value.id;
        jsonobj.category = value.category;
        jsonobj.content = value.content;
        jsonobj.resetByYear = value.resetByYear;
        jsonobj.order = value.order;
        jsonobj.type = value.type;
        params.push(jsonobj);
    });
    return params;
}
export class CategoryReferenceNumberIndex extends Intalio.Model
{
    constructor()
    {
        super();
        this.notUsedCategories = notUsedCategoriesArray;
        this.categories = [];
        this.categoriesReferenceType = [];
        this.categoryId = 0;
        this.isModal = true;
    }
    get()
    {
        var retValue = {
            data: this.getData('/CategoryReferenceNumber/ListNotUsedCategories')
        };
        this.notUsedCategories = retValue;
        return retValue.data;
    }
    getReferenceType() {

        var retValue = {
            data: this.getData('/CategoryReferenceNumber/ListCategoryReferenceNumberGeneratorTypes')
        };
        this.categoriesReferenceType = retValue;
        return retValue.data;
    }
}
class CategoryReferenceNumberIndexView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "categoryreferencenumberindex", model);
    }
    render()
    {

        $('#btnClose').click(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnReferenceSubmit').focus();
                }
                else
                {
                    $('#cmbType').focus();
                }
                ClearForm();
                $("#stringParent").hide();
                $("#stringFunctionName").hide();
                $("#divResetByYear").hide();
                $("#cmbCounterContentContainer").hide();
                $("#cmbSeparatorContainer").hide();
                $("#dynamicStoredProcedureName").hide();
            }
            ClearForm();
            $("#stringParent").hide();
            $("#stringFunctionName").hide();
            $("#divResetByYear").hide();
            $("#cmbCounterContentContainer").hide();
            $("#cmbSeparatorContainer").hide();
            $("#dynamicStoredProcedureName").hide();
        });

        $('#catRefNumberClose').click(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnReferenceSubmit').focus();
                }
                else {
                    $('#cmbType').focus();
                }
            }
            ClearForm();
            $("#stringParent").hide();
            $("#stringFunctionName").hide();
            $("#divResetByYear").hide();
            $("#cmbCounterContentContainer").hide();
            $("#cmbSeparatorContainer").hide();
            $("#dynamicStoredProcedureName").hide();
        });
        var categoriesReferenceType = new CategoriesReferenceType().get(window.language);
        if (!$("#ReferenceNumberOption option[value='" + categoriesReferenceType[0].id
            + "']").length > 0) {
            for (var i = 0; i < categoriesReferenceType.length; i++) {
                $("#ReferenceNumberOption").append('<option value="' + categoriesReferenceType[i].id + '">' + categoriesReferenceType[i].text + '</option>');
            }
        }
        $.fn.select2.defaults.set("theme", "bootstrap");
        Common.gridCommon();
        $("#rowId").val("");
        $('#cmbReferenceCategories').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#indexCategoryContainer')
        }).on('select2:select', function ()
        {
            $("#referencesTable").DataTable().clear().draw();
            gReferencedObject = [];
            gOrderData = [];
            ClearForm();
        });
        $("#cmbReferenceCategories").val('').trigger('change');
        $('#cmbType').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#cmbTypeContainer')
        }).on('change', function ()
        {
            let val = $(this).val();
            $("#contentString").removeAttr("required");
            $("#contentFunctionName").removeAttr("required");
            $("#cmbSeparator").removeAttr("required");
            $("#contentStoredProcedureName").removeAttr("required");
            $("#formPost").parsley().reset();
            if (val == "Year" || val == "Month" || val == "StructureCode") {
                $("#stringParent").hide();
                $("#stringFunctionName").hide();
                $("#divResetByYear").hide();
                $("#cmbCounterContentContainer").hide();
                $("#cmbSeparatorContainer").hide();
                $("#dynamicStoredProcedureName").hide();
            } else if (val == "String") {
                $("#contentString").val('');
                $("#stringParent").show()
                $("#divResetByYear").hide();
                $("#stringFunctionName").hide();
                $("#cmbCounterContentContainer").hide();
                $("#cmbSeparatorContainer").hide();
                $("#dynamicStoredProcedureName").hide();
                $("#contentString").attr("required", "required");
            } else if (val == "Dynamic") {
                $("#contentFunctionName").val('');
                $("#stringFunctionName").show()
                $("#divResetByYear").hide();
                $("#stringParent").hide();
                $("#cmbCounterContentContainer").hide();
                $("#cmbSeparatorContainer").hide();
                $("#dynamicStoredProcedureName").hide();
                $("#contentFunctionName").attr("required", "required");
            } else if (val == "Counter") {
                $("#chkResetByYear").prop("checked", false);
                $("#stringParent").hide();
                $("#stringFunctionName").hide();
                $("#divResetByYear").show();
                $("#cmbCounterContentContainer").show();
                $("#cmbSeparatorContainer").hide();
                $("#contentStoredProcedureName").hide();
            } else if (val == "Separator") {
                $("#cmbSeparator").val('').trigger('change');
                $("#stringParent").hide();
                $("#stringFunctionName").hide();
                $("#divResetByYear").hide();
                $("#cmbCounterContentContainer").hide();
                $("#cmbSeparatorContainer").show();
                $("#dynamicStoredProcedureName").hide();
                $("#cmbSeparator").attr("required", "required");
            }
            else if (val == "DynamicProcedure") {
                $("#contentStoredProcedureName").val('');
                $("#dynamicStoredProcedureName").show()
                $("#stringParent").hide();
                $("#stringFunctionName").hide();
                $("#divResetByYear").hide();
                $("#cmbCounterContentContainer").hide();
                $("#cmbSeparatorContainer").hide();
                $("#contentStoredProcedureName").attr("required", "required");

            }
        });
        $("#cmbType").val('').trigger('change');
        $('#cmbOrder').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#cmbOrderContainer')
        });
        $("#cmbOrder").val('').trigger('change');
        $('#cmbSeparator').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#cmbOrderContainer')
        });
        $("#cmbSeparator").val('').trigger('change');
        $('#ReferenceNumberOption').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            multiple: false,
            dropdownParent: $('#GenerateRefrenceNoOnSign_OnTransfer')
        });
        UpdateOrderDropDown();
        DrawTable();
        $('#btnClose').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnReferenceSubmit').focus();
                }
                else
                {
                    $('#cmbReferenceCategories').focus();
                }
            }
            ClearForm();
            $("#stringParent").hide();
            $("#stringFunctionName").hide();
            $("#divResetByYear").hide();
            $("#cmbCounterContentContainer").hide();
            $("#cmbSeparatorContainer").hide();
            $("#dynamicStoredProcedureName").hide();
        });
        $('#cmbReferenceCategories').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnClose').focus();
                }
                else
                {
                    $('#cmbType').focus();
                }
            }
        });
        $('#formPost').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                e.preventDefault();
            }
        });
        $('#btnReferenceSubmit').on('click', function ()
        {
            let params = {
                "model": getData(),
                "categoryId": $("#cmbReferenceCategories").val()
            }
            let paramsReferenceNumberType = {
                "ReferenceNumberOption": $("#ReferenceNumberOption").val(),
                "categoryId": $("#cmbReferenceCategories").val()
            }
            if (params.model.length > 0){
                var btn = $('#btnReferenceSubmit');
                btn.button('loading');
                var btnClose = $('#btnClose');
                var btnCloseManage = $('#btnCloseManage');
                var btnCloseX = $('#catRefNumberClose');
                var btnCloseManagementX = $('#categoryManagementClose');
                btnClose.attr('disabled', 'disabled');
                btnCloseX.attr('disabled', 'disabled');
                btnCloseManage.attr('disabled', 'disabled');
                btnCloseManagementX.attr('disabled', 'disabled');

                Common.ajaxPost('/CategoryReferenceNumber/EditReferenceNumberType', paramsReferenceNumberType, function (data) {
                    btn.button('reset');
                    btnClose.removeAttr('disabled');
                    btnCloseX.removeAttr('disabled');
                    btnCloseManage.removeAttr('disabled');
                    btnCloseManagementX.removeAttr('disabled');
                    $("#grdItems").DataTable().ajax.reload();
                    $("#hdReferenceId").val(data.id);
                    btnClose.click();
                },
                    Common.ajaxPost('/CategoryReferenceNumber/Index', params, function (data) {
                        btn.button('reset');
                        btnClose.removeAttr('disabled');
                        btnCloseX.removeAttr('disabled');
                        btnCloseManage.removeAttr('disabled');
                        btnCloseManagementX.removeAttr('disabled');
                        Common.showScreenSuccessMsg();
                        $("#grdItems").DataTable().ajax.reload();
                        $("#hdReferenceId").val(data.id);
                        btnClose.click();
                    }, function () { btn.button('reset'); btnClose.removeAttr('disabled'); btnCloseX.removeAttr('disabled'); btnCloseManage.removeAttr('disabled'); btnCloseManagementX.removeAttr('disabled'); Common.showScreenErrorMsg(); }, false));
               
                ClearForm();
                $("#stringParent").hide();
                $("#stringFunctionName").hide();
                $("#divResetByYear").hide();
                $("#cmbCounterContentContainer").hide();
                $("#cmbSeparatorContainer").hide();
                $("#dynamicStoredProcedureName").hide();



            } else {
                Common.alertMsg(Resources.SelectAtLeastOneConfig);
            }
        });
        $('#stringParent').hide();
        $('#stringFunctionName').hide();
        $('#divResetByYear').hide();
        $('#cmbSeparatorContainer').hide();
        $('#cmbCounterContentContainer').hide();
        $('#dynamicStoredProcedureName').hide();

        $("#btnAdd").click(function ()
        {
            $("#formPost").parsley().reset();
            if (!counterExists() || gFromEditCounter)
            {
                $("#cmbType").parsley().validate();
                $("#cmbOrder").parsley().validate();
                $("#ReferenceNumberOption").parsley().validate();
                var obj = [];
                let val = $("#cmbType").val();
                obj.order = $('#cmbOrder').val();
                obj.category = $('#cmbReferenceCategories').val();
                obj.resetByYear = false;
                if (val == "Year" || val == "Month" || val == "StructureCode") {

                    obj.type = val;
                    obj.content = val;
                }
                else if (val == "String") {
                    if ($("#contentString").parsley().validate() && $("#contentString").parsley().isValid() && $("#contentString").val() !== "" && $("#cmbType").parsley().isValid() && $("#cmbOrder").parsley().isValid()) {
                        obj.type = val;
                        obj.content = $("#contentString").val();
                    }
                }
                else if (val == "Dynamic") {
                    if ($("#contentFunctionName").parsley().validate() &&
                        $("#contentFunctionName").val() !== "" && $("#cmbType").parsley().isValid() && $("#cmbOrder").parsley().isValid()) {
                        obj.type = val;
                        obj.content = $("#contentFunctionName").val();
                    }
                } else if (val == "Counter") {
                    if ($("#cmbType").parsley().isValid() && $("#cmbOrder").parsley().isValid()) {
                        obj.type = val;
                        obj.content = $("#cmbCounterContent").children().first().val();
                        obj.resetByYear = $("#chkResetByYear").is(":checked");
                    }
                } else if (val == "Separator") {
                    if ($("#cmbSeparator").parsley().validate() && $("#cmbSeparator").val() && $("#cmbType").parsley().isValid() && $("#cmbOrder").parsley().isValid()) {
                        obj.type = val;
                        obj.content = $("#cmbSeparator").val();
                    }
                }
                else if (val == "DynamicProcedure") {
                    if ($("#contentStoredProcedureName").parsley().validate() && $("#contentStoredProcedureName").val() !== "" &&
                        $("#cmbType").parsley().isValid() && $("#cmbOrder").parsley().isValid()) {
                        obj.type = val;
                        obj.content = $("#contentStoredProcedureName").val();
                    }
                }
                if (obj.type)
                {
                    if ($("#rowId").val() === "")
                    {
                        InsertReferenceRow(obj.type, obj.content, parseInt(obj.order), obj.resetByYear, obj.category);
                    } else
                    {
                        UpdateReferenceRow(obj.type, obj.content, parseInt(obj.order), obj.resetByYear, obj.category);
                    }
                    ClearForm();
                    $("#stringParent").hide();
                    $("#stringFunctionName").hide();
                    $("#divResetByYear").hide();
                    $("#cmbCounterContentContainer").hide();
                    $("#cmbSeparatorContainer").hide();
                    $("#dynamicStoredProcedureName").hide();
               
                }
            } else
            {
                Common.alertMsg(Resources.CounterExists);
            }
        });
        $("#btnReferenceClear").click(function ()
        {
            ClearForm();
        });
        $("#btnClearForm").click(function ()
        {
            gReferencedObject = [];
            gOrderData = [];
            gFromEditCounter = false;
            UpdateOrderDropDown();
        })
        $("#btnNext").click(function ()
        {
            if ($('#cmbReferenceCategories').val() != null)
            {
                var data = $('#cmbReferenceCategories').select2('data');
                $('#modalCategoryReferenceNumberTitle').html(Resources.New + " - " + data[0].text);
                $('#categoryDiv').hide();
                $('#referenceConfigDiv').show();
                $('#referenceTableDiv').show();
                $("#requiredDiv").show();
                $("#btnReferenceSubmit").show();
                $(this).hide();
                $("#modalDiv").removeClass("modal-sm");
                $("#modalDiv").addClass("modal-lg");
            } else
            {
                Common.ajaxGet('/CategoryReferenceNumber/CheckIfAllCategoriesExists/', null, function (retval)
                {
                    if (retval)
                    {
                        Common.alertMsg(Resources.AllCategoryReferenceAlreadyExists);
                    }
                    else
                    {
                        $('#modalCategoryReferenceNumberTitle').html(Resources.New + " - " + Resources.AllCategories);
                        $('#categoryDiv').hide();
                        $('#referenceConfigDiv').show();
                        $('#referenceTableDiv').show();
                        $("#btnReferenceSubmit").show();
                        $("#btnNext").hide();
                        $("#requiredDiv").show();
                        $("#modalDiv").removeClass("modal-sm");
                        $("#modalDiv").addClass("modal-lg");
                        $("#cmbType").focus();
                    }
                }, function () { }, false);

            }

        });
        if (this.model.categoryId != undefined && this.model.categoryId != 0)
        {
            var categoryReferenceId = this.model.categoryId;
            let params = {
                'categoryId': categoryReferenceId
            }
            $('#cmbReferenceCategories').val(categoryReferenceId).trigger('change');
            $('#cmbReferenceCategories').attr("disabled", "disabled");
            Common.ajaxGet('/CategoryReferenceNumber/GetByCategoryId/', params, function (retval)
            {
                var gOrderData = [];
                if (retval.data.length > 0)
                {
                    for (var i = 0; i < retval.data.length; i++)
                    {
                        var referencesTable = $("#referencesTable").DataTable();
                        referencesTable.row.add({ 'id': retval.data[i].id, 'rowId': i + 1, "type": retval.data[i].type, "displayType": Resources[retval.data[i].type], "content": retval.data[i].content, "order": retval.data[i].order, "resetByYear": retval.data[i].resetByYear, "category": categoryReferenceId }).draw();
                        gOrderData.push(i + 1);
                    }
                    gOrderData.push(i + 1);
                    $('#cmbOrder').select2('destroy').empty().select2({
                        dir: window.language === "ar" ? "rtl" : "ltr",
                        language: window.language,
                        minimumResultsForSearch: -1,
                        placeholder: Resources.SelectOrder,
                        width: "100%",
                        allowClear: false,
                        data: gOrderData,
                        dropdownParent: $('#cmbOrderContainer')
                    }).on('select2:select', function ()
                    {
                        $('#cmbOrder').parsley().validate();
                    });
                    $('#cmbOrder').val('').trigger('change');
                } else
                {
                    gReferencedObject = [];
                    gOrderData.push(1);
                    $('#cmbOrder').select2('destroy').empty().select2({
                        dir: window.language === "ar" ? "rtl" : "ltr",
                        language: window.language,
                        minimumResultsForSearch: -1,
                        placeholder: Resources.SelectOrder,
                        width: "100%",
                        allowClear: false,
                        data: gOrderData,
                        dropdownParent: $('#cmbOrderContainer')
                    }).on('select2:select', function ()
                    {
                        $('#cmbOrder').parsley().validate();
                    });
                    $('#cmbOrder').val('').trigger('change');
                }
                $('#ReferenceNumberOption').val(retval.referenceNumberGeneratorType).change();
            }, function () { }, false);
        }

        window.Parsley.addValidator('noarabic', {
            validateString: function (value) {
                return !/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]/.test(value);
            },
            messages: {
                en: Resources.ArabicCharactersNotAllowed,
                fr: Resources.ArabicCharactersNotAllowed,
                ar: Resources.ArabicCharactersNotAllowed,
            }
        });

    }


}
export default { CategoryReferenceNumberIndex, CategoryReferenceNumberIndexView };