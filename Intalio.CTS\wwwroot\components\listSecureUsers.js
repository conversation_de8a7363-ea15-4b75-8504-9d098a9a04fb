import Intalio from "./common.js"

let self;
function generateAndSaveSecureUserId(userId) {
    const params = { name: "" };
    Common.mask(document.getElementById('ListSecureUserContainer'), "ListSecureUserContainer-mask");
    Common.ajaxPost('/User/GetAllUserStructures', params, function (response) {
        const secureIds = new Set(
            response.data.userStructures
                .map(item => item.secureUserId)
                .filter(id => id !== null && id !== undefined)
        );

        let newId;
        do {
            newId = Math.floor(10000000 + Math.random() * 90000000).toString();
        } while (secureIds.has(newId));

        Common.ajaxPost(`User/UpdateUserSecureId?userId=${userId}&newSecureUserId=${newId}`, null, function (response) {
            Common.showScreenSuccessMsg();
            Common.unmask("ListSecureUserContainer-mask");
            $(self.refs["grdItems"]).DataTable().ajax.reload();
        }, function () {
            Common.unmask("ListSecureUserContainer-mask");
            Common.showScreenErrorMsg();
        });

    }, function () {
        Common.unmask("ListSecureUserContainer-mask");
        Common.showScreenErrorMsg();
    });
}


class ListSecureUsersView extends Intalio.View {
    constructor(element, model)
    {
        super(element, "listsecureusers", model)
    }
    render()
    {
        self = this;
        let clickedSearch = false;
        $(self.refs['collapseIcon']).click(function ()
        {
            $(self.refs['collapseIcon']).empty();
            if (clickedSearch)
            {
                $(self.refs['collapseIcon']).append('<i class="fa fa-angle-up fa-lg"></i>');
                $(self.refs['collapsePanel']).attr('class', '');
                $(self.refs['collapsePanel']).addClass('panel-body panel-collapse collapse in');
                clickedSearch = false;
            } else
            {
                $(self.refs['collapseIcon']).append('<i class="fa fa-angle-down fa-lg"></i>');
                $(self.refs['collapsePanel']).attr('class', '');
                $(self.refs['collapsePanel']).addClass('panel-body panel-collapse collapse');
                clickedSearch = true;
            }
        });
        Common.gridCommon();
       
        let table = $(self.refs["grdItems"]).on('draw.dt',
        function ()
        {
            $('#grdItems tbody tr td').each(function ()
            {
                this.setAttribute('title', $(this).text());
            });
        }).DataTable({
            processing: true,
            ordering: true,
            serverSide: true,
            pageLength: 10,
            "ajax": {
                "url": "/User/GetUsersWithSecureUserId",
                "type": "GET",
                "datatype": "json",
                data: function (d)
                {
                    d.Name = $(self.refs["txtName"]).val() ?? "";
                    d.UserId = $(self.refs["txtUserId"]).val() ?? "";
                },
                error: function (jqXHR, status, error)
                {
                    Common.ajaxError(jqXHR, status, error);
                }
            },
            "order": [],
            "columns": [
                { title: "Id", data: "id", visible: false, "orderable": false },
                {
                    title: Resources.Name, data: "name", "orderable": false,
                    render: function (data, type, full, meta)
                    {
                        const name = [full.firstName, full.middleName, full.lastName];
                        return name.filter(Boolean).join(" ");
                    }
                },
                { title: Resources.SecureUserId, data: "secureUserId", "orderable": false, render: $.fn.dataTable.render.text() },
                {
                    "className": "text-center",
                    "autoWidth": false,
                    "bAutoWidth": false,
                    'width': '16px',
                    'orderable': false,
                    'sortable': false,
                    'render': function (data, type, full, meta)
                    {
                        if (!full.secureUserId?.toString().trim()) {
                            let btn = document.createElement("button");
                            btn.setAttribute("class", "btn btn-xs btn-primary generateUserId");
                            btn.setAttribute("title", Resources.GenerateSecureId);
                            btn.setAttribute("clickAttr", `generateAndSaveSecureUserId(${full.id})`);
                            btn.innerHTML = "<i class='fa fa-id-card fa-white'/>";
                            return btn.outerHTML;
                        }
                        return '';
                    }
                }
            ],
            "fnInitComplete": function (settings, json)
            {
                $('[data-toggle="tooltip"]').tooltip();
            },
            dom: 'trpi'
        });


        $(self.refs["grdItems"]).find('tbody').on('click', ".generateUserId", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });

        $(self.refs["btnSearch"]).on('click', function ()
        {
            table.ajax.reload();
        });

        $(self.refs["btnClear"]).on('click', function ()
        {
            $(self.refs["txtName"]).val('');
            $(self.refs["txtUserId"]).val('');
            table.ajax.reload();
        });
    }
}

export { ListSecureUsersView }