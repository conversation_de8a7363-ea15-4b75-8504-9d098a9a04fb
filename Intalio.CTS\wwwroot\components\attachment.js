﻿import Intalio from './common.js'
import AttachmentIndex from './attachmentIndex.js'
import AttachmentUserIndex from './attachmentUserIndex.js'
import DuplicateSignatureRegion from './duplicateSignatureRegion.js'
import MultipleUpload from './multipleUpload.js'
import ByFile from './byFile.js'
import ByScan from './byScan.js'
import VersionHistory from './versionHistory.js'
import { CategoryModel, DelegationUsers } from './lookup.js'
import GenerateLetter from './generateLetter.js'
import attachmentsecurity from './attachmentsecurity.js'
import AttachmentProperties from './attachmentProperties.js'


var gLocked = false;
var gRegistered = false, isChecked = false;
var self;
var currentUser;
var currntAttachment;
var currentAttachmentUserSign;
var lastVierwerUrl;
var signatureList = [];


class Attachment extends Intalio.Model {
    constructor() {
        super();
        this.transferId = null;
        this.documentId = null;
        this.delegationId = null;
        this.readOnly = null;
        this.categoryId = null;
        this.openCorrespondenceDefault = window.OpenCorrespondenceMode === "OpenCorrespondenceDefault";
        this.fromInbox = null;
        this.fromDraft = null;
        this.isCced = null;
        this.ownerUserId = null;
        this.parentComponentId = null;
        this.fromVip = null;
        this.parentLinkedDocumentId = null;
        this.showAttachmentProperties = false;
        this.isSigned = false;
        this.fromRejectedDocument = false;
    }
}

function initTree(self, categoriesObj, actionName) {

    var initialLoad = true;
    var isDraft = self.model.transferId === null || typeof self.model.transferId === 'undefined' ? true : false;
    gRegistered = self.model.fromInbox;
    $(self.refs['attachmentTree']).jstree({
        'core': {
            'multiple': false,
            'check_callback': function (operation, node, parent, position, more) {
                if (operation === "edit" && (parent.id === "#" || node.original.id === "folder_originalMail")) {
                    return false;
                }
                else if (operation === "rename_node" && node.text !== position) {
                    renameNodeByF2(node.text, node.id, parent.id === "folder_originalMail" ? "" : parent.id, node.original.type);
                }
                return true;
            },
            'data': {
                'url': "/Attachment/List?documentId=" + self.model.documentId + "&transferId=" + self.model.transferId + "&delegationId=" + self.model.delegationId + "&parentDocumentId=" + self.model.parentLinkedDocumentId,
                'data': function (node) {
                    return node;
                }
            },
            'themes': {
                'icons': true,
                'name': 'proton',
                'responsive': true,
                'ellipsis': true
            }
        },
        "plugins": ["contextmenu", "wholerow", "sort"],
        'sort': function (a, b) {
            var a1 = this.get_node(a);
            var b1 = this.get_node(b);
            if (a1.id == "folder_originalMail" || b1.id == "folder_originalMail") {
                return b1.id == "folder_originalMail" ? 1 : -1;
            }
            else if (a1.icon == b1.icon) {
                return (a1.text > b1.text) ? 1 : -1;
            } else {
                return (a1.icon < b1.icon) ? 1 : -1;
            }
        },
        "contextmenu": {
            "select_node": false,
            "items": function ($node) {
                var tree = $(self.refs['attachmentTree']).jstree(true);
                var extension = ($node.text.split('.')[$node.text.split('.').length - 1]);

                var retValue = {};
                if ($node.original.type === "1") { //folder
                    if (!self.model.readOnly) {
                        if ($node.id == "0") {
                            retValue = {
                                "AddFolder": {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "label": Resources.AddFolder,
                                    "icon": 'fa fa-plus text-color-context',
                                    "action": function () {
                                        addEditNode("#", null, null, 1, false, self.model);
                                    }
                                },
                                "Scan": {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "label": Resources.Scan,
                                    "icon": 'fa fa-print text-color-context',
                                    "action": function () {
                                        scan(self.model, null, self);
                                    }
                                },
                                "Upload": {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "label": Resources.Upload,
                                    "icon": 'fa fa-upload text-color-context',
                                    "action": function () {
                                        upload(self.model, null, self);
                                    }
                                },
                                "MultipleUpload": {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "label": Resources.MultipleUpload,
                                    "icon": 'fa fa-arrow-circle-up text-color-context',
                                    "action": function () {
                                        multipleUpload(self.model, null);
                                    }
                                }
                            };
                        }
                        else if ($node.id == "folder_originalMail") {
                            if ($node.children.length == 0) {
                                retValue = {
                                    "Scan": {
                                        "separator_before": false,
                                        "separator_after": false,
                                        "label": Resources.Scan,
                                        "icon": 'fa fa-print text-color-context',
                                        "action": function () {
                                            scanOriginalMail(self.model, self);
                                        }
                                    },
                                    "Upload": {
                                        "separator_before": false,
                                        "separator_after": false,
                                        "label": Resources.Upload,
                                        "icon": 'fa fa-upload text-color-context',
                                        "action": function () {
                                            uploadOriginalMail(self.model, self);
                                        }
                                    }
                                };
                            }
                        }
                        else {

                            retValue = {
                                "AddFolder": {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "label": Resources.AddFolder,
                                    "icon": 'fa fa-plus text-color-context',
                                    "action": function () {
                                        addEditNode($node.id.split("_")[1], $node.text, $node.parent.split("_")[1], 1, false, self.model);
                                    }
                                },

                                "Rename": !$node.data.hasEditAccess ? null : {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "label": Resources.Rename,
                                    "icon": 'fa fa-edit text-color-context',
                                    "action": function () {
                                        const lastDotIndex = $node.text.lastIndexOf(extension);
                                        if (lastDotIndex === 0 || lastDotIndex === -1) {
                                            $node.text = $node.text;
                                        }
                                        else {
                                            $node.text = $node.text.slice(0, lastDotIndex - 1);
                                        }
                                        addEditNode($node.id.split("_")[1], $node.text, $node.parent.split("_")[1], 1, true, self.model);
                                    }
                                },
                                "Delete": !$node.data.hasEditAccess ? null : {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "label": Resources.Delete,
                                    "icon": 'fa fa-minus-circle text-color-context',
                                    "action": function () {
                                        var deleteConfirmation = Common.showConfirmMsg(Resources.DeleteConfirmation, function () {
                                            Common.ajaxDelete('/Attachment/DeleteFolder',
                                                {
                                                    'id': $node.id.split("_")[1],
                                                    'documentId': self.model.documentId,
                                                    'transferId': self.model.transferId,
                                                    'delegationId': self.model.delegationId,
                                                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                                                },
                                                function (response) {
                                                    if (response.message != undefined) {
                                                        Common.alertMsg(response.message);
                                                    }
                                                    else {
                                                        //Common.showScreenSuccessMsg(Resources.FolderDeletedSuccessfully);
                                                        //tree.delete_node($node);
                                                        //swal.close();
                                                        var selectedNode = tree.get_selected();
                                                        if (selectedNode.length > 0) {
                                                            if (selectedNode[0] == $node.id) {
                                                                $(self.refs['viewerContainer']).addClass("waitingBackground");
                                                                $(self.refs['viewerFrame']).attr("src", "");
                                                                $("#" + self.model.parentComponentId + "_" + "viewerContainer").addClass("waitingBackground");
                                                                $("#" + self.model.parentComponentId + "_" + "viewerFrame").attr("src", "");
                                                            }
                                                        }
                                                        Common.showScreenSuccessMsg(Resources.FileDeletedSuccessfully);
                                                        tree.delete_node($node);
                                                        swal.close();

                                                    }
                                                }, function () { Common.showScreenErrorMsg(); }, false);
                                        }, null, undefined, false);
                                    }
                                },
                                "Scan": {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "label": Resources.Scan,
                                    "icon": 'fa fa-print text-color-context',
                                    "action": function () {
                                        scan(self.model, $node.id.split("_")[1], self);
                                    }
                                },
                                "Upload": {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "label": Resources.Upload,
                                    "icon": 'fa fa-upload text-color-context',
                                    "action": function () {
                                        upload(self.model, $node.id.split("_")[1], self);
                                    }
                                },
                                "MultipleUpload": {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "label": Resources.MultipleUpload,
                                    "icon": 'fa fa-arrow-circle-up text-color-context',
                                    "action": function () {
                                        multipleUpload(self.model, $node.id.split("_")[1], self);
                                    }
                                },
                                "AttachmentSecurity": ($node.parent == "folder_originalMail") ? null : {
                                    "separator_before": false,
                                    "separator_after": false,
                                    "label": Resources.AttachmentSecurity,
                                    "icon": 'fa fa-shield text-color-context',
                                    "action": function () {

                                        var nodeId = $node.id.split("_")[1];
                                        if ($node.id.startsWith("folder")) {
                                            SetAttachmentSecurity(nodeId, self, "folder");
                                        } else {
                                            SetAttachmentSecurity(nodeId, self, "attachment");
                                        }

                                    }

                                }
                            };
                        }
                    }
                    else { 
                        retValue = {
                            "AttachmentSecurity": !($node.id != "folder_originalMail" || !$node.data.isUserCreated) ? null : {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.AttachmentSecurity,
                                "icon": 'fa fa-shield text-color-context',
                                "action": function () {
                                    SetAttachmentSecurity($node.id.split("_")[1], self, "folder")
                                }
                            }
                        };
                        console.log(retValue);
                    }
                } else { // file
                    if (self.model.readOnly) {
                        retValue = {
                            "Download": {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.Download,
                                "icon": 'fa fa-download text-color-context',
                                "action": function () {
                                    download(self.model, $node.id.split("_")[1]);
                                }
                            },
                            "ViewHistory": {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.ViewHistory,
                                "icon": 'fa fa-history text-color-context',
                                "action": function () {
                                    versionHistory($node.id.split("_")[1], self.model, self.model.readOnly ? false : $node.data.hasEditAccess);
                                }
                            },
                            "AttachmentSecurity": ($node.parent == "folder_originalMail" || !$node.data.isUserCreated) ? null : {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.AttachmentSecurity,
                                "icon": 'fa fa-shield text-color-context',
                                "action": function () {
                                    SetAttachmentSecurity($node.id.split("_")[1], self, "attachment")
                                }
                            }
                        };
                    } else {
                        retValue = {
                            "Rename": !$node.data.hasEditAccess ? null : {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.Rename,
                                "icon": 'fa fa-edit text-color-context',
                                "action": function () {
                                    const lastDotIndex = $node.text.lastIndexOf(extension);
                                    if (lastDotIndex === -1) {
                                        $node.text = $node.text;
                                    }
                                    else {
                                        $node.text = $node.text.slice(0, lastDotIndex - 1);
                                    }
                                    addEditNode($node.id.split("_")[1], $node.text, $node.parent.split("_")[1], 2, true, self.model, extension);
                                }
                            },
                            "Replace": !$node.data.hasEditAccess ? null : {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.Replace,
                                "icon": 'fa fa-files-o text-color-context',
                                "action": function () {
                                    parent = $node.parent.split("_")[1];
                                    if (parent == "originalMail") {
                                        parent = "0";
                                    }
                                    replace(self.model, $node.id.split("_")[1], parent, self);
                                }
                            },
                            "Download": {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.Download,
                                "icon": 'fa fa-download text-color-context',
                                "action": function () {
                                    download(self.model, $node.id.split("_")[1]);
                                }
                            },
                            "Delete": !$node.data.hasEditAccess ? null : {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.Delete,
                                "icon": 'fa fa-minus-circle text-color-context',
                                "action": function () {
                                    Common.showConfirmMsg(Resources.DeleteConfirmation, function () {
                                        Common.ajaxDelete('/Attachment/DeleteFile',
                                            {
                                                'id': $node.id.split("_")[1],
                                                'documentId': self.model.documentId,
                                                'transferId': self.model.transferId,
                                                'delegationId': self.model.delegationId,
                                                '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                                            },
                                            function (response) {
                                                if (response.message != undefined) {
                                                    Common.alertMsg(response.message);
                                                }
                                                else {
                                                    var selectedNode = tree.get_selected();
                                                    if (selectedNode.length > 0) {
                                                        if (selectedNode[0] == $node.id) {
                                                            $(self.refs['viewerContainer']).addClass("waitingBackground");
                                                            $(self.refs['viewerFrame']).attr("src", "");
                                                            $("#" + self.model.parentComponentId + "_" + "viewerContainer").addClass("waitingBackground");
                                                            $("#" + self.model.parentComponentId + "_" + "viewerFrame").attr("src", "");
                                                        }
                                                    }
                                                    Common.showScreenSuccessMsg(Resources.FileDeletedSuccessfully);
                                                    tree.delete_node($node);
                                                    swal.close();
                                                }
                                            }, function () { Common.showScreenErrorMsg(); }, false);
                                    }, null, undefined, false);
                                }
                            },
                            "ViewHistory": {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.ViewHistory,
                                "icon": 'fa fa-history text-color-context',
                                "action": function () {
                                    versionHistory($node.id.split("_")[1], self.model, $node.data.hasEditAccess);
                                }
                            },
                            "ConvertToPdf": self.model.readOnly ? null : {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.ConvertToPdf,
                                "icon": 'fa fa-file-pdf-o text-color-context',
                                "action": function () {
                                    if (window.EnableConfirmationMessage === "True") {
                                        Common.showConfirmMsg(Resources.ProceedConfirmation, function () {
                                            convertToPdf($node.id.split("_")[1], self);
                                        });
                                    } else {
                                        convertToPdf($node.id.split("_")[1], self);
                                    }
                                }
                            },
                            "GenerateLetter": self.model.readOnly ? null : {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.GenerateLetter,
                                "icon": 'fa fa-pencil-square-o text-color-context',
                                "action": function () {
                                    generateLetter($node.id.split("_")[1], self.model, self);
                                }
                            },
                            //"AssignArea": (self.model.readOnly || (window.AttachmentEditable == "False" && $node.parent != "folder_originalMail") || (extension != "pdf" && extension != "docx")) ? null : {
                            //    "separator_before": false,
                            //    "separator_after": false,
                            //    "label": Resources.ConfigureSignature,
                            //    "icon": 'fa fa-history text-color-context',
                            //    "action": function () {
                            //        Common.ajaxGet('/Attachment/CheckAttachmentisLocked', { 'attachmentId': $node.id.split("_")[1] }, function (response) {
                            //            if (response == true) {
                            //                Common.alertMsg(Resources.Locked);
                            //            } else
                            //                configureSignature($node.id.split("_")[1], self.model);
                            //        }, function () { }, false, null, false);
                            //    }
                            //},

                            "AttachmentSecurity": ($node.parent == "folder_originalMail" || !$node.data.isUserCreated) ? null : {
                                "separator_before": false,
                                "separator_after": false,
                                "label": Resources.AttachmentSecurity,
                                "icon": 'fa fa-shield text-color-context',
                                "action": function () {
                                    SetAttachmentSecurity($node.id.split("_")[1], self, "attachment")
                                }
                            }

                        };
                    }
                    if (self.model.showAttachmentProperties) {
                        retValue.AttachmentProperties = ($node.parent == "folder_originalMail") ? null : {
                            "separator_before": false,
                            "separator_after": false,
                            "label": Resources.AttachmentProperties,
                            "icon": 'fa fa-book text-color-context',
                            "action": function () {
                                OpenAttchmentProperties($node, self.model.readOnly || !$node.data.hasEditAccess);
                            }
                        }
                    }
                }
                if ($node.parent == "folder_originalMail") {
                    // Delete the "delete" menu item
                    delete retValue.Delete;
                    let isBroadcast = false;
                    let isCarbonCopy = false;
                    let isMultipleReceivingEntity = false;
                    if (categoriesObj) {
                        if (categoriesObj.basicAttribute !== "" && categoriesObj.basicAttribute !== null) {
                            let basicAttributes = JSON.parse(categoriesObj.basicAttribute);
                            if (basicAttributes.length > 0) {
                                let receivingEntityObj = $.grep(basicAttributes, function (e) {
                                    return e.Name === "ReceivingEntity";
                                });
                                if (receivingEntityObj[0].BroadcastReceivingEntity) {
                                    isBroadcast = true;
                                }
                                let carbonCopy = $.grep(basicAttributes, function (e) {
                                    return e.Name === "CarbonCopy";
                                });
                                if (carbonCopy[0].Enabled) {
                                    isCarbonCopy = true;
                                } else {
                                    if (receivingEntityObj[0].MultipleReceivingEntity) {
                                        isMultipleReceivingEntity = true;
                                    }
                                }
                            }
                        }
                    }
                    if (self.model.fromDraft && $node.data.isWord) {
                        if ($(".referenceNumber").length === 0 && !isChecked) {
                            Common.ajaxGet('/Document/IsDocumentRegistered', { 'id': self.model.documentId }, function (response) {
                                isChecked = true;
                                gRegistered = response;
                            }, function () { }, false, null, false);
                        } else {
                            gRegistered = $(".referenceNumber").length === 0 && isChecked ? gRegistered : $(".referenceNumber").text() !== "";
                        }
                    }
                    retValue = filterContextMenu(retValue, $node, isBroadcast, isCarbonCopy, isMultipleReceivingEntity);

                    retValue.ViewHistory.action = function () {
                        versionHistory($node.id.split("_")[1], self.model, self.model.readOnly ? false : $node.data.hasEditAccess, $node.data.isSigned);
                    }
                    if ($node.data.isSigned) {
                        delete retValue.Replace;
                    }
                } else {
                    delete retValue.ConvertToPdf;
                    delete retValue.GenerateLetter;
                }

                if (actionName != undefined) {
                    var actionArray = actionName.split("_");

                    if (!(actionArray.includes("Folder.AddFolder"))) {
                        delete retValue.AddFolder
                    }
                    if (!(actionArray.includes("Folder.Scan"))) {
                        delete retValue.Scan
                    }
                    if (!(actionArray.includes("Folder.Upload"))) {
                        delete retValue.Upload
                    }
                    if (!(actionArray.includes("Folder.MultipleUpload"))) {
                        delete retValue.MultipleUpload
                    }
                    if (!(actionArray.includes("File.Rename"))) {
                        delete retValue.Rename
                    }
                    if (!(actionArray.includes("File.Replace"))) {
                        delete retValue.Replace
                    }
                    if (!(actionArray.includes("File.Download"))) {
                        delete retValue.Download
                    }
                    if (!(actionArray.includes("File.Delete"))) {
                        delete retValue.Delete
                    }
                    if (!(actionArray.includes("File.ViewHistory"))) {
                        delete retValue.ViewHistory
                    }
                    if (!(actionArray.includes("File.ConvertToPdf"))) {
                        delete retValue.ConvertToPdf
                    }
                    if (!(actionArray.includes("File.GenerateLetter"))) {
                        delete retValue.GenerateLetter
                    }
                    if (!(actionArray.includes("File.AssignArea"))) {
                        delete retValue.AssignArea
                    }
                    if (!(actionArray.includes("File.AttachmentSecurity"))) {
                        delete retValue.AttachmentSecurity
                    }
                    if (!(actionArray.includes("File.AttachmentProperties"))) {
                        delete retValue.AttachmentProperties
                    }
                }



                return retValue;
            }
        }
    }).on("select_node.jstree", function (e, data) {
        
        var isCustomMode = window.ViewerMode === '1' ? "false" :"true";

        if (data.node.original.type == "2" || data.node.id == "folder_originalMail") {
            $(".tree-custom-toolbarBtn").attr('disabled', 'disabled');
            if (data.node.original.type == "2") {
                localStorage.setItem('selectedNodeFileId', data.node.id);
                var viewerUrl = window.ViewerUrl + "/templates?documentId=" + data.node.id.split("_")[1] + "&language=" + window.language + "&token=" +
                    window.IdentityAccessToken + "&version=autocheck&ctsDocumentId=" + self.model.documentId +
                    "&ctsTransferId=" + self.model.transferId + "&delegationId=" + self.model.delegationId + "&isDraft=" + isDraft + "&structId=" + Number($("#hdLoggedInStructureId").val())
                    + "&isCustomMode=" + isCustomMode;

                if (window.viewOnlyMode == 'full' && self.model.attachmentVersion !== undefined && self.model.attachmentVersion !== null && self.model.attachmentVersion.length > 0) {
                    viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + self.model.attachmentVersion)
                }

                if (self.model.fromInbox) {
                    //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
                    var delegatedUser = self.model.delegationId !== null ? new DelegationUsers().getById(Number(self.model.delegationId)) : null; //$("#hdDelegatedUserIds").val().split(window.Splitter);
                    var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                    if ((data.node.parent !== "folder_originalMail" && window.AttachmentEditable !== "True") || self.model.isCced ||
                        !((self.model.ownerUserId !== null && self.model.ownerUserId === Number($("#hdUserId").val()) && self.model.delegationId === null) ||
                            (self.model.ownerDelegatedUserId !== null && self.model.ownerDelegatedUserId === Number($("#hdUserId").val())
                                && self.model.ownerUserId !== null && delegatedUserId === self.model.ownerUserId && self.model.delegationId !== null) ||
                            (self.model.ownerUserId !== null && delegatedUserId === self.model.ownerUserId && self.model.delegationId !== null))) {
                        viewerUrl = viewerUrl + "&viewermode=view";
                        if (window.viewOnlyMode == 'read')
                            viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + self.model.attachmentVersion)

                    }
                    else if (self.model.readOnly || data.node.data.isLocked)
                    {
                        viewerUrl = viewerUrl + "&viewermode=view";
                        if (window.viewOnlyMode == 'read')
                            viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + self.model.attachmentVersion)
                    }
                }
                else if (self.model.fromDraft) {
                    if ((data.node.parent !== "folder_originalMail" && window.AttachmentEditable !== "True")) {
                        viewerUrl = viewerUrl + "&viewermode=view";
                        if (window.viewOnlyMode == 'read')
                            viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + self.model.attachmentVersion)
                    }
                }
                else if (self.model.categoryId == window.FollowUpCategory)
                {
                    viewerUrl = viewerUrl + "&viewermode=edit";
                    if (window.viewOnlyMode == 'read')
                        viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + self.model.attachmentVersion)
                }
                else {
                    viewerUrl = viewerUrl + "&viewermode=view";
                    if (window.viewOnlyMode == 'read')
                        viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + self.model.attachmentVersion)
                }
                if (self.model.openCorrespondenceDefault) {
                    $(self.refs['viewerContainer']).removeClass("waitingBackground");
                    $(self.refs['viewerFrame']).attr("src", viewerUrl);
                } else {
                    $("#" + self.model.parentComponentId + "_" + "viewerContainer").removeClass("waitingBackground");
                    $("#" + self.model.parentComponentId + "_" + "viewerFrame").attr("src", "");
                    $("#" + self.model.parentComponentId + "_" + "viewerFrame").attr("src", viewerUrl);
                }
                lastVierwerUrl = viewerUrl;
            }
            else if (data.node.id == "folder_originalMail" && data.node.children.length == 0) {
                $(self.refs['btnScan']).removeAttr('disabled');
                $(self.refs['btnAttachmentUpload']).removeAttr('disabled');
            }
        }
        else if (!self.model.readOnly) {
            $(".tree-custom-toolbarBtn").removeAttr('disabled');
        }
        self.model.fromVip && window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer" && data.node.original.type != "1" && data.node.id != "folder_originalMail" ? refreshPreviewTab() : null;
    }).on("hover_node.jstree", function (e, data) {
        if (data.node.original) {
            $("#" + data.node.id + "_anchor").attr("title", data.node.original.title);
        }
    }).on("loaded.jstree", function (e, data) {
        let nodes = data?.instance?._model?.data;
        if (nodes) {
            const listFromArray = Object.entries(nodes);
            if (listFromArray && listFromArray.length) {
                for (var i = 0; i < listFromArray.length; i++) {
                    if (listFromArray[i][1].original && listFromArray[i][1].original.isLocked) {
                        document.querySelectorAll("#" + listFromArray[i][0] + "_anchor")[0].style.setProperty("color", "#23b7e5", "important");
                    }
                }
            }
        }
        if (initialLoad) {
            initialLoad = false;
            //$(self.refs['attachmentTree']).jstree().refresh();

            EventReceiver.AttachmentsAfterRender(self);
            var selectedNodes = $(self.refs['attachmentTree']).jstree("get_selected", true);
            if (selectedNodes != null && selectedNodes.length > 0) {
                var selectedNode = selectedNodes[0]
                if (selectedNode != null) {
                    var nodeobj = { "node": selectedNode }
                    $(self.refs['attachmentTree']).jstree().trigger("select_node.jstree", nodeobj);
                }
            }
        }
    });
}
function filterContextMenu(retValue, node, isBroadcast, isCarbonCopy, isMultipleReceivingEntity) {
    if (!node.data.isWord || !gRegistered) {
        delete retValue.ConvertToPdf;
    }
    if (!node.data.isWord || isBroadcast || !(isCarbonCopy || isMultipleReceivingEntity) || !gRegistered) {
        delete retValue.GenerateLetter;
    }
    return retValue;
}
function addEditNode(id, name, parentId, type, edit, model, extension=null) {
    let modalWrapper = $(".modal-window");
    modalWrapper.find("#modalAttachmentNode").remove();
    var attachmentIndexModel = new AttachmentIndex.AttachmentIndex();
    attachmentIndexModel.extension = extension;
    var attachmentIndex = new AttachmentIndex.AttachmentIndexView(modalWrapper, attachmentIndexModel);
    attachmentIndex.render();
    $("#formAttachmentNodeActionPost").parsley().reset();
    $('#hdAttachmentNodeDocumentId').val(model.documentId);
    $('#hdAttachmentNodeTransferId').val(model.transferId);
    $('#hdDelegationId').val(model.delegationId);
    $('#hdAttachmentNodeType').val(type);
    if (edit) {
        $("#hdAttachmentNodeId").val(id);
        $("#hdAttachmentNodeParentId").val(parentId === "0" || parentId == "originalMail" ? null : parentId);
        $("#txtAttachmentNodeName").val(name);
        $("#hdEditAttachmentNodeMode").val(true);
        $('#modalAttachmentNodeTitle').html(Resources.Rename);
    } else {
        $("#hdAttachmentNodeParentId").val(id === "#" ? '' : id);
        $("#hdAttachmentNodeId").val('');
        $("#txtAttachmentNodeName").val('');
        $("#hdEditAttachmentNodeMode").val(false);
        $('#modalAttachmentNodeTitle').html(Resources.AddFolder);
    }
    $('#modalAttachmentNode').modal('show');
    $("#modalAttachmentNode").off("hidden.bs.modal");
    $("#modalAttachmentNode").off("shown.bs.modal");
    $('#modalAttachmentNode').on('shown.bs.modal', function () {
        document.getElementById('txtAttachmentNodeName').focus();
    });
    $('#modalAttachmentNode').on('hidden.bs.modal', function () {
        $('#formAttachmentNodeActionPost').parsley().reset();
        $('#hdAttachmentNodeId').val('');
        $('#hdAttachmentNodeParentId').val('');
        $('#txtAttachmentNodeName').val('');
        $("#hdEditAttachmentNodeMode").val(false);
        $('#hdAttachmentNodeDocumentId').val('');
        $('#hdAttachmentNodeTransferId').val('');
        $('#hdAttachmentNodeType').val('');
        $('#hdDelegationId').val('');
        $('#modalAttachmentNode').remove();
    });
}
function multipleUpload(selfModel, parentId) {
    let modalWrapper = $(".modal-window");
    modalWrapper.find("#modalMultipleUpload").remove();
    var model = new MultipleUpload.MultipleUpload();
    model.transferId = selfModel.transferId;
    model.documentId = selfModel.documentId;
    model.parentId = parentId;
    model.categoryId = selfModel.categoryId,
        model.url = "/Attachment/Upload";
    var multipleUpload = new MultipleUpload.MultipleUploadView(modalWrapper, model);
    multipleUpload.render();
    $('#modalMultipleUpload').modal('show');
    $("#modalMultipleUpload").off("hidden.bs.modal");
    $("#modalMultipleUpload").off("shown.bs.modal");
    $('#modalMultipleUpload').on('shown.bs.modal', function () {
        $('#btnMultipleUpload').focus();
    });
    $('#modalMultipleUpload').on('hidden.bs.modal', function () {
        $('#modalMultipleUpload').remove();
    });
}
function OpenAttchmentProperties(attachmentObject,readonly) {
    var ModalWrapper = $(".modal-window");
    ModalWrapper.find("#modalAttachmentProperties").remove();
    var Modalmodel = new AttachmentProperties.AttachmentProperties();
    Modalmodel.attachmentObject = attachmentObject;
    Modalmodel.readonly = readonly;
    var ModalView = new AttachmentProperties.AttachmentPropertiesView(ModalWrapper, Modalmodel);
    ModalView.render();
    $('#modalAttachmentProperties').modal('show');
    $("#modalAttachmentProperties").off("hidden.bs.modal");
    $("#modalAttachmentProperties").off("shown.bs.modal");
    $("#modalAttachmentProperties").on('shown.bs.modal', function () {
        $("#btnSave").focus();
    });
    $('#modalAttachmentProperties').on('hidden.bs.modal', function () {
        $('#modalAttachmentProperties').remove();
    });
}
function upload(selfModel, parentId, self) {
    var uploadOptions = {
        url: "/Attachment/Upload",
        params: {
            'documentId': selfModel.documentId,
            'transferId': selfModel.transferId,
            'parentId': parentId,
            'delegationId': selfModel.delegationId,
            'categoryId': selfModel.categoryId,
            'fromRejectedDocument': selfModel.fromRejectedDocument,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (response) {
            Common.showScreenSuccessMsg(Resources.FileUploadedSuccessfully);
            let parentNodeId = response.parentId;
            $(self.refs['attachmentTree']).jstree().create_node(parentNodeId, response, "last", null);
            $('#modalFileUpload').modal('hide');
            if (selfModel.showAttachmentProperties) {
                OpenAttchmentProperties(response, selfModel.readOnly || !response.data.hasEditAccess);
            }
        },
        errorCallback: function (response) {
            if (response.responseJSON !== undefined && response.responseJSON.message !== undefined && response.responseJSON.message !== null) {
                Common.alertMsg(response.responseJSON.message);
            }
            else {
                Common.showScreenErrorMsg();
            }
        }
    };
    var byFileWrapper = $(".modal-window");
    byFileWrapper.find("#modalFileUpload").remove();
    var model = new ByFile.ByFile();
    model.isModal = true;
    model.categoryId = null;
    var byFileView = new ByFile.ByFileView(byFileWrapper, model);
    byFileView.render(uploadOptions);

    $('#modalFileUpload').modal('show');
    $("#modalFileUpload").off("hidden.bs.modal");
    $("#modalFileUpload").off("shown.bs.modal");
    $("#modalFileUpload").on('shown.bs.modal', function () {
        $("#" + model.ComponentId + "_fileOriginalMail").focus();
    });
    $('#modalFileUpload').on('hidden.bs.modal', function () {
        $('#modalFileUpload').remove();
    });
}
function uploadOriginalMail(selfModel, self) {
    var uploadOptions = {
        url: "/Attachment/UploadOriginalMail",
        params: {
            'documentId': selfModel.documentId,
            'transferId': selfModel.transferId,
            'delegationId': selfModel.delegationId,
            'categoryId': selfModel.categoryId,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (response) {
            Common.showScreenSuccessMsg(Resources.FileUploadedSuccessfully);
            let parentNodeId = response.parentId;
            $(self.refs['attachmentTree']).jstree().create_node(parentNodeId, response, "last", null);
            $('#modalFileUpload').modal('hide');
            var node = $(self.refs['attachmentTree']).jstree().get_selected(true)[0];
            if (node != undefined && node.id == "folder_originalMail") {
                $(".tree-custom-toolbarBtn").attr('disabled', 'disabled');
            }
        },
        errorCallback: function (response) {
            if (typeof response !== "undefined" && response.responseJSON !== undefined && response.responseJSON.message !== undefined && response.responseJSON.message !== null) {
                Common.alertMsg(response.responseJSON.message);
            }
            else {
                Common.showScreenErrorMsg();
            }
        }
    };
    var byFileWrapper = $(".modal-window");
    byFileWrapper.find("#modalFileUpload").remove();
    var model = new ByFile.ByFile();
    model.isModal = true;
    model.categoryId = selfModel.categoryId.toString();
    var byFileView = new ByFile.ByFileView(byFileWrapper, model);
    byFileView.render(uploadOptions);

    $('#modalFileUpload').modal('show');
    $("#modalFileUpload").off("hidden.bs.modal");
    $("#modalFileUpload").off("shown.bs.modal");
    $("#modalFileUpload").on('shown.bs.modal', function () {
        $("#" + model.ComponentId + "_fileOriginalMail").focus();
    });
    $('#modalFileUpload').on('hidden.bs.modal', function () {
        $('#modalFileUpload').remove();
    });
}
function generateLetter(attachmentId, selfModel, self) {
    var generateLetterOptions = {
        url: "/Attachment/GenerateLetter",
        params: {
            'id': attachmentId,
            'delegationId': selfModel.delegationId,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (response) {
            if (response === "OriginalFileInUse") {
                setTimeout(function () {
                    Common.alertMsg(Resources.OriginalFileInUse);
                }, 400);
            } else if (response === "GenerateLetterNotComplete") {
                setTimeout(function () {
                    Common.alertMsg(Resources.GenerateLetterNotComplete);
                }, 400);
            } else if (response === "SelectReceivingEntityOrCarbonCopy") {
                setTimeout(function () {
                    Common.alertMsg(Resources.SelectReceivingEntityOrCarbonCopy);
                }, 400);
            } else if (response === "AtLeastOneReceivingEntityOrCarbonCopy") {
                setTimeout(function () {
                    Common.alertMsg(Resources.AtLeastOneReceivingEntityOrCarbonCopy);
                }, 400);
            } else {
                Common.showScreenSuccessMsg(Resources.GeneratedLetterSuccessfully);
                var nodeId = "file_" + attachmentId;
                var fullNode = $(".attachmentTree").jstree().get_node(nodeId);
                fullNode.icon = 'fa fa-file-pdf-o';
                fullNode.text = fullNode.text.substr(0, fullNode.text.lastIndexOf('.')) + ".pdf";
                fullNode.data.isWord = false;
                $(".attachmentTree").jstree().redraw_node(fullNode);
                $('#modalFileUpload').modal('hide');
                $('#modalGenerateLetter').modal('hide');
                $(self.refs['attachmentTree']).jstree("deselect_all", true);
                $(self.refs['attachmentTree']).jstree().select_node(nodeId);
                if (selfModel.fromVip && window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer") {
                    refreshPreviewTab();
                }
            }
        },
        errorCallback: function (response) {
            if (typeof response !== "undefined" && response !== undefined && response !== null && response !== "") {
                Common.alertMsg(response);
            }
            else {
                $('#modalGenerateLetter').modal('hide');
                $(self.refs['attachmentTree']).jstree().refresh();
            }
        }
    };
    var generateLetterWrapper = $(".modal-window");
    generateLetterWrapper.find("#modalGenerateLetter").remove();
    var generateLetterView = new GenerateLetter.GenerateLetterView(generateLetterWrapper, null);
    generateLetterView.render(generateLetterOptions);
    $('#modalGenerateLetter').modal('show');
    $("#modalGenerateLetter").off("hidden.bs.modal");
    $("#modalGenerateLetter").off("shown.bs.modal");
    $("#modalGenerateLetter").on('shown.bs.modal', function () {
        $("#" + model.ComponentId + "_fileOriginalMail").focus();
    });
    $('#modalGenerateLetter').on('hidden.bs.modal', function () {
        $('#modalGenerateLetter').remove();
    });
}
function convertToPdf(id, self) {
    Common.mask(self.refs['attachmentTreeContainer'], "attachmentTreeContainer-mask");
    Common.ajaxPost('/Attachment/ConvertToPdf',
        {
            'id': id,
            'delegationId': self.model.delegationId,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        function (response) {
            Common.unmask("attachmentTreeContainer-mask");
            if (response === "OriginalFileInUse") {
                setTimeout(function () {
                    Common.alertMsg(Resources.OriginalFileInUse);
                }, 400);
            } else if (response === "GenerateLetterNotComplete") {
                setTimeout(function () {
                    Common.alertMsg(Resources.GenerateLetterNotComplete);
                }, 400);
            }
            else {
                Common.showScreenSuccessMsg(Resources.FileConvertedToPdfSuccessfully);
                var nodeId = "file_" + id;
                var fullNode = $(".attachmentTree").jstree().get_node(nodeId);
                fullNode.icon = 'fa fa-file-pdf-o';
                fullNode.text = fullNode.text.substr(0, fullNode.text.lastIndexOf('.')) + ".pdf";
                fullNode.data.isWord = false;
                $(".attachmentTree").jstree().redraw_node(fullNode);
                $(self.refs['attachmentTree']).jstree("deselect_all", true);
                $(self.refs['attachmentTree']).jstree().select_node(nodeId);
                swal.close();
                if (self.model.fromVip && window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer") {
                    refreshPreviewTab();
                }
                $('*[id*=_btnSignTemplate]').length > 0 ? $('*[id*=_btnSignTemplate]').hide() : null;
                $('.btn-SignOperations').length > 0 ? $('.btn-SignOperations').hide() : null;
            }
        }, function () { Common.unmask("attachmentTreeContainer-mask"); Common.showScreenErrorMsg(); }, false);
}
function scan(selfModel, parentId, self) {
    var uploadOptions = {
        url: "/Attachment/Upload",
        params: {
            'documentId': selfModel.documentId,
            'transferId': selfModel.transferId,
            'parentId': parentId,
            'delegationId': selfModel.delegationId,
            'categoryId': selfModel.categoryId,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (response) {
            Common.showScreenSuccessMsg(Resources.FileUploadedSuccessfully);
            let parentNodeId = response.parentId;
            $(self.refs['attachmentTree']).jstree().create_node(parentNodeId, response, "last", null);
            $('#modalFileScan').modal('hide');
            if (selfModel.showAttachmentProperties) {
                OpenAttchmentProperties(response, selfModel.readOnly || !response.data.hasEditAccess);
            }
        },
        errorCallback: function (response) {
            if (response.responseJSON !== undefined && response.responseJSON.message !== undefined && response.responseJSON.message !== null) {
                Common.alertMsg(response.responseJSON.message);
            }
            else {
                Common.showScreenErrorMsg();
            }
        }
    };
    var byScanWrapper = $(".modal-window");
    byScanWrapper.find("#modalFileScan").remove();
    var byScanModel = new ByScan.ByScan();
    byScanModel.isModal = true;
    byScanModel.categoryId = selfModel.categoryId;
    var byScanView = new ByScan.ByScanView(byScanWrapper, byScanModel);
    byScanView.render(uploadOptions);

    $('#modalFileScan').modal('show');
    $("#modalFileScan").off("hidden.bs.modal");
    $("#modalFileScan").off("shown.bs.modal");
    $("#modalFileScan").on('shown.bs.modal', function () {
        $("#btnLoad").focus();
    });
    $('#modalFileScan').on('hidden.bs.modal', function () {
        $('#modalFileScan').remove();
    });
}
function scanOriginalMail(selfModel, self) {
    var uploadOptions = {
        url: "/Attachment/UploadOriginalMail",
        params: {
            'documentId': selfModel.documentId,
            'transferId': selfModel.transferId,
            'delegationId': selfModel.delegationId,
            'categoryId': selfModel.categoryId,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (response) {
            Common.showScreenSuccessMsg(Resources.FileUploadedSuccessfully);
            let parentNodeId = response.parentId;
            $(self.refs['attachmentTree']).jstree().create_node(parentNodeId, response, "last", null);
            $('#modalFileScan').modal('hide');
            var node = $(self.refs['attachmentTree']).jstree().get_selected(true)[0];
            if (node != undefined && node.id == "folder_originalMail") {
                $(".tree-custom-toolbarBtn").attr('disabled', 'disabled');
            }
        },
        errorCallback: function (response) {
            if (response.responseJSON !== undefined && response.responseJSON.message !== undefined && response.responseJSON.message !== null) {
                Common.alertMsg(response.responseJSON.message);
            }
            else {
                Common.showScreenErrorMsg();
            }
        }
    };
    var byScanWrapper = $(".modal-window");
    byScanWrapper.find("#modalFileScan").remove();
    var byScanModel = new ByScan.ByScan();
    byScanModel.isModal = true;
    byScanModel.categoryId = selfModel.categoryId;
    var byScanView = new ByScan.ByScanView(byScanWrapper, byScanModel);
    byScanView.render(uploadOptions);

    $('#modalFileScan').modal('show');
    $("#modalFileScan").off("hidden.bs.modal");
    $("#modalFileScan").off("shown.bs.modal");
    $("#modalFileScan").on('shown.bs.modal', function () {
        $("#btnLoad").focus();
    });
    $('#modalFileScan').on('hidden.bs.modal', function () {
        $('#modalFileScan').remove();
    });
}
function versionHistory(fileId, selfModel, hasEditAccess, isSigned) {
    var versionWrapper = $(".modal-window");
    versionWrapper.find("#modalVersionHistory").remove();
    var versionModel = new VersionHistory.VersionHistory();
    versionModel.fileId = fileId;
    versionModel.transferId = selfModel.transferId;
    versionModel.documentId = selfModel.documentId;
    versionModel.delegationId = selfModel.delegationId;
    versionModel.hasEditAccess = hasEditAccess;
    versionModel.allowRestore = isSigned ? false : true;
    var versionView = new VersionHistory.VersionHistoryView(versionWrapper, versionModel);
    let callback = selfModel.fromVip && window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer" ? refreshPreviewTab : null;
    versionView.render(callback);

    $('#modalVersionHistory').modal('show');
    $("#modalVersionHistory").off("hidden.bs.modal");
    $("#modalVersionHistory").off("shown.bs.modal");

    $('#modalVersionHistory').on('hidden.bs.modal', function () {
        $('#modalVersionHistory').remove();
    });
}
function replace(selfModel, id, parentId, self) {
    var uploadOptions = {
        url: "/Attachment/Replace",
        params: {
            'id': id,
            'documentId': selfModel.documentId,
            'transferId': selfModel.transferId,
            'parentId': parentId,
            'delegationId': selfModel.delegationId,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (response) {
            //$(self.refs['attachmentTree']).jstree().rename_node(response, response.text);
            var fullNode = $(".attachmentTree").jstree().get_node(response.id);
            fullNode.text = response.text;
            fullNode.original.title = response.text;
            $(self.refs['attachmentTree']).jstree().redraw_node(fullNode);
            $(self.refs['attachmentTree']).jstree().rename_node(fullNode, response.text);
            $(self.refs['attachmentTree']).jstree().set_icon(response, response.icon);
            $(self.refs['attachmentTree']).jstree().get_node(response).data.version = response.data.version;
            $(self.refs['attachmentTree']).jstree().get_node(response).data.isWord = response.data.isWord;
            $('#modalFileUpload').modal('hide');
            Common.showScreenSuccessMsg(Resources.FileReplacedSuccessfully);
            $(self.refs['attachmentTree']).jstree("deselect_all", true);
            $(self.refs['attachmentTree']).jstree().select_node(response.id);
            if (self.model.fromVip && window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer") {
                refreshPreviewTab();
            }
            if (selfModel.showAttachmentProperties) {
                OpenAttchmentProperties(response, selfModel.readOnly || !response.data.hasEditAccess);
            }
        },
        errorCallback: function (response) {
            if (response.responseJSON !== undefined && response.responseJSON.message !== undefined && response.responseJSON.message !== null) {
                Common.alertMsg(response.responseJSON.message);
            }
            else {
                Common.showScreenErrorMsg();
            }
        }
    };
    var byFileWrapper = $(".modal-window");
    byFileWrapper.find("#modalFileUpload").remove();
    var model = new ByFile.ByFile();
    model.isModal = true;
    if (parentId === "0") {
        model.categoryId = selfModel.categoryId.toString();
    }
    else {
        model.categoryId = null;
    }
    var byFileView = new ByFile.ByFileView(byFileWrapper, model);
    byFileView.render(uploadOptions);
    $('#modalFileUpload').modal('show');
    $("#modalFileUpload").off("hidden.bs.modal");
    $("#modalFileUpload").off("shown.bs.modal");
    $("#modalFileUpload").on('shown.bs.modal', function () {
        $("#" + model.ComponentId + "_fileOriginalMail").focus();
    });
    $('#modalFileUpload').on('hidden.bs.modal', function () {
        $('#modalFileUpload').remove();
    });
}
function download(selfModel, id) {
    Common.ajaxGet('/Attachment/Download?id=' + id + "&transferId=" + selfModel.transferId + "&delegationId=" + selfModel.delegationId, null, function (data) {
        window.location.href = '/Attachment/Download?id=' + id + "&transferId=" + selfModel.transferId + "&delegationId=" + selfModel.delegationId;
    }, function (err) {
        if (err == "Unauthorized") {
            Common.alertMsg(Resources.NoPermission);
        }
    });
}
function renameNodeByF2(nodeName, nodeId, parentId, nodeType) {
    if (!gLocked) {
        let params = {
            'Name': nodeName,
            'Type': parseInt(nodeType),
            'DocumentId': self.model.documentId,
            'TransferId': self.model.transferId,
            'ParentId': parentId,
            'DelegationId': typeof $('#hdDelegationId').val() !== "undefined" ? $('#hdDelegationId').val() : 0,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        };
        params.Id = nodeId.split("_")[1];
        Common.ajaxPostWithFile('/Attachment/Index', params, function (data) {
            gLocked = true;
            if (data.message !== null) {
                Common.alertMsg(data.message);
            }
            else {
                var message = "";
                var nodeId;
                if (data.id != null && data.id != 0) {
                    if (nodeType == "1") {
                        nodeId = "folder_" + data.id;
                        message = Resources.FolderRenamedSuccessfully;
                    }
                    else {
                        nodeId = "file_" + data.id;
                        message = Resources.FileRenamedSuccessfully;
                    }
                    var fullNode = $(".attachmentTree").jstree().get_node(nodeId);
                    fullNode.text = nodeName;
                    fullNode.original.title = nodeName;
                    $(".attachmentTree").jstree().redraw_node(fullNode);
                    $(".attachmentTree").jstree().rename_node(fullNode, nodeName);
                    Common.showScreenSuccessMsg(message);
                }
                gLocked = false;
            }
        }, function () { gLocked = false; Common.showScreenErrorMsg(); }, false);
    }
}
function refreshPreviewTab() {
    if ($('[id$=viewerFrame]') && $('[id$=viewerFrame]').last().attr("src")) {
        $('[id$=viewerFrame]').last().attr("src", $('[id$=viewerFrame]').last().attr("src"));
        $("#preview").click()
    }
}

function SetAttachmentSecurity(NodeId, self, type) {


    let modalWrapper = $(".modal-window");
    var returnedAttachmentSecurity;
    var callback = function (data, from) {
        if (data !== null) {
            if (type === "folder") {
                var url = "Attachment/CheckLockUserIdIsIncluded?AttachmentId=" + 0 + "&FolderId=" + (Number)(NodeId);
            } else {
                var url = "Attachment/CheckLockUserIdIsIncluded?AttachmentId=" + (Number)(NodeId);
            }
            Common.ajaxPostJSON(url, JSON.stringify(data), function (res) {
                setTimeout(() => {
                    if (!res.status) {
                        Common.showConfirmMsg(Resources.TheAttachmentIsCheckedOutBy + " " + res.username + " " + Resources.DoYouWantToDiscardCheckoutAndContinue, function () {
                            var securityUrl = "Attachment/Security?AttachmentId=" + (Number)(NodeId) + "&DiscardCahnges=" + true;
                            if (type === "folder") {
                                if (from == "Append") {
                                    securityUrl = "Attachment/Security?AttachmentId=" + null + "&DiscardCahnges=" + true + "&FolderId=" + (Number)(NodeId) + "&DocumentId=" + self.model.documentId +"&IsAppend="+true;

                                }
                                else {
                                    securityUrl = "Attachment/Security?AttachmentId=" + null + "&DiscardCahnges=" + true + "&FolderId=" + (Number)(NodeId) + "&DocumentId=" + self.model.documentId ;
                                }
                            }

                            Common.ajaxPostJSON(securityUrl, JSON.stringify(data), function (res) {
                                if (type === "folder" && res.message != "") {
                                    Common.alertMsg(res.message);
                                }
                                else {
                                    Common.showScreenSuccessMsg(Resources.UpdateSuccessMsg);
                                    $('.modalTransfer').modal('hide');
                                }
                            }, function (err) {
                                $('.modalTransfer').modal('hide');
                                Common.showScreenErrorMsg(err);
                            });
                        });
                    } else {
                        var securityUrl = "Attachment/Security?AttachmentId=" + (Number)(NodeId);
                        if (type === "folder") {
                            if (from == "Append") {
                                securityUrl = "Attachment/Security?AttachmentId=" + null + "&DiscardCahnges=" + false + "&FolderId=" + (Number)(NodeId) + "&DocumentId=" + self.model.documentId + "&IsAppend=" + true;

                            }
                            else {
                                securityUrl = "Attachment/Security?AttachmentId=" + null + "&DiscardCahnges=" + false + "&FolderId=" + (Number)(NodeId) + "&DocumentId=" + self.model.documentId;
                            }
                        }

                        Common.ajaxPostJSON(securityUrl, JSON.stringify(data), function (res) {
                            if (type === "folder" && res.message != "") {
                                Common.alertMsg(res.message);
                            }
                            else {
                                Common.showScreenSuccessMsg(Resources.UpdateSuccessMsg);
                                $('.modalTransfer').modal('hide');
                            }
                        }, function (err) {
                            $('.modalTransfer').modal('hide');
                            Common.showScreenErrorMsg(err);
                        });
                    }
                }, 300);
            }, function (err) {
                $('.modalTransfer').modal('hide');
                Common.showScreenErrorMsg(err);
            });
        }
        else {
            
            if (type === "folder") {
                Common.ajaxDelete("/Attachment/RemoveSecurity?AttachmentId=" + 0 + "&FolderId=" + NodeId + "&DocumentId=" + self.model.documentId, {}, function (response) {
                    if (response == "") {
                        Common.showScreenSuccessMsg(Resources.UpdateSuccessMsg);
                        $('.modalTransfer').modal('hide');
                    } else {
                        Common.alertMsg(response);
                    }
                }, function (err) {
                    Common.showScreenErrorMsg(err);
                    $('.modalTransfer').modal('hide');
                });
            } else {
                
                Common.ajaxDelete("/Attachment/RemoveSecurity?AttachmentId=" + (Number)(NodeId) + "&DocumentId=" + self.model.documentId, {}, function (response) {
                    Common.showScreenSuccessMsg(Resources.UpdateSuccessMsg);
                    $('.modalTransfer').modal('hide');
                }, function (err) {
                    Common.showScreenErrorMsg(err);
                    $('.modalTransfer').modal('hide');
                });
            }
        }
    };

    var attachmentsecurityComponent = new attachmentsecurity(self.model.userStructure, self.model.delegationId, callback, NodeId, modalWrapper, type);

   
    if (type === "folder") {
        Common.ajaxGet('/Attachment/GetAttachmentSecurity?attachmentId=' + 0 + "&FolderId=" + NodeId, null, function (data) {
            returnedAttachmentSecurity = data;
            attachmentsecurityComponent.render(returnedAttachmentSecurity);
        });
    } else {
        Common.ajaxGet('/Attachment/GetAttachmentSecurity?attachmentId=' + NodeId, null, function (data) {
            returnedAttachmentSecurity = data;
            attachmentsecurityComponent.render(returnedAttachmentSecurity);
        });
    }

    
    $('.modalTransfer').modal('show');

   
    if (type === "folder") {
        const btnTransfer = document.querySelector('[ref="btnTransfer"]');
        if (btnTransfer) {
            btnTransfer.remove();
        }
    } else {
        const btnAppend = document.querySelector('[ref="btnAppend"]');
        const btnOverride = document.querySelector('[ref="btnOverride"]');
        if (btnAppend && btnOverride) {
            btnAppend.remove();
            btnOverride.remove();
        }
    }

   
    $(".modalTransfer").off("hidden.bs.modal");
    $(".modalTransfer").off("shown.bs.modal");
    $('.modalTransfer').on('hidden.bs.modal', function () {
        $(".modalTransfer").parent().remove();
        swal.close();
    });
}


function showSignatureRegion(object) {
    sendViewerMessage({
        type: "AREA_IMPORT",
        data: [
            {
                "id": object.viewerId,
                "label": object.label,
                "page": object.pageNumber,
                "x": object.positionX,
                "y": object.positionY,
                "width": object.width,
                "height": object.height
            }
        ]
    })

    signatureList.push(object);
}
function showAllRegions(attachmentId) {

    setTimeout(() => {
        Common.ajaxGet('/SignatureRegion/GetAllSignatures',
            {
                'attachmentId': attachmentId,
                'documentId': self.model.documentId,
            },
            function (response) {
                if (response) {
                    signatureList = [];
                    if (response && response.length > 0) {
                        response.forEach((item) => { showSignatureRegion(item);/* signatureList = []; signatureList.push(item);*/ });
                    }
                }
            }, function () { Common.showScreenErrorMsg(); }, false)
    }, "200");

}

function UpdateSignatureRegionsList(attachmentId) {

    setTimeout(() => {
        Common.ajaxGet('/SignatureRegion/GetAllSignatures',
            {
                'attachmentId': attachmentId,
                'documentId': self.model.documentId,
            },
            function (response) {
                if (response) {
                    signatureList = [];
                    if (response && response.length > 0) {
                        response.forEach((item) => { signatureList.push(item); });
                    }
                }
            }, function () { Common.showScreenErrorMsg(); }, false)
    }, "200");

}

function configureSignature(fileId, selfModel) {
    var initialLoad = true;
    currntAttachment = fileId;
    $(self.refs['btnBackToAttachment']).removeClass('hidden');
    //$(self.refs['btnSaveSignatureArea']).removeClass('hidden');
    $(self.refs['btnAttachmentAddUser']).removeClass('hidden');
    $(self.refs['btnBackToAttachment']).removeAttr('disabled');
    //$(self.refs['btnSaveSignatureArea']).removeAttr('disabled');
    $(self.refs['btnAttachmentAddUser']).removeAttr('disabled');

    $(self.refs['attachmentTree']).addClass('hidden');
    $(self.refs['configurationTree']).removeClass('hidden');
    $(self.refs['btnAttachmentAddFolder']).addClass('hidden');
    $(self.refs['btnScan']).addClass('hidden');
    $(self.refs['btnAttachmentMultipleUpload']).addClass('hidden');
    $(self.refs['btnAttachmentUpload']).addClass('hidden');

    if (!self.model.openCorrespondenceDefault) {
        lastVierwerUrl = $('[id$=viewerFrame]').attr('src')
    }

    //var viewerUrl = window.ViewerUrl + "/templates?documentId=" + fileId + "&language=" + window.language + "&token=" +
    //    window.IdentityAccessToken + "&version=autocheck&ctsDocumentId=" + self.model.documentId +
    //    "&ctsTransferId=" + self.model.transferId + "&delegationId=" + self.model.delegationId + "&isDraft=true" + "&viewermode=area";

    //$('[id$=viewerFrame]').attr("src", viewerUrl);

    $(self.refs['configurationTree']).jstree({
        'core': {
            'multiple': false,

            'data': {
                'url': "/SignatureRegion/GetUserWithSignature?documentId=" + selfModel.documentId + "&attachmentId=" + fileId,

            },
            'themes': {
                'icons': true,
                'name': 'proton',
                'responsive': true,
                'ellipsis': true
            }
        },
        "plugins": ["contextmenu", "wholerow", "sort"],
        //'sort': function (a, b) {
        //    var a1 = this.get_node(a);
        //    var b1 = this.get_node(b);
        //    if (a1.id == "folder_originalMail" || b1.id == "folder_originalMail") {
        //        return b1.id == "folder_originalMail" ? 1 : -1;
        //    }
        //    else if (a1.icon == b1.icon) {
        //        return (a1.text > b1.text) ? 1 : -1;
        //    } else {
        //        return (a1.icon < b1.icon) ? 1 : -1;
        //    }
        //},
        'sort': function (a, b) {

            var aText = this.get_node(a).text;
            var bText = this.get_node(b).text;

            var numA = parseInt(aText.match(/\d+/)?.[0] || 0, 10);
            var numB = parseInt(bText.match(/\d+/)?.[0] || 0, 10);

            return numA - numB;

        },
        "contextmenu": {
            "select_node": true,
            "items": function ($node) {
                var tree = $(self.refs['configurationTree']).jstree(true);
                var retValue = {};
                if ($node.id.includes('user')) {
                    retValue = {
                        "Add signature": {
                            "separator_before": false,
                            "separator_after": false,
                            "label": Resources.AddNewSignature,
                            "icon": 'fa fa-pencil-square-o text-color-context',
                            "action": function () {
                                addNewSignature(self.model, self, $node);
                            }
                        },

                        "Remove user": {
                            "separator_before": false,
                            "separator_after": false,
                            "label": Resources.Delete,
                            "icon": 'fa fa-minus-circle text-color-context',
                            "action": function () {
                                Common.showConfirmMsg(Resources.DeleteUserWithSignatures, function () {
                                    Common.ajaxDelete('/AttachmentSignUser/DeleteAssignedUser',
                                        {
                                            'attachmentSignUserId': $node.id.split("_")[2],
                                            'documentId': self.model.documentId,
                                        },
                                        function (response) {
                                            if (!response) {
                                                Common.showScreenErrorMsg();
                                            }
                                            else {
                                                Common.showScreenSuccessMsg(Resources.UserDeletedSuccessfully);
                                                $('.configurationTree').jstree("refresh");
                                                var deleted = signatureList.filter(x => x.attachmentSignUserId == parseInt($node.id.split("_")[2]));
                                                deleted.forEach(x => {
                                                    sendViewerMessage({
                                                        type: "AREA_DELETE",
                                                        data:
                                                        {
                                                            "id": x.viewerId,
                                                        }
                                                    })
                                                })
                                                signatureList = signatureList.filter(x => x.attachmentSignUserId != parseInt($node.id.split("_")[2]));
                                                swal.close();
                                            }
                                        }, function () { Common.showScreenErrorMsg(); }, false);
                                }, null, undefined, false);
                            }
                        },

                    };
                }
                else if ($node.id.includes('sign')) {
                    retValue = {
                        "Delete": {
                            "separator_before": false,
                            "separator_after": false,
                            "label": Resources.Delete,
                            "icon": 'fa fa-minus-circle text-color-context',
                            "action": function () {
                                Common.showConfirmMsg(Resources.DeleteConfirmation, function () {
                                    Common.ajaxDelete('/SignatureRegion/DeleteSignature',
                                        {
                                            'signatureId': $node.id.split("_")[1],
                                            'documentId': self.model.documentId,
                                        },
                                        function (response) {
                                            if (response.message != undefined) {
                                                Common.alertMsg(response.message);
                                            }
                                            else {
                                                const index = signatureList.findIndex((obj => obj.id == $node.id.split("_")[1]));
                                                sendViewerMessage({
                                                    type: "AREA_DELETE",
                                                    data:
                                                    {
                                                        "id": signatureList[index].viewerId,
                                                    }
                                                })
                                                if (index > -1) {
                                                    signatureList.splice(index, 1);
                                                }
                                                Common.showScreenSuccessMsg(Resources.AreaDeletedSuccessfully);
                                                tree.delete_node($node);
                                                $('.configurationTree').jstree("refresh");
                                                swal.close();
                                            }
                                        }, function () { Common.showScreenErrorMsg(); }, false);
                                }, null, undefined, false);
                            }
                        },
                        "Duplicate": {
                            "separator_before": false,
                            "separator_after": false,
                            "label": Resources.Duplicate,
                            "icon": 'fa fa-clone text-color-context',
                            "action": function () {
                                let modalWrapper = $(".modal-window");
                                modalWrapper.find("#modalSignatureDuplicate").remove();
                                var DuplicateSignatureRegionModel = new DuplicateSignatureRegion.DuplicateSignatureRegion();
                                DuplicateSignatureRegionModel.signatureRegionId = $node.id.split("_")[1];
                                DuplicateSignatureRegionModel.fileId = currntAttachment;
                                DuplicateSignatureRegionModel.documentId = selfModel.documentId;
                                DuplicateSignatureRegionModel.transferId = selfModel.transferId;
                                var DuplicateSignatureRegionview = new DuplicateSignatureRegion.DuplicateSignatureRegionView(modalWrapper, DuplicateSignatureRegionModel);
                                DuplicateSignatureRegionview.render();
                                $("#modalSignatureDuplicate").parsley().reset();

                                $('#modalSignatureDuplicate').modal('show');
                                $("#modalSignatureDuplicate").off("hidden.bs.modal");
                                $("#modalSignatureDuplicate").off("shown.bs.modal");
                                $('#modalSignatureDuplicate').on('shown.bs.modal', function () {
                                    // document.getElementById('_cmbAttachmentUserToSign').focus();
                                });
                                $('#modalSignatureDuplicate').on('hidden.bs.modal', function () {
                                    $('#formSignatureDuplicateActionPost').parsley().reset();

                                    $('#modalSignatureDuplicate').remove();
                                });

                            },
                            //"Import": {
                            //    "separator_before": false,
                            //    "separator_after": false,
                            //    "label": Resources.Import,
                            //    "icon": 'fa fa-minus-circle text-color-context',
                            //    "action": function () {
                            //        Common.ajaxGet('/SignatureRegion/GetSignatureRegion',
                            //            {
                            //                'signatureId': $node.id.split("_")[1],
                            //                'documentId': self.model.documentId,
                            //            },
                            //            function (response) {
                            //                if (response) {
                            //                    showSignatureRegion(response);
                            //                }
                            //            }, function () { Common.showScreenErrorMsg(); }, false);
                            //    }
                            //},
                        }
                    }                    
                }
                return retValue;
            }
        }
    }).on("select_node.jstree", function (e, data) {

    })
        .on("hover_node.jstree", function (e, data) {

        })
        .on("loaded.jstree", function (e, data) {
            if (initialLoad) {
                initialLoad = false;
                //$(self.refs['attachmentTree']).jstree().refresh();            
            }
        })
        .bind("dblclick.jstree", function (event) {
            var node = $(event.target).closest("li")[0];
            if (node.id.includes("sign")) {
                var index = signatureList.findIndex(x => node.id.split("_")[1] == x.id);
                var SignatureItem = signatureList[index];
                sendViewerMessage({
                    type: "SCROLL_TO_AREA",
                    data:
                    {
                        id: SignatureItem.viewerId,
                        page: SignatureItem.pageNumber,
                        positionX: SignatureItem.positionX,
                        positionY: SignatureItem.positionY
                    }
                })
            }
        })
        .bind("refresh.jstree", function (event) {
            showAllRegions(fileId);
        });

    showAllRegions(fileId);
}
function addNewUser(id, name, parentId, type, edit, model) {
    let modalWrapper = $(".modal-window");
    modalWrapper.find("#modalAttachmentUserNode").remove();
    var attachmentUserIndexModel = new AttachmentUserIndex.AttachmentUserIndex();
    attachmentUserIndexModel.callback = addNewSignature;
    var attachmentUserIndex = new AttachmentUserIndex.AttachmentUserIndexView(modalWrapper, attachmentUserIndexModel);
    attachmentUserIndex.render();
    $("#formAttachmentNodeActionPost").parsley().reset();
    $('#hdAttachmentNodeDocumentId').val(model.documentId);
    $('#hdAttachmentNodeTransferId').val(model.transferId);
    $('#hdDelegationId').val(model.delegationId);
    $('#hdAttachmentId').val(currntAttachment);
    $('#hdAttachmentNodeType').val(type);

    $("#hdAttachmentNodeParentId").val('');
    $("#hdAttachmentNodeId").val(currntAttachment);
    $("#_cmbAttachmentUserToSign").val('');
    $("#hdEditAttachmentNodeMode").val(false);
    $('#modalAttachmentNodeTitle').html(Resources.AddUser);

    $('#modalAttachmentUserNode').modal('show');
    $("#modalAttachmentUserNode").off("hidden.bs.modal");
    $("#modalAttachmentUserNode").off("shown.bs.modal");
    $('#modalAttachmentUserNode').on('shown.bs.modal', function () {
        document.getElementById('_cmbAttachmentUserToSign').focus();
    });
    $('#modalAttachmentUserNode').on('hidden.bs.modal', function () {
        $('#formAttachmentNodeActionPost').parsley().reset();
        $('#hdAttachmentNodeId').val('');
        $('#hdAttachmentNodeParentId').val('');
        $('#_cmbAttachmentUserToSign').val('');
        $("#hdEditAttachmentNodeMode").val(false);
        $('#hdAttachmentNodeDocumentId').val('');
        $('#hdAttachmentNodeTransferId').val('');
        $('#hdAttachmentNodeType').val('');
        $('#hdDelegationId').val('');
        $('#modalAttachmentUserNode').remove();
    });
}
function addNewSignature(model, self, node) {
    currentUser = node.id.split("_")[1];
    currentAttachmentUserSign = node.id.split("_")[2];
    sendViewerMessage({
        type: "AREA_CREATE_START",
        label: node.text + " #" + (signatureList.filter(x => x.userId == currentUser).length + 1),
    })
}
function sendViewerMessage(message) {
    const viewerWindow = $('[id$=viewerFrame]')[0].contentWindow;
    viewerWindow.postMessage(message, "*");
}
function addNewSignaturearea(object) {
    var item = {
        viewerId: object.id,
        height: object.height,
        width: object.width,
        positionX: object.x,
        positionY: object.y,
        pageNumber: object.page,
        userId: parseInt(currentUser),
        attachmentId: parseInt(currntAttachment),
        attachmentSignUserId: parseInt(currentAttachmentUserSign),
        id: 0,
        positionName: "",
        label: object.label
    }

    signatureList.push(item);
    $(self.refs['btnSaveSignatureArea']).click();
}
function updateSignaturearea(object) {

    var upd_obj = signatureList.findIndex((obj => obj.viewerId == object.id));
    if (upd_obj != -1) {
        signatureList[upd_obj].height = object.height;
        signatureList[upd_obj].width = object.width;
        signatureList[upd_obj].positionX = object.x;
        signatureList[upd_obj].positionY = object.y;
        signatureList[upd_obj].page = object.page;
        signatureList[upd_obj].label = object.label
    }

    $(self.refs['btnSaveSignatureArea']).click();
}
function deleteSignaturearea(object) {

    const index = signatureList.findIndex((obj => obj.viewerId == object.id));
    if (index > -1) {
        if (object.id != 0) {
            var signature = signatureList[index]
            Common.ajaxDelete('/SignatureRegion/DeleteSignature',
                {
                    'signatureId': signature.id,
                    'documentId': self.model.documentId,
                },
                function (response) {
                    if (response.message != undefined) {
                        Common.alertMsg(response.message);
                    }
                    else {
                        //signatureList.pop()
                        //deleteSignaturearea({ id: $node.id.split("_")[1] });
                        signatureList.splice(index, 1);
                        Common.showScreenSuccessMsg(Resources.AreaDeletedSuccessfully);
                        $('.configurationTree').jstree("refresh");
                    }
                }, function () { Common.showScreenErrorMsg(); }, false);
        }
        else {
            signatureList.splice(index, 1);
        }
    }
}
function RemoveUserWithSignatures(model, self, node) {
    currentUser = node.id.split("_")[2];
    currentAttachmentUserSign = node.id.split("_")[2];
}

window.addEventListener("message", function (event) {

    if (event.data.type) {
        switch (event.data.type) {
            case "AREA_CREATE":
                addNewSignaturearea(event.data.area);
                break;
            case "AREA_UPDATE":
                updateSignaturearea(event.data.area);
                break;
            case "AREA_DELETE":
                deleteSignaturearea(event.data.area);
                break;
        }
    }


    else {
        if ($(".attachmentTree").length > 0) {
            let data = JSON.parse(event.data);
            let url = data.url;
            if (url.indexOf('/checkin') > -1) {
                let documentLength = 'document/'.length;
                let startOfId = documentLength + url.indexOf('document/');
                let endIdVersion = url.indexOf('/version');
                if (startOfId >= 0 && endIdVersion >= 0) {
                    let id = url.substr(startOfId, endIdVersion - startOfId)
                    if (!isNaN(id)) {
                        Common.ajaxGet('/Document/GetDetailsAfterCheckIn', { 'id': id }, function (response) {
                            if (response.isPDF) {
                                var isSignedDcoument = response.isSigned;
                                var nodeId = "file_" + id;
                                var fullNode = $(".attachmentTree").jstree().get_node(nodeId);
                                if (((fullNode.data.isSigned != isSignedDcoument) && fullNode.data.isSigned != null) ||
                                    (fullNode.data.isSigned == null && isSignedDcoument)) {
                                    fullNode.data.isSigned = isSignedDcoument;
                                    $(".attachmentTree").jstree().redraw_node(fullNode);
                                    $(self.refs['attachmentTree']).jstree("deselect_all", true);
                                    $(self.refs['attachmentTree']).jstree().select_node(nodeId);
                                    if (self.model.fromInbox) {
                                        var isEnableAttributeEdit = response.isEnableAttributeEdit;
                                        if (isEnableAttributeEdit) {
                                            $("a[href*='documentMetadata']").parent('li').data("loaded", false);

                                        }
                                    }
                                    var isEnableAttributeEditSign = response.isEnableAttributeEditSign;
                                    if (isEnableAttributeEditSign) {
                                        $("a[href*='documentMetadata']").parent('li').data("loaded", false);

                                    }
                                }

                            }
                        }, function () { }, false, null, false);


                    }
                }
            }
        }
    }

});

class AttachmentView extends Intalio.View {
    constructor(element, model) {
        super(element, "attachment", model);
    }
    render() {
        self = this;

        $(self.refs['btnAttachmentAddFolder']).hide();
        $(self.refs['btnScan']).hide();
        $(self.refs['btnAttachmentMultipleUpload']).hide();
        $(self.refs['btnAttachmentUpload']).hide();


        if (this.model.actionName != undefined) {
            var actionArray = this.model.actionName.split("_");

            if ((actionArray.includes("Folder.AddFolder"))) {
                $(self.refs['btnAttachmentAddFolder']).show();
            }
            if ((actionArray.includes("Folder.Scan"))) {
                $(self.refs['btnScan']).show();
            }
            if ((actionArray.includes("Folder.Upload"))) {
                $(self.refs['btnAttachmentUpload']).show();
            }

            if ((actionArray.includes("Folder.MultipleUpload"))) {
                $(self.refs['btnAttachmentMultipleUpload']).show();
            }



        }

    Common.ajaxGet('/Transfer/GetTransferDetailsById', { id: self.model.transferId }, function (result)
    {
        self.model.isSigned = result.isSigned;
    });



        var categoriesObj = null;
        if (!self.model.readOnly) {
            if (self.model.categoryId) {
                categoriesObj = new CategoryModel().findFullById(self.model.categoryId);
            }
            $(self.refs['attachmentToolbar']).show();
            $(self.refs['btnAttachmentMultipleUpload']).on('click', function () {
                var parentId = null;
                var node = $(self.refs['attachmentTree']).jstree().get_selected(true)[0];
                if (node != undefined && node.id != "0") {
                    parentId = node.id.split("_")[1];
                }
                multipleUpload(self.model, parentId);
            });
            $(self.refs['btnAttachmentAddFolder']).on('click', function () {
                var id = "#";
                var node = $(self.refs['attachmentTree']).jstree().get_selected(true)[0];
                if (node != undefined && node.Id != "0") {
                    id = node.id.split("_")[1];
                }
                addEditNode(id, null, null, 1, false, self.model);
            });
            $(self.refs['btnAttachmentAddUser']).on('click', function () {
                var id = "#";
                var node = $(self.refs['configurationTree']).jstree().get_selected(true)[0];
                if (node != undefined && node.Id != "0") {
                    id = node.id.split("_")[1];
                }
                addNewUser(id, null, null, 3, false, self.model);
            });
            $(self.refs['btnAttachmentUpload']).on('click', function () {
                var parentId = null;
                var node = $(self.refs['attachmentTree']).jstree().get_selected(true)[0];
                if (node != undefined && node.id == "folder_originalMail") {
                    uploadOriginalMail(self.model, self);
                }
                else {
                    if (node != undefined && node.id != "0") {
                        parentId = node.id.split("_")[1];
                    }
                    upload(self.model, parentId, self);
                }
            });
            $(self.refs['btnScan']).on('click', function () {
                var parentId = null;
                var node = $(self.refs['attachmentTree']).jstree().get_selected(true)[0];
                if (node != undefined && node.id == "folder_originalMail") {
                    scanOriginalMail(self.model, self);
                }
                else {
                    if (node != undefined && node.id != "0") {
                        parentId = node.id.split("_")[1];
                    }
                    scan(self.model, parentId, self);
                }
            });
            $(self.refs['btnBackToAttachment']).on('click', function () {
                $(self.refs['attachmentTree']).removeClass("hidden");
                $(self.refs['btnAttachmentAddFolder']).removeClass('hidden');
                $(self.refs['btnScan']).removeClass('hidden');
                $(self.refs['btnAttachmentMultipleUpload']).removeClass('hidden');
                $(self.refs['btnAttachmentUpload']).removeClass('hidden');

                $(self.refs['configurationTree']).jstree('destroy');
                $(self.refs['configurationTree']).addClass("hidden");
                $(self.refs['btnBackToAttachment']).addClass('hidden');
                $(self.refs['btnSaveSignatureArea']).addClass('hidden');
                $(self.refs['btnAttachmentAddUser']).addClass('hidden');

                //if (!self.model.openCorrespondenceDefault) {
                //    $("#" + self.model.parentComponentId + "_" + "viewerFrame").attr("src", lastVierwerUrl);
                //} else
                //    $(self.refs['viewerFrame']).attr("src", lastVierwerUrl);
                /*$('[id$=viewerFrame]').attr("src", lastVierwerUrl);*/

            });
            $(self.refs['btnSaveSignatureArea']).on('click', function () {
                
                if (signatureList.length > 0) {
                    let params = {
                        signatures: signatureList
                    }
                    Common.ajaxPostJSON('/SignatureRegion/Index?documentId=' + self.model.documentId, JSON.stringify(signatureList), function (data) { Common.showScreenSuccessMsg(); $('.configurationTree').jstree("refresh"); UpdateSignatureRegionsList(currntAttachment); }, function () {
                        Common.showScreenErrorMsg();
                    }, false)

                }
                else {
                    //Common.sh
                }

            });

        }
        else {
            $(self.refs['attachmentToolbar']).hide();
        }
        if (!self.model.openCorrespondenceDefault) {
            $(self.refs['attachmentTreeContainer']).removeClass("col-lg-3").addClass("col-lg-12");
        }

        initTree(this, categoriesObj, this.model.actionName);
    }
}
export default { Attachment, AttachmentView };
