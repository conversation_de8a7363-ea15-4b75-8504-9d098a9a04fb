﻿using Intalio.Core.DAL;
using Intalio.Core.Model;
using System.Collections.Generic;
using System.Linq;

namespace Intalio.Core.API
{
    public static class ManageLookupItems
    {
        #region Public Methods

        public static bool Create(long userId, LookupItemsViewModel model)
        {
            bool retValue = false;
            if (!CheckUnique(model.Id, model.Name, model.NameAr, model.NameFr, model.Code, model.ParentId))
            {
                LookupItems item = new LookupItems
                {
                    Name = model.Name,
                    NameFr = model.NameFr,
                    NameAr = model.NameAr,
                    Code = model.Code,
                    ParentId = model.ParentId,
                    LookupItemParentId = model.LookupItemParentId,
                    CreatedByUserId = userId
                };
                item.Insert();
                retValue = true;
                model.Id = item.Id;
            }
            return retValue;
        }

        public static bool Edit(long userId, LookupItemsViewModel model)
        {
            bool retValue = false;
            if (model.Id != null)
            {
                if (!CheckUnique(model.Id, model.Name, model.NameAr, model.NameFr, model.Code, model.ParentId))
                {
                    LookupItems item = new LookupItems().Find(model.Id.Value);
                    if (item != null)
                    {
                        item.Name = model.Name;
                        item.NameFr = model.NameFr;
                        item.NameAr = model.NameAr;
                        item.Code = model.Code;
                        item.ParentId = model.ParentId;
                        item.LookupItemParentId = model.LookupItemParentId;
                        item.Update();
                        retValue = true;
                    }
                }
            }
            return retValue;
        }

        public static bool Delete(long userId, List<int> ids)
        {
            return new LookupItems().Delete(ids);
        }

        public static (int, List<LookupItemsListViewModel>) List(int startIndex, int pageSize, short? parentId, ExpressionBuilderFilters filter,
              List<SortExpression> sortExpression, Language language = Language.EN)
        {
            LookupItems item = new LookupItems();
            var filterExp = ExpressionBuilder.GetExpression<LookupItems>(filter, ExpressionBuilderOperator.Or);
            var countResult = item.GetCount(parentId, filterExp);
            var itemList = item.List(startIndex, pageSize, parentId, filterExp, sortExpression.OrderByExpression<LookupItems>());
            LookupItems lookupItem = null;
            return (countResult.Result, itemList.Select(t => new LookupItemsListViewModel
            {
                Id = t.Id,
                ParentId = t.ParentId,
                LookupItemParentId = t.LookupItemParentId,
                LookupItemParentName = t.LookupItemParentId.HasValue ? (lookupItem = LookupItems.FindItem(t.LookupItemParentId.Value)) != null ? language == Language.EN ?
                lookupItem.Name : language == Language.FR ? !string.IsNullOrEmpty(lookupItem.NameFr)  ? lookupItem.NameFr : lookupItem.Name : language == Language.AR ? !string.IsNullOrEmpty(lookupItem.NameAr) ? lookupItem.NameAr : lookupItem.Name : lookupItem.Name : string.Empty : string.Empty,
                Name = t.Name,
                NameFr = t.NameFr,
                NameAr = t.NameAr,
                Code = t.Code,
                CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                ModifiedDate = t.ModifiedDate != default ? t.ModifiedDate?.ToString(Constants.DATE_FORMAT) : string.Empty,
            }).ToList());
        }

        public static List<ValueText> ListLookupItems(short id, string text, Language language = Language.EN)
        {
            var list = new LookupItems().ListWithPaging(id, text);
            if (language == Language.EN)
            {
                return list.Select(t => new ValueText { Id = t.Id, Text = t.Name }).ToList();
            }
            else if (language == Language.FR)
            {
                return list.Select(t => new ValueText { Id = t.Id, Text = (string.IsNullOrEmpty(t.NameFr) ? t.Name : t.NameFr) }).ToList();
            }
            else
            {
                return list.Select(t => new ValueText { Id = t.Id, Text = (string.IsNullOrEmpty(t.NameAr) ? t.Name : t.NameAr) }).ToList();
            }
        }

        public static List<LookupItemModel> ListALlLookupItems(short id, string text, Language language = Language.EN)
        {
            var list = Configuration.EnableCaching ? new LookupItems().ListWithCaching(id, text) : new LookupItems().List(id, text);
            if (language == Language.EN)
            {
                return list.Select(t => new LookupItemModel { Id = t.Id, Text = t.Name, Code = t.Code }).ToList();
            }
            else if (language == Language.FR)
            {
                return list.Select(t => new LookupItemModel { Id = t.Id, Text = (string.IsNullOrEmpty(t.NameFr) ? t.Name : t.NameFr), Code = t.Code }).ToList();
            }
            else
            {
                return list.Select(t => new LookupItemModel { Id = t.Id, Text = (string.IsNullOrEmpty(t.NameAr) ? t.Name : t.NameAr), Code = t.Code }).ToList();
            }
        }

        public static short? GetLookupByLookupItemId(int itemId)
        {
            return new LookupItems().GetLookupByLookupItemId(itemId);
        }

        public static List<LookupItemModel> ListLookupItemsByLookupItemId(int id, string text, Language language = Language.EN)
        {
            var list = Configuration.EnableCaching ? new LookupItems().ListByItemIdWithCaching(id, text) : new LookupItems().ListByItemId(id, text);
            if (language == Language.EN)
            {
                return list.Select(t => new LookupItemModel { Id = t.Id, Text = t.Name, Code = t.Code }).ToList();
            }
            else if (language == Language.FR)
            {
                return list.Select(t => new LookupItemModel { Id = t.Id, Text = (string.IsNullOrEmpty(t.NameFr) ? t.Name : t.NameFr), Code = t.Code }).ToList();
            }
            else
            {
                return list.Select(t => new LookupItemModel { Id = t.Id, Text = (string.IsNullOrEmpty(t.NameAr) ? t.Name : t.NameAr), Code = t.Code }).ToList();
            }
        }

        public static bool CheckUnique(int? id, string name, string nameAr, string nameFr, string code, short? parentId)
        {
            return new LookupItems().CheckUnique(id, name, nameAr, nameFr, code, parentId);
        }

        public static string GetNameByLookUpItemId(int id, Language language = Language.EN)
        {
            var retValue = string.Empty;
            var item = new LookupItems().GetNameByLookUpItemId(id);
            if (item != null)
            {
                retValue = item.Name;
                if (language == Language.AR)
                {
                    retValue = !string.IsNullOrEmpty(item.NameAr) ? item.NameAr : item.Name;
                }
                else if (language == Language.FR)
                {
                    retValue = !string.IsNullOrEmpty(item.NameFr) ? item.NameFr : item.Name;
                }
            }
            return retValue;
        }

        #endregion
    }
}
