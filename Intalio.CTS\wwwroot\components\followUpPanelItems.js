﻿import Intalio from './common.js'
import { IdentityService } from './lookup.js'
import Distribution from './distributionList.js'
class FollowUpPanelItems extends Intalio.Model {
    constructor() {
        super();
        this.followUpId = 0;
    }
}
class FollowUpPanelItemsView extends Intalio.View {
    constructor(element, model) {
        super(element, "followUpPanelItems", model);
    }
    render() {
        var self = this;
        $.fn.select2.defaults.set("theme", "bootstrap");
        $('#btnClose').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnSubmit').focus();
                }
                else {
                    $('#txtEvent').focus();
                }
            }
        });
        let table = $("#grdFollowupPanelItems").on('draw.dt',
            function () {
                $('#grdItems tbody tr td').each(function () {
                this.setAttribute('title', $(this).text());
            });
        }).DataTable({
            processing: true,
            ordering: false,
            serverSide: true,
            pageLength: 10,
                "drawCallback": function (settings) {
                },
                "ajax": {
                    "url": "/FollowUp/ListFollowUpPanelItems",
                    "type": "POST",
                    "datatype": "json",
                data: function (d) {
                    d.followUpId = parseInt(self.model.followUpId);
                    return d;
                },
                dataSrc: function (json) {
                    if (json.data.length === 1) {
                        return []; 
                    }
                    if (json.data.length > 1) {
                        return json.data.slice(0, -1);
                    }
                    return json.data;
                }
            },
                "columns": [
                { title: "Id", data: "id", visible: false },
                    //{
                    //    "data": null,
                    //    "title": "#",
                    //    "render": function (data, type, row, meta) {
                    //        return meta.row + 1; 
                    //    }
                    //},
                    { title: Resources.Event, data: "event", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    { title: Resources.EventDate, data: "eventDate", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    { title: Resources.TransferredTo, data: "transferredTo", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    { title: Resources.TransferredDate, data: "transferredDate", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    { title: Resources.ResponsibleUser, data: "responsibleUser", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    { title: Resources.Notes, data: "notes", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    { title: Resources.FollowUpPanelStatus, data: "followUpPanelStatus", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    { title: Resources.CreatedBy, data: "followUpCreatedByUser", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    { title: Resources.ModifiedDate, data: "lastModifiedDate", render: $.fn.dataTable.render.text(), "autoWidth": true },
                    //{
                    //    title: Resources.ModifiedDate, data: "lastModifiedDate", "autoWidth": true,
                    //    render: function (data, type, full, meta) {
                    //        var date = new Date(full.lastModifiedDate);
                    //        var mm = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
                    //        var hh = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
                    //        var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
                    //        var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
                    //        var year = date.getFullYear();
                    //        var dateString = day + '/' + month + '/' + year + " " + hh + ':' + mm;
                    //        return DateConverter.toHijriFormated(dateString, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                    //    }
                    //},
            ],
            dom: 'trpi'
        });
    };
}
export default { FollowUpPanelItems, FollowUpPanelItemsView };