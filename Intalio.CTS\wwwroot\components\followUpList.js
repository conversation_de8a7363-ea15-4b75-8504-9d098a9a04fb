﻿import Intalio from './common.js'
import DocumentDetails from './documentDetails.js'
import { Categories, Nodes } from './lookup.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import CustomizeNodeColomns from '../components/customizeNodeColomns.js'

class FollowUpListModel extends Intalio.Model
{
    constructor()
    {
        super();
        this.title = null;
        this.nodeId = null;
        this.categories = null;
    }
}

var grdFollowUpItems = "grdFollowUpItems";
var self ;
var gFilterPrivacy = "";
var gFilterPriority = "";
var gLocked = false;
var gfirstTime = true;
var gDataChanged = true;
var gAction = "";
var gFilterChanged = false;
var gFilterStructure = "";
var gFilterFromDate = "";
var gFilterToDate = "";
var gFilterUser = "";
var gFilterOverdue = "";
var gOverdue = false;
var clear = false;
var gReportName = "FollowUp Report";

var wrapperParent;
function structureDataForSelect2(data) {
    var retVal = [];
    for (var i = 0; i < data.length; i++) {
        var structureName = data[i].name;
        if (data[i].attributes != null && data[i].attributes.length > 0) {
            var attributeLang = $.grep(data[i].attributes, function (e) {
                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
            });
            if (attributeLang.length > 0) {
                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
            }
        }
        retVal.push({
            id: data[i].id.toString(),
            text: structureName,
        });
    }
    return retVal;
}
function renderTextInSelectStructures(option) {
    var $option;
    if (typeof option.id !== "undefined") {
        $option = $(
            '<div><i style="font-size:12px;" class="' + option.icon + '"></i> ' + option.text + '</div>'
        );
    } else {
        $option = option;
    }
    return $option;
}
function format(row, nodeId)
{
    return '<table style="width:100%" cellspacing="0" border="0">' + buildColumnsDetails(row, nodeId) + '</table>';
}
function openDocument(data, nodeId)
{
        Common.ajaxGet("/FollowUp/CheckIfCanAccessFollowUp", { id: data.id }
            , function (value) {
                gLocked = false;

                if (value) {
                    setTimeout(function () {
                        var wrapper = $(".modal-documents");
                        var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
                        linkedCorrespondenceModel.reference = data.referenceNumber;
                        linkedCorrespondenceModel.subject = data.subject;
                        linkedCorrespondenceModel.documentId = data.documentId;
                        var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
                        linkedCorrespondenceDocument.render();

                        var model = new DocumentDetails.DocumentDetails();
                        model.followupId = data.id;
                        model.documentId = data.documentId;
                        model.originalDocumentId = data.originalDocumentId;
                        model.categoryId = window.FollowUpCategory;
                        model.showMyTransfer = false;
                        model.readonly = data.readonly;
                        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));
                        var tabs = [];
                        if (nodeId !== undefined && $.isNumeric(nodeId)) {
                            tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[window.FollowUpCategory].Tabs;
                            model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[window.FollowUpCategory].SecurityTabs;
                        }
                        model.tabs = $.grep(tabs, function (element, index) {
                            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                                !element.Name.includes("notes") && !element.Name.includes("visualTracking") &&
                                !element.Name.includes("activityLog") &&
                                !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory");
                        });
                        //model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[window.FollowUpCategory].SecurityTabs;
                        model.tabsWithStatic = tabs;
                        model.showBackButton = false;
                        model.fromDraft = false;
                        model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
                        model.isModal = true;
                        model.teamId = data.teamId
                        model.isPrivateFollowUp = data.isPrivate
                        wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

                        var documentView = new DocumentDetails.DocumentDetailsView(wrapper, model);
                        documentView.render();

                        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
                        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
                        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                            $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
                        });
                        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                            if ($(this).data("remove") != true)
                                return;
                            $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                            swal.close();
                            //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
                            //    $('body').addClass('modal-open');
                            //}
                        });
                        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");
                    }, 1000);
                } else {
                    Common.alertMsg(Resources.UserCantAccessFollowUp);
                }
            }, null, null, null, false);

}
function buildFilters(nodeId)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var filters = node.filters !== "" && node.filters !== null ? JSON.parse(node.filters) : [];

    if (filters.length > 0)
    {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++)
        {
            var filter = filters[i];
            switch (filter)
            {
                case "ReferenceNumber":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
                        '<input type="text" id="txtFilterFollowUpReferenceNumber" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "FromDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label><div class="input-group date">' +
                        '<input id="filterFollowUpFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterFollowUpFromDateError">' +
                        '<span class="input-group-addon" id="filterFollowUpFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterFollowUpFromDateError"></div></div></div></div>'
                    break;
                case "ToDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label><div class="input-group date">' +
                        '<input id="filterFollowUpToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterFollowUpToDateError">' +
                        '<span class="input-group-addon" id="filterFollowUpToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterFollowUpToDateError"></div></div></div></div>';
                    break;
                case "Subject":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterFollowUpSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Note":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.Note +
                        '</label><input type="text" id="txtFilterFollowUpNote" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "KeyWord":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.Keyword +
                        '</label><input type="text" id="txtFilterFollowUpKeyWord" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Category":
                    clearFix += 3;
                    var categories = self.model.categories;
                    var followUpCategoryIndex = categories.findIndex(item => item.id == window.FollowUpCategory);
                    categories.splice(followUpCategoryIndex, 1);
                    var div = '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="categoryFilterFollowUpContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterFollowUpCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterFollowUpCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++)
                    {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterFollowUpCategoryError"></div></div></div>';
                    html += div;
                    break;
                case "Year":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.Year +
                        '</label><input type="text" id="txtFilterFollowUpYear" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Priority":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="priorityFilterFollowUpContainer"><div class="form-group"><label class="control-label">' + Resources.Priority + '</label>' +
                        '<select id="cmbFilterFollowUpPriority" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterFollowUpPriorityError" class="form-control"></select></div></div>';
                    break;
                case "ReceivingEntity":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="receivingEntityFilterFollowUpContainer"><div class="form-group"><label class="control-label">' + Resources.ReceivingEntity + '</label>' +
                        '<select id="cmbFilterFollowUpReceivingEntity" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterFollowUpReceivingEntityError" class="form-control"></select></div></div>';
                    break;
                case "SendingEntity":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="sendingEntityFilterFollowUpContainer"><div class="form-group"><label class="control-label">' + Resources.SendingEntity + '</label>' +
                        '<select id="cmbFilterFollowUpSendingEntity" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterFollowUpSendingEntityError" class="form-control"></select></div></div>';
                    break;
                case "FollowedUpBy":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="followedUpByFilterFollowUpContainer"><div class="form-group"><label class="control-label">' + Resources.FollowedUpBy + '</label>' +
                        '<select id="cmbFilterFollowUpFollowedUpBy" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterFollowUpFollowedUpByError" class="form-control"></select></div></div>';
                    break;
            }
            if (clearFix === 12 || clearFix === 11)
            {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 list-action-filter"><button id="btnFilterFollowUpSearch" tabindex="13" type="button" class="btn btn-primary">' + Resources.Search + '</button>' +
            ' <button id="btnFilterFollowUpClear" tabindex="14" type="button" class="btn btn-clear">' + Resources.Clear + '</button></div>';
        html += '</div>';
        $('#filtersContainer').html(html);

        $("#btnFilterFollowUpSearch").on('click', function ()
        {
            GridCommon.Refresh(grdFollowUpItems);
        });
        $("#btnFilterFollowUpClear").on('click', function ()
        {
            $("#cmbFilterFollowUpCategory").val('').trigger('change');
            $("#cmbFilterFollowUpPriority").val('').trigger('change');
            $("#cmbFilterFollowUpReceivingEntity").val('').trigger('change');
            $("#cmbFilterFollowUpSendingEntity").val('').trigger('change');
            $("#cmbFilterFollowUpFollowedUpBy").val('').trigger('change');
            fromDate.clear();
            toDate.clear();
            $("#txtFilterFollowUpSubject").val('');
            $("#txtFilterFollowUpReferenceNumber").val('');
            $("#txtFilterFollowUpNote").val('');
            $("#txtFilterFollowUpKeyWord").val('');
            $("#txtFilterFollowUpYear").val('');
            GridCommon.Refresh(grdFollowUpItems);
        });

        var headers = {};
        headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
        var url = "User/GetUsersStructuresFromCTS";
        var usersUrl = location.origin + '/User/GetUsersStructuresFromCTS';
        $('#cmbFilterFollowUpCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterFollowUpContainer')
        });
        $("#cmbFilterFollowUpCategory").val('').trigger('change');

        $('#cmbFilterFollowUpPriority').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Priorities().get(window.language),
            dropdownParent: $('#priorityFilterFollowUpContainer')
        });
        $("#cmbFilterFollowUpPriority").val('').trigger('change');

        $('#cmbFilterFollowUpReceivingEntity').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            ajax: {
                delay: 250,
                url: url,
                type: "Post",
                dataType: 'json',
                headers: typeof headers !== "undefined" ? headers : "",
                data: function (term) {
                    return { "text": term.term ? term.term : "", structureType: 3, fromSendingandReceiving: true };
                },
                processResults: function (data) {
                    return {
                        results: structureDataForSelect2(data || [])
                    };
                }
            },            dropdownParent: $('#receivingEntityFilterFollowUpContainer')
        });
        $("#cmbFilterFollowUpReceivingEntity").val('').trigger('change');

        $('#cmbFilterFollowUpSendingEntity').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            ajax: {
                delay: 250,
                url: url,
                type: "Post",
                dataType: 'json',
                headers: typeof headers !== "undefined" ? headers : "",
                data: function (term) {
                    return { "text": term.term ? term.term : "", structureType: 3, fromSendingandReceiving: true };
                },
                processResults: function (data) {
                    return {
                        results: structureDataForSelect2(data || [])
                    };
                }
            },
            dropdownParent: $('#sendingEntityFilterFollowUpContainer')
        });
        $("#cmbFilterFollowUpSendingEntity").val('').trigger('change');

        $('#cmbFilterFollowUpFollowedUpBy').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $('#followedUpByFilterFollowUpContainer'),
            width: "100%",
            templateResult: function (option) {

                let $option = renderTextInSelectStructures(option)
                return $option;
            },
            ajax: {
                delay: 400,
                url: usersUrl,
                type: "POST",
                dataType: 'json',

                headers: typeof headers !== "undefined" ? headers : "",
                data: function (term) {
                    return {
                        "searchText": term.term ? term.term : "", "structureType": StructureTypeMode.Both, 'fromSendingandReceiving': false
                    };
                },
                processResults: function (data, term) {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        if (val.userStructure != null && val.userStructure.length > 0) {
                            $.each(val.userStructure, function (key, userStructureVal) {
                                var item = {};
                                item.id = userStructureVal.userId;
                                var structureName = userStructureVal.structure.name;
                                var userName = userStructureVal.user.firstname + " " + userStructureVal.user.lastname
                                item.text = structureName + " / " + userName;
                                item.icon = "fa fa-user-o";
                                item.isStructure = false;
                                item.dataId = userStructureVal.userId;
                                item.structureId = userStructureVal.structureId;
                                listitemsMultiList.push(item);
                            });
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });

        $("#cmbFilterFollowUpFollowedUpBy").val('').trigger('change');

        var fromDate = $('#filterFollowUpFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    maxDate: jQuery('#filterFollowUpToDate').val() && jQuery('#filterFollowUpToDate').val() !== "" ? jQuery('#filterFollowUpToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterFollowUpFromDate_img").click(function ()
        {
            fromDate.toggle();
        });
        var toDate = $('#filterFollowUpToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    minDate: jQuery('#filterFollowUpFromDate').val() && jQuery('#filterFollowUpFromDate').val() !== "" ? jQuery('#filterFollowUpFromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterFollowUpToDate_img").click(function ()
        {
            toDate.toggle();
        });
        $('#filterFollowUpFromDate').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterFollowUpSearch').focus();
                }
                else
                {
                    $('#filterFollowUpToDate').focus();
                }
            }
        });
        $('#btnFilterFollowUpClear').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterFollowUpSearch').focus();
                }
                else
                {
                    $('#filterFollowUpFromDate').focus();
                }
            }
        });
    } else
    {
        $('.searchToRemove > div:first-child').hide();
        $("#gridPanel").removeAttr("style");
        $("#gridPanel").addClass("panel-default");
    }
}
function buildColumns(gridcolumns, nodeId)
{

    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var newColomns = new Nodes().getCustomcolumns(nodeId);


    if (newColomns) {
        node.columns = newColomns.content;
    }
    var columns = node.columns !== "" && node.columns !== null ? JSON.parse(node.columns) : [];
    var columnDetails = $.grep(columns, function (element, index)
    {
        return element.isColumnDetail === true;
    });
    if (columnDetails.length > 0)
    {
        gridcolumns.push({
            "className": 'details-control',
            "orderable": false,
            "data": null,
            "defaultContent": '',
            width: '16px'
        });
    }
    
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var customColumn = [];
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (!column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                customColumn.push({ columnName: column.name, functionName: column.customFunctionName });
                var customColumnKeyword = column.customFunctionName;
                var escapedVariable = column.customFunctionName.replace(/"/g, '&quot;');

                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                gridcolumns.push({
                    title: GetCustomAttributeTranslationByLangauge(customColumnKeyword), "orderable": false, 'defaultContent': '<div data-function="' + escapedVariable + '"></div>',
                    "createdCell": function (td, cellData, rowData, row, col)
                    {
                        var htmlCell = "";
                        var div = td.children[0];
                        var customFunctionName = div !== null && typeof div !== "undefined" ? $(div).attr("data-function") : "";
                        //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                        //{
                        //    var functionName = customFunctionName.split("(")[0];
                        //    htmlCell = eval(functionName + "(" + JSON.stringify(rowData) + ")");
                        //}

                        var customColumns = JSON.parse(rowData.documentForm.form);
                        var columnName = customColumn.find(f => f.functionName == customFunctionName).columnName;

                        var customData = customColumns[columnName]

                        if (customFunctionName.includes("()")) {
                            if (customFunctionName == "getCommitteeName()") {
                                customData = getCommitteeName(customData);
                            }

                            if (customFunctionName == "getMeetingLocation()") {
                                customData = getMeetingLocation(customData);
                            }
                        }

                        htmlCell = htmlCell == "" && customColumns != null ? (customData == undefined ? "" : customData) : htmlCell;

                        $(td).html(htmlCell);
                    }
                });
            } else
            {
                switch (column.name)
                {
                    case "Note":
                        gridcolumns.push(
                            {
                                title: Resources.Notes,
                                data: "notes",
                                render: function (data, type, full, meta) {
                                    if (Array.isArray(full.note) && full.note.length > 0) {
                                        return full.note.map(n => n.notes).join(',');
                                    }
                                    return "";
                                }
                            }
                        )
                        break;
                    case "Category":
                        gridcolumns.push({
                            title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"],
                            render: function (data, type, full, meta)
                            {
                                var categories = new Categories().get(window.language);
                                for (var i = 0; i < categories.length; i++)
                                {
                                    if (categories[i].id === data)
                                    {
                                        return categories[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "ModifiedDate":
                        gridcolumns.push({
                            title: Resources.ModifiedDate, data: "modifiedDate", "orderable": false, width: "100px",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.modifiedDate, null, window.CalendarType);
                            }
                        });
                        break;
                    case "ReferenceNumber":
                        gridcolumns.push({ title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": false, "className": "min-max-width-50-150" });
                        break;
                    case "Subject":
                        gridcolumns.push({ title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"], "className": "min-max-width-50-150" });
                        break;
                    case "FollowUpStatus":
                        gridcolumns.push({
                            title: Resources.Status, data: "statusId", render: $.fn.dataTable.render.text(), "className": "min-max-width-50-150"
                            , render: function (data) {
                                var statuses = self.model.followUpStatuses;
                                for (var i = 0; i < statuses.length; i++) {
                                    if (statuses[i].id === data) {
                                        return "<div class='label' style='background-color:" + (statuses[i].color !== null ? statuses[i].color : "#27c24c") + "'>" + statuses[i].text + "</div>";
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "SendingEntity":
                        gridcolumns.push({ title: Resources.SendingEntity, data: "sendingEntity", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
                        break;
                    case "ReceivingEntity":
                        gridcolumns.push({ title: Resources.ReceivingEntity, data: "receivingEntity", render: $.fn.dataTable.render.text(), "orderable": false });
                        break;
                    case "CreatedDate":
                        gridcolumns.push({
                            title: Resources.CreatedDate, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
                            }
                        });
                        break;
                    case "DueDate":
                        gridcolumns.push({
                            title: Resources.DueDate, data: "dueDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.dueDate, null, window.CalendarType);
                            }
                        });
                        break;
              
                 
                }
            }
        }
    }

}
function buildColumnsDetails(row, nodeId)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var columns = node.columns !== "" && node.columns !== null ? JSON.parse(node.columns) : [];
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var html = "";
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                var customColumn = column.name;
                var customColumnKeyword = column.customFunctionName;

                var htmlCell = "";
                var customFunctionName = column.customFunctionName;
                //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                //{
                //    var functionName = customFunctionName.split("(")[0];
                //    htmlCell = eval(functionName + "(" + JSON.stringify(row.data()) + ")");
                //}

                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                var customColumns = JSON.parse(row.data().documentForm.form);

                customColumns = customColumns[customColumn];

                if (customFunctionName.includes("()")) {
                    if (customFunctionName == "getCommitteeName()") {
                        customColumns = getCommitteeName(customColumns);
                    }

                    if (customFunctionName == "getMeetingLocation()") {
                        customColumns = getMeetingLocation(customColumns);
                    }
                }

                htmlCell = htmlCell == "" && customColumns != null ? (customColumns == undefined ? "" : customColumns) : htmlCell;

                html += '<tr><th style="width: 10%;padding:5px">' + (GetCustomAttributeTranslationByLangauge(customColumnKeyword)) + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + htmlCell + '</td></tr>';

            } else
            {
                switch (column.name) {
                 
                    case "Category":
                        var category = "";
                        var categories = new Categories().get(window.language);
                        if (categories && Array.isArray(categories)) {
                            var matchedCategory = categories.find(c => c.id === row.data().categoryId);
                            category = matchedCategory ? matchedCategory.text : "";
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Category + ':</th>' +
                            '<td style="width: 85%;padding:5px;word-break: break-all;">' + category + '</td></tr>';
                        break;

                    case "ReferenceNumber":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReferenceNumber + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().referenceNumber || '') + '</td></tr>';
                        break;
                    case "Subject":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Subject + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().subject || '') + '</td></tr>';
                        break;
                    case "From":
                        var from = "";
                        if (row.data().fromStructure) {
                            from += row.data().fromStructure;
                        }
                        if (row.data().fromUser) {
                            var user = row.data().fromUser;
                            from += from !== "" ? "/" + user : user;
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.From + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + from + '</td></tr>';
                        break;

                    case "CreatedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.CreatedDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().createdDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "SendingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.SendingEntity + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().sendingEntity || '') + '</td></tr>';
                        break;
                    case "ReceivingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReceivingEntity + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().receivingEntity || '') + '</td></tr>';
                        break;
                    case "Purpose":
                        var purpose = "";
                        var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                        for (let i = 0; i < purposes.length; i++) {
                            if (purposes[i].id === row.data().purposeId) {
                                purpose = purposes[i].text;
                            }
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Purpose + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + purpose + '</td></tr>';
                        break;
              

                    case "OpenedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.OpenedDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().openedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "DueDate":
                        html +=
                            '<tr><th style="width: 10%;padding:5px">' + Resources.DueDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().dueDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "ModifiedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ModifiedDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().modifiedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';

                        //gridcolumns.push({
                        //    title: Resources.ModifiedDate, data: "modifiedDate", "orderable": false, width: "100px",
                        //    render: function (data, type, full, meta) {
                        //        return DateConverter.toHijriFormated(full.modifiedDate, null, window.CalendarType);
                        //    }
                        //});
                        break;
                }

            }
        }
    }
    return html;
}
class FollowUpListView extends Intalio.View
{
    constructor(element, model)
    {
        wrapperParent = model;
        super(element, "followUp", model);
    }
    render()
    {

        self = this;
        var model = this.model;
        gfirstTime = true;
        gDataChanged = true;
        gAction = "";
        gFilterChanged = false;
        gFilterStructure = "";
        gFilterFromDate = "";
        gFilterToDate = "";
        gFilterUser = "";
        gFilterOverdue = "";
        gOverdue = false;
        clear = false;
        gReportName = "FollowUp Report";
        var clickedSearch = false;
        $.fn.select2.defaults.set("theme", "bootstrap");
        buildFilters(self.model.nodeId);

        Common.gridCommon();
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        //var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, grdFollowUpItems);
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, grdFollowUpItems).map(button => {
            button.className = (button.className || '') + ' hidden conditional-buttons';
            return button;
        });
        var exportButton=[];

        var exportButton = [{
            className: 'btn-sm btn-primary',

            extend: 'print',
            text: Resources.Print,
            title: function () {
                return Resources.FollowUpReport;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.FollowUpReport + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [':visible']

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.FollowUpReport.print
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'excelHtml5',
            title: function () {
                return Resources.FollowUpReport;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.FollowUpReport + '_' + formattedDate + '_' + formattedTime;
            },


            exportOptions: {
                columns: [ ':visible']

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.FollowUpReport.excelHTML5
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'pdfHtml5',
            title: function () {
                return Resources.FollowUpReport;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.FollowUpReport + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [ ':visible']
            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.FollowUpReport.pdfHTML5
            }
            //,{
            //className: 'btn-sm btn-primary',
            //text: Resources.CustomizeColumns,
            //action: function (e, dt, node, config) {

            //    var wrapper = $(".modal-window");
            //    var customeModel = new CustomizeNodeColomns.CustomizeNodeColomns();
            //    customeModel.nodeId = model.nodeId;
            //    customeModel.text = "FollowUp";
            //    var CustomizeNodeColomnsViews = new CustomizeNodeColomns.CustomizeNodeColomnsView(wrapper, customeModel);
            //    CustomizeNodeColomnsViews.render();

            //    $("#nodeColomnsModal").parsley().reset();

            //    $('#nodeColomnsModal').modal('show');

            //    $("#nodeColomnsModal").off("hidden.bs.modal");
            //    $("#nodeColomnsModal").off("shown.bs.modal");

            //    $('#nodeColomnsModal').on('shown.bs.modal', function () {
            //    });

            //    $('#nodeColomnsModal').on('hidden.bs.modal', function () {
            //        $('#formPostNode').parsley().reset();
            //        $('#nodeColomnsModal').remove();
            //    });
            //}
            //}
        ];
        var allButtons = [buttons, ...exportButton];

        var columns = [{ title: '<input id="chkAll" type="checkbox" />', width: '16px', "orderable": false, "render": function (data, type, row) { return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />"; } },
        { title: "Id", data: "id", visible: false, "orderable": false }];
        buildColumns(columns, self.model.nodeId);

        columns.push({
            "className": "text-center",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = "";
                var text = "";
                var color = "";


                var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                for (var i = 0; i < privacies.length; i++) {
                    if (privacies[i].id === full.privacyId) {
                        color = privacies[i].color;
                        text = Resources.Privacy + ": " + privacies[i].text;
                        html += "<div class='mr-sm' title='" + text + "' style='height: 24px'>" +
                            "<i class='fa fa-shield fa-lg' style='color: " + color + "'></i>&nbsp;" +
                            "</div>";
                        break;
                    }
                }

                return html;
            }
        });
        columns.push({
            "className": "text-center",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = '';
                var categories = new Categories().get(window.language);
                var matchedCategory, categoryName;

                matchedCategory = categories.find(c => c.id === full.categoryId);

                categoryName = matchedCategory ? matchedCategory.text : "";
                if (matchedCategory.text == "Incoming") {


                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                        "<i class=' fa fa-arrow-down '></i>&nbsp;" +
                        "</div>";
                }
                else if (matchedCategory.text == "Outgoing") {


                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                        "<i class=' fa fa-arrow-up '></i>&nbsp;" +
                        "</div>";
                } else if (matchedCategory.text == "Internal") {


                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                        "<i class=' fa fa-arrows-h '></i>&nbsp;" +
                        "</div>";
                }



                return html;
            }
        });

        columns.push({
            'visible': false,
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {

                const nodes = new CoreComponents.Lookup.Nodes().get();
                var nodeName = nodes.find(e => e.id == self.model.nodeId).name;
                var html = "";
                if (full.isOverDue && nodeName !='Overdued') {
                    html += "<div class='mr-sm' title='" + Resources.OverDue + "'><i class='fa fa-clock-o fa-lg text-danger'></i></div>&nbsp;";
                }
                if (full.isHasNote && nodeName != "WithNote" ) {
                    html += "<div class='mr-sm' title='" + Resources.HasNote + "' style='height: 24px'>" +
                        "<i class='fa fa-sticky-note-o text-info'></i>&nbsp;" +
                        "</div>";
                }

                return "<div id='followUpDivIcons_" + full.id + "' style='display: inline-flex;align-items: center'>" + html + "</div>";
            }
        });

        columns.push({
            "className": "text-center",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (datas, type, full, meta)
            {
                var isReadonly = false;
                let btn = document.createElement("button");
                if (full.statusId == FollowUpStatuses.Completed || full.statusId == FollowUpStatuses.Canceled) {
                    isReadonly = true;
                    btn.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                    btn.setAttribute("title", Resources.View);
                    btn.setAttribute("id", "FollowUpViewBtn_" + full.id);
                    btn.innerHTML = "<i class='fa fa-eye fa-white'/>";
                } else {

                    btn.setAttribute("class", "btn btn-xs btn-primary edit");
                    btn.setAttribute("title", Resources.Edit);
                    btn.setAttribute("id", "FollowUpEditBtn_" + full.id);
                    btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                }
                full.readonly = isReadonly == true ? isReadonly : full.readonly;
                var data = JSON.stringify(full)
                btn.setAttribute("clickattr", "openDocument(" + data + ", " + self.model.nodeId + ")");

                return btn.outerHTML;
            }
        });
        SecurityMatrix.getRowActions(securityMatrix, columns, self.model.nodeId);
        var table = $("#" + grdFollowUpItems)
            .on('draw.dt', function ()
            {
                $('#' + grdFollowUpItems + " td input[type='checkbox']").on('click', function () {
                    if ($(this).is(":checked")) {
                        $(".html5buttons .btn-danger").removeClass("hidden");
                        $(".conditional-buttons").removeClass("hidden");
                    }
                    else if (GridCommon.GetSelectedRows(grdFollowUpItems).length == 1) {
                        $(".html5buttons .btn-danger").addClass("hidden");
                        $(".conditional-buttons").addClass("hidden");
                    }
                });

                $('#' + grdFollowUpItems + ' tbody tr td').each(function ()
                {
                    this.setAttribute('title', $(this).text());
                });
                GridCommon.CheckSelectedRows(grdFollowUpItems);
            })
            .DataTable({
                "createdRow": function (row, data, dataIndex)
                {
                    var color = "";
                    var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                    for (var i = 0; i < priorities.length; i++)
                    {
                        if (priorities[i].id === data.priorityId)
                        {
                            color = priorities[i].color;
                        }
                    }
                    if (color !== "")
                    {
                        $(row).attr('style', "color:" + color);
                    }
                },
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/FollowUp/ListFollowUp",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d)
                    {
                        d.NodeId = self.model.nodeId;
                        d.CategoryId = $("#cmbFilterFollowUpCategory").val() !== null && typeof $("#cmbFilterFollowUpCategory").val() !== "undefined" ? $("#cmbFilterFollowUpCategory").val() : "0";
                        d.FollowedUpById = $("#cmbFilterFollowUpFollowedUpBy").val() !== null && typeof $("#cmbFilterFollowUpFollowedUpBy").val() !== "undefined" ? $("#cmbFilterFollowUpFollowedUpBy").val() : "0";
                        d.SendingEntityId = $("#cmbFilterFollowUpSendingEntity").val() !== null && typeof $("#cmbFilterFollowUpSendingEntity").val() !== "undefined" ? $("#cmbFilterFollowUpSendingEntity").val() : "0";
                        d.ReceivingEntityId = $("#cmbFilterFollowUpReceivingEntity").val() !== null && typeof $("#cmbFilterFollowUpReceivingEntity").val() !== "undefined" ? $("#cmbFilterFollowUpReceivingEntity").val() : "0";
                        d.PriorityId = $("#cmbFilterFollowUpPriority").val() !== null && typeof $("#cmbFilterFollowUpPriority").val() !== "undefined" ? $("#cmbFilterFollowUpPriority").val() : "0";
                        d.FromDate = $("#filterFollowUpFromDate").val() !== "" && typeof $("#filterFollowUpFromDate").val() !== "undefined" ? $("#filterFollowUpFromDate").val() : "";
                        d.ToDate = $("#filterFollowUpToDate").val() !== "" && typeof $("#filterFollowUpToDate").val() !== "undefined" ? $("#filterFollowUpToDate").val() : "";
                        d.Subject = $("#txtFilterFollowUpSubject").val() !== "" && typeof $("#txtFilterFollowUpSubject").val() !== "undefined" ? $("#txtFilterFollowUpSubject").val() : "";
                        d.Year = $("#txtFilterFollowUpYear").val() !== "" && typeof $("#txtFilterFollowUpYear").val() !== "undefined" ? $("#txtFilterFollowUpYear").val() : "0";
                        d.KeyWord = $("#txtFilterFollowUpKeyWord").val() !== "" && typeof $("#txtFilterFollowUpKeyWord").val() !== "undefined" ? $("#txtFilterFollowUpKeyWord").val() : "";
                        d.ReferenceNumber = $("#txtFilterFollowUpReferenceNumber").val() !== "" && typeof $("#txtFilterFollowUpReferenceNumber").val() !== "undefined" ? $("#txtFilterFollowUpReferenceNumber").val() : "";
                        d.Note = $("#txtFilterFollowUpNote").val() !== "" && typeof $("#txtFilterFollowUpNote").val() !== "undefined" ? $("#txtFilterFollowUpNote").val() : "";
                        d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val()
                        return d;
                    }
                },
                "order": [],
                "columns": columns,
                "fnInitComplete": function (settings, json)
                {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons "B>ltrpi',
                buttons: allButtons
            });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        GridCommon.AddCheckBoxEvents(grdFollowUpItems);
        $('#' + grdFollowUpItems + ' tbody').on('click', 'tr', function () {
          
            let checkbox = $(this).find('input[type="checkbox"]');
            if (checkbox.is(":checked")) {
                checkbox.prop("checked", true);
            } else {
                checkbox.prop("checked", false);
            }
            
            UpdateButtonVisibility(grdFollowUpItems);
        });

        function UpdateButtonVisibility(tableId) {
            var selectedRows = GridCommon.GetSelectedRows(tableId).length;
            if (selectedRows > 0) {
                $(".html5buttons .btn-danger").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");
            }
            else if (selectedRows == 0) {
                $(".html5buttons .btn-danger").addClass("hidden");
                $(".conditional-buttons").addClass("hidden");
            }
        }
        $('#' + grdFollowUpItems + ' tbody').on('dblclick', 'tr', function ()
        {
            if (!gLocked)
            {
                gLocked = true;
                try
                {
                    var onclick = $(this).find(".edit").attr("clickattr");
                    eval(onclick);
                } catch (e)
                {
                    gLocked = false;
                }
            }
        });
        $('#' + grdFollowUpItems + ' tbody').on('click', ".edit,.view", function ()
        {

            if (!gLocked) {
                gLocked = true;
                try {
                    var onclick = $(this).attr("clickattr");
                    eval(onclick);
                } catch (e) {
                    gLocked = false;
                }
            }
            
        });
        $('#' + grdFollowUpItems + ' tbody').on('click', 'td.details-control', function ()
        {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown())
            {
                row.child.hide();
                tr.removeClass('shown');
            }
            else
            {
                row.child(format(row, self.model.nodeId)).show();
                tr.addClass('shown');
            }
        });
        SecurityMatrix.InitToolbarColor();
        SecurityMatrix.InitContextMenu(securityMatrix, self.model.nodeId);
        $('#filtersContainer input').off('keydown', function (e) { });
        $('#filtersContainer input').on('keydown', function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                $("#btnFilterFollowUpSearch").trigger('click');
            }
        });

        $('#' + grdFollowUpItems).on('click', '#chkAll', function () {
            let isChecked = $(this).is(":checked");

            if (isChecked) {
                $(".html5buttons .btn-danger").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".html5buttons .btn-danger").addClass("hidden");
                $(".conditional-buttons").addClass("hidden");
            }

            let table = $('#' + grdFollowUpItems).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', isChecked);
        });
        $('#' + grdFollowUpItems).on('change', 'input[type="checkbox"]:not(#chkAll)', function () {
            let table = $('#' + grdFollowUpItems).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();

            let total = $('input[type="checkbox"]:not(#chkAll)', pageNodes).length;
            let checked = $('input[type="checkbox"]:not(#chkAll):checked', pageNodes).length;

            $('#chkAll').prop('checked', total === checked);

            if (checked > 0) {
                $(".html5buttons .btn-danger").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".html5buttons .btn-danger").addClass("hidden");
                $(".conditional-buttons").addClass("hidden");
            }
        });


        table.on('draw', function () {
            $('#chkAll').prop('checked', false);

            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', false);

            $(".html5buttons .btn-danger").addClass("hidden");
            $(".conditional-buttons").addClass("hidden");
        });

        $('.toggleVIP').on('click', function () {
            window.FollowUpMode = "FollowUpVIPView"

            let wrapper = $(".content-wrapper");
            let VIPmodel = new VipDocumentFollowUp.VipDocumentFollowUp();
            VIPmodel.nodeId = wrapperParent.nodeId;
            VIPmodel.delegationId = wrapperParent.delegationId;
            VIPmodel.categories = new Categories().get(window.language, wrapperParent.delegationId);
            VIPmodel.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            VIPmodel.title = $('.content-heading').text();
            let documentView = new VipDocumentFollowUp.VipDocumentFollowUpView(wrapper, VIPmodel);
            documentView.render();

        })
    }
}
export default { FollowUpListModel, FollowUpListView };
