﻿using Intalio.CTS.Core.DAL;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.Model;
using Intalio.Core.DAL;
using Intalio.Core.UI.Controllers;
using Intalio.CTS.Core;
using Intalio.CTS.Filters;
using Intalio.Core;
using Intalio.Core.Model;
using System.Threading.Tasks;
namespace Intalio.CTS.Controllers
{
    [Route("/[controller]/[action]")]

    public class TeamsController : BaseController
    {

        /// <summary>
        /// List Team
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(GridListViewModel<TeamModel>), 200)]
        public async Task<JsonResult> List(int draw, int start, int length)
        {
            try
            {
                var retValue = await ManageTeams.List(start, length, UserId, RoleId, StructureId, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }
        /// <summary>
        /// List categories returns all categories DDL.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<ValueText>), 200)]
        [Produces("application/json")]
        public IActionResult ListTeams()
        {
            try
            {
                return Ok(ManageTeams.ListTeams(UserId,StructureId));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Create or edit Team
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult Index(TeamModel model, List<long> userIds, List<long> structureIds)
        {
            try
            {
                Result retValue = new Result();
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                bool success = false;
                if (ModelState.IsValid)
                {
                    if (model.Id.HasValue)
                    {
                        if (ManageTeams.Edit(model, UserId, StructureId,userIds,structureIds))
                        {
                            success = true;
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        }
                    }
                    else
                    {
                        if (ManageTeams.Create(model, UserId, StructureId,userIds,structureIds))
                        {
                            success = true;
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        }
                    }
                    if (!success)
                    {
                        retValue.Message = TranslationUtility.Translate("TeamAlreadyExist", Language);
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                    }
                    else
                    {
                        retValue.Id = model.Id.Value;
                    }
                }
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Delete Team
        /// </summary>
        /// <param name="id">Team id</param>
        [HttpDelete]
        public IActionResult Delete(long id)
        {
            try
            {
                return Ok(ManageTeams.Delete(id,Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxErrorWithMsg(ex);
            }
        }
        /// <summary>
        /// Get Teams by StructureId
        /// </summary>
        /// <param name="structureId">Structure Id</param>
        /// <returns>List of teams related to the structureId</returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<TeamModel>), 200)]
        public async Task<JsonResult> GetTeamsByStructureId(long structureId)
        {
            try
            {
                var teams = await ManageTeams.GetTeamsByStructureId(structureId);

                return Json(new
                {
                    success = true,
                    data = teams
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// Get Team by team id
        /// </summary>
        /// <param name="id">team Id</param>
        /// <returns>team with team id</returns>
        [HttpGet]
        [ProducesResponseType(typeof(TeamUsersModelView), 200)]
        public async Task<JsonResult> Get(long id)
        {
            try
            {
                var team = await ManageTeams.Get(id);

                return Json(new
                {
                    success = true,
                    data = team
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }
    }
}
    
