﻿using Intalio.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using Microsoft.EntityFrameworkCore;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.DAL
{
    public partial class TeamUsers
    {
        public TeamUsers()
        {
            
        }
        private CTSContext _ctx;
        #region Properties
        public long UserId { get; set; }
        public long TeamId { get; set; }
        public long StructureId { get; set; }
        public virtual User User { get; set; }
        public virtual Team Team { get; set; }
       public virtual Structure Structure { get; set; }
       public virtual User CreatedByUser { get; set; }

        public long CreatedByUserId { get; set; }
        public DateTime CreatedDate { get; set; }

        #endregion
        private void OpenDbContext()
        {
            if (_ctx == null)
            {
                _ctx = new CTSContext();
            }
        }

        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                this.CreatedDate = DateTime.Now;
                ctx.TeamUsers.Add(this);
                ctx.SaveChanges();
            }
        }


        public TeamUsers Find(long userId,long teamId,long structureId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.TeamUsers.AsNoTracking().FirstOrDefault(tu => tu.UserId == userId && tu.TeamId == teamId&&tu.StructureId== structureId);
            }
        }
        public void Update()
        {
            using (var ctx = new CTSContext())
            {
                ctx.Entry(this).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                ctx.SaveChanges();
            }
        }


        public void Delete(long userId,long teamId,long structureId)
        {
            using (var ctx = new CTSContext())
            {
                TeamUsers item = new TeamUsers { UserId = userId,TeamId=teamId,StructureId=structureId };
                ctx.TeamUsers.Attach(item);
                ctx.TeamUsers.Remove(item);
                ctx.SaveChanges();
            }
        }



        public List<TeamUsers> GetAll()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.TeamUsers.ToList();
            }
        }

        public void Delete()
        {
            Delete(UserId,TeamId,StructureId);
        }
        public List<TeamUsers> GetTeamUsersByTeam(long teamId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.TeamUsers.Include(x=> x.Structure).Include(x=>x.User).Where(tu => tu.TeamId == teamId).ToList();
            }
        }

        //public List<Team> GetTeamsByStructureId(long structureId)
        //{
        //    using (var ctx = new CTSContext())
        //    {
        //        var teams = ctx.TeamUsers
        //                    .Where(tu => tu.StructureId == structureId)
        //                    .Select(tu => tu.Team)
        //                    .Distinct()
        //                    .ToList();

        //        return teams;
        //    }
        //}
        #region Conversion

        public static implicit operator TeamUsersModelView(TeamUsers item)
        {
            TeamUsersModelView retValue = null;
            var language = Helper.GetLanguage();
            var structure = string.Empty;
            if (item.Structure != null)
            {
                structure = item.Structure.Name;
                if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(item.Structure.NameAr))
                {
                    structure = item.Structure.NameAr;
                }
                else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(item.Structure.NameFr))
                {
                    structure = item.Structure.NameFr;
                }
            }
            if (item != null)
            {
                retValue.UserId = item.UserId;
                retValue.User = language == Intalio.Core.Language.EN ? $"{item.User.Firstname} {item.User.Lastname}" : $"{IdentityHelperExtension.GetFullName(item.User.Id, language)}";
                retValue.StructureId = item.StructureId;
                retValue.Structure = structure;
                retValue.TeamId= item.TeamId;
                retValue.Team= item.Team!=null? item.Team.Name:string.Empty;
            }

            return retValue;
        }

        #endregion
    }
}
