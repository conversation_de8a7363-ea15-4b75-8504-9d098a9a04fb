﻿
using Intalio.CTS.Core.DAL;
using System.Collections.Generic;

namespace Intalio.CTS.Core.Model
{
    public class FollowUpListViewModel
    {
        public long Id { get; set; }
        public long DocumentId { get; set; }
        public long OriginalDocumentId { get; set; }
        public string Subject { get; set; }
        public string SendingEntity { get; set; }
        public string ReceivingEntity { get; set; }
        public int CategoryId { get; set; }
        public string ReferenceNumber { get; set; }
        public long StatusId { get; set; }
        public string StatusName { get; set; }
        public bool IsOverDue { get; set; }
        public bool IsHasNote { get; set; }
        public short? PriorityId { get; set; }
        public short? PrivacyId { get; set; }
        public string DueDate { get; set; }
        public string CreatedDate { get; set; }
        public long CreatedByUserId { get; set; }
        public string CreatedByUser { get; set; }
        public long? TeamId { get; set; }
        public bool IsPrivate { get; set; }
        public short? FollowUpUserRole { get; set; }
        public bool Readonly { get; set; }


    }
}