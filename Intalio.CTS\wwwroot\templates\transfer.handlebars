<div ref="{{ComponentId}}">
    <div ref="modalTransfer" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade modalTransfer">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="col-md-3 col-xs-2">
                        <h3 ref="modalTransferTitle" class="modal-title">{{Localizer 'TransferTitle'}}</h3>
                        <!--The header title Transfer takes third the width of the header in defult device sizes(md) like Laptops-->
                    </div>
                    <div class="col-md-9 col-xs-10">
                        <button type="button" class="close pl-lg fa-3x" data-dismiss="modal">&times;</button>
                        <div class="pull-right pr-lg pointer" ref="setPurposeToAll" title="{{Localizer 'SetAllPurposes'}}">
                            <i class="fa fa-chevron-circle-down pl-sm fa-2x" aria-hidden="true" style="color:#32a588;padding-top:3px;margin-left:5px"></i>
                        </div>
                        <div ref="purposeContainer" class="pull-right" style="width: 20% !important; padding-left:15px ">
                            <select ref="selectPurpose" class="form-control"></select>
                        </div>


                        <button type="button" class="btn btn-info pull-right" ref="multipleTransfer">
                            <em class="fa fa-address-book-o mr-lg"></em>{{Localizer 'AddressBook'}}
                        </button>
                        <div class="pull-right pr-lg checkbox " style="margin-top:0.5rem">
                            <label>

                                <input id="chkCreateFollowUpTransfer" class="pull-right" style=" margin-left: -15px;" tabindex="56" type="checkbox" value="false">
                                {{Localizer 'CreateFollowUp'}}
                            </label>
                        </div>


                        <!--<button type="button" class="btn btn-info pull-right" ref="multipleTransfer">
    <em class="fa fa-eye mr-sm" aria-hidden="true"></em>{{Localizer 'ShowFavoriteTransfers'}}    </button>-->
                        {{#if favoriteStructures.length}}
                        <div class="pull-right pr-lg checkbox c-checkbox" style="margin:0; margin-top:0.5rem">
                            <label>
                                <input id="chkShowFavoriteTransfers" tabindex="56" type="checkbox" value="true" checked>
                                <span class="fa fa-check"></span>{{Localizer 'ShowFavoriteTransfers'}}
                            </label>
                        </div>
                        {{/if}}


                    </div>
                </div>
                <div class="modal-body">
                    <form ref="formTransferPost" method="post" data-parsley-validate="" novalidate="">
                        <div class="row margin-0">
                            <div class="col-lg-12">
                                {{#if favoriteStructures.length}}
                                <div ref="grdFavoriteTransferContainer">

                                    <h4>{{Localizer 'FavoriteContacts'}}</h4>

                                    <table id="grdFavoriteTransferContainer" ref="grdFavoriteTransferItems" class="table table-striped dataTable table-bordered" style="width: 100%">
                                        <thead>
                                            <tr>
                                                <th class="width-150px">
                                                    {{Localizer 'To'}}<span class="text-danger">*</span>
                                                </th>
                                                <th class="width-100px">
                                                    {{Localizer 'Purpose'}} <span class="text-danger">*</span>
                                                </th>

                                                <th class="width-400px">{{Localizer 'Instruction'}}<span id="{{ComponentId}}_InstructionTextDanger_{{Id}}" class="text-danger" hidden>*</span></th>


                                                <th class="width-50px"></th>
                                                <!-- These <th> elements tags represent the headers title of the cells content -->
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Single Row for Favorites content corresponding to table headers <th> -->
                                            {{#each favoriteStructures}}
                                            <tr id="{{ComponentId}}_favoritesRow_{{id}}" style="overflow: hidden;">

                                                <td id="{{ComponentId}}_transferToContainer_Favorite_{{id}}"
                                                    style="vertical-align:top; padding:12px; overflow-y:auto; width: 30%;">
                                                    {{name}}
                                                </td>

                                                <td id="{{ComponentId}}_transferPurposeContainer_Favorite_{{id}}" style="width: 20%;">
                                                    <select id="{{ComponentId}}_cmbTransferPurpose_Favorite_{{id}}"
                                                            data-parsley-errors-container="#{{ComponentId}}_cmbTransferPurposeError_Favorite"
                                                            ref="favoritesPurpose"
                                                            class="form-control purposeFavorites">
                                                    </select>
                                                    <div id="{{ComponentId}}_cmbTransferPurposeError_Favorite"></div>
                                                </td>
                                                <td style="width: 40%;">
                                                    <div class="input-group"  style="width: 100%;">
                                                        <textarea id="{{../ComponentId}}_transferInstruction_Favorite_{{id}}" rows="2" class="form-control inputChange  InstructionTextArea" style="width: 100%; flex: 1; border-radius: 4px 0 0 4px;"></textarea>
                                                        <span id="{{../ComponentId}}_transferInstruction_img"
                                                              class="input-group-addon triggerInstruction"
                                                              clickAttr="showHideInstruction({{id}},true)"
                                                              style="cursor: pointer; padding: revert-layer;"
                                                              title="{{Localizer 'Instruction'}}">
                                                            <i class="fa fa-ellipsis-h" aria-hidden="true"></i>
                                                        </span>
                                                    </div>

                                                    <textarea name="textarea" tabindex="-1" value="" class="hidden" id="{{../ComponentId}}_hidden_Area_Favorite_{{id}}"></textarea>
                                                </td>

                                                <td id="{{ComponentId}}_transferToList_AddButton_{{id}}" style="text-align: center;">
                                                    <div class="btn btn-primary btn-outline btn-td-add"
                                                         ref="AddFavorites"
                                                         title="{{Localizer 'Add'}}"
                                                         data-id="{{id}}"
                                                         style="display: inline-flex; justify-content: center; align-items: center; padding: 0; cursor: pointer;">
                                                        <i class="fa fa-plus"
                                                           aria-hidden="true"></i>
                                                    </div>
                                                </td>
                                            </tr>
                                            {{/each}}
                                        </tbody>
                                    </table>
                                    <hr />
                                </div> {{/if}}

                                <div class="glyphicon-container-color overflow-auto">

                                    <table ref="grdTransferItems" class="table table-striped table-bordered" style="width: 100%">
                                        <thead>
                                        <tr>
                                            <th class="width-300px">{{Localizer 'To'}}<span class="text-danger">*</span></th>
                                            <th class="width-200px">{{Localizer 'Purpose'}}<span class="text-danger">*</span></th>
                                            <th class="width-200px">{{Localizer 'Priority'}}<span class="text-danger">*</span></th>
                                            <th class="width-200px">{{Localizer 'DueDate'}}</th>
                                            <th class="width-300px">{{Localizer 'Instruction'}}<span id="{{ComponentId}}_InstructionTextDanger_2" class="text-danger" hidden>*</span></th>
                                            <th class="width-50px">{{Localizer 'Private'}}</th>
                                            <th class="width-50px">{{Localizer 'CCed'}}</th>
                                            <!--<th class="width-80px">{{Localizer 'FollowUp'}}</th>-->
                                            <th class="width-50px"></th>
                                            <!--<th class="width-50px"></th>-->
                                        </tr>
                                        </thead>
                                            <tbody>
                                                <tr>
                                                    <td id="{{ComponentId}}_transferToContainer_2">
                                                        <div class="input-group">
                                                            <select id="{{ComponentId}}_cmbTransferTo_2"
                                                                    required
                                                                    data-parsley-errors-container="#{{ComponentId}}_cmbTransferToError" class="form-control">
                                                            </select>
                                                            <span class="input-group-addon triggerAddressBook" id="{{ComponentId}}_transferToAddressBook_2" clickAttr="openTransferAddressBook(2)" style="cursor:pointer">
                                                                <i class="fa fa-address-book-o" aria-hidden="true"></i>
                                                            </span>
                                                        </div>
                                                        <div id="{{ComponentId}}_cmbTransferToError"></div>
                                                    </td>
                                                    <td id="{{ComponentId}}_transferPurposeContainer_2">
                                                        <select id="{{ComponentId}}_cmbTransferPurpose_2"
                                                                required
                                                                data-parsley-errors-container="#{{ComponentId}}_cmbTransferPurposeError"
                                                                class="form-control purposeAll">
                                                        </select>
                                                        <div id="{{ComponentId}}_cmbTransferPurposeError"></div>
                                                    </td>
                                                    <td id="{{ComponentId}}_transferPriorityContainer_2">
                                                        <select id="{{ComponentId}}_cmbTransferPriority_2"
                                                                required
                                                                data-parsley-errors-container="#{{ComponentId}}_cmbTransferPriorityError"
                                                                class="form-control priorityAll">
                                                        </select>
                                                        <div id="{{ComponentId}}_cmbTransferPriorityError"></div>
                                                    </td>
                                                    <td>
                                                        <div class="input-group date staticFlatPickrDate">
                                                            <input id="{{ComponentId}}_transferDueDate_2" type="text" autocomplete="off" class="form-control inputChange" name="Date">
                                                            <span class="input-group-addon" id="{{ComponentId}}_transferDueDate_img" style="cursor:pointer">
                                                                <i class="fa fa-calendar" aria-hidden="true"></i>
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="input-group">
                                                            <textarea id="{{ComponentId}}_transferInstruction_2" rows="2" class="form-control inputChange  InstructionTextArea" style="flex: 1; border-radius: 4px 0 0 4px;"></textarea>
                                                            <span id="{{ComponentId}}_transferInstruction_img"
                                                                  class="input-group-addon triggerInstruction"
                                                                  clickAttr="showHideInstruction(2)"
                                                                  style="cursor: pointer; padding: revert-layer;"
                                                                  title="{{Localizer 'Instruction'}}">
                                                                <i class="fa fa-ellipsis-h" aria-hidden="true"></i>
                                                            </span>
                                                        </div>

                                                        <textarea name="textarea" tabindex="-1" value="" class="hidden" id="{{ComponentId}}_hidden_Area_2"></textarea>
                                                    </td>
                                                    <td class="text-center">
                                                        <input id="{{ComponentId}}_checkInstructionPrivate_2" type="checkbox">
                                                    </td>
                                                    <td id="{{ComponentId}}_transferCcedContainer_2" class="text-center">
                                                        <input id="{{ComponentId}}_TransferCced_2" class="checkbox inputChange transferCcedCheckbox" type="checkbox" name="Cced">
                                                    </td>
                                                    <!--<td id="{{ComponentId}}_transferFollowUpContainer_2" class="text-center">
                                                        <input id="{{ComponentId}}_TransferFollowUp_2" class="checkbox inputChange" type="checkbox" name="FollowUp">
                                                    </td>-->
                                                    <td class="text-center">
                                                        <a id="{{ComponentId}}_btnDeleteParam_2" class="btn btn-danger delete-row hidden"
                                                           title="Delete" clickattr="removeRow(this)" hidden><span class="fa fa-trash fa-fw"></span></a>
                                                    </td>
                                                </tr>
                                            </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="required"><span class="text-danger">*</span> {{Localizer 'RequiredFields'}}</div>
                </div>
                <div class="modal-footer">
                    <div style="text-align:left" id="maintainTransferSection" name="maintainTransferSection" class="col-md-4">
                        <input style="margin-inline: 6px;" id="chkMaintainTransfer" tabindex="54" type="checkbox" name="MaintainTransfer" value="true">
                        <label style="margin-top: 8px;">{{Localizer 'MaintainTransfer'}}</label>
                    </div>
                    <button tabindex="53" ref="btnTransfer" type="button" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin'></i> {{Localizer 'Saving'}}">{{Localizer 'TransferButton'}}</button>
                    <!--<button tabindex="53" ref="btnProceed" type="button" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin'></i> {{Localizer 'Saving'}}">{{Localizer 'Proceed'}}</button>-->
                    <button tabindex="55" ref="btnCloseTransfer" type="button" data-dismiss="modal" class="btn btn-default">{{Localizer 'Close'}}</button>
                </div>
            </div>
        </div>
    </div>
    <div ref="modalTransferInstruction" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close closeTransferInstruction" data-dismiss="modal">&times;</button>
                    <h4 ref="modalTransferInstructionTitle" class="modal-title">{{Localizer 'Instruction'}}</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <textarea class="form-control" tabindex="60" rows="5" fromElement="" id="{{ComponentId}}_txtAreaTransferInstruction"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button tabindex="61" ref="btnTransferInstruction" type="button" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin'></i> {{Localizer 'Saving'}}">{{Localizer 'Save'}}</button>
                    <button tabindex="62" ref="btnCloseTransferInstruction" type="button" data-dismiss="modal" class="btn btn-default">{{Localizer 'Close'}}</button>
                </div>
            </div>
        </div>
    </div>
</div>
