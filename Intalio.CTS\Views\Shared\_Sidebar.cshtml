@using Intalio.Core;
@using Microsoft.AspNetCore.Authorization
@inject IAuthorizationService AuthorizationService
@inject Intalio.Core.Translation Localizer;

@using System.Globalization
@{
    long? followUpPanelNode = null;
}

<div class="aside-inner">
   
    <nav class="sidebar" data-sidebar-anyclick-close="">
      
        <ul class="nav pt-sm">
            
            <li class="search-container">
                <i class="fa fa-search search-icon"></i>
                <input id="sidebar-menusearch" class="search-input" autocomplete="off" placeholder="@Localizer["MenuSearch"]..." type="text">
            </li>

            <li id="liSearch">
                <a href="#search" title="@Localizer["Search"]">
                    <em class="fa fa-search"></em>
                    <span>@Localizer["Search"]</span>
                </a>
            </li> 
            @{
                var currentLanguage = Intalio.Core.Language.EN;
                var language = CultureInfo.CurrentUICulture.Name.Replace("en-GB", "en");
                if (language == "ar")
                    currentLanguage = Intalio.Core.Language.AR;
                else if (language == "fr")
                    currentLanguage = Intalio.Core.Language.FR;

                var userId = Convert.ToInt64(User.Claims.First(t => t.Type == "Id").Value);
                var userPermission = Intalio.CTS.Core.Utility.IdentityHelperExtension.GetUserStrucurePermission(userId, currentLanguage, false);
                long? CurrentStructureId = Intalio.CTS.Core.Configuration.EnablePerStructure? new Intalio.CTS.Core.DAL.UserStructure().getLoggedInStrucureId(userId): null;
                var roleId = Intalio.CTS.Core.Configuration.EnablePerStructure ? userPermission.RoleId : Convert.ToInt32(User.Claims.First(t => t.Type == "RoleId").Value);


                var isHasFollowUpPermission = Intalio.CTS.Core.Utility.IdentityHelperExtension.IsHasFolloUpPermission(roleId,userId,CurrentStructureId);
                if (isHasFollowUpPermission)
                {
                    <li id="liTaskSearch">
                        <a href="#tasksearch" title="@Localizer["TaskSearch"]">
                            <em class="fa fa-search"></em>
                            <span>@Localizer["TaskSearch"]</span>
                        </a>
                    </li>
                }
                
                
                var nodes = Intalio.CTS.Core.Configuration.EnablePerStructure ? Intalio.Core.API.ManageNode.ListNodesByRoleIdUserIdStructureId(roleId, userId, (long)CurrentStructureId, (bool)userPermission.IsSecurityBreakedInheritance, currentLanguage) : Intalio.Core.API.ManageNode.ListNodesByRoleIdUserId(roleId, userId, currentLanguage);
                //var nodes = Intalio.Core.API.ManageNode.ListNodesByRoleIdUserId(roleId, userId, currentLanguage);
                var userNodes = Intalio.Core.API.ManageNode.ListUserNodesByUserIdandStructureId(roleId, userId, CurrentStructureId, currentLanguage);
                var basketnodeId = 0;
                var basketnode = Intalio.CTS.Core.API.ManageParameter.FindByKeyWord("basketnode");
                if (basketnode!=null)
                {
                    if (basketnode.Content != "" && basketnode.Content!=null)
                    {
                        basketnodeId = Int32.Parse(basketnode.Content);

                    }
                }
                bool EnableBasket = false;
                if (nodes.Count > 0)
                {
                     followUpPanelNode = nodes.FirstOrDefault(n => n.Name.ToLower() == @Localizer["InProgress"].ToLower() && n.Inherit.ToLower() == "FollowUp".ToLower())?.Id;
                    <li class="nav-heading">
                        <span>@Localizer["MyCorrespondences"]</span>
                    </li>
                    @Html.BuildNodes(nodes, currentLanguage)
                    ;
                    foreach (var node in nodes)
                    {
                        if (node.Id == basketnodeId)
                        {
                            EnableBasket = true;
                            break;
                        }
                    }
                }
                var MyBasket = new List<Intalio.CTS.Core.DAL.Basket>();
                var OtherBasket = new List<Intalio.CTS.Core.DAL.Basket>();
                if (EnableBasket)
                {
                    MyBasket = Intalio.CTS.Core.API.ManageBasket.ListMyBasket(userId);
                    OtherBasket = Intalio.CTS.Core.API.ManageBasket.ListOtherBasket(userId);
                }
                <li>
                    @if (MyBasket.Count >0)
                    {
                        <a id="MyBaskets" href="#nav-MyBaskets" title="@Localizer["MyBaskets"]" data-toggle="collapse">
                            <em class="fa fa-shopping-basket"></em>
                            <span>@Localizer["MyBaskets"]</span>
                        </a>
                    }
                    else
                    {
                        <a class="hidden" id="MyBaskets" href="#nav-MyBaskets" title="@Localizer["MyBaskets"]" data-toggle="collapse">
                            <em class="fa fa-shopping-basket"></em>
                            <span>@Localizer["MyBaskets"]</span>
                        </a>
                    }
                   
                    <ul id="nav-MyBaskets" class="nav sidebar-subnav collapse">
                        @foreach (var basket in MyBasket)
                        {
                            <li id="liBasket@(basket.Id)">
                                @{
                                    @if (currentLanguage.ToString() == "EN")
                                    {
                                        <a href="#basket/@basket.Id" title="@basket.Name">
                                            <span style="white-space: normal; word-break: break-word;">@basket.Name</span>
                                        </a>
                                    }
                                    else if (currentLanguage.ToString() == "FR")
                                    {
                                        var basketName = string.IsNullOrEmpty(basket.NameFr) ? basket.Name : basket.NameFr;
                                        <a href="#basket/@basket.Id" title="@basketName">
                                            <span style="white-space: normal; word-break: break-word;">@basketName</span>
                                        </a>
                                    }
                                    else if (currentLanguage.ToString() == "AR")
                                    {
                                        var basketName = string.IsNullOrEmpty(basket.NameAr) ? basket.Name : basket.NameAr;
                                        <a href="#basket/@basket.Id" title="@basketName">
                                            <span style="white-space: normal; word-break: break-word;">@basketName</span>
                                        </a>
                                    }




                                    // if (currentLanguage.ToString() == "EN")
                                    // {
                                    //     <a href="#basket/@basket.Id" title="@basket.Name">
                                    //         <span style="white-space: normal; word-break: break-word;">@basket.Name</span>
                                    //     </a>
                                    // }
                                    // else if (currentLanguage.ToString() == "FR")
                                    // {
                                    //     <a href="#basket/@basket.Id" title="@basket.NameFr">
                                    //         <span style="white-space: normal; word-break: break-word;">@basket.NameFr</span>
                                    //     </a>
                                    // }
                                    // else if (currentLanguage.ToString() == "AR")
                                    // {
                                    //     <a href="#basket/@basket.Id" title="@basket.NameAr">
                                    //         <span style="white-space: normal; word-break: break-word;">@basket.NameAr</span>
                                    //     </a>
                                    // }
                                }
                            </li>
                        }
                    </ul>
                </li>
                <li>
                    @if (OtherBasket.Count > 0)
                    {
                        <a id="OtherBaskets" href="#nav-OtherBaskets" title="@Localizer["OtherBaskets"]" data-toggle="collapse">
                            <em class="fa fa-shopping-basket"></em>
                            <span>@Localizer["OtherBaskets"]</span>
                        </a>
                    }
                    else
                    {
                        <a class="hidden" id="OtherBaskets" href="#nav-OtherBaskets" title="@Localizer["OtherBaskets"]" data-toggle="collapse">
                            <em class="fa fa-shopping-basket"></em>
                            <span>@Localizer["OtherBaskets"]</span>
                        </a>
                    }
                    <ul id="nav-OtherBaskets" class="nav sidebar-subnav collapse">
                        @foreach (var basket in OtherBasket)
                        {
                            <li id="liBasket@(basket.Id)">
                                @{
                                    if (currentLanguage.ToString() == "EN")
                                    {
                                        <a href="#basket/@basket.Id" title="@basket.Name">
                                            <span style="white-space: normal; word-break: break-word;">@basket.Name</span>
                                        </a>
                                    }
                                    else if (currentLanguage.ToString() == "FR")
                                    {
                                        var basketName = string.IsNullOrEmpty(basket.NameFr) ? basket.Name : basket.NameFr;
                                        <a href="#basket/@basket.Id" title="@basketName">
                                            <span style="white-space: normal; word-break: break-word;">@basketName</span>
                                        </a>
                                    }
                                    else if (currentLanguage.ToString() == "AR")
                                    {
                                        var basketName = string.IsNullOrEmpty(basket.NameAr) ? basket.Name : basket.NameAr;
                                        <a href="#basket/@basket.Id" title="@basketName">
                                            <span style="white-space: normal; word-break: break-word;">@basketName</span>
                                        </a>
                                    }
                                }
                            </li>
                        }
                    </ul>
                </li>



                <li>
                    @if (userNodes.Count > 0)
                    {
                        <a id="UserNodes" href="#nav-UserNodes" title="@Localizer["MyNodes"]" data-toggle="collapse">
                            <em class="fa fa-sitemap"></em>
                            <span>@Localizer["MyNodes"]</span>
                        </a>
                    }
                    else
                    {
                       
                        <a class="hidden" id="UserNodes" href="#userNode" title="@Localizer["MyNodes"]" data-toggle="collapse">
                            <em class="fa fa-shopping-basket"></em>
                            <span>@Localizer["MyNodes"]</span>
                        </a>
                    }


                    <ul id="nav-UserNodes" class="nav sidebar-subnav collapse">
                         @foreach (var node in userNodes)
                        {
                            <li id="liUserNode@(node.Id)">
                                @{
                                    <a href="#userNode/@node.Id" title="@node.Name">
                                        <span style="white-space: normal; word-break: break-word;">@node.Name</span>
                                    </a>
                                    
                                }
                            </li>
                        }
                    </ul>
                </li>
            }
            @{
                var delegationList = Intalio.CTS.Core.API.ManageDelegation.ListDelegatedToUser(userId);
                var classToggle = delegationList.Count != 1 ? "collapse" : "collapse in";
                foreach (var delegation in delegationList)
                {
                    nodes = Intalio.Core.API.ManageNode.ListNodesByRoleIdUserId(delegation.FromUserRoleId.Value, delegation.FromUserId, currentLanguage);
                    if (nodes.Count > 0)
                    {
                        <li class="delegation" data-id="@delegation.Id">
                            <a href="#<EMAIL>" title="@delegation.FromUser" data-toggle="collapse" class="delegationLi collapsed">
                                <em class="icon-user"></em>
                                <span>@delegation.FromUser @Localizer["Transfers"]</span>
                                 <em class="fa fa-angle-down arrow-menu"></em>
                            </a>
                            <ul id="<EMAIL>" class="nav sidebar-subnav @classToggle" >
                                @Html.BuildDelegationNodes(nodes, delegation, currentLanguage)
                            </ul>
                        </li>
                    }
                }
            }
      

           @*  <li id="liDelegation">
                <a href="#delegation" title="@Localizer["Delegation"]">
                    <em class="fa fa-user-circle"></em>
                    <span>@Localizer["Delegation"]</span>
                </a>
            </li>
            <li id="liToDoList">
                <a href="#todolist" title="@Localizer["ToDoList"]">
                    <em class="fa fa-list"></em>
                    <span>@Localizer["ToDoList"]</span>
                </a>
            </li> *@
        
            @{
                // int roleId = 0;
                // if (currentStructure != null)
                //     roleId = currentStructure.RoleId;
                // else
                // roleId = 
                //     Convert.ToInt32(User.Claims.First(t => t.Type == "RoleId").Value);

                // var DefaultStructureId = string.IsNullOrEmpty(User.Claims.First(t => t.Type == "StructureId").Value) ? null : (long?)Convert.ToInt64(User.Claims.First(t => t.Type == "StructureId").Value);
                var portalMenus = Intalio.CTS.Core.API.ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, CurrentStructureId)
                .Where(t => t.Type == (byte)Intalio.Core.TypeMenu.SideBar).ToList();
                var result = Html.BuildMenus(portalMenus, Intalio.Core.TypeMenu.SideBar, currentLanguage);
                if(!string.IsNullOrEmpty(result.Value))
                {
                    <li class="nav-heading">
                        <span>@Localizer["Menu"]</span>
                    </li>
                    @result;
                }
            }

        </ul>
    </nav>
</div>
<script type="text/javascript">
    window.FollowUpPanelNodeId = @followUpPanelNode
</script>

