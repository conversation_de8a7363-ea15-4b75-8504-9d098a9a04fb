﻿@using System.Globalization
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>@Intalio.CTS.Core.Configuration.ApplicationName</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    @if (IsSectionDefined("Styles"))
    {@RenderSection("Styles", required: false)}
    @{
        if (CultureInfo.CurrentUICulture.Name.Equals("ar"))
        {
            <link href="~/css/main-rtl.min.css?v=@Intalio.CTS.Core.Configuration.Version" rel="stylesheet" type="text/css" />
        }
        else
        {
            <link href="~/css/main.min.css?v=@Intalio.CTS.Core.Configuration.Version" rel="stylesheet" type="text/css" />
        }
    }
</head>
<body>
    <div class="wrapper">
        @RenderBody()
    </div>
    <script asp-append-version="true" src="~/js/core-js.min.js?v=@Intalio.CTS.Core.Configuration.Version"></script>
    @RenderSection("scripts", required: false)
</body>
</html>