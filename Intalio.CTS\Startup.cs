using Cronos;
using Hangfire;
using Hangfire.Oracle.Core;
using Hangfire.PostgreSql;
using HealthChecks.UI.Client;
using IdentityModel.Client;
using Intalio.Core;
using Intalio.Core.API;
using Intalio.Core.Extensions;
using Intalio.Core.Model;
using Intalio.Core.UI.Extensions;
using Intalio.Core.UI.Filters;
using Intalio.CTS.Core;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Service;
using Intalio.CTS.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ModelBinding.Binders;
using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Nest;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Security.Claims;
using System.Text.Json.Serialization;
using System.Text.Json;
using System.Threading.Tasks;
using UserModel = Intalio.CTS.Core.Model.UserModel;
using Azure.Core;
using Hangfire.Console;
using Serilog;
using System.IO;
using Intalio.CTS.Core.Utility;

namespace Intalio.CTS
{
    public class Startup
    {
        public Startup(IConfiguration configuration, IWebHostEnvironment env)
        {
            Configuration = configuration;
            CurrentEnvironment = env;

            IAM.Core.Configuration.DbConnectionString = Configuration.GetSection("ConnectionStrings").GetSection("IAMDbConnection").Value;
            IAM.Core.Configuration.DatabaseType = Configuration.GetValue("DatabaseType", IAM.Core.DatabaseType.MSSQL);

            Core.Configuration.G2GConnectionString = Configuration.GetSection("ConnectionStrings").GetSection("G2GDbConnection").Value;
            Core.Configuration.DbConnectionString = Configuration.GetSection("ConnectionStrings").GetSection("DbConnection").Value;
            Core.Configuration.DatabaseType = Configuration.GetValue("DatabaseType", DatabaseType.MSSQL);
            Core.Configuration.IdentityAuthorityUrl = Configuration.GetSection("IdentityServer").GetSection("Url").Value.TrimEnd('/');
            Core.Configuration.IdentityClientId = Configuration.GetSection("IdentityServer").GetSection("ClientId").Value;
            Core.Configuration.IdentityClientSecret = Configuration.GetSection("IdentityServer").GetSection("ClientSecret").Value;
            Core.Configuration.IsConnectionStringEncrypted = Configuration.GetValue<bool>("IsConnectionStringEncrypted");
            Core.Configuration.SameDomain = Configuration.GetValue("SameDomain", true);
            Core.Configuration.ApiScopeName = Configuration.GetValue("ApiScopeName", "IdentityServerApi");
            Core.Configuration.StorageServerUrl = Configuration.GetSection("StorageServerUrl").Value.TrimEnd('/');
            Core.Configuration.CrawlerServerUrls = Configuration.GetSection("Crawler") != default(IConfigurationSection) && !string.IsNullOrEmpty(Configuration.GetSection("Crawler").GetValue<string>("Urls", string.Empty)) ? Configuration.GetSection("Crawler").GetValue<string>("Urls").Split(Constants.SPLITTER).ToList() : new List<string>();
            Core.Configuration.CrawlerDefaultIndexName = Configuration.GetSection("Crawler") != default(IConfigurationSection) ? Configuration.GetSection("Crawler").GetValue<string>("DefaultIndexName", Constant.DefaultIndexName) : Constant.DefaultIndexName;
            Core.Configuration.CrawlerUserName = Configuration.GetSection("Crawler") != default(IConfigurationSection) ? Configuration.GetSection("Crawler").GetValue<string>("UserName", string.Empty) : string.Empty;
            Core.Configuration.CrawlerPassword = Configuration.GetSection("Crawler") != default(IConfigurationSection) ? Configuration.GetSection("Crawler").GetValue<string>("Password", string.Empty) : string.Empty;
            Core.Configuration.IsCrawlerPasswordEncrypted = Configuration.GetSection("Crawler") != default(IConfigurationSection) ? Configuration.GetSection("Crawler").GetValue<bool>("IsCrawlerPasswordEncrypted", false) : false;
            Core.Configuration.CrawlerBulkSize = Configuration.GetSection("Crawler") != default(IConfigurationSection) ? Configuration.GetSection("Crawler").GetValue<int>("BulkSize", 100) : 100;
            Core.Configuration.CrawlerMaxResultWindow = Configuration.GetSection("Crawler") != default(IConfigurationSection) ? Configuration.GetSection("Crawler").GetValue<int>("CrawlerMaxResultWindow", 10000) : 10000;
            Core.Configuration.ViewerUrl = Configuration.GetSection("ViewerUrl").Value.TrimEnd('/');
            CTS.Configuration.NumberOfReplicas = Configuration.GetSection("Crawler") != default(IConfigurationSection) ? Configuration.GetSection("Crawler").GetValue<int>("NumberOfReplicas", 1) : 1;
            CTS.Configuration.NumberOfShards = Configuration.GetSection("Crawler") != default(IConfigurationSection) ? Configuration.GetSection("Crawler").GetValue<int>("NumberOfShards", 1) : 1;
            CTS.Configuration.CrawlerExecutionTime = Configuration.GetSection("Crawler") != default(IConfigurationSection) ? Configuration.GetSection("Crawler").GetValue("ExecuteServiceEveryTimeInSeconds", 100) : 100;
            CTS.Configuration.CrawlerJobName = Configuration.GetSection("Crawler") != default(IConfigurationSection) ? Configuration.GetSection("Crawler").GetValue<string>("JobName", Constant.CrawlerJobName) : Constant.CrawlerJobName;
            CTS.Configuration.SchedulePollingInterval = Configuration.GetSection("Scheduler") != default(IConfigurationSection) ? Configuration.GetSection("Scheduler").GetValue<int>("SchedulePollingInterval", 100) < 15 ? 100 : Configuration.GetValue<int>("SchedulePollingInterval", 100) : 100;
            CTS.Configuration.StatsPollingInterval = Configuration.GetSection("Scheduler") != default(IConfigurationSection) ? Configuration.GetSection("Scheduler").GetValue<int>("StatsPollingInterval", 10) < 2 ? 10 : Configuration.GetValue<int>("StatsPollingInterval", 10) : 10;
            CTS.Configuration.DownloadWithAnnotation = Configuration.GetValue<bool>("DownloadWithAnnotation", false);
            CTS.Configuration.OCRServiceExecutionTime = Configuration.GetSection("OCR") != default(IConfigurationSection) ? Configuration.GetSection("OCR").GetValue("ExecuteServiceEveryTimeInSeconds", 7200) : 7200;
            CTS.Configuration.OverDue = Configuration.GetSection("OverDue").GetSection("CronExpression").Value;
            CTS.Configuration.IAMSyncServiceCron = Configuration.GetSection("Scheduler") != default(IConfigurationSection) ? Configuration.GetSection("Scheduler").GetValue<string>("IAMSyncServiceCron", "*/30 * * * *") : "*/30 * * * *";
            Core.Configuration.DSURL = Configuration.GetSection("DSURL").Value.TrimEnd('/');
            Core.Configuration.DMSURL = Configuration.GetSection("DMSURL").Value.TrimEnd('/');
            //check if PdfConvertorClientUrl section exists in configuration and it is not null or empty and assign it to Core.Configuration.PdfConvertorClientUrl other wise assign emtpy string

            Core.Configuration.PdfConvertorClientUrl = string.IsNullOrEmpty(Configuration.GetSection("PdfConvertorClientUrl").Value) ? string.Empty : Configuration.GetSection("PdfConvertorClientUrl").Value.TrimEnd('/') ;
            if (Core.Configuration.DatabaseType == DatabaseType.PostgreSQL)
            {
                AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
            }
            Core.Configuration.ViewerClientId = Configuration.GetSection("ViewerClientId").Value;
            CTS.Configuration.AllowedOrigins = Configuration.GetValue("AllowedOrigins", "*").Split(Constants.SPLITTER);
            Intalio.Core.Configuration.ConfigureSystem(GetServerOptions());
            Core.Configuration.ConfigureSystem();

            var config = new ConfigurationBuilder()
                            .SetBasePath(Directory.GetCurrentDirectory())
                            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                            .AddEnvironmentVariables().Build();
            var loggerConfiguration = new LoggerConfiguration().ReadFrom.Configuration(config);
            Log.Logger = loggerConfiguration.Enrich.FromLogContext().CreateLogger();
        }

        public IConfiguration Configuration { get; }

        private IWebHostEnvironment CurrentEnvironment { get; set; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddSingleton<Translation>();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddCors(options =>
            {
                options.AddPolicy("AllowAllOrigins", builder => builder.WithOrigins(CTS.Configuration.AllowedOrigins).AllowAnyMethod().AllowAnyHeader());
            });
            services.AddAuthentication(options =>
            {
                options.DefaultScheme = "Cookies";
                options.DefaultChallengeScheme = "oidc";
            }).AddCookie("Cookies", options =>
             {
                 options.ExpireTimeSpan = DateTime.Now.AddDays(7).TimeOfDay;
                 options.Cookie.Name = "Intalio.CTS";
                 options.Events = new CookieAuthenticationEvents
                 {
                     OnValidatePrincipal = context =>
                     {
                         //check to see if user is authenticated first  
                         if (context.Principal.Identity.IsAuthenticated)
                         {
                             //get the users tokens
                             var tokens = context.Properties.GetTokens();
                             var refreshToken = tokens.First(t => t.Name == "refresh_token");
                             var accessToken = tokens.First(t => t.Name == "access_token");
                             Core.Configuration.IdentityAccessToken = accessToken.Value;
                             var exp = tokens.First(t => t.Name == "expires_at");
                             var expires = DateTime.Parse(exp.Value);
                             Core.Configuration.WebsiteUrl = string.Format("{0}://{1}", context.Request.Scheme, context.Request.Host.Value);
                             if (expires < DateTime.Now)
                             {
                                 var client = new HttpClient();
                                 var discovery = client.GetDiscoveryDocumentAsync(new DiscoveryDocumentRequest { Address = Core.Configuration.IdentityAuthorityUrl, Policy = { RequireHttps = false } }).Result;
                                 if (discovery.IsError)
                                 {
                                     context.RejectPrincipal();
                                     return Task.CompletedTask;
                                 }
                                 var response = client.RequestRefreshTokenAsync(new RefreshTokenRequest
                                 {
                                     Address = discovery.TokenEndpoint,
                                     ClientId = Core.Configuration.IdentityClientId,
                                     ClientSecret = Core.Configuration.IdentityClientSecret,
                                     RefreshToken = refreshToken.Value
                                 }).Result;
                                 if (response.IsError)
                                 {
                                     context.RejectPrincipal();
                                     return Task.CompletedTask;
                                 }

                                 //set new token values
                                 refreshToken.Value = response.RefreshToken;
                                 accessToken.Value = response.AccessToken;
                                 //set new expiration date
                                 var newExpires = DateTime.UtcNow + TimeSpan.FromSeconds(response.ExpiresIn);
                                 exp.Value = newExpires.ToString("o", CultureInfo.InvariantCulture);
                                 //set tokens in auth properties 
                                 context.Properties.StoreTokens(tokens);
                                 //trigger context to renew cookie with new token values
                                 context.ShouldRenew = true;


                                 return Task.CompletedTask;
                             }

                             if (Core.Configuration.ViwerRoleId == 0 && !string.IsNullOrEmpty(Core.Configuration.ViewerClientId))
                             {
                                 if (new System.IdentityModel.Tokens.Jwt.JwtSecurityToken(accessToken.Value.ToString()).Claims.Any(t => t.Type == "Clients" && Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(t.Value).ClientId == Core.Configuration.ViewerClientId))
                                 {
                                     var viewerClient = Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(new System.IdentityModel.Tokens.Jwt.JwtSecurityToken(accessToken.Value.ToString()).Claims.First(t => t.Type == "Clients" && Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(t.Value).ClientId == Core.Configuration.ViewerClientId).Value);
                                     if (viewerClient != null)
                                         Core.Configuration.ViwerRoleId = viewerClient.RoleId;
                                 }
                             }
                             // UserContextAccessor.UserContext = GetUserContext(context.Principal.Identities.FirstOrDefault()?.Claims.ToList());
                         }
                         return Task.CompletedTask;
                     },
                     OnSignedIn = context =>
                     {
                         if (context.Principal.Identity.IsAuthenticated)
                         {
                             //get the users tokens
                             var tokens = context.Properties.GetTokens();
                             var refreshToken = tokens.First(t => t.Name == "refresh_token");
                             var accessToken = tokens.First(t => t.Name == "access_token");
                             
                             var identity = context.Principal.Identities.FirstOrDefault();
                             var StructureIds = identity.Claims.Any(t => t.Type == "StructureIds") && !string.IsNullOrEmpty(identity.Claims.First(t => t.Type == "StructureIds").Value) ? identity.Claims.First(t => t.Type == "StructureIds").Value.Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                             var UserId = Convert.ToInt64(identity.Claims.First(t => t.Type == "Id").Value);
                             
                             if (Core.Configuration.ViwerRoleId == 0 && !string.IsNullOrEmpty(Core.Configuration.ViewerClientId))
                             {
                                 if (new System.IdentityModel.Tokens.Jwt.JwtSecurityToken(accessToken.Value.ToString()).Claims.Any(t => t.Type == "Clients" && Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(t.Value).ClientId == Core.Configuration.ViewerClientId))
                                 {
                                     var viewerClient = Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(new System.IdentityModel.Tokens.Jwt.JwtSecurityToken(accessToken.Value.ToString()).Claims.First(t => t.Type == "Clients" && Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(t.Value).ClientId == Core.Configuration.ViewerClientId).Value);
                                     if (viewerClient != null)
                                         Core.Configuration.ViwerRoleId = viewerClient.RoleId;
                                     identity.AddClaim(new Claim("ViewerRoleId", viewerClient.RoleId.ToString()));

                                 }
                             }
                             Core.Configuration.userId = UserId;
                             UserModel user = Core.Utility.IdentityHelperExtension.GetUser(UserId, accessToken.Value, Intalio.Core.Language.EN);


                             var defaultstructure = Core.API.ManageUser.GetLoggedInStructure(UserId);
                             if (defaultstructure != null && defaultstructure != 0 && !user.Structures.Any(x => x.Id == defaultstructure.Value) && user.Structures.Count() > 0)
                             {
                                 Core.API.ManageUser.UpdateLoggedInStructure(UserId, user.Structures[0].Id, true);
                                 Core.API.ManageUser.UpdateLoggedInStructure(UserId, defaultstructure.Value, false);
                             }
                             if ((defaultstructure == null || defaultstructure == 0) && user != null && (user.DefaultStructureId != null || user.Structures.Count() > 0))
                             {
                                 Core.API.ManageUser.InsertLoggedInStructure(UserId, user.DefaultStructureId != null ? user.DefaultStructureId.Value : user.Structures[0].Id, true);
                             }

                             if (user != null)
                             {
                                 // if the strucure is the default strucure get from metadata attributes
                                 foreach (var structure in StructureIds)
                                 {
                                     UserStrucureModel strucurePrivacy = new UserStrucureModel();
                                     CacheUtility cache = new CacheUtility();
                                     if (structure == user.DefaultStructureId)
                                     {
                                         strucurePrivacy.IsStrucureSender = Convert.ToBoolean(user.Attributes.Where(x => x.Text == "StructureSender").FirstOrDefault().Value);
                                         strucurePrivacy.IsStrucureReceiver = Convert.ToBoolean(user.Attributes.Where(x => x.Text == "StructureReceiver").FirstOrDefault().Value);
                                         strucurePrivacy.PrivacyLevel = Convert.ToInt16(user.Attributes.Where(x => x.Text == "Privacy").FirstOrDefault().Value);
                                         strucurePrivacy.RoleId = Convert.ToInt32(user.Attributes.Where(x => x.Text == "StructureRole").FirstOrDefault().Value);
                                     }
                                     else
                                     {
                                         var currentStructure = user.Structures.Where(x => x.Id == structure).FirstOrDefault();
                                         strucurePrivacy.IsStrucureSender = Convert.ToBoolean(currentStructure.UserAttributes.Where(x => x.Text == "StructureSender").FirstOrDefault().Value);
                                         strucurePrivacy.IsStrucureReceiver = Convert.ToBoolean(currentStructure.UserAttributes.Where(x => x.Text == "StructureReceiver").FirstOrDefault().Value);
                                         strucurePrivacy.PrivacyLevel = Convert.ToInt16(currentStructure.UserAttributes.Where(x => x.Text == "Privacy").FirstOrDefault().Value);
                                         strucurePrivacy.RoleId = Convert.ToInt32(currentStructure.UserAttributes.Where(x => x.Text == "StructureRole").FirstOrDefault().Value);
                                     }
                                     strucurePrivacy.MenusBreakInheritance = ManageMenu.GetMenuBreakInheritanceByUserId(user.Id);
                                     strucurePrivacy.TabsBreakInheritance = ManageTab.GetTabBreakInheritanceByUserId(user.Id);
                                     strucurePrivacy.NodesBreakInheritance =Intalio.Core.API.ManageNode.GetNodeBreakInheritanceByUserId(user.Id);
                                     strucurePrivacy.ActionsBreakInheritance = ManageAction.GetActionBreakInheritanceByUserId(user.Id);
                                     strucurePrivacy.IsSecurityBreakedInheritance = Core.Configuration.EnablePerStructure ? Core.API.ManageUser.CheckBreakInheritanceInStructure((long)user.Id, structure) : Intalio.Core.API.ManageUser.CheckBreakInheritance((long)user.Id);


                                     cache.RemoveCache($"StrucurePrivacy-" + structure + "-" + UserId);
                                     cache.InsertCachedItem(strucurePrivacy, $"StrucurePrivacy-" + structure + "-" + UserId);
                                 }
                             }
                         }
                         return Task.CompletedTask;
                     }
                 };
             })
            .AddJwtBearer("Bearer", options =>
             {
                 options.Authority = Core.Configuration.IdentityAuthorityUrl;
                 options.RequireHttpsMetadata = false;
                 options.TokenValidationParameters = new TokenValidationParameters
                 {
                     ValidateAudience = true,
                     ValidAudiences = Core.Configuration.ApiScopeName.Split(Constants.SEPARATOR).ToList(),
                     AudienceValidator = (audiences, securityToken, validationParameters) =>
                     {
                         var retValue = false;
                         var clients = ((System.IdentityModel.Tokens.Jwt.JwtSecurityToken)securityToken).Claims.Where(t => t.Type == "Clients");
                         if (clients != null && clients.Count() > 0 && !string.IsNullOrEmpty(Core.Configuration.IdentityClientId)
                         && clients.Any(t => Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(t.Value).ClientId == Core.Configuration.IdentityClientId))
                         {
                             retValue = true;
                         }
                         return retValue;
                     }
                 };
                 options.Events = new Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerEvents
                 {
                     OnTokenValidated = e =>
                     {
                         var accessToken = e.SecurityToken as System.IdentityModel.Tokens.Jwt.JwtSecurityToken;
                         Core.Configuration.IdentityAccessToken = accessToken.RawData;

                        

                         ClaimsPrincipal p = e.Principal;
                         if (p != null)
                         {
                             var identity = p.Identities.FirstOrDefault();
                             if (identity != null)
                             {
                                 if (identity.Claims.Any(t => t.Type == ClaimTypes.Role))
                                 {
                                     identity.RemoveClaim(identity.Claims.First(t => t.Type == ClaimTypes.Role));
                                     var client = Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(identity.Claims.First(t => t.Type == "Clients" && Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(t.Value).ClientId == Core.Configuration.IdentityClientId).Value);
                                    
                                     if (Core.Configuration.ViwerRoleId == 0 && !string.IsNullOrEmpty(Core.Configuration.ViewerClientId))
                                     {
                                         if (identity.Claims.Any(t => t.Type == "Clients" && Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(t.Value).ClientId == Core.Configuration.ViewerClientId))
                                         {
                                             var viewerClient = Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(identity.Claims.First(t => t.Type == "Clients" && Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(t.Value).ClientId == Core.Configuration.ViewerClientId).Value);
                                             if (viewerClient != null)
                                                 Core.Configuration.ViwerRoleId = viewerClient.RoleId;
                                         }
                                     }

                                     identity.AddClaim(new Claim(ClaimTypes.Role, client.Role));
                                     identity.AddClaim(new Claim("RoleId", client.RoleId.ToString()));
                                 }
                                 if (identity.Claims.Any(t => t.Type == Core.Configuration.UserPrivacy))
                                 {
                                     if (identity.Claims.Any(t => t.Type == "PrivacyLevel"))
                                     {
                                         identity.RemoveClaim(identity.Claims.First(t => t.Type == "PrivacyLevel"));
                                     }
                                     var userPrivacyLevel = identity.Claims.Any(t => t.Type == Core.Configuration.UserPrivacy) && !string.IsNullOrEmpty(identity.Claims.First(t => t.Type == Core.Configuration.UserPrivacy).Value) ? ManagePrivacy.CheckPrivacyLevel(Convert.ToInt16(identity.Claims.First(t => t.Type == Core.Configuration.UserPrivacy).Value)) : ManagePrivacy.GetLowestLevel();
                                     identity.AddClaim(new Claim("PrivacyLevel", userPrivacyLevel.ToString()));
                                 }
                                 //UserContextAccessor.UserContext = GetUserContext(identity.Claims.ToList());
                             }
                         }
                         return Task.CompletedTask;
                     }
                 };
             })
            .AddOpenIdConnect("oidc", options =>
                {
                    options.Authority = Core.Configuration.IdentityAuthorityUrl;
                    options.RequireHttpsMetadata = false;

                    options.ClientId = Core.Configuration.IdentityClientId;
                    options.ClientSecret = Core.Configuration.IdentityClientSecret;//not mandatory
                    options.ResponseType = "code";
                    options.Scope.Clear();
                    options.Scope.Add("openid");
                    options.Scope.Add("IdentityServerApi");
                    options.Scope.Add("offline_access");

                    HttpClientHandler handler = new HttpClientHandler();
                    handler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;
                    options.BackchannelHttpHandler = handler;


                    if (!string.IsNullOrEmpty(Core.Configuration.ApiScopeName))
                    {
                        options.Scope.Add(Core.Configuration.ApiScopeName);
                    }
                    options.GetClaimsFromUserInfoEndpoint = false;
                    options.SaveTokens = true;
                    //Added to fix http issue
                    if (Core.Configuration.SameDomain)
                    {
                        options.CorrelationCookie.SameSite = SameSiteMode.Lax;
                        options.NonceCookie.SameSite = SameSiteMode.Lax;
                    }
                    options.Events = new OpenIdConnectEvents
                    {
                        OnTicketReceived = e =>
                        {
                            var tokens = e.Properties.GetTokens();
                            var accessToken = tokens.First(t => t.Name == "access_token");
                           
                            if (Core.Configuration.ViwerRoleId == 0 && !string.IsNullOrEmpty(Core.Configuration.ViewerClientId))
                            {
                                if (new System.IdentityModel.Tokens.Jwt.JwtSecurityToken(accessToken.Value.ToString()).Claims.Any(t => t.Type == "Clients" && Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(t.Value).ClientId == Core.Configuration.ViewerClientId))
                                {

                                    var viewerClient = Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(new System.IdentityModel.Tokens.Jwt.JwtSecurityToken(accessToken.Value.ToString()).Claims.First(t => t.Type == "Clients" && Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(t.Value).ClientId == Core.Configuration.ViewerClientId).Value);
                                    if (viewerClient != null)
                                        Core.Configuration.ViwerRoleId = viewerClient.RoleId;
                                }
                            }
                            ClaimsPrincipal p = e.Principal;
                            var identity = p.Identities.FirstOrDefault();
                            Intalio.Core.API.ManageUser.Provision(new UserViewModel
                            {
                                Id = Convert.ToInt32(identity.Claims.First(t => t.Type == "Id").Value),
                                Firstname = identity.Claims.First(t => t.Type == "FirstName").Value,
                                Lastname = identity.Claims.First(t => t.Type == "LastName").Value,
                                RoleId = Convert.ToInt16(identity.Claims.First(t => t.Type == "ApplicationRoleId").Value),
                                Active = true
                            });
                            var structureId = identity.Claims.First(t => t.Type == "StructureId").Value;
                            if (!string.IsNullOrEmpty(structureId))
                            {
                                var structureIdsForProvision = new List<long>();
                                structureIdsForProvision.Add(Convert.ToInt64(structureId));
                                ManageStructure.Provision(structureIdsForProvision);
                            }
                            //ManageStructure.Provision();
                            List<Claim> claimsTobeRemoved = new List<Claim>();
                            foreach (var claim in identity.Claims)
                            {
                                if (new string[] { "ApplicationRoleId", "ApplicationRolePrivileges", "Groups", "MiddleName", "given_name", "family_name", "email" }.Contains(claim.Type))
                                {
                                    claimsTobeRemoved.Add(claim);
                                }
                            }
                            //var viewerClient = Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(identity.Claims.First(t => t.Type == "Clients" && Intalio.Core.Helper.DeserializeJson<UserApplicationRoleModel>(t.Value).ClientId == Core.Configuration.ViewerClientId).Value);

                            
                            //identity.AddClaim(new Claim("ViewerRoleId", viewerClient.RoleId.ToString()));

                            identity.AddClaim(new Claim("RoleId", Convert.ToString(identity.Claims.First(t => t.Type == "ApplicationRoleId").Value)));
                            claimsTobeRemoved.ForEach(t => identity.RemoveClaim(t));

                            var userPrivacyLevel = identity.Claims.Any(t => t.Type == Core.Configuration.UserPrivacy) && !string.IsNullOrEmpty(identity.Claims.First(t => t.Type == Core.Configuration.UserPrivacy).Value) ? ManagePrivacy.CheckPrivacyLevel(Convert.ToInt16(identity.Claims.First(t => t.Type == Core.Configuration.UserPrivacy).Value)) : ManagePrivacy.GetLowestLevel();
                            identity.AddClaim(new Claim("PrivacyLevel", userPrivacyLevel.ToString()));



                            
                            return Task.CompletedTask;
                        },
                        OnRemoteFailure = e =>
                        {
                            e.HandleResponse();
                            e.Response.Redirect("/");
                            return Task.FromResult(0);
                        },
                        
                    };
                });

            services.Configure<RequestLocalizationOptions>(opts =>
            {
                var supportedCultures = new List<CultureInfo>
                {
                    new CultureInfo("en-GB"),
                    new CultureInfo("ar"),
                    new CultureInfo("fr")
                };
                
                var langs = new string[] {"en", "en", "ar", "fr"};
                int langIndex = (int)CTS.Core.Configuration.DefaultApplicationLanguage;
                opts.DefaultRequestCulture = new RequestCulture("en-GB", langs[langIndex]);

                opts.SupportedCultures = new List<CultureInfo>
                {
                    new CultureInfo("en-GB")
                };
                opts.SupportedUICultures = supportedCultures;
            });
            services.Configure<StaticFileOptions>(o =>
            {
                var provider = new FileExtensionContentTypeProvider();
                // Repeat this for each extension you need to map.
                provider.Mappings[".exe"] = "application/octet-stream";
                o.ContentTypeProvider = provider;
            });
            //services.Configure<IISServerOptions>(options =>
            //{
            //    options.MaxRequestBodySize = **********;
            //});
            // dotnet ef migrations add InitialCreate --project Intalio.CTS --context NpgsqlContext --output-dir Migrations/NpgsqlMigrations
            //services.AddDbContext<Core.DAL.NpgsqlContext>(options => options.UseNpgsql("User ID=postgres;Password=P@$$w0rd;Server=localhost;Port=5432;Database=CTS;Integrated Security=true;Pooling=true;", b => b.MigrationsAssembly("Intalio.CTS")));
            //dotnet ef migrations add InitialCreate --project Intalio.CTS --context CTSContext --output-dir Migrations/SqlServerMigrations
            //services.AddDbContext<Core.DAL.CTSContext>(options =>options.UseSqlServer(Core.Configuration.DbConnectionString, b => b.MigrationsAssembly("Intalio.CTS")));
            //dotnet ef migrations add InitialCreate --project Intalio.CTS --context OracleContext --output-dir Migrations/OracleMigrations
            //services.AddDbContext<Core.DAL.OracleContext>(options =>options.UseOracle(Core.Configuration.DbConnectionString, b => b.MigrationsAssembly("Intalio.CTS")));

            //dotnet ef migrations add UpdateV3.2.0 --startup-project Intalio.CTS --project Intalio.CTS.Core --context CTSContext --output-dir Migrations/SqlServerMigrations
            //dotnet ef migrations add UpdateV3.2.0 --startup-project Intalio.CTS --project Intalio.CTS.Core --context NpgsqlContext --output-dir Migrations/NpgsqlMigrations
            //dotnet ef migrations add UpdateV3.2.0 --startup-project Intalio.CTS --project Intalio.CTS.Core --context OracleContext --output-dir Migrations/OracleMigrations
            services.AddSingleton<IUserIdProvider, CommunicationUserIdProvider>();
            services.AddSignalR();

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v4.0.0", new OpenApiInfo { Title = "Intalio Correspondence Tracking System", Version = string.Format("v{0}", Core.Configuration.Version) });
                c.CustomSchemaIds(i => i.FullName);
                c.DocInclusionPredicate((docName, apiDesc) =>
                {
                    if (apiDesc.HttpMethod == null) return false;
                    return true;
                });
                //c.DocumentFilter<SwaggerFilter>();
                c.IncludeXmlComments(System.IO.Path.Combine(System.AppContext.BaseDirectory, "Intalio.CTS.xml"));
                c.IncludeXmlComments(System.IO.Path.Combine(System.AppContext.BaseDirectory, "Intalio.Core.UI.xml"));
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Name = "Authorization",
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer",
                    BearerFormat = "JWT",
                    In = ParameterLocation.Header,
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                          new OpenApiSecurityScheme
                            {
                                Reference = new OpenApiReference
                                {
                                    Type = ReferenceType.SecurityScheme,
                                    Id = "Bearer"
                                }
                            },
                            new string[] {}
                    }
                });
            });
            var dbConnectionString = Core.Configuration.DbConnectionString;
            //if (Core.Configuration.CrawlerServerUrls.Count > 0 || Core.Configuration.EnableOCR )
            //{
                services.AddHangfire(configuration =>
                {
                    configuration.SetDataCompatibilityLevel(CompatibilityLevel.Version_170).UseSimpleAssemblyNameTypeSerializer().UseRecommendedSerializerSettings();
                    configuration.UseConsole();
                    switch (Core.Configuration.DatabaseType)
                    {
                        case DatabaseType.MSSQL:
                            configuration.UseSqlServerStorage(dbConnectionString, new Hangfire.SqlServer.SqlServerStorageOptions
                            {
                                QueuePollInterval = TimeSpan.FromSeconds(CTS.Configuration.SchedulePollingInterval)
                            });
                            break;
                        case DatabaseType.PostgreSQL:
                            configuration.UsePostgreSqlStorage(dbConnectionString, new PostgreSqlStorageOptions { QueuePollInterval = TimeSpan.FromSeconds(CTS.Configuration.SchedulePollingInterval) });
                            break;
                        case DatabaseType.Oracle:
                            GlobalConfiguration.Configuration.UseStorage(new OracleStorage(dbConnectionString, new OracleStorageOptions { QueuePollInterval = TimeSpan.FromSeconds(CTS.Configuration.SchedulePollingInterval) }));
                            break;
                        default:
                            configuration.UseSqlServerStorage(dbConnectionString, new Hangfire.SqlServer.SqlServerStorageOptions
                            {
                                QueuePollInterval = TimeSpan.FromSeconds(CTS.Configuration.SchedulePollingInterval)
                            });
                            break;
                    }
                });
                // Add the processing server as IHostedService
                services.AddHangfireServer((config) => { config.SchedulePollingInterval = TimeSpan.FromSeconds(CTS.Configuration.SchedulePollingInterval); });
            //}
            // Add the HealthCheck service
            switch (Core.Configuration.DatabaseType)
            {
                case DatabaseType.MSSQL:
                    services.AddHealthChecks().AddSqlServer(dbConnectionString, name: "Database");
                    break;
                case DatabaseType.PostgreSQL:
                    services.AddHealthChecks().AddNpgSql(dbConnectionString, name: "Database");
                    break;
                case DatabaseType.Oracle:
                    services.AddHealthChecks().AddOracle(dbConnectionString, name: "Database");
                    break;
                default:
                    services.AddHealthChecks().AddSqlServer(dbConnectionString, name: "Database");
                    break;
            }
            services.AddHealthChecks().AddSmtpHealthCheck(setup =>
            {
                setup.Host = Core.Configuration.SmtpServer;
                setup.Port = Core.Configuration.SmtpPort;
                if (!string.IsNullOrEmpty(Core.Configuration.SmtpUserName) && !string.IsNullOrEmpty(Core.Configuration.SmtpPassword))
                {
                    setup.LoginWith(Core.Configuration.SmtpUserName, Core.Configuration.SmtpPassword);
                }
            }, name: "SMTP").AddUrlGroup(new Uri($"{Core.Configuration.StorageServerUrl.TrimEnd('/')}/hc"), name: "Storage");
            if (Core.Configuration.CrawlerServerUrls.Count > 0)
            {
                foreach (var url in Core.Configuration.CrawlerServerUrls)
                {
                    Uri uri = new Uri(url.TrimEnd('/'));
                    services.AddHealthChecks().AddElasticsearch(setup =>
                    {
                        setup.UseBasicAuthentication(Core.Configuration.CrawlerUserName, Core.Configuration.CrawlerPassword);
                        setup.UseServer(url.TrimEnd('/'));
                    }, name: $"Crawler_{uri.Host}_{uri.Port}");
                }
                services.AddHealthChecks().AddHangfire(options => { }, "Job Scheduler");
            }

            IMvcBuilder builder = services.AddRazorPages();
            services.AddControllers(options => options.ModelBinderProviders.RemoveType<DateTimeModelBinderProvider>());
            services.AddControllers().AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
                options.JsonSerializerOptions.WriteIndented = true;
            });


#if DEBUG
            builder.AddRazorRuntimeCompilation();//Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilitaion package
#endif
            services.AddCoreServices(GetServerOptions(true), false);
            services.AddCoreViews();
            CoreDelegates.InitDelegate();
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Error");
            }
            app.UseForwardedHeaders(new ForwardedHeadersOptions
            {
                ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
            });
            app.UseStatusCodePages(async context =>
            {
                if (context.HttpContext.Response.StatusCode == StatusCodes.Status404NotFound)
                {
                    context.HttpContext.Response.Redirect("/Home");
                }
                await Task.CompletedTask;
            });
            app.UseStaticFiles();
            app.UseRouting();

            app.UseSwagger();
            app.UseSwaggerUI();

            var cachePeriod = env.IsDevelopment() ? "600" : "********";
            app.UseStaticFiles(new StaticFileOptions
            {
                OnPrepareResponse = ctx =>
                {
                    ctx.Context.Response.Headers.Append("Cache-Control", $"public, max-age={cachePeriod}");
                }
            });
            app.UseForwardedHeaders(new ForwardedHeadersOptions
            {
                ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
            });
            var options = app.ApplicationServices.GetService<IOptions<RequestLocalizationOptions>>();
            app.UseRequestLocalization(options.Value);
            app.UseCookiePolicy(new CookiePolicyOptions
            {
                MinimumSameSitePolicy = SameSiteMode.None
            });
            app.UseCors("AllowAllOrigins");//load before authentication
            app.UseAuthentication();
            app.UseAuthorization();
            app.Use(async (context, next) =>
            {
                if (context.User.Identity.IsAuthenticated)
                {
                    UserContextAccessor.UserContext = GetUserContext(context.User.Claims.ToList());
                }
                await next();
            });
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapDefaultControllerRoute().RequireAuthorization();
                endpoints.MapHub<CommunicationHub>("/notificationhub");
            });
            //if (Core.Configuration.CrawlerServerUrls.Count > 0 || Core.Configuration.EnableOCR )
            //{
                int executionTime = 0;
                string cronExpression = string.Empty;
                if (Core.Configuration.CrawlerServerUrls.Count > 0)
                {
                    executionTime = CTS.Configuration.CrawlerExecutionTime;
                    cronExpression = GenerateCronExpressionFromSeconds(Convert.ToDouble(executionTime));
                   CrawlingService.RegisterCrawlingIndexingService(CTS.Configuration.CrawlerJobName, cronExpression);
                }
                if (Core.Configuration.EnableOCR)
                {
                    executionTime = CTS.Configuration.OCRServiceExecutionTime;
                    cronExpression = GenerateCronExpressionFromSeconds(Convert.ToDouble(executionTime));
                    OCRIndexingService.RegisterOCRIndexingService(Constant.OCRServiceJobName, cronExpression);
                }
                if(Core.Configuration.EnableEmailNotification )
                {
                    OverDueService.CheckOverDueServise();

                }
                IAMSyncService.RegisterIamService(Constant.IAMServiceJobName, CTS.Configuration.IAMSyncServiceCron);
                var hangFireAuth = new DashboardOptions
                {
                    Authorization = new[] { new HangFireAuthorizationFilter() },
                    AppPath = "/",
                    DashboardTitle = "Intalio CTS Scheduler",
                    DisplayStorageConnectionString = false,
                    StatsPollingInterval = Convert.ToInt32(TimeSpan.FromSeconds(CTS.Configuration.StatsPollingInterval).TotalMilliseconds)
                };
                app.UseHangfireDashboard("/SchedulerDashboard", options: hangFireAuth);
            //}
            // HealthCheck middleware
            app.UseHealthChecks("/hc", new HealthCheckOptions
            {
                Predicate = _ => true,
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });
        }

        #region Custom Private Methods

        private UserContext GetUserContext(List<Claim> claims)
        {
            UserContext retValue = null;
            if (!claims.IsNullOrEmpty())
            {
                var language = CultureInfo.CurrentUICulture.Name.Replace("en-GB", "en");
                var currentLanguage = Intalio.Core.Language.EN;
                if (language == "ar")
                {
                    currentLanguage = Intalio.Core.Language.AR;
                }
                else if (language == "fr")
                {
                    currentLanguage = Intalio.Core.Language.FR;
                }
                var user = Convert.ToInt64(claims.First(t => t.Type == "Id").Value);
                Core.Configuration.userId = user;
                retValue = new UserContext
                {
                    Id = Convert.ToInt64(claims.First(t => t.Type == "Id").Value),
                    FullName = claims.First(t => t.Type == "DisplayName").Value,
                    Email = claims.First(t => t.Type == "Email").Value,
                    Role = claims.First(t => t.Type == ClaimTypes.Role).Value,
                    RoleId = Core.Configuration.EnablePerStructure ? Convert.ToInt16(Core.Utility.IdentityHelperExtension.GetUserStrucurePermission(user, currentLanguage).RoleId) : Convert.ToInt16(claims.First(t => t.Type == "RoleId").Value),
                    StructureIds = claims.Any(t => t.Type == "StructureIds") && !string.IsNullOrEmpty(claims.First(t => t.Type == "StructureIds").Value) ? claims.First(t => t.Type == "StructureIds").Value.Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>(),
                    DefaultStructureId = string.IsNullOrEmpty(claims.First(t => t.Type == "StructureId").Value) ? null : (long?)Convert.ToInt64(claims.First(t => t.Type == "StructureId").Value),
                    ManagerId = claims.Any(t => t.Type == "ManagerId") && !string.IsNullOrEmpty(claims.First(t => t.Type == "ManagerId").Value) ? (long?)Convert.ToInt64(claims.First(t => t.Type == "ManagerId").Value) : null,
                    GroupIds = claims.Any(t => t.Type == "GroupIds") && !string.IsNullOrEmpty(claims.First(t => t.Type == "GroupIds").Value) ? claims.First(t => t.Type == "GroupIds").Value.Split(Constants.SEPARATOR).Select(short.Parse).ToList() : new List<short>(),
                    PrivacyLevel = claims.Any(t => t.Type == Core.Configuration.UserPrivacy) && !string.IsNullOrEmpty(claims.First(t => t.Type == Core.Configuration.UserPrivacy).Value) ? Convert.ToInt16(claims.First(t => t.Type == Core.Configuration.UserPrivacy).Value) : default,
                    IsStructureReceiver = Core.Configuration.EnablePerStructure ? Core.Utility.IdentityHelperExtension.GetUserStrucurePermission(user, currentLanguage).IsStrucureReceiver :claims.Any(t => t.Type == Core.Configuration.UserStructureReceiver) && !string.IsNullOrEmpty(claims.First(t => t.Type == Core.Configuration.UserStructureReceiver).Value) ? Convert.ToBoolean(claims.First(t => t.Type == Core.Configuration.UserStructureReceiver).Value) : false,
                    IsStructureSender = Core.Configuration.EnablePerStructure ? Core.Utility.IdentityHelperExtension.GetUserStrucurePermission(user, currentLanguage).IsStrucureSender : claims.Any(t => t.Type == Core.Configuration.UserStructureSender) && !string.IsNullOrEmpty(claims.First(t => t.Type == Core.Configuration.UserStructureSender).Value) ? Convert.ToBoolean(claims.First(t => t.Type == Core.Configuration.UserStructureSender).Value) : false,
                    Attributes = claims.Select(t => new TextValue { Text = t.Type, Value = t.Value }).ToList(),
                    StructureId= Core.Configuration.EnablePerStructure ? Core.Utility.IdentityHelperExtension.getCurrentLoggedInStructure(user) != null ? (long)Core.Utility.IdentityHelperExtension.getCurrentLoggedInStructure(user) : Convert.ToInt64(claims.First(t => t.Type == "StructureId").Value) : Convert.ToInt64(claims.First(t => t.Type == "StructureId").Value)
                };
            }
            return retValue;
        }

        private string GenerateCronExpressionFromSeconds(double executionTime)
        {
            //check cron expression here : https://crontab.guru/
            //executionTime is in minutes
            string cronExpression = "*/1 * * * *";//every 1 minute

            //seconds
            if (executionTime >= 1 && executionTime < 60)
            {
                cronExpression = "*/" + executionTime + " */1 * ? * *";//every 'x' seconds
            }
            //minutes
            else if (executionTime >= 60 && executionTime < 3600 /*60 x 60 = 3600*/)
            {
                double minutes = executionTime / 60;
                minutes = Math.Round(minutes);
                if (minutes == 60)
                {
                    cronExpression = "0 */1 * * *";//every 1 hour
                }
                else
                {
                    cronExpression = "*/" + minutes + " * * * *";//every 'x' minutes
                }
            }
            //hours
            else if (executionTime >= 3600 && executionTime < 216000 /*3600 x 60 = 216000*/)
            {
                double hours = executionTime / 1440;
                hours = Math.Round(hours);
                if (hours == 60)
                {
                    cronExpression = "0 0 */1 * *";//every 1 day
                }
                else
                {
                    cronExpression = "0 */" + hours + " * * *";//every 'x' hours
                }
            }
            //days
            else if (executionTime >= 216000 && executionTime < 6048000 /* 216000 x 28 = 6048000*/ )
            {
                double days = executionTime / 1440;
                days = Math.Round(days);
                if (days == 60)
                {
                    cronExpression = "0 0 1 */1 *";//every 1 month
                }
                else
                {
                    cronExpression = "0 0 */" + days + " * *";//every 'x' days
                }
            }
            else if (executionTime >= 6048000)
            {
                cronExpression = "0 0 1 */1 *";//every 1 month
            }

            return cronExpression;
        }

        private ServerOptions GetServerOptions( bool withConfiguration = false)
        {
            var coreOptions = new ServerOptions
            {
                DbConnectionString = Core.Configuration.DbConnectionString,
                DatabaseType = Core.Configuration.DatabaseType,
                EnableCaching = withConfiguration ? Core.Configuration.EnableCaching : false,
                EnableEmailNotification = withConfiguration ? Core.Configuration.EnableEmailNotification : false,
                IdentityAuthorityUrl = Core.Configuration.IdentityAuthorityUrl,
                CalendarType = withConfiguration ? Core.Configuration.CalendarType : CalendarType.None,
                PdfConvertorClientUrl = Core.Configuration.PdfConvertorClientUrl,
                SmtpSettings = new SmtpSettings
                {
                    SmtpServer = withConfiguration ? Core.Configuration.SmtpServer : string.Empty,
                    SystemEmail = withConfiguration ? Core.Configuration.SmtpSystemEmail : string.Empty,
                    UserName = withConfiguration ? Core.Configuration.SmtpUserName : string.Empty,
                    Password = withConfiguration ? Core.Configuration.SmtpPassword : string.Empty,
                    Port = withConfiguration ? Core.Configuration.SmtpPort : default,
                    EnableSSL = withConfiguration ? Core.Configuration.SmtpEnableSSL : false
                },
                Features = new Features
                {
                    Dictionary = new OptionalFeature { Enabled = true },
                    SecurityMatrix = new SecurityMatrixModule { Enabled = true },
                    Parameter = new OptionalFeature { Enabled = true },
                    ApplicationServers = new OptionalFeature { Enabled = true },
                    NotificationTemplate = new OptionalFeature { Enabled = true },
                    CustomizationFile = new OptionalFeature { Enabled = true },
                    Delegation = new OptionalFeature { Enabled = false },
                    Purpose = new OptionalFeature { Enabled = true },
                    Status = new OptionalFeature { Enabled = true },
                    ToDoList = new ToDoListModule { Enabled = true },
                    Lookup = new LookupModule { Enabled = true },
                    Classification = new OptionalFeature { Enabled = true },
                    Priority = new OptionalFeature { Enabled = true },
                    Privacy = new OptionalFeature { Enabled = true },
                    Importance = new OptionalFeature { Enabled = true },
                    SearchAssignedSecurity = new OptionalFeature { Enabled = true },
                    SendingRule = new OptionalFeature { Enabled = true },
                    DocumentType = new OptionalFeature { Enabled = true },
                    Tags = new OptionalFeature { Enabled = true },
                    Ocr = new OcrModule { Enabled = true },
                    Audit = new OptionalFeature { Enabled = true }
                }
            };
            return coreOptions;
        }

        #endregion
    }
}
