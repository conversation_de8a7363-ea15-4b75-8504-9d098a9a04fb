﻿IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'DocumentNotRegistered')
Begin
INSERT INTO [TranslatorDictionary] ([Keyword],[EN],[FR],[AR],[IsSystem]) 
VALUES (N'DocumentNotRegistered',N'Document Not Registered',N'Document non enregistré',N'الوثيقة غير مسجلة',1)
END


IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'DeleteEventConfirmation')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'DeleteEventConfirmation', 
            N'Are you sure you want to delete this event?',       
            N'Êtes-vous sûr de vouloir supprimer cet événement ?',       
            N'هل أنت متأكد أنك تريد حذف هذا الحدث؟',  
            1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'CannotBeRecalled')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('CannotBeRecalled', N'This correspondence cannot be recalled',N'لا يمكن استدعاء هذه المراسلات', N'Cette correspondance ne peut être rappelée', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'PersonalInbox')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('PersonalInbox', N'Personal Inbox',N'البريد الشخصي', N'Courrier personnel', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'InternalPersonalInbox')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('InternalPersonalInbox', N'Personal Inbox - Internal',N'البريد الشخصي - الداخلي', N'Courrier personnel - Interne', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'IncomingPersonalInbox')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('IncomingPersonalInbox', N'Personal Inbox - Incoming',N'البريد الشخصي - الوارد', N'Courrier personnel - Entrant', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'OutgoingPersonalInbox')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('OutgoingPersonalInbox', N'Personal Inbox - Outgoing',N'البريد الشخصي - الصادر', N'Courrier personnel - Sortant', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'DepartmentInbox')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('DepartmentInbox', N'Department Inbox',N'بريد الإدارة', N'Boîte de réception du département', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'IncomingDepartmentInbox')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('IncomingDepartmentInbox', N'Incoming Department Inbox',N'بريد الإدارة - الوارد', N'Boîte de réception du département - Entrant', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'InternalDepartmentInbox')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('InternalDepartmentInbox', N'Internal Department Inbox',N'بريد الإدارة - الداخلي', N'Boîte de réception du département - Interne', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'OutgoingDepartmentInbox')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('OutgoingDepartmentInbox', N'Outgoing Department Inbox',N'بريد الإدارة - الصادر', N'Boîte de réception du département - Sortant', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'SentOutgoing')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('SentOutgoing', N'Sent - Outgoing',N'البريد المرسل - الصادر', N'Courrier envoyé - Sortant', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'SentInternal')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('SentInternal', N'Sent - Internal',N'البريد المرسل - الداخلي', N'Courrier envoyé - Interne', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'SentIncoming')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('SentIncoming', N'Sent - Incoming',N'البريد المرسل - الوارد', N'Courrier envoyé - Entrant', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'AcceptReject')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('AcceptReject', N'Accept or Reject',N'استلام او ارجاع', N'Accepter ou rejeter', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'ReadyForTransferReport')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('ReadyForTransferReport', N'Ready For Transfer',N'جاهز للإرسال', N'Le Seigneur des anneaux', 1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'ReadyToExportReport')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('ReadyToExportReport', N'Ready To Export',N'جاهز للتصدير', N'Le monde des ténèbres', 1)
END
-----------------------------------------------------------------------------------------------
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword=N'reportRejectedDocuments')
BEGIN
	INSERT INTO TranslatorDictionary (Keyword, EN, AR, FR, IsSystem)
	VALUES ('reportRejectedDocuments', N'Report Returned Documents',N'تقرير البريد المعاد', N'Rapport sur les documents retournés', 1)
END
ELSE
BEGIN
UPDATE TranslatorDictionary
SET 
    EN = 'Report Returned Documents',
    AR =	N'تقرير البريد المعاد',
    FR = N'Rapport sur les documents retournés'
WHERE 
    Keyword = 'reportRejectedDocuments';
END