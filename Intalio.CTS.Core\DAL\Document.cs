﻿using Intalio.Core;
using Intalio.Core.Interfaces;
using Intalio.Core.Model;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Text.Json;
using System.Text;
using System.Threading.Tasks;
using DelegationModel = Intalio.CTS.Core.Model.DelegationModel;

namespace Intalio.CTS.Core.DAL
{
    public partial class Document : IDbObject<Document>, IDisposable
    {
        #region Private Fields

        private CTSContext _ctx;

        private const string MSSQL = @"Exec usp_GetCountOfRelevantTags '{0}','{1}'";
        private const string Oracle = @"SELECT * from fn_GetCountOfRelevantTags('{0}','{1}')";
        private const string Postgresql = @"SELECT * from public.fn_GetCountOfRelevantTags('{0}','{1}');";

        private const string MSSQL_LISTOCRPENDINGS = @"Exec usp_ListOCRPendings {0},{1}";
        private const string Oracle_LISTOCRPENDINGS = @"SELECT * from fn_ListOCRPendings('{0}','{1}')";
        private const string Postgresql_LISTOCRPENDINGS = @"SELECT * from public.fn_ListOCRPendings('{0}','{1}');";

        #endregion

        #region Ctor

        public Document()
        {
            ActivityLog = new HashSet<ActivityLog>();
            Attachment = new HashSet<Attachment>();
            DocumentCarbonCopy = new HashSet<DocumentCarbonCopy>();
            DocumentReceiverEntity = new HashSet<DocumentReceiverEntity>();
            LinkedDocumentDocument = new HashSet<LinkedDocument>();
            LinkedDocumentLinkedDocumentNavigation = new HashSet<LinkedDocument>();
            NonArchivedAttachments = new HashSet<NonArchivedAttachments>();
            Note = new HashSet<Note>();
            Transfer = new HashSet<Transfer>();
            DocumentForm = new DocumentForm();
            Folder = new HashSet<Folder>();
            OcrContent = new HashSet<OcrContent>();
        }

        #endregion

        #region Properties

        public long Id { get; set; }
        public short CategoryId { get; set; }
        public short? PriorityId { get; set; }
        public short StatusId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public long CreatedByUserId { get; set; }
        public short? ClassificationId { get; set; }
        public short? ImportanceId { get; set; }
        public short? PrivacyId { get; set; }
        public string ReferenceNumber { get; set; }
        public short? DocumentTypeId { get; set; }
        public string Subject { get; set; }
        public string SubjectSearch { get; set; }
        public long? SendingEntityId { get; set; }
        public bool? ByTemplate { get; set; }
        public DateTime? DueDate { get; set; }
        public long? AttachmentId { get; set; }
        public DateTime? ClosedDate { get; set; }
        public long CreatedByStructureId { get; set; }
        public bool? IsLocked { get; set; }
        public string ExternalReferenceNumber { get; set; }
        public DateTime? InitialCreatedDate { get; set; }
        public bool? IsSigned { get; set; }
        public string? SignedVersion { get; set; }
        public int? ReferenceSequence { get; set; }
        public string? ReceiverPerson { get; set; }
        public long? SenderPerson { get; set; }
        public bool IsExternalSender { get; set; }
        public bool IsExternalReceiver { get; set; }
        public long? G2GDocumentId { get; set; }
        public DateTime? DocumentDate { get; set; }
        public bool TemplateHasSignature { get; set; }

        public DocumentForm DocumentForm { get; set; }
        public int DraftStatus { get; set; }

        public virtual Attachment AttachmentNavigation { get; set; }
        public virtual Category Category { get; set; }
        public virtual Classification Classification { get; set; }
        public virtual User CreatedByUser { get; set; }
        public virtual DocumentType DocumentType { get; set; }
        public virtual Importance Importance { get; set; }
        public virtual Priority Priority { get; set; }
        public virtual Privacy Privacy { get; set; }
        public virtual Structure SendingEntity { get; set; }
        public virtual Status Status { get; set; }
        public virtual ICollection<ActivityLog> ActivityLog { get; set; }
        public virtual ICollection<Attachment> Attachment { get; set; }
        public virtual ICollection<DocumentCarbonCopy> DocumentCarbonCopy { get; set; }
        public virtual ICollection<DocumentReceiverEntity> DocumentReceiverEntity { get; set; }
        public virtual ICollection<Note> Note { get; set; }
        public virtual ICollection<Transfer> Transfer { get; set; }
        public virtual ICollection<NonArchivedAttachments> NonArchivedAttachments { get; set; }
        public virtual ICollection<LinkedDocument> LinkedDocumentDocument { get; set; }
        public virtual ICollection<LinkedDocument> LinkedDocumentLinkedDocumentNavigation { get; set; }
        public virtual ICollection<Folder> Folder { get; set; }
        public virtual Structure CreatedByStructure { get; set; }
        public virtual ICollection<OcrContent> OcrContent { get; set; }
        public virtual ICollection<Event> Event { get; set; }
        public virtual ICollection<Instruction> Instruction{ get; set; }
        //public long? AttachmentCount { get; set; }
        //public long? NotesCount { get; set; }
        //public long? LinkedCorrespondanceCount { get; set; }
        public virtual ICollection<DocumentLock> DocumentLocks { get; set; }

        #endregion

        #region Private Methods

        private void OpenDbContext()
        {
            if (_ctx == null)
            {
                _ctx = new CTSContext();
            }
        }

        private void updateSubjectSearch()
        {
            if (!String.IsNullOrEmpty(Subject))
                this.SubjectSearch = Helper.GetSearchString(Subject);
        }

        #endregion

        #region Public Methods

        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                CreatedDate = DateTime.Now;
                ModifiedDate = DateTime.Now;
                updateSubjectSearch();
                ctx.Document.Add(this);
                ctx.SaveChanges();
            }
        }

        public void Update()
        {
            using (var ctx = new CTSContext())
            {
                updateSubjectSearch();
                ModifiedDate = DateTime.Now;
                foreach (var item in DocumentReceiverEntity.Where(t => t.Id <= 0))
                {
                    item.DocumentId = Id;
                    ctx.DocumentReceiverEntity.Add(item);
                }
                foreach (var item in DocumentCarbonCopy.Where(t => t.Id <= 0))
                {
                    item.DocumentId = Id;
                    ctx.DocumentCarbonCopy.Add(item);
                }
                ctx.Entry(this).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        public void UpdateIncludeDocumentForm()
        {
            using (var ctx = new CTSContext())
            {
                updateSubjectSearch();
                ModifiedDate = DateTime.Now;
                foreach (var item in DocumentReceiverEntity.Where(t => t.Id <= 0))
                {
                    item.DocumentId = Id;
                    ctx.DocumentReceiverEntity.Add(item);
                }
                foreach (var item in DocumentCarbonCopy.Where(t => t.Id <= 0))
                {
                    item.DocumentId = Id;
                    ctx.DocumentCarbonCopy.Add(item);
                }
                ctx.Entry(this).State = EntityState.Modified;
                ctx.Entry(DocumentForm).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }
        
        public void UpdateReferenceNumber()
        {
            using (var ctx = new CTSContext())
            {
                InitialCreatedDate = CreatedDate;
                updateSubjectSearch();
                ModifiedDate = DateTime.Now;
                CreatedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.InitialCreatedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.CreatedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.ReferenceNumber).IsModified = true;
                ctx.Entry(this).Property(x => x.ReferenceSequence).IsModified = true;
                ctx.SaveChanges();
            }
        }

        public void UpdateStatus()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.StatusId).IsModified = true;
                ctx.SaveChanges();
            }
        }
        //public void UpdateRecivingEntity()
        //{
        //    using (var ctx = new CTSContext())
        //    {
        //        //ModifiedDate = DateTime.Now;
        //        //ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
        //        ctx.Entry(this).Collection(x => x.DocumentReceiverEntity).IsModified = true;
        //        ctx.SaveChanges();
        //    }
        //}
        public void UpdateStatusByList(List<Document> documents)
        {
            using (var ctx = new CTSContext())
            {
                for (int i = 0; i < documents.Count; i++)
                {
                    documents[i].ModifiedDate = DateTime.Now;
                    ctx.Entry(documents[i]).Property(x => x.ModifiedDate).IsModified = true;
                    ctx.Entry(documents[i]).Property(x => x.StatusId).IsModified = true;
                }
                ctx.SaveChanges();
            }
        }

        public void UpdateAttachmentIdWithoutModifiedDate()
        {
            using (var ctx = new CTSContext())
            {
                ctx.Entry(this).Property(x => x.AttachmentId).IsModified = true;
                ctx.SaveChanges();
            }
        }

        public void UpdateAttachmentId()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.AttachmentId).IsModified = true;
                ctx.SaveChanges();
            }
        }

        public void UpdateStatusCloseDateAndTransfer()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.StatusId).IsModified = true;
                ctx.Entry(this).Property(x => x.ClosedDate).IsModified = true;
                foreach (var item in Transfer.Where(t => t.Id <= 0))
                {
                    ctx.Entry(item).State = EntityState.Added;
                }
                ctx.SaveChanges();
            }
        }

        public Document Find(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().FirstOrDefault(c => c.Id == id);
            }
        }

        public async Task<Document> FindAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Document.AsNoTracking().FirstOrDefaultAsync(c => c.Id == id);
            }
        }

        public Document FindIncludeSendingEntity(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(t => t.SendingEntity).AsNoTracking().FirstOrDefault(c => c.Id == id);
            }
        }

        public List<Document> List()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().ToList();
            }
        }

        public List<Document> ListByIds(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Where(t => ids.Contains(t.Id)).ToList();
            }
        }

        public List<Document> ListWithCaching()
        {
            List<Document> retValue = new CacheUtility().GetCachedItem<List<Document>>(typeof(Document).Name);
            if (retValue == null)
            {
                retValue = new CacheUtility().InsertCachedItem<List<Document>>(List(), typeof(Document).Name);
            }
            return retValue;
        }

        public void Delete(long id)
        {
            using (var ctx = new CTSContext())
            {
                Document item = new Document { Id = id };
                ctx.Document.Attach(item);
                ctx.Document.Remove(item);
                ctx.SaveChanges();
            }
        }

        public void Delete()
        {
            Delete(Id);
        }

        public void Delete(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                foreach (var id in ids)
                {
                    Document item = new Document { Id = id };
                    ctx.Document.Attach(item);
                    ctx.Document.Remove(item);
                }
                ctx.SaveChanges();
            }
        }

        public bool CheckStatusInUse(short statusId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.StatusId == statusId);
            }
        }

        public bool CheckClassificationInUse(short classificationId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.ClassificationId == classificationId);
            }
        }

        public bool CheckImportanceInUse(short importanceId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.ImportanceId == importanceId);
            }
        }

        public bool CheckPriorityInUse(short priorityId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.PriorityId == priorityId);
            }
        }

        public bool CheckPrivacyInUse(short privacyId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.PrivacyId == privacyId);
            }
        }

        public bool CheckDocumentTypeInUse(short documentTypeId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.DocumentTypeId == documentTypeId);
            }
        }

        public bool CheckCategoryInUse(short id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.CategoryId == id);
            }
        }

        public bool HasAccess(long id, long userId, List<long> structureIds)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.Id == id && (t.CreatedByUserId == userId || t.Transfer.Any(n => n.ToUserId == userId
                || t.Transfer.Any(n => n.FromUserId == userId || structureIds.Contains(n.FromStructureId.Value) || structureIds.Contains(n.ToStructureId.Value)))));
            }
        }

        public List<Document> ListDraft(int startIndex, int pageSize, long userId, Expression<Func<Document, bool>> filterExpression = null,
            List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                //ctx.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup).AsNoTracking();

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t => t.CreatedByUserId == userId && t.StatusId == (short)DocumentStatus.Draft);
                return query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public async Task<List<Document>> ListDraftAsync(int startIndex, int pageSize, long userId, List<short> categoryIds, Expression<Func<Document, bool>> filterExpression = null,
            List<SortExpression<Document>> sortExpression = null, short? privacyLevel = null, bool isStructureDraft = false)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking();

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }

                if (isStructureDraft)
                    query = query.Where(t => t.StatusId == (short)DocumentStatus.Draft && ((privacyLevel == null || t.PrivacyId <= privacyLevel || t.PrivacyId == null) || t.CreatedByUserId == userId));
                else
                    query = query.Where(t => t.CreatedByUserId == userId && t.StatusId == (short)DocumentStatus.Draft && (privacyLevel == null || t.PrivacyId <= privacyLevel || t.PrivacyId == null));

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                var topDocumentIds = pageSize < 0 ? await query.Select(t => t.Id).ToListAsync() : await query.Select(t => t.Id).Skip(startIndex).Take(pageSize).ToListAsync();


                IQueryable<Document> finalQuery = ctx.Document.AsNoTracking()
                    .Where(t => topDocumentIds.Contains(t.Id));

                if (sortExpression != null)
                {
                    finalQuery = finalQuery.DynamicOrderBy(sortExpression);
                }

                finalQuery=finalQuery.Include(t => t.SendingEntity)
                    .Include(t => t.Note)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.CreatedByUser);

                return await finalQuery.ToListAsync();
            }
        }

        public Task<int> GetDraftCount(long userId, List<short> categoryIds, Expression<Func<Document, bool>> filterExpression = null, short? privacyLevel = null, bool isStructureDraft = false)
        {
            OpenDbContext();
            IQueryable<Document> query = _ctx.Document.AsNoTracking();
            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            if (!categoryIds.IsNullOrEmpty())
            {
                query = query.Where(t => categoryIds.Contains(t.CategoryId));
            }
            if (isStructureDraft)
                query = query.Where(t => t.StatusId == (short)DocumentStatus.Draft && ((privacyLevel == null || t.PrivacyId <= privacyLevel || t.PrivacyId == null) || t.CreatedByUserId == userId));
            else
                query = query.Where(t => t.CreatedByUserId == userId && t.StatusId == (short)DocumentStatus.Draft && (privacyLevel == null || t.PrivacyId <= privacyLevel || t.PrivacyId == null));

            var result = query.Select(d => d.Id);

            IQueryable<Document> countquery = _ctx.Document.AsNoTracking().Where(d => result.Contains(d.Id));

            return result.CountAsync();

            //var result = query.Select(d => d.Id).ToListAsync();

            //var countquery = _ctx.Document.AsNoTracking().Where(d => result.Result.Contains(d.Id));

            //return countquery.CountAsync();
        }

        public List<Document> ListMyRequests(int startIndex, int pageSize, long userId, Expression<Func<Document, bool>> filterExpression = null,
            List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup).AsNoTracking();
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t => t.StatusId == (short)DocumentStatus.InProgress && t.CreatedByUserId == userId);
                return query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public async Task<List<Document>> ListMyRequestsAsync(int startIndex, int pageSize, long userId, Expression<Func<Document, bool>> filterExpression = null,
            List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.LinkedDocumentDocument).ThenInclude(t => t.LinkedDocumentNavigation)
                    .Include(t => t.SendingEntity).Include(t=>t.Note)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup).AsNoTracking();
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t => t.StatusId == (short)DocumentStatus.InProgress && t.CreatedByUserId == userId);
                return pageSize < 0 ? await query.ToListAsync() : await query.Skip(startIndex).Take(pageSize).ToListAsync();

                //return await query.Skip(startIndex).Take(pageSize).ToListAsync();
            }
        }

        public Task<int> GetMyRequestsCount(long userId, Expression<Func<Document, bool>> filterExpression = null)
        {
            OpenDbContext();
            IQueryable<Document> query = _ctx.Document.AsNoTracking();
            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            query = query.Where(t => t.StatusId == (short)DocumentStatus.InProgress && t.CreatedByUserId == userId);
            return query.CountAsync();
        }
        public async Task<List<Document>> ListCustomBasketDocumentsAsync(int startIndex, int pageSize, List<long> DocumentIds, Expression<Func<Document, bool>> filterExpression = null,
            List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup).AsNoTracking();
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t => DocumentIds.Contains(t.Id) && t.StatusId != (short)DocumentStatus.Draft);
                return await query.Skip(startIndex).Take(pageSize).ToListAsync();

            }
        }

        public Task<int> GetCustomBasketCount(List<long> DocumentIds, Expression<Func<Document, bool>> filterExpression = null)
        {
            OpenDbContext();
            IQueryable<Document> query = _ctx.Document.AsNoTracking();
            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            query = query.Where(t => DocumentIds.Contains(t.Id) && t.StatusId != (short)DocumentStatus.Draft);
            return query.CountAsync();
        }
        public List<DocumentPrivacytModel> ListDocumentPrivacIesByIds(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking()
                    .Where(t => ids.Contains(t.Id))
                    .Select(document => new DocumentPrivacytModel
                    {
                        DocumentId = document.Id,
                        PrivacyId = document.PrivacyId ?? 1
                    }).ToList();
            }
        }
        public List<Document> ListClosed(int startIndex, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Expression<Func<Document, bool>> filterExpression = null,
            List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup).AsNoTracking();
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t => t.ClosedDate.HasValue && (t.CreatedByUserId == userId ||
                t.Transfer.Any(x => x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))));
                return query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public async Task<List<Document>> ListClosedAsync(int startIndex, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Expression<Func<Document, bool>> filterExpression = null,
            List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity).Include(t=>t.Note)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup).AsNoTracking();
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t => t.ClosedDate.HasValue && (t.CreatedByUserId == userId ||
                t.Transfer.Any(x => x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))));
                return pageSize < 0 ? await query.ToListAsync() : await query.Skip(startIndex).Take(pageSize).ToListAsync();

            }
        }

        public Task<int> GetClosedCount(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Expression<Func<Document, bool>> filterExpression = null)
        {
            OpenDbContext();
            IQueryable<Document> query = (from d in _ctx.Document
                                          join t in _ctx.Transfer on d.Id equals t.DocumentId
                                          join p in _ctx.Privacy on d.PrivacyId equals p.Id
                                          where d.ClosedDate.HasValue &&
                                          (d.CreatedByUserId == userId ||
                                          (t.ToUserId == userId || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && p.Level <= privacyLevel))
                                          )
                                          select d).AsNoTracking();
            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            return query.Select(t => t.Id).Distinct().CountAsync();
        }

        public async Task<int> GetDraftTodayCount(long userId, List<short> categoryIds,Expression<Func<Document, bool>> filterExpression = null, short? privacyLevel = null, bool isStructureDraft = false)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Today.Date;//to fix oracle issue
                IQueryable<Document> query = ctx.Document/*Where(t => t.StatusId == (short)DocumentStatus.Draft && t.CreatedByUserId == userId && t.CreatedDate.Date == today && (privacyLevel == null || t.PrivacyId <= privacyLevel || t.PrivacyId == null))*/.AsNoTracking();
                if (isStructureDraft)
                    query = query.Where(t => t.StatusId == (short)DocumentStatus.Draft && t.CreatedDate.Date == today && ((privacyLevel == null || t.PrivacyId <= privacyLevel || t.PrivacyId == null) || t.CreatedByUserId == userId));
                else
                    query = query.Where(t => t.CreatedByUserId == userId && t.StatusId == (short)DocumentStatus.Draft && t.CreatedDate.Date == today && (privacyLevel == null || t.PrivacyId <= privacyLevel || t.PrivacyId == null));

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                return await query.CountAsync();
            }
        }

        public async Task<int> GetMyRequestsTodayCount(long userId, Expression<Func<Document, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Today.Date;//to fix oracle issue
                IQueryable<Document> query = ctx.Document.Where(t => t.StatusId == (short)DocumentStatus.InProgress && t.CreatedByUserId == userId && t.CreatedDate.Date == today).AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return await query.CountAsync();
            }
        }

        public async Task<int> GetClosedTodayCount(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Expression<Func<Document, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Today.Date;//to fix oracle issue
                IQueryable<Document> query = (from d in ctx.Document
                                              join t in ctx.Transfer on d.Id equals t.DocumentId
                                              join p in ctx.Privacy on d.PrivacyId equals p.Id
                                              where d.ClosedDate.HasValue && d.ClosedDate.Value.Date == today &&
                                              (d.CreatedByUserId == userId ||
                                              (t.ToUserId == userId || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && p.Level <= privacyLevel))
                                              )
                                              select d).AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return await query.Select(t => t.Id).Distinct().CountAsync();
            }
        }

        /// <summary>
        /// Include Category,CreatedUser Transfer
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Document FindIncludeTransfers(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(e => e.Category).Include(t => t.Privacy)
                    .Include(e => e.CreatedByUser).Include(e => e.Transfer).Include(t => t.SendingEntity)
                    .Include(t => t.DocumentReceiverEntity)                    
                    .ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity)
                    .ThenInclude(t => t.EntityGroup).AsNoTracking()
                    .Include(t=>t.Attachment)
                    .Include(d => d.AttachmentNavigation)
                    .FirstOrDefault(t => t.Id == id);
            }
        }
        public Document FindIncludeAttachments(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(t=>t.Attachment).FirstOrDefault(t => t.Id == id);
            }
        }
        public Document FindIncludeLinkedDocument(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document
                    .Include(x => x.LinkedDocumentDocument)
                    .Include(x => x.LinkedDocumentLinkedDocumentNavigation)
                    .AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        public Document FindIncludeCategoryCreatedByStructure(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(e => e.Category).Include(e => e.CreatedByStructure).AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }
        public bool ISdesignatedPersonExist(long id)
        {
            using (var ctx = new CTSContext())
            {
                var documents = ctx.Document.Where(d => (d.IsExternalSender && d.SenderPerson == id) || d.IsExternalReceiver).ToList();

                return documents.Any(d =>(d.IsExternalSender && d.SenderPerson == id) ||(d.IsExternalReceiver && IsIdInReceiverPerson(d.ReceiverPerson, id)));
            }
        }
        private bool IsIdInReceiverPerson(string receiverPersonJson, long id)
        {
            if (string.IsNullOrEmpty(receiverPersonJson))
                return false;
            try
            {
                var receiverPersonDict = JsonSerializer.Deserialize<Dictionary<string, string>>(receiverPersonJson);

                if (receiverPersonDict.TryGetValue("Id", out var ids))
                {
                    var idArray = ids.Split('-');
                    return idArray.Contains(id.ToString());
                }
                return false;
            }
            catch (JsonException)
            {
                return false;
            }
        }
        public Document FindIncludeCategory(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(e => e.Category).AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include: Category, DocumentForm, DocumentReceiverEntity, DocumentCarbonCopy, SendingEntity, Structure, CreatedByUser, Classification, DocumentType
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Document FindIncludeAll(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(x => x.Category).Include(x => x.DocumentForm).Include(x => x.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(x => x.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(x => x.SendingEntity).Include(x => x.DocumentCarbonCopy).ThenInclude(t => t.Structure)
                    .Include(x => x.CreatedByUser).Include(x => x.Classification).Include(x => x.DocumentType)
                    .Include(x => x.LinkedDocumentDocument)
                    .Include(x => x.LinkedDocumentLinkedDocumentNavigation)
                    .Include(x => x.Privacy)
                    .Include(x => x.AttachmentNavigation)
                    .AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include: DocumentForm, DocumentReceiverEntity, DocumentCarbonCopy, LinkedDocumentDocument, Note, Attachmentnavigation
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Document FindIncludeForCopy(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(x => x.DocumentForm).Include(x => x.DocumentReceiverEntity)
                    .Include(x => x.DocumentCarbonCopy)
                    .Include(x => x.LinkedDocumentDocument).ThenInclude(x=>x.LinkedDocumentNavigation)
                    .Include(x => x.LinkedDocumentLinkedDocumentNavigation)
                    .Include(x => x.Note).Include(x=>x.NonArchivedAttachments)
                    .AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }
        /// <summary>
        /// Include: DocumentForm, DocumentReceiverEntity, DocumentCarbonCopy, LinkedDocumentDocument, Note, Attachment ,NonArchivedAttachments
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Document FindIncludeForExport(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(x => x.DocumentForm)
                    .Include(x => x.DocumentReceiverEntity).ThenInclude(x => x.Structure)
                    .Include(x => x.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(x => x.DocumentCarbonCopy).ThenInclude(t => t.Structure)
                    .Include(x => x.LinkedDocumentDocument)
                    .Include(x => x.LinkedDocumentLinkedDocumentNavigation)
                    .Include(x => x.Note).Include(x => x.Attachment).ThenInclude(t => t.AttachmentSecurities).Include(x => x.NonArchivedAttachments)
                    .Include(x => x.Category).Include(x => x.Transfer)
                    .AsNoTracking().FirstOrDefault(t => t.Id == id);


                

            }
        }
        public async Task<Document> FindIncludeAllAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Document.Include(x => x.Transfer).Include(x => x.Category).Include(x => x.DocumentForm).Include(x => x.DocumentReceiverEntity).ThenInclude(t => t.Structure).ThenInclude(t => t.Parent)
                    .Include(x => x.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(x => x.SendingEntity).Include(x => x.DocumentCarbonCopy).ThenInclude(t => t.Structure).ThenInclude(e => e.Parent)
                    .Include(x => x.CreatedByUser).Include(x => x.Classification).Include(x => x.DocumentType)
                    .Include(t => t.AttachmentNavigation)
                    .Include(t => t.Attachment).ThenInclude(t => t.AttachmentSignUser).ThenInclude(t => t.SignatureRegion)
                    .AsNoTracking().FirstOrDefaultAsync(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include DocumentForm, Include DocumentReceiverEntity, Include DocumentCarbonCopy
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Document FindIncludeDocumentForm(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(x => x.DocumentForm).Include(x => x.DocumentReceiverEntity).Include(x => x.DocumentCarbonCopy)
                    .Include(x => x.Attachment)
                    .AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include DocumentForm, Include DocumentReceiverEntity, Include DocumentCarbonCopy
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Document> FindIncludeDocumentFormAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                //add transfer
                return await ctx.Document.Include(x => x.DocumentForm).Include(x => x.DocumentReceiverEntity).Include(x => x.DocumentCarbonCopy)
                    .AsNoTracking().FirstOrDefaultAsync(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include: DocumentForm, DocumentReceiverEntity, SendingEntity, DocumentCarbonCopy, Structure, CreatedByUser, Classification
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Document FindIncludeDocumentFormClassification(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(x => x.DocumentForm).Include(x => x.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(x => x.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(x => x.SendingEntity).Include(x => x.DocumentCarbonCopy).ThenInclude(t => t.Structure)
                    .Include(x => x.CreatedByUser).Include(x => x.Classification)
                    .AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include: DocumentForm, DocumentReceiverEntity, DocumentCarbonCopy, Structure, Classification, Priority, Privacy, Importance
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Document> FindIncludeDocumentMetadataAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Document.Include(x => x.DocumentForm).Include(x => x.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(x => x.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(x => x.SendingEntity).Include(x => x.DocumentCarbonCopy).ThenInclude(t => t.Structure)
                    .Include(x => x.Classification).Include(x => x.Priority).Include(x => x.Privacy).Include(x => x.Importance).Include(x => x.DocumentType)
                    .AsNoTracking().FirstOrDefaultAsync(t => t.Id == id);

            }
        }

        /// <summary>
        /// Include: DocumentReceiverEntity, DocumentCarbonCopy
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Document FindIncludeReceiversAndCarbonCopy(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(x => x.DocumentReceiverEntity).Include(x => x.DocumentCarbonCopy)
                    .AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include: DocumentReceiverEntity, DocumentCarbonCopy, Structure, EntityGroup
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Document FindIncludeEntities(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(x => x.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                     .Include(x => x.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                     .Include(x => x.DocumentCarbonCopy).ThenInclude(t => t.Structure).Include(t => t.DocumentForm)
                     .AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include: DocumentReceiverEntity, DocumentCarbonCopy
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Document FindIncludeReceiversAndCarbonCopyAndCategory(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(x => x.Category).Include(x => x.DocumentReceiverEntity).Include(x => x.DocumentCarbonCopy)
                    .AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        public List<Document> ListSearch(int startIndex, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, Expression<Func<Document, bool>> filterExpression = null, Expression<Func<Document, bool>> categoryFilterExpression = null,
            List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity).Include(t => t.CreatedByUser)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup).Include(t => t.Transfer).AsNoTracking();
                List<SearchAssignedSecurity> AssignedStructures = searchAssignedStructures != null
                ? new SearchAssignedSecurity().GetSearchAssignedSecurity(userId, searchAssignedStructures)
                : new List<SearchAssignedSecurity>();

             
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (categoryFilterExpression != null)
                {
                    query = query.Where(categoryFilterExpression);
                }

                var userCondition = PredicateBuilder.New<Document>(false);
                userCondition = userCondition.Or(t =>
                    t.StatusId != (short)DocumentStatus.Draft &&
                    t.CreatedByUserId == userId ||
                    t.Transfer.Any(x => x.FromUserId.Value == userId) ||
                    t.Transfer.Any(x => x.ToUserId.Value == userId)
                );
                if (searchAssignedStructures != null)
                {
                    foreach (var assignedStructure in AssignedStructures)
                    {
                        var SearchAssginCondition = PredicateBuilder.New<Document>(true);

                        SearchAssginCondition = SearchAssginCondition.Or(t =>
                            (searchAssignedStructureSearchUsersDocuments ?
                                t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) ||
                                                    searchAssignedStructures.Contains(x.ToStructureId.Value) ||
                                                    searchAssignedStructures.Contains(x.FromStructureId.Value)) :
                                t.Transfer.Any(x => !x.ToUserId.HasValue &&
                                                    (structureIds.Contains(x.ToStructureId.Value) ||
                                                     (x.ToStructureId.Value == assignedStructure.ToStructureId && (t.Privacy != null ? t.Privacy.Level <= assignedStructure.PrivacyLevel : true)) ||
                                                     (x.FromStructureId.Value == assignedStructure.ToStructureId && (t.Privacy != null ? t.Privacy.Level <= assignedStructure.PrivacyLevel : true)))))

                        );

                        userCondition = userCondition.Or(SearchAssginCondition);
                    }

                    query = query.Where(userCondition);
                }
                else
                {
                    query = query.Where(userCondition.Or(t =>
                        (t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) &&
                        isStructureReceiver &&
                        (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)))
                    );
                }

                return query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public async Task<List<Document>> ListSearchAsync(int startIndex, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<SearchAssignedSecurity> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, bool useAllStructures, Expression<Func<Document, bool>> filterExpression = null, Expression<Func<Document, bool>> categoryFilterExpression = null,
        Expression<Func<Document, bool>> simpleSearchFilterExpression = null, List<SortExpression<Document>> sortExpression = null, string keyword = null, string simpleSearch = null, Expression<Func<Document, bool>> categoryIgnoreFilterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity).Include(t => t.CreatedByUser)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup).Include(t => t.Transfer).Include(t=>t.Note).AsNoTracking();

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (categoryFilterExpression != null)
                {
                    query = query.Where(categoryFilterExpression);
                }
                if (categoryIgnoreFilterExpression != null)
                {
                   query = query.Where(categoryIgnoreFilterExpression);
                }
                if (simpleSearchFilterExpression != null)
                {
                    query = query.Where(simpleSearchFilterExpression);
                }

                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(t => (t.DocumentForm != null && !string.IsNullOrEmpty(t.DocumentForm.Keyword)) ? (Constants.SPLITTER + t.DocumentForm.Keyword.ToLower() + Constants.SPLITTER).Contains(Constants.SPLITTER + keyword.ToLower() + Constants.SPLITTER) : false);
                }


                var userCondition = PredicateBuilder.New<Document>(false);

                //add conditions for user transfers
                if (useAllStructures)
                    userCondition = userCondition.Or(t => t.Transfer.Any(x => x.FromUserId.Value == userId) || t.Transfer.Any(x => x.ToUserId.Value == userId));
                else
                    userCondition = userCondition.Or(t =>
                         t.Transfer.Any(x => x.FromUserId.Value == userId && structureIds.Contains(x.FromStructureId.Value))
                        || t.Transfer.Any(x => x.ToUserId.Value == userId && structureIds.Contains(x.ToStructureId.Value))
                    );

                //add conditions for search assign structures' transfers
                if (searchAssignedStructures != null && searchAssignedStructures.Count > 0)
                {
                    var groupedStructures = searchAssignedStructures
                        .GroupBy(s => s.ToStructureId)
                        .ToDictionary(g => g.Key, g => g.ToList());

                    foreach (var groupStructure in groupedStructures.Values)
                    {
                            var preferredStructure = (groupStructure.Count > 1 && groupStructure.Any(x=> x.UserId.HasValue)) ? groupStructure.FirstOrDefault(s => s.UserId.HasValue) : groupStructure.FirstOrDefault();

                            if (preferredStructure == null)
                            {
                                continue;
                            }


                        var searchAssignedCondition = PredicateBuilder.New<Document>(true)
                            .Or(t => t.Transfer.Any(
                                x => (x.ToStructureId.Value == preferredStructure.ToStructureId || x.FromStructureId.Value == preferredStructure.ToStructureId)
                                    && (t.Privacy != null ? t.Privacy.Level <= preferredStructure.PrivacyLevel : false)
                                    && (searchAssignedStructureSearchUsersDocuments ? true : !x.ToUserId.HasValue)
                                ));

                        userCondition = userCondition.Or(searchAssignedCondition);
                    }
                }

                //add condition for users' structure transfers
                //TODO: do something similar to SSA, but for structure receivers, loop over structures, if they have receiver add their condition, otherwise skip. this is in case useAllStructures == true
                if (isStructureReceiver)
                    userCondition = userCondition.Or(t => t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true));

                query = query.Where(userCondition);
                
                return await query.Skip(startIndex).Take(pageSize).ToListAsync();
            }
        }

        public async Task<List<Document>> ListSearchCorrespondenceAsync(
            int startIndex, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, 
            short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments,
            Expression<Func<Document, bool>> filterExpression = null, Expression<Func<Document, bool>> categoryFilterExpression = null,
          List<SortExpression<Document>> sortExpression = null, string keyword = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity).Include(t => t.CreatedByUser)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup).Include(t => t.Transfer).AsNoTracking();
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (categoryFilterExpression != null)
                {
                    query = query.Where(categoryFilterExpression);
                }

                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(t => (t.DocumentForm != null && !string.IsNullOrEmpty(t.DocumentForm.Keyword)) ? (Constants.SPLITTER + t.DocumentForm.Keyword.ToLower() + Constants.SPLITTER).Contains(Constants.SPLITTER + keyword.ToLower() + Constants.SPLITTER) : false);
                }
                query = query.Where(t => t.StatusId != (short)DocumentStatus.Draft);

                return await query.Skip(startIndex).Take(pageSize).ToListAsync();
            }
        }



        public async Task<int> GetSearchCount(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<SearchAssignedSecurity> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, bool useAllStructures, Expression<Func<Document, bool>> filterExpression = null, Expression<Func<Document, bool>> categoryFilterExpression = null,
            Expression<Func<Document, bool>> simpleSearchFilterExpression = null, string keyword = null, Expression<Func<Document, bool>> categoryIgnoreFilterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.Transfer).AsNoTracking();

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (categoryFilterExpression != null)
                {
                    query = query.Where(categoryFilterExpression);
                }
                if (categoryIgnoreFilterExpression != null)
                {
                   query = query.Where(categoryIgnoreFilterExpression);
                }
                if (simpleSearchFilterExpression != null)
                {
                    query = query.Where(simpleSearchFilterExpression);
                }
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(t => (t.DocumentForm != null && !string.IsNullOrEmpty(t.DocumentForm.Keyword)) ? (Constants.SPLITTER + t.DocumentForm.Keyword.ToLower() + Constants.SPLITTER).Contains(Constants.SPLITTER + keyword.ToLower() + Constants.SPLITTER) : false);
                }
                
                
                var userCondition = PredicateBuilder.New<Document>(false);

                //add conditions for user transfers
                if (useAllStructures)
                    userCondition = userCondition.Or(t => t.Transfer.Any(x => x.FromUserId.Value == userId) || t.Transfer.Any(x => x.ToUserId.Value == userId));
                else
                    userCondition = userCondition.Or(t =>
                         t.Transfer.Any(x => x.FromUserId.Value == userId && structureIds.Contains(x.FromStructureId.Value))
                        || t.Transfer.Any(x => x.ToUserId.Value == userId && structureIds.Contains(x.ToStructureId.Value))
                    );

                //add conditions for search assign structures' transfers
                if (searchAssignedStructures != null && searchAssignedStructures.Count > 0)
                {
                    // Group searchAssignedStructures by ToStructureId
                    var groupedStructures = searchAssignedStructures
                        .GroupBy(s => s.ToStructureId)
                        .ToDictionary(g => g.Key, g => g.ToList());

                    foreach (var group in groupedStructures.Values)
                    {
                        // Find the structure with a non-null UserId if it exists
                        var preferredStructure = (group.Count > 1 && group.Any(x => x.UserId.HasValue)) ? group.FirstOrDefault(s => s.UserId.HasValue) : group.FirstOrDefault();


                        if (preferredStructure == null)
                        {
                            // Skip this group since there's no structure with a non-null UserId
                            continue;
                        }

                        var searchAssignedCondition = PredicateBuilder.New<Document>(true)
                            .Or(t => t.Transfer.Any(
                                x => (x.ToStructureId.Value == preferredStructure.ToStructureId || x.FromStructureId.Value == preferredStructure.ToStructureId)
                                    && (t.Privacy != null ? t.Privacy.Level <= preferredStructure.PrivacyLevel : false)
                                    && (searchAssignedStructureSearchUsersDocuments ? true : !x.ToUserId.HasValue)
                                ));

                        userCondition = userCondition.Or(searchAssignedCondition);
                    }
                }

                //add condition for users' structure transfers
                //TODO: do something similar to SSA, but for structure receivers, loop over structures, if they have receiver add their condition, otherwise skip. this is in case useAllStructures == true
                if (isStructureReceiver)
                    userCondition = userCondition.Or(t => t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true));

                query = query.Where(userCondition);

                return await query.CountAsync();
            }
        }

        public bool CheckHaveAccess(long id, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long roleId, long structureId, bool fromRejectedDocument = false)
        {
            using (var ctx = new CTSContext())
            {
                using (var CoreContext = new Intalio.Core.DAL.CoreContext())
                {
                    var HaveAccessStructureInbox = CoreContext.NodeSecurity.Include(d => d.Node).AsNoTracking().AnyAsync(s => s.Node.Inherit == "StructureInbox" && (s.RoleId == roleId || (s.UserId == userId && s.StructureId == structureId))).Result;

                    return ctx.Document.AsNoTracking().Any(t => t.Id == id && (t.CreatedByUserId == userId ||
                t.Transfer.Any(n => n.ToUserId == userId ||
                (((((n.ToUserId.HasValue && n.IsSigned == true && HaveAccessStructureInbox) || !n.ToUserId.HasValue) && structureIds.Contains(n.ToStructureId.Value)) || (fromRejectedDocument && structureIds.Contains(n.FromStructureId.Value))) && isStructureReceiver && n.Document.Privacy.Level <= privacyLevel))));

                }
            }
        }
        public bool CheckHaveAccess(long id, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long roleId, long structureId, DelegationModel delegation, bool fromRejectedDocument = false)
        {
            using (var ctx = new CTSContext())
            {
                using (var coreContext = new Intalio.Core.DAL.CoreContext())
                {
                    var retval = false;
                    var HaveAccessStructureInbox = coreContext.NodeSecurity.Include(d => d.Node).AsNoTracking().AnyAsync(s => s.Node.Inherit == "StructureInbox" && (s.RoleId == roleId || (s.UserId == userId && s.StructureId == structureId))).Result;

                    if (delegation != null)
                    {
                        var delegationStartDate = delegation.FromDate;
                        if (delegation.StartDate != null)
                            delegationStartDate = delegation.StartDate.Value;

                        if (delegation.ShowOldCorespondence && delegation.StartDate == null)
                        {
                            retval = ctx.Document.AsNoTracking().Any(t => t.Id == id && (t.CreatedByUserId == userId ||
                           t.Transfer.Any(n => (n.ToUserId == userId && n.CreatedDate <= delegation.ToDate) ||
                         (((((n.ToUserId.HasValue && n.IsSigned == true && HaveAccessStructureInbox) || !n.ToUserId.HasValue) && structureIds.Contains(n.ToStructureId.Value)) || (n.RequestStatus.HasValue && structureIds.Contains(n.FromStructureId.Value))) && isStructureReceiver && n.Document.Privacy.Level <= privacyLevel))));

                        }
                        else
                        {
                            retval = ctx.Document.AsNoTracking().Any(t => t.Id == id && (t.CreatedByUserId == userId ||
                          t.Transfer.Any(n => (n.ToUserId == userId && n.CreatedDate >= delegationStartDate && n.CreatedDate <= delegation.ToDate) ||
                          (((((n.ToUserId.HasValue && n.IsSigned == true && HaveAccessStructureInbox) || !n.ToUserId.HasValue) && structureIds.Contains(n.ToStructureId.Value)) || (n.RequestStatus.HasValue && structureIds.Contains(n.FromStructureId.Value))) && isStructureReceiver && n.Document.Privacy.Level <= privacyLevel))));

                        }
                    }
                    else
                    {
                        var hasBasketPermission = ctx.BasketDocument
                            .AsNoTracking()
                            .Any(x => x.DocumentId == id &&
                                           (x.Basket.CreatedByUserId == userId ||
                                            x.Basket.BasketPermissions.Any(p => p.UserId == userId)));

                        retval = ctx.Document.AsNoTracking().Any(t => t.Id == id && (
                                 (t.CreatedByUserId == userId || t.CreatedByStructureId == structureId)||
                                 t.Transfer.Any(n => n.ToUserId == userId ||
                                    (((((n.ToUserId.HasValue && n.IsSigned == true && HaveAccessStructureInbox) || !n.ToUserId.HasValue) && structureIds.Contains(n.ToStructureId.Value)) || ( n.RequestStatus.HasValue && structureIds.Contains(n.FromStructureId.Value))) && isStructureReceiver && n.Document.Privacy.Level <= privacyLevel)) || 
                                    hasBasketPermission
                             ));
                    }
                    return retval;
                }
            }
        }

        public async Task<bool> CheckHaveAccessAsync(long id, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long roleId, long structureId)
        {
            using (var ctx = new CTSContext())
            {
                using (var CoreContext = new Intalio.Core.DAL.CoreContext())
                {
                    var HaveAccessStructureInbox = CoreContext.NodeSecurity.Include(d => d.Node).AsNoTracking().AnyAsync(s => s.Node.Inherit == "StructureInbox" && (s.RoleId == roleId || (s.UserId == userId && s.StructureId == structureId))).Result;

                    return await ctx.Document.AsNoTracking().AnyAsync(t => t.Id == id && (t.CreatedByUserId == userId ||
                t.Transfer.Any(n => n.ToUserId == userId ||
              (((((n.ToUserId.HasValue && n.IsSigned == true && HaveAccessStructureInbox) || !n.ToUserId.HasValue) && structureIds.Contains(n.ToStructureId.Value)) || (n.RequestStatus.HasValue && structureIds.Contains(n.FromStructureId.Value))) && isStructureReceiver && n.Document.Privacy.Level <= privacyLevel))));

                }
            }
        }

        public async Task<bool> CheckHaveAccessAsync(long id, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long roleId, long structureId, DelegationModel delegation)
        {
            var retval = false;
            using (var ctx = new CTSContext())
            {
                using (var CoreContext = new Intalio.Core.DAL.CoreContext())
                {
                    var HaveAccessStructureInbox = CoreContext.NodeSecurity.Include(d => d.Node).AsNoTracking().AnyAsync(s => s.Node.Inherit == "StructureInbox" && (s.RoleId == roleId || (s.UserId == userId && s.StructureId == structureId))).Result;

                    if (delegation != null)
                    {
                        var delegationStartDate = delegation.FromDate;
                        if (delegation.StartDate != null)
                            delegationStartDate = delegation.StartDate.Value;

                        if (delegation.ShowOldCorespondence && delegation.StartDate == null)
                        {
                            retval = await ctx.Document.AsNoTracking().AnyAsync(t => t.Id == id && (t.CreatedByUserId == userId ||
                             t.Transfer.Any(n => (n.ToUserId == userId && n.CreatedDate <= delegation.ToDate) ||
                           (((((n.ToUserId.HasValue && n.IsSigned == true && HaveAccessStructureInbox) 
                           || !n.ToUserId.HasValue) && structureIds.Contains(n.ToStructureId.Value)) 
                           || (n.RequestStatus.HasValue && structureIds.Contains(n.FromStructureId.Value))) && isStructureReceiver 
                           && n.Document.Privacy.Level <= privacyLevel))));

                        }
                        else
                        {
                            retval = await ctx.Document.AsNoTracking().AnyAsync(t => t.Id == id && (t.CreatedByUserId == userId ||
                            t.Transfer.Any(n => (n.ToUserId == userId && n.CreatedDate >= delegationStartDate && n.CreatedDate <= delegation.ToDate) ||
                          (((((n.ToUserId.HasValue && n.IsSigned == true && HaveAccessStructureInbox) || !n.ToUserId.HasValue)
                          && structureIds.Contains(n.ToStructureId.Value)) || (n.RequestStatus.HasValue && structureIds.Contains(n.FromStructureId.Value)))
                          && isStructureReceiver && n.Document.Privacy.Level <= privacyLevel))));

                        }
                    }
                    else
                    {
                        var hasBasketPermission = await ctx.BasketDocument
                            .AsNoTracking()
                            .AnyAsync(x => x.DocumentId == id &&
                                           (x.Basket.CreatedByUserId == userId ||
                                            x.Basket.BasketPermissions.Any(p => p.UserId == userId)));

                        retval = await ctx.Document.AsNoTracking().AnyAsync(t => t.Id == id && 
                        ( (t.CreatedByUserId == userId || t.CreatedByStructureId == structureIds[0]) ||
                        t.Transfer.Any(n => n.ToUserId == userId ||
                      (((((n.ToUserId.HasValue && n.IsSigned == true && HaveAccessStructureInbox) || !n.ToUserId.HasValue) && structureIds.Contains(n.ToStructureId.Value)) 
                      || (n.RequestStatus.HasValue && structureIds.Contains(n.FromStructureId.Value))) && isStructureReceiver && n.Document.Privacy.Level <= privacyLevel)) 
                        || hasBasketPermission));

                    }
                }
                return retval;
            }
        }

        public bool CheckHaveAccessIncludeSearchAssigned(long id, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments = false)
        {
            using (var ctx = new CTSContext())
            {
                //if (!searchAssignedStructures.IsNullOrEmpty())
                //{
                //    structureIds.AddRange(searchAssignedStructures);
                //    structureIds = structureIds.Distinct().ToList();
                //}
                return ctx.Document.AsNoTracking().Any(t => t.Id == id && (t.CreatedByUserId == userId
                || t.Transfer.Any(x => x.ToUserId.Value == userId)
                || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true))));
            }
        }

        public async Task<bool> CheckHaveAccessIncludeSearchAssignedAsync(long id, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments = false)
        {
            using (var ctx = new CTSContext())
            {
                //if (!searchAssignedStructures.IsNullOrEmpty())
                //{
                //    structureIds.AddRange(searchAssignedStructures);
                //    structureIds = structureIds.Distinct().ToList();
                //}
                return await ctx.Document.AsNoTracking().AnyAsync(t => t.Id == id
                && (t.CreatedByUserId == userId || t.Transfer.Any(x => x.ToUserId.Value == userId)
                || (
                (!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true))));
            }
        }

        public async Task<bool> CheckHaveAccessIncludeSearchAssignedAsync(long id, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments = false, DelegationModel delegation = null)
        {
            var retval = false;
            using (var ctx = new CTSContext())
            {
                //if (!searchAssignedStructures.IsNullOrEmpty())
                //{
                //    structureIds.AddRange(searchAssignedStructures);
                //    structureIds = structureIds.Distinct().ToList();
                //}

                if (delegation != null)
                {
                    var delegationStartDate = delegation.FromDate;
                    if (delegation.StartDate != null)
                        delegationStartDate = delegation.StartDate.Value;

                    if (delegation.ShowOldCorespondence && delegation.StartDate == null)
                    {
                        retval = await ctx.Document.AsNoTracking().AnyAsync(t => t.Id == id
                       && (t.CreatedByUserId == userId || t.Transfer.Any(x => x.ToUserId.Value == userId && x.CreatedDate <= delegation.ToDate)
                       || (
                       (!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                        : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                        : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                           && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true))));
                    }
                    else
                    {
                        retval = await ctx.Document.AsNoTracking().AnyAsync(t => t.Id == id
                       && (t.CreatedByUserId == userId || t.Transfer.Any(x => x.ToUserId.Value == userId && x.CreatedDate >= delegationStartDate && x.CreatedDate <= delegation.ToDate)
                       || (
                       (!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                        : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                        : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                           && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true))));

                    }
                }
                else
                {
                    retval = await ctx.Document.AsNoTracking().AnyAsync(t => t.Id == id
                        && (t.CreatedByUserId == userId || t.Transfer.Any(x => x.ToUserId.Value == userId)
                        || (
                        (!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                         : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                         : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                            && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true))));
                }


            }
            return retval;
        }

        public List<ValueText> CountByCreatedByCategoryStatistics(long userId)
        {
            using (var ctx = new CTSContext())
            {
                var grouped = from doc in ctx.Document.AsNoTracking()
                              where doc.CreatedByUserId == userId
                              group doc.Id by doc.CategoryId into g
                              select new ValueText
                              {
                                  Id = g.Key,
                                  Text = Convert.ToString(g.Count())
                              };
                return grouped.ToList();
            }
        }

        public async Task<List<ValueText>> CountByCreatedByCategoryStatisticsAsync(long userId, long? structureId)
        {
            using (var ctx = new CTSContext())
            {
                if (structureId != null)
                {
                    var grouped = from doc in ctx.Document.AsNoTracking()
                                  where doc.CreatedByUserId == userId
                                  where doc.CreatedByStructureId == structureId
                                  group doc.Id by doc.CategoryId into g
                                  select new ValueText
                                  {
                                      Id = g.Key,
                                      Text = Convert.ToString(g.Count())
                                  };
                    return await grouped.ToListAsync();
                }
                else
                {
                    var grouped = from doc in ctx.Document.AsNoTracking()
                                  where doc.CreatedByUserId == userId
                                  group doc.Id by doc.CategoryId into g
                                  select new ValueText
                                  {
                                      Id = g.Key,
                                      Text = Convert.ToString(g.Count())
                                  };
                    return await grouped.ToListAsync();
                }
                
            }
        }

        public void UpdateStatusAndClosedDate()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.StatusId).IsModified = true;
                ctx.Entry(this).Property(x => x.ClosedDate).IsModified = true;
                ctx.SaveChanges();
            }
        }

        public Document FindIncludeCategoryUser(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(e => e.Category).Include(e => e.CreatedByUser).AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        public bool CheckDraft(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.Id == id && t.StatusId == (int)DocumentStatus.Draft);
            }
        }

        /// <summary>
        /// Without original
        /// </summary>
        /// <param name="documentId"></param>
        /// <returns></returns>
        public bool CheckHasLockedAttachments(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.Id == documentId && t.Attachment.Any(a => a.Id != t.AttachmentId && (a.IsLocked == true || a.IsEditLocked == true) && !a.TransferId.HasValue));
            }
        }

        /// <summary>
        /// Original
        /// </summary>
        /// <param name="documentId"></param>
        /// <returns></returns>
        public bool CheckOriginalDocumentLocked(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.Id == documentId && t.Attachment.Any(a => a.Id == t.AttachmentId && (a.IsLocked == true || a.IsEditLocked == true)));
            }
        }

        /// <summary>
        /// Original locked by user
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public bool CheckOriginalDocumentLockedByUser(long documentId, long userId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(
                        t => t.Id == documentId 
                        && t.Attachment.Any(a => 
                            a.Id == t.AttachmentId 
                            && (
                                (a.IsLocked == true && a.LockedByUserId == userId) 
                                || (a.IsEditLocked == true && a.EditLockedByUserId == userId)
                                )
                            )
                        );
            }
        }

        /// <summary>
        /// Attachments with original locked
        /// </summary>
        /// <param name="documentId"></param>
        /// <returns></returns>
        public bool CheckHasLockedAttachmentsWithOriginal(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.Id == documentId && t.Attachment.Any(a => a.IsLocked == true || a.IsEditLocked == true));
            }
        }

        /// <summary>
        /// Attachments with original locked by user
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public bool CheckHasLockedAttachmentsByUser(long documentId, long userId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.Id == documentId && t.Attachment.Any(a => (a.IsLocked == true && a.LockedByUserId == userId) || (a.EditLockedByUserId == userId && a.IsEditLocked == true)));
            }
        }

        public Document FindWithTransfer(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(p => p.OcrContent).Include(p => p.Privacy).Include(t => t.Transfer).Include(dr => dr.DocumentReceiverEntity).Include(f => f.DocumentForm).AsNoTracking().FirstOrDefault(c => c.Id == id);
            }
        }

        public List<DocumentCrawlingModel> ListByCreatedDate(DateTime? lastCreatedDate, int index, int bulkSize)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(o => o.OcrContent).Include(p => p.Privacy).Include(dr => dr.DocumentReceiverEntity).Include(f => f.DocumentForm).AsNoTracking().Where(d => (d.StatusId != (short)DocumentStatus.Draft) && (!lastCreatedDate.HasValue || d.CreatedDate >= lastCreatedDate.Value));
                query = query.OrderBy(o => o.CreatedDate).Skip(index).Take(bulkSize);
                return query.Select(s => (DocumentCrawlingModel)s).ToList();
            }
        }

        public List<DocumentCrawlingModel> ListByModifiedDate(DateTime? lastModifiedDate, int index, int bulkSize)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(o => o.OcrContent).Include(p => p.Privacy).Include(dr => dr.DocumentReceiverEntity).Include(df => df.DocumentForm).AsNoTracking().Where(d => (d.StatusId != (short)DocumentStatus.Draft) && ((!lastModifiedDate.HasValue && d.ModifiedDate.HasValue) || (d.ModifiedDate.HasValue && lastModifiedDate.HasValue && d.ModifiedDate.Value >= lastModifiedDate.Value)));
                query = query.OrderBy(o => o.ModifiedDate).Skip(index).Take(bulkSize);
                return query.Select(s => (DocumentCrawlingModel)s).ToList();
            }
        }

        public List<string> ListKeywordByCreatedDate(DateTime? lastCreatedDate, int index, int bulkSize)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(f => f.DocumentForm).AsNoTracking().Where(d => (d.StatusId != (short)DocumentStatus.Draft) && (!lastCreatedDate.HasValue || d.CreatedDate >= lastCreatedDate.Value) && !string.IsNullOrEmpty(d.DocumentForm.Keyword));
                query = query.OrderBy(o => o.CreatedDate).Skip(index).Take(bulkSize);
                return query.Select(s => s.DocumentForm.Keyword).ToList();
            }
        }

        public List<string> ListKeywordByModifiedDate(DateTime? lastModifiedDate, int index, int bulkSize)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(df => df.DocumentForm).AsNoTracking().Where(d => (d.StatusId != (short)DocumentStatus.Draft) && ((!lastModifiedDate.HasValue && d.ModifiedDate.HasValue) || (d.ModifiedDate.HasValue && lastModifiedDate.HasValue && d.ModifiedDate.Value >= lastModifiedDate.Value)) && !string.IsNullOrEmpty(d.DocumentForm.Keyword));
                query = query.OrderBy(o => o.ModifiedDate).Skip(index).Take(bulkSize);
                return query.Select(s => s.DocumentForm.Keyword).ToList();
            }
        }

        public List<RelevantTagsModel> GetCountOfRelevantTags(List<string> tags)
        {
            List<RelevantTagsModel> retValue = new List<RelevantTagsModel>();
            var query = string.Empty;
            switch (Configuration.DatabaseType)
            {
                case DatabaseType.MSSQL: query = string.Format(MSSQL, string.Join(Constants.SPLITTER, tags).Replace("'", "''"), Constants.SPLITTER); break;
                case DatabaseType.PostgreSQL: query = string.Format(Postgresql, string.Join(Constants.SPLITTER, tags).Replace("'", "''"), Constants.SPLITTER); break;
                case DatabaseType.Oracle: query = string.Format(Oracle, string.Join(Constants.SPLITTER, tags).Replace("'", "''"), Constants.SPLITTER); break;
            }
            using (var context = new CTSContext())
            using (var command = context.Database.GetDbConnection().CreateCommand())
            {
                command.CommandText = query;
                command.CommandType = CommandType.Text;
                context.Database.OpenConnection();
                using (var result = command.ExecuteReader())
                {
                    while (result.Read())
                    {
                        retValue.Add(new RelevantTagsModel
                        {
                            Text = result["Text"].ToString(),
                            Count = Convert.ToInt32(result["Count"])
                        });
                    }
                }
            }
            return retValue;
        }
        public static string GetDynamicReferenceNumberTypeContent(string functionName, long userId, long? StructureId, long? DocumentId, long? transferId)
        {
            string retValue = "";
            using (var context = new CTSContext())
            using (var command = context.Database.GetDbConnection().CreateCommand())
            {
                functionName = functionName.Replace("\"", "'");
                var sb = new StringBuilder();
                sb.Append("SELECT CAST(dbo." + functionName + "(");
                sb.Append(userId);
                sb.Append(", ");
                sb.Append(StructureId.HasValue ? StructureId.Value.ToString() : "NULL");
                sb.Append(", ");
                sb.Append(DocumentId.HasValue ? DocumentId.Value.ToString() : "NULL");
                sb.Append(", ");
                sb.Append(transferId.HasValue ? transferId.Value.ToString() : "NULL");
                sb.Append(") AS NVARCHAR(50));");

                command.CommandText = sb.ToString();
                command.CommandType = CommandType.Text;
                context.Database.OpenConnection();

                using (var result = command.ExecuteReader())
                {
                    if (result.Read())
                    {
                        object value = result.GetValue(0);
                        if (value != DBNull.Value)
                        {
                            retValue = value.ToString();
                        }
                        else
                        {
                            retValue = "";
                        }
                    }
                }
            }
            return retValue;
        }

        public static string ExecuteDynamicStoredProcedure(string storedProcedureName, long userId, long? StructureId, long? DocumentId, long? transferId)
        {
            string retValue = "";

            using (var context = new CTSContext())
            using (var command = context.Database.GetDbConnection().CreateCommand())
            {
                // Sanitize the stored procedure name (if needed)
                storedProcedureName = storedProcedureName.Replace("\"", "").Replace("'", "");

                // Build the dynamic SQL query directly
                var sb = new StringBuilder();
                sb.Append("EXEC dbo." + storedProcedureName + " ");
                sb.Append(userId); // Append userId directly
                sb.Append(", ");
                sb.Append(StructureId.HasValue ? StructureId.Value.ToString() : "NULL");
                sb.Append(", ");
                sb.Append(DocumentId.HasValue ? DocumentId.Value.ToString() : "NULL");
                sb.Append(", ");
                sb.Append(transferId.HasValue ? transferId.Value.ToString() : "NULL");

                // Set the command text to the dynamic SQL query
                command.CommandText = sb.ToString();
                command.CommandType = CommandType.Text;

                // Open the database connection
                context.Database.OpenConnection();

                // Execute the command and read the result
                using (var result = command.ExecuteReader())
                {
                    if (result.Read())
                    {
                        // If there's a value, assign it; otherwise, return an empty string
                        object value = result.GetValue(0);
                        if (value != DBNull.Value)
                        {
                            retValue = value.ToString();
                        }
                        else
                        {
                            retValue = "";
                        }
                    }
                }
            }

            return retValue;
        }

        public string GetReferenceNumber(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Where(c => c.Id == id).Select(t => t.ReferenceNumber).FirstOrDefault();
            }
        }

        /// <summary>
        /// Include Category,CreatedUser,Priority,Privacy Transfer
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Document FindIncludeTransfersPrioritiesAndPriorities(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.Include(e => e.Category)
                    .Include(e => e.CreatedByUser).Include(e => e.Transfer).Include(t => t.SendingEntity)
                    .Include(e => e.Priority).Include(t => t.Privacy)
                    .Include(t => t.DocumentReceiverEntity)
                    .ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity)
                    .ThenInclude(t => t.EntityGroup).AsNoTracking()
                    .FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include Category,CreatedUser,Priority,Privacy Transfer
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Document> FindIncludeTransfersPrioritiesAndPrioritiesAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Document.Include(e => e.Category)
                    .Include(e => e.CreatedByUser).Include(e => e.Transfer).Include(t => t.SendingEntity)
                    .Include(e => e.Priority).Include(t => t.Privacy)
                    .Include(t => t.DocumentReceiverEntity)
                    .ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity)
                    .ThenInclude(t => t.EntityGroup).AsNoTracking()
                    .FirstOrDefaultAsync(t => t.Id == id);
            }
        }

        public bool HasDraftAccess(long id, long userId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.Id == id && t.CreatedByUserId == userId && t.StatusId == (int)DocumentStatus.Draft);
            }
        }

        public bool HasDraftAccess(long id, long userId, long structureId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.Id == id && (t.CreatedByUserId == userId || t.CreatedByStructureId == structureId) && t.StatusId == (int)DocumentStatus.Draft);
            }
        }

        public void UpdateLock()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.IsLocked).IsModified = true;
                ctx.SaveChanges();
            }
        }

        public bool HasExternalReferencedDocument(long id, short categoryId, long? sendingEntityId, string externalReferenceNumber)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Any(t => t.Id != id && t.CategoryId == categoryId && t.SendingEntityId == sendingEntityId && t.ExternalReferenceNumber.ToLower() == externalReferenceNumber.ToLower());
            }
        }

        public List<CategoryStatusCountModel> GetCountPerCategoryAndStatus(List<long> structureIds = null, string fromDate = "", string toDate = "", long? userId = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                if (userId != null)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                return query.GroupBy(t => new { t.CategoryId, t.StatusId }).Select(t => new CategoryStatusCountModel { StatusId = t.Key.StatusId, CategoryId = t.Key.CategoryId, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryStatusCountModel>> GetCountPerCategoryAndStatusAsync(List<long> structureIds = null, string fromDate = "", string toDate = "", long? userId = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                if (userId != null)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                return await query.GroupBy(t => new { t.CategoryId, t.StatusId }).Select(t => new CategoryStatusCountModel { StatusId = t.Key.StatusId, CategoryId = t.Key.CategoryId, Count = t.Count() }).ToListAsync();
            }
        }

        public List<DocumentsCreatedPerDayModel> GetDocumentsCreatedPerDay(int year, List<short> categoryIds, List<long> structureIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.CreatedDate.Year == year);
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                return query.GroupBy(t => t.CreatedDate.Date).Select(t => new DocumentsCreatedPerDayModel { Date = string.Format("{0}-{1}-{2}", t.Key.Year.ToString(), t.Key.Month.ToString(), t.Key.Day.ToString()), Value = t.Count() }).ToList(); ;
            }
        }

        public async Task<List<DocumentsCreatedPerDayModel>> GetDocumentsCreatedPerDayAsync(int year, List<short> categoryIds, List<long> structureIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.CreatedDate.Year == year);
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                return await query.GroupBy(t => t.CreatedDate.Date).Select(t => new DocumentsCreatedPerDayModel { Date = string.Format("{0}-{1}-{2}", t.Key.Year.ToString(), t.Key.Month.ToString(), t.Key.Day.ToString()), Value = t.Count() }).ToListAsync(); ;
            }
        }

        public List<int> GetAvailableYears()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking()
                    .Where(t => t.StatusId != (short)DocumentStatus.Draft)
                    .GroupBy(t => t.CreatedDate.Year).Select(t => t.Key).ToList();
            }
        }

        public async Task<List<int>> GetAvailableYearsAsync()
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Document.AsNoTracking()
                    .Where(t => t.StatusId != (short)DocumentStatus.Draft)
                    .GroupBy(t => t.CreatedDate.Year).Select(t => t.Key).ToListAsync();
            }
        }

        public List<DocumentsCreatedPerDepartmentModel> GetStatisticsPerDepartment(List<long> structureIds, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                return query.GroupBy(t => new { t.CategoryId, t.CreatedByStructureId }).Select(t => new DocumentsCreatedPerDepartmentModel { StructureId = t.Key.CreatedByStructureId, CategoryId = t.Key.CategoryId, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<DocumentsCreatedPerDepartmentModel>> GetStatisticsPerDepartmentAsync(List<long> structureIds, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                return await query.GroupBy(t => new { t.CategoryId, t.CreatedByStructureId }).Select(t => new DocumentsCreatedPerDepartmentModel { StructureId = t.Key.CreatedByStructureId, CategoryId = t.Key.CategoryId, Count = t.Count() }).ToListAsync();
            }
        }

        public List<DocumentsCreatedPerUserModel> GetStatisticsPerUser(List<long> structureIds, List<long> userIds, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                if (structureIds.IsNullOrEmpty() && userIds.IsNullOrEmpty())
                {
                    return new List<DocumentsCreatedPerUserModel>();
                }
                else
                {
                    IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);
                    if (!string.IsNullOrEmpty(fromDate))
                    {
                        DateTime from = DateTime.Parse(fromDate).Date;
                        query = query.Where(t => t.CreatedDate.Date >= from);
                    }
                    if (!string.IsNullOrEmpty(toDate))
                    {
                        DateTime to = DateTime.Parse(toDate).Date;
                        query = query.Where(t => t.CreatedDate.Date <= to);
                    }
                    if (!structureIds.IsNullOrEmpty())
                    {
                        query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                    }
                    if (!userIds.IsNullOrEmpty())
                    {
                        query = query.Where(t => userIds.Contains(t.CreatedByUserId));
                    }
                    return query.GroupBy(t => new { t.CategoryId, t.CreatedByUserId }).Select(t => new DocumentsCreatedPerUserModel { UserId = t.Key.CreatedByUserId, CategoryId = t.Key.CategoryId, Count = t.Count() }).ToList();
                }
            }
        }

        public async Task<List<DocumentsCreatedPerUserModel>> GetStatisticsPerUserAsync(List<long> structureIds, List<long> userIds, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                if (structureIds.IsNullOrEmpty() && userIds.IsNullOrEmpty())
                {
                    return new List<DocumentsCreatedPerUserModel>();
                }
                else
                {
                    IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);
                    if (!string.IsNullOrEmpty(fromDate))
                    {
                        DateTime from = DateTime.Parse(fromDate).Date;
                        query = query.Where(t => t.CreatedDate.Date >= from);
                    }
                    if (!string.IsNullOrEmpty(toDate))
                    {
                        DateTime to = DateTime.Parse(toDate).Date;
                        query = query.Where(t => t.CreatedDate.Date <= to);
                    }
                    if (!structureIds.IsNullOrEmpty())
                    {
                        query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                    }
                    if (!userIds.IsNullOrEmpty())
                    {
                        query = query.Where(t => userIds.Contains(t.CreatedByUserId));
                    }
                    return await query.GroupBy(t => new { t.CategoryId, t.CreatedByUserId }).Select(t => new DocumentsCreatedPerUserModel { UserId = t.Key.CreatedByUserId, CategoryId = t.Key.CategoryId, Count = t.Count() }).ToListAsync();
                }
            }
        }

        public Task<int> GetInProgressCount(List<short> categoryIds = null, string fromDate = "", string toDate = "")
        {
            OpenDbContext();
            IQueryable<Document> query = _ctx.Document.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.InProgress);
            if (!categoryIds.IsNullOrEmpty())
            {
                query = query.Where(t => categoryIds.Contains(t.CategoryId));
            }
            if (!string.IsNullOrEmpty(fromDate))
            {
                DateTime from = DateTime.Parse(fromDate).Date;
                query = query.Where(t => t.CreatedDate.Date >= from);
            }
            if (!string.IsNullOrEmpty(toDate))
            {
                DateTime to = DateTime.Parse(toDate).Date;
                query = query.Where(t => t.CreatedDate.Date <= to);
            }
            return query.CountAsync();
        }

        public Task<int> GetCompletedCount(List<short> categoryIds = null, string fromDate = "", string toDate = "")
        {
            OpenDbContext();
            IQueryable<Document> query = _ctx.Document.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.Completed);
            if (!categoryIds.IsNullOrEmpty())
            {
                query = query.Where(t => categoryIds.Contains(t.CategoryId));
            }
            if (!string.IsNullOrEmpty(fromDate))
            {
                DateTime from = DateTime.Parse(fromDate).Date;
                query = query.Where(t => t.CreatedDate.Date >= from);
            }
            if (!string.IsNullOrEmpty(toDate))
            {
                DateTime to = DateTime.Parse(toDate).Date;
                query = query.Where(t => t.CreatedDate.Date <= to);
            }
            return query.CountAsync();
        }

        public List<CategoryCountModel> GetDocumentsInProgressOverdue(List<long> structureIds = null, string fromDate = "", string toDate = "", long? userId = null)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.InProgress && t.DueDate.HasValue && t.DueDate < today);
                if (userId != null)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetDocumentsInProgressOverdueAsync(List<long> structureIds = null, string fromDate = "", string toDate = "", long? userId = null)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.InProgress && t.DueDate.HasValue && t.DueDate < today);
                if (userId != null)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public List<CategoryCountModel> GetDocumentsInProgressOnTime(List<long> structureIds = null, string fromDate = "", string toDate = "", long? userId = null)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.InProgress && t.DueDate.HasValue && t.DueDate >= today);
                if (userId != null)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetDocumentsInProgressOnTimeAsync(List<long> structureIds = null, string fromDate = "", string toDate = "", long? userId = null)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.InProgress && t.DueDate.HasValue && t.DueDate >= today);
                if (userId != null)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public List<CategoryCountModel> GetDocumentsCompletedOverdue(List<long> structureIds = null, string fromDate = "", string toDate = "", long? userId = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.Completed && t.DueDate.HasValue && t.DueDate < t.ClosedDate.Value.Date);
                if (userId != null)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetDocumentsCompletedOverdueAsync(List<long> structureIds = null, string fromDate = "", string toDate = "", long? userId = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.Completed && t.DueDate.HasValue && t.DueDate < t.ClosedDate.Value.Date);
                if (userId != null)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public List<CategoryCountModel> GetDocumentsCompletedOnTime(List<long> structureIds = null, string fromDate = "", string toDate = "", long? userId = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.Completed && t.DueDate.HasValue && t.DueDate >= t.ClosedDate.Value.Date);
                if (userId != null)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetDocumentsCompletedOnTimeAsync(List<long> structureIds = null, string fromDate = "", string toDate = "", long? userId = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.Completed && t.DueDate.HasValue && t.DueDate >= t.ClosedDate.Value.Date);
                if (userId != null)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public Task<int> GetNonDraftCount()
        {
            OpenDbContext();
            IQueryable<Document> query = _ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);
            return query.CountAsync();
        }

        public double GetApplicationPerDayCount()
        {
            using (var ctx = new CTSContext())
            {
                var list = (from doc in ctx.Document.AsNoTracking()
                            where doc.StatusId != (short)DocumentStatus.Draft
                            group doc by new { doc.CreatedDate.Year, doc.CreatedDate.Month, doc.CreatedDate.Day } into g
                            select g.Count());
                return list.Any() ? list.Average() : 0;
            }
        }

        public async Task<double> GetApplicationPerDayCountAsync()
        {
            using (var ctx = new CTSContext())
            {
                var list = (from doc in ctx.Document.AsNoTracking()
                            where doc.StatusId != (short)DocumentStatus.Draft
                            group doc by new { doc.CreatedDate.Year, doc.CreatedDate.Month, doc.CreatedDate.Day } into g
                            select g.Count());
                return await list.AnyAsync() ? await list.AverageAsync() : 0;
            }
        }

        public double GetApplicationPerMonthCount()
        {
            using (var ctx = new CTSContext())
            {
                var list = (from doc in ctx.Document.AsNoTracking()
                            where doc.StatusId != (short)DocumentStatus.Draft
                            group doc by new { doc.CreatedDate.Year, doc.CreatedDate.Month } into g
                            select g.Count());
                return list.Any() ? list.Average() : 0;
            }
        }

        public async Task<double> GetApplicationPerMonthCountAsync()
        {
            using (var ctx = new CTSContext())
            {
                var list = (from doc in ctx.Document.AsNoTracking()
                            where doc.StatusId != (short)DocumentStatus.Draft
                            group doc by new { doc.CreatedDate.Year, doc.CreatedDate.Month } into g
                            select g.Count());
                return await list.AnyAsync() ? await list.AverageAsync() : 0;
            }
        }

        public Task<int> GetInProgressCountByUser(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<short> categoryIds, long structureId, string fromDate = "", string toDate = "")
        {
            OpenDbContext();
            IQueryable<Document> query;
            if (Intalio.CTS.Core.Configuration.EnablePerStructure)
            {
                query = (from d in _ctx.Document
                         join t in _ctx.Transfer on d.Id equals t.DocumentId
                         join p in _ctx.Privacy on d.PrivacyId equals p.Id
                         where !d.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.InProgress &&
                         ((d.CreatedByUserId == userId && d.CreatedByStructureId == structureId) ||
                         ((t.ToUserId == userId && t.ToStructureId == structureId) || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && p.Level <= privacyLevel))
                         )
                         select d).AsNoTracking();
            }
            else
            {
                query = (from d in _ctx.Document
                         join t in _ctx.Transfer on d.Id equals t.DocumentId
                         join p in _ctx.Privacy on d.PrivacyId equals p.Id
                         where !d.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.InProgress &&
                         ((d.CreatedByUserId == userId ) ||
                         ((t.ToUserId == userId) || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && p.Level <= privacyLevel))
                         )
                         select d).AsNoTracking();
            }

            
            if (!categoryIds.IsNullOrEmpty())
            {
                query = query.Where(t => categoryIds.Contains(t.CategoryId));
            }
            if (!string.IsNullOrEmpty(fromDate))
            {
                DateTime from = DateTime.Parse(fromDate).Date;
                query = query.Where(t => t.CreatedDate.Date >= from);
            }
            if (!string.IsNullOrEmpty(toDate))
            {
                DateTime to = DateTime.Parse(toDate).Date;
                query = query.Where(t => t.CreatedDate.Date <= to);
            }
            return query.Select(t => t.Id).Distinct().CountAsync();
        }

        public Task<int> GetCompletedCountByUser(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<short> categoryIds, long structureId, string fromDate = "", string toDate = "")
        {
            OpenDbContext();
            IQueryable<Document> query = (from d in _ctx.Document
                                          join t in _ctx.Transfer on d.Id equals t.DocumentId
                                          join p in _ctx.Privacy on d.PrivacyId equals p.Id
                                          where d.ClosedDate.HasValue && t.StatusId == (short)DocumentStatus.Completed &&
                                          ((d.CreatedByUserId == userId && d.CreatedByStructureId == structureId) ||
                                          ((t.ToUserId == userId && t.ToStructureId == structureId) || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && p.Level <= privacyLevel))
                                          )
                                          select d).AsNoTracking();
            if (!categoryIds.IsNullOrEmpty())
            {
                query = query.Where(t => categoryIds.Contains(t.CategoryId));
            }
            if (!string.IsNullOrEmpty(fromDate))
            {
                DateTime from = DateTime.Parse(fromDate).Date;
                query = query.Where(t => t.CreatedDate.Date >= from);
            }
            if (!string.IsNullOrEmpty(toDate))
            {
                DateTime to = DateTime.Parse(toDate).Date;
                query = query.Where(t => t.CreatedDate.Date <= to);
            }
            return query.Select(t => t.Id).Distinct().CountAsync();
        }

        public DocumentsAverageDurationMonthWithTotalModel GetAverageDurationForCorrespondenceCompletionPerMonth(int year, List<short> categoryIds, long? structureId, long? userId)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.ClosedDate.HasValue && t.CreatedDate.Year == year);
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                if (structureId.HasValue)
                {
                    query = query.Where(t => t.CreatedByStructureId == structureId);
                }
                if (userId.HasValue)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }

                var list = query.Select(t => new { t.CreatedDate, t.ClosedDate, t.DueDate }).ToList();
                var months = new List<int>() { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };
                var averageByMonth = from month in months
                                     join r in list
                                     on month equals r.CreatedDate.Month
                                     into g
                                     select new DocumentsAverageDurationMonthModel
                                     {
                                         Month = month.ToString(),
                                         Average = g.Count() > 0 ? g.Average(x => (x.ClosedDate.Value.Date - x.CreatedDate.Date).TotalDays) : 0
                                     };
                return new DocumentsAverageDurationMonthWithTotalModel
                {
                    TotalAverage = list.Count() > 0 ? list.Average(x => (x.ClosedDate.Value.Date - x.CreatedDate.Date).TotalDays) : 0,
                    DocumentAverageDurationList = averageByMonth.ToList()
                };
            }
        }

        public async Task<DocumentsAverageDurationMonthWithTotalModel> GetAverageDurationForCorrespondenceCompletionPerMonthAsync(int year, List<short> categoryIds, long? structureId, long? userId)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.ClosedDate.HasValue && t.CreatedDate.Year == year);
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                if (structureId.HasValue && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    query = query.Where(t => t.CreatedByStructureId == structureId);
                }
                if (userId.HasValue)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }

                var list = await query.Select(t => new { t.CreatedDate, t.ClosedDate, t.DueDate }).ToListAsync();
                var months = new List<int>() { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };
                var averageByMonth = from month in months
                                     join r in list
                                     on month equals r.CreatedDate.Month
                                     into g
                                     select new DocumentsAverageDurationMonthModel
                                     {
                                         Month = month.ToString(),
                                         Average = g.Count() > 0 ? g.Average(x => (x.ClosedDate.Value.Date - x.CreatedDate.Date).TotalDays) : 0
                                     };
                return new DocumentsAverageDurationMonthWithTotalModel
                {
                    TotalAverage = list.Count() > 0 ? list.Average(x => (x.ClosedDate.Value.Date - x.CreatedDate.Date).TotalDays) : 0,
                    DocumentAverageDurationList = averageByMonth.ToList()
                };
            }
        }

        public List<DocumentAverageDurationForCorrespondenceModel> ListStructureAverageDurationForCorrespondenceCompletion(int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.ClosedDate.HasValue && t.CreatedDate.Year == year);
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                return query.Select(t => new
                {
                    StructureId = t.CreatedByStructureId,
                    ClosedDate = t.ClosedDate.Value.Date,
                    CreatedDate = t.CreatedDate.Date
                }).ToList().GroupBy(t => t.StructureId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    StructureId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => Convert.ToDouble((x.ClosedDate.Date - x.CreatedDate.Date).TotalDays)) : 0
                }).ToList();
            }
        }

        public async Task<List<DocumentAverageDurationForCorrespondenceModel>> ListStructureAverageDurationForCorrespondenceCompletionAsync(int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.ClosedDate.HasValue && t.CreatedDate.Year == year);
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                return (await query.Select(t => new
                {
                    StructureId = t.CreatedByStructureId,
                    ClosedDate = t.ClosedDate.Value.Date,
                    CreatedDate = t.CreatedDate.Date
                }).ToListAsync()).GroupBy(t => t.StructureId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    StructureId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => Convert.ToDouble((x.ClosedDate.Date - x.CreatedDate.Date).TotalDays)) : 0
                }).ToList();
            }
        }

        public List<DocumentAverageDurationForCorrespondenceModel> ListUserStructureAverageDurationForCorrespondenceCompletion(long structureId, int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.ClosedDate.HasValue &&
                t.CreatedDate.Year == year && t.CreatedByStructureId == structureId);

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                return query.Select(t => new
                {
                    UserId = t.CreatedByUserId,
                    ClosedDate = t.ClosedDate.Value.Date,
                    CreatedDate = t.CreatedDate.Date
                }).ToList().GroupBy(t => t.UserId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    UserId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => Convert.ToDouble((x.ClosedDate.Date - x.CreatedDate.Date).TotalDays)) : 0
                }).ToList();
            }
        }

        public async Task<List<DocumentAverageDurationForCorrespondenceModel>> ListUserStructureAverageDurationForCorrespondenceCompletionAsync(long structureId, int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.ClosedDate.HasValue &&
                t.CreatedDate.Year == year && t.CreatedByStructureId == structureId);

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                return (await query.Select(t => new
                {
                    UserId = t.CreatedByUserId,
                    ClosedDate = t.ClosedDate.Value.Date,
                    CreatedDate = t.CreatedDate.Date
                }).ToListAsync()).GroupBy(t => t.UserId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    UserId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => Convert.ToDouble((x.ClosedDate.Date - x.CreatedDate.Date).TotalDays)) : 0
                }).ToList();
            }
        }

        public DocumentsAverageDurationMonthWithTotalModel GetAverageDurationForCorrespondenceDelayPerMonth(int year, List<short> categoryIds, long? structureId, long? userId)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now;
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.CreatedDate.Year == year && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate.Date)));
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                if (structureId.HasValue && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    query = query.Where(t => t.CreatedByStructureId == structureId);
                }
                if (userId.HasValue)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                var list = query.Select(t => new { t.CreatedDate, t.ClosedDate, t.DueDate }).ToList();
                var months = new List<int>() { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };
                var averageByMonth = from month in months
                                     join r in list
                                     on month equals r.CreatedDate.Month
                                     into g
                                     select new DocumentsAverageDurationMonthModel
                                     {
                                         Month = month.ToString(),
                                         Average = g.Count() > 0 ? g.Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? (x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays : 0) :
                                         (x.DueDate.Value.Date < todayDate.Date ? (todayDate.Date - x.DueDate.Value.Date).TotalDays : 0)) : 0
                                     };
                return new DocumentsAverageDurationMonthWithTotalModel
                {
                    TotalAverage = list.Count() > 0 ? list.Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? (x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays : 0) :
                    (x.DueDate.Value.Date < todayDate.Date ? (todayDate.Date - x.DueDate.Value.Date).TotalDays : 0)) : 0,
                    DocumentAverageDurationList = averageByMonth.ToList()
                };
            }
        }

        public async Task<DocumentsAverageDurationMonthWithTotalModel> GetAverageDurationForCorrespondenceDelayPerMonthAsync(int year, List<short> categoryIds, long? structureId, long? userId)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now;
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.CreatedDate.Year == year && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate.Date)));
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                if (structureId.HasValue && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    query = query.Where(t => t.CreatedByStructureId == structureId);
                }
                if (userId.HasValue)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                var list = await query.Select(t => new { t.CreatedDate, t.ClosedDate, t.DueDate }).ToListAsync();
                var months = new List<int>() { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };
                var averageByMonth = from month in months
                                     join r in list
                                     on month equals r.CreatedDate.Month
                                     into g
                                     select new DocumentsAverageDurationMonthModel
                                     {
                                         Month = month.ToString(),
                                         Average = g.Count() > 0 ? g.Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? (x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays : 0) :
                                         (x.DueDate.Value.Date < todayDate.Date ? (todayDate.Date - x.DueDate.Value.Date).TotalDays : 0)) : 0
                                     };
                return new DocumentsAverageDurationMonthWithTotalModel
                {
                    TotalAverage = list.Count() > 0 ? list.Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? (x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays : 0) :
                    (x.DueDate.Value.Date < todayDate.Date ? (todayDate.Date - x.DueDate.Value.Date).TotalDays : 0)) : 0,
                    DocumentAverageDurationList = averageByMonth.ToList()
                };
            }
        }

        public List<DocumentAverageDurationForCorrespondenceModel> ListStructureAverageDurationForCorrespondenceDelay(int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now.Date;
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.CreatedDate.Year == year && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate)));
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                return query.Select(t => new
                {
                    StructureId = t.CreatedByStructureId,
                    ClosedDate = t.ClosedDate,
                    DueDate = t.DueDate
                }).ToList().GroupBy(t => t.StructureId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    StructureId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? Convert.ToDouble((x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays) : 0) :
                                        (x.DueDate.Value.Date < todayDate ? Convert.ToDouble((todayDate - x.DueDate.Value.Date).TotalDays) : 0)) : 0
                }).ToList();
            }
        }

        public async Task<List<DocumentAverageDurationForCorrespondenceModel>> ListStructureAverageDurationForCorrespondenceDelayAsync(int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now.Date;
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.CreatedDate.Year == year && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate)));
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                return (await query.Select(t => new
                {
                    StructureId = t.CreatedByStructureId,
                    ClosedDate = t.ClosedDate,
                    DueDate = t.DueDate
                }).ToListAsync()).GroupBy(t => t.StructureId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    StructureId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? Convert.ToDouble((x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays) : 0) :
                                        (x.DueDate.Value.Date < todayDate ? Convert.ToDouble((todayDate - x.DueDate.Value.Date).TotalDays) : 0)) : 0
                }).ToList();
            }
        }

        public List<DocumentAverageDurationForCorrespondenceModel> ListUserStructureAverageDurationForCorrespondenceDelay(long structureId, int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now.Date;
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.CreatedDate.Year == year && t.CreatedByStructureId == structureId && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate)));

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                return query.Select(t => new
                {
                    UserId = t.CreatedByUserId,
                    ClosedDate = t.ClosedDate,
                    DueDate = t.DueDate
                }).ToList().GroupBy(t => t.UserId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    UserId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? Convert.ToDouble((x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays) : 0) :
                                        (x.DueDate.Value.Date < todayDate ? Convert.ToDouble((todayDate - x.DueDate.Value.Date).TotalDays) : 0)) : 0
                }).ToList();
            }
        }

        public async Task<List<DocumentAverageDurationForCorrespondenceModel>> ListUserStructureAverageDurationForCorrespondenceDelayAsync(long structureId, int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now.Date;

                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft && t.CreatedDate.Year == year && t.CreatedByStructureId == structureId && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate)));

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.CategoryId));
                }
                return (await query.Select(t => new
                {
                    UserId = t.CreatedByUserId,
                    ClosedDate = t.ClosedDate,
                    DueDate = t.DueDate
                }).ToListAsync()).GroupBy(t => t.UserId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    UserId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? Convert.ToDouble((x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays) : 0) :
                                        (x.DueDate.Value.Date < todayDate ? Convert.ToDouble((todayDate - x.DueDate.Value.Date).TotalDays) : 0)) : 0
                }).ToList();
            }
        }

        //Commented for performance
        //public List<StatisticalCorrespondenceQueryModel> GetCreatedCountByStructure(List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedStructures, List<long> selectedUsers, Expression<Func<Document, bool>> filterExpression = null)
        //{
        //    using (var ctx = new CTSContext())
        //    {
        //        IQueryable<Document> query = ctx.Document.Include(t => t.Transfer).Include(t => t.CreatedByStructure)
        //            .Include(t => t.CreatedByUser).AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);

        //        if (selectedStructures.Count > 0)
        //        {
        //            query = query.Where(t => selectedStructures.Contains(t.CreatedByStructureId));
        //        }
        //        if (selectedUsers.Count > 0)
        //        {
        //            query = query.Where(t => selectedUsers.Contains(t.CreatedByUserId));
        //        }
        //        if (filterExpression != null)
        //        {
        //            query = query.Where(filterExpression);
        //        }
        //        if (!isAdmin)
        //        {
        //            query = query.Where(t => t.CreatedByUserId == userId
        //              || t.Transfer.Any(x => x.FromUserId.Value == userId)
        //              || t.Transfer.Any(x => x.ToUserId.Value == userId)
        //              || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
        //              : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
        //              : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
        //              && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));
        //        }
        //        return query.Select(t => (StatisticalCorrespondenceQueryModel)t).ToList();
        //    }
        //}

        public List<ValueText> GetCreatedByStructureCategoryCount(long createdByStructureId, List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedUsers, List<long> selectedStructures, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Where(t => t.StatusId != (short)DocumentStatus.Draft && t.CreatedByStructureId == createdByStructureId);


                if (selectedStructures.Count > 0)
                {
                    query = query.Where(t => selectedStructures.Contains(t.CreatedByStructureId));
                }

                if (selectedUsers.Count > 0)
                {
                    query = query.Where(t => selectedUsers.Contains(t.CreatedByUserId));
                }

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }

                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }

                if (!isAdmin)
                {
                    query = query.Where(t => t.CreatedByUserId == userId
                      || t.Transfer.Any(x => x.FromUserId.Value == userId)
                      || t.Transfer.Any(x => x.ToUserId.Value == userId)
                      || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                      && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));
                }
                var s = query.GroupBy(t => t.CategoryId).Select(t => new ValueText { Id = t.Key, Text = t.Count().ToString() }).ToList();
                return s;
            }
        }

        public async Task<List<ValueText>> GetCreatedByStructureCategoryCountAsync(long createdByStructureId, List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedUsers, List<long> selectedStructures, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Where(t => t.StatusId != (short)DocumentStatus.Draft && t.CreatedByStructureId == createdByStructureId);


                if (selectedStructures.Count > 0)
                {
                    query = query.Where(t => selectedStructures.Contains(t.CreatedByStructureId));
                }

                if (selectedUsers.Count > 0)
                {
                    query = query.Where(t => selectedUsers.Contains(t.CreatedByUserId));
                }

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }

                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }

                if (!isAdmin)
                {
                    query = query.Where(t => t.CreatedByUserId == userId
                      || t.Transfer.Any(x => x.FromUserId.Value == userId)
                      || t.Transfer.Any(x => x.ToUserId.Value == userId)
                      || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                      && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));
                }
                var s = await query.GroupBy(t => t.CategoryId).Select(t => new ValueText { Id = t.Key, Text = t.Count().ToString() }).ToListAsync();
                return s;
            }
        }

        public List<ValueText> GetCreatedByUserCategoryCount(long createdByStructureId, long createdByUserId, List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedUsers, List<long> selectedStructures, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Where(t => t.StatusId != (short)DocumentStatus.Draft
                && t.CreatedByStructureId == createdByStructureId && t.CreatedByUserId == createdByUserId);

                if (selectedStructures.Count > 0)
                {
                    query = query.Where(t => selectedStructures.Contains(t.CreatedByStructureId));
                }

                if (selectedUsers.Count > 0)
                {
                    query = query.Where(t => selectedUsers.Contains(t.CreatedByUserId));
                }

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }

                if (!isAdmin)
                {
                    query = query.Where(t => t.CreatedByUserId == userId
                      || t.Transfer.Any(x => x.FromUserId.Value == userId)
                      || t.Transfer.Any(x => x.ToUserId.Value == userId)
                      || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                      && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));
                }
                var s = query.GroupBy(t => t.CategoryId).Select(t => new ValueText { Id = t.Key, Text = t.Count().ToString() }).ToList();
                return s;
            }
        }

        public async Task<List<ValueText>> GetCreatedByUserCategoryCountAsync(long createdByStructureId, long createdByUserId, List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedUsers, List<long> selectedStructures, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Where(t => t.StatusId != (short)DocumentStatus.Draft
                && t.CreatedByStructureId == createdByStructureId && t.CreatedByUserId == createdByUserId);

                if (selectedStructures.Count > 0)
                {
                    query = query.Where(t => selectedStructures.Contains(t.CreatedByStructureId));
                }

                if (selectedUsers.Count > 0)
                {
                    query = query.Where(t => selectedUsers.Contains(t.CreatedByUserId));
                }

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }

                if (!isAdmin)
                {
                    query = query.Where(t => t.CreatedByUserId == userId
                      || t.Transfer.Any(x => x.FromUserId.Value == userId)
                      || t.Transfer.Any(x => x.ToUserId.Value == userId)
                      || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                      && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));
                }
                var s = await query.GroupBy(t => t.CategoryId).Select(t => new ValueText { Id = t.Key, Text = t.Count().ToString() }).ToListAsync();
                return s;
            }
        }

        public Task<int> GetCreatedByStructureIdsCount(List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedStructures, List<long> selectedUsers, Expression<Func<Document, bool>> filterExpression = null)
        {
            OpenDbContext();
            IQueryable<Document> query = _ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);

            if (selectedStructures.Count > 0)
            {
                query = query.Where(t => selectedStructures.Contains(t.CreatedByStructureId));
            }
            if (selectedUsers.Count > 0)
            {
                query = query.Where(t => selectedUsers.Contains(t.CreatedByUserId));
            }
            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            if (!isAdmin)
            {
                query = query.Where(t => t.CreatedByUserId == userId
                  || t.Transfer.Any(x => x.FromUserId.Value == userId)
                  || t.Transfer.Any(x => x.ToUserId.Value == userId)
                  || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                  : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                  : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                  && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));
            }
            return query.Select(t => t.CreatedByStructureId).Distinct().CountAsync();
        }

        public List<long> GetCreatedByStructureIds(int start, int pageSize, List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedStructures, List<long> selectedUsers, Expression<Func<Document, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);

                if (selectedStructures.Count > 0)
                {
                    query = query.Where(t => selectedStructures.Contains(t.CreatedByStructureId));
                }
                if (selectedUsers.Count > 0)
                {
                    query = query.Where(t => selectedUsers.Contains(t.CreatedByUserId));
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (!isAdmin)
                {
                    query = query.Where(t => t.CreatedByUserId == userId
                      || t.Transfer.Any(x => x.FromUserId.Value == userId)
                      || t.Transfer.Any(x => x.ToUserId.Value == userId)
                      || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                      && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));
                }
                return query.Select(t => t.CreatedByStructureId).Distinct().Skip(start).Take(pageSize).ToList();
            }
        }

        public async Task<List<long>> GetCreatedByStructureIdsAsync(int start, int pageSize, List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedStructures, List<long> selectedUsers, Expression<Func<Document, bool>> filterExpression = null, long? loggedInStructure = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);

                if (selectedStructures.Count > 0)
                {
                    query = query.Where(t => selectedStructures.Contains(t.CreatedByStructureId));
                }
                if (selectedUsers.Count > 0)
                {
                    query = query.Where(t => selectedUsers.Contains(t.CreatedByUserId));
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (!isAdmin)
                {
                    query = query.Where(t => (t.CreatedByUserId == userId && t.CreatedByStructureId == loggedInStructure)
                      || t.Transfer.Any(x => x.FromUserId.Value == userId && x.FromStructureId.Value == loggedInStructure)
                      || t.Transfer.Any(x => x.ToUserId.Value == userId && x.ToStructureId.Value == loggedInStructure)
                      || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                      && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));
                }
                return await query.Select(t => t.CreatedByStructureId).Distinct().Skip(start).Take(pageSize).ToListAsync();
            }
        }

        public Document GetDocumentCorrespondenceDetail(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, Expression<Func<Document, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.DocumentCarbonCopy).ThenInclude(t => t.Structure)
                    .Include(t => t.CreatedByUser)
                    .Include(t => t.Privacy).Include(t => t.Priority).AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }

                query = query.Where(t => t.CreatedByUserId == userId
                      || t.Transfer.Any(x => x.FromUserId.Value == userId)
                      || t.Transfer.Any(x => x.ToUserId.Value == userId)
                      || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                      && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));

                return query.OrderByDescending(t => t.CreatedDate).FirstOrDefault();
            }
        }

        public async Task<Document> GetDocumentCorrespondenceDetailAsync(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, Expression<Func<Document, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.DocumentCarbonCopy).ThenInclude(t => t.Structure)
                    .Include(t => t.CreatedByUser)
                    .Include(t => t.Privacy).Include(t => t.Priority).AsNoTracking().Where(t => t.StatusId != (short)DocumentStatus.Draft);

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }

                query = query.Where(t => t.CreatedByUserId == userId
                      || t.Transfer.Any(x => x.FromUserId.Value == userId)
                      || t.Transfer.Any(x => x.ToUserId.Value == userId)
                      || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                      && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));

                return await query.OrderByDescending(t => t.CreatedDate).FirstOrDefaultAsync();
            }
        }

        public Document FindByReferenceNumberSecured(string referenceNumber, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Where(t => t.StatusId != (short)DocumentStatus.Draft && t.ReferenceNumber == referenceNumber);

                query = query.Where(t => t.CreatedByUserId == userId
                      || t.Transfer.Any(x => x.FromUserId.Value == userId)
                      || t.Transfer.Any(x => x.ToUserId.Value == userId)
                      || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                      : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                      && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));

                return query.OrderByDescending(t => t.CreatedDate).FirstOrDefault();
            }
        }

        public async Task<Document> FindByReferenceNumberSecuredAsync(string referenceNumber, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, bool useAllStructures)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Where(t => t.StatusId != (short)DocumentStatus.Draft && t.ReferenceNumber == referenceNumber);

                if (useAllStructures)
                {

                    query = query.Where(t => t.CreatedByUserId == userId
                     || t.Transfer.Any(x => x.FromUserId.Value == userId)
                     || t.Transfer.Any(x => x.ToUserId.Value == userId)
                     || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                     : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                     : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                     && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));
                    

                }
                else
                {
                    query = query.Where(t => (t.CreatedByUserId == userId && structureIds.Contains(t.CreatedByStructureId))
                     || t.Transfer.Any(x => x.FromUserId.Value == userId && structureIds.Contains(x.FromStructureId.Value))
                     || t.Transfer.Any(x => x.ToUserId.Value == userId && structureIds.Contains(x.ToStructureId.Value))
                     || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? t.Transfer.Any(x => (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))
                     : t.Transfer.Any(x => !x.ToUserId.HasValue && (structureIds.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.ToStructureId.Value) || searchAssignedStructures.Contains(x.FromStructureId.Value))))
                     : t.Transfer.Any(x => !x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value)) && isStructureReceiver)
                     && (t.Privacy != null ? t.Privacy.Level <= privacyLevel : true)));

                }

                return await query.OrderByDescending(t => t.CreatedDate).FirstOrDefaultAsync();
            }
        }

        public List<Document> ListInProgressCorrespondences(int startIndex, int pageSize, Expression<Func<Document, bool>> filterExpression = null,
            Expression<Func<Document, bool>> stcFilterExpression = null, Expression<Func<Document, bool>> userFilterExpression = null, List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Document>();
                IQueryable<Document> query = ctx.Document
                    .Include(d => d.SendingEntity)
                    .Include(d => d.DocumentReceiverEntity)
                    .ThenInclude(d => d.Structure)
                    .Include(d => d.Transfer)
                    .ThenInclude(t => t.FromUser)
                    .Include(d => d.Transfer)
                    .ThenInclude(t => t.ToUser)
                    .Include(t => t.Transfer)
                    .ThenInclude(d => d.FromStructure)
                    .Include(t => t.Transfer)
                    .ThenInclude(d => d.ToStructure)
                    .AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (stcFilterExpression != null)
                {
                    query = query.Where(stcFilterExpression);
                }
                if (userFilterExpression != null)
                {
                    query = query.Where(userFilterExpression);
                }
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                query = query.Where(x => !x.ClosedDate.HasValue && x.StatusId == (int)Core.DocumentStatus.InProgress);
                return pageSize < 0 ? query.ToList() : query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public async Task<List<Document>> ListInProgressCorrespondencesAsync(int startIndex, int pageSize, Expression<Func<Document, bool>> filterExpression = null,
            Expression<Func<Document, bool>> stcFilterExpression = null, Expression<Func<Document, bool>> userFilterExpression = null, List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Document>();
                IQueryable<Document> query = ctx.Document
                    .Include(d => d.SendingEntity)
                    .Include(d => d.DocumentReceiverEntity)
                    .ThenInclude(d => d.Structure)
                    .Include(d => d.DocumentReceiverEntity)
                    .ThenInclude(d => d.EntityGroup)
                    .Include(d => d.Transfer)
                    .ThenInclude(t => t.FromUser)
                    .Include(d => d.Transfer)
                    .ThenInclude(t => t.ToUser)
                    .Include(t => t.Transfer)
                    .ThenInclude(d => d.FromStructure)
                    .Include(t => t.Transfer)
                    .ThenInclude(d => d.ToStructure)
                    .AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (stcFilterExpression != null)
                {
                    query = query.Where(stcFilterExpression);
                }
                if (userFilterExpression != null)
                {
                    query = query.Where(userFilterExpression);
                }
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                query = query.Where(x => !x.ClosedDate.HasValue && x.StatusId == (int)Core.DocumentStatus.InProgress);
                return pageSize < 0 ? await query.ToListAsync() : await query.Skip(startIndex).Take(pageSize).ToListAsync();
            }
        }

        public Task<int> GetInProgressCorrespondencesCount(Expression<Func<Document, bool>> filterExpression = null,
            Expression<Func<Document, bool>> stcFilterExpression = null, Expression<Func<Document, bool>> userFilterExpression = null)
        {
            OpenDbContext();
            IQueryable<Document> query = _ctx.Document.AsNoTracking();

            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            if (stcFilterExpression != null)
            {
                query = query.Where(stcFilterExpression);
            }
            if (userFilterExpression != null)
            {
                query = query.Where(userFilterExpression);
            }
            query = query.Where(x => !x.ClosedDate.HasValue && x.StatusId == (int)Core.DocumentStatus.InProgress);
            return query.CountAsync();
        }

        public List<Document> ListCompletedCorrespondences(int startIndex, int pageSize, Expression<Func<Document, bool>> filterExpression = null,
            Expression<Func<Document, bool>> stcFilterExpression = null, Expression<Func<Document, bool>> userFilterExpression = null, List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Document>();
                IQueryable<Document> query = ctx.Document
                    .Include(d => d.SendingEntity)
                    .Include(d => d.DocumentReceiverEntity)
                    .ThenInclude(d => d.Structure)
                    .Include(d => d.Transfer)
                    .ThenInclude(t => t.FromUser)
                    .Include(d => d.Transfer)
                    .ThenInclude(t => t.ToUser)
                    .Include(t => t.Transfer)
                    .ThenInclude(d => d.FromStructure)
                    .Include(t => t.Transfer)
                    .ThenInclude(d => d.ToStructure)
                    .AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (stcFilterExpression != null)
                {
                    query = query.Where(stcFilterExpression);
                }
                if (userFilterExpression != null)
                {
                    query = query.Where(userFilterExpression);
                }
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                query = query.Where(x => x.ClosedDate.HasValue && x.StatusId == (int)Core.DocumentStatus.Completed);
                return pageSize < 0 ? query.ToList() : query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public async Task<List<Document>> ListCompletedCorrespondencesAsync(int startIndex, int pageSize, Expression<Func<Document, bool>> filterExpression = null,
            Expression<Func<Document, bool>> stcFilterExpression = null, Expression<Func<Document, bool>> userFilterExpression = null, List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Document>();
                IQueryable<Document> query = ctx.Document
                    .Include(d => d.SendingEntity)
                    .Include(d => d.DocumentReceiverEntity)
                    .ThenInclude(d => d.Structure)
                    .Include(d => d.DocumentReceiverEntity)
                    .ThenInclude(d => d.EntityGroup)
                    .Include(d => d.Transfer)
                    .ThenInclude(t => t.FromUser)
                    .Include(d => d.Transfer)
                    .ThenInclude(t => t.ToUser)
                    .Include(t => t.Transfer)
                    .ThenInclude(d => d.FromStructure)
                    .Include(t => t.Transfer)
                    .ThenInclude(d => d.ToStructure)
                    .AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (stcFilterExpression != null)
                {
                    query = query.Where(stcFilterExpression);
                }
                if (userFilterExpression != null)
                {
                    query = query.Where(userFilterExpression);
                }
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                query = query.Where(x => x.ClosedDate.HasValue && x.StatusId == (int)Core.DocumentStatus.Completed);
                return pageSize < 0 ? await query.ToListAsync() : await query.Skip(startIndex).Take(pageSize).ToListAsync();
            }
        }

        public Task<int> GetCompletedCorrespondencesCount(Expression<Func<Document, bool>> filterExpression = null, Expression<Func<Document, bool>> stcFilterExpression = null,
            Expression<Func<Document, bool>> userFilterExpression = null)
        {
            OpenDbContext();
            IQueryable<Document> query = _ctx.Document.AsNoTracking();

            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            if (stcFilterExpression != null)
            {
                query = query.Where(stcFilterExpression);
            }
            if (userFilterExpression != null)
            {
                query = query.Where(userFilterExpression);
            }
            query = query.Where(x => x.ClosedDate.HasValue && x.StatusId == (int)Core.DocumentStatus.Completed);
            return query.CountAsync();
        }

        public List<Document> ListMyRequestsVip(int startIndex, long userId, Expression<Func<Document, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity).AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t => t.StatusId == (short)DocumentStatus.InProgress && t.CreatedByUserId == userId);
                return query.OrderByDescending(t => t.ModifiedDate).Skip(startIndex).Take(Constants.PAGING).ToList();
            }
        }

        public List<Document> ListClosedVip(int startIndex, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Expression<Func<Document, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity).AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t => t.ClosedDate.HasValue && (t.CreatedByUserId == userId ||
                t.Transfer.Any(x => x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))));
                return query.OrderByDescending(t => t.ClosedDate).Skip(startIndex).Take(Constants.PAGING).ToList();
            }
        }

        public List<Document> ListDraftVip(int startIndex, long userId, Expression<Func<Document, bool>> filterExpression = null, bool isStructureDraft = false, short? privacyLevel = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Document> query = ctx.Document.Include(t => t.SendingEntity).AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                //query = query.Where(t => t.CreatedByUserId == userId && t.StatusId == (short)DocumentStatus.Draft);

                //if (isStructureDraft)
                //    query = query.Where(t => t.StatusId == (short)DocumentStatus.Draft);
                //else
                //    query = query.Where(t => t.CreatedByUserId == userId && t.StatusId == (short)DocumentStatus.Draft);

                if (isStructureDraft)
                    query = query.Where(t => t.StatusId == (short)DocumentStatus.Draft && ((privacyLevel == null || t.PrivacyId <= privacyLevel || t.PrivacyId == null) || t.CreatedByUserId == userId));
                else
                    query = query.Where(t => t.CreatedByUserId == userId && t.StatusId == (short)DocumentStatus.Draft && (privacyLevel == null || t.PrivacyId <= privacyLevel || t.PrivacyId == null));

                //return query.OrderByDescending(t => t.CreatedDate).Skip(startIndex).Take(Constants.PAGING).ToList();
                return query.OrderByDescending(t => t.ModifiedDate).Skip(startIndex).Take(Constants.PAGING).ToList();
            }
        }

        public List<long> ListDeletedIds(List<long> ids)
        {
            List<long> dbIds = new List<long>();
            using (var ctx = new CTSContext())
            {
                dbIds = ctx.Document.AsNoTracking().Where(d => ids.Contains(d.Id)).Select(s => s.Id).ToList();
                return ids.Except(dbIds).ToList();
            }
        }

        public List<long> ListIdsOfCompletedDocumentWithPendingAttachments(int pageSize)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Document.AsNoTracking().Where(t => t.StatusId == (short)Core.DocumentStatus.Completed && t.Attachment.Any(a => a.Status == (int)Core.AttachmentStatus.Pending))
                    .OrderBy(t => t.Id).Select(t => t.Id).Take(pageSize).ToList();
            }
        }


        public void UpdateModifiedDateByListIds(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                for (int i = 0; i < ids.Count; i++)
                {
                    Document document = new Document { Id = ids[i] };
                    document.ModifiedDate = DateTime.Now;
                    ctx.Entry(document).Property(x => x.ModifiedDate).IsModified = true;
                }
                ctx.SaveChanges();
            }
        }

        public void UpdateDocumentIsSigned(long id, bool isSigned, string SignedVersion = null)
        {
            using (var ctx = new CTSContext())
            {
                Document document = new Document { Id = id };
                document.ModifiedDate = DateTime.Now;
                document.IsSigned = isSigned;
                document.SignedVersion = SignedVersion;
                ctx.Entry(document).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(document).Property(x => x.IsSigned).IsModified = true;
                ctx.Entry(document).Property(x => x.SignedVersion).IsModified = true;
                ctx.SaveChanges();
            }
        }
 
        public List<OCRPendingModel> ListOCRPendings(long start, short pageSize)
        {
            List<OCRPendingModel> retValue = new List<OCRPendingModel>();
            var query = string.Empty;
            switch (Configuration.DatabaseType)
            {
                case DatabaseType.MSSQL: query = string.Format(MSSQL_LISTOCRPENDINGS, start, pageSize); break;
                case DatabaseType.PostgreSQL: query = string.Format(Postgresql_LISTOCRPENDINGS, start, pageSize); break;
                case DatabaseType.Oracle: query = string.Format(Oracle_LISTOCRPENDINGS, start, pageSize); break;
            }
            using (var context = new CTSContext())
            using (var command = context.Database.GetDbConnection().CreateCommand())
            {
                command.CommandText = query;
                command.CommandType = CommandType.Text;
                context.Database.OpenConnection();
                using (var result = command.ExecuteReader())
                {
                    while (result.Read())
                    {
                        retValue.Add(new OCRPendingModel
                        {
                            Id = Convert.ToInt64(result["Id"]),
                            RecordId = Convert.ToInt64(result["RecordId"]),
                            AttachmentId = Convert.ToInt64(result["AttachmentId"]),
                            StorageAttachmentId = Convert.ToInt64(result["StorageAttachmentId"]),
                        });
                    }
                }
            }
            return retValue;
        }

        public async Task<(short CategoryId, long? AttachmentId, bool IsCompleted)> GetOriginalAttachmentIdAndCategoryId(long id)
        {
            using (var ctx = new CTSContext())
            {
                var item = await ctx.Document.Where(d => d.Id == id).Select(s => new { CategoryId = s.CategoryId, AttachmentId = s.AttachmentId, IsCompleted = s.ClosedDate.HasValue }).FirstOrDefaultAsync();
                if (item != null)
                {
                    return (item.CategoryId, item.AttachmentId, item.IsCompleted);
                }
                return default;
            }
        }

        public async Task<bool> IsRegistered(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Document.AsNoTracking().AnyAsync(t => t.Id == id && !string.IsNullOrEmpty(t.ReferenceNumber));
            }
        }
        public async Task<bool> IsOriginalDocumentSigned(long id, long attachmentId)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Document
                    .AsNoTracking().AnyAsync(t =>
                    (t.AttachmentId == null && t.IsSigned.Value && t.Id == id) ||
                    (t.AttachmentId != null && t.AttachmentId.Value == attachmentId && t.Id == id && t.IsSigned.Value));
            }
        }
        public bool IsCompleted(long id)
        {
            using (var ctx = new CTSContext())
            {
                var completedStatusId =  ctx.Status.FirstOrDefault(s => s.Name == "Completed").Id;

                return  ctx.Document.AsNoTracking().Any(t => t.Id == id && t.StatusId==completedStatusId);
            }
        }
        public async Task<List<WeeklySignedDocuments>> GetSignedDocumentsWeeklyStatisticsAsync(int weeks, long userId)
        {
            using (var ctx = new CTSContext())
            {
                DateTime startDate = DateTime.Now.AddDays(-weeks * 7);
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.IsSigned == true && t.CreatedDate >= startDate);

                if (userId > 0)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }

                var documentsList = query.ToList().GroupBy(t => new
                {
                    Year = t.CreatedDate.Year,
                    Month = t.CreatedDate.Month,
                    Week = Intalio.Core.Helper.GetWeekOfYear(t.CreatedDate)
                })
                    .Select(g => new WeeklySignedDocuments
                    {
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        WeekNumber = g.Key.Week,
                        Count = g.Count()
                    })
                    .OrderByDescending(t => t.Year)
                    .ThenByDescending(c => c.WeekNumber)
                    .ToList();
                return documentsList.ToList();
            }
        }
        public async Task<List<MonthlyCompletedDocuments>> GetCompletedDocumentsPerCategoryMonthlyStatisticsAsync(int months, long userId)
        {
            using (var ctx = new CTSContext())
            {
                DateTime startDate = DateTime.Now.AddMonths(-months);
                IQueryable<Document> query = ctx.Document.AsNoTracking().Where(t => t.StatusId == (short)DocumentStatus.Completed && t.CreatedDate >= startDate);

                if (userId > 0)
                {
                    query = query.Where(t => t.CreatedByUserId == userId);
                }
                var list = await query.Select(t => new { t.CreatedDate, t.ClosedDate, t.DueDate, t.CategoryId }).ToListAsync();

                var documentsList = query.GroupBy(t => new { t.CreatedDate.Year, t.CreatedDate.Month, t.CategoryId })
                    .Select(g => new MonthlyCompletedDocuments
                    {
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        CategoryId = g.Key.CategoryId,
                        Count = g.Count()
                    })
                    .OrderByDescending(t => t.Year)
                    .ThenByDescending(t => t.Month)
                    .ToList();
                return documentsList.ToList();
            }
        }
        public async Task<List<MonthlyDocumentActionsModel>> GetDocumentsActionsMonthlyStatisticsAsync(int months, long userId)
        {
            using (var ctx = new CTSContext())
            {
                DateTime startDate = DateTime.Now.AddMonths(-months);
                IQueryable<ActivityLog> query = ctx.ActivityLog.AsNoTracking().Where(t => t.CreatedDate >= startDate);

                var activityLogList = query.GroupBy(t => new { t.CreatedDate.Year, t.CreatedDate.Month})
                    .Select(g => new MonthlyDocumentActionsModel
                    {
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        Count = g.Count()
                    })
                    .OrderByDescending(t => t.Year)
                    .ThenByDescending(t => t.Month)
                    .ToList();
                return activityLogList.ToList();
            }
        }
        public Task<int> GetRegisteredDocumentsByMeTodayCountAsync(long userId)
        {
            OpenDbContext();
            DateTime todayDate = DateTime.Now;

            IQueryable<Document> query = _ctx.Document.AsNoTracking().Where(t => !string.IsNullOrEmpty(t.ReferenceNumber) && t.CreatedByUserId == userId && t.CreatedDate == DateTime.Now);

            return query.CountAsync();
        }
        public Task<int> GetTransferredTransfersByMeTodayAsync(long userId)
        {
            OpenDbContext();

            IQueryable<Transfer> query = _ctx.Transfer.AsNoTracking().Where(t => t.FromUserId == userId && t.CreatedDate == DateTime.Now);

            return query.CountAsync();
        }
        public Task<int> GetTransferredTransfersToMeTodayAsync(long userId)
        {
            OpenDbContext();

            IQueryable<Transfer> query = _ctx.Transfer.AsNoTracking().Where(t => t.ToUserId == userId && t.CreatedDate == DateTime.Now);

            return query.CountAsync();
        }
        public Task<int> GetOverDueTransfersToUserAsync(long userId)
        {
            OpenDbContext();

            IQueryable<Transfer> query = _ctx.Transfer.AsNoTracking().Where(t => t.ToUserId == userId && t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate < t.ClosedDate.Value.Date);

            return query.CountAsync();
        }
        public void UpdateDocumentIncludeTransfers()
        {
            using (var ctx = new CTSContext())
            {
                ctx.Entry(this).State = EntityState.Modified;
                foreach (var item in this.Transfer)
                {
                    item.Update();
                }
                ctx.SaveChanges();
            }
        }


        public async Task<List<Document>> ListOutgoingFromDepartmentAsync(int startIndex, int pageSize,int departmentId,bool getAll, List<SortExpression<Document>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Document>();
                IQueryable<Document> query = ctx.Document
                    .Include(d=>d.LinkedDocumentDocument)
                    .ThenInclude(d=>d.LinkedDocumentNavigation)
                    .Include(d => d.DocumentReceiverEntity)
                    .ThenInclude(d => d.Structure)
                    .Include(d => d.DocumentReceiverEntity)
                    .ThenInclude(d => d.EntityGroup)
                    .AsNoTracking();

               
                //Outgoing categoryId=2 
                query = query.Where(x => x.CategoryId==2 && !string.IsNullOrEmpty(x.DocumentForm.Form) &&  x.DocumentForm.Form.Contains(string.Format("\"department\":{0}", departmentId.ToString())));

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (getAll)
                    return await query.ToListAsync();
                else
                    return pageSize < 0 ? await query.ToListAsync() : await query.Skip(startIndex).Take(pageSize).ToListAsync();
            }
        }

        public Task<int> GetOutgoingFromDepartmentCount(int departmentId)
        {
            OpenDbContext();
            IQueryable<Document> query = _ctx.Document.AsNoTracking();

                query = query.Where(x => x.CategoryId==2 && !string.IsNullOrEmpty(x.DocumentForm.Form) &&  x.DocumentForm.Form.Contains(string.Format("\"department\":{0}", departmentId.ToString())));
            return query.CountAsync();
        }
        public async Task LockDocumentAsync(int documentId, int userId)
        {
            using var context = new CTSContext();
            using var transaction = await context.Database.BeginTransactionAsync();
            try
            {
                DocumentLock newLock = new()
                {
                    DocumentId = documentId,
                    UserId = userId
                };
                context.DocumentLock.Add(newLock);
                await context.SaveChangesAsync();

                //Now we lock existing entries related to the corrspondence
                //Notes
                var notes = await context.Note
                    .Where(m => m.DocumentId == documentId && m.DocumentLock == null)
                    .ToListAsync();
                notes.ForEach(m => m.DocumentLockId = newLock.Id);

                //Non archived attachments
                
                var nonArchivedAttachments = await context.NonArchivedAttachments
                    .Where(m => m.DocumentId == documentId && m.DocumentLock == null)
                    .ToListAsync();
                nonArchivedAttachments.ForEach(m => m.DocumentLockId = newLock.Id);

                //Linked Document
                var linkedDocuments = await context.LinkedDocument
                    .Where(m => m.DocumentId == documentId && m.DocumentLock == null)
                    .ToListAsync();
                linkedDocuments.ForEach(m => m.DocumentLockId = newLock.Id);

                //Attachments
                var attachments = await context.Attachment
                    .Where(m => m.DocumentId == documentId && m.DocumentLock == null)
                    .ToListAsync();
                attachments.ForEach(m => m.DocumentLockId = newLock.Id);

                await context.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch
            {
                throw;
            }
        }

        public async Task<long?> LockedByUserAsync(int documentId)
        {
            using var context = new CTSContext();
            var lastLock = await context.DocumentLock
                .AsNoTracking()
                .Where(l => l.DocumentId == documentId)
                .OrderByDescending(l => l.LockedAt)
                .FirstOrDefaultAsync();

            return lastLock?.UserId;
        }

        public async Task<string> UnlockDocumentAsync(int documentId, int userId)
        {
            //If the user is unlocking it
            //And the last one who has locked it is him, then
            //it is obvious that he has permission to unlock it
            using var context = new CTSContext();

            var lastLock = await context.DocumentLock
                .Where(l => l.DocumentId == documentId)
                .OrderByDescending(l => l.LockedAt)
                .FirstOrDefaultAsync();

            if (lastLock is null)
            {
                return "DocumentNotLocked";
            }

            if (lastLock.UserId != userId)
            {
                return $"DocumentLockedByDifferentUser-{lastLock.UserId}";
            }

            //Now we lock existing entries related to the corrspondence
            //Notes
            var notes = await context.Note
                .Where(m => m.DocumentId == documentId && m.DocumentLockId == lastLock.Id)
                .ToListAsync();
            notes.ForEach(m => m.DocumentLockId = null);

            //Linked Document
            var linkedDocuments = await context.LinkedDocument
                .Where(m => m.DocumentId == documentId && m.DocumentLockId == lastLock.Id)
                .ToListAsync();
            linkedDocuments.ForEach(m => m.DocumentLockId = null);

            //Attachments
            var attachments = await context.Attachment
                .Where(m => m.DocumentId == documentId && m.DocumentLockId == lastLock.Id)
                .ToListAsync();
            attachments.ForEach(m => m.DocumentLockId = null);

            context.Remove(lastLock);
            await context.SaveChangesAsync();
            return "DocumentUnlocked";
        }

    

        #endregion

        #region Dispose

        public void Dispose()
        {
            if (_ctx != null)
            {
                _ctx.Dispose();
            }
        }

        #endregion

        #region Conversion

        public static implicit operator DocumentDetailsModel(Document item)
        {
            DocumentDetailsModel retValue = null;
            if (item != null)
            {
                List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();

                foreach (var receiver in item.DocumentReceiverEntity)
                {
                    long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                    bool isEntityGroup = receiver.EntityGroupId.HasValue;
                    string text = string.Empty;

                    receivers.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        IsEntityGroup = isEntityGroup
                    });
                }
                var lang = Helper.GetLanguage();
                retValue = new DocumentDetailsModel
                {
                    Id = item.Id,
                    CategoryId = item.CategoryId,
                    ReferenceNumber = item.ReferenceNumber,
                    Subject = item.Subject,
                    Status = item.StatusId,
                    CustomAttributes = item.Category.CustomAttribute,
                    CustomAttributesTranslation = item.Category.CustomAttribute,
                    BasicAttributes = item.Category.BasicAttribute,
                    FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                    CarbonCopy = item.DocumentCarbonCopy.Count > 0 ? item.DocumentCarbonCopy.Select(x => x.StructureId).ToList() : new List<long>(),
                    ClassificationId = item.ClassificationId,
                    DocumentTypeId = item.DocumentTypeId,
                    DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                    ImportanceId = item.ImportanceId,
                    PriorityId = item.PriorityId.Value,
                    PrivacyId = item.PrivacyId.Value,
                    SendingEntityId = item.SendingEntityId.Value,
                    Receivers = receivers,
                    CreatedByUser = item.CreatedByUser == null ? String.Empty :
                    (lang == Language.EN ?
                    $"{item.CreatedByUser?.Firstname} {item.CreatedByUser?.Lastname}" : $"{IdentityHelperExtension.GetFullName(item.CreatedByUser.Id, lang)}"),
                    CreatedByStructureId = item.CreatedByStructureId,
                    IsLocked = item.IsLocked
                };
            }
            return retValue;
        }

        public static implicit operator DocumentCrawlingModel(Document item)
        {
            DocumentCrawlingModel retValue = null;
            if (item != null)
            {
                string ocrContent = string.Empty;
                var ocrContents = item.OcrContent.ToList();

                foreach (var ocrItem in ocrContents)
                {
                    ocrContent += ocrItem.Content + " ";
                }
                retValue = new DocumentCrawlingModel
                {
                    Id = item.Id,
                    CategoryId = item.CategoryId,
                    ReferenceNumber = item.ReferenceNumber,
                    ReferenceSequence = item.ReferenceSequence,
                    SendingEntityId = item.SendingEntityId,
                    StatusId = item.StatusId,
                    PriorityId = item.PriorityId,
                    PrivacyId = item.PrivacyId,
                    Subject = item.Subject,
                    ClassificationId = item.ClassificationId,
                    ImportanceId = item.ImportanceId,
                    DocumentTypeId = item.DocumentTypeId,
                    DueDate = item.DueDate,
                    ByTemplate = item.ByTemplate,
                    AttachmentId = item.AttachmentId,
                    CreatedDate = item.CreatedDate,
                    ModifiedDate = item.ModifiedDate,
                    CreatedByUserId = item.CreatedByUserId,
                    ClosedDate = item.ClosedDate,
                    CreatedByStructureId = item.CreatedByStructureId,
                    IsLocked = item.IsLocked,
                    Transfers = item.Transfer.Any() ? item.Transfer.Select(s => (TransferCrawlingModel)s).ToList() : null,
                    PrivacyLevel = item.Privacy != null ? item.Privacy.Level : null,
                    DocumentReceiverEntity = item.DocumentReceiverEntity.Where(t => t.StructureId.HasValue).Select(t => t.StructureId.Value).ToList(),
                    Form = item.DocumentForm != null && !string.IsNullOrEmpty(item.DocumentForm.Form) && item.DocumentForm.Form != "[]" ? DocumentCrawlingModel.DeserializeToDictionary(item.DocumentForm.Form) : null,
                    InitialCreatedDate = item.InitialCreatedDate,
                    OcrContent = ocrContent.Trim(),
                    Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                    Keyword = item.DocumentForm != null && !string.IsNullOrEmpty(item.DocumentForm.Keyword) ? item.DocumentForm.Keyword.Split(Constants.SPLITTER).ToList() : new List<string>()
                };
            }
            return retValue;
        }

        //Commented for performance
        //public static implicit operator StatisticalCorrespondenceQueryModel(Document item)
        //{
        //    StatisticalCorrespondenceQueryModel retValue = null;
        //    if (item != null)
        //    {
        //        retValue = new StatisticalCorrespondenceQueryModel
        //        {
        //            CreatedByUserId = item.CreatedByUserId,
        //            CreatedByStructureId = item.CreatedByStructureId,
        //            CategoryId = item.CategoryId,
        //            CreatedByStructure = item.CreatedByStructure,
        //            CreatedByUser = item.CreatedByUser,
        //            Transfer = item.Transfer.ToList()
        //        };
        //    }
        //    return retValue;
        //}

        #endregion
    }
}
