﻿using Intalio.Core;
using Intalio.Core.Model;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using Serilog;

namespace Intalio.CTS.Core.Utility
{
    public static class IdentityHelperExtension
    {
        public static Intalio.CTS.Core.Model.UserModel GetUser(long userId, [NotNull] string token, Language language)
        {
            DefaultInterpolatedStringHandler defaultInterpolatedStringHandler = new DefaultInterpolatedStringHandler(16, 2);
            defaultInterpolatedStringHandler.AppendFormatted(Configuration.IdentityAuthorityUrl);
            defaultInterpolatedStringHandler.AppendLiteral("/api/GetUser?id=");
            defaultInterpolatedStringHandler.AppendFormatted(userId);
            defaultInterpolatedStringHandler.AppendFormatted("&language=");
            defaultInterpolatedStringHandler.AppendFormatted(language);
            return Intalio.Core.Helper.HttpGet<Intalio.CTS.Core.Model.UserModel>(defaultInterpolatedStringHandler.ToStringAndClear(), token);
        }

        public static List<Intalio.CTS.Core.Model.RoleModel> GetAllRoles([NotNull] string token)
        {
            DefaultInterpolatedStringHandler defaultInterpolatedStringHandler = new DefaultInterpolatedStringHandler(16, 2);
            defaultInterpolatedStringHandler.AppendFormatted(Configuration.IdentityAuthorityUrl);
            defaultInterpolatedStringHandler.AppendLiteral("/api/ListAllRoles");
            return Intalio.Core.Helper.HttpGet<List<Intalio.CTS.Core.Model.RoleModel>>(defaultInterpolatedStringHandler.ToStringAndClear(), token);
        }

        public static UserName GetUserName(long userId, Language lang)
        {
            var user = GetUser(userId, Configuration.IdentityAccessToken, lang);
            var userName = new UserName();
            var userAttributes = user?.Attributes;

            if (user == null) 
            {
                Log.Warning($"ERROR: IdentityHelperExtension.GetUserName returned user us null. userId: {userId}, token: {Configuration.IdentityAccessToken}, lang: {lang.ToString()}");
                userName.FirstName = "";
                userName.LastName = "";
                return userName;
            }


            if (userAttributes?.Count > 0)
            {
                if (lang == Language.AR)
                {

                    userName.FirstName = userAttributes.SingleOrDefault(x => x.Text == Configuration.FirstNameAr)?.Value ?? String.Empty;
                    userName.MiddleName = userAttributes.SingleOrDefault(x => x.Text == Configuration.MiddleNameAr)?.Value ?? String.Empty;
                    userName.LastName = userAttributes.SingleOrDefault(x => x.Text == Configuration.LastNameAr)?.Value ?? String.Empty;
                }
                else if (lang == Language.FR)
                {
                    userName.FirstName = userAttributes.SingleOrDefault(x => x.Text == Configuration.FirstNameFr)?.Value ?? String.Empty;
                    userName.MiddleName = userAttributes.SingleOrDefault(x => x.Text == Configuration.MiddleNameFr)?.Value ?? String.Empty;
                    userName.LastName = userAttributes.SingleOrDefault(x => x.Text == Configuration.LastNameFr)?.Value ?? String.Empty;


                }
            }
            if (string.IsNullOrEmpty(userName.FirstName))
            {
                userName.FirstName = user.FirstName;
                userName.LastName = user.LastName;
            }
            return userName;
        }
        public static string GetFullName(long userId, Language lang)
        {
            var userName = GetUserName(userId, lang);
            return $"{userName.FirstName} {userName.LastName}";
        }

        public static string GetUserInboxMode(string keyword, long userId)
        {
            var inboxMode = new DAL.UserParameter().FindByUserId(keyword, userId);
            if (inboxMode == null)
                return "InboxDefault";
            return inboxMode.Content;
        }
        public static string GetTrippleFullName(long userId, Language lang)
        {
            var userName = GetUserName(userId, lang);
            return $"{userName.FirstName} {userName.MiddleName} {userName.LastName}";
        }

        public static UserStrucureModel GetUserStrucurePermission(long UserId, Language language, bool cache = true)
        {
            //query db to get strucure
            long? CurrentStructureId = new DAL.UserStructure().getLoggedInStrucureId(UserId);

            Model.UserModel user = null;
            user = GetUser(UserId, Configuration.IdentityAccessToken, Language.EN);

            if (CurrentStructureId == null || !user.Structures.Any(x => x.Id == CurrentStructureId))
            {
                if (user != null && (user.DefaultStructureId != null || user.Structures.Count() > 0))
                {
                    CurrentStructureId = user.DefaultStructureId != null ? user.DefaultStructureId.Value : user.Structures[0].Id;

                    var existingStructure = new DAL.UserStructure().FindByStructureIdAndUserId(CurrentStructureId ?? 0, UserId);
                    if (existingStructure == null)
                        ManageUser.InsertLoggedInStructure(UserId, CurrentStructureId.Value, true);

                    else
                    {
                        existingStructure.IsLoggedInStructure = true;
                        existingStructure.Update();
                    }
                }
            }

            //check cache 
            UserStrucureModel strucurePrivacy = null;
            if (cache)
                strucurePrivacy = new CacheUtility().GetCachedItem<UserStrucureModel>($"StrucurePrivacy-" + CurrentStructureId + "-" + UserId);
            // check if there is no cached strucure 
            if (strucurePrivacy == null)
            {
                //call IAM API to get strucure attributes
                if (user == null)
                    user = GetUser(UserId, Configuration.IdentityAccessToken, language);

                // if the strucure is the default strucure get from metadata attributes
                strucurePrivacy = new UserStrucureModel();
                if (CurrentStructureId == user.DefaultStructureId || CurrentStructureId == null)
                {
                    strucurePrivacy.IsStrucureSender = Convert.ToBoolean(user.Attributes.Where(x => x.Text == "StructureSender").FirstOrDefault().Value);
                    strucurePrivacy.IsStrucureReceiver = Convert.ToBoolean(user.Attributes.Where(x => x.Text == "StructureReceiver").FirstOrDefault().Value);
                    strucurePrivacy.PrivacyLevel = Convert.ToInt16(user.Attributes.Where(x => x.Text == "Privacy").FirstOrDefault().Value);
                    strucurePrivacy.RoleId = Convert.ToInt32(user.Attributes.Where(x => x.Text == "StructureRole").FirstOrDefault().Value);
                    strucurePrivacy.AllowEditSigned = user.Attributes.Where(x => x.Text == "AllowEditSigned").FirstOrDefault() != null ? Convert.ToBoolean(user.Attributes.Where(x => x.Text == "AllowEditSigned").FirstOrDefault().Value) : false;
                    strucurePrivacy.EditDesignatedPerson = user.Attributes.Where(x => x.Text == "EditDesignatedPerson").FirstOrDefault() != null ? Convert.ToBoolean(user.Attributes.Where(x => x.Text == "EditDesignatedPerson").FirstOrDefault().Value) : false;



                }
                else
                {
                    foreach (var structure in user.Structures)
                    {
                        if (structure.Id == CurrentStructureId)
                        {
                            strucurePrivacy.IsStrucureSender = Convert.ToBoolean(structure.UserAttributes.Where(x => x.Text == "StructureSender").FirstOrDefault().Value);
                            strucurePrivacy.IsStrucureReceiver = Convert.ToBoolean(structure.UserAttributes.Where(x => x.Text == "StructureReceiver").FirstOrDefault().Value);
                            strucurePrivacy.PrivacyLevel = Convert.ToInt16(structure.UserAttributes.Where(x => x.Text == "Privacy").FirstOrDefault().Value);
                            strucurePrivacy.RoleId = Convert.ToInt32(structure.UserAttributes.Where(x => x.Text == "StructureRole").FirstOrDefault().Value);
                            strucurePrivacy.AllowEditSigned = Convert.ToBoolean(structure.UserAttributes.Where(x => x.Text == "AllowEditSigned").FirstOrDefault().Value);
                            strucurePrivacy.EditDesignatedPerson = Convert.ToBoolean(structure.UserAttributes.Where(x => x.Text == "EditDesignatedPerson").FirstOrDefault().Value);

                        }
                    }
                }

                strucurePrivacy.MenusBreakInheritance = Intalio.Core.API.ManageMenu.GetMenuBreakInheritanceByUserId(user.Id);
                strucurePrivacy.TabsBreakInheritance = Intalio.Core.API.ManageTab.GetTabBreakInheritanceByUserId(user.Id);
                strucurePrivacy.NodesBreakInheritance = Intalio.Core.API.ManageNode.GetNodeBreakInheritanceByUserId(user.Id);
                strucurePrivacy.ActionsBreakInheritance = Intalio.Core.API.ManageAction.GetActionBreakInheritanceByUserId(user.Id);
                strucurePrivacy.IsSecurityBreakedInheritance = Configuration.EnablePerStructure ? ManageUser.CheckBreakInheritanceInStructure((long)user.Id, CurrentStructureId) : Intalio.Core.API.ManageUser.CheckBreakInheritance((long)user.Id);

                    // cache strucure privacy data
                    if (strucurePrivacy != null && cache)
                {
                    new CacheUtility().InsertCachedItem(strucurePrivacy, $"StrucurePrivacy-" + CurrentStructureId + "-" + UserId);
                }
            }
            return strucurePrivacy;
        }

        public static bool IsInRole(int acceptedRole, long userId)
        {
            var currentLanguage = Language.EN;
            var language = CultureInfo.CurrentUICulture.Name.Replace("en-GB", "en");
            if (language == "ar")
            {
                currentLanguage = Language.AR;
            }
            else if (language == "fr")
            {
                currentLanguage = Language.FR;
            }

            if (GetUserStrucurePermission(userId, currentLanguage).RoleId == acceptedRole)
            {
                return true;
            }
            else { return false; }
        }

        public static bool IsHasFolloUpPermission(int roleId, long userId, long? structureId)
        {
            var userSecurity = IdentityHelperExtension.GetUserStrucurePermission(userId, Language.EN);
            if (userSecurity == null)
            {
                return false;
            }
            var isSecurityBreakedInheritance = false;
            if (userSecurity.IsSecurityBreakedInheritance != null && (bool)userSecurity.IsSecurityBreakedInheritance)
            {
                isSecurityBreakedInheritance = true;

            }
            var followUpCategorySecurity = ManageCategory.ListByRoleIdUserIdStructureId(roleId, userId, structureId, isSecurityBreakedInheritance).Select(d => d.Id).ToList();
            if (followUpCategorySecurity.Count > 0 && followUpCategorySecurity.Contains((short)Configuration.FollowUpCategory))
            {
                return true;
            }

            return false;
        }

        public static long? getCurrentLoggedInStructure(long userId)
        {
            long? CurrentStructureId = new DAL.UserStructure().getLoggedInStrucureId(userId);
            return CurrentStructureId;
        }
        public static short? getCurrentLoggedInStructurePrivacyLevel(long userId)
        {
            short? CurrentStructurePrivacyId = new DAL.UserStructure().getLoggedInStrucurePrivacyId(userId);
            return CurrentStructurePrivacyId;
        }

        public static (long,string) getCurrentLoggedInStructureInfo(long userId)
        {
            var CurrentStructure = new DAL.UserStructure().getLoggedInStrucureInfo(userId);
            return (CurrentStructure.Item1, CurrentStructure.Item2);
        }
    }
}
