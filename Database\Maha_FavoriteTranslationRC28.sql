﻿IF NOT EXISTS (
    SELECT 1 FROM [dbo].[TranslatorDictionary] WHERE Keyword = N'FavoriteStructureAddedSuccessfully'
)
BEGIN
    INSERT INTO [dbo].[TranslatorDictionary] (Keyword, EN, FR, AR, IsSystem)
    VALUES (
        N'FavoriteStructureAddedSuccessfully',N'Favorite Structure Added Successfully', N'Structure favorite ajoutée avec succès',    
        N'تمت إضافة الهيكل المفضل بنجاح',         
        0                             
    );
END;

IF NOT EXISTS (
    SELECT 1 FROM [dbo].[TranslatorDictionary] WHERE Keyword = N'UnexpectedErrorOccurred'
)
BEGIN
    INSERT INTO [dbo].[TranslatorDictionary] (Keyword, EN, FR, AR, IsSystem)
    VALUES (
        N'UnexpectedErrorOccurred',N'Unexpected Error Occurred', N'Unexpected Error Occurred',    
        N'حدث خطأ غير متوقع',         
        0                             
    );
END;