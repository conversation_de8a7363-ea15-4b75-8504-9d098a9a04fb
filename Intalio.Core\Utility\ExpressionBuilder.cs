using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
namespace Intalio.Core
{
    public enum ExpressionBuilderOperator
    {
        And,
        Or
    }

    public enum Operator
    {
        Contains,
        GreaterThan,
        GreaterThanOrEqual,
        LessThan,
        LessThanOr<PERSON>qualTo,
        StartsWith,
        EndsWith,
        Equals,
        NotEqual,
        Any,
        AnyShort
    }

    public class ExpressionBuilderFilter
    {
        public string PropertyName { get; set; }
        public object Value { get; set; }
        public Operator Operator { get; set; } = Operator.Contains;
    }

    public class ExpressionBuilderFilters : List<ExpressionBuilderFilter>
    {
        public void Add(string Name, object Value, Operator op)
        {
            this.Add(new ExpressionBuilderFilter() { PropertyName = Name, Value = Value, Operator = op });
        }
    }

    public static class ExpressionBuilder
    {
        private static MethodInfo containsMethod = typeof(string).GetMethod("Contains", new[] { typeof(string) });
        private static MethodInfo containsMethodInteger = typeof(List<long>).GetMethod("Contains", new[] { typeof(long) });
        private static MethodInfo containsMethodShort = typeof(List<short>).GetMethod("Contains", new[] { typeof(short) });
        private static MethodInfo startsWithMethod = typeof(string).GetMethod("StartsWith", new[] { typeof(string) });
        private static MethodInfo endsWithMethod = typeof(string).GetMethod("EndsWith", new[] { typeof(string) });

        public static Expression<Func<T, bool>> GetExpression<T>(List<ExpressionBuilderFilter> filters, ExpressionBuilderOperator @operator = ExpressionBuilderOperator.And)
        {
            if (filters.Count == 0)
            {
                return null;
            }
            ParameterExpression param = Expression.Parameter(typeof(T), "parm");

            Expression exp = null;

            if (filters.Count == 1)
            {
                exp = GetExpression(param, filters[0]);
            }
            else
            {
                if (filters.Count == 2)
                {
                    exp = GetExpression<T>(param, filters[0], filters[1], @operator);
                }
                else
                {
                    while (filters.Count > 0)
                    {
                        var f1 = filters[0];
                        var f2 = filters[1];
                        if (exp == null)
                        {
                            exp = GetExpression<T>(param, filters[0], filters[1], @operator);
                        }
                        else
                        {
                            exp = @operator == ExpressionBuilderOperator.And ? Expression.AndAlso(exp, GetExpression<T>(param, filters[0], filters[1])) : Expression.Or(exp, GetExpression<T>(param, filters[0], filters[1], @operator)); // Add to our existing expression
                        }
                        filters.Remove(f1);
                        filters.Remove(f2);
                        if (filters.Count == 1)
                        {
                            exp = @operator == ExpressionBuilderOperator.And ? Expression.AndAlso(exp, GetExpression(param, filters[0])) : Expression.Or(exp, GetExpression(param, filters[0])); ;
                            filters.RemoveAt(0);
                        }
                    }
                }
            }
            return Expression.Lambda<Func<T, bool>>(exp, param);
        }

        private static Expression GetExpression(ParameterExpression param, ExpressionBuilderFilter filter)
        {
            MemberExpression member = null;

            if (filter.Operator == Operator.Any)
            {
                if (filter.PropertyName.Contains("."))
                {
                    var properties = filter.PropertyName.Split(".");
                    member = Expression.Property(param, properties[0]);
                    member = properties.Skip(1).Aggregate(member, (current, propertyName) => Expression.Property(current, propertyName));
                }
                else
                {
                    member = Expression.Property(param, filter.PropertyName);
                }
                return Expression.Call(Expression.Constant(filter.Value), containsMethodInteger, Expression.Convert(member, typeof(long)));
            }

            else if (filter.Operator == Operator.AnyShort)
            {
                if (filter.PropertyName.Contains("."))
                {
                    var properties = filter.PropertyName.Split(".");
                    member = Expression.Property(param, properties[0]);
                    member = properties.Skip(1).Aggregate(member, (current, propertyName) => Expression.Property(current, propertyName));
                }
                else
                {
                    member = Expression.Property(param, filter.PropertyName);
                }
                return Expression.Call(Expression.Constant(filter.Value), containsMethodShort, Expression.Convert(member, typeof(short)));
            }

            if (filter.PropertyName.Contains("."))
            {
                var properties = filter.PropertyName.Split(".");
                member = Expression.Property(param, properties[0]);
                member = properties.Skip(1).Aggregate(member, (current, propertyName) => Expression.Property(current, propertyName));
            }
            else
            {
                if (filter.PropertyName.Contains("["))
                {
                    var navigationProperty = filter.PropertyName.Substring(0, filter.PropertyName.IndexOf("["));
                    var navigationSubProperty = filter.PropertyName.Substring(filter.PropertyName.IndexOf("[") + 1, filter.PropertyName.IndexOf("]") - filter.PropertyName.IndexOf("[") - 1);
                    member = Expression.Property(param, navigationProperty);
                    var navigationSubPropertyType = param.Type.GetProperty(navigationProperty).PropertyType;//List
                    var navigationSubPropertyObjectType = navigationSubPropertyType.GetGenericArguments()[0];

                    var anyInfo = typeof(Enumerable).GetMethods(BindingFlags.Static | BindingFlags.Public).First(m => m.Name == "Any" && m.GetParameters().Count() == 2);
                    anyInfo = anyInfo.MakeGenericMethod(navigationSubPropertyObjectType);

                    ParameterExpression listItemParam = Expression.Parameter(navigationSubPropertyObjectType, "i");

                    var lambda = Expression.Lambda(GetExpression(listItemParam,
                        new ExpressionBuilderFilter { PropertyName = navigationSubProperty, Operator = filter.Operator, Value = filter.Value }), listItemParam);

                    return Expression.Call(anyInfo, member, lambda);
                }
                else
                {
                    member = Expression.Property(param, filter.PropertyName);
                }
            }

            ConstantExpression constant = filter.Value == null ? Expression.Constant(filter.Value, member.Type) : Expression.Constant(filter.Value);
            switch (filter.Operator)
            {
                case Operator.Equals:
                    if (filter.Value != null && member.Type != filter.Value.GetType())
                    {
                        return Expression.Equal(member, Expression.Convert(constant, member.Type));
                    }
                    return Expression.Equal(member, constant);

                case Operator.NotEqual:
                    if (filter.Value != null && member.Type != filter.Value.GetType())
                    {
                        return Expression.NotEqual(member, Expression.Convert(constant, member.Type));
                    }
                    return Expression.NotEqual(member, constant);

                case Operator.Contains:
                    Expression memberExpression = Expression.Call(member, typeof(string).GetMethod("ToLower", System.Type.EmptyTypes));
                    Expression constantExpression = Expression.Call(constant, typeof(string).GetMethod("ToLower", System.Type.EmptyTypes));
                    return Expression.Call(memberExpression, containsMethod, constantExpression);

                case Operator.GreaterThan:
                    if (filter.Value != null && member.Type != filter.Value.GetType())//fix:System.InvalidOperationException: The binary operator GreaterThan is not defined for the types 'System.Nullable`1[System.DateTime]' and 'System.DateTime'
                    {
                        return Expression.GreaterThan(member, Expression.Convert(constant, member.Type));
                    }
                    return Expression.GreaterThan(member, constant);

                case Operator.GreaterThanOrEqual:
                    if (filter.Value != null && member.Type != filter.Value.GetType())
                    {
                        return Expression.GreaterThanOrEqual(member, Expression.Convert(constant, member.Type));
                    }
                    return Expression.GreaterThanOrEqual(member, constant);

                case Operator.LessThan:
                    if (filter.Value != null && member.Type != filter.Value.GetType())
                    {
                        return Expression.LessThan(member, Expression.Convert(constant, member.Type));
                    }
                    return Expression.LessThan(member, constant);

                case Operator.LessThanOrEqualTo:
                    if (filter.Value != null && member.Type != filter.Value.GetType())
                    {
                        return Expression.LessThanOrEqual(member, Expression.Convert(constant, member.Type));
                    }
                    return Expression.LessThanOrEqual(member, constant);

                case Operator.StartsWith:
                    return Expression.Call(member, startsWithMethod, constant);

                case Operator.EndsWith:
                    return Expression.Call(member, endsWithMethod, constant);
            }

            return null;
        }

        private static BinaryExpression GetExpression<T>(ParameterExpression param, ExpressionBuilderFilter filter1, ExpressionBuilderFilter filter2, ExpressionBuilderOperator @operator = ExpressionBuilderOperator.And)
        {
            Expression result1 = GetExpression(param, filter1);
            Expression result2 = GetExpression(param, filter2);
            return @operator == ExpressionBuilderOperator.And ? Expression.AndAlso(result1, result2) : Expression.Or(result1, result2);
        }

    }
}