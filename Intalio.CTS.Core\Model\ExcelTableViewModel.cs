﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.Model
{
    public class ExportActivityLogGridTableViewModel
    {
        //public List<ActivityLogListViewModel> ActivitylogRows { get; set; }
        public string Format { get; set; }
        public string ActivityLogActionId { get; set; }
        public int DocumentId { get; set; }
        public long? DelegationId { get; set; }
        public string OrderColumn { get; set; }
        public string OrderDir { get; set; }

    }
}
