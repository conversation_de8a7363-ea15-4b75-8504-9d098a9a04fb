SET IDENTITY_INSERT [dbo].[ActivityLogAction] ON 
insert into [ActivityLogAction] (Id, [Name]) VALUES (74, 'Reply')
SET IDENTITY_INSERT [dbo].[ActivityLogAction] OFF
GO

UPDATE [dbo].[Category]
SET [CustomAttributeTranslation] = N'[
  { "Keyword": "Date", "En": "Date", "Ar": "التاريخ", "Fr": "Date" },
  { "Keyword": "From", "En": "From", "Ar": "من", "Fr": "De" },
  { "Keyword": "Instructions", "En": "Instructions", "Ar": "تعليمات", "Fr": "Instructions" },
  { "Keyword": "IsPrivate", "En": "Is Private", "Ar": "خاص", "Fr": "Privée" },
  { "Keyword": "Remind?", "En": "Remind?", "Ar": "تذكير؟", "Fr": "<PERSON><PERSON>er ?" },
  { "Keyword": "Reminder", "En": "Reminder", "Ar": "تذكير", "Fr": "Rappel" },
  { "Keyword": "required", "En": "{{field}} is required", "Ar": "{{field}} مطلوب", "Fr": "{{field}} est requis", "Type": "required" },
  { "Keyword": "Status", "En": "Status", "Ar": "الحالة", "Fr": "Statut" },
  { "Keyword": "Team", "En": "Team", "Ar": "الفريق", "Fr": "Équipe" },
  { "Keyword": "Time", "En": "Time", "Ar": "الوقت", "Fr": "Heure" },
  { "Keyword": "To", "En": "To", "Ar": "إلى", "Fr": "À" }
]'
WHERE [Name] = 'FollowUp';
GO


﻿IF NOT EXISTS (SELECT 1 FROM [dbo].[TranslatorDictionary] WHERE Keyword = N'SigningDocument')
BEGIN
    INSERT INTO [dbo].[TranslatorDictionary] (Keyword, EN, FR, AR, IsSystem)
    VALUES (N'ExportingDocument', 
            N'Exporting Document', 
            N'Exporting Document', 
            N'جاري تصدير الكتاب', 
            0);
END;
GO

alter table transfer
add IsExporting bit NOT NULL DEFAULT(0)

GO


IF NOT EXISTS (SELECT 1 FROM Parameter WHERE Keyword=N'RCVersion')
BEGIN
	INSERT INTO Parameter (Keyword,[Description], Content, IsSystem)
	VALUES (N'RCVersion','RC version of CTS', 'RC26', 0)
END
UPDATE Parameter set Content = 'RC26' where Keyword = 'RCVersion'
GO