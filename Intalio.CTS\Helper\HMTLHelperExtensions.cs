﻿using Intalio.Core;
using Intalio.Core.Model;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc.Rendering;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Intalio.CTS
{
    public static class HtmlHelpers
    {
        #region Public Methods

        public static string isActive(this IHtmlHelper html, string controller = null, string action = null)
        {
            string activeClass = "active"; // change here if you another name to activate sidebar items
            // detect current app state
            string actualAction = (string)html.ViewContext.RouteData.Values["action"];
            string actualController = (string)html.ViewContext.RouteData.Values["controller"];

            if (string.IsNullOrEmpty(controller))
                controller = actualController;

            if (string.IsNullOrEmpty(action))
                action = actualAction;

            return (controller.ToLower() == actualController.ToLower() && action.ToLower() == actualAction.ToLower()) ? activeClass : string.Empty;
        }
        public static string ToDropDown(this IHtmlHelper html, List<ValueText> items, long? selectedValue = null)
        {
            StringBuilder sb = new StringBuilder();
            if (items != null)
            {
                foreach (var item in items)
                {
                    sb.AppendFormat("<option value='{0}' {1}>{2}</option>", item.Id, (item.Id == selectedValue) ? "selected" : "", item.Text);
                }
            }
            return sb.ToString();
        }
        public static HtmlString BuildMenus(this IHtmlHelper html, List<MenuListViewModel> portalMenus, TypeMenu type, Language language = Language.EN)
        {
            StringBuilder sb = new StringBuilder();
            var parents = portalMenus.Where(t => t.ParentMenuId == null);
            foreach (var item in parents.OrderBy(t => Convert.ToInt32(t.Order)).ToList()) //TemplatesManagement
            {
                if(item.Name == "TemplatesManagement")// apply IAM attributes permissions for template management page
                {
                    Core.Model.UserModel user = Core.Utility.IdentityHelperExtension.GetUser(Core.Configuration.userId,Core.Configuration.IdentityAccessToken, Language.EN);

                    var EnablePersonalTemplates = Convert.ToBoolean(user.Attributes.Where(x => x.Text == "EnablePersonalTemplates").FirstOrDefault().Value);
                    var EnableStructureTemplates = Convert.ToBoolean(user.Attributes.Where(x => x.Text == "EnableStructureTemplates").FirstOrDefault().Value);

                    EnableStructureTemplates = EnableStructureTemplates == false ? user.Structures.Any(d =>
                    d.UserAttributes.Any(f =>
                    f.Text == "EnableStructureTemplates" && Convert.ToBoolean(f.Value) == true)) : EnableStructureTemplates;
                    if (!(EnablePersonalTemplates && EnableStructureTemplates))
                    {
                        continue;
                    }
                }
                var childMenus = portalMenus.Where(t => t.ParentMenuId == item.Id).Distinct().ToList();
                if (type == TypeMenu.SideBar)
                {
                    if (childMenus.Count > 0)
                    {
                        sb.AppendFormat("<li><a href='#nav-{0}' data-toggle='collapse'><em class='fa {1}'></em><span>{2}</span></a>" +
                            "<ul id='nav-{0}' class='nav sidebar-subnav collapse'>{3}</ul></li>", item.Name.Replace(" ", ""), item.Icon,
                            TranslationUtility.Translate(item.Name, language), GetSubMenus(childMenus, portalMenus, type, language));
                    }
                    else
                    {
                        if (item.MenuTypeId.HasValue)
                        {
                            var title = item.Title != null ? TranslationUtility.Translate(item.Title, language) : "";
                            var openInIframe = item.OpenInIframe == true ? "true" : "false";
                            var icon = !string.IsNullOrEmpty(item.Icon) ? string.Format("<em class='{0}'></em>", item.Icon) : "";
                            switch (item.MenuTypeId)
                            {
                                case (int)MenuType.Url:
                                    if (item.UrlType == (int)UrlType.Blank)
                                    {
                                        sb.AppendFormat("<li><a href='{0}' target='_blank'>{1}<span>{2}</span></a></li>",
                                            item.Url, icon, TranslationUtility.Translate(item.Name, language));

                                    }
                                    else if (item.UrlType == (int)UrlType.Self)
                                    {
                                        sb.AppendFormat("<li><a href='{0}' target='_self'>{1}<span>{2}</span></a></li>", item.Url, icon, TranslationUtility.Translate(item.Name, language));
                                    }
                                    else if (item.UrlType == (int)UrlType.Frame)
                                    {
                                        sb.AppendFormat("<li id='liCustomMenu{0}'><a onclick=\"Common.setActiveSidebarMenu('liCustomMenu{0}');{1}\">{2}<span>{3}</span></a></li>",
                                            item.Id, string.Format("MenuSecurityActions.buildFromUrlCall('{0}','{1}',false,{2},'{3}')", item.Url, title, openInIframe, item.IframeHeight),
                                            icon, TranslationUtility.Translate(item.Name, language));
                                    }
                                    else if (item.UrlType == (int)UrlType.Modal)
                                    {
                                        sb.AppendFormat("<li id='liCustomMenu{0}'><a onclick=\"Common.setActiveSidebarMenu('liCustomMenu{0}');{1}\">{2}<span>{3}</span></a></li>",
                                            item.Id, string.Format("MenuSecurityActions.buildFromUrlCall('{0}','{1}',true,{2},'{3}')", item.Url, title, openInIframe, item.IframeHeight),
                                            icon, TranslationUtility.Translate(item.Name, language));
                                    }
                                    break;
                                case (int)MenuType.JsFunction:
                                    sb.AppendFormat("<li id='liCustomMenu{0}'><a onclick=\"Common.setActiveSidebarMenu('liCustomMenu{0}');{1}\">{2}<span>{3}</span></a></li>", item.Id, item.JsFunction, icon, TranslationUtility.Translate(item.Name, language));
                                    break;
                                case (int)MenuType.Html:
                                    sb.AppendFormat("<li id='liCustomMenu{0}'><input id='{0}' type='hidden' value='{1}'/><a onclick=\"Common.setActiveSidebarMenu('liCustomMenu{0}');{2}\">{3}<span>{4}</span></a></li>",
                                        item.Id, item.Html, string.Format("MenuSecurityActions.renderHtmlToWrapper('{0}','{1}',{2})", item.Id, title, item.OpenInModal.ToString().ToLower()),
                                        icon, TranslationUtility.Translate(item.Name, language));
                                    break;
                            }
                        }
                    }
                }
                else
                {
                    if (childMenus.Count > 0)
                    {
                        sb.AppendFormat("<li><a href='#' data-toggle='dropdown'>{0} <div class='arrow arrow--down'></div></a>" +
                            "<ul class='dropdown-menu animated fadeIn'>{1}</ul></li>", TranslationUtility.Translate(item.Name, language),
                            GetSubMenus(childMenus, portalMenus, type, language));
                    }
                    else
                    {
                        if (item.MenuTypeId.HasValue)
                        {
                            var title = item.Title != null ? TranslationUtility.Translate(item.Title, language) : "";
                            var openInIframe = item.OpenInIframe == true ? "true" : "false";
                            switch (item.MenuTypeId)
                            {
                                case (int)MenuType.Url:
                                    if (item.UrlType == (int)UrlType.Blank)
                                    {
                                        sb.AppendFormat("<li class='to_close'><a href='{0}' target='_blank'><span>{1}</span></a></li>",
                                            item.Url, TranslationUtility.Translate(item.Name, language));

                                    }
                                    else if (item.UrlType == (int)UrlType.Self)
                                    {
                                        sb.AppendFormat("<li class='to_close'><a href='{0}' target='_self'><span>{1}</span></a></li>", item.Url, TranslationUtility.Translate(item.Name, language));
                                    }
                                    else if (item.UrlType == (int)UrlType.Frame)
                                    {
                                        sb.AppendFormat("<li class='to_close'><a onclick=\"{0}\"><span>{1}</span></a></li>",
                                            string.Format("MenuSecurityActions.buildFromUrlCall('{0}','{1}',false,{2},'{3}')", item.Url, title, openInIframe, item.IframeHeight),
                                            TranslationUtility.Translate(item.Name, language));
                                    }
                                    else if (item.UrlType == (int)UrlType.Modal)
                                    {
                                        sb.AppendFormat("<li class='to_close'><a onclick=\"{0}\"><span>{1}</span></a></li>",
                                            string.Format("MenuSecurityActions.buildFromUrlCall('{0}','{1}',true,{2},'{3}')", item.Url, title, openInIframe, item.IframeHeight),
                                            TranslationUtility.Translate(item.Name, language));
                                    }
                                    break;
                                case (int)MenuType.JsFunction:
                                    sb.AppendFormat("<li class='to_close'><a onclick=\"{0}\"><span>{1}</span></a></li>", item.JsFunction, TranslationUtility.Translate(item.Name, language));
                                    break;
                                case (int)MenuType.Html:
                                    sb.AppendFormat("<li class='to_close'><input id='{0}' type='hidden' value='{1}'/><a onclick=\"{2}\"><span>{3}</span></a></li>",
                                        item.Id, item.Html, string.Format("MenuSecurityActions.renderHtmlToWrapper('{0}','{1}',{2})", item.Id, title, item.OpenInModal.ToString().ToLower()),
                                        TranslationUtility.Translate(item.Name, language));
                                    break;
                            }
                        }
                    }
                }
            }
            return new HtmlString(sb.ToString());
        }
        public static HtmlString BuildMenusById(this IHtmlHelper html, int id, List<MenuListViewModel> portalMenus, TypeMenu type, Language language = Language.EN)
        {
            StringBuilder sb = new StringBuilder();
            foreach (var item in portalMenus.Where(t => t.ParentMenuId == id).OrderBy(t => t.Order).ToList())
            {
                var childMenus = portalMenus.Where(t => t.ParentMenuId == item.Id).Distinct().ToList();
                if (childMenus.Count > 0)
                {
                    sb.AppendFormat("<li class='dropdown-submenu'><a href='#' data-toggle='dropdown'>{0} </a>" +
                        "<ul class='dropdown-menu animated fadeIn'>{1}</ul></li>", TranslationUtility.Translate(item.Name, language),
                        GetSubMenus(childMenus, portalMenus, type, language));
                }
                else
                {
                    if (item.MenuTypeId.HasValue)
                    {
                        var title = item.Title != null ? TranslationUtility.Translate(item.Title, language) : "";
                        var openInIframe = item.OpenInIframe == true ? "true" : "false";
                        switch (item.MenuTypeId)
                        {
                            case (int)MenuType.Url:
                                if (item.UrlType == (int)UrlType.Blank)
                                {
                                    sb.AppendFormat("<li class='to_close'><a href='{0}' target='_blank'><span>{1}</span></a></li>",
                                        item.Url, TranslationUtility.Translate(item.Name, language));

                                }
                                else if (item.UrlType == (int)UrlType.Self)
                                {
                                    sb.AppendFormat("<li class='to_close'><a href='{0}' target='_self'><span>{1}</span></a></li>", item.Url, TranslationUtility.Translate(item.Name, language));
                                }
                                else if (item.UrlType == (int)UrlType.Frame)
                                {
                                    sb.AppendFormat("<li class='to_close'><a onclick=\"{0}\"><span>{1}</span></a></li>",
                                        string.Format("MenuSecurityActions.buildFromUrlCall('{0}','{1}',false,{2},'{3}')", item.Url, title, openInIframe, item.IframeHeight),
                                        TranslationUtility.Translate(item.Name, language));
                                }
                                else if (item.UrlType == (int)UrlType.Modal)
                                {
                                    sb.AppendFormat("<li class='to_close'><a onclick=\"{0}\"><span>{1}</span></a></li>",
                                        string.Format("MenuSecurityActions.buildFromUrlCall('{0}','{1}',true,{2},'{3}')", item.Url, title, openInIframe, item.IframeHeight),
                                        TranslationUtility.Translate(item.Name, language));
                                }
                                break;
                            case (int)MenuType.JsFunction:
                                sb.AppendFormat("<li class='to_close'><a onclick=\"{0}\"><span>{1}</span></a></li>", item.JsFunction.Replace("\"", "&quot;"), TranslationUtility.Translate(item.Name, language));
                                break;
                            case (int)MenuType.Html:
                                sb.AppendFormat("<li class='to_close'><input id='{0}' type='hidden' value='{1}'/><a onclick=\"{2}\"><span>{3}</span></a></li>",
                                    item.Id, item.Html, string.Format("MenuSecurityActions.renderHtmlToWrapper('{0}','{1}',{2})", item.Id, title, item.OpenInModal.ToString().ToLower()),
                                    TranslationUtility.Translate(item.Name, language));
                                break;
                        }
                    }
                }

            }
            return new HtmlString(sb.ToString());
        }
        public static HtmlString BuildNodes(this IHtmlHelper html, List<NodeTreeListViewModel> nodes, Language language = Language.EN)
        {
            StringBuilder sb = new StringBuilder();
            nodes = nodes.Where(t => t.IsSearch != true && t.Visible == true).ToList();
            var parents = nodes.Where(t => t.ParentNodeId == null);
            var expandValues = nodes.Where(t=>t.Expand == true).ToList();
            foreach (var item in parents.OrderBy(t => t.Order).ToList())
            {

                var childNodes = nodes.Where(t => t.ParentNodeId == item.Id).Distinct().ToList();
                if (childNodes.Count > 0)
                {
                    var subNodes = GetSubNodes(childNodes, nodes, language);
                    if (!string.IsNullOrEmpty(subNodes))
                    {
                        if (expandValues.Any(t => t.Id == item.Id && t.Expand == true))

                        {
                            sb.AppendFormat("<li><a href='#nav-{0}' data-toggle='collapse'data-bs-parent='' aria-expanded='true' aria-controls='nav-{0}' ><em class='fa {1}'></em><span>{2}</span><em class='fa fa-angle-down arrow-menu'></em></a>" +
                             "<ul id='nav-{0}' class='nav sidebar-subnav collapse in expa' aria-expanded='true'>{3}</ul></li>", string.Format("{0}{1}", item.Name.Replace(" ", ""), item.Id),
                             item.Icon, item.Name, subNodes);


                        }
                        else
                        {

                            sb.AppendFormat("<li><a href='#nav-{0}' data-toggle='collapse' aria-controls='nav-{0}'><em class='fa {1}'></em><span>{2}</span><em class='fa fa-angle-down arrow-menu'></em></a>" +
                                "<ul id='nav-{0}' class='nav sidebar-subnav collapse'>{3}</ul></li>", string.Format("{0}{1}", item.Name.Replace(" ", ""), item.Id),
                                item.Icon, item.Name, subNodes);

                        }
                    }
                }
                else if (!string.IsNullOrEmpty(item.Inherit))
                {
                    var nodeRoute = "\"" + item.Inherit + "\"";

                    var icon = !string.IsNullOrEmpty(item.Icon) ? string.Format("<em class='{0}'></em>", item.Icon) : "";
                    Core.NodeInherit inherit;
                    Enum.TryParse(item.Inherit, out inherit);
                    switch (inherit)
                    {
                        case Core.NodeInherit.Draft:
                            var totalDraft = "<div id='countTotalDraft" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayDraft = "<div id='countTodayDraft" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liDraft{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalDraft : string.Empty), (item.EnableTodayCount == true ? todayDraft : string.Empty), icon, item.Name, item.Inherit,nodeRoute);
                            break;
                        case Core.NodeInherit.Inbox:
                            var totalInbox = "<div id='countTotalInbox" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayInbox = "<div id='countTodayInbox" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            var unreadInbox = "<div id='countUnreadInbox" + item.Id + "' class='label label-primary pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Unread", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liInbox{0}' data-id='{0}' data-inherit='{7}'><a href='javascript:nodesRoutes({8},{0});'><div class='parentMenu' {1}>{2}{3}{4}</div>{5}<span>{6}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true || item.EnableUnreadCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalInbox : string.Empty), (item.EnableTodayCount == true ? todayInbox : string.Empty), (item.EnableUnreadCount == true ? unreadInbox : string.Empty), icon, item.Name, item.Inherit,nodeRoute);
                            break;
                        case Core.NodeInherit.StructureInbox:
                            var totalStructureInbox = "<div id='countTotalStructureInbox" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayStructureInbox = "<div id='countTodayStructureInbox" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            var unreadStructureInbox = "<div id='countUnreadStructureInbox" + item.Id + "' class='label label-primary pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Unread", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liStructureInbox{0}' data-id='{0}' data-inherit='{7}'><a href='javascript:nodesRoutes({8},{0});'><div class='parentMenu' {1}>{2}{3}{4}</div>{5}<span>{6}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true || item.EnableUnreadCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalStructureInbox : string.Empty), (item.EnableTodayCount == true ? todayStructureInbox : string.Empty), (item.EnableUnreadCount == true ? unreadStructureInbox : string.Empty), icon, item.Name, item.Inherit,nodeRoute);
                            break;
                        case Core.NodeInherit.Completed:
                            var totalCompleted = "<div id='countTotalCompleted" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayCompleted = "<div id='countTodayCompleted" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liCompleted{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalCompleted : string.Empty), (item.EnableTodayCount == true ? todayCompleted : string.Empty), icon, item.Name, item.Inherit, nodeRoute);
                            break;
                        case Core.NodeInherit.MyRequests:
                            var totalMyRequests = "<div id='countTotalMyRequests" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayMyRequests = "<div id='countTodayMyRequests" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liMyRequests{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalMyRequests : string.Empty), (item.EnableTodayCount == true ? todayMyRequests : string.Empty), icon, item.Name, item.Inherit, nodeRoute);
                            break;
                        case Core.NodeInherit.Sent:
                            var totalSent = "<div id='countTotalSent" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todaySent = "<div id='countTodaySent" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liSent{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalSent : string.Empty), (item.EnableTodayCount == true ? todaySent : string.Empty), icon, item.Name, item.Inherit, nodeRoute);
                            break; 
                        case Core.NodeInherit.StructureSent:
                            var totalStructureSent = "<div id='countTotalStructureSent" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayStructureSent = "<div id='countTodayStructureSent" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liStructureSent{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalStructureSent : string.Empty), (item.EnableTodayCount == true ? todayStructureSent : string.Empty), icon, item.Name, item.Inherit, nodeRoute);
                            break;
                        case Core.NodeInherit.Closed:
                            var totalClosed = "<div id='countTotalClosed" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayClosed = "<div id='countTodayClosed" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liClosed{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalClosed : string.Empty), (item.EnableTodayCount == true ? todayClosed : string.Empty), icon, item.Name, item.Inherit,nodeRoute);
                            break;
                        case Core.NodeInherit.FollowUp:
                            var totalFollowUp = "<div id='countTotalFollowUp" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayFollowUp = "<div id='countTodayFollowUp" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liFollowUp{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalFollowUp : string.Empty), (item.EnableTodayCount == true ? todayFollowUp : string.Empty), icon, item.Name, item.Inherit,nodeRoute);
                            break;
                        case Core.NodeInherit.Custom:
                            var function = string.IsNullOrEmpty(item.CustomFunctions) ? "" : item.CustomFunctions.Split(Constants.SPLITTER)[0];
                            var totalCustom = "<div id='countTotalCustom" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayCustom = "<div id='countTodayCustom" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            var unreadCustom = "<div id='countUnreadCustom" + item.Id + "' class='label label-primary  pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Unread", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liCustom{0}' data-id='{0}' data-customFunctions='{12}' data-enableTodayCount='{9}' data-enableTotalCount='{10}' data-enableUnreadCount='{11}' data-inherit='{8}'><a href='javascript:nodesRoutes({13},{0});' class='pointer'><div class='parentMenu' {2}>{3}{4}{5}</div>{6}<span>{7}</span></a></li>", item.Id, function,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true || item.EnableUnreadCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalCustom : string.Empty), (item.EnableTodayCount == true ? todayCustom : string.Empty), (item.EnableUnreadCount == true ? unreadCustom : string.Empty), icon, item.Name, item.Inherit,
                                item.EnableTodayCount, item.EnableTotalCount, item.EnableUnreadCount, item.CustomFunctions, nodeRoute);
                            break;
                    }
                }
            }
            return new HtmlString(sb.ToString());
        }
        public static HtmlString BuildDelegationNodes(this IHtmlHelper html, List<NodeTreeListViewModel> nodes, Intalio.CTS.Core.Model.DelegationMenuModel delegation, Language language = Language.EN)
        {
            long delegationId = delegation.Id;
            StringBuilder sb = new StringBuilder();
            nodes = nodes.Where(t => t.IsSearch != true && t.Visible == true).ToList();
            var parents = nodes.Where(t => t.ParentNodeId == null);
            foreach (var item in parents.OrderBy(t => t.Order).ToList())
            {
                var childNodes = nodes.Where(t => t.ParentNodeId == item.Id).Distinct().ToList();
                if (childNodes.Count > 0)
                {
                    var subNodes = GetDelegationSubNodes(childNodes, nodes, delegation, language);
                    if (!string.IsNullOrEmpty(subNodes))
                    {
                         sb.AppendFormat("<li><a href='#nav-{0}' data-toggle='collapse'><em class='fa {1}'></em><span>{2}  <em class='fa fa-angle-down arrow-menu'></em></span></a>"+"<ul id='nav-{0}' class='nav sidebar-subnav collapse'>{3}</ul></li>",
                          string.Format("{0}{1}{2}", item.Name.Replace(" ", ""), item.Id, delegationId), item.Icon, item.Name, subNodes);
                    }
                }
                else if (!string.IsNullOrEmpty(item.Inherit))
                {
                    var nodeRoute = "\"" + item.Inherit + "\"";

                    var icon = !string.IsNullOrEmpty(item.Icon) ? string.Format("<em class='{0}'></em>", item.Icon) : "";
                    Core.NodeInherit inherit;
                    Enum.TryParse(item.Inherit, out inherit);
                    switch (inherit)
                    {
                        case Core.NodeInherit.Inbox:
                            var totalInbox = "<div id='countTotalInbox" + item.Id + "-" + delegationId + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayInbox = "<div id='countTodayInbox" + item.Id + "-" + delegationId + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            var unreadInbox = "<div id='countUnreadInbox" + item.Id + "-" + delegationId + "' class='label label-primary pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Unread", language) + "'>0</div>";
                            sb.AppendFormat("<li id='index-{0}' data-id='{0}' data-inherit='{7}'><a href='javascript:nodesRoutes({9},{0},{8});'><div class='parentMenu' {1}>{2}{3}{4}</div>{5}<span>{6}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true || item.EnableUnreadCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalInbox : string.Empty), (item.EnableTodayCount == true ? todayInbox : string.Empty), (item.EnableUnreadCount == true ? unreadInbox : string.Empty), icon, item.Name, item.Inherit, delegationId,nodeRoute);
                            break;
                        case Core.NodeInherit.Completed:
                            var totalCompleted = "<div id='countTotalCompleted" + item.Id + "-" + delegationId + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayCompleted = "<div id='countTodayCompleted" + item.Id + "-" + delegationId + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='completed-{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({8},{0},{7});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalCompleted : string.Empty), (item.EnableTodayCount == true ? todayCompleted : string.Empty), icon, item.Name, item.Inherit, delegationId, nodeRoute);
                            break;
                        case Core.NodeInherit.Sent:
                            var totalSent = "<div id='countTotalSent" + item.Id + "-" + delegationId + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todaySent = "<div id='countTodaySent" + item.Id + "-" + delegationId + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='sent-{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({8},{0},{7});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalSent : string.Empty), (item.EnableTodayCount == true ? todaySent : string.Empty), icon, item.Name, item.Inherit, delegationId,nodeRoute);
                            break;
                        case Core.NodeInherit.Draft:
                            if (delegation.DraftInbox == true)
                            {
                                var totalDraft = "<div id='countTotalDraft" + item.Id + "-" + delegationId + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                                var todayDraft = "<div id='countTodayDraft" + item.Id + "-" + delegationId + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                                var unreadDraft = "<div id='countUnreadDraft" + item.Id + "-" + delegationId + "' class='label label-primary pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Unread", language) + "'>0</div>";
                                sb.AppendFormat("<li id='index-{0}' data-id='{0}' data-inherit='{7}'><a href='javascript:nodesRoutes({9},{0},{8});'><div class='parentMenu' {1}>{2}{3}{4}</div>{5}<span>{6}</span></a></li>", item.Id,
                                    ((item.EnableTotalCount == true || item.EnableTodayCount == true || item.EnableUnreadCount == true) ? string.Empty : "style='display:none;'"),
                                    (item.EnableTotalCount == true ? totalDraft : string.Empty), (item.EnableTodayCount == true ? todayDraft : string.Empty), (item.EnableUnreadCount == true ? unreadDraft : string.Empty), icon, item.Name, item.Inherit, delegationId, nodeRoute);
                                break;
                            }
                            else
                                break;
                            
                    }
                }
            }
            return new HtmlString(sb.ToString());
        }

        #endregion

        #region Private Methods

        private static string GetSubMenus(List<MenuListViewModel> childMenus, List<MenuListViewModel> portalMenus,
            TypeMenu type, Language language = Language.EN)
        {
            StringBuilder sb = new StringBuilder();
            foreach (var item in childMenus.OrderBy(t => Convert.ToInt32(t.Order)).ToList())
            {
                var subMenus = portalMenus.Where(t => t.ParentMenuId == item.Id).Distinct().ToList();
                if (type == TypeMenu.SideBar)
                {

                    if (subMenus.Count > 0)
                    {
                        sb.AppendFormat("<li><a href='#nav-{0}' data-toggle='collapse'><em class='fa {1}'></em><span>{2}</span></a>" +
                            "<ul id='nav-{0}' class='nav sidebar-subnav collapse'>{3}</ul></li>", item.Name.Replace(" ", ""), item.Icon,
                            TranslationUtility.Translate(item.Name, language), GetSubMenus(subMenus, portalMenus, type, language));
                    }
                    else
                    {
                        var icon = !string.IsNullOrEmpty(item.Icon) ? string.Format("<em class='{0}'></em>", item.Icon) : "";
                        if (item.MenuTypeId.HasValue)
                        {
                            var title = item.Title != null ? TranslationUtility.Translate(item.Title, language) : "";
                            var openInIframe = item.OpenInIframe == true ? "true" : "false";
                            switch (item.MenuTypeId)
                            {
                                case (int)MenuType.Url:
                                    if (item.UrlType == (int)UrlType.Blank)
                                    {
                                        sb.AppendFormat("<li><a href='{0}' target='_blank'>{1}<span>{2}</span></a></li>",
                                            item.Url, icon, TranslationUtility.Translate(item.Name, language));

                                    }
                                    else if (item.UrlType == (int)UrlType.Self)
                                    {
                                        sb.AppendFormat("<li><a href='{0}' target='_self'>{1}<span>{2}</span></a></li>", item.Url, icon, TranslationUtility.Translate(item.Name, language));
                                    }
                                    else if (item.UrlType == (int)UrlType.Frame)
                                    {
                                        sb.AppendFormat("<li id='liCustomMenu{0}'><a onclick=\"Common.setActiveSidebarMenu('liCustomMenu{0}');{1}\">{2}<span>{3}</span></a></li>",
                                            item.Id, string.Format("MenuSecurityActions.buildFromUrlCall('{0}','{1}',false,{2},'{3}')", item.Url, title, openInIframe, item.IframeHeight),
                                            icon, TranslationUtility.Translate(item.Name, language));
                                    }
                                    else if (item.UrlType == (int)UrlType.Modal)
                                    {
                                        sb.AppendFormat("<li id='liCustomMenu{0}'><a onclick=\"Common.setActiveSidebarMenu('liCustomMenu{0}');{1}\">{2}<span>{3}</span></a></li>",
                                            item.Id, string.Format("MenuSecurityActions.buildFromUrlCall('{0}','{1}',true,{2},'{3}')", item.Url, title, openInIframe, item.IframeHeight),
                                            icon, TranslationUtility.Translate(item.Name, language));
                                    }
                                    break;
                                case (int)MenuType.JsFunction:
                                    sb.AppendFormat("<li id='liCustomMenu{0}'><a onclick=\"Common.setActiveSidebarMenu('liCustomMenu{0}');{1}\">{2}<span>{3}</span></a></li>", item.Id, item.JsFunction.Replace("\"", "&quot;"), icon, TranslationUtility.Translate(item.Name, language));
                                    break;
                                case (int)MenuType.Html:
                                    sb.AppendFormat("<li id='liCustomMenu{0}'><input id='{0}' type='hidden' value='{1}'/><a onclick=\"Common.setActiveSidebarMenu('liCustomMenu{0}');{2}\">{3}<span>{4}</span></a></li>",
                                        item.Id, item.Html, string.Format("MenuSecurityActions.renderHtmlToWrapper('{0}','{1}',{2})", item.Id, title, item.OpenInModal.ToString().ToLower()),
                                        icon, TranslationUtility.Translate(item.Name, language));
                                    break;
                            }
                        }
                        else
                        {
                            sb.AppendFormat("<li><a href='#'>{0}<span>{1}</span></a></li>", icon, TranslationUtility.Translate(item.Name, language));
                        }
                    }
                }
                else
                {
                    if (subMenus.Count > 0)
                    {
                        sb.AppendFormat("<li class='dropdown-submenu'><a href='#' data-toggle='dropdown'>{0} </a>" +
                            "<ul class='dropdown-menu animated fadeIn'>{1}</ul></li>", TranslationUtility.Translate(item.Name, language),
                            GetSubMenus(subMenus, portalMenus, type, language));
                    }
                    else
                    {

                        if (item.MenuTypeId.HasValue)
                        {
                            var title = item.Title != null ? TranslationUtility.Translate(item.Title, language) : "";
                            var openInIframe = item.OpenInIframe == true ? "true" : "false";
                            switch (item.MenuTypeId)
                            {
                                case (int)MenuType.Url:
                                    if (item.UrlType == (int)UrlType.Blank)
                                    {
                                        sb.AppendFormat("<li><a href='{0}' target='_blank'><span>{1}</span></a></li>",
                                            item.Url, TranslationUtility.Translate(item.Name, language));

                                    }
                                    else if (item.UrlType == (int)UrlType.Self)
                                    {
                                        sb.AppendFormat("<li><a href='{0}' target='_self'><span>{1}</span></a></li>", item.Url, TranslationUtility.Translate(item.Name, language));
                                    }
                                    else if (item.UrlType == (int)UrlType.Frame)
                                    {
                                        sb.AppendFormat("<li><a onclick=\"{0}\"><span>{1}</span></a></li>",
                                            string.Format("MenuSecurityActions.buildFromUrlCall('{0}','{1}',false,{2},'{3}')", item.Url, title, openInIframe, item.IframeHeight),
                                            TranslationUtility.Translate(item.Name, language));
                                    }
                                    else if (item.UrlType == (int)UrlType.Modal)
                                    {
                                        sb.AppendFormat("<li><a onclick=\"{0}\"><span>{1}</span></a></li>",
                                            string.Format("MenuSecurityActions.buildFromUrlCall('{0}','{1}',true,{2},'{3}')", item.Url, title, openInIframe, item.IframeHeight),
                                            TranslationUtility.Translate(item.Name, language));
                                    }
                                    break;
                                case (int)MenuType.JsFunction:
                                    sb.AppendFormat("<li><a onclick=\"{0}\"><span>{1}</span></a></li>", item.JsFunction.Replace("\"", "&quot;"), TranslationUtility.Translate(item.Name, language));
                                    break;
                                case (int)MenuType.Html:
                                    sb.AppendFormat("<li><input id='{0}' type='hidden' value='{1}'/><a onclick=\"{2}\"><span>{3}</span></a></li>",
                                        item.Id, item.Html, string.Format("MenuSecurityActions.renderHtmlToWrapper('{0}','{1}',{2})", item.Id, title, item.OpenInModal.ToString().ToLower()),
                                        TranslationUtility.Translate(item.Name, language));
                                    break;
                            }
                        }
                        else
                        {
                            sb.AppendFormat("<li><a href='#'>{0}</a></li>", TranslationUtility.Translate(item.Name, language));
                        }
                    }
                }
            }
            return sb.ToString();
        }
        private static string GetSubNodes(List<NodeTreeListViewModel> childNodes, List<NodeTreeListViewModel> nodes, Language language = Language.EN)
        {
            StringBuilder sb = new StringBuilder();
            foreach (var item in childNodes.OrderBy(t => t.Order).ToList())
            {
                var subNodes = nodes.Where(t => t.ParentNodeId == item.Id).Distinct().ToList();
                if (subNodes.Count > 0)
                {
                    var stringSubNodes = GetSubNodes(subNodes, nodes, language);
                    if (!string.IsNullOrEmpty(stringSubNodes))
                    {
                        sb.AppendFormat("<li><a href='#nav-{0}' data-toggle='collapse'><em class='fa {1}'></em><span>{2}</span></a>" +
                        "<ul id='nav-{0}' class='nav sidebar-subnav collapse'>{3}</ul></li>", string.Format("{0}{1}", item.Name.Replace(" ", ""), item.Id),
                        item.Icon, item.Name, stringSubNodes);
                    }
                }
                else if (!string.IsNullOrEmpty(item.Inherit))
                {
                    var nodeRoute = "\"" + item.Inherit + "\"";
                    var icon = !string.IsNullOrEmpty(item.Icon) ? string.Format("<em class='{0}'></em>", item.Icon) : "";
                    Core.NodeInherit inherit;
                    Enum.TryParse(item.Inherit, out inherit);
                    switch (inherit)
                    {
                        case Core.NodeInherit.Draft:
                            var totalDraft = "<div id='countTotalDraft" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayDraft = "<div id='countTodayDraft" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liDraft{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalDraft : string.Empty), (item.EnableTodayCount == true ? todayDraft : string.Empty), icon, item.Name, item.Inherit,nodeRoute);
                            break;
                        case Core.NodeInherit.Inbox:
                            var totalInbox = "<div id='countTotalInbox" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayInbox = "<div id='countTodayInbox" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            var unreadInbox = "<div id='countUnreadInbox" + item.Id + "' class='label label-primary pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Unread", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liInbox{0}' data-id='{0}' data-inherit='{7}'><a href='javascript:nodesRoutes({8},{0});'><div class='parentMenu' {1}>{2}{3}{4}</div>{5}<span>{6}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true || item.EnableUnreadCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalInbox : string.Empty), (item.EnableTodayCount == true ? todayInbox : string.Empty), (item.EnableUnreadCount == true ? unreadInbox : string.Empty), icon, item.Name, item.Inherit,nodeRoute);

                            break;
                        case Core.NodeInherit.StructureInbox:
                            var totalStructureInbox = "<div id='countTotalStructureInbox" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayStructureInbox = "<div id='countTodayStructureInbox" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            var unreadStructureInbox = "<div id='countUnreadStructureInbox" + item.Id + "' class='label label-primary pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Unread", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liStructureInbox{0}' data-id='{0}' data-inherit='{7}'><a href='javascript:nodesRoutes({8},{0});'><div class='parentMenu' {1}>{2}{3}{4}</div>{5}<span>{6}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true || item.EnableUnreadCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalStructureInbox : string.Empty), (item.EnableTodayCount == true ? todayStructureInbox : string.Empty), (item.EnableUnreadCount == true ? unreadStructureInbox : string.Empty), icon, item.Name, item.Inherit,nodeRoute);

                            break;
                        case Core.NodeInherit.Completed:
                            var totalCompleted = "<div id='countTotalCompleted" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayCompleted = "<div id='countTodayCompleted" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liCompleted{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalCompleted : string.Empty), (item.EnableTodayCount == true ? todayCompleted : string.Empty), icon, item.Name, item.Inherit,nodeRoute);
                            break;
                        case Core.NodeInherit.MyRequests:
                            var totalMyRequests = "<div id='countTotalMyRequests" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayMyRequests = "<div id='countTodayMyRequests" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liMyRequests{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalMyRequests : string.Empty), (item.EnableTodayCount == true ? todayMyRequests : string.Empty), icon, item.Name, item.Inherit, nodeRoute);
                            break;
                        case Core.NodeInherit.Sent:
                            var totalSent = "<div id='countTotalSent" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todaySent = "<div id='countTodaySent" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liSent{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalSent : string.Empty), (item.EnableTodayCount == true ? todaySent : string.Empty), icon, item.Name, item.Inherit,nodeRoute);
                            break;
                        case Core.NodeInherit.StructureSent:
                            var totalStructureSent = "<div id='countTotalStructureSent" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayStructureSent = "<div id='countTodayStructureSent" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liStructureSent{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalStructureSent : string.Empty), (item.EnableTodayCount == true ? todayStructureSent : string.Empty), icon, item.Name, item.Inherit,nodeRoute);
                            break;
                        case Core.NodeInherit.Closed:
                            var totalClosed = "<div id='countTotalClosed" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayClosed = "<div id='countTodayClosed" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liClosed{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalClosed : string.Empty), (item.EnableTodayCount == true ? todayClosed : string.Empty), icon, item.Name, item.Inherit, nodeRoute);
                            break;
                        case Core.NodeInherit.FollowUp:
                            var totalFollowUp = "<div id='countTotalFollowUp" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayFollowUp = "<div id='countTodayFollowUp" + item.Id + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liFollowUp{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({7},{0});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalFollowUp : string.Empty), (item.EnableTodayCount == true ? todayFollowUp : string.Empty), icon, item.Name, item.Inherit, nodeRoute);
                            break;
                        case Core.NodeInherit.Custom:
                            var function = string.IsNullOrEmpty(item.CustomFunctions) ? "" : item.CustomFunctions.Split(Constants.SPLITTER)[0];
                            var totalCustom = "<div id='countTotalCustom" + item.Id + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayCustom = "<div id='countTodayCustom" + item.Id + "' class='label label-warning  pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            var unreadCustom = "<div id='countUnreadCustom" + item.Id + "' class='label label-primary  pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='liCustom{0}' data-id='{0}' data-customFunctions='{12}' data-enableTodayCount='{9}' data-enableTotalCount='{10}' data-enableUnreadCount='{11}' data-inherit='{8}'><a href='javascript:nodesRoutes({13},{0});' class='pointer'><div class='parentMenu' {2}>{3}{4}{5}</div>{6}<span>{7}</span></a></li>", item.Id, function,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true || item.EnableUnreadCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalCustom : string.Empty), (item.EnableTodayCount == true ? todayCustom : string.Empty), (item.EnableUnreadCount == true ? unreadCustom : string.Empty), icon, item.Name, item.Inherit,
                                item.EnableTodayCount, item.EnableTotalCount, item.EnableUnreadCount, item.CustomFunctions, nodeRoute);

                            break;
                    }
                }

            }
            return sb.ToString();
        }
        private static string GetDelegationSubNodes(List<NodeTreeListViewModel> childNodes, List<NodeTreeListViewModel> nodes, Intalio.CTS.Core.Model.DelegationMenuModel delegation, Language language = Language.EN)
        {
            long delegationId = delegation.Id;
            StringBuilder sb = new StringBuilder();
            foreach (var item in childNodes.OrderBy(t => t.Order).ToList())
            {
                var subNodes = nodes.Where(t => t.ParentNodeId == item.Id).Distinct().ToList();
                if (subNodes.Count > 0)
                {
                    var stringSubNodes = GetDelegationSubNodes(subNodes, nodes, delegation, language);
                    if (!string.IsNullOrEmpty(stringSubNodes))
                    {
                        sb.AppendFormat("<li><a href='#nav-{0}' data-toggle='collapse'><em class='fa {1}'></em><span>{2}</span></a><ul id='nav-{0}' class='nav sidebar-subnav collapse'>{3}</ul></li>",
                            string.Format("{0}{1}{2}", item.Name.Replace(" ", ""), item.Id, delegationId), item.Icon, item.Name, stringSubNodes);
                    }
                }
                else if (!string.IsNullOrEmpty(item.Inherit))
                {
                    var nodeRoute = "\"" + item.Inherit + "\"";

                    var icon = !string.IsNullOrEmpty(item.Icon) ? string.Format("<em class='{0}'></em>", item.Icon) : "";
                    Core.NodeInherit inherit;
                    Enum.TryParse(item.Inherit, out inherit);
                    switch (inherit)
                    {
                        case Core.NodeInherit.Inbox:
                            var totalInbox = "<div id='countTotalInbox" + item.Id + "-" + delegationId + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayInbox = "<div id='countTodayInbox" + item.Id + "-" + delegationId + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            var unreadInbox = "<div id='countUnreadInbox" + item.Id + "-" + delegationId + "' class='label label-primary pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Unread", language) + "'>0</div>";
                            sb.AppendFormat("<li id='index-{0}' data-id='{0}' data-inherit='{7}'><a href='javascript:nodesRoutes({9},{0},{8});'><div class='parentMenu' {1}>{2}{3}{4}</div>{5}<span>{6}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true || item.EnableUnreadCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalInbox : string.Empty), (item.EnableTodayCount == true ? todayInbox : string.Empty), (item.EnableUnreadCount == true ? unreadInbox : string.Empty), icon, item.Name, item.Inherit, delegationId,nodeRoute);

                            break;
                        case Core.NodeInherit.Completed:
                            var totalCompleted = "<div id='countTotalCompleted" + item.Id + "-" + delegationId + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todayCompleted = "<div id='countTodayCompleted" + item.Id + "-" + delegationId + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='completed-{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({8},{0},{7});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalCompleted : string.Empty), (item.EnableTodayCount == true ? todayCompleted : string.Empty), icon, item.Name, item.Inherit, delegationId, nodeRoute);
                            break;
                        case Core.NodeInherit.Sent:
                            var totalSent = "<div id='countTotalSent" + item.Id + "-" + delegationId + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                            var todaySent = "<div id='countTodaySent" + item.Id + "-" + delegationId + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                            sb.AppendFormat("<li id='sent-{0}' data-id='{0}' data-inherit='{6}'><a href='javascript:nodesRoutes({8},{0},{7});'><div class='parentMenu' {1}>{2}{3}</div>{4}<span>{5}</span></a></li>", item.Id,
                                ((item.EnableTotalCount == true || item.EnableTodayCount == true) ? string.Empty : "style='display:none;'"),
                                (item.EnableTotalCount == true ? totalSent : string.Empty), (item.EnableTodayCount == true ? todaySent : string.Empty), icon, item.Name, item.Inherit, delegationId,nodeRoute);
                            break;
                        case Core.NodeInherit.Draft:
                            if(delegation.DraftInbox == true)
                            {
                                var totalDraft = "<div id='countTotalDraft" + item.Id + "-" + delegationId + "' class='label label-success pull-right flex-center' title='" + TranslationUtility.Translate("Total", language) + "'>0</div>";
                                var todayDraft = "<div id='countTodayDraft" + item.Id + "-" + delegationId + "' class='label label-warning pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Today", language) + "'>0</div>";
                                var unreadDraft = "<div id='countUnreadDraft" + item.Id + "-" + delegationId + "' class='label label-primary pull-right mr-sm flex-center' title='" + TranslationUtility.Translate("Unread", language) + "'>0</div>";
                                sb.AppendFormat("<li id='index-{0}' data-id='{0}' data-inherit='{7}'><a href='javascript:nodesRoutes({9},{0},{8});'><div class='parentMenu' {1}>{2}{3}{4}</div>{5}<span>{6}</span></a></li>", item.Id,
                                    ((item.EnableTotalCount == true || item.EnableTodayCount == true || item.EnableUnreadCount == true) ? string.Empty : "style='display:none;'"),
                                    (item.EnableTotalCount == true ? totalDraft : string.Empty), (item.EnableTodayCount == true ? todayDraft : string.Empty), (item.EnableUnreadCount == true ? unreadDraft : string.Empty), icon, item.Name, item.Inherit, delegationId, nodeRoute);
                                break;
                            }
                            break;
                            
                    }
                }

            }
            return sb.ToString();
        }

        #endregion
    }
}
