import Intalio from './common.js'
class LinkedCorrespondenceDocument extends Intalio.Model
{
    constructor()
    {
        super();
        this.reference = null;
        this.subject = null;
        this.from = null;
        this.to = null;
        this.transferDate = null;
        this.registerDate = null;
        this.registeredBy = null;
    }
}
class LinkedCorrespondenceDocumentView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "linkedcorrespondencedocument", model);
    }
    render()
    {
        var self = this;
        //if ($('.modal.fade.in').length >= 1) {
        //    $("#" + self.model.ComponentId + "_btnMinimize").remove();
        //}
        $("#" + self.model.ComponentId + "_btnMinimize").click(function () {
            $(self.refs['modalLinkedCorrespondenceDocument']).modal("hide");
            
            const hashSegment = window.location.hash.split('#')[1]?.split('/')[0] || "";
            if ($("i.maximize-modal[data-document='" + self.model.documentId + hashSegment + "']").length > 0)
                return;

            minimizeModal(self.model.ComponentId, self.model.reference, self.model.subject, self.model.from, self.model.to, self.model.transferDate, self.model.documentId);
        });

        $("#" + self.model.ComponentId + "_btnClose").click(function () {
            
            $(self.refs['modalLinkedCorrespondenceDocument']).modal("hide");
            $(self.refs['modalLinkedCorrespondenceDocument']).data("remove", true);

            $("i.maximize-modal[data-component='" + self.model.ComponentId + "']").remove()
            //window.history.back();
            //window.location.href = '/';

            if (window.location.href.includes("#document")) {
                window.history.go(-2);
            } else {
                //window.history.back();
            }
        });

    };
}

function minimizeModal(componentId, reference, subject, from, to, transferDate, documentId) {
    const hashSegment = window.location.hash.split('#')[1]?.split('/')[0] || "";
    if ($("i.maximize-modal[data-document='" + documentId + hashSegment+"']").length > 0)
        return;

    var content = "";

    if (reference != null && reference != "" && reference != undefined)
        content += `<p><strong>${Resources.ReferenceNumber}:</strong> ${reference}</p>`
    if (subject != null && subject != "" && subject != undefined)
        content += `<p><strong>${Resources.Subject}:</strong> ${subject}</p>`
    if (from != null && from != "" && from != undefined)
        content += `<p><strong>${Resources.From}:</strong> ${from}</p>`
    if (to != null && to != "" && to != undefined)
        content += `<p><strong>To:</strong> ${to}</p>`
    if (transferDate != null && transferDate != "" && transferDate != undefined)
        content += `<p><strong>Transfer Date:</strong> ${transferDate}</p>`
    //if (registerDate != null && registerDate != "" && registerDate != undefined)
    //    content += `<p><strong>Registration Date:</strong> ${registerDate}</p>`
    //if (registeredBy != null && registeredBy != "" && registeredBy != undefined)
    //    content += `<p><strong>Registered By:</strong> ${registeredBy}</p>`
    //if ($("html").attr("dir") === "rtl") {
    //    $("div.wrapper footer").append(`<i class="fa fa-envelope-o  maximize-modal" data-toggle="popover" data-html="true" style="float:left; margin:0 1rem 0 0;" data-component="${componentId}" data-placement="top" data-content="${content}" ></i>`);
    //    $('[data-toggle="popover"]').popover({ trigger: 'hover', offset: [0, 100],  });
    //} else {
    $("div.wrapper footer").append(`<i class="fa fa-envelope-o ml-lg maximize-modal" data-toggle="popover" data-html="true" data-component="${componentId}"  data-document="${documentId}${hashSegment}" data-placement="top" data-content="${content}" ></i>`);
        $('[data-toggle="popover"]').popover({ trigger: 'hover', offset: [0, 100] });
    //}
    
    
}

export default { LinkedCorrespondenceDocument, LinkedCorrespondenceDocumentView };
