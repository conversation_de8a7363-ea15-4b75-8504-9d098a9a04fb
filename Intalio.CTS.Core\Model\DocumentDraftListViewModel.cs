﻿
using Intalio.CTS.Core.DAL;
using System.Collections.Generic;

namespace Intalio.CTS.Core.Model
{
    public class DocumentDraftListViewModel
    {
        public long Id { get; set; }
        public string Subject { get; set; }
        public string SendingEntity { get; set; }
        public string ReceivingEntity { get; set; }
        public int CategoryId { get; set; }
        public short? ImportanceId { get; set; }
        public string ReferenceNumber { get; set; }
        public int Status { get; set; }
        public bool IsOverDue { get; set; }
        public short? PriorityId { get; set; }
        public short? PrivacyId { get; set; }
        public string DueDate { get; set; }
        public string CreatedDate { get; set; }
        public string ModifiedDate { get; set; }
        public string ClosedDate { get; set; }
        public long CreatedByUserId { get; set; }
        public string CreatedByUser { get; set; }
        public int DraftStatus { get; set; }

        public DocumentForm DocumentForm { get; set; }
        public string Body { get; set; }

        public virtual ICollection<Note> Note { get; set; }




    }
}