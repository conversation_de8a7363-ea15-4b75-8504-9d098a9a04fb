﻿import Intalio from './common.js'
import followUpPanelIndex from './followUpPanelIndex.js'
import followUpPanelItems from './followUpPanelItems.js'
var clickedSearch = false;
let table;
function renderTextInSelectStructures(option) {
    var $option;
    if (typeof option.id !== "undefined") {
        $option = $(
            '<div><i style="font-size:12px;" class="' + option.icon + '"></i> ' + option.text + '</div>'
        );
    } else {
        $option = option;
    }
    return $option;
}
function versionHistory(followUpId) {
    var versionWrapper = $(".modal-window");
    versionWrapper.find("#modalVersionHistory").remove();
    var versionModel = new VersionHistory.VersionHistory();
    versionModel.fileId = fileId;
    versionModel.transferId = selfModel.transferId;
    versionModel.documentId = selfModel.documentId;
    versionModel.delegationId = selfModel.delegationId;
    versionModel.hasEditAccess = hasEditAccess;
    versionModel.allowRestore = isSigned ? false : true;
    var versionView = new VersionHistory.VersionHistoryView(versionWrapper, versionModel);
    let callback = selfModel.fromVip && window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer" ? refreshPreviewTab : null;
    versionView.render(callback);

    $('#modalVersionHistory').modal('show');
    $("#modalVersionHistory").off("hidden.bs.modal");
    $("#modalVersionHistory").off("shown.bs.modal");

    $('#modalVersionHistory').on('hidden.bs.modal', function () {
        $('#modalVersionHistory').remove();
    });
}
//function drawTable(model) {
//    let retTable = $("#followUpPanelGrdItems").on('draw.dt',
//        function () {
//            $('#followUpPanelGrdItems tbody tr td').each(function (index) {
//                if ($(this).hasClass('sortableDateInDatatable')) {
//                    if ($(this)[0].lastElementChild) {
//                        this.setAttribute('title', $(this)[0].lastElementChild.textContent);
//                    }
//                } else {
//                    this.setAttribute('title', $(this).text());
//                }
//            });
//        }).DataTable({
//            processing: true,
//            ordering: true,
//            serverSide: true,
//            pageLength: 10,
//            "paging": true,
//            "ajax": {
//                "url": "/FollowUp/ListFollowUpPanel",
//                "type": "POST",
//                "datatype": "json",
//                data: function (d) {
//                    d.NodeId = window.FollowUpPanelNodeId;
//                    d.FollowUpSubject = $("#txtFollowUpSubject").val() !== "" && typeof $("#txtFollowUpSubject").val() !== "undefined" ? $("#txtFollowUpSubject").val() : "";
//                    d.FollowUpCreatedByUserId = $("#followUpCreatedByUser").val() !== null && typeof $("#followUpCreatedByUser").val() !== "undefined" ? $("#followUpCreatedByUser").val() : "0";

//                    return d;
//                }
//            },
//            "order": [],
//            "columns": [



//                {
//                    title: '<input id="chkAll" type="checkbox" />', width: '16px', "orderable": false,
//                    "render": function (data, type, row) {
//                        return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />";
//                    }
//                },
//                { title: "Id", data: "id", visible: false, "orderable": false },
//                 { title: Resources.SendingEntity, data: "sendingEntity", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.DocumentCreatedDate, data: "documentCreatedDate", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.Priority, data: "priority", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.Event, data: "event", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.EventDate, data: "eventDate", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.TransferredTo, data: "transferredTo", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.TransferredDate, data: "transferredDate", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.ResponsibleUser, data: "responsibleUser", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.Notes, data: "notes", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                { title: Resources.ModifiedByUSer, data: "modifiedByUSer", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                { title: Resources.LastModifiedDate, data: "lastModifiedDate", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.FollowUpPanelStatus, data: "followUpPanelStatus", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                 { title: Resources.FollowUpCreatedByUser, data: "followUpCreatedByUser", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
//                {
//                    "className": "text-right",
//                    "autoWidth": false,
//                    "bAutoWidth": false,
//                    width: "16px",
//                    'orderable': false,
//                    'sortable': false,
//                    'render': function (data, type, full, meta) {
//                        var html = "";

//                        let btn = document.createElement("button");
//                        btn.setAttribute("class", "btn btn-xs btn-primary edit");
//                        btn.setAttribute("title", Resources.Edit);
//                        btn.setAttribute("clickAttr", "openfollowUpPanelWindow(" + full.followUpId +")");
//                        btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
//                        html += btn.outerHTML;

//                        let btnHistory = document.createElement("button");
//                        btnHistory.setAttribute("class", "btn btn-xs btn-warning ViewHistory");
//                        btnHistory.setAttribute("title", Resources.ViewHistory);
//                        btnHistory.setAttribute("clickAttr", "openfollowUpPanelHistory(" + full.followUpId + ")");
//                        btnHistory.innerHTML = "<i class='fa fa-eye fa-white'/>";
//                        html += btnHistory.outerHTML;

//                        return "<div style='display: inline-flex;'>" + html + "</div>";
//                    }
//                }
//            ],
//            dom: '<"html5buttons"B>trpi',
//            buttons: [
//                {
//                    className: 'btn-sm btn-danger',
//                    text: Resources.Delete,
//                    action: function () {
//                        var checkedRows = $('#followUpPanelGrdItems tbody').find('input[type="checkbox"]:checked');
//                        if (checkedRows.length > 0) {
//                            Common.showConfirmMsg(Resources.DeleteConfirmation, function () {
//                                var ids = new Array();
//                                checkedRows.each(function (index, obj) {
//                                    ids.push(parseInt(obj.getAttribute('data-id')));
//                                });
//                                Common.ajaxDelete('/FollowUpPanel/Delete',
//                                    {
//                                        'ids': ids, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
//                                    },
//                                    function (data) {
//                                        if (data !== null && data.length > 0) {
//                                            swal.close();
//                                            let msg = "\n ○ ";
//                                            var followUpPanels = data;
//                                            for (var i = 0; i < followUpPanels.length; i++) {
//                                                msg += window.language == "en" ? followUpPanels[i].name : window.language == "fr" ? followUpPanels[i].nameFr : window.language == "ar" ? followUpPanels[i].nameAr : followUpPanels[i].name;
//                                            }
//                                            //setTimeout(function () {
//                                            //    Common.alertMsg(Resources.GroupsInUse + msg);
//                                            //}, 300);

//                                            if (ids.length > followUpPanels.length) {
//                                                retTable.ajax.reload();
//                                            }
//                                        } else {
//                                            retTable.ajax.reload();
//                                        }
//                                    }, null, false);
//                            });
//                        }
//                        else {
//                            Common.alertMsg(Resources.NoRowSelected);
//                        }
//                    }
//                }
//            ]
//        });
//    return retTable;
//}
function drawTable(model) {

    var exportButton = [{
        className: 'btn-sm btn-primary',

        extend: 'print',
        title: function () {
            return Resources.FollowUpPanelReport;
        },
        filename: function () {
            var currentDate = new Date();

            var formattedDate = currentDate.toISOString().split('T')[0];

            var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

            return Resources.FollowUpReport + '_' + formattedDate + '_' + formattedTime;
        },

        exportOptions: {
            columns: [':visible']

        },
        customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.FollowUpReport.print
    },
    {
        className: 'btn-sm btn-primary',

        extend: 'excelHtml5',
        title: function () {
            return Resources.FollowUpPanelReport;
        },
        filename: function () {
            var currentDate = new Date();

            var formattedDate = currentDate.toISOString().split('T')[0];

            var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

            return Resources.FollowUpReport + '_' + formattedDate + '_' + formattedTime;
        },


        exportOptions: {
            columns: [':visible']

        },
        customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.FollowUpReport.excelHTML5
    },
    {
        className: 'btn-sm btn-primary',

        extend: 'pdfHtml5',
        title: function () {
            return Resources.FollowUpPanelReport;
        },
        filename: function () {
            var currentDate = new Date();

            var formattedDate = currentDate.toISOString().split('T')[0];

            var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

            return Resources.FollowUpReport + '_' + formattedDate + '_' + formattedTime;
        },

        exportOptions: {
            columns: [':visible']
        },
        customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.FollowUpReport.pdfHTML5
    }
       
    ];

    let retTable = $("#followUpPanelGrdItems").on('draw.dt', function () {
        $('#followUpPanelGrdItems tbody tr td').each(function (index) {
            if ($(this).hasClass('sortableDateInDatatable')) {
                if ($(this)[0].lastElementChild) {
                    this.setAttribute('title', $(this)[0].lastElementChild.textContent);
                }
            } else {
                this.setAttribute('title', $(this).text());
            }
        });
    }).DataTable({
        processing: true,
        ordering: true,
        serverSide: true,
        pageLength: 10,
        paging: true,
        ajax: {
            url: "/FollowUp/ListFollowUpPanel",
            type: "POST",
            datatype: "json",
            data: function (d) {
                d.NodeId = window.FollowUpPanelNodeId;
                d.FollowUpSubject = $("#txtFollowUpSubject").val() || "";
                d.FollowUpCreatedByUserId = $("#followUpCreatedByUser").val() || "0";
                return d;
            }
        },
        order: [],
        columns: [
            {
                className: 'details-control',
                orderable: false,
                data: null,
                defaultContent: '',
                width: '30px'
            },
            { title: "Id", data: "id", visible: false, orderable: false },
            { title: Resources.SendingEntity, data: "sendingEntity", render: $.fn.dataTable.render.text(), "autoWidth": true },
            { title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "autoWidth": true },
            { title: Resources.DocumentCreatedDate, data: "documentCreatedDate", render: $.fn.dataTable.render.text(), "autoWidth": true },
            { title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "autoWidth": true },
            { title: Resources.Priority, data: "priority", render: $.fn.dataTable.render.text(), "autoWidth": true },
            { title: Resources.FollowUpCreatedByUser, data: "followUpCreatedByUser", render: $.fn.dataTable.render.text(), "autoWidth": true },
            { title: Resources.ModifiedByUSer, data: "modifiedByUSer", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },
            { title: Resources.LastModifiedDate, data: "lastModifiedDate", render: $.fn.dataTable.render.text(), "autoWidth": true/*false, "className": "min-max-width-50-250"*/ },

            {
                "className": "text-right",
                "autoWidth": false,
                "bAutoWidth": false,
                width: "16px",
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta) {
                    var html = "";

                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-primary edit mr-sm");
                    btn.setAttribute("title", Resources.Add);
                    btn.setAttribute("clickAttr", "openfollowUpPanelWindow(" + full.followUpId + ")");
                    btn.innerHTML = "<i class='fa fa-plus fa-white'/>";
                    html += btn.outerHTML;

                    let btnHistory = document.createElement("button");
                    btnHistory.setAttribute("class", "btn btn-xs btn-warning ViewHistory");
                    btnHistory.setAttribute("title", Resources.ViewHistory);
                    btnHistory.setAttribute("clickAttr", "openfollowUpPanelHistory(" + full.followUpId + ")");
                    btnHistory.innerHTML = "<i class='fa fa-file-text fa-white'/>";
                    html += btnHistory.outerHTML;

                    return "<div style='display: inline-flex;'>" + html + "</div>";
                }
            }
        ],
        dom: '<"html5buttons"B>trpi',
        buttons: exportButton,
    });

    // Function to format child row
    function formatChildRow(rowData) {
        return `
            <table style="width:100%" cellspacing="0" border="0">
                <tr><th style="width: 10%;padding:5px"><strong>${Resources.Event}:</strong></th><td style="width: 90%;padding:5px;word-break: break-all;">${rowData.event}</td></tr>
                <tr><th style="width: 10%;padding:5px"><strong>${Resources.EventDate}:</strong></th><td style="width: 90%;padding:5px;word-break: break-all;">${rowData.eventDate}</td></tr>
                <tr><th style="width: 10%;padding:5px"><strong>${Resources.TransferredTo}:</strong></th><td style="width: 90%;padding:5px;word-break: break-all;">${rowData.transferredTo}</td></tr>
                <tr><th style="width: 10%;padding:5px"><strong>${Resources.TransferredDate}:</strong></th><td style="width: 90%;padding:5px;word-break: break-all;">${rowData.transferredDate}</td></tr>
                <tr><th style="width: 10%;padding:5px"><strong>${Resources.ResponsibleUser}:</strong></th><td style="width: 90%;padding:5px;word-break: break-all;">${rowData.responsibleUser}</td></tr>
                <tr><th style="width: 10%;padding:5px"><strong>${Resources.FollowUpPanelStatus}:</strong></th><td style="width: 90%;padding:5px;word-break: break-all;">${rowData.followUpPanelStatus}</td></tr>
                <tr><th style="width: 10%;padding:5px"><strong>${Resources.Notes}:</strong></th><td style="width: 90%;padding:5px;word-break: break-all;">${rowData.notes}</td></tr>
                <tr><th style="width: 10%;padding:5px"><strong>${Resources.CreatedBy}:</strong></th><td style="width: 90%;padding:5px;word-break: break-all;">${rowData.followUpCreatedByUser}</td></tr>
                </table>
        `; 
    }

    // Toggle child row on button click
    $('#followUpPanelGrdItems tbody').on('click', 'td.details-control', function () {
        var tr = $(this).closest('tr');
        var row = retTable.row(tr);

        if (row.child.isShown()) {
            row.child.hide();
            tr.removeClass('shown');
        } else {
            row.child(formatChildRow(row.data())).show();
            tr.addClass('shown');
        }
    });

    return retTable;
}

function openfollowUpPanelWindow(followUpId) {
    var wrapper = $(".modal-window");
    var modelIndex = new followUpPanelIndex.FollowUpPanelIndex();
    modelIndex.followUpId = followUpId;
    var grpIndex = new followUpPanelIndex.FollowUpPanelIndexView(wrapper, modelIndex);
    grpIndex.render();

    $('#modalFollowUpPanelIndexTitle').html(Resources.New);

    $('#modalFollowUpPanelIndex').addClass('modalScroll');
    $('#modalFollowUpPanelIndex').modal('show');
    $("#modalFollowUpPanelIndex").off("hidden.bs.modal");
    $("#modalFollowUpPanelIndex").off("shown.bs.modal");
    $('#modalFollowUpPanelIndex').on('shown.bs.modal', function () {
        document.getElementById('txtEvent').focus();
    });
    $('#modalFollowUpPanelIndex').on('hidden.bs.modal', function () {
        $('#formPostFollowUpPanelIndex').parsley().reset();
        $("#modalFollowUpPanelIndex").remove();
        swal.close();
    });
}

function openfollowUpPanelHistory(followUpId)
{
    var wrapper = $(".modal-window");
    var modelIndex = new followUpPanelItems.FollowUpPanelItems();
    modelIndex.followUpId = followUpId;
    var grpIndex = new followUpPanelItems.FollowUpPanelItemsView(wrapper, modelIndex);
    grpIndex.render();

    $('#modalFollowUpPanelItemsTitle').html(Resources.View);

    $('#modalFollowUpPanelItems').addClass('modalScroll');
    $('#modalFollowUpPanelItems').modal('show');
    $("#modalFollowUpPanelItems").off("hidden.bs.modal");
    $("#modalFollowUpPanelItems").off("shown.bs.modal");
    $('#modalFollowUpPanelItems').on('shown.bs.modal', function () {
    });
    $('#modalFollowUpPanelItems').on('hidden.bs.modal', function () {
        $("#modalFollowUpPanelItems").remove();
        swal.close();
    });
}
class FollowUpPanel extends Intalio.Model {
    constructor() {
        super();
    }
}
class FollowUpPanelView extends Intalio.View {
    constructor(element, model) {
        super(element, "followUpPanel", model);
    }
    render() {
        var model = this.model;
        $.fn.select2.defaults.set("theme", "bootstrap");
        var headers = {};
        headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
        var usersUrl = location.origin + '/User/GetUsersStructuresFromCTS';
        $('#followUpCreatedByUser').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $('#followUpCreatedByUserContainer'),
            width: "100%",
            templateResult: function (option) {

                let $option = renderTextInSelectStructures(option)
                return $option;
            },
            ajax: {
                delay: 400,
                url: usersUrl,
                type: "POST",
                dataType: 'json',

                headers: typeof headers !== "undefined" ? headers : "",
                data: function (term) {
                    return {
                        "searchText": term.term ? term.term : "", "structureType": StructureTypeMode.Internal, 'fromSendingandReceiving': false
                    };
                },
                processResults: function (data, term) {
                    data = window.EnablePerStructure ? data.filter(item => item.id.toString() == $('#hdLoggedInStructureId').val()) : data;
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        if (val.userStructure != null && val.userStructure.length > 0) {
                            $.each(val.userStructure, function (key, userStructureVal) {
                                var item = {};
                                item.id = userStructureVal.userId;
                                var structureName = userStructureVal.structure.name;
                                var userName = userStructureVal.user.firstname + " " + userStructureVal.user.lastname
                                item.text = structureName + " / " + userName;
                                item.icon = "fa fa-user-o";
                                item.isStructure = false;
                                item.dataId = userStructureVal.userId;
                                item.structureId = userStructureVal.structureId;
                                listitemsMultiList.push(item);
                            });
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });


        $('#collapseIcon').click(function () {
            $('#collapseIcon').empty();
            if (clickedSearch) {
                $('#collapseIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
                $('#collapsePanel').attr('class', '');
                $('#collapsePanel').addClass('panel-body panel-collapse collapse in');
                clickedSearch = false;
            } else {
                $('#collapseIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
                $('#collapsePanel').attr('class', '');
                $('#collapsePanel').addClass('panel-body panel-collapse collapse');
                clickedSearch = true;
            }
        });

        $("#btnSearch").on('click', function () {
            table.ajax.reload();
        });
        $("#btnClear").on('click', function () {
            $("#txtFollowUpSubject").val('');
            $("#followUpCreatedByUser").val('').trigger('change');
            table.ajax.reload();
        });
        $('#txtFollowUpSubject').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnClear').focus();
                }
                else {
                    $('#btnSearch').focus();
                }
            }
            if (code === 13) {
                $('#btnSearch').click();
            }
        });
        $('#btnClear').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnSearch').focus();
                }
                else {
                    $('#txtFollowUpSubject').focus();
                }
            }
        });
        Common.gridCommon();
        table = drawTable(model);
        $('#followUpPanelGrdItems tbody').on('click', 'tr', function () {
            var input = this.getElementsByTagName('input')[0];
            if (typeof input !== "undefined")
                input.checked = input.checked ? false : true;
        });
        $('#followUpPanelGrdItems tbody').on('click', ".edit,.ViewHistory", function () {
            
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        $('#chkAll').change(function () {
            $('tbody tr td input[type="checkbox"]').prop('checked', $(this).prop('checked'));
        });
    }
}
export default { FollowUpPanel, FollowUpPanelView };