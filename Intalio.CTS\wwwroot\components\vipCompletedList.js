﻿import Intalio from './common.js'
import VipDocumentDetails from './vipDocumentDetails.js'
import SendTransferModal from './sendTransfer.js'
import { CategoryModel, Categories, IdentityService, DelegationUsers } from './lookup.js'
import SendToReceivingEntityIndex from './sendToReceivingEntity.js'
import Transfer from './transfer.js'
import DocumentCompleted from './completedList.js'

class VipDocumentCompleted extends Intalio.Model
{
    constructor()
    {
        super();
        this.title = null;
        this.nodeId = null;
        this.delegationId = null;
        this.categories = null;
    }
}
function buildFilters(nodeId, categories)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var filters = node.filters !== "" && node.filters !== null ? JSON.parse(node.filters) : [];
    if (filters.length > 0)
    {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++)
        {
            var filter = filters[i];
            switch (filter)
            {
                case "ReferenceNumber":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
                        '<input type="text" id="txtFilterCompletedReferenceNumber" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "FromDate":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label>' +
                        '<div class="input-group date"><input id="filterCompletedFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterCompletedFromDateError">' +
                        '<span class="input-group-addon" id="filterCompletedFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterCompletedFromDateError"></div></div></div></div>';
                    break;
                case "ToDate":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label>' +
                        '<div class="input-group date"><input id="filterCompletedToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterCompletedToDateError">' +
                        '<span class="input-group-addon" id="filterCompletedToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterCompletedToDateError"></div></div></div></div>';
                    break;
                case "Category":
                    clearFix += 6;
                    var div = '<div class="col-md-6" id="categoryFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterCompletedCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++)
                    {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterCompletedCategoryError"></div></div></div>';
                    html += div;
                    break;
                case "Subject":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterCompletedSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Purpose":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="purposeFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.Purpose + '</label>' +
                        '<select id="cmbFilterCompletedPurpose" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedPurposeError" class="form-control"></select></div></div>';
                    break;
                case "Priority":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="priorityFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.Priority + '</label>' +
                        '<select id="cmbFilterCompletedPriority" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedPriorityError" class="form-control"></select></div></div>';
                    break;
                case "Privacy":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="privacyFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.Privacy + '</label>' +
                        '<select id="cmbFilterCompletedPrivacy" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedPrivacyError" class="form-control"></select></div></div>';
                    break;
                case "Structure":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="structureFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.Structure + '</label>' +
                        '<select id="cmbFilterCompletedStructure" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedStructureError" class="form-control"></select></div></div>';
                    break;
                case "User":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="userFilterCompletedContainer"><div class="form-group"><label class="control-label">' + Resources.User + '</label>' +
                        '<select id="cmbFilterCompletedUser" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterCompletedUserError" class="form-control"></select></div></div>';
                    break;
            }
            if (clearFix === 12 || clearFix === 11)
            {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '</div>';
        $('#filtersContainer').html(html);

        var clickedSearch = false;
        $('#collapseCompletedIcon').click(function ()
        {
            $('#collapseCompletedIcon').empty();
            if (clickedSearch)
            {
                $('#collapseCompletedIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
                $('#collapseCompletedPanel').attr('class', '');
                $('#collapseCompletedPanel').addClass('panel-body panel-collapse collapse in');
                clickedSearch = false;
            } else
            {
                $('#collapseCompletedIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
                $('#collapseCompletedPanel').attr('class', '');
                $('#collapseCompletedPanel').addClass('panel-body panel-collapse collapse');
                clickedSearch = true;
            }
        });
        $("#btnFilterCompletedSearch").on('click', function ()
        {
            gFromSearch = true;
            gNoMoreData = false;
            gPageIndex = 0;
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            loadCompletedList();
        });
        $("#btnFilterCompletedClear").on('click', function ()
        {
            $("#cmbFilterCompletedPurpose").val('').trigger('change');
            $("#cmbFilterCompletedPriority").val('').trigger('change');
            $("#cmbFilterCompletedPrivacy").val('').trigger('change');
            $("#cmbFilterCompletedCategory").val('').trigger('change');
            $("#cmbFilterCompletedStatus").val('').trigger('change');
            $("#txtFilterCompletedReferenceNumber").val('');
            $("#txtFilterCompletedSubject").val('');
            fromDate.clear();
            toDate.clear();
            $("#cmbFilterCompletedStructure").val('').trigger('change');
            $("#cmbFilterCompletedUser").val('').trigger('change');

            gFromSearch = true;
            gNoMoreData = false;
            gPageIndex = 0;
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            loadCompletedList();
        });
        $('#cmbFilterCompletedPurpose').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Purposes().get(window.language),
            dropdownParent: $('#purposeFilterCompletedContainer')
        });
        $("#cmbFilterCompletedPurpose").val('').trigger('change');

        $('#cmbFilterCompletedPriority').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Priorities().get(window.language),
            dropdownParent: $('#priorityFilterCompletedContainer')
        });
        $("#cmbFilterCompletedPriority").val('').trigger('change');

        $('#cmbFilterCompletedPrivacy').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Privacies().get(window.language),
            dropdownParent: $('#privacyFilterCompletedContainer')
        });
        $("#cmbFilterCompletedPrivacy").val('').trigger('change');

        $('#cmbFilterCompletedCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterCompletedContainer')
        });
        $("#cmbFilterCompletedCategory").val('').trigger('change');

        $('#cmbFilterCompletedStructure').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#structureFilterCompletedContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes',
                type: "POST",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term)
                {
                    return {
                        "text": term.term ? term.term : "", "language": window.language, "attributes": [window.StructureNameAr, window.StructureNameFr]
                    };
                },
                processResults: function (data)
                {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val)
                    {
                        var structureName = val.name;
                        if (val.attributes != null && val.attributes.length > 0)
                        {
                            var attributeLang = $.grep(val.attributes, function (e)
                            {
                                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                            });
                            if (attributeLang.length > 0)
                            {
                                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                            }
                        }
                        var item = {};
                        item.id = val.id;
                        item.text = structureName;
                        item.isStructure = true;
                        item.dataId = val.id;
                        listitemsMultiList.push(item);
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        }).on("change", function ()
        {
            if (document.getElementById('cmbFilterCompletedUser') !== null)
            {
                var type = "GET";
                var url = '/api/SearchUsers';
                //var structures = $('#cmbFilterCompletedStructure').val();
                //if (structures !== "" && structures !== null)
                //{
                //    type = "POST";
                //    url = '/api/SearchUsersByStructureIds';
                //}
                $('#cmbFilterCompletedUser').select2({
                    minimumInputLength: 0,
                    allowClear: false,
                    placeholder: "",
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    dropdownParent: $("#userFilterCompletedContainer"),
                    multiple: true,
                    width: "100%",
                    ajax: {
                        delay: 400,
                        url: window.IdentityUrl + url,
                        type: type,
                        dataType: 'json',
                        headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                        data: function (term)
                        {
                            var params = { "text":  "", "language": window.language };
                            //var params = { "text": term.term ? term.term : "", "language": window.language };
                            //var structures = $('#cmbFilterCompletedStructure').val();
                            //if (structures !== "" && structures !== null)
                            //{
                            //    params.ids = $('#cmbFilterCompletedStructure').val();
                            //}
                            return params;
                        },
                        processResults: function (data, term)
                        {
                            var termSearch = term.term ? term.term : "";

                            var structures = $('#cmbFilterCompletedStructure').val();
                            var listitemsMultiList = [];
                            $.each(data, function (key, val)
                            {
                                if (structures !== "" && structures !== null &&
                                    !((val.structureIds != null && val.structureIds.some(r => structures.indexOf(String(r)) >= 0)) ||
                                        structures.includes(val.defaultStructureId))) {
                                    return;
                                }
                                var fullName = val.fullName;
                                if (window.language != 'en') {
                                    fullName = getFullNameByLangauge(val);
                                    fullName = fullName.trim() == "" ? val.fullName : fullName;
                                }
                                var allNames = getFullNameInAllLangauge(val);
                                if (allNames.length == 0) allNames.push(fullName);
                                if (termSearch != "" &&
                                    !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                                    return;
                                }
                                let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                                if (!isExist) {
                                    var item = {};
                                    item.id = val.id;
                                    item.text = fullName;
                                    item.isStructure = false;
                                    item.dataId = val.id;
                                    listitemsMultiList.push(item);
                                }
                            });
                            return {
                                results: listitemsMultiList
                            };
                        }
                    },
                    sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
                }).val("").trigger("change");
            }
        });
        $("#cmbFilterCompletedStructure").val('').trigger('change');
        $('#cmbFilterCompletedUser').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#userFilterCompletedContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchUsers',
                type: "GET",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term)
                {
                    return { "text":  "", "language": window.language };
                    //return { "text": term.term ? term.term : "", "language": window.language };
                },
                processResults: function (data, term)
                {
                    var termSearch = term.term ? term.term : "";

                    var listitemsMultiList = [];
                    $.each(data, function (key, val)
                    {
                        var fullName = val.fullName;
                        if (window.language != 'en') {
                            fullName = getFullNameByLangauge(val);
                            fullName = fullName.trim() == "" ? val.fullName : fullName;
                        }
                        var allNames = getFullNameInAllLangauge(val);
                        if (allNames.length == 0) allNames.push(fullName);
                        if (termSearch != "" &&
                            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                            return;
                        }
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = fullName;
                            item.isStructure = false;
                            item.dataId = val.id;
                            listitemsMultiList.push(item);
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });
        $("#cmbFilterCompletedUser").val('').trigger('change');

        var fromDate = $('#filterCompletedFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    maxDate: jQuery('#filterCompletedToDate').val() && jQuery('#filterCompletedToDate').val() !== "" ? jQuery('#filterCompletedToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterCompletedFromDate_img").click(function ()
        {
            fromDate.toggle();
        });
        var toDate = $('#filterCompletedToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    minDate: jQuery('#filterCompletedFromDate').val() && jQuery('#filterCompletedFromDate').val() !== "" ? jQuery('#filterCompletedFromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterCompletedToDate_img").click(function ()
        {
            toDate.toggle();
        });
        $('#txtFilterCompletedReferenceNumber').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterCompletedSearch').focus();
                }
                else
                {
                    $('#filterCompletedFromDate').focus();
                }
            }
        });
        $('#btnFilterCompletedClear').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterCompletedSearch').focus();
                }
                else
                {
                    $('#filterCompletedFromDate').focus();
                }
            }
        });
    } else
    {
        $("#btnOpenSearchCompletedModal").remove();
        $("#divSearchCompleted").remove();
    }
}
function openDocument(id, delegationId, nodeId)
{
    var params = { id: id };
    if (delegationId !== null)
    {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetDocumentBasicInfoByTransferId', params, function (response)
    {
        $('.card-max-width').addClass('card-min-width');
        $('.card-max-width').removeClass('card-max-width');
        $('input:checkbox').removeAttr('checked');
        $('#completedListContainer li').removeClass("active");
        $("input[data-id='" + id + "']").parent().parent().parent().addClass("active");
        $("input[data-id='" + id + "']").prop('checked', true);
        gSelectedRowId = id;
        gLocked = false;
        let item = "liCompleted" + nodeId;
        if (delegationId !== null)
        {
            item = "completed-" + nodeId;
        }
        Common.setActiveSidebarMenu(item);
        $(".delegation").removeClass("active");

        var model = new VipDocumentDetails.VipDocumentDetails();
        model.readonly = true;
        model.delegationId = delegationId;
        model.id = id;
        model.documentId = response.id;
        model.referenceNumber = response.referenceNumber;
        model.categoryName = response.categoryName;
        model.statusId = response.status;
        model.createdByUser = response.createdByUser;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.showMyTransfer = true;
        model.attachmentId = response.attachmentId;
        model.categoryId = response.categoryId;
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
        model.tabs = $.grep(tabs, function (element, index)
        {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
        });
        model.tabsWithStatic = tabs;
        model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
        model.showBackButton = false;
        model.nodeId = nodeId;
        model.showPreview = window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer" && response.attachmentId !== null;
        model.attachmentCount = response.attachmentCount;
        model.noteCount = response.notesCount;
        model.linkedCount = response.linkedCorrespondanceCount
        model.attachmentVersion = response.attachmentVersion;

        var wrapper = $("#completedDocumentDetailsContainer");
        wrapper.empty();
        $(".modal-window").empty();
        var view = new VipDocumentDetails.VipDocumentDetailsView(wrapper, model);
        view.render();
        $(".documentHeader").hide();
        $(".waitingBackground").removeClass("waitingBackground");
        $(".vipDetailsPanel").show();
        $(".vipCorrLeftPanel").removeClass("col-lg-12 col-md-12 col-sm-12 col-xs-12");
        $(".vipCorrLeftPanel").addClass("col-lg-3 col-md-4 col-sm-4 col-xs-12");
        $(".mdl-ul").removeAttr("style")
    }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
}
function loadCompletedList()
{
    if (!gNoMoreData)
    {
        Common.mask(document.getElementById('completedListContainer'), "completedListContainer-mask");
        var params = {};
        addFilters(params);
        params.NodeId = gSelf.model.nodeId;
        params.DelegationId = gSelf.model.delegationId;
        params.start = gPageIndex;
        params.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
        Common.ajaxPost('/Transfer/ListCompletedVip', params, function (response)
        {
            if (response.length > 0)
            {
                gPageIndex += window.Paging;
                if (response.length < window.Paging)
                {
                    gNoMoreData = true;
                }
            } else
            {
                gNoMoreData = true;
            }
            createListData(response);
            gLocked = false;
            Common.unmask("completedListContainer-mask");
            if (gFromSearch)
            {
                $("#divSearchCompleted").fadeOut();
            }
        }, function () { gLocked = false; Common.showScreenErrorMsg(); });
    } else
    {
        gLocked = false;
    }
}
function createListData(data)
{
    var html = '';
    if (data.length === 0 && gPageIndex === 0)
    {
        html = '<ul class="mdl-ul"><div class="nodata dataTables_empty pt-lg">' + Resources.NoDataToDisplay + '</div></ul>';
        $('#completedListContainer').html(html);
    } else if (data.length > 0)
    {
        html = '<ul class="mdl-ul" style="margin: 0px 5px 0 0;">';
        var htmlLi = '';
        var color = "";

        for (var i = 0; i < data.length; i++)
        {
            var transfer = data[i];
            var liClass = "mdl-li";
            if (!transfer.isRead)
            {
                liClass += " unread";
            }
            var htmlIcons = "";
            if (transfer.importanceId)
            {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var j = 0; j < importances.length; j++)
                {
                    if (importances[j].id === transfer.importanceId)
                    {
                        htmlIcons += "<i class='fa fa-exclamation fa-lg mr-sm' title='" + importances[j].text + "' style='color:" + importances[j].color + "'></i>";
                    }
                }
            }
            var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            var privacies = new CoreComponents.Lookup.Privacies().get(window.language);


            var transfer = data[i];
            if (window.PriorityPrivacyAction == "2") {
                for (var j = 0; j < privacies.length; j++) {
                    if (privacies[j].id === transfer.privacyId) {
                        color = privacies[j].color;
                        break;
                    }
                }
            } else {
                for (var j = 0; j < priorities.length; j++) {
                    if (priorities[j].id === transfer.priorityId) {
                        color = priorities[j].color;
                        break;
                    }
                }
            }
            transfer.referenceNumber = transfer.referenceNumber ?? "";
            var from = transfer.fromStructure !== "" ? transfer.fromStructure + '/' + transfer.fromUser : transfer.fromUser;           
            htmlLi += '<li class="' + liClass + '">';
            htmlLi += '<div class="mdl-container">';
            htmlLi += '<div id="leftbox" class="pull-left">';
            htmlLi += '<div class="inside_color_line pull_left"></div>';
            htmlLi += '<input data-id=' + transfer.id + ' data-categoryid=' + transfer.categoryId + ' type="checkbox" class="pointer m-sm ml-10px" onclick="event.stopPropagation();">';
            htmlLi += "<input type='hidden' data-id=" + transfer.id + " value='" + JSON.stringify(transfer) + "'/>";
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-circle icon-primary'></i></span>"
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-arrow-left'></i></span>"
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-arrow-right'></i></span>"
            htmlLi += '</div>';
            htmlLi += '<div id="middlebox" class="mdl-text-msg pull-left pl-sm card-max-width">';
            //htmlLi += '<span class="dot"></span>';
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + transfer.referenceNumber + '">' + transfer.referenceNumber + '</span>';
            htmlLi += '<span class="mdl-span text-primary bold" style="color:' + color + '" title="' + transfer.subject + '">' + transfer.subject + '</span>';
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + from + '">' + from + '</span>';
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + transfer.toStructure + '">' + transfer.toStructure + '</span>';
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + transfer.purposeName + '">' + transfer.purposeName + '</span>';

            htmlLi += '</div>';
            htmlLi += '<div id="rightbox" class="pull-right text-right"><div class="mdl-time mr-sm" title="' + Resources.TransferDate + '">' + dateFormat(transfer.transferDate) + '</div>';
            if (htmlIcons !== "")
            {
                htmlLi += '<div class="mdl-action">' + htmlIcons + '</div>';
            }
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</li>';
        }
        html += htmlLi;
        html += '</ul>';
        if (gPageIndex === 15)
        {
            $('#completedListContainer').html(html);
        } else
        {
            $('#completedListContainer ul').append(htmlLi);
        }
    }
}
function addFilters(d)
{
    d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
    if (gFromSearch)
    {
        d.PriorityId = $("#cmbFilterCompletedPriority").val() !== null && typeof $("#cmbFilterCompletedPriority").val() !== "undefined" ? $("#cmbFilterCompletedPriority").val() : "0";
        d.PrivacyId = $("#cmbFilterCompletedPrivacy").val() !== null && typeof $("#cmbFilterCompletedPrivacy").val() !== "undefined" ? $("#cmbFilterCompletedPrivacy").val() : "0";
        d.PurposeId = $("#cmbFilterCompletedPurpose").val() !== null && typeof $("#cmbFilterCompletedPurpose").val() !== "undefined" ? $("#cmbFilterCompletedPurpose").val() : "0";
        d.CategoryId = $("#cmbFilterCompletedCategory").val() !== null && typeof $("#cmbFilterCompletedCategory").val() !== "undefined" ? $("#cmbFilterCompletedCategory").val() : "0";
        d.ReferenceNumber = $("#txtFilterCompletedReferenceNumber").val() !== "" && typeof $("#txtFilterCompletedReferenceNumber").val() !== "undefined" ? $("#txtFilterCompletedReferenceNumber").val() : "";
        d.Subject = $("#txtFilterCompletedSubject").val() !== "" && typeof $("#txtFilterCompletedSubject").val() !== "undefined" ? $("#txtFilterCompletedSubject").val() : "";
        d.FromDate = $("#filterCompletedFromDate").val() !== "" && typeof $("#filterCompletedFromDate").val() !== "undefined" ? $("#filterCompletedFromDate").val() : "";
        d.ToDate = $("#filterCompletedToDate").val() !== "" && typeof $("#filterCompletedToDate").val() !== "undefined" ? $("#filterCompletedToDate").val() : "";
        d.StructureIds = $("#cmbFilterCompletedStructure").val() !== null && typeof $("#cmbFilterCompletedStructure").val() !== "undefined" ? $("#cmbFilterCompletedStructure").val() : [];
        d.UserIds = $("#cmbFilterCompletedUser").val() !== null && typeof $("#cmbFilterCompletedUser").val() !== "undefined" ? $("#cmbFilterCompletedUser").val() : [];
    }
}
function dateFormat(dateText)
{
    var dateFull = dateText.split(" ")[0].split("/");
    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth() + 1; //January is 0!
    var yy = today.getFullYear();
    var time = DateConverter.toHijriFormated(dateText.split(" ")[0], null, window.CalendarType);
    if (parseInt(dateFull[0]) === dd && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy)
    {
        time = "";
        var timeSeparator = ":";
        var hours = parseInt(dateText.split(" ")[1].split(":")[0]);
        var amPm = Resources.AM;
        if (hours > 12)
        {
            time += (hours - 12) + timeSeparator;
            amPm = Resources.PM;
        } else if (hours === 12)
        {
            time += "12" + timeSeparator;
            amPm = Resources.PM;
        } else
        {
            time += (hours < 10 ? '0' : '') + hours + timeSeparator;
            amPm = Resources.AM;
        }
        var minutes = parseInt(dateText.split(" ")[1].split(":")[1]);
        minutes = (minutes < 10 ? '0' : '') + minutes;
        time += minutes + " " + amPm;
    } else if (parseInt(dateFull[0]) === (dd - 1) && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy)
    {
        time = Resources.Yesterday;
    }
    return time;
}
var gLocked = false;
var gNoMoreData = false;
var gFromSearch = false;
var gPageIndex = 0;
var gSelectedRowId, gSelf;
var wrapperParent;
class VipDocumentCompletedView extends Intalio.View
{
    constructor(element, model)
    {
        wrapperParent = model;
        super(element, "vipcompleted", model);
    }
    render()
    {
        $.fn.select2.defaults.set("theme", "bootstrap");
        gSelf = this;
        gLocked = false;
        gPageIndex = 0;
        gNoMoreData = false;
        gFromSearch = false;
        gSelectedRowId = null;
        var followUpCategoryIndex = gSelf.model.categories.findIndex(item => item.id == window.FollowUpCategory);
        gSelf.model.categories.splice(followUpCategoryIndex, 1);

        buildFilters(gSelf.model.nodeId, gSelf.model.categories);
        loadCompletedList();
        var lastScrollTop = 0;
        $('#completedListContainer').on('scroll', function ()
        {
            var div = $(this);
            var st = div.scrollTop();
            if (st > lastScrollTop && st + div.innerHeight() + 5 >= div[0].scrollHeight)
            {
                if (!gLocked)
                {
                    gLocked = true;
                    try
                    {
                        loadCompletedList();
                    } catch (e)
                    {
                        gLocked = false;
                    }
                }
            }
            lastScrollTop = st;
        });
        $('#completedListContainer').on('click', 'li', function (e)
        {
            if ($(e.target).hasClass("lockIcon") || $(e.target).hasClass("unlockIcon"))
            {
                return;
            }
            if (!gLocked)
            {
                gLocked = true;
                try
                {
                    var input = this.getElementsByTagName('input')[0];
                    if (typeof input !== "undefined")
                    {
                        if (!$(this).hasClass("active"))
                        {
                            var id = input.getAttribute("data-id");
                            if (gSelectedRowId !== id)
                            {
                                if (!$(".filterInfoDiv").hasClass("hidden")) {
                                    $(".filterInfoDiv").addClass("hidden");
                                }
                                openDocument(id, gSelf.model.delegationId, gSelf.model.nodeId);
                            } else
                            {
                                gLocked = false;
                            }
                        } else
                        {
                            gLocked = false;
                        }
                    } else
                    {
                        gLocked = false;
                    }
                } catch (e)
                {
                    gLocked = false;
                }
            }
        });
        $('#completedListContainer').on('click', 'input', function ()
        {
            var input = this;
            if (typeof input !== "undefined")
            {
                input.checked = input.checked ? false : true;
            }
        });
        $('#chkAll').change(function ()
        {
            var checked = $(this).prop('checked');
            $('#completedListContainer input[type="checkbox"]').prop('checked', checked);
        });
        $(document).click(function (e)
        {
            if ($(e.target).hasClass("select2-selection__choice__remove") || e.target.tagName.toLowerCase() === "body")
            {
                return;
            }
        });
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        SecurityMatrix.initContextMenuVip(securityMatrix, gSelf.model.nodeId);
        SecurityMatrix.initToolbarMenuVip(securityMatrix, gSelf.model.nodeId, 'completedListContainer');

        $('.filterInfoDiv').draggable({ containment: 'window', cancel: '.cancelDrag' });
        $("#btnOpenSearchCompletedModal").on("click", function () {
            if (window.language == "ar") {
                if ($(".vipDetailsPanel").css('display') == 'none') {
                    var position = $("#btnOpenSearchCompletedModal").position().left + $("#btnOpenSearchCompletedModal").width() + 970;
                    $(".filterInfoDiv").attr('style', 'right:' + position + 'px;top:170px;');
                }
                else
                    $(".filterInfoDiv").attr('style', 'right:' + $("#btnOpenSearchCompletedModal").position().right + 'px;top:170px;');
            }
            else {
                if ($(".vipDetailsPanel").css('display') == 'none') {
                    var position = $("#btnOpenSearchCompletedModal").position().left - 220;
                    $(".filterInfoDiv").attr('style', 'left:' + position + 'px;top:170px;');

                }
                else
                    $(".filterInfoDiv").attr('style', 'left:' + $("#btnOpenSearchCompletedModal").position().left + 'px;top:170px;');
            }
            if ($(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").removeClass("hidden");
            } else {
                $(".filterInfoDiv").addClass("hidden");
            }
        });

        $("#btnFilterCloseIcon").on("click", function () {
            $(".filterInfoDiv").addClass("hidden");
        });

        var actions = $.grep(securityMatrix.SecurityNodes[Number(gSelf.model.nodeId)].Actions, function (element, index)
        {
            return element.Type === Number(TypeAction.Toolbar);
        });
        if (actions.length === 0)
        {
            $("#completedRow").addClass("hideCheckbox");
        }
        $('#filtersContainer input').off('keydown');
        $('#filtersContainer input').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                $("#btnFilterCompletedSearch").trigger('click');
            }
        });

        $('.toggleVIP').on('click', function () {
            if (window.InboxMode === "LocalVIPView") {
                window.InboxMode = "InboxDefault";
            } else if (window.InboxMode !== "InboxDefault") {
                window.InboxMode = "LocalInboxDefaultView";
            }

            let wrapper = $(".content-wrapper");
            let defaultmodel = new DocumentCompleted.DocumentCompleted();
            defaultmodel.nodeId = wrapperParent.nodeId;
            defaultmodel.delegationId = wrapperParent.delegationId;
            defaultmodel.categories = new Categories().get(window.language, wrapperParent.delegationId);
            defaultmodel.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            defaultmodel.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            defaultmodel.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            defaultmodel.title = $('.content-heading').text();
            let documentView = new DocumentCompleted.DocumentCompletedView(wrapper, defaultmodel);
            documentView.render();
        })
    }
}
export default { VipDocumentCompleted, VipDocumentCompletedView };