﻿using Intalio.Core;
using Intalio.Core.Interfaces;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using Intalio.IAM.Core;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Constants = Intalio.Core.Constants;


namespace Intalio.CTS.Core.DAL
{
    public partial class FollowUpPanel : Intalio.Core.Interfaces.IDbObject<FollowUpPanel>, IDisposable
    {

        #region Private Fields

        private CTSContext _ctx;

        #endregion

        #region Properties

        public long Id { get; set; }
        public long FollowUpId { get; set; }
        public string Event { get; set; }
        public DateTime? EventDate { get; set; }
        public string TransferredTo { get; set; }
        public DateTime? TransferredDate { get; set; }
        public string ResponsibleUser { get; set; }
        public string Notes { get; set; }
        public long? ModifiedByUSerId { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string FollowUpPanelStatus { get; set; }
        public long? CreatedByUserId { get; set; }
        public DateTime? CreatedDate { get; set; }

        public virtual FollowUp FollowUp { get; set; }
        public virtual User ModifiedByUSer { get; set; }
        public virtual User CreatedByUser { get; set; }

        #endregion

        #region Public Methods
        public FollowUpPanel Find(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FollowUpPanel.AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }
        public async Task<FollowUpPanel> FindWithIncludeAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.FollowUpPanel
                                   .Include(f => f.FollowUp)
                                   .Include(f => f.CreatedByUser)
                                   .Include(f => f.ModifiedByUSer).AsNoTracking()
                                   .FirstOrDefaultAsync(t => t.Id == id);
            }
        }
        public async Task<FollowUpPanel> Insert(FollowUpPanel item)
        {
            item.Insert();
            return await item.FindWithIncludeAsync(item.Id);
        }
        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                CreatedDate = DateTime.Now;
                ctx.FollowUpPanel.Add(this);
                ctx.SaveChanges();
            }
        }
        public void Update(long userId)
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }
        public void Update()
        {

        }
        public void Delete(long id)
        {
            using CTSContext ctx = new CTSContext();
            FollowUpPanel entity = new FollowUpPanel
            {
                Id = (short)id
            };
            ctx.FollowUpPanel.Attach(entity);
            ctx.FollowUpPanel.Remove(entity);
            ctx.SaveChanges();
        }
        public void Delete()
        {
            Delete(Id);
        }
        public List<FollowUpPanel> List()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FollowUpPanel.AsNoTracking().ToList();
            }
        }
        public List<FollowUpPanel> ListWithInclude()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FollowUpPanel
                    .Include(f => f.FollowUp)
                    .Include(f => f.FollowUp).ThenInclude(d => d.OriginalDocument)
                    .Include(f => f.FollowUp).ThenInclude(d => d.OriginalDocument).ThenInclude(d => d.SendingEntity)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument).ThenInclude(d => d.Priority)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument).ThenInclude(d => d.CreatedByUser)
                    .Include(f => f.CreatedByUser)
                    .Include(f => f.ModifiedByUSer)
                    .AsNoTracking().ToList();
            }
        }
        public async Task<List<FollowUpPanel>> ListByFollowUpIdWithInclude(long followUpId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FollowUpPanel
                    .Include(f => f.FollowUp)
                    .Include(f => f.FollowUp).ThenInclude(d => d.OriginalDocument)
                    .Include(f => f.FollowUp).ThenInclude(d => d.OriginalDocument).ThenInclude(d => d.SendingEntity)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument).ThenInclude(d => d.Priority)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument).ThenInclude(d => d.CreatedByUser)
                    .Include(f => f.CreatedByUser)
                    .Include(f => f.ModifiedByUSer)
                    .AsNoTracking().Where(t => t.FollowUpId == followUpId).ToList();
            }
        }
        public async Task<(int count, List<FollowUpPanel> list)> ListFollowUpPanel(
       int startIndex,
       int pageSize,
       List<long> followUpIds,
       List<Intalio.Core.SortExpression<FollowUpPanel>> sortExpressions = null)
        {
            using (var ctx = new CTSContext())
            {
                var rawList = await ctx.FollowUpPanel
                    .Include(f => f.FollowUp)
                        .ThenInclude(d => d.OriginalDocument)
                            .ThenInclude(d => d.SendingEntity)
                    .Include(f => f.FollowUp)
                        .ThenInclude(d => d.FollowUpDocument)
                            .ThenInclude(d => d.Priority)
                    .Include(f => f.FollowUp)
                        .ThenInclude(d => d.FollowUpDocument)
                            .ThenInclude(d => d.CreatedByUser)
                    .Include(f => f.CreatedByUser)
                    .Include(f => f.ModifiedByUSer)
                    .AsNoTracking()
                    .Where(d => followUpIds.Contains(d.FollowUpId))
                    .ToListAsync();

                var groupedList = rawList
                    .GroupBy(f => f.FollowUpId)
                    .Select(g => g.OrderByDescending(p => p.ModifiedDate).FirstOrDefault())
                    .AsQueryable();

                if (sortExpressions != null)
                {
                    groupedList = groupedList.DynamicOrderBy(sortExpressions);
                }
                else
                {
                    groupedList = groupedList.OrderByDescending(c => c.CreatedDate);
                }

                int total = groupedList.Count();
                var pagedList = pageSize < 0
                    ? groupedList.ToList()
                    : groupedList.Skip(startIndex).Take(pageSize).ToList();

                return (total, pagedList);
            }
        }


        public async Task<(int count, List<FollowUpPanel> list)> ListFollowUpPanelItemsAsync(int startIndex, int pageSize, List<long> followUpIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<FollowUpPanel> query = ctx.FollowUpPanel
                    .Include(f => f.FollowUp)
                    .Include(f => f.FollowUp).ThenInclude(d => d.OriginalDocument)
                    .Include(f => f.FollowUp).ThenInclude(d => d.OriginalDocument).ThenInclude(d => d.SendingEntity)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument).ThenInclude(d => d.Priority)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument).ThenInclude(d => d.CreatedByUser)
                    .Include(f => f.CreatedByUser)
                    .Include(f => f.ModifiedByUSer)
                    .AsNoTracking();


                query = query.Where(d => followUpIds.Contains(d.FollowUpId)).OrderByDescending(p => p.ModifiedDate);
                var list = pageSize < 0
                 ? query.ToList()
                 : query.Skip(startIndex).Take(pageSize).ToList();
                return ((await query.ToListAsync()).Count(), list);
            }
        }
        public async Task<(int count, List<FollowUpPanel> list)> ListFollowUpPanelHistoryAsync(int startIndex, int pageSize, List<long> followUpIds, List<Intalio.Core.SortExpression<FollowUpPanel>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<FollowUpPanel> query = ctx.FollowUpPanel
                    .Include(f => f.FollowUp)
                    .Include(f => f.FollowUp).ThenInclude(d => d.OriginalDocument)
                    .Include(f => f.FollowUp).ThenInclude(d => d.OriginalDocument).ThenInclude(d => d.SendingEntity)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument).ThenInclude(d => d.Priority)
                    .Include(f => f.FollowUp).ThenInclude(d => d.FollowUpDocument).ThenInclude(d => d.CreatedByUser)
                    .Include(f => f.CreatedByUser)
                    .Include(f => f.ModifiedByUSer)
                    .AsNoTracking();


                query = query.Where(d => followUpIds.Contains(d.FollowUpId));


                //if (sortExpression != null)
                //{
                //    query = query.DynamicOrderBy(sortExpression);
                //}

                var list = pageSize < 0
                     ? query.ToList()
                     : query.Skip(startIndex).Take(pageSize).ToList();

                return ((await query.ToListAsync()).Count(), list);
            }
        }
        //public async Task<int> GetFollowUpPanelCountAsync(List<long> followUpIds)
        //{

        //    using (var ctx = new CTSContext())
        //    {
        //        OpenDbContext();
        //        IQueryable<FollowUpPanel> query = _ctx.FollowUpPanel.AsNoTracking();


        //           var list =  query.Where(d=>followUpIds.Contains(d.FollowUpId))
        //            .GroupBy(f => f.FollowUpId)
        //            .Select(g => g.OrderByDescending(p => p.ModifiedDate).FirstOrDefault()).ToList().Count();

        //        return await query.CountAsync();

        //    }
        //}

        #endregion

        #region Private Methods

        private void OpenDbContext()
        {
            if (_ctx == null)
            {
                _ctx = new CTSContext();
            }
        }

        #endregion

        #region Dispose
        public void Dispose()
        {
            if (_ctx != null)
            {
                _ctx.Dispose();
            }
        }
        #endregion

        #region Conversion
        public static implicit operator FollowUpPanelListViewModel(FollowUpPanel item)
        {
            FollowUpPanelListViewModel retValue = new FollowUpPanelListViewModel();
            var language = Helper.GetLanguage();
            if (item != null)
            {
                var sendingEntity = string.Empty;
                if (item.FollowUp.OriginalDocument.SendingEntity != null)
                {
                    var sendingEntityItem = item.FollowUp?.OriginalDocument?.SendingEntity;
                    sendingEntity = sendingEntityItem.Name;
                    if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(sendingEntityItem.NameAr))
                    {
                        sendingEntity = sendingEntityItem.NameAr;
                    }
                    else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(sendingEntityItem.NameFr))
                    {
                        sendingEntity = sendingEntityItem.NameFr;
                    }
                }

                var priority = string.Empty;
                if (item.FollowUp?.FollowUpDocument?.Priority != null)
                {
                    priority = item.FollowUp?.FollowUpDocument?.Priority.Name;
                    if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(item.FollowUp?.FollowUpDocument?.Priority.NameAr))
                    {
                        priority = item.FollowUp?.FollowUpDocument?.Priority.NameAr;
                    }
                    else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(item.FollowUp?.FollowUpDocument?.Priority.NameFr))
                    {
                        priority = item.FollowUp?.FollowUpDocument?.Priority.NameFr;
                    }
                }

                retValue.Id = item.Id;
                retValue.FollowUpId = item.FollowUpId;
                retValue.FollowUpPanelStatus = item.FollowUpPanelStatus ?? "";
                retValue.FollowUp = item.FollowUp != null ? item.FollowUp : null;
                retValue.SendingEntity = sendingEntity ?? "";
                retValue.Subject = item.FollowUp?.FollowUpDocument?.Subject ?? "";
                retValue.ReferenceNumber = item.FollowUp?.OriginalDocument?.ReferenceNumber ?? "";
                retValue.DocumentCreatedDate = item.FollowUp?.OriginalDocument?.CreatedDate.ToString(Constants.DATE_FORMAT) ?? "";
                retValue.Priority = priority ?? "";
                retValue.Event = item.Event ?? "";
                retValue.EventDate = item.EventDate.HasValue ? item.EventDate.Value.Date.ToString(Constants.DATE_FORMAT) : string.Empty;
                retValue.TransferredTo = item.TransferredTo ?? "";
                retValue.TransferredDate = item.TransferredDate.HasValue ? item.TransferredDate.Value.Date.ToString(Constants.DATE_FORMAT) : string.Empty;
                retValue.ResponsibleUser = item.ResponsibleUser ?? "";
                retValue.Notes = item.Notes ?? "";
                retValue.ModifiedByUSer = item.ModifiedByUSer == null ? String.Empty :
                   (language == Intalio.Core.Language.EN ?
                   $"{item.ModifiedByUSer?.Firstname} {item.ModifiedByUSer?.Lastname}" :
                   $"{IdentityHelperExtension.GetFullName((long)item.ModifiedByUSerId, language)}");
                retValue.LastModifiedDate = item.ModifiedDate.HasValue ? item.ModifiedDate.Value.ToString() : string.Empty;
                retValue.FollowUpCreatedByUser = item.FollowUp?.FollowUpDocument?.CreatedByUser == null ? String.Empty :
                   (language == Intalio.Core.Language.EN ?
                   $"{item.FollowUp?.FollowUpDocument?.CreatedByUser?.Firstname} {item.FollowUp?.FollowUpDocument?.CreatedByUser?.Lastname}" :
                   $"{IdentityHelperExtension.GetFullName((long)item.FollowUp?.FollowUpDocument?.CreatedByUserId, language)}");
            }
            return retValue;

        }
        #endregion
    }
}