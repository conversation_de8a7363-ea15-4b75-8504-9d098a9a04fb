﻿import Intalio from './common.js'
import DocumentDetails from './documentDetails.js'
import { Categories, Nodes, DraftStatuses } from './lookup.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import VipDocumentDraft from './vipDraftList.js'
import CustomizeNodeColomns from '../components/customizeNodeColomns.js'

class DocumentDraft extends Intalio.Model
{
    constructor()
    {
        super();
        this.title = null;
        this.nodeId = null;
        this.categories = null;
    }
}
var gfirstTime = true;
var gDataChanged = true;
var gAction = "";
var gFilterChanged = false;
var gFilterStructure = "";
var gFilterFromDate = "";
var gFilterToDate = "";
var gFilterUser = "";
var gFilterPrivacy = "";
var gFilterPriority = "";
var gFilterOverdue = "";
var gReportName = "Draft Report";
function format(row, nodeId)
{
    return '<table style="width:100%" cellspacing="0" border="0">' + buildColumnsDetails(row, nodeId) + '</table>';
}
function openDocument(id, nodeId, delegationId)
{
    Common.ajaxGet('/Document/Get', { id: id, delegationId: delegationId }, function (data)
    {
        gLocked = false;
        //Common.setActiveSidebarMenu("liDraft");
        $(".delegation").removeClass("active");
        //$("#gridContainerDiv").hide();

        var wrapper = $(".modal-documents");
        var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
        linkedCorrespondenceModel.reference = data.referenceNumber;
        linkedCorrespondenceModel.subject = data.subject;
        linkedCorrespondenceModel.documentId = id;
        //linkedCorrespondenceModel.subject = $("#" + model.ComponentId + "_subjectTaskPanel").html();
        //linkedCorrespondenceModel.from = $("#" + model.ComponentId + "_fromTaskPanel").html();
        //linkedCorrespondenceModel.to = $("#" + model.ComponentId + "_toTaskPanel").html();
        //linkedCorrespondenceModel.transferDate = $("#" + model.ComponentId + "_transferDateTaskPanel").html();
        //linkedCorrespondenceModel.registerDate = $("#" + model.ComponentId + "_registerDateTaskPanel").html();
        //linkedCorrespondenceModel.registeredBy = $("#" + model.ComponentId + "_registerByTaskPanel").html();
        var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
        linkedCorrespondenceDocument.render();

        var model = new DocumentDetails.DocumentDetails();
        model.documentId = id;
        model.statusId = data.status;
        model.categoryId = data.categoryId;
        model.categoryName = data.categoryName;
        model.documentModel = data;
        model.senderPerson = data.senderPerson;
        model.receiverPerson = data.receiverPerson;
        model.isExternalReceiver = data.isExternalReceiver;
        model.isExternalSender = data.isExternalSender;
        model.referenceNumber = data.referenceNumber;
        model.showMyTransfer = false;
        model.readonly = false;
        model.hasAttachments = data.attachmentCount > 0;
        model.templateHasSignature = data.TemplateHasSignature;
        model.attachmentVersion = data.attachmentVersion;
        model.isSigned = data.IsSigned;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language, delegationId));
        var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[data.categoryId].Tabs;
        model.tabs = $.grep(tabs, function (element, index)
        {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
        });
        model.tabsWithStatic = tabs;
        model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[data.categoryId].SecurityTabs;
        model.showBackButton = true;
        model.attachmentId = data.attachmentId;

        model.fromDraft = true;
        model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
        model.isModal = true;
        model.showBackButton = false;
        model.delegationId = delegationId;
        wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

        var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
        view.render();

        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
            $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');

        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
            if ($(this).data("remove") != true)
                return;
            $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
            $('#modalSignatureTemplates').remove();
            $('#pdfModal').remove();
            swal.close();
            if ($(".modal-documents").children().length > 0 || $(".modal-window").children().length > 0) {
                $('body').addClass('modal-open');
            }

        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

        if (!id)
        {
            TreeNode.addToNodeCount(TreeNode.Draft, 1, 1);
        }
        $(document).off('click', '.btn-back');
        $(document).on('click', '.btn-back', function ()
        {
            $("#gridContainerDiv").show();
            documentView.remove();
            $(".toRemove").remove();
        });
        $(document).off('click', '.btn-export');
    }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
}
function buildFilters(nodeId)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var filters = node.filters !== "" && node.filters !== null ? JSON.parse(node.filters) : [];

    if (filters.length > 0)
    {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++)
        {
            var filter = filters[i];
            switch (filter)
            {
                case "FromDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label><div class="input-group date">' +
                        '<input id="filterDraftFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterDraftFromDateError">' +
                        '<span class="input-group-addon" id="filterDraftFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterDraftFromDateError"></div></div></div></div>'
                    break;
                case "ToDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label><div class="input-group date">' +
                        '<input id="filterDraftToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterDraftToDateError">' +
                        '<span class="input-group-addon" id="filterDraftToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterDraftToDateError"></div></div></div></div>';
                    break;
                case "Subject":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterDraftSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Category":
                    clearFix += 3;
                    var categories = new Categories().get(window.language);
                    var followUpCategoryIndex = categories.findIndex(item => item.id == window.FollowUpCategory);
                    categories.splice(followUpCategoryIndex, 1);
                    var div = '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="categoryFilterDraftContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterDraftCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterDraftCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++)
                    {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterDraftCategoryError"></div></div></div>';
                    html += div;
                    break;
            }
            if (clearFix === 12 || clearFix === 11)
            {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 list-action-filter"><button id="btnFilterDraftSearch" tabindex="5" type="button" class="btn btn-primary">' + Resources.Search + '</button>' +
            ' <button id="btnFilterDraftClear" tabindex="6" type="button" class="btn btn-clear">' + Resources.Clear + '</button></div>';
        html += '</div>';
        $('#filtersContainer').html(html);

        //var clickedSearch = false;
        //$('#collapseDraftIcon').click(function ()
        //{
        //    $('#collapseDraftIcon').empty();
        //    if (clickedSearch)
        //    {
        //        $('#collapseDraftIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
        //        $('#collapseDraftPanel').attr('class', '');
        //        $('#collapseDraftPanel').addClass('panel-body panel-collapse collapse in');
        //        clickedSearch = false;
        //    } else
        //    {
        //        $('#collapseDraftIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
        //        $('#collapseDraftPanel').attr('class', '');
        //        $('#collapseDraftPanel').addClass('panel-body panel-collapse collapse');
        //        clickedSearch = true;
        //    }
        //});
        $("#btnFilterDraftSearch").on('click', function ()
        {
            GridCommon.Refresh(gTableName);
        });
        $("#btnFilterDraftClear").on('click', function ()
        {
            $("#cmbFilterDraftCategory").val('').trigger('change');
            fromDate.clear();
            toDate.clear();
            $("#txtFilterDraftSubject").val('');
            GridCommon.Refresh(gTableName);
        });
        $('#cmbFilterDraftCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterDraftContainer')
        });
        $("#cmbFilterDraftCategory").val('').trigger('change');
        var fromDate = $('#filterDraftFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    maxDate: jQuery('#filterDraftToDate').val() && jQuery('#filterDraftToDate').val() !== "" ? jQuery('#filterDraftToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterDraftFromDate_img").click(function ()
        {
            fromDate.toggle();
        });
        var toDate = $('#filterDraftToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    minDate: jQuery('#filterDraftFromDate').val() && jQuery('#filterDraftFromDate').val() !== "" ? jQuery('#filterDraftFromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterDraftToDate_img").click(function ()
        {
            toDate.toggle();
        });
        $('#filterDraftFromDate').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterDraftSearch').focus();
                }
                else
                {
                    $('#filterDraftToDate').focus();
                }
            }
        });
        $('#btnFilterDraftClear').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterDraftSearch').focus();
                }
                else
                {
                    $('#filterDraftFromDate').focus();
                }
            }
        });
    } else
    {
        $('.searchToRemove > div:first-child').hide();
        $("#gridPanel").removeAttr("style");
        $("#gridPanel").addClass("panel-default");
    }
}
function buildColumns(gridcolumns, nodeId)
{
    gridcolumns.push({
        visible: false,
        title: Resources.Notes,
        data: "notes",
        render: function (data, type, full, meta) {
            if (Array.isArray(full.note) && full.note.length > 0) {
                // Add a bullet point before each note and join with new lines
                return full.note.map(n => `\u2022 ${n.notes} \n`).join('\n');
            }

            return "";
        }
    });




    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var newColomns = new Nodes().getCustomcolumns(nodeId);


    if (newColomns) {
        node.columns = newColomns.content;
    }
    var columns = node.columns !== "" && node.columns !== null ? JSON.parse(node.columns) : [];
    var columnDetails = $.grep(columns, function (element, index)
    {
        return element.isColumnDetail === true;
    });
    if (columnDetails.length > 0)
    {
        gridcolumns.push({
            "className": 'details-control',
            "orderable": false,
            "data": null,
            "defaultContent": '',
            width: '16px'
        });
    }
    gridcolumns.push(
        {
            "className": "text-left",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            data: "importanceId",
            'render': function (data, type, full, meta)
            {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var i = 0; i < importances.length; i++)
                {
                    if (importances[i].id === data)
                    {
                        let btnView = document.createElement("i");
                        btnView.setAttribute("class", "fa fa-exclamation fa-lg");
                        btnView.setAttribute("style", "color:" + importances[i].color);
                        btnView.setAttribute("title", importances[i].text);
                        return btnView.outerHTML;
                    }
                }
                return "";
            }
        });
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var customColumn = [];
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (!column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                customColumn.push({ columnName: column.name, functionName: column.customFunctionName });
                var customColumnKeyword = column.customFunctionName;
                var escapedVariable = column.customFunctionName.replace(/"/g, '&quot;');

                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                gridcolumns.push({
                    title: GetCustomAttributeTranslationByLangauge(customColumnKeyword), "orderable": false, 'defaultContent': '<div data-function="' + escapedVariable + '"></div>',
                    "createdCell": function (td, cellData, rowData, row, col)
                    {
                        var htmlCell = "";
                        var div = td.children[0];
                        var customFunctionName = div !== null && typeof div !== "undefined" ? $(div).attr("data-function") : "";
                        //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                        //{
                        //    var functionName = customFunctionName.split("(")[0];
                        //    htmlCell = eval(functionName + "(" + JSON.stringify(rowData) + ")");
                        //}

                        var customColumns = JSON.parse(rowData.documentForm.form);
                        var columnName = customColumn.find(f => f.functionName == customFunctionName).columnName;

                        var customData = customColumns[columnName]

                        if (customFunctionName.includes("()")) {
                            if (customFunctionName == "getCommitteeName()") {
                                customData = getCommitteeName(customData);
                            }

                            if (customFunctionName == "getMeetingLocation()") {
                                customData = getMeetingLocation(customData);
                            }
                        }

                        htmlCell = htmlCell == "" && customColumns != null ? (customData == undefined ? "" : customData) : htmlCell;

                        $(td).html(htmlCell);
                    }
                });
            } else
            {
                switch (column.name)
                {
                    case "Note":
                        gridcolumns.push(
                            {
                                title: Resources.Notes,
                                data: "notes",
                                render: function (data, type, full, meta) {
                                    if (Array.isArray(full.note) && full.note.length > 0) {
                                        return full.note.map(n => n.notes).join(',');
                                    }
                                    return "";
                                }
                            }
                        )
                        break;
                    case "Category":
                        gridcolumns.push({
                            title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"],
                            render: function (data, type, full, meta)
                            {
                                var categories = new Categories().get(window.language);
                                for (var i = 0; i < categories.length; i++)
                                {
                                    if (categories[i].id === data)
                                    {
                                        return categories[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "ModifiedDate":
                        gridcolumns.push({
                            title: Resources.ModifiedDate, data: "modifiedDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.modifiedDate, null, window.CalendarType);
                            }
                        });
                        break;
                    case "ReferenceNumber":
                        gridcolumns.push({ title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": false, "className": "min-max-width-50-150" });
                        break;
                    case "Subject":
                        gridcolumns.push({ title: Resources.Subject, data: "subject", render: function(data, type, full, meta)
                            {
                                return `<span class="subjectSpan">${full.subject ?? ""}</span>`;
                            }, "orderable": true, orderSequence: ["asc", "desc"], "className": "min-max-width-50-150" });
                        break;
                    case "SendingEntity":
                        gridcolumns.push({ title: Resources.SendingEntity, data: "sendingEntity", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
                        break;
                    case "ReceivingEntity":
                        gridcolumns.push({ title: Resources.ReceivingEntity, data: "receivingEntity", render: $.fn.dataTable.render.text(), "orderable": false });
                        break;
                    case "User":
                        gridcolumns.push({ title: Resources.CreatedBy, data: "createdByUser", render: $.fn.dataTable.render.text(), "orderable": false });
                        break;
                    case "DraftStatus":
                        gridcolumns.push({
                            title: Resources.Status, data: "draftStatus", "orderable": true, orderSequence: ["asc", "desc"],
                            render: function (data, type, full, meta)
                            {
                                var draftstatuses = new DraftStatuses().get();
                                for (var i = 0; i < draftstatuses.length; i++)
                                {
                                    if (draftstatuses[i].id === data)
                                    {
                                        return draftstatuses[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "CreatedDate":
                        gridcolumns.push({
                            title: Resources.CreatedDate, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
                            }
                        });
                        break;
              
                 
                }
            }
        }
    }

}
function buildColumnsDetails(row, nodeId)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var columns = node.columns !== "" && node.columns !== null ? JSON.parse(node.columns) : [];
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var html = "";
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                var customColumn = column.name;
                var customColumnKeyword = column.customFunctionName;

                var htmlCell = "";
                var customFunctionName = column.customFunctionName;
                //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                //{
                //    var functionName = customFunctionName.split("(")[0];
                //    htmlCell = eval(functionName + "(" + JSON.stringify(row.data()) + ")");
                //}

                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                var customColumns = JSON.parse(row.data().documentForm.form);

                customColumns = customColumns[customColumn];

                if (customFunctionName.includes("()")) {
                    if (customFunctionName == "getCommitteeName()") {
                        customColumns = getCommitteeName(customColumns);
                    }

                    if (customFunctionName == "getMeetingLocation()") {
                        customColumns = getMeetingLocation(customColumns);
                    }
                }

                htmlCell = htmlCell == "" && customColumns != null ? (customColumns == undefined ? "" : customColumns) : htmlCell;

                html += '<tr><th style="width: 10%;padding:5px">' + (GetCustomAttributeTranslationByLangauge(customColumnKeyword)) + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + htmlCell + '</td></tr>';

            } else
            {
                switch (column.name) {
                 
                    case "Category":
                        var category = "";
                        var categories = new Categories().get(window.language);
                        if (categories && Array.isArray(categories)) {
                            var matchedCategory = categories.find(c => c.id === row.data().categoryId);
                            category = matchedCategory ? matchedCategory.text : "";
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Category + ':</th>' +
                            '<td style="width: 85%;padding:5px;word-break: break-all;">' + category + '</td></tr>';
                        break;

                    case "ReferenceNumber":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReferenceNumber + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().referenceNumber || '') + '</td></tr>';
                        break;
                    case "Subject":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Subject + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().subject || '') + '</td></tr>';
                        break;
                    case "From":
                        var from = "";
                        if (row.data().fromStructure) {
                            from += row.data().fromStructure;
                        }
                        if (row.data().fromUser) {
                            var user = row.data().fromUser;
                            from += from !== "" ? "/" + user : user;
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.From + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + from + '</td></tr>';
                        break;

                    case "CreatedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.CreatedDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().createdDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "SendingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.SendingEntity + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().sendingEntity || '') + '</td></tr>';
                        break;
                    case "ReceivingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReceivingEntity + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().receivingEntity || '') + '</td></tr>';
                        break;
                    case "Purpose":
                        var purpose = "";
                        var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                        for (let i = 0; i < purposes.length; i++) {
                            if (purposes[i].id === row.data().purposeId) {
                                purpose = purposes[i].text;
                            }
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Purpose + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + purpose + '</td></tr>';
                        break;
              

                    case "OpenedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.OpenedDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().openedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "DueDate":
                        html +=
                            '<tr><th style="width: 10%;padding:5px">' + Resources.DueDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().dueDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "ModifiedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ModifiedDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().modifiedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';

                        //gridcolumns.push({
                        //    title: Resources.ModifiedDate, data: "modifiedDate", "orderable": false, width: "100px",
                        //    render: function (data, type, full, meta) {
                        //        return DateConverter.toHijriFormated(full.modifiedDate, null, window.CalendarType);
                        //    }
                        //});
                        break;
                }

            }
        }
    }
    return html;
}



var gTableName = "grdDraftItems";
var gLocked = false;

var gfirstTime = true;
var gDataChanged = true;
var gAction = "";
var gFilterChanged = false;
var gFilterStructure = "";
var gFilterFromDate = "";
var gFilterToDate = "";
var gFilterUser = "";
var gFilterOverdue = "";
var gOverdue = false;
var clear = false;
var gReportName = "Draft Reports ";

var wrapperParent;
class DocumentDraftView extends Intalio.View
{
    constructor(element, model)
    {
        wrapperParent = model;
        super(element, "draft", model);
    }
    render()
    {

        var self = this;
        var model = this.model;
        var self = this;
        gfirstTime = true;
        gDataChanged = true;
        gAction = "";
        gFilterChanged = false;
        gFilterStructure = "";
        gFilterFromDate = "";
        gFilterToDate = "";
        gFilterUser = "";
        gFilterOverdue = "";
        gOverdue = false;
        clear = false;
        gReportName = "Draft Report";
        var clickedSearch = false;
        $.fn.select2.defaults.set("theme", "bootstrap");
        buildFilters(self.model.nodeId);
        Common.gridCommon();
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        //var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName);
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName).map(button => {
            button.className = (button.className || '') + ' hidden conditional-buttons';
            return button;
        });

        var exportButton = [{
            className: 'btn-sm btn-primary',

            extend: 'print',
            text: Resources.Print,
            title: function () {
                return Resources.DraftReport;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.DraftReport + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [':visible',2,3]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.draftReport.print
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'excelHtml5',
            title: function () {
                return Resources.DraftReport;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.DraftReport + '_' + formattedDate + '_' + formattedTime;
            },


            exportOptions: {
                columns: [ ':visible',2,3]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.draftReport.excelHTML5
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'pdfHtml5',
            title: function () {
                return Resources.DraftReport;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.DraftReport + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [ ':visible',2,3]
            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.draftReport.pdfHTML5
        }, {
            className: 'btn-sm btn-primary',
            text: Resources.CustomizeColumns,
            action: function (e, dt, node, config) {

                var wrapper = $(".modal-window");
                var customeModel = new CustomizeNodeColomns.CustomizeNodeColomns();
                customeModel.nodeId = model.nodeId;
                customeModel.text = "Draft";
                var CustomizeNodeColomnsViews = new CustomizeNodeColomns.CustomizeNodeColomnsView(wrapper, customeModel);
                CustomizeNodeColomnsViews.render();

                $("#nodeColomnsModal").parsley().reset();

                $('#nodeColomnsModal').modal('show');

                $("#nodeColomnsModal").off("hidden.bs.modal");
                $("#nodeColomnsModal").off("shown.bs.modal");

                $('#nodeColomnsModal').on('shown.bs.modal', function () {
                });

                $('#nodeColomnsModal').on('hidden.bs.modal', function () {
                    $('#formPostNode').parsley().reset();
                    $('#nodeColomnsModal').remove();
                });
            }
        }
        ];

        buttons.push({
            className: 'btn-sm btn-danger hidden',
            text: Resources.Delete,
            action: function (e, dt, node, config)
            {
                var ids = GridCommon.GetSelectedRows(gTableName);
                if (ids.length > 0)
                {
                    Common.showConfirmMsg(Resources.DeleteConfirmationCorrespondence, function ()
                    {
                        Common.mask(document.getElementById('grdDraftItems'), "draftListContainer-mask");
                        Common.ajaxDelete('/Document/Delete',
                            {
                                'ids': ids, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                            },
                            function (result)
                            {
                                if (result != null)
                                {
                                    swal.close()
                                    let msg = "";
                                    for (var i = 0; i < result.length; i++)
                                    {
                                        if (!result[i].updated)
                                        {
                                            msg += "\n ○ " + result[i];
                                        }
                                    }
                                    Common.unmask("draftListContainer-mask");
                                    if (msg !== "")
                                    {
                                        setTimeout(function ()
                                        {
                                            Common.alertMsg(Resources.CannotDeleteRegisteredDocumentsWarning + msg);
                                        }, 500);
                                    }
                                    GridCommon.Refresh(gTableName);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                                } else
                                {
                                    Common.unmask("draftListContainer-mask");
                                    Common.showScreenErrorMsg();
                                }
                                $(".html5buttons .btn-danger").addClass("hidden");
                            }, null, false);
                    });
                }
                else
                {
                    Common.alertMsg(Resources.NoRowSelected);
                }
            }
        });

        var allButtons = [exportButton, ...buttons];

        var columns = [{ title: '<input id="chkAll" type="checkbox" />', width: '16px', "orderable": false, "render": function (data, type, row) { return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />"; } },
        { title: "Id", data: "id", visible: false, "orderable": false }];
        buildColumns(columns, self.model.nodeId);
        columns.push({
            "className": "text-center",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = '';
                var categories = new Categories().get(window.language);
                var matchedCategory, categoryName;
                matchedCategory = categories.find(c => c.id === full.categoryId);

                categoryName = matchedCategory ? matchedCategory.text : "";
                if (matchedCategory.text == "Incoming") {
                 

                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                        "<i class=' fa fa-arrow-down '></i>&nbsp;" +
                        "</div>";
                }
                else if (matchedCategory.text == "Outgoing") {
                   

                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                        "<i class=' fa fa-arrow-up '></i>&nbsp;" +
                        "</div>";
                } else if (matchedCategory.text == "Internal") {
               

                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                        "<i class=' fa fa-arrows-h '></i>&nbsp;" +
                        "</div>";
                }



                return html;
            }
        });

        columns.push({
            "className": "text-center",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta)
            {
                


                let btn = document.createElement("button");
                btn.setAttribute("class", "btn btn-xs btn-primary edit");
                btn.setAttribute("title", Resources.Edit);
                btn.setAttribute("clickattr", "openDocument(" + full.id + ", " + self.model.nodeId + ", " + self.model.delegationId + ")");
                btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                return btn.outerHTML;
            }
        });

        SecurityMatrix.getRowActions(securityMatrix, columns, self.model.nodeId);
        var table = $("#" + gTableName)
            .on('draw.dt', function ()
            {
                $('#' + gTableName + " td input[type='checkbox']").on('click', function () {
                    if ($(this).is(":checked")) {
                        $(".html5buttons .btn-danger").removeClass("hidden");
                        $(".conditional-buttons").removeClass("hidden");
                    }
                    else if (GridCommon.GetSelectedRows(gTableName).length == 1) {
                        $(".html5buttons .btn-danger").addClass("hidden");
                        $(".conditional-buttons").addClass("hidden");
                    }
                });

                $('#' + gTableName + ' tbody tr td').each(function ()
                {
                    this.setAttribute('title', $(this).text());
                });
                GridCommon.CheckSelectedRows(gTableName);
            })
            .DataTable({
                "createdRow": function (row, data, dataIndex)
                {
                    var color = "";
                    var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                    for (var i = 0; i < priorities.length; i++)
                    {
                        if (priorities[i].id === data.priorityId)
                        {
                            color = priorities[i].color;
                        }
                    }
                    if (color !== "")
                    {
                        $(row).attr('style', "color:" + color);
                    }
                },
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/Document/ListDraft",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d)
                    {
                        d.NodeId = self.model.nodeId;
                        d.CategoryId = $("#cmbFilterDraftCategory").val() !== null && typeof $("#cmbFilterDraftCategory").val() !== "undefined" ? $("#cmbFilterDraftCategory").val() : "0";
                        d.FromDate = $("#filterDraftFromDate").val() !== "" && typeof $("#filterDraftFromDate").val() !== "undefined" ? $("#filterDraftFromDate").val() : "";
                        d.ToDate = $("#filterDraftToDate").val() !== "" && typeof $("#filterDraftToDate").val() !== "undefined" ? $("#filterDraftToDate").val() : "";
                        d.Subject = $("#txtFilterDraftSubject").val() !== "" && typeof $("#txtFilterDraftSubject").val() !== "undefined" ? $("#txtFilterDraftSubject").val() : "";
                        d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val()
                        d.DelegationId = model.delegationId;
                        return d;
                    }
                },
                "order": [],
                "columns": columns,
                "fnInitComplete": function (settings, json)
                {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons "B>ltrpi',
                buttons: allButtons
            });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        GridCommon.AddCheckBoxEvents(gTableName);
        if (window.AllowRowSelection=="True") {
            $('#' + gTableName + ' tbody').on('click', 'tr', function () {
            
                let checkbox = $(this).find('input[type="checkbox"]');
                if (checkbox.is(":checked")) {
                    checkbox.prop("checked", true);
                } else {
                    checkbox.prop("checked", false);
                }

                UpdateButtonVisibility(gTableName);
            
            });
        }

        function UpdateButtonVisibility(tableId) {
            var selectedRows = GridCommon.GetSelectedRows(tableId).length;
            if (selectedRows > 0) {
                $(".html5buttons .btn-danger").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");
            }
            else if (selectedRows == 0) {
                $(".html5buttons .btn-danger").addClass("hidden");
                $(".conditional-buttons").addClass("hidden");
            }
        }
        $('#' + gTableName + ' tbody').on('dblclick', 'tr', function ()
        {
            if (!gLocked)
            {
                gLocked = true;
                try
                {
                    var onclick = $(this).find(".edit").attr("clickattr");
                    eval(onclick);
                } catch (e)
                {
                    gLocked = false;
                }
            }
        });
        $('#' + gTableName + ' tbody').on('click', '.subjectSpan', function () {
            if (!gLocked) {
                gLocked = true;
                try {
                    var onclick = $(this).closest('tr').find(".edit").attr("clickattr");
                    eval(onclick);
                } catch (e) {
                    gLocked = false;
                }
            }
        });
        $('#grdDraftItems tbody').on('click', ".edit", function ()
        {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#' + gTableName + ' tbody').on('click', 'td.details-control', function ()
        {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown())
            {
                row.child.hide();
                tr.removeClass('shown');
            }
            else
            {
                row.child(format(row, self.model.nodeId)).show();
                tr.addClass('shown');
            }
        });
        SecurityMatrix.InitToolbarColor();
        SecurityMatrix.InitContextMenu(securityMatrix, self.model.nodeId);
        $('#filtersContainer input').off('keydown', function (e) { });
        $('#filtersContainer input').on('keydown', function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                $("#btnFilterDraftSearch").trigger('click');
            }
        });
        $('#' + gTableName).on('click', '#chkAll', function () {
            let isChecked = $(this).is(":checked");

            if (isChecked) {
                $(".html5buttons .btn-danger").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".html5buttons .btn-danger").addClass("hidden");
                $(".conditional-buttons").addClass("hidden");
            }

            let table = $('#' + gTableName).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', isChecked);
        });

        $('#' + gTableName).on('change', 'input[type="checkbox"]', function () {
            let table = $('#' + gTableName).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();

            let totalCheckboxes = $('input[type="checkbox"]', pageNodes).length;
            let checkedCheckboxes = $('input[type="checkbox"]:checked', pageNodes).length;

            $('#chkAll').prop('checked', totalCheckboxes === checkedCheckboxes);

            if (checkedCheckboxes > 0) {
                $(".html5buttons .btn-danger").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".html5buttons .btn-danger").addClass("hidden");
                $(".conditional-buttons").addClass("hidden");
            }
        });

        table.on('draw', function () {
            $('#chkAll').prop('checked', false);

            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', false);

            $(".html5buttons .btn-danger").addClass("hidden");
            $(".conditional-buttons").addClass("hidden");
        });


        $('.toggleVIP').on('click', function () {
            if (window.InboxMode === "InboxDefault") {
                window.InboxMode = "LocalVIPView";
            } else if (window.InboxMode === "LocalInboxDefaultView") {
                window.InboxMode = "InboxVIPView";
            }
            let wrapper = $(".content-wrapper");
            let VIPmodel = new VipDocumentDraft.VipDocumentDraft();
            VIPmodel.nodeId = wrapperParent.nodeId;
            VIPmodel.delegationId = wrapperParent.delegationId;
            VIPmodel.categories = new Categories().get(window.language, wrapperParent.delegationId);
            VIPmodel.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            VIPmodel.title = $('.content-heading').text();
            let documentView = new VipDocumentDraft.VipDocumentDraftView(wrapper, VIPmodel);
            documentView.render();

        })
    }
}
export default { DocumentDraft, DocumentDraftView };
