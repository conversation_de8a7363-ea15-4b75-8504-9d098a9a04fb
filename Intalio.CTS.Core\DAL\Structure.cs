﻿using Intalio.Core;
using Intalio.Core.Interfaces;
using Intalio.CTS.Core.Model;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.DAL
{
    public partial class Structure : IDbObject<Structure>, IDisposable
    {
        #region Ctor

        public Structure()
        {
            DocumentCarbonCopy = new HashSet<DocumentCarbonCopy>();
            DocumentCreatedByStructure = new HashSet<Document>();
            DocumentReceiverEntity = new HashSet<DocumentReceiverEntity>();
            DocumentSendingEntity = new HashSet<Document>();
            TransferFromStructure = new HashSet<Transfer>();
            TransferToStructure = new HashSet<Transfer>();
            EntityStructure = new HashSet<StructureUserGroup>();
        }

        #endregion

        #region Private Fields

        private CTSContext _ctx;

        #endregion

        #region Properties

        public long Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public long? ParentId { get; set; }
        public long? DepartmentId { get; set; }

        public string NameAr { get; set; }
        public string NameFr { get; set; }
        public bool IsPrivate { get; set; }
        public bool IsExternal { get; set; }
        public bool IsDepartment { get; set; }
        public bool? HideStructureFromTransfer { get; set; }

        public virtual ICollection<TeamUsers> TeamUsers { get; set; }
        public virtual Structure Parent { get; set; }

        public virtual ICollection<DocumentCarbonCopy> DocumentCarbonCopy { get; set; }
        public virtual ICollection<Document> DocumentCreatedByStructure { get; set; }
        public virtual ICollection<DocumentReceiverEntity> DocumentReceiverEntity { get; set; }
        public virtual ICollection<Document> DocumentSendingEntity { get; set; }
        public virtual ICollection<Transfer> TransferFromStructure { get; set; }
        public virtual ICollection<Transfer> TransferToStructure { get; set; }
        public virtual ICollection<StructureUserGroup> EntityStructure { get; set; }
        public virtual ICollection<UserStructure> UserStructure { get; set; }
        public virtual Instruction Instruction {  get; set; }
        public virtual Instruction ManagerInstruction { get; set; }
        #endregion

        #region Private Methods

        private void OpenDbContext()
        {
            if (_ctx == null)
            {
                _ctx = new CTSContext();
            }
        }

        #endregion

        #region Public Methods

        public void Delete(long id)
        {
            using (var ctx = new CTSContext())
            {
                Structure item = new Structure { Id = (short)id };
                ctx.Structure.Attach(item);
                ctx.Structure.Remove(item);
                ctx.SaveChanges();
            }
        }

        public void Delete()
        {
            Delete(Id);
        }

        public Structure Find(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Structure.AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        public async Task<Structure> FindAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Structure.AsNoTracking().FirstOrDefaultAsync(t => t.Id == id);
            }
        }

        public List<long> FindNotExistent(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                return ids.Where(t => !ctx.Structure.AsNoTracking().Any(y => y.Id == t)).ToList();
            }
        }

        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                ctx.Structure.Add(this);
                ctx.SaveChanges();
            }
        }

        public void Update()
        {
            using (var ctx = new CTSContext())
            {
                ctx.Entry(this).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                ctx.SaveChanges();
            }
        }
        public List<Structure> ListPrivateStructures()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Structure.Where(s=>s.IsPrivate).AsNoTracking().ToList();
            }
        }
        public List<Structure> ListByIds(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Structure.AsNoTracking().Where(t => ids.Contains(t.Id)).ToList();
            }
        }

        public List<Structure> ListAll()
        {
            using (var ctx = new CTSContext())
            {

                var structures = ctx.Structure
                          .Include(U => U.UserStructure)
                          .ThenInclude(u => u.User)
                          .ToList();
                return structures;
            }
        }

        public List<List<Structure>> GetAllUserStructures()
        {
            using (var ctx = new CTSContext())
            {

                var xx = ctx.Structure
                          .Include(U => U.UserStructure)
                          .ThenInclude(u => u.User)
                          .Where(s => s.IsExternal != true)
                          .AsNoTracking()
                          .GroupBy(U => U.Id)
                          .Select(g => g.ToList())
                          .ToList();
                return xx;
            }
        }

        public List<SimpleTreeNode> GetAllUserStructuresForTree()
        {
            using (var dbContext = new CTSContext())
            {
                // Part 1: Select '0' as id, null as parent, '/' as text
                List<SimpleTreeNode> result = new List<SimpleTreeNode>()
                {
                    new SimpleTreeNode
                    {
                        Id = "0",
                        Parent = "#",
                        ParentId = "",
                        Text = "/",
                        Icon = "fa fa-building-o text-et-color",
                        State = new TreeNodeState() { Disabled = false, Opened = true, Selected = false },
                        NodeId = "0",
                    }
                };

                // Part 2: Select from Structure
                List<SimpleTreeNode> structures = dbContext.Structure.Where(s => s.IsExternal == false)
                    .Select(s => new SimpleTreeNode
                    {
                        Id = "Structure_" + s.Id.ToString(),
                        Parent = s.ParentId != null ? "Structure_" + s.ParentId.ToString() : "0",
                        ParentId = s.ParentId != null ? "Structure" + s.ParentId.ToString() : "0",
                        Text = s.Name,
                        Icon = "fa fa-building-o text-et-color",
                        data = new KeyValuePair<string,string>("Structure" + s.ParentId.ToString(), s.Id.ToString()),
                        NodeId = "Structure" + s.Id.ToString(),
                    }).ToList();

                // Part 3: Select from User and UserStructure
                List<SimpleTreeNode> users = dbContext.userStructure
                    .Select(us => new SimpleTreeNode
                    {
                        Id = "User_" + us.StructureId.ToString() + "_" + us.User.Id.ToString(),
                        Parent = "Structure_" + us.StructureId.ToString(),
                        ParentId = "Structure" + us.StructureId.ToString(),
                        Text = us.User.Firstname + " " + us.User.Lastname,
                        Icon = "icon-user text-et-color",
                        data = new KeyValuePair<string, string>("User" + us.StructureId.ToString(), us.UserId.ToString()),
                        NodeId = "User" + us.UserId.ToString(),
                    }).ToList();

                // Combine all parts using Union
                result.AddRange(structures);
                result.AddRange(users);
                return result;
            }
        }

        public List<Structure> GetIncludeUserStructure(Expression<Func<Structure, bool>> expression)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Structure
                    .Include(u => u.UserStructure).ThenInclude(us => us.User)
                    .Include(u => u.Parent)
                    .Include(u => u.UserStructure).ThenInclude(u => u.Privacy).Where(expression).Where(x=>x.HideStructureFromTransfer!=true).AsNoTracking().ToList();
            }
        }

        public List<Structure> GetWithOutUserStructure(Expression<Func<Structure, bool>> expression)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Structure.Where(expression).AsNoTracking().ToList();
            }
        }
        #endregion

        #region Dispose

        public void Dispose()
        {
            if (_ctx != null)
            {
                _ctx.Dispose();
            }
        }

        #endregion

        #region Conversion

        public static implicit operator StructureListViewModel(Structure item)
        {
            StructureListViewModel retValue = null;
            if (item != null)
            {
                retValue = new StructureListViewModel
                {
                    Id = item.Id,
                    Name = item.Name,
                    Code = !string.IsNullOrEmpty(item.Code) ? item.Code : "",
                };
            }
            return retValue;
        }

        public static implicit operator StructureViewModel(Structure item)
        {
            StructureViewModel retValue = null;
            if (item != null)
            {
                retValue = new StructureViewModel
                {
                    Id = item.Id,
                    Name = item.Name,
                    NameAr = item.NameAr,
                    NameFr = item.NameFr,
                    Code = !string.IsNullOrEmpty(item.Code) ? item.Code : ""
                };
            }
            return retValue;
        }

        #endregion
    }
}
