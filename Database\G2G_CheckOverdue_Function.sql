USE [ISFCTS2]
GO

/****** Object:  UserDefinedFunction [dbo].[checkOverDue]    Script Date: 9/29/2024 11:49:28 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE FUNCTION [dbo].[checkOverDue]
( 
    @DateValue  datetime
) 
RETURNS bit 
AS 
BEGIN 
    DECLARE @retValue bit 
    if(CAST(@DateValue AS DATE) <  CAST(GETDATE() AS DATE))
    set @retValue = 1
    else
    set @retValue = 0
    
    RETURN @retValue 
END

GO
