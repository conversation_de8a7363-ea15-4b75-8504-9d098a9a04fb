﻿import Intalio from './common.js'
class FollowUpOriginalDocumentModel extends Intalio.Model {
    constructor() {
        super();
        this.reference = null;
        this.subject = null;
        this.from = null;
        this.to = null;
        this.transferDate = null;
        this.registerDate = null;
        this.registeredBy = null;
    }
}
class FollowUpOriginalDocumentView extends Intalio.View {
    constructor(element, model) {
        super(element, "followuporiginaldocument", model);
    }
    render() {
        var self = this;
        //if ($('.modal.fade.in').length >= 1) {
        //    $("#" + self.model.ComponentId + "_btnMinimize").remove();
        //}
        $("#" + self.model.ComponentId + "_btnMinimize").click(function () {
            $(self.refs['modalFollowUpOriginalDocument']).modal("hide");
            if ($("i.maximize-modal[data-document='" + self.model.documentId + "']").length > 0) {
                return;
            }
                

            minimizeModal(self.model.ComponentId, self.model.reference, self.model.subject, self.model.from, self.model.to, self.model.transferDate, self.model.registerDate, self.model.registeredBy, self.model.documentId);
        });

        $("#" + self.model.ComponentId + "_btnClose").click(function () {
            $(self.refs['modalFollowUpOriginalDocument']).modal("hide");
            $(self.refs['modalFollowUpOriginalDocument']).data("remove", true);

            $("i.maximize-modal[data-component='" + self.model.ComponentId + "']").remove()
        });

    };
}

function minimizeModal(componentId, reference, subject, from, to, transferDate, registerDate, registeredBy, documentId) {
    if ($("i.maximize-modal[data-document='" + documentId + "']").length > 0)
        return;

    var content = "";

    if (reference != null && reference != "" && reference != undefined)
        content += `<p><strong>${Resources.ReferenceNumber}:</strong> ${reference}</p>`
    if (subject != null && subject != "" && subject != undefined)
        content += `<p><strong>${Resources.Subject}:</strong> ${subject}</p>`
    if (from != null && from != "" && from != undefined)
        content += `<p><strong>From:</strong> ${from}</p>`
    if (to != null && to != "" && to != undefined)
        content += `<p><strong>To:</strong> ${to}</p>`
    if (transferDate != null && transferDate != "" && transferDate != undefined)
        content += `<p><strong>Transfer Date:</strong> ${transferDate}</p>`
    if (registerDate != null && registerDate != "" && registerDate != undefined)
        content += `<p><strong>Registration Date:</strong> ${registerDate}</p>`
    if (registeredBy != null && registeredBy != "" && registeredBy != undefined)
        content += `<p><strong>Registered By:</strong> ${registeredBy}</p>`
    var isArabic = $("html").attr("dir") === "rtl"; // or $("html").attr("lang") === "ar"
    var positionClass = isArabic ? "ml-0 mr-lg" : "ml-lg"; // Use Bootstrap classes or custom
    $("div.wrapper footer").append(`<i class="fa fa-envelope-o ${positionClass} maximize-modal" data-toggle="popover" data-html="true" data-component="${componentId}"  data-document="${documentId}" data-placement="top" data-content="${content}" ></i>`);
    $('[data-toggle="popover"]').popover({ trigger: 'hover', offset: [0, 100] });

}

export default { FollowUpOriginalDocumentModel, FollowUpOriginalDocumentView };
