﻿using Elasticsearch.Net;
using Intalio.Core;
using Intalio.Core.DAL;
using Intalio.Core.Model;
using Intalio.Core.UI.Filters;
using Intalio.CTS.Core;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Filters;
using Intalio.IAM.Core.API;
using LinqKit;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.CodeAnalysis.FlowAnalysis;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Renci.SshNet.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using static iTextSharp.text.pdf.AcroFields;
using Serilog;

namespace Intalio.CTS.Controllers
{
    [Route("[controller]/[action]")]
    public class DocumentController : BaseController
    {
        #region Fields

        private readonly IHubContext<CommunicationHub> _hub;

        #endregion

        #region Ctor

        public DocumentController(IHubContext<CommunicationHub> hub)
        {
            _hub = hub;
        }

        #endregion

        #region Ajax

        [HttpPost]
        [HideInSwagger]
        public IActionResult UpdateDocumentStatus([FromForm] long documentId, [FromForm] DocumentStatus status)
        {
            var res = ManageDocument.UpdateDocumentStatus(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, status);
            return Ok(res);
        }

        [HttpPost]
        [HideInSwagger]
        public async Task<IActionResult> FilterG2GExternal([FromBody] List<int> ids)
        {
            var client = new HttpClient(new HttpClientHandler() { ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator });
            string url = Intalio.CTS.Core.Configuration.G2GService.TrimEnd('/') + "/G2GActions.svc/json/FilterG2GExternal";

            if (ids.Count == 0)
            {
                return Ok();
            }

            var json = Newtonsoft.Json.JsonConvert.SerializeObject(ids);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await client.PostAsync(url, content);
            string responseBody = await response.Content.ReadAsStringAsync();
            if (!response.IsSuccessStatusCode)
            {
                return BadRequest();
            }
            return Ok(responseBody);
        }

        [HttpPost]
        [HideInSwagger]
        public async Task<IActionResult> ExportToG2G([FromQuery] int documentId, [FromBody]List<G2GRequestModel> govIds)
        {
            try
            {
                var canExport = Convert.ToBoolean(User.Claims.FirstOrDefault(t => t.Type == "CanExportG2G")?.Value);
                if (!canExport)
                {
                    return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden);
                }
                var result = await ManageDocument.ExportToG2G(documentId, govIds, UserId, StructureId);
                if (result != null)
                {
                    return Ok(result);
                }
                return BadRequest();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        /// <summary>
        /// Create or edit a document
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [RequestSizeLimit(2147483648)] //2 GB
        [RequestFormLimits(MultipartBodyLengthLimit = 1073741824)]//1 GB
        public async Task<IActionResult> Save([FromForm] DocumentViewModel model)
        {
            try
            {
                Log.Information("isftesting save model: " + JsonConvert.SerializeObject(model));
                var retValue = string.Empty;
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                if (!model.Register || (model.Register && ModelState.IsValid))
                {
                    FileViewModel originalMail = new FileViewModel();
                    var file = Request.Form.Files.FirstOrDefault(t => t.Name == "OriginalMail[0]");
                    if (file != null)
                    {
                        originalMail.Name = Intalio.Core.Helper.GetFileName(file.FileName);
                        originalMail.FileSize = file.Length;
                        originalMail.ContentType = file.ContentType;
                        originalMail.Data = file.OpenReadStream().StreamToByte();
                        originalMail.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();

                    }
                    else if (model.ScannedFile != null)
                    {
                        originalMail = model.ScannedFile;
                    }

                    if (model.Id.HasValue)
                    {
                        (bool Edited, string ReferenceNumber, string Message) editDocument;
                        if (model.DelegationId != null)
                        {
                            var delegation = model.DelegationId.HasValue ? ManageDelegation.GetByDelegationId(UserId, model.DelegationId.Value) : null;
                            if (delegation.DraftInbox == true)
                            {
                                var userData = Core.API.ManageUser.GetUser(delegation.FromUserId);
                                long structureId = Core.Utility.IdentityHelperExtension.getCurrentLoggedInStructure(delegation.FromUserId) != null ? (long)Core.Utility.IdentityHelperExtension.getCurrentLoggedInStructure(UserId):default;
                                var structureReceiverAttribute = userData.Attributes.Where(a => a.Text == "StructureReceiver").FirstOrDefault();
                                var allowEditSign = userData.Attributes.Where(a => a.Text == "AllowEditSigned").FirstOrDefault();
                                editDocument = await ManageDocument.Edit(model, delegation.FromUserId, structureId, userData.StructureIds, Convert.ToBoolean(structureReceiverAttribute.Value), delegation.PrivacyLevel, Convert.ToBoolean(allowEditSign.Value), model.TransferId);


                            }
                            else
                                editDocument = await ManageDocument.Edit(model, UserId, StructureId, StructureIds, IsStructureReceiver, PrivacyLevel, AllowEditSigned, model.TransferId);


                        }
                        else
                            editDocument = await ManageDocument.Edit(model, UserId, StructureId, StructureIds, IsStructureReceiver, PrivacyLevel, AllowEditSigned, model.TransferId);

                        if (editDocument.Edited)
                        {
                            if (!string.IsNullOrEmpty(editDocument.Message))
                            {
                                retValue = editDocument.Message;
                            }
                            else
                            {
                                retValue = editDocument.ReferenceNumber;
                            }
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        }
                    }
                    else
                    {

                        bool createdDocument;
                        if (model.DelegationId != null)
                        {

                            var delegation = model.DelegationId.HasValue ? ManageDelegation.GetByDelegationId(UserId, model.DelegationId.Value) : null;
                            if (delegation.DraftInbox == true)
                            {

                                var userData = Core.API.ManageUser.GetUser(delegation.FromUserId);

                                var structureReceiverAttribute = userData.Attributes.Where(a => a.Text == "StructureReceiver").FirstOrDefault();
                                var structureSenderAttribute = userData.Attributes.Where(a => a.Text == "StructureSender").FirstOrDefault();

                                var fromUserName = Core.API.ManageUser.GetUserName(UserId, Language);
                                var toUserName = Core.API.ManageUser.GetUserName((long?)UserId ?? 0, Language);
                                string activityLogNote = toUserName.FirstName + " " + toUserName.LastName + " " + TranslationUtility.Translate("OnBehalfOf", Language) + " " + fromUserName.FirstName + " " + fromUserName.LastName + TranslationUtility.Translate("CopyCorrespondence", Language);
                                createdDocument = model.IsCopy ?
                                    (await ManageDocument.CreateCopy(model, originalMail, delegation.FromUserId, userData.ApplicationRoleId.Value, userData.StructureIds, Convert.ToBoolean(structureReceiverAttribute.Value), Convert.ToBoolean(structureSenderAttribute.Value), delegation.PrivacyLevel, StructureId, activityLogNote, ActivityLogs.Save, Language, UserId)).success :
                                    await ManageDocument.Create(model, originalMail, delegation.FromUserId, userData.ApplicationRoleId.Value, userData.StructureIds, Convert.ToBoolean(structureReceiverAttribute.Value), delegation.PrivacyLevel, Language);


                            }
                            else
                                createdDocument = model.IsCopy ?
                                    (await ManageDocument.CreateCopy(model, originalMail, UserId, RoleId, StructureIds, IsStructureReceiver, IsStructureSender, PrivacyLevel, StructureId, TranslationUtility.Translate("CopyCorrespondence", Language), ActivityLogs.Save, Language)).success :
                                    await ManageDocument.Create(model, originalMail, UserId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel, Language);


                        }
                        else
                            createdDocument = model.IsCopy ?
                                    (await ManageDocument.CreateCopy(model, originalMail, UserId, RoleId, StructureIds, IsStructureReceiver, IsStructureSender, PrivacyLevel, StructureId, TranslationUtility.Translate("CopyCorrespondence", Language), ActivityLogs.Save, Language)).success :
                                    await ManageDocument.Create(model, originalMail, UserId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel, Language);


                        if (createdDocument /*await ManageDocument.Create(model, originalMail, UserId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel,Language)*/)
                        {
                            retValue = model.Id.ToString();
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        }
                        else
                        {
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden;
                        }
                    }
                }
                Log.Information("isftesting save retValue: " + JsonConvert.SerializeObject(retValue));
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        [HttpPost]
        [RequestSizeLimit(2147483648)] //2 GB
        [RequestFormLimits(MultipartBodyLengthLimit = 1073741824)]//1 GB
        [CustomActionsAuthorizationFilter(new string[] { nameof(CustomActions.Edit) })]


        public async Task<IActionResult> SaveComplete([FromForm] DocumentViewModel model)

        {
            try
            {
                var retValue = string.Empty;
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                if (!model.Register || (model.Register && ModelState.IsValid))
                {
                    FileViewModel originalMail = new FileViewModel();
                    var file = Request.Form.Files.FirstOrDefault(t => t.Name == "OriginalMail[0]");
                    if (file != null)
                    {
                        originalMail.Name = Intalio.Core.Helper.GetFileName(file.FileName);
                        originalMail.FileSize = file.Length;
                        originalMail.ContentType = file.ContentType;
                        originalMail.Data = file.OpenReadStream().StreamToByte();
                        originalMail.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();
                    }
                    else if (model.ScannedFile != null)
                    {
                        originalMail = model.ScannedFile;
                    }

                    if (model.Id.HasValue)
                    {
                        var editDocument = await ManageDocument.EditComplete(model, UserId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel);
                        if (editDocument.Edited)
                        {
                            if (!string.IsNullOrEmpty(editDocument.Message))
                            {
                                retValue = editDocument.Message;
                            }
                            else
                            {
                                retValue = editDocument.ReferenceNumber;
                            }
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        }
                    }
                    else
                    {
                        if (await ManageDocument.Create(model, originalMail, UserId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel, Language))
                        {
                            retValue = model.Id.ToString();
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        }
                        else
                        {
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden;
                        }
                    }
                }
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// edit a document
        /// </summary>
        /// <param name="model"></param>
        /// <param name="transferId"></param>
        /// <returns></returns>
        /// <remarks>
        /// must be allow edit attribute and original document not signed
        /// <b>FromForm</b> is added before each parameter to support swagger issue with multipart form-data (application/x-www-form-urlencoded)
        /// </remarks>
        [HttpPost]
        [RequestSizeLimit(2147483648)] //2 GB
        [RequestFormLimits(MultipartBodyLengthLimit = 1073741824)]//1 GB
        public async Task<IActionResult> Edit([FromForm] DocumentViewModel model, [FromForm] long? transferId)
        {
            try
            {
                var retValue = string.Empty;
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                if (!model.Register || (model.Register && ModelState.IsValid))
                {
                    FileViewModel originalMail = new FileViewModel();
                    var file = Request.Form.Files.FirstOrDefault(t => t.Name == "OriginalMail[0]");
                    if (file != null)
                    {
                        originalMail.Name = Intalio.Core.Helper.GetFileName(file.FileName);
                        originalMail.FileSize = file.Length;
                        originalMail.ContentType = file.ContentType;
                        originalMail.Data = file.OpenReadStream().StreamToByte();
                        originalMail.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();
                    }
                    else if (model.ScannedFile != null)
                    {
                        originalMail = model.ScannedFile;
                    }
                    if (model.Id.HasValue)
                    {
                        var editDocument = await ManageDocument.Edit(model, UserId, StructureId, StructureIds, IsStructureReceiver, PrivacyLevel, AllowEditSigned, transferId, Language);
                        if (editDocument.Edited)
                        {
                            if (!string.IsNullOrEmpty(editDocument.Message))
                            {
                                retValue = editDocument.Message;
                            }
                            else
                            {
                                retValue = editDocument.ReferenceNumber;
                            }
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        }
                    }
                }
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Send a document
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> Send(SendReplyModel model)
        {
            try
            {
                if (!IsStructureSender && model.StructureReceivers.Any(x => !StructureIds.Any(t => t == x)))
                {
                    return Ok("NotStructureSender");
                }
                if (!StructureIds.Contains(model.StructureId))
                {
                    model.StructureId = StructureId;
                }
                if (model.WithSign && model.SignatureTemplateId != null)
                {
                    var signed = await ManageTransfer.SignDocument(model.DocumentId.Value, model.TransferId, UserId, StructureIds, IsStructureReceiver, IsStructureSender, PrivacyLevel, model.SignatureTemplateId.Value, model.DelegationId, Language);
                    if (!signed.updated)
                    {
                        var res = await Core.API.ManageAttachment.RestoreWordDocument(model.DocumentId.Value);
                        if (!res)
                        {
                            throw new InvalidOperationException("Failed to restore Word document.");
                        }
                    }
                    return Ok(signed.message);
                }
                //var result = ManageDocument.Send(UserId, StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel, model.Id, model.TransferId, model.PurposeId, model.DueDate, model.Instruction, model.StructureId);
                //if (!ManageTransfer.IsAudioFile(model.VoiceNote))
                //{
                //    return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status415UnsupportedMediaType);
                //}
                (bool Sent, string Message) result;
                if (model.DelegationId != null)
                {
                    var delegation = model.DelegationId.HasValue ? ManageDelegation.GetByDelegationId(UserId, model.DelegationId.Value) : null;
                    if (delegation.DraftInbox)
                    {
                        var userData = Core.API.ManageUser.GetUser(delegation.FromUserId);

                        var structureReceiverAttribute = userData.Attributes.Where(a => a.Text == "StructureReceiver").FirstOrDefault();
                        var structureSenderAttribute = userData.Attributes.Where(a => a.Text == "StructureSender").FirstOrDefault();

                        var allowEditSign = userData.Attributes.Where(a => a.Text == "AllowEditSigned").FirstOrDefault();
                        result = await ManageDocument.Send(delegation.FromUserId, userData.StructureIds, Convert.ToBoolean(structureSenderAttribute.Value), Convert.ToBoolean(structureReceiverAttribute.Value), delegation.PrivacyLevel, model.Id, model.TransferId, model.PurposeId, model.DueDate, model.Instruction, model.StructureId, model.VoiceNote, model.VoiceNotePrivacy, Language);

                    }
                    else
                        result = await ManageDocument.Send(UserId, StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel, model.Id, model.TransferId, model.PurposeId, model.DueDate, model.Instruction, model.StructureId, model.VoiceNote, model.VoiceNotePrivacy, Language);



                }
                else
                    result = await ManageDocument.Send(UserId, StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel, model.Id, model.TransferId, model.PurposeId, model.DueDate, model.Instruction, model.StructureId, model.VoiceNote, model.VoiceNotePrivacy, Language);

                if (result.Sent)
                {
                    _hub.Send(model.Id, Language);
                    return Ok();
                }
                else if (result.Message == "FileInUse")
                {
                    return Ok(result.Message);
                }
                else if (result.Message == "CantGenerateReferenceNumber")
                {
                    return Ok(result.Message);
                }
                return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Get a draft document
        /// </summary>
        /// <param name="id">Document id</param>
        /// <remarks>The document must have a <b>draft status</b> and is <b>created by the current user</b></remarks>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(DocumentDetailsModel), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> Get(long id, long? delegationId = null,bool fromRejectedDocument= false)
        {
            try
            {
                dynamic model;
                if (delegationId != null)
                {
                    var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(UserId, delegationId.Value) : null;
                    if (delegation.DraftInbox)
                    {
                        var userData = Core.API.ManageUser.GetUser(delegation.FromUserId);

                        model = await ManageDocument.FindCreatedByUser(delegation.FromUserId, id, userData.ApplicationRoleId.Value, userData.StructureIds[0], Language, fromRejectedDocument, delegation);

                    }
                    else
                    {
                        model = await ManageDocument.FindCreatedByUser(UserId, id, RoleId, StructureIds[0], Language, fromRejectedDocument);

                    }

                }
                else
                {
                    var s = StructureIds;
                    model = await ManageDocument.FindCreatedByUser(UserId, id, RoleId, StructureIds[0], Language, fromRejectedDocument);

                }
                if (model != null)
                {
                    return Ok(model);
                }
                return HttpAjaxAccessDeniedError();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, id);
            }
        }

        /// <summary>
        /// Get a document
        /// </summary>
        /// <remarks>The document must be <b>created by the current user</b></remarks>
        /// <param name="id">Document id</param>
        /// <param name="basketId">basketId id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(DocumentDetailsModel), 200)]
        [Produces("application/json")]
        public IActionResult GetDocument(long id, int? basketId)
        {
            try
            {
                var model = ManageDocument.GetDocument(UserId, id, basketId, StructureIds, Language, RoleId);
                if (model != null)
                {
                    return Ok(model);
                }
                return HttpAjaxAccessDeniedError();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, id);
            }
        }

        /// <summary>
        /// Find document basic info by transfer
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(DocumentDetailsModel), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> GetDocumentBasicInfoByTransferId(long id, long? delegationId, bool? overrideAccess, short? nodeId)
        {
            try
            {
                DocumentDetailsModel model = await ManageDocument.FindDocumentBasicInfoByTransferId(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, id, delegationId, Language, overrideAccess == true, nodeId);
                return Ok(model);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Find document basic info
        /// </summary>
        /// <param name="id">Document id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(DocumentDetailsModel), 200)]
        [Produces("application/json")]
        public IActionResult GetDocumentBasicInfo(long id, long? delegationId)
        {
            try
            {
                DocumentDetailsModel model = ManageDocument.FindDocumentBasicInfo(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, id, delegationId, Language);
                return Ok(model);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [CustomTabsAuthorizationFilter(new string[] { nameof(CustomTabs.AgendaTopicsList) })]
        [HttpGet]
        public JsonResult GetDocumentCustomInfo(long documentId, long? delegationId)
        {
            try
            {
                int draw = 0;

                var retValue = ManageDocument.FindDocumentCustomInfo(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, documentId, delegationId, Language);

                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Count,
                    recordsFiltered = retValue.Count,
                    data = retValue
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        [CustomTabsAuthorizationFilter(new string[] { nameof(CustomTabs.AgendaTopicsList) })]

        [HttpDelete]
        public IActionResult DeleteDocumentCustomInfo(long documentId, List<long> ids, long? delegationId)
        {
            try
            {
                return Ok(ManageDocument.DeleteDocumentCustomInfo(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, documentId, ids, delegationId, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        [HttpPost]
        public IActionResult EditDocumentCustomInfo(long documentId, long id, long? delegationId, string resolution)
        {
            try
            {
                return Ok(ManageDocument.EditDocumentCustomInfo(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, id, documentId, resolution, delegationId, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Get document by id
        /// </summary>
        /// <remarks>The document must be <b>created by the current user</b>or <b>have access on any document task</b></remarks>
        /// <param name="id">Document id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(DocumentDetailsModel), 200)]

        [Produces("application/json")]
        public async Task<IActionResult> GetSearchDocument(long id, long? delegationId, long? parentDocumentId)
        {
            try
            {
                var model = await ManageDocument.GetSearchDocument(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, id, delegationId, parentDocumentId, Language, false);
                if (model != null)
                {
                    return Ok(model);
                }
                return Ok("NoAccess");
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, id);
            }
        }

        [HttpGet]
        [ProducesResponseType(typeof(DocumentDetailsModel), 200)]
        //[CustomActionsAuthorizationFilter(new string[] { nameof(CustomActions.Edit) })]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.ManageCorrespondence), nameof(CustomMenus.ManageStructureUsersCorrespondences) })]
        [Produces("application/json")]
        public async Task<IActionResult> GetSearchDocumentEdit(long id, long? delegationId, long? parentDocumentId)
        {
            try
            {
                var model = await ManageDocument.GetSearchDocument(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, id, delegationId, parentDocumentId, Language, true);
                if (model != null)
                {
                    return Ok(model);
                }
                return Ok("NoAccess");
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, id);
            }
        }

        /// <summary>
        /// Delete documents.
        /// </summary>
        /// <remarks>The document will be deleted only if it has a <b>draft status</b>, <b>not yet registered</b> and is <b>created by the current user</b></remarks>
        /// <param name="ids">Ids of the documents to be deleted</param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<IActionResult> Delete(List<long> ids)
        {
            try
            {
                return Ok(await ManageDocument.Delete(UserId, StructureIds[0], ids));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List draft documentsdocument/GetDraftCounts
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<DocumentDraftListViewModel>), 200)]
        public async Task<JsonResult> ListDraft([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                long? delegationId = null;
                if (Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]))
                {
                    delegationId = Convert.ToInt64(Request.Form["DelegationId"][0]);
                }
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt32(Request.Form["StructureId"][0]) : default;

                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Json(new
                    {
                        draw,
                        recordsTotal = 0,
                        recordsFiltered = 0,
                        data = new List<DocumentDraftListViewModel>()
                    });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                bool isStructureDraft = filter.Any(f => f.PropertyName == "CreatedByStructure");
                filter.RemoveAll(f => f.PropertyName == "CreatedByStructure");

                if (categoryId != default)
                {
                    filter.Add("CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("CreatedByStructureId", structureId, Operator.Equals);
                }

                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Category.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Subject" });
                            break;
                        case "sendingEntity":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "SendingEntity.Name" });
                            break;
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                        case "modifiedDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "ModifiedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "ModifiedDate" });
                }
                var retValue = await ManageDocument.ListDraft(start, length, UserId, filter, sorting, delegationId, Language, isStructureDraft, PrivacyLevel);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List my requests
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<DocumentDraftListViewModel>), 200)]
        public async Task<JsonResult> ListMyRequests([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt32(Request.Form["StructureId"][0]) : default;

                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Json(new
                    {
                        draw,
                        recordsTotal = 0,
                        recordsFiltered = 0,
                        data = new List<DocumentDraftListViewModel>()
                    });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (categoryId != default)
                {
                    filter.Add("CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now.Date, Operator.LessThan);
                }
                if (structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("CreatedByStructureId", structureId, Operator.Equals);
                }

                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Category.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Subject" });
                            break;
                        case "sendingEntity":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "SendingEntity.Name" });
                            break;
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "ModifiedDate" });
                }
                var retValue = await ManageDocument
                                  .ListMyRequests(start, length, UserId, filter, sorting, language: Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }/// <summary>
         /// List Custom Basket
         /// </summary>
         /// <param name="draw"></param>
         /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
         /// <param name="length">Number of records to be returned</param>
         /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<DocumentDraftListViewModel>), 200)]
        public async Task<JsonResult> ListCustomBasketDocuments([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                int BasketId = Request.Form["BasketId"].Count > 0 ? Convert.ToInt32(Request.Form["BasketId"][0]) : default;
                var result = ManageNode.GetNodeConditionsInExpressionBuilder(0);
                ExpressionBuilderFilters filter = result.Filters;
                (int, List<DocumentDraftListViewModel>) retValue = (0, new List<DocumentDraftListViewModel>());

                if (categoryId != default)
                {
                    filter.Add("CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Category.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Subject" });
                            break;
                        case "sendingEntity":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "SendingEntity.Name" });
                            break;
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "ModifiedDate" });
                }
                var UserPermition = ManageBasket.CheckUserPermition(BasketId, UserId);
                if (UserPermition.IsOwmer != false || UserPermition.RoleId.HasValue)
                {
                    retValue = await ManageDocument
                                 .ListCustomBasketDocuments(start, length, UserPermition.DocumentIds, filter, sorting, language: Language);
                }
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List closed documents
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<DocumentDraftListViewModel>), 200)]
        public async Task<JsonResult> ListClosed([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt32(Request.Form["StructureId"][0]) : default;

                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Json(new
                    {
                        draw,
                        recordsTotal = 0,
                        recordsFiltered = 0,
                        data = new List<DocumentDraftListViewModel>()
                    });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (categoryId != default)
                {
                    filter.Add("CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("CreatedByStructureId", structureId, Operator.Equals);
                }

                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Category.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Subject" });
                            break;
                        case "sendingEntity":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "SendingEntity.Name" });
                            break;
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = await ManageDocument.ListClosed(start, length, UserId, StructureIds, IsStructureReceiver,
                                                                                PrivacyLevel, filter, sorting, language: Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// Get draft documents count
        /// </summary>
        /// <param name="nodeId">Node id</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetDraftCounts(short nodeId, long? delegationId, long? loggedInStructureId)
        {
            try
            {

                if (Core.Configuration.EnablePerStructure && (loggedInStructureId == null || !StructureIds.Any(S => S == loggedInStructureId.Value)))
                {
                    (int Total, int Today) errValue = (0, 0);
                    return Ok(new { errValue.Total, errValue.Today });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                (int Total, int Today) retValue = await ManageDocument.GetDraftCounts(UserId, loggedInStructureId, result.Filters, delegationId, PrivacyLevel);
                return Ok(new { retValue.Total, retValue.Today });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Get my requests documents count
        /// </summary>
        /// <param name="nodeId">Node id</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetMyRequestsCounts(short nodeId, long? loggedInStructureId)
        {
            try
            {
                if ((loggedInStructureId == null || !StructureIds.Any(S => S == loggedInStructureId.Value)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    (int Total, int Today) errValue = (0, 0);
                    return Ok(new { errValue.Total, errValue.Today });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                (int Total, int Today) retValue = await ManageDocument.GetMyRequestsCounts(UserId, loggedInStructureId, result.Filters);
                return Ok(new { retValue.Total, retValue.Today });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Get closed documents count
        /// </summary>
        /// <param name="nodeId">Node id</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetClosedCounts(short nodeId, long? loggedInStructureId)
        {
            try
            {
                if ((loggedInStructureId == null || !StructureIds.Any(S => S == loggedInStructureId.Value)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    (int Total, int Today) errValue = (0, 0);
                    return Ok(new { errValue.Total, errValue.Today });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                (int Total, int Today) retValue = await ManageDocument.GetClosedCounts(UserId, StructureIds, loggedInStructureId, IsStructureReceiver, PrivacyLevel, result.Filters);
                return Ok(new { retValue.Total, retValue.Today });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Get document visual tracking
        /// </summary>
        /// <param name="id">Document id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        //[CustomTabsAuthorizationFilter(new string[] { nameof(CustomTabs.VisualTracking) })]

        [HttpGet]
        [ProducesResponseType(typeof(List<TrackingModel>), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> GetTrackingData(long id, long? delegationId)
        {
            try
            {
                return Ok(await ManageDocument.GetTrackingData(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, id, RoleId, delegationId, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Get document by transfer
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(DocumentViewModel), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> GetDocumentByTransferId(long id, long? delegationId, bool fromStructureInbox = false)
        {
            try
            {
                var model = await ManageDocument.FindByTransferId(UserId, StructureIds, fromStructureInbox ? true : IsStructureReceiver, PrivacyLevel, id, AllowEditSigned, delegationId, Language, RoleId);
                if (model != null)
                {
                    return Ok(model);
                }
                return HttpAjaxAccessDeniedError();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, id);
            }
        }

        /// <summary>
        /// Get document barcode
        /// </summary>
        /// <param name="documentId">Document id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(byte[]), 200)]
        public IActionResult PreviewBarcodeByDocument(int documentId, long? delegationId)
        {
            try
            {
                byte[] retValue = ManageDocument.GenerateBarcode(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxErrorWithMsg(ex);
            }
        }

        /// <summary>
        /// Check if document has an external document linked to it by external reference number
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(bool), 200)]
        public IActionResult HasExternalReferencedDocument(long id)
        {
            try
            {
                return Ok(ManageDocument.HasExternalReferencedDocument(id, UserId));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Complete draft document
        /// </summary>
        /// <param name="id">document id</param>
        /// <param name="purposeId">purpose id</param>
        /// <param name="visibleForStructureUsers"></param>
        /// /// <param name="forArchive"></param>
        /// <param name="toStructureIds"></param>
        /// <param name="rootFolderId"></param>
        /// <returns></returns>
        /// <remarks>
        /// Document will be completed and a closed transfer will be created and sent to document created by structure in case visibleForStructureUsers is <b>true</b>
        /// Document will be completed and a closed transfer will be created and sent to current user in case visibleForStructureUsers is <b>false</b>
        /// </remarks>
        [HttpPost]
        [ProducesResponseType(typeof(bool), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> Complete(long id, short purposeId, List<long> toStructureIds, string completeReasonNote, bool visibleForStructureUsers = false, long? delegationId = null, bool? forArchive = false, long? rootFolderId = null)
        {
            try
            {
                (bool Success, string Message) retValue;
                if (delegationId != null)
                {
                    var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(UserId, delegationId.Value) : null;
                    if (delegation != null)
                        retValue = ManageDocument.Complete(delegation.FromUserId, delegation.StructureIds[0], id, purposeId, toStructureIds, completeReasonNote, visibleForStructureUsers);
                    else
                        retValue = ManageDocument.Complete(UserId, StructureId, id, purposeId, toStructureIds, completeReasonNote, visibleForStructureUsers);


                }
                else
                    retValue = ManageDocument.Complete(UserId, StructureId, id, purposeId, toStructureIds, completeReasonNote, visibleForStructureUsers);

                if (!retValue.Success)
                {
                    return Ok(retValue.Message);
                }
                else
                {
                    if (forArchive ?? false)
                    {
                        var response = await ManageDocument.Archive(UserId, id, RoleId, StructureId, StructureIds, IsStructureReceiver, PrivacyLevel, rootFolderId ?? 0);
                        if (!response.Success)
                        {
                            return Ok(response.Message);
                        }
                    }
                    if (Core.Configuration.EnableEmailNotification && EnableNotifications)
                    {
                        ManageNotification.CompleteDocument(id);
                    }
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Create or edit a document for reply by category
        /// </summary>
        /// <param name="model"></param>
        /// <param name="AttachmentIds"></param>
        /// <param name="previousDocumentId"></param>
        /// <returns></returns>
        [HttpPost]
        [RequestSizeLimit(2147483648)] //2 GB
        [RequestFormLimits(MultipartBodyLengthLimit = 1073741824)]//1 GB
        public async Task<IActionResult> ReplyByCategory([FromForm] DocumentViewModel model, List<string> AttachmentIds, long previousDocumentId)
        {
            try
            {
                var retValue = string.Empty;
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                FileViewModel originalMail = new FileViewModel();
                var file = Request.Form.Files.FirstOrDefault(t => t.Name == "OriginalMail[0]");
                if (file != null)
                {
                    originalMail.Name = Intalio.Core.Helper.GetFileName(file.FileName);
                    originalMail.FileSize = file.Length;
                    originalMail.ContentType = file.ContentType;
                    originalMail.Data = file.OpenReadStream().StreamToByte();
                    originalMail.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();
                }
                else if (model.ScannedFile != null)
                {
                    originalMail = model.ScannedFile;
                }
                if (!StructureIds.Contains(model.CreatedByStructureId))
                {
                    model.CreatedByStructureId = StructureId;
                }

                if (await ManageDocument.CreateReplyByCategory(model, originalMail, previousDocumentId, UserId, StructureId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel, IsStructureSender, AttachmentIds, Language))
                {
                    retValue = model.Id.ToString();
                    code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                }
                else
                {
                    code = Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden;
                }
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Create Meeting Agenda
        /// </summary>
        /// <param name="model"></param>
        /// <param name="SelectedDocumentIds"></param>
        /// <returns></returns>
        [HttpPost]
        [RequestSizeLimit(2147483648)] //2 GB
        [RequestFormLimits(MultipartBodyLengthLimit = 1073741824)]//1 GB

        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CreateMeetingAgenda) })]

        public async Task<IActionResult> CreateMeetingAgenda([FromForm] DocumentViewModel model, List<long> SelectedDocumentIds)
        {
            try
            {
                var retValue = new { id = string.Empty, state = string.Empty };
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                if (!model.Register || (model.Register && ModelState.IsValid))
                {
                    FileViewModel originalMail = new FileViewModel();
                    var file = Request.Form.Files.FirstOrDefault(t => t.Name == "OriginalMail[0]");
                    if (file != null)
                    {
                        originalMail.Name = Intalio.Core.Helper.GetFileName(file.FileName);
                        originalMail.FileSize = file.Length;
                        originalMail.ContentType = file.ContentType;
                        originalMail.Data = file.OpenReadStream().StreamToByte();
                        originalMail.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();
                    }
                    else if (model.ScannedFile != null)
                    {
                        originalMail = model.ScannedFile;
                    }
                    var result = await ManageDocument.CreateMeetingAgenda(model, originalMail, SelectedDocumentIds, UserId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel);
                    if (result.Success)
                    {
                        retValue = new { id = model.Id.ToString(), state = result.State.ToString() };
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                    }
                    else
                    {
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden;
                    }
                }
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Create or edit a document for Meeting Resoluton
        /// </summary>
        /// <param name="model"></param>
        /// <param name="previousDocumentId"></param>
        /// <returns></returns>
        [HttpPost]
        [RequestSizeLimit(2147483648)] //2 GB
        [RequestFormLimits(MultipartBodyLengthLimit = 1073741824)]//1 GB
        public async Task<IActionResult> CreateMeetingResolutionDocument([FromForm] DocumentViewModel model, long previousDocumentId)
        {
            try
            {
                var retValue = new { id = string.Empty, state = string.Empty };
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                FileViewModel originalMail = new FileViewModel();
                var file = Request.Form.Files.FirstOrDefault(t => t.Name == "OriginalMail[0]");
                if (file != null)
                {
                    originalMail.Name = Intalio.Core.Helper.GetFileName(file.FileName);
                    originalMail.FileSize = file.Length;
                    originalMail.ContentType = file.ContentType;
                    originalMail.Data = file.OpenReadStream().StreamToByte();
                    originalMail.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();
                }
                else if (model.ScannedFile != null)
                {
                    originalMail = model.ScannedFile;
                }
                if (!StructureIds.Contains(model.CreatedByStructureId))
                {
                    model.CreatedByStructureId = StructureId;
                }
                var result = await ManageDocument.CreateMeetingResolution(model, originalMail, previousDocumentId, UserId, StructureId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel, IsStructureSender);
                if (result.Success)
                {
                    retValue = new { id = model.Id.ToString(), state = result.State.ToString() };
                    code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                }
                else
                {
                    code = Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden;
                }
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Create or edit a document for Meeting Minutes and sep 
        /// </summary>
        /// <param name="model"></param>
        /// <param name="SelectedDocumentIds"></param>
        /// <returns></returns>
        [CustomTabsAuthorizationFilter(new string[] { nameof(CustomTabs.AgendaTopicsList) })]

        [HttpPost]
        [RequestSizeLimit(2147483648)] //2 GB
        [RequestFormLimits(MultipartBodyLengthLimit = 1073741824)]//1 GB
        public async Task<IActionResult> CreateMeetingDocument([FromForm] DocumentViewModel model, List<long> SelectedDocumentIds, long PreviousDocumentId)
        {
            try
            {
                var retValue = new { id = string.Empty, state = string.Empty };
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                FileViewModel originalMail = new FileViewModel();
                var file = Request.Form.Files.FirstOrDefault(t => t.Name == "OriginalMail[0]");
                if (file != null)
                {
                    originalMail.Name = Intalio.Core.Helper.GetFileName(file.FileName);
                    originalMail.FileSize = file.Length;
                    originalMail.ContentType = file.ContentType;
                    originalMail.Data = file.OpenReadStream().StreamToByte();
                    originalMail.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();
                }
                else if (model.ScannedFile != null)
                {
                    originalMail = model.ScannedFile;
                }
                if (!StructureIds.Contains(model.CreatedByStructureId))
                {
                    model.CreatedByStructureId = StructureId;
                }
                var result = await ManageDocument.CreateMeeting(model, originalMail, SelectedDocumentIds, PreviousDocumentId, UserId, StructureId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel, IsStructureSender);
                if (result.Success)
                {
                    retValue = new { id = model.Id.ToString(), state = result.State.ToString() };
                    code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                }
                else
                {
                    code = Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden;
                }
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List my requests for vip mode
        /// </summary>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<DocumentDraftListViewVipModel>), 200)]
        public IActionResult ListMyRequestsVip([FromForm] int start)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt32(Request.Form["StructureId"][0]) : default;
                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Ok(new List<DocumentDraftListViewVipModel>());
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (categoryId != default)
                {
                    filter.Add("CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (overdue || result.OverDue.HasValue)
                {
                    filter.Add("DueDate", DateTime.Now.Date, Operator.LessThan);
                    //filter.Add("DueDate", "CreatedDate.Date", Operator.NotEqual);
                }
                if (structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("CreatedByStructureId", structureId, Operator.Equals);
                }


                return Ok(ManageDocument.ListMyRequestsVip(start, UserId, filter, language: Language));

            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List closed documents for vip mode
        /// </summary>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<DocumentDraftListViewVipModel>), 200)]
        public IActionResult ListClosedVip([FromForm] int start)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt32(Request.Form["StructureId"][0]) : default;

                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Ok(new List<DocumentDraftListViewVipModel>());
                }
                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (categoryId != default)
                {
                    filter.Add("CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("CreatedByStructureId", structureId, Operator.Equals);
                }

                return Ok(ManageDocument.ListClosedVip(start, UserId, StructureIds,
                                                               IsStructureReceiver, PrivacyLevel, filter, language: Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List draft documents for vip mode
        /// </summary>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<DocumentDraftListViewModel>), 200)]
        public IActionResult ListDraftVip([FromForm] int start)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt32(Request.Form["StructureId"][0]) : default;

                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Ok(new List<DocumentDraftListViewVipModel>());
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                bool isStructureDraft = filter.Any(f => f.PropertyName == "CreatedByStructure");
                filter.RemoveAll(f => f.PropertyName == "CreatedByStructure");

                if (categoryId != default)
                {
                    filter.Add("CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("CreatedByStructureId", structureId, Operator.Equals);
                }

                return Ok(ManageDocument.ListDraftVip(start, UserId, filter, Language, isStructureDraft, PrivacyLevel));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Get draft document
        /// </summary>
        /// <remarks>The document must be <b>created by the current user</b></remarks>
        /// <param name="id">Document id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(DocumentDraftListViewModel), 200)]
        [Produces("application/json")]
        public IActionResult GetDraftDocument(long id)
        {
            try
            {
                var model = ManageDocument.GetDraftDocument(UserId, id, Language);
                if (model != null)
                {
                    return Ok(model);
                }
                return HttpAjaxAccessDeniedError();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, id);
            }
        }

        /// <summary>
        /// check document is registed
        /// </summary>
        /// <param name="id">Document id</param>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        public async Task<IActionResult> IsDocumentRegistered(long id)
        {
            try
            {
                var result = await ManageDocument.IsDocumentRegistered(id, UserId, StructureIds, IsStructureReceiver, PrivacyLevel);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, id);
            }
        }
        [HttpGet]
        [Produces("application/json")]
        public async Task<IActionResult> GetDetailsAfterCheckIn(long id)
        {
            try
            {

                (bool isPDF, bool isSigned) isPDFSigned = await ManageDocument.IsOriginalDocumentSigned(id, UserId, StructureIds, IsStructureReceiver, PrivacyLevel);
                var result = new
                {
                    isPDF = isPDFSigned.isPDF,
                    isSigned = isPDFSigned.isSigned,
                    isEnableAttributeEdit = Intalio.CTS.Core.Configuration.EnableAttributeEdit,
                    isEnableAttributrEditSign = AllowEditSigned
                };
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, id);
            }
        }

        [HttpPost]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.ManageCorrespondence) })]
        [Produces("application/json")]
        public IActionResult ReturnToDraft(long id)
        {
            try
            {
                var result = ManageDocument.ReturnToDraft(id, UserId, RoleId, Language);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, id);
            }
        }


        [HttpPost]
        public async Task<IActionResult> Export(DocumentViewModel model)
        {
            (bool Export, string Message, List<long> NewIncomingDocumentIds) result = (false, "", new List<long>());
            try
            {
                var transfer = ManageTransfer.FindIncludeDocumentAndCategory(model.TransferId.Value);
                ManageTransfer.StartExporting(model.Id.Value, model.TransferId);
                var validation = ManageTransfer.CheckCanExportOrComplete(UserId, transfer, StructureIds, IsStructureReceiver, PrivacyLevel, true, true);
                if (validation.Valid)
                {
                    var file = Request.Form.Files.FirstOrDefault(t => t.Name == "OriginalMail[0]");
                    result = await ManageDocument.ExportOutgoing(model, UserId, IsStructureSender, IsStructureReceiver, (short)RoleId, PrivacyLevel, AllowEditSigned, StructureId, StructureIds, file, language: Language);
                    ManageTransfer.EndExporting(model.Id.Value, model.TransferId, result.Export);
                    if (result.Export)
                    {
                        ManageTransfer.UpdateExportedDate(transfer, true, DateTime.Now);
                        var CompleteResult = ManageTransfer.CompleteATransfer(UserId, transfer, StructureIds, IsStructureReceiver, PrivacyLevel,null,true,true,Language);
                        if (!CompleteResult.Any(s => s.Updated == false))
                        {
                            foreach (var id in result.NewIncomingDocumentIds)
                            {
                                _hub.Send(id, Language);
                            }
                        }
                        else
                        {
                            if (result.NewIncomingDocumentIds.Any())
                            {
                                var documentsIds = new List<long>();
                                documentsIds.AddRange(result.NewIncomingDocumentIds);
                                await ManageDocument.Delete(UserId, StructureIds[0], documentsIds, true);
                            }
                            return Ok(CompleteResult.FirstOrDefault(s => s.Updated == false).Message);

                        }
                        return Ok(result.Message);

                    }
                    else
                    {
                        if (result.Message == "")
                        {
                            return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized);
                        }
                        else
                        {
                            return Ok(result.Message);
                        }
                    }
                }
                else
                {
                    ManageTransfer.EndExporting(model.Id.Value, model.TransferId, false);
                    return Ok(validation.model.Message);
                }
            }
            catch (Exception ex)
            {
                ManageTransfer.EndExporting(model.Id.Value, model.TransferId, false);
                return HttpAjaxError(ex, 1);
            }
        }

        [HttpPost]
        public async Task<IActionResult> ResendRejectedDocument(long incomingDocumentId, DocumentViewModel model)
        {
                (bool Export, string Message, List<long> NewIncomingDocumentIds) result=(false, "", new List<long>());
            try
            {
                var transfer = ManageTransfer.FindIncludeDocumentAndCategory(model.TransferId.Value);
                ManageTransfer.StartExporting(model.Id.Value, model.TransferId);
                var validation = ManageTransfer.CheckCanExportOrComplete(UserId, transfer, StructureIds, IsStructureReceiver, PrivacyLevel, true, true);
                if (validation.Valid)
                {
                    var file = Request.Form.Files.FirstOrDefault(t => t.Name == "OriginalMail[0]");
                    result = await ManageDocument.ExportOutgoing(model, UserId, IsStructureSender, IsStructureReceiver, (short)RoleId, PrivacyLevel, AllowEditSigned, StructureId, StructureIds, file, Language);
                    ManageTransfer.EndExporting(model.Id.Value, model.TransferId, result.Export);
                    if (result.Export)
                    {
                        var documentsIds = new List<long>() { incomingDocumentId };
                        var deleteResult = await ManageDocument.Delete(UserId, StructureIds[0], documentsIds, true);

                        if (deleteResult.Any(s => !s.IsNull()))
                        {
                            throw new Exception();
                        }
                        else
                        {
                            var CompleteResult = ManageTransfer.CompleteATransfer(UserId, transfer, StructureIds, IsStructureReceiver, PrivacyLevel, null, true, true, Language);
                        }
                        return Ok(result.Message);

                    }
                    else
                    {
                        if (result.Message == "")
                        {
                            return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized);
                        }
                        else
                        {
                            return Ok(result.Message);
                        }
                    }
                }
                else
                {
                    ManageTransfer.EndExporting(model.Id.Value, model.TransferId,false);
                    return Ok(validation.model.Message);
                }
            }
            catch (Exception ex)
            {
                ManageTransfer.EndExporting(model.Id.Value, model.TransferId, false);
                if (result.NewIncomingDocumentIds.Any())
                {
                    var documentsIds = new List<long>();
                    documentsIds.AddRange(result.NewIncomingDocumentIds);
                    await ManageDocument.Delete(UserId, StructureIds[0], documentsIds, true);
                }
                return HttpAjaxError(ex, 1);
            }
        }
        /// <summary>
        /// Get original mail id by document id
        /// </summary>
        /// <param name="id">Document id</param>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        public IActionResult DownloadOriginalMail(long id)
        {
            try
            {
                return Ok(ManageDocument.GetOriginalMailId(id));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        #endregion

        /// <summary>
        /// Get a draft document
        /// </summary>
        /// <param name="documentId">Document id</param>
        /// <returns></returns>
        [HttpPost]
        [Produces("application/json")]
        public async Task<IActionResult> CancelTask(long documentId, bool overrideAccess = false)
        {
            try
            {
                bool result = await ManageDocument.CancelTask(documentId, UserId, overrideAccess);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, documentId);
            }
        }

        /// <summary>
        /// Get document from/to date
        /// </summary>
        /// <param name="id">Document id</param>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        public IActionResult GetDocumentFromToDate(long id)
        {
            try
            {
                (string followUpFromDate, string followUpToDate) retValue = ManageDocument.GetDocumentFromToDate(id, UserId);
                return Ok(new { retValue.followUpFromDate, retValue.followUpToDate });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// This function for re-open follow-up that closed
        /// </summary>
        /// <param name="documentIds"></param>
        /// <returns></returns>
        [HttpPost]
        [Produces("application/json")]
        public IActionResult Reopen(List<long> documentIds)
        {
            bool result = ManageDocument.Reopen(documentIds);
            return Ok(result);
        }

        [HttpPost]
        [Produces("application/json")]
        public IActionResult ChangeDraftStatus(long documentId, int draftStatus)
        {
            bool result = ManageDocument.ChangeDraftStatus(documentId, draftStatus, UserId, StructureId, PrivacyLevel);
            if (result)
                return Ok();
            else
                return BadRequest();
        }

        [HttpGet]
        public IActionResult GenerateReferenceNumber(long documentId, long? transferId)
        {
            try
            {
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                var retValue = ManageDocument.GenerateReferenceNumber(UserId, documentId, transferId, Language);

                if (!retValue.result)
                {
                    return Ok(null);
                }
                else
                {
                    return Ok(retValue.document);
                }
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        [HttpGet]
        public IActionResult CheckDocumentAttachmentLocked(long documentId)
        {
            try
            {
                var retValue = ManageDocument.CheckHasLockedAttachmentsWithOriginal(documentId);
                return Ok(retValue);

            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        public IActionResult CheckDocumentHasAttachment(long documentId)
        {
            try
            {
                var retValue = ManageDocument.CheckDocumentHasAttachments(documentId);
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }




    }
}
