using Intalio.Core;
using Intalio.CTS.Core.Model;
using Microsoft.EntityFrameworkCore;
using System.Reflection.Metadata;

namespace Intalio.CTS.Core.DAL
{
    public partial class CTSContext : DbContext
    {
        #region Ctor

        public CTSContext()
        {
        }

        public CTSContext(DbContextOptions<CTSContext> options)
            : base(options)
        {
        }

        #endregion

        #region Properties

        public virtual DbSet<ActivityLog> ActivityLog { get; set; }
        public virtual DbSet<ActivityLogAction> ActivityLogAction { get; set; }
        public virtual DbSet<AdvanceSearchConfiguration> AdvanceSearchConfiguration { get; set; }
        public virtual DbSet<AttachmentFolder> AttachmentFolder { get; set; }
        public virtual DbSet<BarcodeConfiguration> BarcodeConfiguration { get; set; }
        public virtual DbSet<Category> Category { get; set; }
        public virtual DbSet<CategoryReferenceCounter> CategoryReferenceCounter { get; set; }
        public virtual DbSet<CategoryReferenceNumber> CategoryReferenceNumber { get; set; }
        public virtual DbSet<CategorySecurity> CategorySecurity { get; set; }
        public virtual DbSet<CategorySearchSecurity> CategorySearchSecurity { get; set; }
        public virtual DbSet<Classification> Classification { get; set; }
        public virtual DbSet<Document> Document { get; set; }
        public virtual DbSet<DocumentCarbonCopy> DocumentCarbonCopy { get; set; }
        public virtual DbSet<DocumentForm> DocumentForm { get; set; }
        public virtual DbSet<AttachmentForm> AttachmentForm { get; set; }
        public virtual DbSet<DocumentReceiverEntity> DocumentReceiverEntity { get; set; }
        public virtual DbSet<Importance> Importance { get; set; }
        public virtual DbSet<LinkedDocument> LinkedDocument { get; set; }
        public virtual DbSet<FilingPlan> FilingPlan { get; set; }
        public virtual DbSet<FilingPlanKeyword> FilingPlanKeyword { get; set; }
        public virtual DbSet<Priority> Priority { get; set; }
        public virtual DbSet<Privacy> Privacy { get; set; }
        public virtual DbSet<Purpose> Purpose { get; set; }
        public virtual DbSet<PurposeSecurity> PurposeSecurity { get; set; }
        public virtual DbSet<ScanConfiguration> ScanConfiguration { get; set; }
        public virtual DbSet<Status> Status { get; set; }
        public virtual DbSet<Template> Template { get; set; }
        public virtual DbSet<TemplateAttachmentData> TemplateAttachmentData { get; set; }
        public virtual DbSet<Transfer> Transfer { get; set; }
        public virtual DbSet<User> User { get; set; }
        public virtual DbSet<Structure> Structure { get; set; }
        public virtual DbSet<DocumentType> DocumentType { get; set; }
        public virtual DbSet<Note> Note { get; set; }
        public virtual DbSet<NonArchivedAttachments> NonArchivedAttachments { get; set; }
        public virtual DbSet<NonArchivedAttachmentsTypes> NonArchivedAttachmentsTypes { get; set; }
        public virtual DbSet<OcrContent> OcrContent { get; set; }
        public virtual DbSet<Folder> Folder { get; set; }
        public virtual DbSet<Attachment> Attachment { get; set; }
        public virtual DbSet<EntityGroup> EntityGroup { get; set; }
        public virtual DbSet<StructureUserGroup> StructureUserGroup { get; set; }
        public virtual DbSet<FavoriteStructure> FavoriteStructure { get; set; }
        public virtual DbSet<Distribution> Distribution { get; set; }
        public virtual DbSet<DistributionStructure> DistributionStructure { get; set; }
        public virtual DbSet<AutoForward> AutoForward { get; set; }
        public virtual DbSet<ExceptionRules> ExceptionRules { get; set; }
        public virtual DbSet<Delegation> Delegation { get; set; }
        public virtual DbSet<DeliveryNoteTemplate> DeliveryNoteTemplate { get; set; }
        public virtual DbSet<DeliveryNoteTemplateData> DeliveryNoteTemplateData { get; set; }
        public virtual DbSet<AttachmentSecurity> AttachmentSecurity { get; set; }
        public virtual DbSet<SearchAssignedSecurity> SearchAssignedSecurity { get; set; }
        public virtual DbSet<DashboardAssignedStructures> DashboardAssignedStructures { get; set; }
        public virtual DbSet<SendingRule> SendingRule { get; set; }
        public virtual DbSet<SignatureRegion> SignatureRegion { get; set; }
        public virtual DbSet<AttachmentSignUser> AttachmentSignUser { get; set; }
        public virtual DbSet<Workflow> Workflow { set; get; }
        public virtual DbSet<WorkflowStep> WorkflowStep { get; set; }
        public virtual DbSet<UserStructure> userStructure { get; set; }
        public virtual DbSet<TransfersMigrateHistory> TransfersMigrateHistory { get; set; }
        public virtual DbSet<Basket> Basket { get; set; }
        public virtual DbSet<BasketPermissions> BasketPermissions { get; set; }
        public virtual DbSet<BasketDocument> BasketDocument { get; set; }
        public virtual DbSet<Committee> Committee { get; set; }
        public virtual DbSet<DesignatedPerson> DesignatedPerson { get; set; }
        public virtual DbSet<CategoryReferenceNumberType> CategoryReferenceNumberType { get; set; }
        public virtual DbSet<Assignee> Assignee { get; set; }
        public virtual DbSet<FollowUpSecurity> FollowUpSecurity { get; set; }
        public virtual DbSet<UserParameter> userParameter { get; set; }
        public virtual DbSet<StructureSignature> StructureSignature { get; set; }
        public virtual DbSet<FollowUpStatus> FollowUpStatus { get; set; }
        public virtual DbSet<FollowUpUsers> FollowUpUsers { get; set; }
        public virtual DbSet<FollowUp> FollowUp { get; set; }
        public virtual DbSet<Team> Team{ get; set; }
        public virtual DbSet<TeamUsers> TeamUsers { get; set; }
        public virtual DbSet<Event> Event { get; set; }
        public virtual DbSet<Instruction> Instruction { get; set; }

        public virtual DbSet<DocumentLock> DocumentLock { get; set; }
        public virtual DbSet<FollowUpPanel> FollowUpPanel { get; set; }

        #endregion

        #region Protected Methods

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                var dbConnectionString = Configuration.DbConnectionString;
                switch (Configuration.DatabaseType)
                {
                    case DatabaseType.MSSQL:
                        optionsBuilder.UseSqlServer(dbConnectionString);
                        break;
                    case DatabaseType.PostgreSQL:
                        optionsBuilder.UseNpgsql(dbConnectionString);
                        break;
                    case DatabaseType.Oracle:
                        optionsBuilder.UseOracle(dbConnectionString);
                        break;
                    default:
                        optionsBuilder.UseSqlServer(dbConnectionString);
                        break;
                }
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<G2GDocumentModel>().HasNoKey();
            modelBuilder.Entity<ActivityLog>(entity =>
            {
                entity.Property(e => e.Note).HasMaxLength(550);
                entity.HasOne(d => d.ActivityLogAction)
                    .WithMany(p => p.ActivityLog)
                    .HasForeignKey(d => d.ActivityLogActionId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ActivityLog_ActivityLogAction");
                //change here   ==> cascade
                entity.HasOne(d => d.Document)
                    .WithMany(p => p.ActivityLog)
                    .HasForeignKey(d => d.DocumentId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_ActivityLog_Document");

                entity.HasOne(d => d.Transfer)
                    .WithMany(p => p.ActivityLog)
                    .HasForeignKey(d => d.TransferId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_ActivityLog_Transfer");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.ActivityLog)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ActivityLog_User");

              
            });

            modelBuilder.Entity<ActivityLogAction>(entity =>
            {
                entity.Property(e => e.Description).HasMaxLength(250);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<CategoryReferenceCounter>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(150);
            });

            modelBuilder.Entity<CategoryReferenceNumber>(entity =>
            {
                entity.Property(e => e.Content).HasMaxLength(150);

                entity.Property(e => e.Type).HasMaxLength(150);

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.CategoryReferenceNumber)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_CategoryReferenceNumber_Category");
            });

            modelBuilder.Entity<Classification>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.NameAr).HasMaxLength(50);

                entity.Property(e => e.NameFr).HasMaxLength(50);
            });

            modelBuilder.Entity<AttachmentFolder>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.AttachmentFolder)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_AttachmentFolder_Category");

                entity.HasOne(d => d.Parent)
                    .WithMany(p => p.InverseParent)
                    .HasForeignKey(d => d.ParentId)
                    .HasConstraintName("FK_AttachmentFolder_AttachmentFolder");
            });

            modelBuilder.Entity<BarcodeConfiguration>(entity =>
            {
                entity.Property(e => e.CaptionAboveAlign).HasMaxLength(8);

                entity.Property(e => e.CaptionAboveColor).HasMaxLength(7);

                entity.Property(e => e.CaptionAboveFontFamily).HasMaxLength(50);

                entity.Property(e => e.CaptionAboveText).HasMaxLength(50);

                entity.Property(e => e.CaptionBelowAlign).HasMaxLength(8);

                entity.Property(e => e.CaptionBelowColor).HasMaxLength(7);

                entity.Property(e => e.CaptionBelowFontFamily).HasMaxLength(50);

                entity.Property(e => e.CaptionBelowText).HasMaxLength(50);

                entity.Property(e => e.CodeTextAlign).HasMaxLength(8);

                entity.Property(e => e.CodeTextColor).HasMaxLength(7);

                entity.Property(e => e.CodeTextFontFamily).HasMaxLength(50);

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.BarcodeConfiguration)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_BarcodeConfiguration_Category");
            });

            modelBuilder.Entity<Category>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.NameAr).HasMaxLength(150);

                entity.Property(e => e.NameFr).HasMaxLength(150);

                entity.Property(e => e.Grouping).HasMaxLength(150);

                entity.Property(e => e.AllowedExtensionsByFile).HasMaxLength(150);
            });

            modelBuilder.Entity<CategorySecurity>(entity =>
            {
                entity.HasOne(d => d.Category)
                    .WithMany(p => p.CategorySecurity)
                    .HasForeignKey(d => d.CategoryId)
                    .HasConstraintName("FK_CategorySecurity_Category");
            });

            modelBuilder.Entity<PurposeSecurity>(entity =>
            {
                entity.HasOne(d => d.Purpose)
                    .WithMany(p => p.PurposeSecurity)
                    .HasForeignKey(d => d.PurposeId)
                    .HasConstraintName("FK_PurposeSecurity_Purpose");
            });

            modelBuilder.Entity<TeamUsers>(entity =>
            {
                entity.HasKey(tu => new { tu.UserId, tu.TeamId });

                entity.HasOne(tu => tu.User)
                      .WithMany(u => u.TeamUsers)
                      .HasForeignKey(tu => tu.UserId)
                      .OnDelete(DeleteBehavior.Cascade)
                      .HasConstraintName("FK_TeamUser_User");

                entity.HasOne(tu => tu.CreatedByUser)
                    .WithMany()
                    .HasForeignKey(tu => tu.CreatedByUserId);
             

                entity.HasOne(tu => tu.Team)
                      .WithMany(t => t.TeamUsers)
                      .HasForeignKey(tu => tu.TeamId)
                      .OnDelete(DeleteBehavior.Cascade)
                      .HasConstraintName("FK_TeamUser_Team");

                entity.HasOne(tu => tu.Structure)
                      .WithMany(s => s.TeamUsers)
                      .HasForeignKey(tu => tu.StructureId)
                      .OnDelete(DeleteBehavior.Cascade)
                      .HasConstraintName("FK_TeamUser_Structure");
            });
            modelBuilder.Entity<Event>(entity =>
            {
                entity.HasOne(d => d.Document)
                   .WithMany(p => p.Event)
                   .HasForeignKey(d => d.DocumentId)
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("FK_Event_Document");
                entity.HasOne(d => d.CreatedByUser)
                  .WithMany(p => p.CreatedEvents)
                  .HasForeignKey(d => d.CreatedByUserId)
                  .OnDelete(DeleteBehavior.Cascade)
                  .HasConstraintName("FK_Event_User");
                entity.HasOne(d => d.Transfer)
                   .WithMany(p => p.Event)
                   .HasForeignKey(d => d.TransferId)
                   .OnDelete(DeleteBehavior.SetNull)
                   .HasConstraintName("FK_Event_Transfer");
            });
            modelBuilder.Entity<Instruction>(entity =>
            {

                entity.HasOne(d => d.User)
                    .WithMany(p => p.Instruction)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_Instruction_User");


                entity.HasOne(d => d.CreatedByUser)
                    .WithMany(p => p.CreatedInstructions)
                    .HasForeignKey(d => d.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_Instruction_CreatedByUser");


                entity.HasOne(d => d.Document)
                    .WithMany(p => p.Instruction)
                    .HasForeignKey(d => d.DocumentId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_Instruction_Document");

                entity.HasOne(d => d.Structure)
                .WithOne(p => p.Instruction) 
                .HasForeignKey<Instruction>(d => d.StructureId)  
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_Instruction_Structure");

                entity.HasOne(d => d.ManagerStructure)
                    .WithOne(p => p.ManagerInstruction)  
                    .HasForeignKey<Instruction>(d => d.ManagerStructureId)  
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_Instruction_ManagerStructure");

            });

            modelBuilder.Entity<Document>(entity =>
            {
                entity.Property(e => e.ReferenceNumber).HasMaxLength(80);

                entity.Property(e => e.Subject)
                    .HasMaxLength(350);

                entity.Property(e => e.DueDate).HasColumnType("date");

                entity.Property(e => e.ExternalReferenceNumber).HasMaxLength(80);

                entity.HasOne(d => d.AttachmentNavigation)
                    .WithMany(p => p.DocumentNavigation)
                    .HasForeignKey(d => d.AttachmentId)
                    .OnDelete(DeleteBehavior.NoAction)
                    .HasConstraintName("FK_Document_Attachment");

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.Document)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Document_Category");

                entity.HasOne(d => d.Classification)
                    .WithMany(p => p.Document)
                    .HasForeignKey(d => d.ClassificationId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Document_Classification");

                entity.HasOne(d => d.CreatedByUser)
                    .WithMany(p => p.Document)
                    .HasForeignKey(d => d.CreatedByUserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Document_User");

                entity.HasOne(d => d.DocumentType)
                    .WithMany(p => p.Document)
                    .HasForeignKey(d => d.DocumentTypeId)
                    .HasConstraintName("FK_Document_DocumentType");

                entity.HasOne(d => d.Importance)
                    .WithMany(p => p.Document)
                    .HasForeignKey(d => d.ImportanceId)
                    .HasConstraintName("FK_Document_Importance");

                entity.HasOne(d => d.Priority)
                    .WithMany(p => p.Document)
                    .HasForeignKey(d => d.PriorityId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Document_Priority");

                entity.HasOne(d => d.Privacy)
                    .WithMany(p => p.Document)
                    .HasForeignKey(d => d.PrivacyId)
                    .HasConstraintName("FK_Document_Privacy");

                entity.HasOne(d => d.SendingEntity)
                    .WithMany(p => p.DocumentSendingEntity)
                    .HasForeignKey(d => d.SendingEntityId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Document_Structure");

                entity.HasOne(d => d.Status)
                    .WithMany(p => p.Document)
                    .HasForeignKey(d => d.StatusId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Document_Status");

                entity.HasOne(d => d.CreatedByStructure)
                    .WithMany(p => p.DocumentCreatedByStructure)
                    .HasForeignKey(d => d.CreatedByStructureId)
                    .OnDelete(DeleteBehavior.NoAction)
                    .HasConstraintName("FK_Document_Structure1");

                entity.HasOne(o => o.DocumentForm).WithOne().HasForeignKey<DocumentForm>(o => o.Id);
            });

            modelBuilder.Entity<DocumentCarbonCopy>(entity =>
            {
                entity.HasOne(d => d.Document)
                    .WithMany(p => p.DocumentCarbonCopy)
                    .HasForeignKey(d => d.DocumentId)
                    .HasConstraintName("FK_DocumentCarbonCopy_Document");
               

                entity.HasOne(d => d.Structure)
                    .WithMany(p => p.DocumentCarbonCopy)
                    .HasForeignKey(d => d.StructureId)
                    .OnDelete(DeleteBehavior.NoAction)
                    .HasConstraintName("FK_DocumentCarbonCopy_Structure");
            });

            modelBuilder.Entity<DocumentForm>(entity =>
            {
                entity.ToTable("Document");
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Body)
                   .HasMaxLength(2000);

                entity.Property(e => e.Keyword)
                   .HasMaxLength(750);
            });

            modelBuilder.Entity<LinkedDocument>(entity =>
            {
                entity.HasOne(d => d.Document)
                    .WithMany(p => p.LinkedDocumentDocument)
                    .HasForeignKey(d => d.DocumentId)
                    .HasConstraintName("FK_LinkedDocument_Document");

                entity.HasOne(d => d.LinkedDocumentNavigation)
                    .WithMany(p => p.LinkedDocumentLinkedDocumentNavigation)
                    .HasForeignKey(d => d.LinkedDocumentId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_LinkedDocument_Document_Document");

                entity.HasOne(d => d.Transfer)
                   .WithMany(p => p.LinkedDocument)
                   .HasForeignKey(d => d.TransferId)
                   .HasConstraintName("FK_LinkedDocument_Transfer");
            });

            modelBuilder.Entity<DocumentReceiverEntity>(entity =>
            {
                entity.HasOne(d => d.Document)
                    .WithMany(p => p.DocumentReceiverEntity)
                    .HasForeignKey(d => d.DocumentId)
                    .HasConstraintName("FK_DocumentReceiverEntity_Document");

                entity.HasOne(d => d.EntityGroup)
                  .WithMany(p => p.DocumentReceiverEntities)
                  .HasForeignKey(d => d.EntityGroupId)
                  .HasConstraintName("FK_DocumentReceiverEntity_EntityGroup");

                entity.HasOne(d => d.Structure)
                    .WithMany(p => p.DocumentReceiverEntity)
                    .HasForeignKey(d => d.StructureId)
                    .OnDelete(DeleteBehavior.NoAction)
                    .HasConstraintName("FK_DocumentReceiverEntity_Structure");
            });

            modelBuilder.Entity<Importance>(entity =>
            {
                entity.Property(e => e.IconColor).HasMaxLength(7);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.NameAr).HasMaxLength(50);

                entity.Property(e => e.NameFr).HasMaxLength(50);
            });

            modelBuilder.Entity<Priority>(entity =>
            {
                entity.Property(e => e.Color)
                    .IsRequired()
                    .HasMaxLength(7);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.NameAr).HasMaxLength(50);

                entity.Property(e => e.NameFr).HasMaxLength(50);
            });

            modelBuilder.Entity<Privacy>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.NameAr).HasMaxLength(50);

                entity.Property(e => e.NameFr).HasMaxLength(50);
            });

            modelBuilder.Entity<Purpose>(entity =>
            {
                entity.Property(e => e.Cced).HasColumnName("CCed");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.NameAr).HasMaxLength(50);

                entity.Property(e => e.NameFr).HasMaxLength(50);
            });

            modelBuilder.Entity<ScanConfiguration>(entity =>
            {
                entity.Property(e => e.AllowedColors).HasMaxLength(50);

                entity.Property(e => e.AllowedResolutions).HasMaxLength(50);

                entity.Property(e => e.Color)
                    .IsRequired()
                    .HasMaxLength(7);

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.ScanConfiguration)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_ScanConfiguration_ScanConfiguration");
            });

            modelBuilder.Entity<Status>(entity =>
            {
                entity.Property(e => e.Color).HasMaxLength(7);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.NameAr).HasMaxLength(50);

                entity.Property(e => e.NameFr).HasMaxLength(50);
            });

            modelBuilder.Entity<Template>(entity =>
            {
                entity.Property(e => e.ItemContentType).HasMaxLength(500);

                entity.Property(e => e.ItemName).HasMaxLength(150);

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.Template)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_Template_Category");

                entity.HasOne(d => d.ItemParent)
                    .WithMany(p => p.InverseItemParent)
                    .HasForeignKey(d => d.ItemParentId)
                    .HasConstraintName("FK_Template_Template");

                entity.HasOne(d => d.TemplateAttachmentData)
                    .WithMany(p => p.Template)
                    .HasForeignKey(d => d.TemplateAttachmentDataId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_Template_TemplateAttachmentData");







            });

            modelBuilder.Entity<TemplateAttachmentData>(entity =>
            {
                entity.Property(e => e.Data).IsRequired();
            });

            modelBuilder.Entity<Transfer>(entity =>
            {
                entity.Property(e => e.Cced).HasColumnName("CCed");

                entity.Property(e => e.DueDate).HasColumnType("date");

                entity.Property(e => e.Instruction).HasMaxLength(1500);

                entity.HasOne(d => d.Document)
                    .WithMany(p => p.Transfer)
                    .HasForeignKey(d => d.DocumentId)
                    .HasConstraintName("FK_Transfer_Document");

                entity.HasOne(d => d.FromStructure)
                    .WithMany(p => p.TransferFromStructure)
                    .HasForeignKey(d => d.FromStructureId)
                    .HasConstraintName("FK_Transfer_Structure");

                entity.HasOne(d => d.FromUser)
                    .WithMany(p => p.TransferFromUser)
                    .HasForeignKey(d => d.FromUserId)
                    .HasConstraintName("FK_Transfer_User2");

                entity.HasOne(d => d.Purpose)
                    .WithMany(p => p.Transfer)
                    .HasForeignKey(d => d.PurposeId)
                    .HasConstraintName("FK_Transfer_Purpose");

                entity.HasOne(d => d.Priority)
                    .WithMany(p => p.Transfer)
                    .HasForeignKey(d => d.PriorityId)
                    .HasConstraintName("FK_Transfer_Priority");

                entity.HasOne(d => d.Status)
                    .WithMany(p => p.Transfer)
                    .HasForeignKey(d => d.StatusId)
                    .HasConstraintName("FK_Transfer_Status");

                entity.HasOne(d => d.OwnerDelegatedUser)
                    .WithMany(p => p.TransferOwnerDelegatedUser)
                    .HasForeignKey(d => d.OwnerDelegatedUserId)
                    .HasConstraintName("FK_Transfer_User1");

                entity.HasOne(d => d.OwnerUser)
                    .WithMany(p => p.TransferOwnerUser)
                    .HasForeignKey(d => d.OwnerUserId)
                    .HasConstraintName("FK_Transfer_User");

                entity.HasOne(d => d.ToStructure)
                    .WithMany(p => p.TransferToStructure)
                    .HasForeignKey(d => d.ToStructureId)
                    .HasConstraintName("FK_Transfer_Structure1");

                entity.HasOne(d => d.ToUser)
                    .WithMany(p => p.TransferToUser)
                    .HasForeignKey(d => d.ToUserId)
                    .HasConstraintName("FK_Transfer_User3");

                entity.HasOne(d => d.Workflow)
                    .WithOne(p => p.Transfer)
                    .HasForeignKey<Workflow>(d => d.TransferId)
                    .HasConstraintName("FK_Transfer_Workflow");

                entity.HasOne(e => e.WorkflowStep)
                .WithMany(e => e.Transfers)
                .HasForeignKey(e => e.WorkflowStepId)
                .HasConstraintName("FK_Transfer_WorkflowStep");

            });

            modelBuilder.Entity<User>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.Firstname)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Lastname)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Configuration).HasMaxLength(750);
            });

            modelBuilder.Entity<Structure>(entity =>
            {
                entity.Property(e => e.Id).ValueGeneratedNever();

                entity.Property(e => e.Code).HasMaxLength(50);

                entity.Property(e => e.Name).IsRequired().HasMaxLength(450);

                entity.Property(e => e.NameAr).HasMaxLength(450);

                entity.Property(e => e.NameFr).HasMaxLength(450);
            });

            modelBuilder.Entity<DocumentType>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.NameAr).HasMaxLength(150);

                entity.Property(e => e.NameFr).HasMaxLength(150);
            });

            modelBuilder.Entity<Note>(entity =>
            {
                entity.Property(e => e.Notes)
                    .IsRequired()
                    .HasMaxLength(2000);

                entity.HasOne(d => d.CreatedByUser)
                    .WithMany(p => p.NoteCreatedByUser)
                    .HasForeignKey(d => d.CreatedByUserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Note_Users");

                entity.HasOne(d => d.Document)
                   .WithMany(p => p.Note)
                   .HasForeignKey(d => d.DocumentId)
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("FK_Note_Document");

                entity.HasOne(d => d.CreatedByDelegatedUser)
                    .WithMany(p => p.NoteCreatedByDelegatedUser)
                    .HasForeignKey(d => d.CreatedByDelegatedUserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Note_Delegation_User");

                entity.HasOne(d => d.Transfer)
                   .WithMany(p => p.Note)
                   .HasForeignKey(d => d.TransferId)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_Note_Transfer");
            });

            modelBuilder.Entity<NonArchivedAttachments>(entity =>
            {
                entity.Property(e => e.Description)
                    .HasMaxLength(350);

                entity.HasOne(d => d.CreatedByUser)
                    .WithMany(p => p.NonArchivedAttachmentsUser)
                    .HasForeignKey(d => d.CreatedByUserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_NonArchivedAttachments_User");

                entity.HasOne(d => d.Document)
                   .WithMany(p => p.NonArchivedAttachments)
                   .HasForeignKey(d => d.DocumentId)
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("FK_NonArchivedAttachments_Document");

                entity.HasOne(d => d.CreatedByDelegatedUser)
                    .WithMany(p => p.NonArchivedAttachmentsDelegatedUser)
                    .HasForeignKey(d => d.CreatedByDelegatedUserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_NonArchivedAttachments_DelegatedUser");


                entity.HasOne(d => d.Type)
                   .WithMany(p => p.NonArchivedAttachments_NonArchivedAttachmentsTypes)
                   .HasForeignKey(d => d.TypeId)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_NonArchivedAttachments_NonArchivedAttachmentsTypes");

                entity.HasOne(d => d.Transfer)
                   .WithMany(p => p.NonArchivedAttachments)
                   .HasForeignKey(d => d.TransferId)
                   .OnDelete(DeleteBehavior.ClientSetNull)
                   .HasConstraintName("FK_NonArchivedAttachments_Transfer");
            });

            modelBuilder.Entity<NonArchivedAttachmentsTypes>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(150);

                entity.Property(e => e.NameAr).HasMaxLength(150);

                entity.Property(e => e.NameFr).HasMaxLength(150);
            });

            modelBuilder.Entity<OcrContent>(entity =>
            {
                entity.HasOne(d => d.Document)
                    .WithMany(p => p.OcrContent)
                    .HasForeignKey(d => d.RecordId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_OcrContent_Document");
            });

            modelBuilder.Entity<FilingPlan>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(150);

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.FilingPlan)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_FilingPlan_Category");

                entity.HasOne(d => d.FilingPlanKeyword)
                    .WithMany(p => p.FilingPlan)
                    .HasForeignKey(d => d.FilingPlanKeywordId)
                    .HasConstraintName("FK_FilingPlan_FilingPlanKeyword");

                entity.HasOne(d => d.Parent)
                    .WithMany(p => p.InverseParent)
                    .HasForeignKey(d => d.ParentId)
                    .HasConstraintName("FK_FilingPlan_FilingPlan");
            });

            modelBuilder.Entity<FilingPlanKeyword>(entity =>
            {
                entity.Property(e => e.Name).HasMaxLength(150);
            });

            modelBuilder.Entity<Folder>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.PhysicalName)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.HasOne(d => d.Document)
                  .WithMany(p => p.Folder)
                  .HasForeignKey(d => d.DocumentId)
                  .OnDelete(DeleteBehavior.Cascade)
                  .HasConstraintName("FK_Folder_Document");

                entity.HasOne(d => d.Transfer)
                  .WithMany(p => p.Folder)
                  .HasForeignKey(d => d.TransferId)
                  .HasConstraintName("FK_Folder_Transfer");

                entity.HasOne(d => d.Parent)
                    .WithMany(p => p.Children)
                    .HasForeignKey(d => d.ParentId)
                    .HasConstraintName("FK_Folder_Folder");

                entity.HasOne(d => d.CreatedByUser)
                    .WithMany(p => p.FolderCreatedByUser)
                    .HasForeignKey(d => d.CreatedByUserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Folder_Users");

                entity.HasOne(d => d.CreatedByDelegatedUser)
                    .WithMany(p => p.FolderCreatedByDelegatedUser)
                    .HasForeignKey(d => d.CreatedByDelegatedUserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Folder_Delegation_User");
            });

            modelBuilder.Entity<Attachment>(entity =>
            {
                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(150);

                entity.Property(e => e.Extension)
                   .IsRequired()
                   .HasMaxLength(50);

                entity.Property(e => e.Status).HasDefaultValueSql("((1))");

                entity.HasOne(d => d.Document)
                 .WithMany(p => p.Attachment)
                 .HasForeignKey(d => d.DocumentId)
                 .OnDelete(DeleteBehavior.Cascade)
                 .HasConstraintName("FK_Attachment_Document");

                entity.HasOne(d => d.Transfer)
                  .WithMany(p => p.Attachment)
                  .HasForeignKey(d => d.TransferId)
                  .HasConstraintName("FK_Attachment_Transfer");

                entity.HasOne(d => d.Folder)
                    .WithMany(p => p.Attachment)
                    .HasForeignKey(d => d.FolderId)
                    .HasConstraintName("FK_Attachment_Folder");

                entity.HasOne(d => d.CreatedByUser)
                    .WithMany(p => p.AttachmentCreatedByUser)
                    .HasForeignKey(d => d.CreatedByUserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Attachment_Users");

                entity.HasOne(d => d.CreatedByDelegatedUser)
                    .WithMany(p => p.AttachmentCreatedByDelegatedUser)
                    .HasForeignKey(d => d.CreatedByDelegatedUserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Attachment_Delegation_User");
                entity.HasOne(o => o.AttachmentForm).WithOne().HasForeignKey<AttachmentForm>(o => o.Id);

            });

            modelBuilder.Entity<AttachmentForm>(entity =>
            {
                entity.ToTable("Attachment");
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Body)
                   .HasMaxLength(2000);

                entity.Property(e => e.Keyword)
                   .HasMaxLength(750);
            });

            modelBuilder.Entity<EntityGroup>(entity =>
            {
                entity.ToTable("EntityGroup");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasMaxLength(450);

                entity.Property(e => e.NameAr).HasMaxLength(450);

                entity.Property(e => e.NameFr).HasMaxLength(450);

                entity.Property(e => e.Type).HasDefaultValueSql("((1))");
            });

            modelBuilder.Entity<StructureUserGroup>(entity =>
            {
                entity.ToTable("StructureUserGroup");

                entity.HasOne(d => d.EntityGroup)
                    .WithMany(p => p._EntityGroup)
                    .HasForeignKey(d => d.EntityGroupId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_StructureUserGroup_Group");

                entity.HasOne(d => d.EntityStructure)
                    .WithMany(p => p.EntityStructure)
                    .HasForeignKey(d => d.StructureId)
                    .HasConstraintName("FK_StructureUserGroup_Structure");

                entity.HasOne(d => d.EntityUser)
                    .WithMany(p => p.UserGroup)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_StructureUserGroup_User");
            });

            modelBuilder.Entity<UserStructure>(entity =>
            {
                entity.HasOne(e => e.Structure)
                .WithMany(e => e.UserStructure)
                .HasForeignKey(e => e.StructureId)
                .HasConstraintName("FK_Structure_UserStructure");

                entity.HasOne(e => e.User)
                .WithMany(e => e.UserStructure)
                .HasForeignKey(e => e.UserId)
                .HasConstraintName("FK_User_UserStructure")
                .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(d => d.Privacy)
                .WithMany(p => p.UserStructure)
                .HasForeignKey(d => d.PrivacyId)
                .HasConstraintName("FK_Privacy_UserStructure");
            });

            modelBuilder.Entity<FavoriteStructure>(entity =>
            {
                entity.ToTable("FavoriteStructure");

            });
            modelBuilder.Entity<Distribution>(entity =>
            {
                entity.ToTable("DistributionList");

            });
            modelBuilder.Entity<DistributionStructure>(entity =>
            {
                entity.ToTable("DistributionListDetails");

            });

            modelBuilder.Entity<DeliveryNoteTemplateData>(entity =>
            {
                entity.HasOne(d => d.DeliveryNoteTemplate)
                    .WithOne(p => p.DeliveryNoteTemplateData)
                    .HasForeignKey<DeliveryNoteTemplate>(d => d.DeliveryNoteTemplateDataId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("FK_DeliveryNote_DeliveryNoteData");
            });

            modelBuilder.Entity<SignatureRegion>(entity =>
            {
                entity.HasOne(e => e.AttachmentSignUser)
                .WithMany(e => e.SignatureRegion)
                .HasForeignKey(e => e.AttachmentSignUserId)
                .HasConstraintName("FK_AttachmentSignUser_SignatureRegion");

            });

            modelBuilder.Entity<AttachmentSignUser>(entity =>
            {
                entity.HasOne(e => e.Attachment)
                .WithMany(e => e.AttachmentSignUser)
                .HasForeignKey(e => e.AttachmentId)
                .HasConstraintName("FK_Attachment_AttachmentSignUser");

                entity.HasOne(e => e.User)
                .WithMany(e => e.AttachmentSignUser)
                .HasForeignKey(e => e.UserId)
                .HasConstraintName("FK_User_AttachmentSignUser");

            });

            modelBuilder.Entity<Workflow>(entity =>
            {
                entity.HasOne(e => e.InitatorUser)
                .WithMany(e => e.Workflow)
                .HasForeignKey(e => e.InitiatorUserId)
                .HasConstraintName("FK_Workflow_User");
                
            });

            modelBuilder.Entity<WorkflowStep>(entity =>
            {
                entity.HasOne(e => e.Workflow)
                .WithMany(e => e.WorkflowStep)
                .HasForeignKey(e => e.WorkflowId)
                .HasConstraintName("FK_Workflow_WorkflowStep");

                entity.HasOne(e => e.User)
                .WithMany(e => e.WorkflowSteps)
                .HasForeignKey(e => e.UserId)
                .HasConstraintName("FK_User_WorkflowStep")
                .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(e => e.Purpose)
                .WithMany(e => e.WorkflowSteps)
                .HasForeignKey(e => e.PurposeId)
                .HasConstraintName("FK_Purpose_WorkflowStep")
                .OnDelete(DeleteBehavior.NoAction);

            });

            modelBuilder.Entity<UserParameter>(entity =>
            {
                entity.Property(e => e.Keyword)
                    .IsRequired().HasMaxLength(50);

                entity.Property(e => e.Content)
                    .HasMaxLength(1000);

                entity.Property(e => e.Description)
                    .HasMaxLength(500);
            });


            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);

        #endregion
    }

    public partial class NpgsqlContext : CTSContext
    {
        public NpgsqlContext()
        {
        }

        public NpgsqlContext(DbContextOptions<NpgsqlContext> options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder options)
        {
            options.UseNpgsql("User ID=postgres;Password=P@$$w0rd;Server=localhost;Port=5432;Database=CTS;Integrated Security=true;Pooling=true;", b => b.MigrationsAssembly("Intalio.CTS.Core"));
        }
    }

    public partial class OracleContext : CTSContext
    {
        public OracleContext()
        {
        }

        public OracleContext(DbContextOptions<OracleContext> options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder options)
        {
            options.UseOracle("User Id=SYS;Password=*****;Data Source=localhost:1521/orcl", b => b.MigrationsAssembly("Intalio.CTS.Core"));
        }
    }
}
