﻿using Aspose.Cells;
using Aspose.Pdf;
using Aspose.Pdf.Text;
using Aspose.Words.Shaping;
using Intalio.Core;
using Intalio.Core.API;
using Intalio.Core.Model;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using iTextSharp.text.pdf;
using iTextSharp.text;
using System.Drawing.Imaging;

namespace Intalio.CTS.Core.API
{
    public static class ManageReport
    {
        #region Public Methods

        /// <summary>
        /// List Completed correspondences.
        /// Must be non draft.
        /// Not all data is retrieved (paging is supported).
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="filter"></param>
        /// <param name="stcFilter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <param name="sortExpression"></param>
        /// <returns></returns>
        public static async Task<(long, List<CorrespondenceReportListViewModel>)> ListCompletedCorrespondences(int startIndex, int pageSize, ExpressionBuilderFilters filter = null,
            ExpressionBuilderFilters stcFilter = null, ExpressionBuilderFilters userFilter = null, List<SortExpression> sortExpression = null,
            Language language = Language.EN)
        {
            using (DAL.Document item = new DAL.Document())
            {
                var filterExp = ExpressionBuilder.GetExpression<DAL.Document>(filter, ExpressionBuilderOperator.And);
                var stcFilterExp = ExpressionBuilder.GetExpression<DAL.Document>(stcFilter, ExpressionBuilderOperator.Or);
                var userFilterExp = ExpressionBuilder.GetExpression<DAL.Document>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetCompletedCorrespondencesCount(filterExp, stcFilterExp, userFilterExp);
                var itemList = await item.ListCompletedCorrespondencesAsync(startIndex, pageSize, filterExp, stcFilterExp, userFilterExp, sortExpression.OrderByExpression<DAL.Document>());
                return (countResult, itemList.Select(d =>
                {
                    var sendingEntity = string.Empty;
                    if (d.SendingEntity != null)
                    {
                        sendingEntity = d.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(d.SendingEntity.NameAr))
                        {
                            sendingEntity = d.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(d.SendingEntity.NameFr))
                        {
                            sendingEntity = d.SendingEntity.NameFr;
                        }
                        if(string.IsNullOrEmpty(sendingEntity))
                        {
                            sendingEntity = d.SendingEntity.Name;

                        }
                    }
                    var isOverdue = false;
                    if (d.DueDate.HasValue)
                    {
                        if (d.ClosedDate.HasValue)
                        {
                            isOverdue = d.ClosedDate.Value.Date > d.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > d.DueDate.Value;
                        }
                    }
                    return new CorrespondenceReportListViewModel
                    {
                        Id = d.Id,
                        Subject = d.Subject,
                        SendingEntity = sendingEntity,
                        ReceivingEntity = d.DocumentReceiverEntity.Count > 0 ? string.Join(Constants.SEPARATOR, d.DocumentReceiverEntity
                        .Select(t =>
                        {
                            var receiverName = string.Empty;
                            if (t.Structure != null)
                            {
                                receiverName = t.Structure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                {
                                    receiverName = t.Structure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                {
                                    receiverName = t.Structure.NameFr;
                                }
                                if (string.IsNullOrEmpty(receiverName))
                                {
                                    receiverName = t.Structure.Name;

                                }
                            }
                            else if (t.EntityGroup != null)
                            {
                                receiverName = t.EntityGroup.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
                                {
                                    receiverName = t.EntityGroup.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
                                {
                                    receiverName = t.EntityGroup.NameFr;
                                }
                                if (string.IsNullOrEmpty(receiverName))
                                {
                                    receiverName = t.EntityGroup.Name;

                                }
                            }
                            return receiverName;
                        }).ToList()) : string.Empty,
                        CategoryId = d.CategoryId,
                        ImportanceId = d.ImportanceId,
                        ReferenceNumber = d.ReferenceNumber,
                        Status = d.StatusId,
                        IsOverDue = isOverdue,
                        PriorityId = d.PriorityId,
                        DueDate = d.DueDate != null ? d.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        CreatedDate = d.CreatedDate.ToString(Constants.DATE_FORMAT),
                        ModifiedDate = d.ModifiedDate != default ? d.ModifiedDate?.ToString(Constants.DATE_FORMAT) : string.Empty,
                        Transfers = d.Transfer.Select(t =>
                        {
                            var fromStructure = string.Empty;
                            if (t.FromStructureId.HasValue)
                            {
                                fromStructure = t.FromStructure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                                {
                                    fromStructure = t.FromStructure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                                {
                                    fromStructure = t.FromStructure.NameFr;
                                }
                                if (string.IsNullOrEmpty(fromStructure))
                                {
                                    fromStructure = t.FromStructure.Name;

                                }
                            }
                            var toStructure = string.Empty;
                            if (t.ToStructureId.HasValue)
                            {
                                toStructure = t.ToStructure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                                {
                                    toStructure = t.ToStructure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                                {
                                    toStructure = t.ToStructure.NameFr;
                                }
                                if (string.IsNullOrEmpty(toStructure))
                                {
                                    toStructure = t.ToStructure.Name;

                                }
                            }
                            var isOverdue = false;
                            if (t.DueDate.HasValue)
                            {
                                if (t.ClosedDate.HasValue)
                                {
                                    isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                                }
                                else
                                {
                                    isOverdue = DateTime.Now.Date > t.DueDate.Value;
                                }
                            }

                            return new TransferReportDetailsListViewModel
                            {
                                Id = t.Id,
                                FromUser = t.FromUserId.HasValue ? (language==Language.EN?
                                    $"{t.FromUser.Firstname} {t.FromUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                                ToUser = t.ToUserId.HasValue ?(language==Language.EN?
                                    $"{t.ToUser.Firstname} {t.ToUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                                FromStructure = fromStructure,
                                ToStructure = toStructure,
                                IsOverDue = isOverdue,
                                TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT)
                            };
                        }).ToList()
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List Completed correspondence.
        /// Must be non draft.
        /// All data is retrieved (paging is not supported). 
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="stcFilter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(long, List<CorrespondenceReportListViewModel>)> ListCompletedCorrespondencesRetrievingAllData(ExpressionBuilderFilters filter = null,
            ExpressionBuilderFilters stcFilter = null, ExpressionBuilderFilters userFilter = null, Language language = Language.EN)
        {
            using (DAL.Document item = new DAL.Document())
            {
                int startIndex = 0, pageSize = -1;
                var filterExp = ExpressionBuilder.GetExpression<DAL.Document>(filter, ExpressionBuilderOperator.And);
                var stcFilterExp = ExpressionBuilder.GetExpression<DAL.Document>(stcFilter, ExpressionBuilderOperator.Or);
                var userFilterExp = ExpressionBuilder.GetExpression<DAL.Document>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetCompletedCorrespondencesCount(filterExp, stcFilterExp, userFilterExp);
                var itemList = await item.ListCompletedCorrespondencesAsync(startIndex, pageSize, filterExp, stcFilterExp, userFilterExp);
                return (countResult, itemList.Select(d =>
                {
                    var sendingEntity = string.Empty;
                    if (d.SendingEntity != null)
                    {
                        sendingEntity = d.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(d.SendingEntity.NameAr))
                        {
                            sendingEntity = d.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(d.SendingEntity.NameFr))
                        {
                            sendingEntity = d.SendingEntity.NameFr;
                        }
                        if (string.IsNullOrEmpty(sendingEntity))
                        {
                            sendingEntity = d.SendingEntity.Name;

                        }
                    }
                    var isOverdue = false;
                    if (d.DueDate.HasValue)
                    {
                        if (d.ClosedDate.HasValue)
                        {
                            isOverdue = d.ClosedDate.Value.Date > d.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > d.DueDate.Value;
                        }
                    }
                    return new CorrespondenceReportListViewModel
                    {
                        Id = d.Id,
                        Subject = d.Subject,
                        SendingEntity = sendingEntity,
                        ReceivingEntity = d.DocumentReceiverEntity.Count > 0 ? string.Join(Constants.SEPARATOR, d.DocumentReceiverEntity
                        .Select(t =>
                        {
                            var receiverName = string.Empty;
                            if (t.Structure != null)
                            {
                                receiverName = t.Structure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                {
                                    receiverName = t.Structure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                {
                                    receiverName = t.Structure.NameFr;
                                }
                                if (string.IsNullOrEmpty(receiverName))
                                {
                                    receiverName = t.Structure.Name;

                                }
                            }
                            else if (t.EntityGroup != null)
                            {
                                receiverName = t.EntityGroup.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
                                {
                                    receiverName = t.EntityGroup.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
                                {
                                    receiverName = t.EntityGroup.NameFr;
                                }
                                if (string.IsNullOrEmpty(receiverName))
                                {
                                    receiverName = t.EntityGroup.NameFr;

                                }
                            }
                            return receiverName;
                        }).ToList()) : string.Empty,
                        CategoryId = d.CategoryId,
                        ImportanceId = d.ImportanceId,
                        ReferenceNumber = d.ReferenceNumber,
                        Status = d.StatusId,
                        IsOverDue = isOverdue,
                        PriorityId = d.PriorityId,
                        DueDate = d.DueDate != null ? d.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        CreatedDate = d.CreatedDate.ToString(Constants.DATE_FORMAT),
                        ModifiedDate = d.ModifiedDate != default ? d.ModifiedDate?.ToString(Constants.DATE_FORMAT) : string.Empty,
                        Transfers = d.Transfer.Select(t =>
                        {
                            var fromStructure = string.Empty;
                            if (t.FromStructureId.HasValue)
                            {
                                fromStructure = t.FromStructure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                                {
                                    fromStructure = t.FromStructure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                                {
                                    fromStructure = t.FromStructure.NameFr;
                                }
                                if (string.IsNullOrEmpty(fromStructure))
                                {
                                    fromStructure = t.FromStructure.Name;

                                }
                            }
                            var toStructure = string.Empty;
                            if (t.ToStructureId.HasValue)
                            {
                                toStructure = t.ToStructure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                                {
                                    toStructure = t.ToStructure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                                {
                                    toStructure = t.ToStructure.NameFr;
                                }
                                if (string.IsNullOrEmpty(toStructure))
                                {
                                    toStructure = t.ToStructure.NameFr;

                                }
                            }
                            var isOverdue = false;
                            if (t.DueDate.HasValue)
                            {
                                if (t.ClosedDate.HasValue)
                                {
                                    isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                                }
                                else
                                {
                                    isOverdue = DateTime.Now.Date > t.DueDate.Value;
                                }
                            }

                            return new TransferReportDetailsListViewModel
                            {
                                Id = t.Id,
                                FromUser = t.FromUserId.HasValue ?(language==Language.EN?
                                $"{t.FromUser.Firstname} {t.FromUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                                ToUser = t.ToUserId.HasValue ? (language==Language.EN? $"{t.ToUser.Firstname} {t.ToUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                                FromStructure = fromStructure,
                                ToStructure = toStructure,
                                IsOverDue = isOverdue,
                                TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT)
                            };
                        }).ToList()
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List InProgress correspondences.
        /// Must be non draft.
        /// Not all data is retrieved (paging is supported).
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="filter"></param>
        /// <param name="stcFilter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <param name="sortExpression"></param>
        /// <returns></returns>
        public static async Task<(long, List<CorrespondenceReportListViewModel>)> ListInProgressCorrespondences(int startIndex, int pageSize, ExpressionBuilderFilters filter = null,
            ExpressionBuilderFilters stcFilter = null, ExpressionBuilderFilters userFilter = null, List<SortExpression> sortExpression = null,
            Language language = Language.EN)
        {
            using (DAL.Document item = new DAL.Document())
            {
                var filterExp = ExpressionBuilder.GetExpression<DAL.Document>(filter, ExpressionBuilderOperator.And);
                var stcFilterExp = ExpressionBuilder.GetExpression<DAL.Document>(stcFilter, ExpressionBuilderOperator.Or);
                var userFilterExp = ExpressionBuilder.GetExpression<DAL.Document>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetInProgressCorrespondencesCount(filterExp, stcFilterExp, userFilterExp);
                var itemList = await item.ListInProgressCorrespondencesAsync(startIndex, pageSize, filterExp, stcFilterExp, userFilterExp, sortExpression.OrderByExpression<DAL.Document>());
                return (countResult, itemList.Select(d =>
                {
                    var sendingEntity = string.Empty;
                    if (d.SendingEntity != null)
                    {
                        sendingEntity = d.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(d.SendingEntity.NameAr))
                        {
                            sendingEntity = d.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(d.SendingEntity.NameFr))
                        {
                            sendingEntity = d.SendingEntity.NameFr;
                        }
                        if (string.IsNullOrEmpty(sendingEntity))
                        {
                            sendingEntity = d.SendingEntity.Name;

                        }
                    }
                    var isOverdue = false;
                    if (d.DueDate.HasValue)
                    {
                        if (d.ClosedDate.HasValue)
                        {
                            isOverdue = d.ClosedDate.Value.Date > d.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > d.DueDate.Value;
                        }
                    }
                    return new CorrespondenceReportListViewModel
                    {
                        Id = d.Id,
                        Subject = d.Subject,
                        SendingEntity = sendingEntity,
                        ReceivingEntity = d.DocumentReceiverEntity.Count > 0 ? string.Join(Constants.SEPARATOR, d.DocumentReceiverEntity
                        .Select(t =>
                        {
                            var receiverName = string.Empty;
                            if (t.Structure != null)
                            {
                                receiverName = t.Structure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                {
                                    receiverName = t.Structure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                {
                                    receiverName = t.Structure.NameFr;
                                }
                                if (string.IsNullOrEmpty(receiverName))
                                {
                                    receiverName = t.Structure.Name;

                                }
                            }
                            else if (t.EntityGroup != null)
                            {
                                receiverName = t.EntityGroup.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
                                {
                                    receiverName = t.EntityGroup.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
                                {
                                    receiverName = t.EntityGroup.NameFr;
                                }
                                if (string.IsNullOrEmpty(receiverName))
                                {
                                    receiverName = t.EntityGroup.Name;

                                }
                            }
                            return receiverName;
                        }).ToList()) : string.Empty,
                        CategoryId = d.CategoryId,
                        ImportanceId = d.ImportanceId,
                        ReferenceNumber = d.ReferenceNumber,
                        Status = d.StatusId,
                        IsOverDue = isOverdue,
                        PriorityId = d.PriorityId,
                        DueDate = d.DueDate != null ? d.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        CreatedDate = d.CreatedDate.ToString(Constants.DATE_FORMAT),
                        ModifiedDate = d.ModifiedDate != default ? d.ModifiedDate?.ToString(Constants.DATE_FORMAT) : string.Empty,
                        Transfers = d.Transfer.Select(t =>
                        {
                            var fromStructure = string.Empty;
                            if (t.FromStructureId.HasValue)
                            {
                                fromStructure = t.FromStructure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                                {
                                    fromStructure = t.FromStructure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                                {
                                    fromStructure = t.FromStructure.NameFr;
                                }
                                if (string.IsNullOrEmpty(fromStructure))
                                {
                                    fromStructure = t.FromStructure.Name;

                                }
                            }
                            var toStructure = string.Empty;
                            if (t.ToStructureId.HasValue)
                            {
                                toStructure = t.ToStructure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                                {
                                    toStructure = t.ToStructure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                                {
                                    toStructure = t.ToStructure.NameFr;
                                }
                                if (string.IsNullOrEmpty(toStructure))
                                {
                                    toStructure = t.ToStructure.Name;

                                }
                            }
                            var isOverdue = false;
                            if (t.DueDate.HasValue)
                            {
                                if (t.ClosedDate.HasValue)
                                {
                                    isOverdue = t.ClosedDate.Value.Date > t.DueDate.Value;
                                }
                                else
                                {
                                    isOverdue = DateTime.Now.Date > t.DueDate.Value;
                                }
                            }
                            return new TransferReportDetailsListViewModel
                            {
                                Id = t.Id,
                                FromUser = 
                                t.FromUserId.HasValue ?(language==Language.EN?
                                $"{t.FromUser.Firstname} {t.FromUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                                ToUser = t.ToUserId.HasValue ? (language==Language.EN?
                                $"{t.ToUser.Firstname} {t.ToUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                                FromStructure = fromStructure,
                                ToStructure = toStructure,
                                IsOverDue = isOverdue,
                                TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT)
                            };
                        }).ToList()
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List InProgress correspondence.
        /// Must be non draft.
        /// All data is retrieved (paging is not supported). 
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="stcFilter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(long, List<CorrespondenceReportListViewModel>)> ListInProgressCorrespondencesRetrievingAllData(ExpressionBuilderFilters filter = null,
            ExpressionBuilderFilters stcFilter = null, ExpressionBuilderFilters userFilter = null, Language language = Language.EN)
        {
            using (DAL.Document item = new DAL.Document())
            {
                int startIndex = 0, pageSize = -1;
                var filterExp = ExpressionBuilder.GetExpression<DAL.Document>(filter, ExpressionBuilderOperator.And);
                var stcFilterExp = ExpressionBuilder.GetExpression<DAL.Document>(stcFilter, ExpressionBuilderOperator.Or);
                var userFilterExp = ExpressionBuilder.GetExpression<DAL.Document>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetInProgressCorrespondencesCount(filterExp, stcFilterExp, userFilterExp);
                var itemList = await item.ListInProgressCorrespondencesAsync(startIndex, pageSize, filterExp, stcFilterExp, userFilterExp);
                return (countResult, itemList.Select(d =>
                {
                    var sendingEntity = string.Empty;
                    if (d.SendingEntity != null)
                    {
                        sendingEntity = d.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(d.SendingEntity.NameAr))
                        {
                            sendingEntity = d.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(d.SendingEntity.NameFr))
                        {
                            sendingEntity = d.SendingEntity.NameFr;
                        }
                        if (string.IsNullOrEmpty(sendingEntity))
                        {
                            sendingEntity = d.SendingEntity.Name;

                        }
                    }
                    var isOverdue = false;
                    if (d.DueDate.HasValue)
                    {
                        if (d.ClosedDate.HasValue)
                        {
                            isOverdue = d.ClosedDate.Value.Date > d.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > d.DueDate.Value;
                        }
                    }
                    return new CorrespondenceReportListViewModel
                    {
                        Id = d.Id,
                        Subject = d.Subject,
                        SendingEntity = sendingEntity,
                        ReceivingEntity = d.DocumentReceiverEntity.Count > 0 ? string.Join(Constants.SEPARATOR, d.DocumentReceiverEntity
                        .Select(t =>
                        {
                            var receiverName = string.Empty;
                            if (t.Structure != null)
                            {
                                receiverName = t.Structure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                {
                                    receiverName = t.Structure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                {
                                    receiverName = t.Structure.NameFr;
                                }
                                if (string.IsNullOrEmpty(receiverName))
                                {
                                    receiverName = t.Structure.Name;

                                }
                            }
                            else if (t.EntityGroup != null)
                            {
                                receiverName = t.EntityGroup.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
                                {
                                    receiverName = t.EntityGroup.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
                                {
                                    receiverName = t.EntityGroup.NameFr;
                                }
                                if (string.IsNullOrEmpty(receiverName))
                                {
                                    receiverName = t.EntityGroup.Name;

                                }
                            }
                            return receiverName;
                        }).ToList()) : string.Empty,
                        CategoryId = d.CategoryId,
                        ImportanceId = d.ImportanceId,
                        ReferenceNumber = d.ReferenceNumber,
                        Status = d.StatusId,
                        IsOverDue = isOverdue,
                        PriorityId = d.PriorityId,
                        DueDate = d.DueDate != null ? d.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        CreatedDate = d.CreatedDate.ToString(Constants.DATE_FORMAT),
                        ModifiedDate = d.ModifiedDate != default ? d.ModifiedDate?.ToString(Constants.DATE_FORMAT) : string.Empty,
                        Transfers = d.Transfer.Select(t =>
                        {
                            var fromStructure = string.Empty;
                            if (t.FromStructureId.HasValue)
                            {
                                fromStructure = t.FromStructure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                                {
                                    fromStructure = t.FromStructure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                                {
                                    fromStructure = t.FromStructure.NameFr;
                                }
                                if (string.IsNullOrEmpty(fromStructure))
                                {
                                    fromStructure = t.FromStructure.Name;

                                }
                            }
                            var toStructure = string.Empty;
                            if (t.ToStructureId.HasValue)
                            {
                                toStructure = t.ToStructure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                                {
                                    toStructure = t.ToStructure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                                {
                                    toStructure = t.ToStructure.NameFr;
                                }
                                if (string.IsNullOrEmpty(toStructure))
                                {
                                    toStructure = t.ToStructure.Name;

                                }
                            }
                            var isOverdue = false;
                            if (t.DueDate.HasValue)
                            {
                                if (t.ClosedDate.HasValue)
                                {
                                    isOverdue = t.ClosedDate.Value.Date > t.DueDate.Value;
                                }
                                else
                                {
                                    isOverdue = DateTime.Now.Date > t.DueDate.Value;
                                }
                            }

                            return new TransferReportDetailsListViewModel
                            {
                                Id = t.Id,
                                FromUser =  t.FromUserId.HasValue ? ( language==Language.EN?
                                $"{t.FromUser.Firstname} {t.FromUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                                ToUser = t.ToUserId.HasValue ?(language==Language.EN?
                                $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}")
                                : string.Empty,
                                FromStructure = fromStructure,
                                ToStructure = toStructure,
                                IsOverDue = isOverdue,
                                TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT)
                            };
                        }).ToList()
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List InProgress transfers.
        /// Must be non draft.
        /// Not all data is retrieved (paging is supported).
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="filter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <param name="sortExpression"></param>
        /// <returns></returns>
        public static async Task<(long, List<TransferReportListViewModel>)> ListTransfersSentToStructureAsync(int startIndex, int pageSize,int structureId, ExpressionBuilderFilters filter = null,
             ExpressionBuilderFilters userFilter = null, List<SortExpression> sortExpression = null,
            Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                var filterExp = ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And);
                var userFilterExp = ExpressionBuilder.GetExpression<Transfer>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetTransfersSentToStructureCount(structureId, filterExp, userFilterExp);
                var itemList = await item.ListTransfersSentToStructureAsync(startIndex, pageSize, structureId, filterExp, userFilterExp, sortExpression.OrderByExpression<Transfer>());
                return (countResult, itemList.Select(t =>
                {
                    var fromStructure = string.Empty;
                    if (t.FromStructureId.HasValue)
                    {
                        fromStructure = t.FromStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            fromStructure = t.FromStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            fromStructure = t.FromStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(fromStructure))
                        {
                            fromStructure = t.FromStructure.Name;

                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(toStructure))
                        {
                            toStructure = t.ToStructure.Name;

                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value.Date > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > t.DueDate.Value;
                        }
                    }

                    return new TransferReportListViewModel
                    {
                        Id = t.Id,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.Document.ReferenceNumber,
                        Subject = t.Document.Subject,
                        FromUserId = t.FromUserId,
                        ToUserId = t.ToUserId,
                        FromStructureId = t.FromStructureId,
                        ToStructureId = t.ToStructureId,
                        FromUser = t.FromUserId.HasValue ? 
                        (language==Language.EN? $"{t.FromUser.Firstname} {t.FromUser.Lastname}": 
                        $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") :
                        string.Empty,
                        ToUser = t.ToUserId.HasValue ? (language==Language.EN?
                        $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                        FromStructure = fromStructure,
                        ToStructure = toStructure,
                        IsOverDue = isOverdue,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List InProgress transfers.
        /// Must be non draft.
        /// All data is retrieved (paging is not supported). 
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(long, List<TransferReportListViewModel>)> ListTransfersSentToStructureRetrievingAllData(int structureId,ExpressionBuilderFilters filter = null,
            ExpressionBuilderFilters userFilter = null, Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                int startIndex = 0, pageSize = -1;
                var filterExp = ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And);
                var userFilterExp = ExpressionBuilder.GetExpression<Transfer>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetTransfersSentToStructureCount(structureId,filterExp, userFilterExp);
                var itemList = await item.ListTransfersSentToStructureAsync(startIndex, pageSize, structureId, filterExp, userFilterExp);
                return (countResult, itemList.Select(t =>
                {
                    var fromStructure = string.Empty;
                    if (t.FromStructureId.HasValue)
                    {
                        fromStructure = t.FromStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            fromStructure = t.FromStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            fromStructure = t.FromStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(fromStructure))
                        {
                            fromStructure = t.FromStructure.Name;

                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(toStructure))
                        {
                            toStructure = t.ToStructure.Name;

                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value.Date > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > t.DueDate.Value;
                        }
                    }

                    return new TransferReportListViewModel
                    {
                        Id = t.Id,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.Document.ReferenceNumber,
                        Subject = t.Document.Subject,
                        FromUserId = t.FromUserId,
                        ToUserId = t.ToUserId,
                        FromStructureId = t.FromStructureId,
                        ToStructureId = t.ToStructureId,
                        FromUser = t.FromUserId.HasValue ?( language==Language.EN?
                        $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                        ToUser = t.ToUserId.HasValue ? (language==Language.EN?
                        $"{t.ToUser.Firstname} {t.ToUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                        FromStructure = fromStructure,
                        ToStructure = toStructure,
                        IsOverDue = isOverdue,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                    };
                }).ToList());
            }
        }


        /// <summary>
        /// List InProgress transfers.
        /// Must be non draft.
        /// Not all data is retrieved (paging is supported).
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="departmentId"></param>
        /// <param name="getAll"></param>
        /// <param name="language"></param>
        /// <param name="sortExpression"></param>
        /// <returns></returns>
        public static async Task<(long, List<CorrespondenceReportListViewModel>)> ListOutgoingFromDepartmentAsync(int startIndex, int pageSize, int departmentId,bool getAll,
            List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            using (DAL.Document item = new DAL.Document())
            {
                var countResult = await item.GetOutgoingFromDepartmentCount(departmentId);
                var itemList = await item.ListOutgoingFromDepartmentAsync(startIndex, pageSize, departmentId, getAll, sortExpression.OrderByExpression<DAL.Document>());
                return (countResult, itemList.Select(d =>
                {

                    var receiverEntity = d.DocumentReceiverEntity.Count > 0 ? string.Join(Constants.SEPARATOR,
                        d.DocumentReceiverEntity.Select(t => {
                              var receiverName = string.Empty;
                              if (t.Structure != null)
                              {
                                  receiverName = t.Structure.Name;
                                  if (language == Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                  {
                                      receiverName = t.Structure.NameAr;
                                  }
                                  else if (language == Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                  {
                                      receiverName = t.Structure.NameFr;
                                  }
                                  if (string.IsNullOrEmpty(receiverName))
                                  {
                                      receiverName = t.Structure.Name;

                                  }
                              }
                              else if (t.EntityGroup != null)
                              {
                                  receiverName = t.EntityGroup.Name;
                                  if (language == Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
                                  {
                                      receiverName = t.EntityGroup.NameAr;
                                  }
                                  else if (language == Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
                                  {
                                      receiverName = t.EntityGroup.NameFr;
                                  }
                                  if (string.IsNullOrEmpty(receiverName))
                                  {
                                      receiverName = t.EntityGroup.Name;

                                  }
                              }
                              return receiverName;
                          }).ToList()) : string.Empty;
                    //Incoming category id =1;
                    var isLinkedToIncoming = d.LinkedDocumentDocument.Any(s => s.LinkedDocumentNavigation.CategoryId == 1);
                    return new CorrespondenceReportListViewModel
                    {
                        Id = d.Id,
                        Subject = d.Subject,
                        IsLinkedToIncoming= isLinkedToIncoming,
                        ReceivingEntity = receiverEntity,
                        ReferenceNumber = d.ReferenceNumber,
                        CreatedDate = d.CreatedDate.ToString(Constants.DATE_FORMAT),
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List InProgress transfers.
        /// Must be non draft.
        /// Not all data is retrieved (paging is supported).
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="filter"></param>
        /// <param name="stcFilter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <param name="sortExpression"></param>
        /// <returns></returns>
        public static async Task<(long, List<TransferReportListViewModel>)> ListInProgressTransfers(int startIndex, int pageSize, ExpressionBuilderFilters filter = null,
            ExpressionBuilderFilters stcFilter = null, ExpressionBuilderFilters userFilter = null, List<SortExpression> sortExpression = null,
            Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                var filterExp = ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And);
                var stcFilterExp = ExpressionBuilder.GetExpression<Transfer>(stcFilter, ExpressionBuilderOperator.Or);
                var userFilterExp = ExpressionBuilder.GetExpression<Transfer>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetInProgressTransfersCount(filterExp, stcFilterExp, userFilterExp);
                var itemList = await item.ListInProgressTransfersAsync(startIndex, pageSize, filterExp, stcFilterExp, userFilterExp, sortExpression.OrderByExpression<Transfer>());
                return (countResult, itemList.Select(t =>
                {
                    var fromStructure = string.Empty;
                    if (t.FromStructureId.HasValue)
                    {
                        fromStructure = t.FromStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            fromStructure = t.FromStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            fromStructure = t.FromStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(fromStructure))
                        {
                            fromStructure = t.FromStructure.Name;

                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(toStructure))
                        {
                            toStructure = t.ToStructure.Name;

                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value.Date > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > t.DueDate.Value;
                        }
                    }

                    return new TransferReportListViewModel
                    {
                        Id = t.Id,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.Document.ReferenceNumber,
                        Subject = t.Document.Subject,
                        FromUserId = t.FromUserId,
                        ToUserId = t.ToUserId,
                        FromStructureId = t.FromStructureId,
                        ToStructureId = t.ToStructureId,
                        FromUser = t.FromUserId.HasValue ?
                        (language==Language.EN? $"{t.FromUser.Firstname} {t.FromUser.Lastname}": 
                        $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") :
                        string.Empty,
                        ToUser = t.ToUserId.HasValue ? (language==Language.EN?
                        $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                        FromStructure = fromStructure,
                        ToStructure = toStructure,
                        IsOverDue = isOverdue,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List InProgress transfers.
        /// Must be non draft.
        /// All data is retrieved (paging is not supported). 
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="stcFilter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(long, List<TransferReportListViewModel>)> ListInProgressTransfersRetrievingAllData(ExpressionBuilderFilters filter = null,
            ExpressionBuilderFilters stcFilter = null, ExpressionBuilderFilters userFilter = null, Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                int startIndex = 0, pageSize = -1;
                var filterExp = ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And);
                var stcFilterExp = ExpressionBuilder.GetExpression<Transfer>(stcFilter, ExpressionBuilderOperator.Or);
                var userFilterExp = ExpressionBuilder.GetExpression<Transfer>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetInProgressTransfersCount(filterExp, stcFilterExp, userFilterExp);
                var itemList = await item.ListInProgressTransfersAsync(startIndex, pageSize, filterExp, stcFilterExp, userFilterExp);
                return (countResult, itemList.Select(t =>
                {
                    var fromStructure = string.Empty;
                    if (t.FromStructureId.HasValue)
                    {
                        fromStructure = t.FromStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            fromStructure = t.FromStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            fromStructure = t.FromStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(fromStructure))
                        {
                            fromStructure = t.FromStructure.Name;

                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(toStructure))
                        {
                            toStructure = t.ToStructure.Name;

                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value.Date > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > t.DueDate.Value;
                        }
                    }

                    return new TransferReportListViewModel
                    {
                        Id = t.Id,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.Document.ReferenceNumber,
                        Subject = t.Document.Subject,
                        FromUserId = t.FromUserId,
                        ToUserId = t.ToUserId,
                        FromStructureId = t.FromStructureId,
                        ToStructureId = t.ToStructureId,
                        FromUser = t.FromUserId.HasValue ?( language==Language.EN?
                        $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                        ToUser = t.ToUserId.HasValue ? (language==Language.EN?
                        $"{t.ToUser.Firstname} {t.ToUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                        FromStructure = fromStructure,
                        ToStructure = toStructure,
                        IsOverDue = isOverdue,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List completed transfers.
        /// Must be non draft.
        /// Not all data is retrieved (paging is supported).
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="filter"></param>
        /// <param name="stcFilter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <param name="sortExpression"></param>
        /// <returns></returns>
        public static async Task<(long, List<TransferReportListViewModel>)> ListCompletedTransfers(int startIndex, int pageSize, ExpressionBuilderFilters filter = null,
            ExpressionBuilderFilters stcFilter = null, ExpressionBuilderFilters userFilter = null, List<SortExpression> sortExpression = null,
            Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                var filterExp = ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And);
                var stcFilterExp = ExpressionBuilder.GetExpression<Transfer>(stcFilter, ExpressionBuilderOperator.Or);
                var userFilterExp = ExpressionBuilder.GetExpression<Transfer>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetCompletedTransfersCount(filterExp, stcFilterExp, userFilterExp);
                var itemList = await item.ListCompletedTransfersAsync(startIndex, pageSize, filterExp, stcFilterExp, userFilterExp, sortExpression.OrderByExpression<Transfer>());
                return (countResult, itemList.Select(t =>
                {
                    var fromStructure = string.Empty;
                    if (t.FromStructureId.HasValue)
                    {
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            fromStructure = t.FromStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            fromStructure = t.FromStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(fromStructure))
                        {
                            fromStructure = t.FromStructure.Name;

                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(toStructure))
                        {
                            toStructure = t.ToStructure.Name;

                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value.Date > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > t.DueDate.Value;
                        }
                    }

                    return new TransferReportListViewModel
                    {
                        Id = t.Id,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.Document.ReferenceNumber,
                        Subject = t.Document.Subject,
                        FromUserId = t.FromUserId,
                        ToUserId = t.ToUserId,
                        FromStructureId = t.FromStructureId,
                        ToStructureId = t.ToStructureId,
                        FromUser = t.FromUserId.HasValue ? (language==Language.EN?
                        $"{t.FromUser.Firstname} {t.FromUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                        ToUser = t.ToUserId.HasValue ?(language==Language.EN?
                        $"{t.ToUser.Firstname} {t.ToUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                        FromStructure = fromStructure,
                        ToStructure = toStructure,
                        IsOverDue = isOverdue,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List Completed transfers.
        /// Must be non draft
        /// All data is retrieved (paging is not supported). 
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="stcFilter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(long, List<TransferReportListViewModel>)> ListCompletedTransfersRetrievingAllData(ExpressionBuilderFilters filter = null,
            ExpressionBuilderFilters stcFilter = null, ExpressionBuilderFilters userFilter = null, Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                int startIndex = 0, pageSize = -1;
                var filterExp = ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And);
                var stcFilterExp = ExpressionBuilder.GetExpression<Transfer>(stcFilter, ExpressionBuilderOperator.Or);
                var userFilterExp = ExpressionBuilder.GetExpression<Transfer>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetCompletedTransfersCount(filterExp, stcFilterExp, userFilterExp);
                var itemList = await item.ListCompletedTransfersAsync(startIndex, pageSize, filterExp, stcFilterExp, userFilterExp);
                return (countResult, itemList.Select(t =>
                {
                    var fromStructure = string.Empty;
                    if (t.FromStructureId.HasValue)
                    {
                        fromStructure = t.FromStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            fromStructure = t.FromStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            fromStructure = t.FromStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(fromStructure))
                        {
                            fromStructure = t.FromStructure.Name;

                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                        if (string.IsNullOrEmpty(toStructure))
                        {
                            toStructure = t.ToStructure.Name;

                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value.Date > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > t.DueDate.Value;
                        }
                    }

                    return new TransferReportListViewModel
                    {
                        Id = t.Id,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.Document.ReferenceNumber,
                        Subject = t.Document.Subject,
                        FromUserId = t.FromUserId,
                        ToUserId = t.ToUserId,
                        FromStructureId = t.FromStructureId,
                        ToStructureId = t.ToStructureId,
                        FromUser = t.FromUserId.HasValue ?(language==Language.EN?
                        $"{t.FromUser.Firstname} {t.FromUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                        ToUser = t.ToUserId.HasValue ?(language==Language.EN?
                        $"{t.ToUser.Firstname} {t.ToUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                        FromStructure = fromStructure,
                        ToStructure = toStructure,
                        IsOverDue = isOverdue,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List Operations By User.
        /// Must be non draft.
        /// Not all data is retrieved (paging is supported).
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="filter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <param name="sortExpression"></param>
        /// <returns></returns>
        public static async Task<(long, List<AuditReportListViewModel>)> ListOperationByUser(int startIndex, int pageSize, ExpressionBuilderFilters filter = null,
            ExpressionBuilderFilters userFilter = null, List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            using (ActivityLog item = new ActivityLog())
            {
                var filterExp = ExpressionBuilder.GetExpression<ActivityLog>(filter, ExpressionBuilderOperator.And);
                var userFilterExp = ExpressionBuilder.GetExpression<ActivityLog>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetOperationByUserCount(filterExp, userFilterExp);
                var itemList = await item.ListOperationByUserAsync(startIndex, pageSize, filterExp, userFilterExp, sortExpression.OrderByExpression<ActivityLog>());
                return (countResult, itemList.Select(t =>
                {
                    return new AuditReportListViewModel
                    {
                        CategoryId = t.Document?.CategoryId,
                        ReferenceNumber = t.Document?.ReferenceNumber,
                        Subject = t.Document?.Subject,
                        UserId = t.UserId,
                        ActionId = t.ActivityLogActionId,
                        User = language==Language.EN? $"{t.User.Firstname} {t.User.Lastname}": $"{IdentityHelperExtension.GetFullName(t.User.Id, language)}",
                        CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        Id = t.Id,
                        Note = string.IsNullOrEmpty(t.Note) ? string.Empty : t.Note,
                        Action = TranslationUtility.Translate(t.ActivityLogAction.Name, language)
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List Operations By User.
        /// Must be non draft.
        /// All data is retrieved (paging is not supported).
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="userFilter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(long, List<AuditReportListViewModel>)> ListOperationByUserRetrievingAllData(ExpressionBuilderFilters filter = null,
            ExpressionBuilderFilters userFilter = null, Language language = Language.EN)
        {
            using (ActivityLog item = new ActivityLog())
            {
                int startIndex = 0, pageSize = -1;
                var filterExp = ExpressionBuilder.GetExpression<ActivityLog>(filter, ExpressionBuilderOperator.And);
                var userFilterExp = ExpressionBuilder.GetExpression<ActivityLog>(userFilter, ExpressionBuilderOperator.Or);
                var countResult = await item.GetOperationByUserCount(filterExp, userFilterExp);
                var itemList = await item.ListOperationByUserAsync(startIndex, pageSize, filterExp, userFilterExp);
                return (countResult, itemList.Select(t =>
                {
                    return new AuditReportListViewModel
                    {
                        CategoryId = t.Document?.CategoryId,
                        ReferenceNumber = t.Document?.ReferenceNumber,
                        Subject = t.Document?.Subject,
                        UserId = t.UserId,
                        ActionId = t.ActivityLogActionId,
                        User =language==Language.EN? $"{t.User.Firstname} {t.User.Lastname}": $"{IdentityHelperExtension.GetFullName(t.User.Id, language)}",
                        CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        Id = t.Id,
                        Note = string.IsNullOrEmpty(t.Note) ? string.Empty : t.Note,
                        Action = TranslationUtility.Translate(t.ActivityLogAction.Name, language)
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List Operations By Correspondence.
        /// Must be non draft.
        /// Not all data is retrieved (paging is supported).
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="filter"></param>
        /// <param name="language"></param>
        /// <param name="sortExpression"></param>
        /// <returns></returns>
        public static async Task<(long, List<AuditReportListViewModel>)> ListOperationByCorrespondence(int startIndex, int pageSize, ExpressionBuilderFilters filter = null,
            List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            using (ActivityLog item = new ActivityLog())
            {
                var filterExp = ExpressionBuilder.GetExpression<ActivityLog>(filter, ExpressionBuilderOperator.And);
                var countResult = await item.GetOperationByCorrespondenceCount(filterExp);
                var itemList = await item.ListOperationByCorrespondenceAsync(startIndex, pageSize, filterExp, sortExpression.OrderByExpression<ActivityLog>());
                return (countResult, itemList.Select(t =>
                {
                    return new AuditReportListViewModel
                    {
                        ReferenceNumber = t.Document?.ReferenceNumber,
                        Subject = t.Document?.Subject,
                        UserId = t.UserId,
                        ActionId = t.ActivityLogActionId,
                        User =language==Language.EN? $"{t.User.Firstname} {t.User.Lastname}": $"{IdentityHelperExtension.GetFullName(t.User.Id, language)}",
                        CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        Id = t.Id,
                        Note = string.IsNullOrEmpty(t.Note) ? string.Empty : t.Note,
                        Action = TranslationUtility.Translate(t.ActivityLogAction.Name, language)
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List Operations By Correspondence.
        /// Must be non draft.
        /// All data is retrieved (paging is not supported). 
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(long, List<AuditReportListViewModel>)> ListOperationByCorrespondenceRetrievingAllData(ExpressionBuilderFilters filter = null, Language language = Language.EN)
        {
            using (ActivityLog item = new ActivityLog())
            {
                int startIndex = 0, pageSize = -1;
                var filterExp = ExpressionBuilder.GetExpression<ActivityLog>(filter, ExpressionBuilderOperator.And);
                var countResult = await item.GetOperationByCorrespondenceCount(filterExp);
                var itemList = await item.ListOperationByCorrespondenceAsync(startIndex, pageSize, filterExp);
                return (countResult, itemList.Select(t =>
                {
                    return new AuditReportListViewModel
                    {
                        ReferenceNumber = t.Document?.ReferenceNumber,
                        Subject = t.Document?.Subject,
                        UserId = t.UserId,
                        ActionId = t.ActivityLogActionId,
                        User =language==Language.EN? $"{t.User.Firstname} {t.User.Lastname}": $"{IdentityHelperExtension.GetFullName(t.User.Id, language)}",
                        CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        Id = t.Id,
                        Note = string.IsNullOrEmpty(t.Note) ? string.Empty : t.Note,
                        Action = TranslationUtility.Translate(t.ActivityLogAction.Name, language)
                    };
                }).ToList());
            }
        }

        //Commented for performance
        ///// <summary>
        ///// Displays for each structure and each user inside a structure the total number of 
        ///// created correspondence per category and the total number of sent and received transfers
        ///// Not all data is retrieved (paging is supported).
        ///// </summary>
        ///// <param name="userId"></param>
        ///// <param name="structureIds"></param>
        ///// <param name="roleId"></param>
        ///// <param name="startIndex"></param>
        ///// <param name="pageSize"></param>
        ///// <param name="isStructureReceiver"></param>
        ///// <param name="privacyLevel"></param>
        ///// <param name="selectedStructures"></param>
        ///// <param name="selectedUsers"></param>
        ///// <param name="filter"></param>
        ///// <param name="language"></param>
        ///// <returns></returns>
        //Commented for performance
        //public static (int, List<StatisticalCorrespondenceListViewModel>) GetCreatedCountByStructure(long userId, List<long> structureIds, int roleId, int startIndex, int pageSize, bool isStructureReceiver, short privacyLevel, List<long> selectedStructures, List<long> selectedUsers, ExpressionBuilderFilters filter, Language language)
        //{
        //    bool isAdmin = roleId == (int)Role.Administrator;
        //    var filterExp = ExpressionBuilder.GetExpression<DAL.Document>(filter, ExpressionBuilderOperator.And);
        //    var searchAssignedStructures = ManageSearchAssignedSecurity.GetSearchAssignedSecurity(userId, structureIds);
        //    var list = new DAL.Document().GetCreatedCountByStructure(structureIds, userId, isAdmin, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, selectedStructures, selectedUsers, filterExp);
        //    List<long> createdByStructureIds = new List<long>();
        //    int totalCount = list.Select(t => t.CreatedByStructureId).Distinct().ToList().Count;
        //    createdByStructureIds = list.Select(t => t.CreatedByStructureId).Distinct().Skip(startIndex).Take(pageSize).ToList();

        //    var retValue = new List<StatisticalCorrespondenceListViewModel>();
        //    for (int i = 0; i < createdByStructureIds.Count; i++)
        //    {
        //        StatisticalCorrespondenceListViewModel model = retValue.Find(t => t.StructureId == createdByStructureIds[i]);
        //        if (model == null)
        //        {
        //            Structure structure = list.Where(t => t.CreatedByStructureId == createdByStructureIds[i]).FirstOrDefault().CreatedByStructure;
        //            string structureName = structure.Name;
        //            if (structure != null)
        //            {
        //                if (language == Language.AR && !string.IsNullOrEmpty(structure.NameAr))
        //                {
        //                    structureName = structure.NameAr;
        //                }
        //                else if (language == Language.FR && !string.IsNullOrEmpty(structure.NameFr))
        //                {
        //                    structureName = structure.NameFr;
        //                }
        //            }
        //            model = new StatisticalCorrespondenceListViewModel();
        //            model.StructureId = createdByStructureIds[i];
        //            model.StructureName = structureName;
        //            List<long> toUsers = list.Select(t => t.Transfer.Where(t => t.ToStructureId == createdByStructureIds[i]).Select(t => t.ToUserId.HasValue ? t.ToUserId.Value : default).Distinct().ToList()).FirstOrDefault();
        //            List<long> fromUsers = list.Select(t => t.Transfer.Where(t => t.FromStructureId == createdByStructureIds[i]).Select(t => t.FromUserId.HasValue ? t.FromUserId.Value : default).Distinct().ToList()).FirstOrDefault();
        //            toUsers.Remove(default);
        //            fromUsers.Remove(default);

        //            model.CountTotalTransfersSent = list.Select(t => t.Transfer.Where(t => t.FromStructureId == createdByStructureIds[i]).ToList()).Sum(x => x.Count());
        //            model.CountTotalTransfersReceived = list.Select(t => t.Transfer.Where(t => t.ToStructureId == createdByStructureIds[i]).ToList()).Sum(x => x.Count());
        //            model.CategoryCount = new Dictionary<long, long>();
        //            var categoryItems = list.Where(t => t.CreatedByStructureId == createdByStructureIds[i]).GroupBy(t => t.CategoryId).Select(t => new { CategoryId = t.Key, Count = t.Count() }).ToList();
        //            for (int j = 0; j < categoryItems.Count; j++)
        //            {
        //                model.CategoryCount.Add(categoryItems[j].CategoryId, categoryItems[j].Count);
        //            }
        //            var userCategoryItems = list.Where(t => t.CreatedByStructureId == createdByStructureIds[i]).GroupBy(t => new { t.CreatedByUserId, t.CategoryId }).Select(t => new { t.Key.CreatedByUserId, t.Key.CategoryId, Count = t.Count() }).ToList();
        //            model.Users = new List<UserStatisticalCorrespondenceListViewModel>();

        //            for (int j = 0; j < userCategoryItems.Count; j++)
        //            {
        //                var user = model.Users.Find(t => t.UserId == userCategoryItems[j].CreatedByUserId);
        //                if (user == null)
        //                {
        //                    var targetUser = list.Where(t => t.CreatedByUserId == userCategoryItems[j].CreatedByUserId).FirstOrDefault().CreatedByUser;
        //                    user = new UserStatisticalCorrespondenceListViewModel();
        //                    user.CategoryCount = new Dictionary<long, long>();
        //                    user.UserId = userCategoryItems[j].CreatedByUserId;
        //                    user.UserName = targetUser != null ? $"{targetUser.Firstname} {targetUser.Lastname}" : "";
        //                    user.CountTotalTransfersSent = list.Select(t => t.Transfer.Where(t => t.FromStructureId == createdByStructureIds[i] && t.FromUserId == userCategoryItems[j].CreatedByUserId).ToList()).Sum(x => x.Count());
        //                    user.CountTotalTransfersReceived = list.Select(t => t.Transfer.Where(t => t.ToStructureId == createdByStructureIds[i] && t.ToUserId == userCategoryItems[j].CreatedByUserId).ToList()).Sum(x => x.Count());
        //                    user.CategoryCount.Add(userCategoryItems[j].CategoryId, userCategoryItems[j].Count);
        //                    model.Users.Add(user);
        //                }
        //                else
        //                {
        //                    user.CategoryCount.Add(userCategoryItems[j].CategoryId, userCategoryItems[j].Count);
        //                }
        //            }
        //            for (int j = 0; j < toUsers.Count; j++)
        //            {
        //                var user = model.Users.Find(t => t.UserId == toUsers[j]);
        //                if (user == null)
        //                {
        //                    var targetUser = Intalio.Core.API.ManageUser.Find(toUsers[j]);
        //                    user = new UserStatisticalCorrespondenceListViewModel();
        //                    user.CategoryCount = new Dictionary<long, long>();
        //                    user.UserId = toUsers[j];
        //                    user.UserName = targetUser != null ? $"{targetUser.Firstname} {targetUser.Lastname}" : "";
        //                    user.CountTotalTransfersReceived = list.Select(t => t.Transfer.Where(t => t.ToStructureId == createdByStructureIds[i] && t.ToUserId == toUsers[j]).ToList()).Sum(x => x.Count());
        //                    model.Users.Add(user);
        //                }
        //            }
        //            for (int j = 0; j < fromUsers.Count; j++)
        //            {
        //                var user = model.Users.Find(t => t.UserId == fromUsers[j]);
        //                if (user == null)
        //                {
        //                    var targetUser = Intalio.Core.API.ManageUser.Find(fromUsers[j]);
        //                    user = new UserStatisticalCorrespondenceListViewModel();
        //                    user.CategoryCount = new Dictionary<long, long>();
        //                    user.UserId = fromUsers[j];
        //                    user.UserName = targetUser != null ? $"{targetUser.Firstname} {targetUser.Lastname}" : "";
        //                    user.CountTotalTransfersSent = list.Select(t => t.Transfer.Where(t => t.FromStructureId == createdByStructureIds[i] && t.FromUserId == fromUsers[j]).ToList()).Sum(x => x.Count());
        //                    model.Users.Add(user);
        //                }
        //            }
        //            retValue.Add(model);
        //        }
        //    }
        //    return (totalCount, retValue);
        //}

        /// <summary>
        /// Displays for each structure; can include users also, the total number of 
        /// created correspondence per category and the total number of sent and received transfers
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="roleId"></param>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="selectedStructures"></param>
        /// <param name="selectedUsers"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <param name="filter"></param>
        /// <param name="language"></param>
        /// <param name="includeUsers"></param>
        /// <returns></returns>
        public static async Task<(int, List<StatisticalCorrespondenceListViewModel>)> GetCreatedCountByStructure(long userId, List<long> structureIds, int roleId, int startIndex, int pageSize, bool isStructureReceiver, short privacyLevel, List<long> selectedStructures, List<long> selectedUsers, string fromDate, string toDate, ExpressionBuilderFilters filter, Language language, bool includeUsers = false)
        {
            using (DAL.Document item = new DAL.Document())
            {
                bool isAdmin = roleId == (int)Role.Administrator;
                var filterExp = ExpressionBuilder.GetExpression<DAL.Document>(filter, ExpressionBuilderOperator.And);
                var searchAssignedStructures = ManageSearchAssignedSecurity.GetSearchAssignedSecurity(userId, structureIds);
                var totalCount = await item.GetCreatedByStructureIdsCount(structureIds, userId, isAdmin, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, selectedStructures, selectedUsers, filterExp);
                var listedStructures = await item.GetCreatedByStructureIdsAsync(startIndex, pageSize, structureIds, userId, isAdmin, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, selectedStructures, selectedUsers, filterExp);
                var retValue = new List<StatisticalCorrespondenceListViewModel>();
                for (int i = 0; i < listedStructures.Count; i++)
                {
                    StatisticalCorrespondenceListViewModel model = new StatisticalCorrespondenceListViewModel();
                    Structure structure = await new Structure().FindAsync(listedStructures[i]);
                    string structureName = structure.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(structure.NameAr))
                    {
                        structureName = structure.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(structure.NameFr))
                    {
                        structureName = structure.NameFr;
                    }
                    if (string.IsNullOrEmpty(structureName))
                    {
                        structureName = structure.Name;

                    }
                    model.StructureId = listedStructures[i];
                    model.StructureName = structureName;
                    model.CountTotalTransfersSent = await new Transfer().GetSentOrReceiveCountByStructureAsync(listedStructures[i], false, structureIds, userId, isAdmin, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, selectedUsers, selectedStructures, fromDate, toDate);
                    model.CountTotalTransfersReceived = await new Transfer().GetSentOrReceiveCountByStructureAsync(listedStructures[i], true, structureIds, userId, isAdmin, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, selectedUsers, selectedStructures, fromDate, toDate);
                    model.CategoryCount = new Dictionary<long, long>();
                    var categoryItems = await new DAL.Document().GetCreatedByStructureCategoryCountAsync(listedStructures[i], structureIds, userId, isAdmin, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, selectedUsers, selectedStructures, fromDate, toDate);
                    for (int j = 0; j < categoryItems.Count; j++)
                    {
                        model.CategoryCount.Add(categoryItems[j].Id, Convert.ToInt64(categoryItems[j].Text));
                    }
                    model.Users = new List<UserStatisticalCorrespondenceListViewModel>();
                    if (includeUsers)
                    {
                        model.Users = await GetStatisticalReportCreatedByUser(listedStructures[i], userId, structureIds, roleId, isStructureReceiver, privacyLevel, selectedUsers, selectedStructures, fromDate, toDate, language);
                    }
                    retValue.Add(model);
                }
                return (totalCount, retValue);
            }
        }

        /// <summary>
        /// Displays for each  each user inside a structure the total number of 
        /// created correspondence per category and the total number of sent and received transfers
        /// </summary>
        /// <param name="targetStructureId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="roleId"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="selectedUsers"></param>
        /// <param name="selectedStructures"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<List<UserStatisticalCorrespondenceListViewModel>> GetStatisticalReportCreatedByUser(long targetStructureId, long userId, List<long> structureIds, int roleId, bool isStructureReceiver, short privacyLevel, List<long> selectedUsers, List<long> selectedStructures, string fromDate, string toDate, Language language)
        {
            List<UserStatisticalCorrespondenceListViewModel> retValue = new List<UserStatisticalCorrespondenceListViewModel>();
            if (targetStructureId != default)
            {
                bool isAdmin = roleId == (int)Role.Administrator;
                var searchAssignedStructures = ManageSearchAssignedSecurity.GetSearchAssignedSecurity(userId, structureIds);
                var listedUsers = await ListUsersByStructureId(targetStructureId, language);
                for (int i = 0; i < listedUsers.Count; i++)
                {
                    UserStatisticalCorrespondenceListViewModel model = new UserStatisticalCorrespondenceListViewModel();
                    model.UserId = listedUsers[i].Id;
                    model.UserName = language==Language.EN? $"{listedUsers[i].FirstName} {listedUsers[i].LastName}": $"{IdentityHelperExtension.GetFullName(listedUsers[i].Id, language)}";
                    model.CountTotalTransfersSent = await new Transfer().GetSentOrReceiveCountByUserAsync(targetStructureId, listedUsers[i].Id, false, structureIds, userId, isAdmin, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, selectedUsers, selectedStructures, fromDate, toDate);
                    model.CountTotalTransfersReceived = await new Transfer().GetSentOrReceiveCountByUserAsync(targetStructureId, listedUsers[i].Id, true, structureIds, userId, isAdmin, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, selectedUsers, selectedStructures, fromDate, toDate);
                    model.CategoryCount = new Dictionary<long, long>();
                    var categoryItems = await new DAL.Document().GetCreatedByUserCategoryCountAsync(targetStructureId, listedUsers[i].Id, structureIds, userId, isAdmin, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, selectedUsers, selectedStructures, fromDate, toDate);
                    for (int j = 0; j < categoryItems.Count; j++)
                    {
                        model.CategoryCount.Add(categoryItems[j].Id, Convert.ToInt64(categoryItems[j].Text));
                    }
                    retValue.Add(model);
                }
            }
            return retValue;
        }

        //Commented for performance
        ///// <summary>
        ///// Displays for each structure and each user inside a structure the total number of 
        ///// created correspondence per category and the total number of sent and received transfers
        ///// All data is retrieved (paging is not supported).
        ///// </summary>
        ///// <param name="userId"></param>
        ///// <param name="structureIds"></param>
        ///// <param name="roleId"></param>
        ///// <param name="isStructureReceiver"></param>
        ///// <param name="privacyLevel"></param>
        ///// <param name="selectedStructures"></param>
        ///// <param name="selectedUsers"></param>
        ///// <param name="filter"></param>
        ///// <param name="language"></param>
        ///// <returns></returns>
        //public static (int, List<StatisticalCorrespondenceListViewModel>) GetCreatedCountByStructureRetrievingAllData(long userId, List<long> structureIds, int roleId, bool isStructureReceiver, short privacyLevel, List<long> selectedStructures, List<long> selectedUsers, ExpressionBuilderFilters filter, Language language)
        //{
        //    bool isAdmin = roleId == (int)Role.Administrator;
        //    var filterExp = ExpressionBuilder.GetExpression<DAL.Document>(filter, ExpressionBuilderOperator.And);
        //    var searchAssignedStructures = ManageSearchAssignedSecurity.GetSearchAssignedSecurity(userId, structureIds);
        //    var list = new DAL.Document().GetCreatedCountByStructure(structureIds, userId, isAdmin, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, selectedStructures, selectedUsers, filterExp);
        //    List<long> createdByStructureIds = new List<long>();
        //    int totalCount = list.Select(t => t.CreatedByStructureId).Distinct().ToList().Count;
        //    createdByStructureIds = list.Select(t => t.CreatedByStructureId).Distinct().ToList();

        //    var retValue = new List<StatisticalCorrespondenceListViewModel>();
        //    for (int i = 0; i < createdByStructureIds.Count; i++)
        //    {
        //        StatisticalCorrespondenceListViewModel model = retValue.Find(t => t.StructureId == createdByStructureIds[i]);
        //        if (model == null)
        //        {
        //            Structure structure = list.Where(t => t.CreatedByStructureId == createdByStructureIds[i]).FirstOrDefault().CreatedByStructure;
        //            string structureName = structure.Name;
        //            if (structure != null)
        //            {
        //                if (language == Language.AR && !string.IsNullOrEmpty(structure.NameAr))
        //                {
        //                    structureName = structure.NameAr;
        //                }
        //                else if (language == Language.FR && !string.IsNullOrEmpty(structure.NameFr))
        //                {
        //                    structureName = structure.NameFr;
        //                }
        //            }
        //            model = new StatisticalCorrespondenceListViewModel();
        //            model.StructureId = createdByStructureIds[i];
        //            model.StructureName = structureName;
        //            List<long> toUsers = list.Select(t => t.Transfer.Where(t => t.ToStructureId == createdByStructureIds[i]).Select(t => t.ToUserId.HasValue ? t.ToUserId.Value : default).Distinct().ToList()).FirstOrDefault();
        //            List<long> fromUsers = list.Select(t => t.Transfer.Where(t => t.FromStructureId == createdByStructureIds[i]).Select(t => t.FromUserId.HasValue ? t.FromUserId.Value : default).Distinct().ToList()).FirstOrDefault();
        //            toUsers.Remove(default);
        //            fromUsers.Remove(default);

        //            model.CountTotalTransfersSent = list.Select(t => t.Transfer.Where(t => t.FromStructureId == createdByStructureIds[i]).ToList()).Sum(x => x.Count());
        //            model.CountTotalTransfersReceived = list.Select(t => t.Transfer.Where(t => t.ToStructureId == createdByStructureIds[i]).ToList()).Sum(x => x.Count());
        //            model.CategoryCount = new Dictionary<long, long>();
        //            var categoryItems = list.Where(t => t.CreatedByStructureId == createdByStructureIds[i]).GroupBy(t => t.CategoryId).Select(t => new { CategoryId = t.Key, Count = t.Count() }).ToList();
        //            for (int j = 0; j < categoryItems.Count; j++)
        //            {
        //                model.CategoryCount.Add(categoryItems[j].CategoryId, categoryItems[j].Count);
        //            }
        //            var userCategoryItems = list.Where(t => t.CreatedByStructureId == createdByStructureIds[i]).GroupBy(t => new { t.CreatedByUserId, t.CategoryId }).Select(t => new { t.Key.CreatedByUserId, t.Key.CategoryId, Count = t.Count() }).ToList();
        //            model.Users = new List<UserStatisticalCorrespondenceListViewModel>();

        //            for (int j = 0; j < userCategoryItems.Count; j++)
        //            {
        //                var user = model.Users.Find(t => t.UserId == userCategoryItems[j].CreatedByUserId);
        //                if (user == null)
        //                {
        //                    var targetUser = list.Where(t => t.CreatedByUserId == userCategoryItems[j].CreatedByUserId).FirstOrDefault().CreatedByUser;
        //                    user = new UserStatisticalCorrespondenceListViewModel();
        //                    user.CategoryCount = new Dictionary<long, long>();
        //                    user.UserId = userCategoryItems[j].CreatedByUserId;
        //                    user.UserName = targetUser != null ? $"{targetUser.Firstname} {targetUser.Lastname}" : "";
        //                    user.CountTotalTransfersSent = list.Select(t => t.Transfer.Where(t => t.FromStructureId == createdByStructureIds[i] && t.FromUserId == userCategoryItems[j].CreatedByUserId).ToList()).Sum(x => x.Count());
        //                    user.CountTotalTransfersReceived = list.Select(t => t.Transfer.Where(t => t.ToStructureId == createdByStructureIds[i] && t.ToUserId == userCategoryItems[j].CreatedByUserId).ToList()).Sum(x => x.Count());
        //                    user.CategoryCount.Add(userCategoryItems[j].CategoryId, userCategoryItems[j].Count);
        //                    model.Users.Add(user);
        //                }
        //                else
        //                {
        //                    user.CategoryCount.Add(userCategoryItems[j].CategoryId, userCategoryItems[j].Count);
        //                }
        //            }
        //            for (int j = 0; j < toUsers.Count; j++)
        //            {
        //                var user = model.Users.Find(t => t.UserId == toUsers[j]);
        //                if (user == null)
        //                {
        //                    var targetUser = Intalio.Core.API.ManageUser.Find(toUsers[j]);
        //                    user = new UserStatisticalCorrespondenceListViewModel();
        //                    user.CategoryCount = new Dictionary<long, long>();
        //                    user.UserId = toUsers[j];
        //                    user.UserName = targetUser != null ? $"{targetUser.Firstname} {targetUser.Lastname}" : "";
        //                    user.CountTotalTransfersReceived = list.Select(t => t.Transfer.Where(t => t.ToStructureId == createdByStructureIds[i] && t.ToUserId == toUsers[j]).ToList()).Sum(x => x.Count());
        //                    model.Users.Add(user);
        //                }
        //            }
        //            for (int j = 0; j < fromUsers.Count; j++)
        //            {
        //                var user = model.Users.Find(t => t.UserId == fromUsers[j]);
        //                if (user == null)
        //                {
        //                    var targetUser = Intalio.Core.API.ManageUser.Find(fromUsers[j]);
        //                    user = new UserStatisticalCorrespondenceListViewModel();
        //                    user.CategoryCount = new Dictionary<long, long>();
        //                    user.UserId = fromUsers[j];
        //                    user.UserName = targetUser != null ? $"{targetUser.Firstname} {targetUser.Lastname}" : "";
        //                    user.CountTotalTransfersSent = list.Select(t => t.Transfer.Where(t => t.FromStructureId == createdByStructureIds[i] && t.FromUserId == fromUsers[j]).ToList()).Sum(x => x.Count());
        //                    model.Users.Add(user);
        //                }
        //            }
        //            retValue.Add(model);
        //        }
        //    }
        //    return (totalCount, retValue);
        //}

        /// <summary>
        /// Return the Id of a document by reference number if the user has access
        /// </summary>
        /// <param name="referenceNumber"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <returns></returns>
        public static async Task<long> FindDocumentIdByReferenceNumberSecured(string referenceNumber, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel)
        {
            bool useAllStructures = true;
            if(Configuration.EnablePerStructure)
                useAllStructures = false;
            var searchAssignedStructures = ManageSearchAssignedSecurity.GetSearchAssignedSecurity(userId, structureIds);
            DAL.Document document = await new DAL.Document().FindByReferenceNumberSecuredAsync(referenceNumber, userId, structureIds, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, useAllStructures);
            return document != null ? document.Id : default;
        }

        /// <summary>
        /// Get correspondence detail by document id
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<DocumentCorrespondenceDetailListViewModel> GetDocumentCorrespondenceDetail(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language language = Language.EN)
        {
            DocumentCorrespondenceDetailListViewModel retValue = new DocumentCorrespondenceDetailListViewModel();
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                filter.Add("Id", documentId, Intalio.Core.Operator.Equals);
                var filterExp = ExpressionBuilder.GetExpression<DAL.Document>(filter, ExpressionBuilderOperator.And);
                var searchAssignedStructures = ManageSearchAssignedSecurity.GetSearchAssignedSecurity(userId, structureIds);
                var document = await new DAL.Document().GetDocumentCorrespondenceDetailAsync(userId, structureIds, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Core.Configuration.SearchAssignedStructureSearchUsersDocuments, filterExp);
                if (document != null)
                {
                    DocumentCorrespondenceDetailListViewModel model = new DocumentCorrespondenceDetailListViewModel();
                    string sendingEntity = string.Empty;
                    if (document.SendingEntity != null)
                    {
                        sendingEntity = document.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(document.SendingEntity.NameAr))
                        {
                            sendingEntity = document.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(document.SendingEntity.NameFr))
                        {
                            sendingEntity = document.SendingEntity.NameFr;
                        }
                        if (string.IsNullOrEmpty(sendingEntity))
                        {
                            sendingEntity = document.SendingEntity.Name;

                        }
                    }
                    string privacy = string.Empty;
                    if (document.Privacy != null)
                    {
                        privacy = document.Privacy.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(document.Privacy.NameAr))
                        {
                            privacy = document.Privacy.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(document.Privacy.NameFr))
                        {
                            privacy = document.Privacy.NameFr;
                        }
                        if (string.IsNullOrEmpty(privacy))
                        {
                            privacy = document.Privacy.Name;

                        }
                    }

                    string priority = string.Empty;
                    if (document.Priority != null)
                    {
                        priority = document.Priority.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(document.Priority.NameAr))
                        {
                            priority = document.Priority.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(document.Priority.NameFr))
                        {
                            priority = document.Priority.NameFr;
                        }
                        if (string.IsNullOrEmpty(priority))
                        {
                            priority = document.Priority.Name;

                        }
                    }

                    model.SendingEntity = sendingEntity;
                    model.ReceivingEntity = document.DocumentReceiverEntity.Count > 0 ? string.Join(Constants.SEPARATOR, document.DocumentReceiverEntity
                        .Select(t =>
                        {
                            var receiverName = string.Empty;
                            if (t.Structure != null)
                            {
                                receiverName = t.Structure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                {
                                    receiverName = t.Structure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                {
                                    receiverName = t.Structure.NameFr;
                                }
                                if (string.IsNullOrEmpty(receiverName))
                                {
                                    receiverName = t.Structure.Name;

                                }
                            }
                            else if (t.EntityGroup != null)
                            {
                                receiverName = t.EntityGroup.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
                                {
                                    receiverName = t.EntityGroup.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
                                {
                                    receiverName = t.EntityGroup.NameFr;
                                }
                                if (string.IsNullOrEmpty(receiverName))
                                {
                                    receiverName = t.EntityGroup.Name;

                                }
                            }
                            return receiverName;
                        }).ToList()) : string.Empty;
                    model.Id = document.Id;
                    model.Subject = document.Subject;
                    model.ReferenceNumber = document.ReferenceNumber;
                    model.PriorityId = document.PriorityId;
                    model.PrivacyId = document.PrivacyId;
                    model.Privacy = privacy;
                    model.Priority = priority;
                    model.RegisterDate = document.CreatedDate.ToString(Constants.DATE_FORMAT);
                    model.CreatedBy = document.CreatedByUser != null ?(language==Language.EN?
                        $"{document.CreatedByUser.Firstname} {document.CreatedByUser.Lastname}": $"{IdentityHelperExtension.GetFullName(document.CreatedByUser.Id, language)}") : string.Empty;
                    model.CarbonCopies = document.DocumentCarbonCopy.Count > 0 ? string.Join(Constants.SEPARATOR, document.DocumentCarbonCopy
                        .Select(t =>
                        {
                            var structureName = string.Empty;
                            if (t.Structure != null)
                            {
                                structureName = t.Structure.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                {
                                    structureName = t.Structure.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                {
                                    structureName = t.Structure.NameFr;
                                }
                                if (string.IsNullOrEmpty(structureName))
                                {
                                    structureName = t.Structure.Name;

                                }
                            }
                            return structureName;
                        }).ToList()) : string.Empty;
                    retValue = model;
                }
            }
            return retValue;
        }

        /// <summary>
        /// Get transfers details of a targeted correspondence by document id
        /// Not all data is retrieved (paging is supported).
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="start"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="sortExpression"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<TransferCorrespondenceDetailListViewModel>)> ListTransferCorrespondenceDetail(long documentId, int start, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<SortExpression> sortExpression = null, long? delegationId = null, Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                List<TransferCorrespondenceDetailListViewModel> retValue = new List<TransferCorrespondenceDetailListViewModel>();
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
                if (delegation != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                }
                if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                    filter.Add("Document.Id", documentId, Intalio.Core.Operator.Equals);
                    var filterExp = ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And);
                    var count = await item.GetDocumentTransfersCorrespondenceDetailCount(filterExp);
                    List<Transfer> transfers = await item.ListTransferCorrespondenceDetailAsync(start, pageSize, filterExp, sortExpression?.OrderByExpression<Transfer>());
                    foreach (Transfer transfer in transfers)
                    {
                        TransferCorrespondenceDetailListViewModel model = new TransferCorrespondenceDetailListViewModel();
                        string fromStructure = string.Empty;
                        string ToStructure = string.Empty;
                        if (transfer.FromStructure != null)
                        {
                            fromStructure = transfer.FromStructure.Name;
                            if (language == Language.AR && !string.IsNullOrEmpty(transfer.FromStructure.NameAr))
                            {
                                fromStructure = transfer.FromStructure.NameAr;
                            }
                            else if (language == Language.FR && !string.IsNullOrEmpty(transfer.FromStructure.NameFr))
                            {
                                fromStructure = transfer.FromStructure.NameFr;
                            }
                            if (string.IsNullOrEmpty(fromStructure))
                            {
                                fromStructure = transfer.FromStructure.Name;

                            }
                        }
                        if (transfer.ToStructure != null)
                        {
                            ToStructure = transfer.ToStructure.Name;
                            if (language == Language.AR && !string.IsNullOrEmpty(transfer.ToStructure.NameAr))
                            {
                                ToStructure = transfer.ToStructure.NameAr;
                            }
                            else if (language == Language.FR && !string.IsNullOrEmpty(transfer.ToStructure.NameFr))
                            {
                                ToStructure = transfer.ToStructure.NameFr;
                            }
                            if (string.IsNullOrEmpty(ToStructure))
                            {
                                ToStructure = transfer.ToStructure.Name;

                            }
                        }

                        model.FromStructure = fromStructure;
                        model.ToStructure = ToStructure;
                        model.FromUser = transfer.FromUser != null ? (language==Language.EN?
                            $"{transfer.FromUser.Firstname} {transfer.FromUser.Lastname}": $"{IdentityHelperExtension.GetFullName(transfer.FromUser.Id, language)}") : string.Empty;
                        model.ToUser = transfer.ToUser != null ? (language==Language.EN?
                            $"{transfer.ToUser.Firstname} {transfer.ToUser.Lastname}": $"{IdentityHelperExtension.GetFullName(transfer.ToUser.Id, language)}") : string.Empty;
                        model.Id = transfer.Id;
                        model.CreatedDate = transfer.CreatedDate.ToString(Constants.DATE_FORMAT);
                        retValue.Add(model);
                    }
                    return (count, retValue);
                }
                return (0, retValue);
            }
        }

        /// <summary>
        /// Get transfers details of a targeted correspondence by document id
        /// All data is retrieved (paging is not supported).
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="sortExpression"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<TransferCorrespondenceDetailListViewModel>)> ListTransferCorrespondenceDetailRetrievingAllData(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<SortExpression> sortExpression = null, long? delegationId = null, Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                List<TransferCorrespondenceDetailListViewModel> retValue = new List<TransferCorrespondenceDetailListViewModel>();
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
                if (delegation != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                }
                if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    int startIndex = 0, pageSize = -1;
                    ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                    filter.Add("Document.Id", documentId, Intalio.Core.Operator.Equals);
                    var filterExp = ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And);
                    var count = await item.GetDocumentTransfersCorrespondenceDetailCount(filterExp);
                    List<Transfer> transfers = await item.ListTransferCorrespondenceDetailAsync(startIndex, pageSize, filterExp, sortExpression?.OrderByExpression<Transfer>());
                    foreach (Transfer transfer in transfers)
                    {
                        TransferCorrespondenceDetailListViewModel model = new TransferCorrespondenceDetailListViewModel();
                        string fromStructure = string.Empty;
                        string ToStructure = string.Empty;
                        if (transfer.FromStructure != null)
                        {
                            fromStructure = transfer.FromStructure.Name;
                            if (language == Language.AR && !string.IsNullOrEmpty(transfer.FromStructure.NameAr))
                            {
                                fromStructure = transfer.FromStructure.NameAr;
                            }
                            else if (language == Language.FR && !string.IsNullOrEmpty(transfer.FromStructure.NameFr))
                            {
                                fromStructure = transfer.FromStructure.NameFr;
                            }
                            if (string.IsNullOrEmpty(fromStructure))
                            {
                                fromStructure = transfer.FromStructure.Name;

                            }
                        }
                        if (transfer.ToStructure != null)
                        {
                            ToStructure = transfer.ToStructure.Name;
                            if (language == Language.AR && !string.IsNullOrEmpty(transfer.ToStructure.NameAr))
                            {
                                ToStructure = transfer.ToStructure.NameAr;
                            }
                            else if (language == Language.FR && !string.IsNullOrEmpty(transfer.ToStructure.NameFr))
                            {
                                ToStructure = transfer.ToStructure.NameFr;
                            }
                            if (string.IsNullOrEmpty(ToStructure))
                            {
                                ToStructure = transfer.ToStructure.Name;

                            }
                        }
                        var langauge = Helper.GetLanguage();
                        model.FromStructure = fromStructure;
                        model.ToStructure = ToStructure;
                        model.FromUser = transfer.FromUser != null ? (language==Language.EN? $"{transfer.FromUser.Firstname} {transfer.FromUser.Lastname}": $"{IdentityHelperExtension.GetFullName(transfer.FromUser.Id, language)}") : string.Empty;
                        model.ToUser = transfer.ToUser != null ? (language ==Language.EN? $"{transfer.ToUser.Firstname} {transfer.ToUser.Lastname}": $"{IdentityHelperExtension.GetFullName(transfer.ToUser.Id, language)}") : string.Empty;
                        model.Id = transfer.Id;
                        model.CreatedDate = transfer.CreatedDate.ToString(Constants.DATE_FORMAT);
                        retValue.Add(model);
                    }
                    return (count, retValue);
                }
                return (0, retValue);
            }
        }

        /// <summary>
        /// List notes for Correspondence Detail Report
        /// Not all data is retrieved (paging is supported).
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<(int, List<NoteListViewModel>)> ListNoteCorrespondenceDetail(long documentId, int startIndex, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null)
        {
            var retValue = (0, new List<NoteListViewModel>());
            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                DAL.Note item = new DAL.Note();
                var countResult = await item.GetCount(documentId, userId);
                var itemList = await item.ListForDocumentDetailReportAsync(startIndex, pageSize, documentId, userId);
                var language = Helper.GetLanguage();
                retValue = (countResult, itemList.Select(t => new NoteListViewModel
                {
                    Id = t.Id,
                    Notes = t.Notes,
                    IsPrivate = t.IsPrivate,
                    CreatedBy =language==Language.EN? $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.CreatedByUser.Id, language)}",
                    CreatedByDelegatedUser = t.CreatedByDelegatedUser != null ? (language==Language.EN? $"{t.CreatedByDelegatedUser.Firstname} {t.CreatedByDelegatedUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.CreatedByDelegatedUser.Id, language)}") : string.Empty,
                    CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                    IsCreatedByDelegator = t.CreatedByDelegatedUserId.HasValue,
                    IsEditable = false
                }).ToList());
            }
            return retValue;
        }

        /// <summary>
        /// List notes for Correspondence Detail Report
        /// All data is retrieved (paging is not supported).
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<(int, List<NoteListViewModel>)> ListNoteCorrespondenceDetailRetrievingAllData(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null)
        {
            var retValue = (0, new List<NoteListViewModel>());
            int startIndex = 0, pageSize = -1;
            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                DAL.Note item = new DAL.Note();
                var countResult = await item.GetCount(documentId, userId);
                var itemList = await item.ListForDocumentDetailReportAsync(startIndex, pageSize, documentId, userId);
                var lang = Helper.GetLanguage();
                retValue = (countResult, itemList.Select(t => new NoteListViewModel
                {
                    Id = t.Id,
                    Notes = t.Notes,
                    IsPrivate = t.IsPrivate,
                    CreatedBy =lang==Language.EN? $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.CreatedByUser.Id, lang)}",
                    CreatedByDelegatedUser = t.CreatedByDelegatedUser != null ? (lang==Intalio.Core.Language.EN? $"{t.CreatedByDelegatedUser.Firstname} {t.CreatedByDelegatedUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.CreatedByDelegatedUser.Id, lang)}") : string.Empty,
                    CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                    IsCreatedByDelegator = t.CreatedByDelegatedUserId.HasValue,
                    IsEditable = false
                }).ToList());
            }
            return retValue;
        }

        /// <summary>
        /// List non archive attachments for Correspondence Detail Report
        /// Not all data is retrieved (paging is supported).
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<NonArchivedAttachmentsListViewModel>)> ListNonArchivedAttachmentsCorrespondenceDetail(long documentId, int startIndex, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language language = Language.EN)
        {
            var retValue = (0, new List<NonArchivedAttachmentsListViewModel>());
            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                NonArchivedAttachments item = new NonArchivedAttachments();
                var countResult = await item.GetCount(documentId);
                var itemList = await item.ListForDocumentDetailReportAsync(startIndex, pageSize, documentId);
                retValue = (countResult, itemList.Select(t => new NonArchivedAttachmentsListViewModel
                {
                    Id = t.Id,
                    CreatedBy =language==Language.EN? $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.CreatedByUser.Id, language)}",
                    CreatedByDelegatedUser = t.CreatedByDelegatedUser != null ?(language==Language.EN? $"{t.CreatedByDelegatedUser.Firstname} {t.CreatedByDelegatedUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.CreatedByDelegatedUser.Id, language)}") : string.Empty,
                    CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                    Quantity = t.Quantity,
                    Description = t.Description,
                    TypeId = t.TypeId,
                    Type = t.Type != null ? language == Language.EN ? t.Type.Name : language == Language.FR ? !string.IsNullOrEmpty(t.Type.NameFr) ? t.Type.NameFr : t.Type.Name : language == Language.AR ? !string.IsNullOrEmpty(t.Type.NameAr) ? t.Type.NameAr : t.Type.Name : t.Type.Name : string.Empty,
                    IsEditable = false,
                    IsCreatedByDelegator = t.CreatedByDelegatedUserId.HasValue
                }).ToList());
            }
            return retValue;
        }

        /// <summary>
        /// List non archive attachments for Correspondence Detail Report
        /// All data is retrieved (paging is not supported).
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<NonArchivedAttachmentsListViewModel>)> ListNonArchivedAttachmentsCorrespondenceDetailRetrievingAllData(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language language = Language.EN)
        {
            var retValue = (0, new List<NonArchivedAttachmentsListViewModel>());
            int startIndex = 0, pageSize = -1;
            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                NonArchivedAttachments item = new NonArchivedAttachments();
                var countResult = await item.GetCount(documentId);
                var itemList = await item.ListForDocumentDetailReportAsync(startIndex, pageSize, documentId);
                retValue = (countResult, itemList.Select(t => new NonArchivedAttachmentsListViewModel
                {
                    Id = t.Id,
                    CreatedBy =language==Language.EN? $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.CreatedByUser.Id, language)}",
                    CreatedByDelegatedUser = t.CreatedByDelegatedUser != null ?(language==Language.EN ?$"{t.CreatedByDelegatedUser.Firstname} {t.CreatedByDelegatedUser.Lastname}": $"{IdentityHelperExtension.GetFullName(t.CreatedByDelegatedUser.Id, language)}") : string.Empty,
                    CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                    Quantity = t.Quantity,
                    Description = t.Description,
                    TypeId = t.TypeId,
                    Type = t.Type != null ? language == Language.EN ? t.Type.Name : language == Language.FR ? !string.IsNullOrEmpty(t.Type.NameFr) ? t.Type.NameFr : t.Type.Name : language == Language.AR ? !string.IsNullOrEmpty(t.Type.NameAr) ? t.Type.NameAr : t.Type.Name : t.Type.Name : string.Empty,
                    IsEditable = false,
                    IsCreatedByDelegator = t.CreatedByDelegatedUserId.HasValue
                }).ToList());
            }
            return retValue;
        }

        /// <summary>
        /// List linked document for  Correspondence Detail Report
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="documentId"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<List<LinkedDocumentListViewModel>> ListLinkedDocumentCorrespondenceDetail(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language language = Language.EN)
        {
            List<LinkedDocumentListViewModel> retValue = new List<LinkedDocumentListViewModel>();
            var directLinks = new List<LinkedDocumentListViewModel>();
            var recursiveLinks = new List<LinkedDocumentListViewModel>();
            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                List<LinkedDocument> linkedDocuments = await ListRecursiveByDocumentId(documentId);
                var linkedDocIds = new List<long>();
                foreach (var item in linkedDocuments)
                {
                    if (!retValue.Any(x => x.LinkedDocumentId == item.LinkedDocumentId))
                    {
                        if (item.DocumentId == documentId)
                        {
                            string status = string.Empty;
                            if (!item.LinkedDocumentNavigation.Status.IsNull())
                            {
                                status = item.LinkedDocumentNavigation.Status.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Status.NameAr))
                                {
                                    status = item.LinkedDocumentNavigation.Status.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Status.NameFr))
                                {
                                    status = item.LinkedDocumentNavigation.Status.NameFr;
                                }
                                if (string.IsNullOrEmpty(status))
                                {
                                    status = item.LinkedDocumentNavigation.Status.Name;

                                }
                            }
                            string category = string.Empty;
                            if (!item.LinkedDocumentNavigation.Category.IsNull())
                            {
                                category = item.LinkedDocumentNavigation.Category.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Category.NameAr))
                                {
                                    category = item.LinkedDocumentNavigation.Category.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Category.NameFr))
                                {
                                    category = item.LinkedDocumentNavigation.Category.NameFr;
                                }
                                if (string.IsNullOrEmpty(category))
                                {
                                    category = item.LinkedDocumentNavigation.Category.Name;

                                }
                            }
                            var linkedDocumentModel = (LinkedDocumentListViewModel)item;
                            linkedDocumentModel.IsDirectLink = true;
                            linkedDocumentModel.Status = status;
                            linkedDocumentModel.Category = category;
                            linkedDocIds.Add(item.LinkedDocumentId);
                            retValue.Add(linkedDocumentModel);
                        }
                        else if (item.LinkedDocumentId == documentId)
                        {
                            string status = string.Empty;
                            if (!item.LinkedDocumentNavigation.Status.IsNull())
                            {
                                status = item.LinkedDocumentNavigation.Status.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Status.NameAr))
                                {
                                    status = item.LinkedDocumentNavigation.Status.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Status.NameFr))
                                {
                                    status = item.LinkedDocumentNavigation.Status.NameFr;
                                }
                                if (string.IsNullOrEmpty(status))
                                {
                                    status = item.LinkedDocumentNavigation.Status.Name;

                                }
                            }
                            string category = string.Empty;
                            if (!item.LinkedDocumentNavigation.Category.IsNull())
                            {
                                category = item.Document.Category.Name;
                                if (language == Language.AR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Category.NameAr))
                                {
                                    category = item.LinkedDocumentNavigation.Category.NameAr;
                                }
                                else if (language == Language.FR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Category.NameFr))
                                {
                                    category = item.LinkedDocumentNavigation.Category.NameFr;
                                }
                                if (string.IsNullOrEmpty(category))
                                {
                                    category = item.LinkedDocumentNavigation.Category.Name;

                                }
                            }
                            item.LinkedDocumentNavigation = item.Document;
                            var linkedDocumentModel = (LinkedDocumentListViewModel)item;
                            linkedDocumentModel.IsDirectLink = true;
                            linkedDocumentModel.Status = status;
                            linkedDocumentModel.Category = category;
                            linkedDocIds.Add(item.DocumentId);
                            linkedDocumentModel.LinkedDocumentId = item.DocumentId;
                            retValue.Add(linkedDocumentModel);
                        }
                        else
                        {
                            if (linkedDocIds.Contains(item.DocumentId) && !linkedDocIds.Contains(item.LinkedDocumentId))
                            {
                                string status = string.Empty;
                                if (!item.LinkedDocumentNavigation.Status.IsNull())
                                {
                                    status = item.LinkedDocumentNavigation.Status.Name;
                                    if (language == Language.AR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Status.NameAr))
                                    {
                                        status = item.LinkedDocumentNavigation.Status.NameAr;
                                    }
                                    else if (language == Language.FR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Status.NameFr))
                                    {
                                        status = item.LinkedDocumentNavigation.Status.NameFr;
                                    }
                                    if (string.IsNullOrEmpty(status))
                                    {
                                        status = item.Document.Status.Name;

                                    }
                                }
                                string category = string.Empty;
                                if (!item.LinkedDocumentNavigation.Category.IsNull())
                                {
                                    category = item.LinkedDocumentNavigation.Category.Name;
                                    if (language == Language.AR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Category.NameAr))
                                    {
                                        category = item.LinkedDocumentNavigation.Category.NameAr;
                                    }
                                    else if (language == Language.FR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Category.NameFr))
                                    {
                                        category = item.LinkedDocumentNavigation.Category.NameFr;
                                    }
                                    if (string.IsNullOrEmpty(category))
                                    {
                                        category = item.LinkedDocumentNavigation.Category.Name;

                                    }
                                }
                                var linkedDocumentModel = (LinkedDocumentListViewModel)item;
                                linkedDocumentModel.Status = status;
                                linkedDocumentModel.Category = category;
                                linkedDocIds.Add(linkedDocumentModel.LinkedDocumentId);
                                retValue.Add(linkedDocumentModel);
                            }
                            else if (linkedDocIds.Contains(item.LinkedDocumentId) && !linkedDocIds.Contains(item.DocumentId))
                            {
                                string status = string.Empty;
                                if (!item.LinkedDocumentNavigation.Status.IsNull())
                                {
                                    status = item.LinkedDocumentNavigation.Status.Name;
                                    if (language == Language.AR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Status.NameAr))
                                    {
                                        status = item.LinkedDocumentNavigation.Status.NameAr;
                                    }
                                    else if (language == Language.FR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Status.NameFr))
                                    {
                                        status = item.LinkedDocumentNavigation.Status.NameFr;
                                    }
                                    if (string.IsNullOrEmpty(status))
                                    {
                                        status = item.LinkedDocumentNavigation.Status.Name;

                                    }
                                }
                                string category = string.Empty;
                                if (!item.LinkedDocumentNavigation.Category.IsNull())
                                {
                                    category = item.LinkedDocumentNavigation.Category.Name;
                                    if (language == Language.AR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Category.NameAr))
                                    {
                                        category = item.LinkedDocumentNavigation.Category.NameAr;
                                    }
                                    else if (language == Language.FR && !string.IsNullOrEmpty(item.LinkedDocumentNavigation.Category.NameFr))
                                    {
                                        category = item.LinkedDocumentNavigation.Category.NameFr;
                                    }
                                    if (string.IsNullOrEmpty(category))
                                    {
                                        category = item.LinkedDocumentNavigation.Category.Name;

                                    }
                                }
                                item.LinkedDocumentNavigation = item.Document;
                                var linkedDocumentModel = (LinkedDocumentListViewModel)item;
                                linkedDocumentModel.LinkedDocumentId = item.DocumentId;
                                linkedDocumentModel.Status = status;
                                linkedDocumentModel.Category = category;
                                linkedDocIds.Add(linkedDocumentModel.LinkedDocumentId);
                                retValue.Add(linkedDocumentModel);
                            }
                        }
                    }
                }
            }
            return retValue;
        }

        /// <summary>
        /// Export correspondence detail report as pdf
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<byte[]> ExportPdfCorrespondenceDetailDocument(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language language = Language.EN)
        {
            int pageIndex = 1;
            bool newPage = false;
            byte[] documentBytes;

            InitPdfLicense();
            string reportTitle = TranslationUtility.Translate("CorrespondenceDetail", language);
            Aspose.Pdf.Document document = CreatePdfTitleDocument(reportTitle);
            document = await ExportPdfCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, document, newPage, pageIndex, delegationId, language);
            document = await ExportPdfTransferCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, document, newPage, pageIndex, delegationId, language);
            document = await ExportPdfNoteCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, document, newPage, pageIndex, delegationId, language);
            document = await ExportPdfNonArchiveCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, document, newPage, pageIndex, delegationId, language);
            document = await ExportPdfLinkedCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, document, newPage, pageIndex, delegationId, language);
            using (MemoryStream documentStream = new MemoryStream())
            {
                document.Save(documentStream);
                documentBytes = documentStream.ToArray();
            }
            return documentBytes;
        }

        /// <summary>
        /// Export correspondence detail report as excel
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<byte[]> ExportExcelCorrespondenceDetailDocument(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language language = Language.EN)
        {
            byte[] documentBytes;
            Workbook workbook = new Workbook();
            Worksheet sheet = workbook.Worksheets[0];
            sheet.Cells.StandardHeight = 15;
            sheet.Cells.StandardWidth = 17.5;
            Aspose.Cells.Cells cells = workbook.Worksheets[0].Cells;
            string reportTitle = TranslationUtility.Translate("CorrespondenceDetail", language);
            CreateExcelTitleDocument(cells, reportTitle);
            int startRow = 15;

            InitExcelLicense();
            await ExportExcelCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, cells, delegationId, language);
            startRow += await ExportExcelTransferCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, startRow, cells, delegationId, language);
            startRow += await ExportExcelNoteListCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, startRow, cells, delegationId, language);
            startRow += await ExportExcelNonArchivedAttachmentsList(documentId, userId, structureIds, isStructureReceiver, privacyLevel, startRow, cells, delegationId, language);
            await ExportExcelLinkedDocumentList(documentId, userId, structureIds, isStructureReceiver, privacyLevel, startRow, cells, delegationId, language);

            using (MemoryStream documentStream = new MemoryStream())
            {
                workbook.Save(documentStream, Aspose.Cells.SaveFormat.Xlsx);
                documentBytes = documentStream.ToArray();
            }
            return documentBytes;
        }

        #endregion

        #region helpers
        public static byte[] ConvertWordToPdf(byte[] data, string fileName)
        {
            try
            {
                byte[] wordPdfData;

                var wordDoc = new Aspose.Words.Document(new MemoryStream(data));
                using (var wordPdfStream = new MemoryStream())
                {
                    wordDoc.Save(wordPdfStream, Aspose.Words.SaveFormat.Pdf);
                    wordPdfData = wordPdfStream.ToArray();
                }
                return wordPdfData;

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting {fileName} to PDF: {ex.Message}");
                return null;
            }

        }

        /// <summary>
        /// convert any attachment which is not pdf to pdf
        /// </summary>
        /// <param name="data"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>

        public static async Task<byte[]> ConvertToPdf(byte[] data, string fileName)
        {
            return await Task.Run(() =>
            {


                try
                {

                    using (MemoryStream ms = new MemoryStream(data))
                    {
                        using (MemoryStream outStream = new MemoryStream())
                        {
                            using (iTextSharp.text.Document document = new iTextSharp.text.Document())
                            {
                                PdfWriter writer = PdfWriter.GetInstance(document, outStream);
                                document.Open();

                                if (IsImage(data))
                                {
                                    iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(data);
                                    image.ScaleToFit(document.PageSize.Width - document.LeftMargin - document.RightMargin, document.PageSize.Height - document.TopMargin - document.BottomMargin);
                                    image.Alignment = iTextSharp.text.Element.ALIGN_CENTER;
                                    document.Add(image);
                                }
                                else if (IsTextFile(fileName))
                                {
                                    string textContent = System.Text.Encoding.UTF8.GetString(data);
                                    document.Add(new Paragraph(textContent));
                                }
                                //else if (IsWordFile(fileName))
                                //{
                                //    var wordDoc = new Aspose.Words.Document(new MemoryStream(data));
                                //    using (var wordPdfStream = new MemoryStream())
                                //    {
                                //        wordDoc.Save(wordPdfStream, Aspose.Words.SaveFormat.Pdf);
                                //        byte[] wordPdfData = wordPdfStream.ToArray();
                                //        File.WriteAllBytes("C:\\Users\\<USER>\\Desktop\\generatedPdf.pdf", wordPdfData);
                                //        PdfReader reader = new PdfReader(wordPdfData);
                                //        using (PdfCopy copy = new PdfCopy(document, outStream))
                                //        {

                                //            for (int i = 1; i <= reader.NumberOfPages; i++)
                                //            {
                                //                PdfImportedPage page = copy.GetImportedPage(reader, i);
                                //                copy.AddPage(page);
                                //            }
                                //        }
                                //    }
                                //}
                                else
                                {
                                    document.Add(new Paragraph("The provided data could not be identified as an image, text, or Word document."));
                                }

                                document.Close();
                            }

                            return outStream.ToArray();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error converting {fileName} to PDF: {ex.Message}");
                    return null;
                }
            });
        }

        /// <summary>
        /// check attachment is pdf or not
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static bool IsPdf(byte[] data)
        {
            try
            {
                using (MemoryStream ms = new MemoryStream(data))
                {
                    using (var reader = new PdfReader(ms))
                    {
                        return true;
                    }
                }
            }
            catch
            {
                return false;
            }
        }

        private static bool IsImage(byte[] data)
        {
            try
            {
                using (MemoryStream ms = new MemoryStream(data))
                {
                    using (System.Drawing.Image img = System.Drawing.Image.FromStream(ms))
                    {
                        return img.RawFormat.Equals(ImageFormat.Jpeg) || img.RawFormat.Equals(ImageFormat.Png) ||
                               img.RawFormat.Equals(ImageFormat.Gif) || img.RawFormat.Equals(ImageFormat.Bmp) ||
                               img.RawFormat.Equals(ImageFormat.Tiff);
                    }
                }
            }
            catch
            {
                return false;
            }
        }

        private static bool IsTextFile(string fileName)
        {
            string extension = Path.GetExtension(fileName).ToLower();
            return extension == ".txt";
        }

        public static bool IsWordFile(string fileName)
        {
            string extension = Path.GetExtension(fileName).ToLower();
            return extension == ".doc" || extension == ".docx";
        }
        /// <summary>
        /// Merge Attachments  as pdf
        /// </summary>
        /// <param name="pdfDocuments"></param>
        /// <returns></returns>
        public static async Task<byte[]> MergePdfDocuments(List<byte[]> pdfDocuments)
        {
            return await Task.Run(() =>
            {
                using (MemoryStream outputMemoryStream = new MemoryStream())
                {
                    using (iTextSharp.text.Document document = new iTextSharp.text.Document())
                    {
                        using (PdfCopy pdfCopy = new PdfCopy(document, outputMemoryStream))
                        {
                            document.Open();

                            foreach (byte[] pdfBytes in pdfDocuments)
                            {
                                using (PdfReader pdfReader = new PdfReader(pdfBytes))
                                {
                                    for (int page = 1; page <= pdfReader.NumberOfPages; page++)
                                    {
                                        PdfImportedPage importedPage = pdfCopy.GetImportedPage(pdfReader, page);
                                        pdfCopy.AddPage(importedPage);
                                    }
                                }
                            }
                        }
                    }

                    return outputMemoryStream.ToArray();
                }
            });
        }

        #endregion

        #region Private Methods

        private static async Task<List<Intalio.Core.Model.UserModel>> ListUsersByStructureId(long structureId, Language language = Language.EN)
        {
            string url = $"{ Configuration.IdentityAuthorityUrl }/api/ListUsersByStructureId?id=" + structureId + "&language=" + language;
            return await Intalio.Core.Helper.HttpGetAsync<List<Intalio.Core.Model.UserModel>>(url, Configuration.IdentityAccessToken);
        }

        private static async Task<List<LinkedDocument>> ListRecursiveByDocumentId(long documentId, long? parentId = null, List<long> documentIds = null)
        {
            List<LinkedDocument> retValue = new List<LinkedDocument>();
            if (documentIds.IsNull())
            {
                documentIds = new List<long>();
            }
            if (!documentIds.IsNull() && documentIds.Contains(documentId))
            {
                return retValue;
            }
            else
            {
                documentIds.Add(documentId);
            }
            List<LinkedDocument> linkedDocuments = await new LinkedDocument().ListByDocumentIdIncludeStatusAndCategoryAsync(documentId, parentId);
            if (!linkedDocuments.IsNullOrEmpty())
            {
                retValue.AddRange(linkedDocuments);
                foreach (var item in linkedDocuments)
                {
                    if (item.DocumentId != documentId)
                    {
                        retValue.AddRange(await ListRecursiveByDocumentId(item.DocumentId, documentId, documentIds));
                    }
                    else
                    {
                        retValue.AddRange(await ListRecursiveByDocumentId(item.LinkedDocumentId, documentId, documentIds));
                    }
                }
            }
            return retValue;
        }

        #region Export Pdf

        private static Aspose.Pdf.Document CreatePdfTitleDocument(string reportTitle)
        {
            Aspose.Pdf.Document document = new Aspose.Pdf.Document();
            document.Info.Title = reportTitle;
            Page pdfPage = document.Pages.Add();
            pdfPage.SetPageSize(Aspose.Pdf.PageSize.A4.Width, Aspose.Pdf.PageSize.A4.Height);
            TextFragment textFragment = new TextFragment(reportTitle);
            textFragment.TextState.FontSize = 20;
            textFragment.TextState.FontStyle = FontStyles.Bold;
            textFragment.TextState.ForegroundColor = Color.Parse("#00AE8D");
            textFragment.TextState.HorizontalAlignment = HorizontalAlignment.Center;
            textFragment.Margin = new MarginInfo { Bottom = 10f };
            pdfPage.Paragraphs.Add(textFragment);

            textFragment = new TextFragment(DateTime.Now.ToString("dd/MM/yyyy H:mm"));
            textFragment.TextState.FontSize = 10;
            textFragment.TextState.FontStyle = FontStyles.Italic;
            textFragment.TextState.ForegroundColor = Color.Parse("#444444");
            textFragment.TextState.HorizontalAlignment = HorizontalAlignment.Center;
            textFragment.Margin = new MarginInfo { Bottom = 30f };
            pdfPage.Paragraphs.Add(textFragment);

            return document;
        }

        private static async Task<Aspose.Pdf.Document> ExportPdfCorrespondenceDetail(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Aspose.Pdf.Document document, bool newPage, int pageIndex, long? delegationId = null, Language language = Language.EN)
        {
            var model = await ManageReport.GetDocumentCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId, language);
            if (model != null)
            {
                Page pdfPage;
                if (newPage)
                {
                    pdfPage = document.Pages.Add();
                }
                else
                {
                    pdfPage = document.Pages[pageIndex];
                }
                Table tab1 = new Table();
                tab1.Margin.Top = 20;
                tab1.Margin.Bottom = 40;
                tab1.ColumnWidths = "225 225";
                tab1.Alignment = HorizontalAlignment.Center;
                tab1.DefaultCellBorder = new BorderInfo(BorderSide.All, 0.1F, Color.Parse("#cccccc"));
                tab1.Border = new BorderInfo(BorderSide.All, 0F);

                MarginInfo margin = new MarginInfo();
                margin.Top = 7f;
                margin.Left = 5f;
                margin.Right = 5f;
                margin.Bottom = 7f;
                tab1.DefaultCellPadding = margin;

                MarginInfo tableHeadersMargin = new MarginInfo();
                tableHeadersMargin.Top = 10f;
                tableHeadersMargin.Left = 5f;
                tableHeadersMargin.Right = 5f;
                tableHeadersMargin.Bottom = 10f;

                TextFragment tf1 = new TextFragment(TranslationUtility.Translate("Correspondence", language));
                tf1.TextState.ForegroundColor = Color.Parse("#333333");
                tf1.TextState.FontSize = 12;
                tf1.TextState.FontStyle = FontStyles.Bold;
                pdfPage.Paragraphs.Add(tf1);

                tab1.RepeatingRowsCount = 1;
                Aspose.Pdf.Row row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("ReferenceNumber", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");

                Aspose.Pdf.Cell tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.Margin = tableHeadersMargin;
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                TextFragment cellText = new TextFragment();
                cellText.TextState.ForegroundColor = Color.Parse("#555555");
                cellText.Text = model.ReferenceNumber;
                row.Cells.Add().Alignment = HorizontalAlignment.Center;
                row.Cells[1].Paragraphs.Add(cellText);

                row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("SendingEntity", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");

                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                cellText = new TextFragment();
                cellText.TextState.ForegroundColor = Color.Parse("#555555");
                cellText.Text = model.SendingEntity;
                row.Cells.Add().Alignment = HorizontalAlignment.Center;
                row.Cells[1].Paragraphs.Add(cellText);

                row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("ReceivingEntity", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");

                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                cellText = new TextFragment();
                cellText.TextState.ForegroundColor = Color.Parse("#555555");
                cellText.Text = model.ReceivingEntity;
                row.Cells.Add().Alignment = HorizontalAlignment.Center;
                row.Cells[1].Paragraphs.Add(cellText);

                row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("CreatedBy", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");

                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                cellText = new TextFragment();
                cellText.TextState.ForegroundColor = Color.Parse("#555555");
                cellText.Text = model.CreatedBy;
                row.Cells.Add().Alignment = HorizontalAlignment.Center;
                row.Cells[1].Paragraphs.Add(cellText);

                row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("Priority", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");

                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                cellText = new TextFragment();
                cellText.TextState.ForegroundColor = Color.Parse("#555555");
                cellText.Text = model.Priority;
                row.Cells.Add().Alignment = HorizontalAlignment.Center;
                row.Cells[1].Paragraphs.Add(cellText);

                row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("Privacy", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");

                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                cellText = new TextFragment();
                cellText.TextState.ForegroundColor = Color.Parse("#555555");
                cellText.Text = model.Privacy;
                row.Cells.Add().Alignment = HorizontalAlignment.Center;
                row.Cells[1].Paragraphs.Add(cellText);

                row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("RegisterDate", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");

                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                cellText = new TextFragment();
                cellText.TextState.ForegroundColor = Color.Parse("#555555");
                cellText.Text = model.RegisterDate;
                row.Cells.Add().Alignment = HorizontalAlignment.Center;
                row.Cells[1].Paragraphs.Add(cellText);

                row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("CarbonCopies", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");

                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                cellText = new TextFragment();
                cellText.TextState.ForegroundColor = Color.Parse("#555555");
                cellText.Text = model.CarbonCopies;
                row.Cells.Add().Alignment = HorizontalAlignment.Center;
                row.Cells[1].Paragraphs.Add(cellText);

                row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("Subject", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");

                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                cellText = new TextFragment();
                cellText.TextState.ForegroundColor = Color.Parse("#555555");
                cellText.Text = model.Subject;
                row.Cells.Add().Alignment = HorizontalAlignment.Center;
                row.Cells[1].Paragraphs.Add(cellText);

                pdfPage.Paragraphs.Add(tab1);
            }
            return document;
        }

        private static async Task<Aspose.Pdf.Document> ExportPdfTransferCorrespondenceDetail(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Aspose.Pdf.Document document, bool newPage, int pageIndex, long? delegationId = null, Language language = Language.EN, List<SortExpression> sortExpression = null)
        {
            var model = (await ManageReport.ListTransferCorrespondenceDetailRetrievingAllData(documentId, userId, structureIds, isStructureReceiver, privacyLevel, sortExpression, delegationId, language)).Item2;
            if (model != null && model.Count > 0)
            {
                Page pdfPage;
                if (newPage)
                {
                    pdfPage = document.Pages.Add();
                }
                else
                {
                    pdfPage = document.Pages[pageIndex];
                }
                Table tab1 = new Table();
                tab1.Margin.Top = 20;
                tab1.Margin.Bottom = 40;
                tab1.ColumnWidths = "90 90 90 90 90";
                tab1.Alignment = HorizontalAlignment.Center;
                tab1.DefaultCellBorder = new BorderInfo(BorderSide.All, 0.1F, Color.Parse("#cccccc"));
                tab1.Border = new BorderInfo(BorderSide.All, 0F);

                MarginInfo margin = new MarginInfo();
                margin.Top = 7f;
                margin.Left = 5f;
                margin.Right = 5f;
                margin.Bottom = 7f;
                tab1.DefaultCellPadding = margin;

                MarginInfo tableHeadersMargin = new MarginInfo();
                tableHeadersMargin.Top = 10f;
                tableHeadersMargin.Left = 5f;
                tableHeadersMargin.Right = 5f;
                tableHeadersMargin.Bottom = 10f;

                TextFragment tf1 = new TextFragment(TranslationUtility.Translate("Transfer", language));
                tf1.TextState.ForegroundColor = Color.Parse("#333333");
                tf1.TextState.FontSize = 12;
                tf1.TextState.FontStyle = FontStyles.Bold;
                pdfPage.Paragraphs.Add(tf1);

                tab1.RepeatingRowsCount = 1;
                Aspose.Pdf.Row row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("FromStructure", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                Aspose.Pdf.Cell tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.Margin = tableHeadersMargin;
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("FromUser", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[1].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("ToStructure", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[2].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("ToUser", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[3].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("TransferDate", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[4].Paragraphs.Add(tf1);

                for (int i = 0; i < model.Count; i++)
                {
                    Aspose.Pdf.Row rowModel = tab1.Rows.Add();
                    TextFragment cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].FromStructure;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[0].Paragraphs.Add(cellText);

                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].FromUser;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[1].Paragraphs.Add(cellText);

                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].ToStructure;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[2].Paragraphs.Add(cellText);

                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].ToUser;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[3].Paragraphs.Add(cellText);

                    string cellTextCreatedDate = model[i].CreatedDate;
                    if (Configuration.CalendarType != CalendarType.None)
                    {
                        var createdDate = DateTime.ParseExact(model[i].CreatedDate, Constants.DATE_FORMAT, CultureInfo.InvariantCulture);
                        cellTextCreatedDate = Intalio.Core.Helper.ConvertGregorianToHijri(createdDate, Constants.DATE_FORMAT);
                    }
                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = cellTextCreatedDate;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[4].Paragraphs.Add(cellText);
                }
                pdfPage.Paragraphs.Add(tab1);
            }
            return document;
        }

        private static async Task<Aspose.Pdf.Document> ExportPdfNoteCorrespondenceDetail(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Aspose.Pdf.Document document, bool newPage, int pageIndex, long? delegationId = null, Language language = Language.EN, List<SortExpression> sortExpression = null)
        {
            var model = (await ManageReport.ListNoteCorrespondenceDetailRetrievingAllData(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId)).Item2;
            if (model != null && model.Count > 0)
            {
                Page pdfPage;
                if (newPage)
                {
                    pdfPage = document.Pages.Add();
                }
                else
                {
                    pdfPage = document.Pages[pageIndex];
                }
                Table tab1 = new Table();
                tab1.Margin.Top = 20;
                tab1.Margin.Bottom = 40;
                tab1.ColumnWidths = "100 100 50 200";
                tab1.Alignment = HorizontalAlignment.Center;
                tab1.DefaultCellBorder = new BorderInfo(BorderSide.All, 0.1F, Color.Parse("#cccccc"));
                tab1.Border = new BorderInfo(BorderSide.All, 0F);

                MarginInfo margin = new MarginInfo();
                margin.Top = 7f;
                margin.Left = 5f;
                margin.Right = 5f;
                margin.Bottom = 7f;
                tab1.DefaultCellPadding = margin;

                MarginInfo tableHeadersMargin = new MarginInfo();
                tableHeadersMargin.Top = 10f;
                tableHeadersMargin.Left = 5f;
                tableHeadersMargin.Right = 5f;
                tableHeadersMargin.Bottom = 10f;

                TextFragment tf1 = new TextFragment(TranslationUtility.Translate("Notes", language));
                tf1.TextState.ForegroundColor = Color.Parse("#333333");
                tf1.TextState.FontSize = 12;
                tf1.TextState.FontStyle = FontStyles.Bold;
                pdfPage.Paragraphs.Add(tf1);

                tab1.RepeatingRowsCount = 1;
                Aspose.Pdf.Row row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("CreatedBy", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                Aspose.Pdf.Cell tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.Margin = tableHeadersMargin;
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("CreatedDate", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[1].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("Private", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[2].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("Note", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[3].Paragraphs.Add(tf1);


                for (int i = 0; i < model.Count; i++)
                {
                    Aspose.Pdf.Row rowModel = tab1.Rows.Add();
                    TextFragment cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].CreatedBy;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[0].Paragraphs.Add(cellText);

                    string cellTextCreatedDate = model[i].CreatedDate;
                    if (Configuration.CalendarType != CalendarType.None)
                    {
                        var createdDate = DateTime.ParseExact(model[i].CreatedDate, Constants.DATE_FORMAT, CultureInfo.InvariantCulture);
                        cellTextCreatedDate = Intalio.Core.Helper.ConvertGregorianToHijri(createdDate, Constants.DATE_FORMAT);
                    }
                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = cellTextCreatedDate;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[1].Paragraphs.Add(cellText);

                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].IsPrivate ? "✔" : "✖";
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[2].Paragraphs.Add(cellText);

                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = string.IsNullOrEmpty(model[i].Notes) ? string.Empty : Regex.Replace(model[i].Notes, @"<[^>]+>|&nbsp;", string.Empty).Trim();
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[3].Paragraphs.Add(cellText);
                }
                pdfPage.Paragraphs.Add(tab1);
            }
            return document;
        }

        private static async Task<Aspose.Pdf.Document> ExportPdfNonArchiveCorrespondenceDetail(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Aspose.Pdf.Document document, bool newPage, int pageIndex, long? delegationId = null, Language language = Language.EN, List<SortExpression> sortExpression = null)
        {
            var model = (await ManageReport.ListNonArchivedAttachmentsCorrespondenceDetailRetrievingAllData(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId, language)).Item2;
            if (model != null && model.Count > 0)
            {
                Page pdfPage;
                if (newPage)
                {
                    pdfPage = document.Pages.Add();
                }
                else
                {
                    pdfPage = document.Pages[pageIndex];
                }
                Table tab1 = new Table();
                tab1.Margin.Top = 20;
                tab1.Margin.Bottom = 40;
                tab1.ColumnWidths = "100 100 150 100";
                tab1.Alignment = HorizontalAlignment.Center;
                tab1.DefaultCellBorder = new BorderInfo(BorderSide.All, 0.1F, Color.Parse("#cccccc"));
                tab1.Border = new BorderInfo(BorderSide.All, 0F);

                MarginInfo margin = new MarginInfo();
                margin.Top = 7f;
                margin.Left = 5f;
                margin.Right = 5f;
                margin.Bottom = 7f;
                tab1.DefaultCellPadding = margin;

                MarginInfo tableHeadersMargin = new MarginInfo();
                tableHeadersMargin.Top = 10f;
                tableHeadersMargin.Left = 5f;
                tableHeadersMargin.Right = 5f;
                tableHeadersMargin.Bottom = 10f;

                TextFragment tf1 = new TextFragment(TranslationUtility.Translate("NonArchivedAttachments", language));
                tf1.TextState.ForegroundColor = Color.Parse("#333333");
                tf1.TextState.FontSize = 12;
                tf1.TextState.FontStyle = FontStyles.Bold;
                pdfPage.Paragraphs.Add(tf1);

                tab1.RepeatingRowsCount = 1;
                Aspose.Pdf.Row row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("Type", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                Aspose.Pdf.Cell tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.Margin = tableHeadersMargin;
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("Quantity", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[1].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("Description", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[2].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("CreatedBy", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[3].Paragraphs.Add(tf1);


                for (int i = 0; i < model.Count; i++)
                {
                    Aspose.Pdf.Row rowModel = tab1.Rows.Add();
                    TextFragment cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].Type;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[0].Paragraphs.Add(cellText);

                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].Quantity.ToString();
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[1].Paragraphs.Add(cellText);

                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = string.IsNullOrEmpty(model[i].Description) ? "" : model[i].Description;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[2].Paragraphs.Add(cellText);

                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].CreatedBy;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[3].Paragraphs.Add(cellText);
                }
                pdfPage.Paragraphs.Add(tab1);
            }
            return document;
        }

        private static async Task<Aspose.Pdf.Document> ExportPdfLinkedCorrespondenceDetail(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Aspose.Pdf.Document document, bool newPage, int pageIndex, long? delegationId = null, Language language = Language.EN, List<SortExpression> sortExpression = null)
        {
            var model = await ManageReport.ListLinkedDocumentCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId);
            if (model != null && model.Count > 0)
            {
                Page pdfPage;
                if (newPage)
                {
                    pdfPage = document.Pages.Add();
                }
                else
                {
                    pdfPage = document.Pages[pageIndex];
                }
                Table tab1 = new Table();
                tab1.Margin.Top = 20;
                tab1.Margin.Bottom = 40;
                tab1.ColumnWidths = "90 90 90 90 90";
                tab1.Alignment = HorizontalAlignment.Center;
                tab1.DefaultCellBorder = new BorderInfo(BorderSide.All, 0.1F, Color.Parse("#cccccc"));
                tab1.Border = new BorderInfo(BorderSide.All, 0F);

                MarginInfo margin = new MarginInfo();
                margin.Top = 7f;
                margin.Left = 5f;
                margin.Right = 5f;
                margin.Bottom = 7f;
                tab1.DefaultCellPadding = margin;

                MarginInfo tableHeadersMargin = new MarginInfo();
                tableHeadersMargin.Top = 10f;
                tableHeadersMargin.Left = 5f;
                tableHeadersMargin.Right = 5f;
                tableHeadersMargin.Bottom = 10f;

                TextFragment tf1 = new TextFragment(TranslationUtility.Translate("LinkedCorrespondence", language));
                tf1.TextState.ForegroundColor = Color.Parse("#333333");
                tf1.TextState.FontSize = 12;
                tf1.TextState.FontStyle = FontStyles.Bold;
                pdfPage.Paragraphs.Add(tf1);

                tab1.RepeatingRowsCount = 1;
                Aspose.Pdf.Row row = tab1.Rows.Add();
                tf1 = new TextFragment(TranslationUtility.Translate("Category", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                Aspose.Pdf.Cell tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.Margin = tableHeadersMargin;
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                row.Cells.Add(tableHeaderCell);
                row.Cells[0].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("ReferenceNumber", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[1].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("CreatedBy", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[2].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("CreatedDate", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[3].Paragraphs.Add(tf1);

                tf1 = new TextFragment(TranslationUtility.Translate("Status", language));
                tf1.TextState.FontSize = 10;
                tf1.TextState.FontStyle = FontStyles.Bold;
                tf1.TextState.ForegroundColor = Color.Parse("#555555");
                tableHeaderCell = new Aspose.Pdf.Cell();
                tableHeaderCell.BackgroundColor = Color.Parse("#eeeeee");
                tableHeaderCell.Alignment = HorizontalAlignment.Center;
                tableHeaderCell.Margin = tableHeadersMargin;
                row.Cells.Add(tableHeaderCell);
                row.Cells[4].Paragraphs.Add(tf1);

                for (int i = 0; i < model.Count; i++)
                {
                    Aspose.Pdf.Row rowModel = tab1.Rows.Add();
                    TextFragment cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].Category;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[0].Paragraphs.Add(cellText);

                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].LinkedDocumentReferenceNumber != null ? model[i].LinkedDocumentReferenceNumber : "";
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[1].Paragraphs.Add(cellText);

                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].LinkedBy;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[2].Paragraphs.Add(cellText);

                    string cellTextCreatedDate = model[i].CreatedDate;
                    if (Configuration.CalendarType != CalendarType.None)
                    {
                        var createdDate = DateTime.ParseExact(model[i].CreatedDate, Constants.DATE_FORMAT, CultureInfo.InvariantCulture);
                        cellTextCreatedDate = Intalio.Core.Helper.ConvertGregorianToHijri(createdDate, Constants.DATE_FORMAT);
                    }
                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = cellTextCreatedDate;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[3].Paragraphs.Add(cellText);

                    cellText = new TextFragment();
                    cellText.TextState.ForegroundColor = Color.Parse("#555555");
                    cellText.Text = model[i].Status;
                    rowModel.Cells.Add().Alignment = HorizontalAlignment.Center;
                    rowModel.Cells[4].Paragraphs.Add(cellText);
                }
                pdfPage.Paragraphs.Add(tab1);
            }
            return document;
        }

        #endregion

        #region Pdf License

        private static void InitPdfLicense()
        {
            Stream stream = new Intalio.Core.Utility.AsposeLicense().Get();
            if (stream != null)
            {
                new Aspose.Pdf.License().SetLicense(stream);
            }
        }

        #endregion

        #region Export Excel

        private static void CreateExcelTitleDocument(Aspose.Cells.Cells cells, string reportTitle)
        {
            cells.Merge(0, 0, 2, 5);
            cells.Merge(2, 0, 1, 5);
            // Setting the value to the cells
            Aspose.Cells.Cell cell = cells["A1"];
            cell.PutValue(reportTitle);
            var style = cell.GetStyle();
            style.VerticalAlignment = TextAlignmentType.Center;
            style.HorizontalAlignment = TextAlignmentType.Center;
            style.IsTextWrapped = true;
            style.Font.Size = 15;
            style.Font.IsBold = true;
            style.Font.Color = System.Drawing.ColorTranslator.FromHtml("#00AE8D");
            cell.SetStyle(style);

            cell = cells["A3"];
            style = cell.GetStyle();
            cell.PutValue(string.Empty);
            style.VerticalAlignment = TextAlignmentType.Center;
            style.HorizontalAlignment = TextAlignmentType.Center;
            style.IsTextWrapped = true;
            style.Font.Size = 10;
            style.Font.IsItalic = true;
            style.Font.Color = System.Drawing.ColorTranslator.FromHtml("#444444");
            style.SetBorder(BorderType.TopBorder, CellBorderType.Thick, System.Drawing.Color.White);
            cell.SetStyle(style);
            cell = cells["B3"];
            cell.SetStyle(style);
            cell = cells["C3"];
            cell.SetStyle(style);
            cell = cells["D3"];
            cell.SetStyle(style);
            cell = cells["E3"];
            cell.SetStyle(style);
        }

        private static async Task<bool> ExportExcelCorrespondenceDetail(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Aspose.Cells.Cells cells, long? delegationId = null, Language language = Language.EN)
        {
            var model = await GetDocumentCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId, language);
            if (model != null)
            {
                int row = 4;
                cells.Merge(row - 1, 0, 1, 5);
                // Setting the value to the cells
                Aspose.Cells.Cell cell = cells["A" + row];
                cell.PutValue(TranslationUtility.Translate("Correspondence", language));
                cell.SetStyle(FormatTitle(cell.GetStyle(), "#333333"));

                //1,2
                cells.Merge(row, 0, 1, 2);
                cells.Merge(row, 2, 1, 3);
                row++;
                cell = cells["A" + row];
                cell.PutValue(TranslationUtility.Translate("ReferenceNumber", language));
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee", "#555555", IsBold: true));
                cell = cells["B" + row];
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee"));
                cell = cells["C" + row];
                cell.SetStyle(FormatCell(cell.GetStyle(), "#555555"));
                cell.PutValue(model.ReferenceNumber);

                cells.Merge(row, 0, 1, 2);
                cells.Merge(row, 2, 1, 3);
                row++;
                cell = cells["A" + row];
                cell.PutValue(TranslationUtility.Translate("SendingEntity", language));
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee", "#555555", IsBold: true));
                cell = cells["B" + row];
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee"));
                cell = cells["C" + row];
                cell.SetStyle(FormatCell(cell.GetStyle(), "#555555"));
                cell.PutValue(model.SendingEntity);

                cells.Merge(row, 0, 1, 2);
                cells.Merge(row, 2, 1, 3);
                row++;
                cell = cells["A" + row];
                cell.PutValue(TranslationUtility.Translate("ReceivingEntity", language));
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee", "#555555", IsBold: true));
                cell = cells["B" + row];
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee"));
                cell = cells["C" + row];
                cell.SetStyle(FormatCell(cell.GetStyle(), "#555555"));
                cell.PutValue(model.ReceivingEntity);
                //4
                cells.Merge(row, 0, 1, 2);
                cells.Merge(row, 2, 1, 3);
                row++;
                cell = cells["A" + row];
                cell.PutValue(TranslationUtility.Translate("CreatedBy", language));
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee", "#555555", IsBold: true));
                cell = cells["B" + row];
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee"));
                cell = cells["C" + row];
                cell.SetStyle(FormatCell(cell.GetStyle(), "#555555"));
                cell.PutValue(model.CreatedBy);

                cells.Merge(row, 0, 1, 2);
                cells.Merge(row, 2, 1, 3);
                row++;
                cell = cells["A" + row];
                cell.PutValue(TranslationUtility.Translate("Priority", language));
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee", "#555555", IsBold: true));
                cell = cells["B" + row];
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee"));
                cell = cells["C" + row];
                cell.SetStyle(FormatCell(cell.GetStyle(), "#555555"));
                cell.PutValue(model.Priority);

                cells.Merge(row, 0, 1, 2);
                cells.Merge(row, 2, 1, 3);
                row++;//7
                cell = cells["A" + row];
                cell.PutValue(TranslationUtility.Translate("Privacy", language));
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee", "#555555", IsBold: true));
                cell = cells["B" + row];
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee"));
                cell = cells["C" + row];
                cell.SetStyle(FormatCell(cell.GetStyle(), "#555555"));
                cell.PutValue(model.Privacy);

                cells.Merge(row, 0, 1, 2);
                cells.Merge(row, 2, 1, 3);
                row++;
                cell = cells["A" + row];
                cell.PutValue(TranslationUtility.Translate("RegisterDate", language));
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee", "#555555", IsBold: true));
                cell = cells["B" + row];
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee"));
                cell = cells["C" + row];
                cell.SetStyle(FormatCell(cell.GetStyle(), "#555555"));
                string cellTextRegisterDate = model.RegisterDate;
                if (Configuration.CalendarType != CalendarType.None)
                {
                    var createdDate = DateTime.ParseExact(model.RegisterDate, Constants.DATE_FORMAT, CultureInfo.InvariantCulture);
                    cellTextRegisterDate = Intalio.Core.Helper.ConvertGregorianToHijri(createdDate, Constants.DATE_FORMAT);
                }
                cell.PutValue(cellTextRegisterDate);

                cells.Merge(row, 0, 1, 2);
                cells.Merge(row, 2, 1, 3);
                row++;
                cell = cells["A" + row];
                cell.PutValue(TranslationUtility.Translate("CarbonCopies", language));
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee", "#555555", IsBold: true));
                cell = cells["B" + row];
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee"));
                cell = cells["C" + row];
                cell.SetStyle(FormatCell(cell.GetStyle(), "#555555"));
                cell.PutValue(model.CarbonCopies);

                cells.Merge(row, 0, 1, 2);
                cells.Merge(row, 2, 1, 3);
                row++;
                cell = cells["A" + row];
                cell.PutValue(TranslationUtility.Translate("Subject", language));
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee", "#555555", IsBold: true));
                cell = cells["B" + row];
                cell.SetStyle(Format(cell.GetStyle(), "#eeeeee"));
                cell = cells["C" + row];
                cell.SetStyle(FormatCell(cell.GetStyle(), "#555555"));
                cell.PutValue(model.Subject);
                return true;
            }
            return false;
        }

        private static async Task<int> ExportExcelTransferCorrespondenceDetail(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, int startRow, Aspose.Cells.Cells cells, long? delegationId = null, Language language = Language.EN)
        {
            List<TransferCorrespondenceDetailListViewModel> model = (await ManageReport.ListTransferCorrespondenceDetailRetrievingAllData(documentId, userId, structureIds, isStructureReceiver, privacyLevel, null, delegationId, language)).Item2;
            int retValue = 0;
            if (model.Any())
            {
                retValue = model.Count + 4;
                cells.Merge(startRow, 0, 1, 5);
                // Setting the value to the cells
                Aspose.Cells.Cell cell = cells["A" + (startRow + 1)];
                cell.PutValue(TranslationUtility.Translate("Transfer", language));
                cell.SetStyle(FormatTitle(cell.GetStyle(), "#333333"));

                startRow++;
                cells[startRow, 0].PutValue(TranslationUtility.Translate("FromStructure", language));
                cells[startRow, 1].PutValue(TranslationUtility.Translate("FromUser", language));
                cells[startRow, 2].PutValue(TranslationUtility.Translate("ToStructure", language));
                cells[startRow, 3].PutValue(TranslationUtility.Translate("ToUser", language));
                cells[startRow, 4].PutValue(TranslationUtility.Translate("TransferDate", language));
                FormatCells(startRow, cells, FormatCellTable(cells[startRow, 0].GetStyle(), "#eeeeee", isBold: true), 4);

                for (int i = 1; i <= model.Count; i++)
                {
                    cells[startRow + i, 0].PutValue(model[i - 1].FromStructure);
                    cells[startRow + i, 1].PutValue(model[i - 1].FromUser);
                    cells[startRow + i, 2].PutValue(model[i - 1].ToStructure);
                    cells[startRow + i, 3].PutValue(model[i - 1].ToUser);
                    string cellTextCreatedDate = model[i - 1].CreatedDate;
                    if (Configuration.CalendarType != CalendarType.None)
                    {
                        var createdDate = DateTime.ParseExact(model[i - 1].CreatedDate, Constants.DATE_FORMAT, CultureInfo.InvariantCulture);
                        cellTextCreatedDate = Intalio.Core.Helper.ConvertGregorianToHijri(createdDate, Constants.DATE_FORMAT);
                    }
                    cells[startRow + i, 4].PutValue(cellTextCreatedDate);
                    if (i % 2 == 0)
                    {
                        FormatCells(startRow + i, cells, FormatCellTable(cells[startRow + i, 0].GetStyle()), 3);
                        cells[startRow + i, 4].SetStyle(FormatCellTable(cells[startRow + i, 0].GetStyle(), isLast: true));
                    }
                    else
                    {
                        FormatCells(startRow + i, cells, FormatCellTable(cells[startRow + i, 0].GetStyle(), "#FFFFFF"), 3);
                        cells[startRow + i, 4].SetStyle(FormatCellTable(cells[startRow + i, 0].GetStyle(), "#FFFFFF", true));
                    }
                }
            }
            return retValue;
        }

        private static async Task<int> ExportExcelNoteListCorrespondenceDetail(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, int startRow, Aspose.Cells.Cells cells, long? delegationId, Language language)
        {
            List<NoteListViewModel> model = (await ManageReport.ListNoteCorrespondenceDetailRetrievingAllData(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId)).Item2;
            int retValue = 0;
            if (model.Any())
            {
                retValue = model.Count + 4;
                System.Text.RegularExpressions.Regex rx = new System.Text.RegularExpressions.Regex("<[^>]*>");
                cells.Merge(startRow, 0, 1, 5);
                // Setting the value to the cells
                Aspose.Cells.Cell cell = cells["A" + (startRow + 1)];
                cell.PutValue(TranslationUtility.Translate("Notes", language));
                cell.SetStyle(FormatTitle(cell.GetStyle(), "#333333"));

                startRow++;
                cells[startRow, 0].PutValue(TranslationUtility.Translate("CreatedBy", language));
                cells[startRow, 1].PutValue(TranslationUtility.Translate("CreatedDate", language));
                cells[startRow, 2].PutValue(TranslationUtility.Translate("Private", language));
                cells[startRow, 3].PutValue(TranslationUtility.Translate("Notes", language));
                FormatCells(startRow, cells, FormatCellTable(cells[startRow, 0].GetStyle(), "#eeeeee", isBold: true), 4);

                for (int i = 1; i <= model.Count; i++)
                {
                    cells[startRow + i, 0].PutValue(model[i - 1].CreatedBy);
                    string cellTextCreatedDate = model[i - 1].CreatedDate;
                    if (Configuration.CalendarType != CalendarType.None)
                    {
                        var createdDate = DateTime.ParseExact(model[i - 1].CreatedDate, Constants.DATE_FORMAT, CultureInfo.InvariantCulture);
                        cellTextCreatedDate = Intalio.Core.Helper.ConvertGregorianToHijri(createdDate, Constants.DATE_FORMAT);
                    }
                    cells[startRow + i, 1].PutValue(cellTextCreatedDate);
                    cells[startRow + i, 2].PutValue(model[i - 1].IsPrivate ? "✔" : "✖");
                    cells[startRow + i, 3].PutValue(rx.Replace(model[i - 1].Notes, ""));
                    cells.Merge(startRow + i, 3, 1, 2);
                    if (i % 2 == 0)
                    {
                        FormatCells(startRow + i, cells, FormatCellTable(cells[startRow + i, 0].GetStyle(), isTextWrapped: false), 3);
                        cells[startRow + i, 4].SetStyle(FormatCellTable(cells[startRow + i, 0].GetStyle(), isLast: true));
                    }
                    else
                    {
                        FormatCells(startRow + i, cells, FormatCellTable(cells[startRow + i, 0].GetStyle(), "#FFFFFF", isTextWrapped: false), 3);
                        cells[startRow + i, 4].SetStyle(FormatCellTable(cells[startRow + i, 0].GetStyle(), "#FFFFFF", true));
                    }
                }
            }
            return retValue;
        }

        private static async Task<int> ExportExcelNonArchivedAttachmentsList(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, int startRow, Aspose.Cells.Cells cells, long? delegationId, Language language)
        {
            List<NonArchivedAttachmentsListViewModel> model = (await ManageReport.ListNonArchivedAttachmentsCorrespondenceDetailRetrievingAllData(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId, language)).Item2;
            int retValue = 0;
            if (model.Any())
            {
                retValue = model.Count + 4;
                System.Text.RegularExpressions.Regex rx = new System.Text.RegularExpressions.Regex("<[^>]*>");
                cells.Merge(startRow, 0, 1, 5);
                // Setting the value to the cells
                Aspose.Cells.Cell cell = cells["A" + (startRow + 1)];
                cell.PutValue(TranslationUtility.Translate("NonArchivedAttachments", language));
                cell.SetStyle(FormatTitle(cell.GetStyle(), "#333333"));

                startRow++;
                cells[startRow, 0].PutValue(TranslationUtility.Translate("Type", language));
                cells[startRow, 1].PutValue(TranslationUtility.Translate("Quantity", language));
                cells[startRow, 2].PutValue(TranslationUtility.Translate("CreatedBy", language));
                cells[startRow, 3].PutValue(TranslationUtility.Translate("Description", language));
                FormatCells(startRow, cells, FormatCellTable(cells[startRow, 0].GetStyle(), "#eeeeee", isBold: true), 4);

                for (int i = 1; i <= model.Count; i++)
                {
                    cells[startRow + i, 0].PutValue(model[i - 1].Type);
                    cells[startRow + i, 1].PutValue(model[i - 1].Quantity);
                    cells[startRow + i, 2].PutValue(model[i - 1].CreatedBy);
                    cells[startRow + i, 3].PutValue(rx.Replace(string.IsNullOrEmpty(model[i - 1].Description) ? "" : model[i - 1].Description, ""));
                    cells.Merge(startRow + i, 3, 1, 2);
                    if (i % 2 == 0)
                    {
                        FormatCells(startRow + i, cells, FormatCellTable(cells[startRow + i, 0].GetStyle(), isTextWrapped: false), 3);
                        cells[startRow + i, 4].SetStyle(FormatCellTable(cells[startRow + i, 0].GetStyle(), isLast: true));
                    }
                    else
                    {
                        FormatCells(startRow + i, cells, FormatCellTable(cells[startRow + i, 0].GetStyle(), "#FFFFFF", isTextWrapped: false), 3);
                        cells[startRow + i, 4].SetStyle(FormatCellTable(cells[startRow + i, 0].GetStyle(), "#FFFFFF", true));
                    }
                }
            }
            return retValue;
        }

        private static async Task<bool> ExportExcelLinkedDocumentList(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, int startRow, Aspose.Cells.Cells cells, long? delegationId, Language language)
        {
            List<LinkedDocumentListViewModel> model = await ListLinkedDocumentCorrespondenceDetail(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId, language);
            if (model != null && model.Any())
            {
                System.Text.RegularExpressions.Regex rx = new System.Text.RegularExpressions.Regex("<[^>]*>");
                cells.Merge(startRow, 0, 1, 5);
                // Setting the value to the cells
                Aspose.Cells.Cell cell = cells["A" + (startRow + 1)];
                cell.PutValue(TranslationUtility.Translate("LinkedCorrespondence", language));
                cell.SetStyle(FormatTitle(cell.GetStyle(), "#333333"));

                startRow++;
                cells[startRow, 0].PutValue(TranslationUtility.Translate("Category", language));
                cells[startRow, 1].PutValue(TranslationUtility.Translate("ReferenceNumber", language));
                cells[startRow, 2].PutValue(TranslationUtility.Translate("CreatedBy", language));
                cells[startRow, 3].PutValue(TranslationUtility.Translate("CreatedDate", language));
                cells[startRow, 4].PutValue(TranslationUtility.Translate("Status", language));
                FormatCells(startRow, cells, FormatCellTable(cells[startRow, 0].GetStyle(), "#eeeeee", isBold: true), 4);

                for (int i = 1; i <= model.Count; i++)
                {
                    cells[startRow + i, 0].PutValue(model[i - 1].Category);
                    cells[startRow + i, 1].PutValue(model[i - 1].LinkedDocumentReferenceNumber);
                    cells[startRow + i, 2].PutValue(model[i - 1].LinkedBy);

                    string cellTextCreatedDate = model[i - 1].CreatedDate;
                    if (Configuration.CalendarType != CalendarType.None)
                    {
                        var createdDate = DateTime.ParseExact(model[i - 1].CreatedDate, Constants.DATE_FORMAT, CultureInfo.InvariantCulture);
                        cellTextCreatedDate = Intalio.Core.Helper.ConvertGregorianToHijri(createdDate, Constants.DATE_FORMAT);
                    }
                    cells[startRow + i, 3].PutValue(cellTextCreatedDate);
                    cells[startRow + i, 4].PutValue(model[i - 1].Status);
                    if (i % 2 == 0)
                    {
                        FormatCells(startRow + i, cells, FormatCellTable(cells[startRow + i, 0].GetStyle(), isTextWrapped: false), 3);
                        cells[startRow + i, 4].SetStyle(FormatCellTable(cells[startRow + i, 0].GetStyle(), isLast: true));
                    }
                    else
                    {
                        FormatCells(startRow + i, cells, FormatCellTable(cells[startRow + i, 0].GetStyle(), "#FFFFFF", isTextWrapped: false), 3);
                        cells[startRow + i, 4].SetStyle(FormatCellTable(cells[startRow + i, 0].GetStyle(), "#FFFFFF", true));
                    }
                }
                return true;
            }
            return false;
        }

        private static Style Format(Style style, string backgroundColor = null, string color = null, bool IsBold = false)
        {
            // Setting the vertical alignment
            style.VerticalAlignment = TextAlignmentType.Center;
            // Setting the horizontal alignment
            style.HorizontalAlignment = TextAlignmentType.Center;
            if (!string.IsNullOrEmpty(backgroundColor))
            {
                style.ForegroundColor = System.Drawing.ColorTranslator.FromHtml(backgroundColor);
                style.Pattern = BackgroundType.Solid;
            }
            if (!string.IsNullOrEmpty(color))
            {
                style.Font.Color = System.Drawing.ColorTranslator.FromHtml(color);
            }
            style.SetBorder(BorderType.TopBorder, CellBorderType.Thin, System.Drawing.Color.LightGray);
            style.SetBorder(BorderType.RightBorder, CellBorderType.Thin, System.Drawing.Color.LightGray);
            style.SetBorder(BorderType.BottomBorder, CellBorderType.Thin, System.Drawing.Color.LightGray);
            style.SetBorder(BorderType.LeftBorder, CellBorderType.Thin, System.Drawing.Color.LightGray);

            style.IsTextWrapped = true;
            style.Font.IsBold = IsBold;

            return style;
        }

        private static Style FormatTitle(Style style, string color = "")
        {
            style.VerticalAlignment = TextAlignmentType.Center;
            style.HorizontalAlignment = TextAlignmentType.Left;
            style.IsTextWrapped = true;
            style.Font.Size = 11;
            style.Font.IsBold = true;
            if (!string.IsNullOrEmpty(color))
            {
                style.Font.Color = System.Drawing.ColorTranslator.FromHtml(color);
            }
            return style;
        }

        private static Style FormatCell(Style style, string color = "")
        {
            style.VerticalAlignment = TextAlignmentType.Center;
            style.HorizontalAlignment = TextAlignmentType.Center;
            style.IsTextWrapped = true;
            if (!string.IsNullOrEmpty(color))
            {
                style.Font.Color = System.Drawing.ColorTranslator.FromHtml(color);
            }
            return style;
        }

        private static Style FormatCellTable(Style style, string color = "#fafbfc", bool isLast = false, bool isTextWrapped = true, bool isBold = false)
        {
            style.ForegroundColor = System.Drawing.ColorTranslator.FromHtml(color);
            style.Pattern = BackgroundType.Solid;
            style.SetBorder(BorderType.TopBorder, CellBorderType.Thin, System.Drawing.Color.LightGray);
            style.SetBorder(BorderType.RightBorder, CellBorderType.Thin, System.Drawing.Color.LightGray);
            style.SetBorder(BorderType.BottomBorder, CellBorderType.Thin, System.Drawing.Color.LightGray);
            style.SetBorder(BorderType.LeftBorder, CellBorderType.Thin, System.Drawing.Color.LightGray);

            style.Font.Color = System.Drawing.ColorTranslator.FromHtml("#555555");
            style.IsTextWrapped = isTextWrapped;
            style.VerticalAlignment = TextAlignmentType.Center;
            style.HorizontalAlignment = TextAlignmentType.Left;
            style.Font.IsBold = isBold;
            return style;
        }

        private static void FormatCells(int startRow, Aspose.Cells.Cells cells, Style style, int numberOfColumn)
        {
            for (int i = 0; i <= numberOfColumn; i++)
            {
                cells[startRow, i].SetStyle(style);
            }
        }

        #endregion

        #region Excel License

        private static void InitExcelLicense()
        {
            Stream stream = new Intalio.Core.Utility.AsposeLicense().Get();
            if (stream != null)
            {
                new Aspose.Cells.License().SetLicense(stream);
            }
        }

        #endregion

        #endregion
    }
}
