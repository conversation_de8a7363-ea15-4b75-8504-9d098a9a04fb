﻿--g2g script

-- Change the name of G2G 
-- Change the name of IAM 
-- Change the name of CTS

use CTS
go



CREATE FUNCTION [dbo].[checkOverDue]
( 
    @DateValue  datetime
) 
RETURNS bit 
AS 
BEGIN 
    DECLARE @retValue bit 
    if(CAST(@DateValue AS DATE) <  CAST(GETDATE() AS DATE))
    set @retValue = 1
    else
    set @retValue = 0
    
    RETURN @retValue 
END
GO

ALTER TABLE Document
ADD G2GDocumentId bigint null;
Go


if not exists (select 1 from [Node] where name=N'G2G')
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'G2G', CAST(N'2024-07-15T20:32:39.0100070' AS DateTime2), NULL, NULL, NULL, 10, N'fa fa-bitbucket-square', N'Custom', NULL, 1, 1, 1, NULL, Null, NULL)
GO


declare @parentNdeid int = (select id from node where name=N'G2G')

INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'Queued', CAST(N'2024-07-15T20:32:39.0100070' AS DateTime2), @parentNdeid, NULL, NULL, 10, N'fa fa-bitbucket-square', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("Queued"),GetG2GCount("Queued"),GetG2GCount("Queued")', NULL)
 
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'IncomingRecalled', CAST(N'2024-07-16T13:39:54.1636565' AS DateTime2), @parentNdeid, NULL, NULL, 11, N'fa fa-mail-reply-all', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("IncomingRecalled"),GetG2GCount("IncomingRecalled"),GetG2GCount("IncomingRecalled")', NULL)
 
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'IncomingRejected', CAST(N'2024-07-17T11:05:13.7540813' AS DateTime2), @parentNdeid, NULL, NULL, 12, N'fa fa-angle-double-left', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("IncomingRejected"),GetG2GCount("IncomingRejected"),GetG2GCount("IncomingRejected")', NULL)
 
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'SentFromStructure', CAST(N'2024-07-17T15:03:33.1827023' AS DateTime2), @parentNdeid, NULL, NULL, 13, N'fa fa-send', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("SentFromStructure"),GetG2GCount("SentFromStructure"),GetG2GCount("SentFromStructure")', NULL)
 
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'ReceiveOrReject', CAST(N'2024-07-17T15:38:47.4275289' AS DateTime2), @parentNdeid, NULL, NULL, 14, N'fa fa-recycle', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("ReceiveOrReject"),GetG2GCount("ReceiveOrReject"),GetG2GCount("ReceiveOrReject")', NULL)
 
--INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'PendingToReceive', CAST(N'2024-07-17T16:07:33.5733881' AS DateTime2), @parentNdeid, NULL, NULL, 15, N'fa fa-warning', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("G2G_DocumentInboxPendingToReceive"),GetG2GCount("G2G_DocumentInboxPendingToReceive"),GetG2GTodayCount("G2G_DocumentInboxPendingToReceive")', NULL)
 
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'Recalled', CAST(N'2024-07-17T17:51:24.8732797' AS DateTime2), @parentNdeid, NULL, NULL, 15, N'fa fa-repeat', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("Recalled"),GetG2GCount("Recalled"),GetG2GCount("Recalled")', NULL)
 
INSERT [dbo].[Node] ( [Name], [CreatedDate], [ParentNodeId], [Filters], [Columns], [Order], [Icon], [Inherit], [IsSearch], [Visible], [EnableTodayCount], [EnableTotalCount], [Conditions], [CustomFunctions], [EnableUnreadCount]) VALUES ( N'Rejected', CAST(N'2024-07-17T18:07:12.7360742' AS DateTime2), @parentNdeid, NULL, NULL, 16, N'fa fa-close', N'Custom', NULL, 1, 1, 1, NULL, N'CTSCoreComponents.CustomNodes.g2g("Rejected"),GetG2GCount("Rejected"),GetG2GCount("Rejected")', NULL)
 
GO


declare @parentNdeid int = (select id from node where name=N'G2G')


INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'Queued' and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'Queued'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'Queued'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'Queued'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] ='IncomingRecalled'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'IncomingRejected'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'IncomingRejected'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'IncomingRejected'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'IncomingRejected'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'MyTransfer'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'SentFromStructure'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'Recalled'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'Recalled'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ((select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'Recalled'  and ParentNodeId=@parentNdeid ))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'Recalled'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ((select id from [dbo].[Tab] where [Name] = 'VisualTracking'), (select id from [dbo].[Node] where [Name] = 'Rejected'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Notes'), (select id from [dbo].[Node] where [Name] = 'Rejected'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ( (select id from [dbo].[Tab] where [Name] = 'Attachments'), (select id from [dbo].[Node] where [Name] = 'Rejected'  and ParentNodeId=@parentNdeid))
 
INSERT [dbo].[TabsNodes] ([TabId], [NodeId]) VALUES ((select id from [dbo].[Tab] where [Name] = 'Attributes'), (select id from [dbo].[Node] where [Name] = 'Rejected'  and ParentNodeId=@parentNdeid))
 

go
--declare @parentNdeid int = (select id from node where name=N'G2G')


--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Queued'  and ParentNodeId=@parentNdeid), 1, NULL, NULL)
 
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES (  (select id from [dbo].[Node] where [Name] = 'IncomingRecalled'  and ParentNodeId=@parentNdeid), 1, NULL, NULL)
 
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'IncomingRejected'  and ParentNodeId=@parentNdeid), 1, NULL, NULL)
 
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'SentFromStructure'  and ParentNodeId=@parentNdeid), 1, NULL, NULL)
 
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'SentFromStructure'  and ParentNodeId=@parentNdeid), 2, NULL, NULL)
  
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'SentFromStructure'  and ParentNodeId=@parentNdeid), 3, NULL, NULL)
 
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'SentFromStructure'  and ParentNodeId=@parentNdeid), 4, NULL, NULL)
 
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'SentFromStructure'  and ParentNodeId=@parentNdeid), 5, NULL, NULL)
 
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'ReceiveOrReject'  and ParentNodeId=@parentNdeid), 1, NULL, NULL)
 
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Recalled'  and ParentNodeId=@parentNdeid), 1, NULL, NULL)
 
--INSERT [dbo].[NodeSecurity] ([NodeId], [RoleId], [UserId], [StructureId]) VALUES ( (select id from [dbo].[Node] where [Name] = 'Rejected'  and ParentNodeId=@parentNdeid), 1, NULL, NULL)
--GO

--INSERT [dbo].[Menu] ( [Name], [Icon], [Order], [ParentMenuId], [MenuTypeId], [Title], [Url], [UrlType], [Html], [IframeHeight], [JsFunction], [OpenInModal], [OpenInIframe], [Type], [CreatedByUserId], [CreatedDate], [ModifiedDate]) VALUES ( N'G2G', N'fa fa-500px', 1, NULL, 1, NULL, N'#g2gOrganizationMapping', 2, NULL, NULL, NULL, 0, NULL, 2, 1, CAST(N'2024-07-14T18:25:42.6381681' AS DateTime2), CAST(N'2024-07-14T18:50:17.7861421' AS DateTime2))
--GO

if not exists (select 1 from [Parameter] where Keyword=N'g2GUrl')
INSERT INTO [dbo].[Parameter]
           ([Keyword]
           ,[Description]
           ,[Content]
		   ,[IsSystem])
     VALUES
           ('g2GUrl'
           ,'G2G url to export'
           ,'http://localhost:8965/G2GApplication.Web'
		   ,0)
GO

if not exists (select 1 from [Parameter] where Keyword=N'g2GService')
INSERT INTO [dbo].[Parameter]
           ([Keyword]
           ,[Description]
           ,[Content]
		   ,[IsSystem])
     VALUES
           ('g2GService'
           ,'G2G wcf service url to export'
           ,'http://localhost:8965/G2GApplication.WCFService'
		   ,0)
GO

if not exists (select 1 from [TranslatorDictionary] where Keyword=N'G2GRefNo')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'G2GRefNo', N'G2G Ref No', N'G2G Ref No', N'G2G Ref No', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'Queued')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Queued', N'Queued', N'Queued', N'في قائمة الانتظار', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'IncomingRecalled')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'IncomingRecalled', N'Incoming Recalled', N'Incoming Recalled', N'تم التذكير بالوارد', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'IncomingRejected')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'IncomingRejected', N'Incoming Rejected', N'Incoming Rejected', N'الواردة مرفوضة', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'SentFromStructure')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'SentFromStructure', N'Sent From Structure', N'Sent From Structure', N'مرسل من الهيكل', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'ReceiveOrReject')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'ReceiveOrReject', N'Receive Or Reject', N'Receive Or Reject', N'تلقي أو رفض', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'PendingToReceive')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'PendingToReceive', N'Pending To Receive', N'Pending To Receive', N'في انتظار الاستلام', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'Recalled')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Recalled', N'Recalled', N'Recalled', N'استدعى', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'Rejected')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Rejected', N'Rejected', N'Rejected', N'مرفوض', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'Recall')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Recall', N'Recall', N'Recall', N'استدعى', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'Resend')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Resend', N'Resend', N'Resend', N'إعادة إرسال', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'StopSending')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'StopSending', N'Stop Sending', N'Stop Sending', N'توقف عن الإرسال', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'Receive')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Receive', N'Receive', N'Receive', N'تسلم', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'Reject')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Reject', N'Reject', N'Reject', N'رفض', 0)
GO
if not exists (select 1 from [TranslatorDictionary] where Keyword=N'Fetch')
INSERT [dbo].[TranslatorDictionary] ( [Keyword], [EN], [FR], [AR], [IsSystem]) VALUES ( N'Fetch', N'Fetch', N'Fetch', N'أحضر', 0)
GO

declare @ActionId bigint;

if not exists (select 1 from [Action] where name='G2GAction')
INSERT INTO [dbo].[Action] ([Name], [Icon], [Color], [Tooltip], [Type], [JsFunction], [ShowInReadMode], [ShowInEditMode], [CreatedByUserId], [CreatedDate], [CategoryIds])
     select N'G2GAction', N'fa fa-outdent', N'#00AE8D', N'G2GAction', 4, N'CTSCoreComponents.CustomActions.G2GAction()', 1, 1, 1, GETDATE(), Category.Id
	 from Category
	 where [Name] like N'%Outgoing%'

select @ActionId = @@IDENTITY

--INSERT INTO [dbo].[ActionSecurity] ([ActionId], [RoleId])
--select @ActionId, [Role].Id
--from IAM.[dbo].[Role]

--INSERT INTO [dbo].[ActionsNodes] ([ActionId], [NodeId])
--select @ActionId, [Node].Id
--from [Node] 
--inner join [Node] as parentNode on (parentNode.[Name] = N'Inbox' or parentNode.[Name] = N'Draft')
--where parentNode.Id = [Node].ParentNodeId and [Node].[Name] like N'%Outgoing%' 

Go

if not exists (select 1 from [TranslatorDictionary] where Keyword=N'G2GAction')
INSERT INTO [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) VALUES (N'G2GAction', N'G2G Action', N'G2G Action', N'G2G Action', 0)
Go

-- vw_All_Contacts_Structures --

if exists(select 1 from sys.views where name='vw_All_Contacts_Structures' and type='v')
    drop view [vw_All_Contacts_Structures];
go

create VIEW [dbo].[vw_All_Contacts_Structures]
AS
SELECT [Id], [Name] AS ToEntity, 'S' as  Ab_TYPE
FROM dbo.Structure
UNION ALL
(SELECT Id, COALESCE (Firstname, '') + ' ' + COALESCE (Lastname, '') AS ToEntity, 'C' as Ab_TYPE 
FROM dbo.[User])
GO

-- usp_SelectStructureListFromCTSAddressBook --

IF EXISTS (SELECT * 
            FROM sysobjects 
            WHERE id = object_id(N'[dbo].[usp_SelectStructureListFromCTSAddressBook]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
begin
    drop procedure  [dbo].[usp_SelectStructureListFromCTSAddressBook];
end
go

create PROCEDURE [dbo].[usp_SelectStructureListFromCTSAddressBook]
(@isInternal bit)
AS
BEGIN
    select Id, isnull((select [Name] + ' ('+ [NameAr] +') ' from dbo.Structure where Id = isnull(ParentId,''))+' / ' , '') + 
    [Name] + ' ('+ [NameAr] +')'  + ' | ' +  convert(nvarchar(10), Id) as FULLNAME,
    [Name] as [NAME]
    from dbo.Structure
    where [IsExternal] <> @isInternal
END
go

-- usp_SelectFromCTSByGCTID --

IF EXISTS (SELECT * 
            FROM sysobjects 
            WHERE id = object_id(N'[dbo].[usp_SelectFromCTSByGCTID]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
begin
    drop procedure  [dbo].[usp_SelectFromCTSByGCTID];
end
go

CREATE PROCEDURE usp_SelectFromCTSByGCTID (@gctId int)
AS
BEGIN
    declare @cntId int
    declare @name nvarchar(300)
    declare @stcId int
    select @cntId = Id from dbo.[User] where Id = @gctId
	select @stcId = Id from dbo.[Structure] where Id = @gctId

    if @cntId is null
    begin
        select [Name] as [Name], [NameAr] as [NameAr]
		from dbo.[Structure]
		where Id = @gctId
    end
    else
    begin
        select coalesce([Firstname], '') + ' ' + coalesce([Lastname], '') as [Name], coalesce([FirstnameAr], '') + ' ' + coalesce([LastnameAr], '') as [NameAr]
		from dbo.[User]
		where Id = @gctId
    end
END
GO

-- G2G_DocumentInbox --

if exists(select 1 from sys.views where name='G2G_DocumentInbox' and type='v')
    drop view [G2G_DocumentInbox];
go

CREATE VIEW [dbo].[G2G_DocumentInbox]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT
    g2gdoc.ID G2G_Internal_ID,
    CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 1) AS SMALLINT)  DOC_CATEGORY,
    dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
    CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
    docStatus.[Name] AS STATUS,
    COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE,
    dbo.Document.[Subject] AS SUBJECT,
    '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
    dbo.Document.PrivacyId AS PRIVACYLEVEL,
    '' AS TAGS,
    dbo.Document.Form,
    '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15,
    dbo.Document.CreatedDate AS DOCUMENTDATE,
    dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom,
    STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
    STRING_AGG(CAST(ReceiverEntity.Name AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT,
    dbo.Priority.Name AS DOC_PRIORITY,
    tsf.StatusId AS DOC_TRANSFER_STATUS,
	CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
    dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
    dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
    dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
    dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
    CAST(dbo.Privacy.Id AS SMALLINT)As Id,
     dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
    CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
    CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
    dbo.Document.DocumentTypeId AS DOC_TYPE,
    CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
    dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103) AS REGISTERDATE,
    dbo.Privacy.[Name] AS PrivacyState,
    SenderEntity.[Name] AS DOC_SENDER_FULL_NAME,
    0 AS AttachmentsCount,
    g2gOu1.Name AS MainFromAr,
    g2gOu1.EnName AS MainFrom,
    g2gOu2.Name AS SubFromAr,
    g2gOu2.EnName AS SubFrom,
    g2gOu3.Name AS MainToAr,
    g2gOu3.EnName AS MainTo,
    g2gOu4.Name AS SubToAr,
    g2gOu4.EnName AS SubTo,
	 COALESCE(CAST(g2gOu3.GCT_ID  AS BIGINT), 0) AS MainGctIdTo,
	COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS MainGctIdRedirectTo,
	COALESCE(CAST(g2gOu4.GCT_ID   AS BIGINT), 0) AS SubGctIdTo,
	COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS SubGctIdRedirectTo,
    tsf.TSF_TO AS TSF_TO,
    CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
    CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
	 g2gdoc.ExportedBy, 
	g2gdoc.ExportedByStcGctId, 
    SenderEntity.[NameAr] AS DOC_SENDER_FULL_NAME_AR,
    STRING_AGG(CAST(ReceiverEntity.NameAr AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT_FULL_NAME_AR,
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    'Incoming' AS G2G_Category,
    g2gstatus.Name AS GStatus,
    g2gstatus.NameAr AS GStatusAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status AS G2GStatusID,
    COALESCE(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
    COALESCE(dbo.Document.Subject, g2gdoc.Subject) AS Correspondence,
    '' AS Correspondence_Name,
    g2gdoc.G2G_CREATION_DATE AS SYSREGDATE,
    CAST(tsf.TSF_FROM_STRUCTURE  AS BIGINT)AS TSF_FROM_STRUCTURE,
    '' AS ORIGINAL_FROM_STRUCTURE,
    '' AS G2G_VSID,
    g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE
FROM 
    dbo.Document WITH (NOLOCK)
    INNER JOIN dbo.Category ON dbo.Document.CategoryId = dbo.Category.Id
    INNER JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
    INNER JOIN dbo.Privacy WITH (NOLOCK) ON dbo.Document.PrivacyId = dbo.Privacy.Id
    INNER JOIN G2G.dbo.G2G_Documents AS g2gdoc WITH (NOLOCK) ON g2gdoc.DOC_ID = dbo.Document.Id
    INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 WITH (NOLOCK) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID
    INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 WITH (NOLOCK) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID
    INNER JOIN G2G.dbo.G2G_Status AS g2gstatus WITH (NOLOCK) ON g2gdoc.[Status] = g2gstatus.ID
    INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3 WITH (NOLOCK) ON g2gdoc.G2G_MAIN_SITE_TO_ID = g2gOu3.G2G_ID
    INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 WITH (NOLOCK) ON g2gdoc.G2G_SUB_SITE_TO_ID = g2gOu4.G2G_ID
    LEFT JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
    LEFT JOIN dbo.Structure AS ReceiverEntity ON dbo.DocumentReceiverEntity.StructureId = ReceiverEntity.Id
    LEFT JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
    LEFT JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
    LEFT JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
	 LEFT JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
    LEFT JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
    CROSS APPLY (
        SELECT 
            dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
            (SELECT MAX(StatusId) FROM dbo.Transfer WHERE Transfer.DocumentId = g2gdoc.DOC_ID) AS StatusId
        FROM dbo.Transfer WITH (NOLOCK)
        LEFT JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        LEFT JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        LEFT JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        LEFT JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE
    (g2gdoc.Status = 2 OR (g2gdoc.Status = 11 AND (SELECT [Value] FROM G2G.dbo.G2G_SysConfig WHERE KeyCode = 'CreateTransferAfterRecieving') = 'false'))
GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
	g2gdoc.ExportedBy, 
    g2gdoc.ExportedByStcGctId
GO

-- G2G_DocumentInboxQueued --

if exists(select 1 from sys.views where name='G2G_DocumentInboxQueued' and type='v')
    drop view G2G_DocumentInboxQueued;
go

CREATE VIEW [dbo].[G2G_DocumentInboxQueued]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT       g2gdoc.ID G2G_Internal_ID,
 CAST(COALESCE(dbo.Document.Id, 0) AS BIGINT) AS DOC_ID,
    CAST(COALESCE(dbo.Document.CategoryId, 1) AS SMALLINT) AS DOC_CATEGORY,
dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
                            CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
						docStatus.[Name] AS STATUS,
						  COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE, 
                         dbo.Document.[Subject] AS SUBJECT,
						 '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
                       dbo.Document.PrivacyId AS PRIVACYLEVEL,
    '' AS TAGS,
    dbo.Document.Form,
    '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15,
                        dbo.Document.CreatedDate AS DOCUMENTDATE,
    dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom,
    STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
    STRING_AGG(CAST(ReceiverEntity.Name AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT,
    dbo.Priority.Name AS DOC_PRIORITY,
    tsf.StatusId AS DOC_TRANSFER_STATUS,
	CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
    dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
    dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
    dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
						 dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
    dbo.Privacy.Id,
     dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
    CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
     CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
    dbo.Document.DocumentTypeId AS DOC_TYPE,
    CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
    dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						  dbo.Privacy.[Name] AS PrivacyState,
    SenderEntity.[Name] AS DOC_SENDER_FULL_NAME,
                         0 AS AttachmentsCount, 
						 g2gOu1.Name AS MainFromAr,
						 g2gOu1.EnName AS MainFrom, 
						 g2gOu2.Name AS SubFromAr, 
						 g2gOu2.EnName AS SubFrom,
						 recip.G2G_MainSiteTo_ID,
						 recip.G2G_SubSiteTo_ID, 
                         recip.IsCC, 
						 g2gOu3.Name AS MainToAr,
						 g2gOu3.EnName AS MainTo, 
						 g2gOu4.Name AS SubToAr,
						 g2gOu4.EnName AS SubTo,
						 g2gstatus.Name AS Doc_status,
						  CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
						 tsf.TSF_TO AS TSF_TO,
    CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
    CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
						  SenderEntity.[NameAr] AS DOC_SENDER_FULL_NAME_AR,
    STRING_AGG(CAST(ReceiverEntity.NameAr AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT_FULL_NAME_AR,
                         G2G_SERIAL, 
						 G2G_REF_NO, 
                         g2gdoc.G2G_MAIN_SITE_FROM_ID, 
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         recip.G2G_MainSiteTo_ID G2G_MAIN_SITE_TO_ID,
						 recip.G2G_SubSiteTo_ID G2G_SUB_SITE_TO_ID,
                         'Outgoing' as G2G_Category,
						 g2gstatus.Name GStatus,
						 g2gstatus.NameAr GStatusAr,
						 g2gdoc.IsLocked,
						 g2gdoc.MaxAttempt,
                         g2gdoc.Status G2GStatusID,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME, 
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                          COALESCE(dbo.Document.Subject, g2gdoc.Subject) AS Correspondence,
						 '' as Correspondence_Name,
                         g2gdoc.G2G_CREATION_DATE AS SYSREGDATE, 
						 tsf.TSF_FROM_STRUCTURE,
						 '' as ORIGINAL_FROM_STRUCTURE, 
                         '' G2G_VSID, 
						 g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE,
						CAST(0  AS BIGINT)AS MainGctIdTo,
						CAST(0  AS BIGINT)AS MainGctIdRedirectTo,
						CAST(0  AS BIGINT)AS SubGctIdTo,
						CAST(0  AS BIGINT)AS SubGctIdRedirectTo
FROM            dbo.Document WITH (NOLOCK)
						 left outer JOIN dbo.Category  with (nolock) ON dbo.Document.CategoryId = dbo.Category.Id
						 left outer JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
						 left outer JOIN dbo.Privacy with (nolock) ON dbo.Document.PrivacyId= dbo.Privacy.Id  
						 left outer JOIN G2G.dbo.G2G_Documents AS g2gdoc with (nolock) ON g2gdoc.DOC_ID = dbo.Document.Id
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID 
						 left outer JOIN G2G.dbo.G2G_Status AS g2gstatus with (nolock) ON g2gdoc.Status = g2gstatus.ID 
						 left outer JOIN G2G.dbo.G2G_DOC_RECIPIENTS AS recip with (nolock) ON recip.DOC_ID = g2gdoc.DOC_ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3 with (nolock) ON recip.G2G_MainSiteTo_ID = g2gOu3.G2G_ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 with (nolock) ON recip.G2G_SubSiteTo_ID = g2gOu4.G2G_ID 
						  left JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
						  left JOIN dbo.Structure AS ReceiverEntity ON dbo.DocumentReceiverEntity.StructureId = ReceiverEntity.Id
						  left JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
						  left JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
						  left JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
						   left JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
						  left JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
                         outer apply
                             (SELECT    
			dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
			(
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
                               FROM dbo.Transfer WITH (NOLOCK)
                               INNER JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        left JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        left JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        left JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 4 )

GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	recip.G2G_MainSiteTo_ID,
						 recip.G2G_SubSiteTo_ID, 
                         recip.IsCC,
						  g2gdoc.ExportedBy,
                         g2gdoc.ExportedByStcGctId,
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR
GO

-- G2G_DocumentInboxSent --

if exists(select 1 from sys.views where name='G2G_DocumentInboxSent' and type='v')
    drop view G2G_DocumentInboxSent;
go

CREATE VIEW [dbo].[G2G_DocumentInboxSent]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT
    g2gdoc.ID AS G2G_Internal_ID,
    CAST(COALESCE(dbo.Document.Id, 0) AS BIGINT) AS DOC_ID,
    CAST(COALESCE(dbo.Document.CategoryId, 5) AS SMALLINT) AS DOC_CATEGORY,
    dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
    CAST(dbo.Document.PriorityId AS SMALLINT) AS PRIORITY,
    docStatus.[Name] AS STATUS,
    COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE,
    dbo.Document.[Subject] AS SUBJECT,
    '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
    dbo.Document.PrivacyId AS PRIVACYLEVEL,
    '' AS TAGS,
    dbo.Document.Form,
    '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15,
    dbo.Document.CreatedDate AS DOCUMENTDATE,
    dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom,
    STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
    STRING_AGG(CAST(ReceiverEntity.Name AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT,
    dbo.Priority.Name AS DOC_PRIORITY,
    tsf.StatusId AS DOC_TRANSFER_STATUS,
    CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
    tsf.TSF_DUE_DATE,
    tsf.TSF_CLOSURE_DATE,
    tsf.TSF_DATE,
    tsf.TSF_LOCKDATE,
    tsf.TSF_DESCRIPTION,
    CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
    tsf.PriorityId,
    tsf.TSF_FROM_STRUCT_STC_NAME,
    tsf.TSF_FROM_STRUCT_STC_NAME_AR,
    tsf.TSF_TO_STRUCT_STC_NAME,
    tsf.TSF_TO_STRUCT_STC_NAME_AR,
    dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
    dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
    dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
    dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
    dbo.Privacy.Id,
    dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
    CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
    CAST(dbo.Document.CreatedByStructureId AS BIGINT) AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
    dbo.Document.DocumentTypeId AS DOC_TYPE,
    CAST(dbo.Document.StatusId AS SMALLINT) AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
    dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103) AS REGISTERDATE,
    dbo.Privacy.[Name] AS PrivacyState,
    SenderEntity.[Name] AS DOC_SENDER_FULL_NAME,
    0 AS AttachmentsCount,
    g2gOu1.Name AS MainFromAr,
    g2gOu1.EnName AS MainFrom,
    g2gOu2.Name AS SubFromAr,
    g2gOu2.EnName AS SubFrom,
    recip.G2G_MainSiteTo_ID,
    recip.G2G_SubSiteTo_ID,
    recip.IsCC,
    g2gOu3.Name AS MainToAr,
    g2gOu3.EnName AS MainTo,
    g2gOu4.Name AS SubToAr,
    g2gOu4.EnName AS SubTo,
    g2gstatus.Name AS Doc_status,
	CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
    CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,	
    COALESCE(CAST(g2gOu3.GCT_ID AS BIGINT), 0) AS MainGctIdTo,
    COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive AS BIGINT), 0) AS MainGctIdRedirectTo,
    COALESCE(CAST(g2gOu4.GCT_ID AS BIGINT), 0) AS SubGctIdTo,
    COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive AS BIGINT), 0) AS SubGctIdRedirectTo,	
   
    CAST(tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
    CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT) AS TSF_ID,
    SenderEntity.[NameAr] AS DOC_SENDER_FULL_NAME_AR,
    STRING_AGG(CAST(ReceiverEntity.NameAr AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT_FULL_NAME_AR,
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    recip.G2G_MainSiteTo_ID AS G2G_MAIN_SITE_TO_ID,
    recip.G2G_SubSiteTo_ID AS G2G_SUB_SITE_TO_ID,
    'Outgoing' AS G2G_Category,
    g2gstatus.Name AS GStatus,
    g2gstatus.NameAr AS GStatusAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status AS G2GStatusID,
    COALESCE(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
    COALESCE(dbo.Document.Subject, g2gdoc.Subject) AS Correspondence,
    '' AS Correspondence_Name,
    g2gdoc.G2G_CREATION_DATE AS SYSREGDATE,
    tsf.TSF_FROM_STRUCTURE,
    '' AS ORIGINAL_FROM_STRUCTURE,
    '' AS G2G_VSID,
    g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE
FROM
    dbo.Document WITH (NOLOCK)
    LEFT JOIN dbo.Category WITH (NOLOCK) ON dbo.Document.CategoryId = dbo.Category.Id
    LEFT JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
    LEFT JOIN dbo.Privacy WITH (NOLOCK) ON dbo.Document.PrivacyId = dbo.Privacy.Id
    LEFT JOIN G2G.dbo.G2G_Documents AS g2gdoc WITH (NOLOCK) ON g2gdoc.DOC_ID = dbo.Document.Id
    LEFT JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 WITH (NOLOCK) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID
    LEFT JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 WITH (NOLOCK) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID
    LEFT JOIN G2G.dbo.G2G_Status AS g2gstatus WITH (NOLOCK) ON g2gdoc.Status = g2gstatus.ID
    LEFT JOIN G2G.dbo.G2G_DOC_RECIPIENTS AS recip WITH (NOLOCK) ON recip.DOC_ID = g2gdoc.DOC_ID
    LEFT JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3 WITH (NOLOCK) ON recip.G2G_MainSiteTo_ID = g2gOu3.G2G_ID
    LEFT JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 WITH (NOLOCK) ON recip.G2G_SubSiteTo_ID = g2gOu4.G2G_ID
    LEFT JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
    LEFT JOIN dbo.Structure AS ReceiverEntity ON dbo.DocumentReceiverEntity.StructureId = ReceiverEntity.Id
    LEFT JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
    LEFT JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
    LEFT JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
    LEFT JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId = docStatus.Id
    LEFT JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
    OUTER APPLY (
        SELECT
            dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
            (
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
        FROM dbo.Transfer WITH (NOLOCK)
        LEFT JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        LEFT JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        LEFT JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        LEFT JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE
    g2gdoc.Status = 3
    AND 0 = (
        SELECT COUNT(1)
        FROM G2G.dbo.G2G_Documents_Sent ss
        WHERE ss.DOC_ID = 10369 --dbo.Document.Id
    )
GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	recip.G2G_MainSiteTo_ID,
	recip.G2G_SubSiteTo_ID,
	recip.IsCC,
	g2gdoc.ExportedBy, 
	g2gdoc.ExportedByStcGctId,
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR


union

SELECT
    0 AS G2G_Internal_ID,
    CAST(COALESCE(dbo.Document.Id, 0) AS BIGINT) AS DOC_ID,
    CAST(COALESCE(dbo.Document.CategoryId, 5) AS SMALLINT) AS DOC_CATEGORY,
    dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
    dbo.Document.PriorityId AS PRIORITY,
    docStatus.[Name] AS STATUS,
    COALESCE(dbo.Document.CreatedDate, g2gdoc.regdate) AS RECEIVEDATE,
    dbo.Document.[Subject] AS SUBJECT,
    '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
    dbo.Document.PrivacyId AS PRIVACYLEVEL,
    '' AS TAGS,
    dbo.Document.Form,
    '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15,
    dbo.Document.CreatedDate AS DOCUMENTDATE,
    dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom,
    STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
    STRING_AGG(CAST(ReceiverEntity.Name AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT,
    dbo.Priority.Name AS DOC_PRIORITY,
    tsf.StatusId AS DOC_TRANSFER_STATUS,
    CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
    tsf.TSF_DUE_DATE,
    tsf.TSF_CLOSURE_DATE,
    tsf.TSF_DATE,
    tsf.TSF_LOCKDATE,
    tsf.TSF_DESCRIPTION,
    CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
    tsf.PriorityId,
    tsf.TSF_FROM_STRUCT_STC_NAME,
    tsf.TSF_FROM_STRUCT_STC_NAME_AR,
    tsf.TSF_TO_STRUCT_STC_NAME,
    tsf.TSF_TO_STRUCT_STC_NAME_AR,
    dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
    dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
    dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
    dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
    dbo.Privacy.Id,
    dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
    CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
    CAST(dbo.Document.CreatedByStructureId AS BIGINT) AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
    dbo.Document.DocumentTypeId AS DOC_TYPE,
    CAST(dbo.Document.StatusId AS SMALLINT) AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
    dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103) AS REGISTERDATE,
    dbo.Privacy.[Name] AS PrivacyState,
    SenderEntity.[Name] AS DOC_SENDER_FULL_NAME,
    0 AS AttachmentsCount,
    g2gOu1.Name AS MainFromAr,
    g2gOu1.EnName AS MainFrom,
    g2gOu2.Name AS SubFromAr,
    g2gOu2.EnName AS SubFrom,
    g2gdoc.G2G_MainSiteTo_ID,
    g2gdoc.G2G_SubSiteTo_ID,
    g2gdoc.IsCC,
    g2gOu3.Name AS MainToAr,
    g2gOu3.EnName AS MainTo,
    g2gOu4.Name AS SubToAr,
    g2gOu4.EnName AS SubTo,
    g2gstatus.Name AS Doc_status,
    CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
    CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
    COALESCE(CAST(g2gOu3.GCT_ID AS BIGINT), 0) AS MainGctIdTo,
    COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive AS BIGINT), 0) AS MainGctIdRedirectTo,
    COALESCE(CAST(g2gOu4.GCT_ID AS BIGINT), 0) AS SubGctIdTo,
    COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive AS BIGINT), 0) AS SubGctIdRedirectTo,
    CAST(tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
    CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT) AS TSF_ID,
    SenderEntity.[NameAr] AS DOC_SENDER_FULL_NAME_AR,
    STRING_AGG(CAST(ReceiverEntity.NameAr AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT_FULL_NAME_AR,
    '' AS G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MainSiteTo_ID AS G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SubSiteTo_ID AS G2G_SUB_SITE_TO_ID,
    'Outgoing' AS G2G_Category,
    g2gstatus.Name AS GStatus,
    g2gstatus.NameAr AS GStatusAr,
    0 AS IsLocked,
    0 AS MaxAttempt,
    g2gdoc.Status AS G2GStatusID,
    COALESCE(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
    COALESCE(dbo.Document.Subject, '') AS Correspondence,
    '' AS Correspondence_Name,
    g2gdoc.RegDate AS SYSREGDATE,
    CAST(tsf.TSF_FROM_STRUCTURE AS BIGINT) AS TSF_FROM_STRUCTURE,
    '' AS ORIGINAL_FROM_STRUCTURE,
    g2gdoc.G2G_VSID,
    g2gdoc.RegDate AS TRANSFERDATE
FROM
    dbo.Document WITH (NOLOCK)
    LEFT JOIN dbo.Category WITH (NOLOCK) ON dbo.Document.CategoryId = dbo.Category.Id
    LEFT JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
    LEFT JOIN dbo.Privacy WITH (NOLOCK) ON dbo.Document.PrivacyId = dbo.Privacy.Id
    LEFT JOIN G2G.dbo.G2G_Documents_Sent AS g2gdoc WITH (NOLOCK) ON g2gdoc.DOC_ID = dbo.Document.Id
    LEFT JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 WITH (NOLOCK) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID
    LEFT JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 WITH (NOLOCK) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID
    LEFT JOIN G2G.dbo.G2G_SentHistory_Status AS g2gstatus WITH (NOLOCK) ON g2gdoc.Status = g2gstatus.ID
    LEFT JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3 WITH (NOLOCK) ON g2gdoc.G2G_MainSiteTo_ID = g2gOu3.G2G_ID
    LEFT JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 WITH (NOLOCK) ON g2gdoc.G2G_SubSiteTo_ID = g2gOu4.G2G_ID
    LEFT JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
    LEFT JOIN dbo.Structure AS ReceiverEntity ON dbo.DocumentReceiverEntity.StructureId = ReceiverEntity.Id
    LEFT JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
    LEFT JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
    LEFT JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
    LEFT JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId = docStatus.Id
    LEFT JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
    OUTER APPLY (
        SELECT
            dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
            (
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
        FROM
            dbo.Transfer WITH (NOLOCK)
            INNER JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
            LEFT JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
            LEFT JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
            LEFT JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE
            dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE
    g2gdoc.Status IN (1, 2)
    AND 0 < (
        SELECT COUNT(1)
        FROM G2G.dbo.G2G_Documents_Sent ss
        WHERE ss.DOC_ID = 10369 --dbo.Document.Id
    )

GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.RegDate,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MainSiteTo_ID,
    g2gdoc.G2G_SubSiteTo_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    tsf.TSF_FROM_STRUCTURE,g2gdoc.IsCC,g2gdoc.ExportedBy,g2gdoc.ExportedByStcGctId,g2gdoc.G2G_VSID,
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR
GO
 
-- G2G_DocumentInboxRejected --

if exists(select 1 from sys.views where name='G2G_DocumentInboxRejected' and type='v')
    drop view G2G_DocumentInboxRejected;
go

CREATE VIEW [dbo].[G2G_DocumentInboxRejected]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT     g2gdoc.ID G2G_Internal_ID, 
CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 5) AS SMALLINT)  DOC_CATEGORY,
 dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
                      CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
    docStatus.[Name] AS STATUS,
    COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE,
                         dbo.Document.[Subject] AS SUBJECT,
    '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
                          dbo.Document.PrivacyId AS PRIVACYLEVEL,
    '' AS TAGS,
    dbo.Document.Form,
						  '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15, 
                         dbo.Document.CreatedDate AS DOCUMENTDATE,
    dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom, 
                         STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
						STRING_AGG(CAST(ReceiverEntity.Name AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT,
						dbo.Priority.Name AS DOC_PRIORITY, 
                        tsf.StatusId AS DOC_TRANSFER_STATUS, 
						CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
						 dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
    dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
    dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
    dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
						 dbo.Privacy.Id,
    dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
						 CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
						CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
						 dbo.Document.DocumentTypeId AS DOC_TYPE,
     CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
						  dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
						 '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
						dbo.Priority.Id AS PRIORITY_ID,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						 dbo.Privacy.[Name] AS PrivacyState,
    SenderEntity.[Name] AS DOC_SENDER_FULL_NAME,
                         0 AS AttachmentsCount, 
						 g2gOu1.Name AS MainFromAr,
						 g2gOu1.EnName AS MainFrom,
						 g2gOu2.Name AS SubFromAr, 
						 g2gOu2.EnName AS SubFrom, 
						 recip.G2G_MainSiteTo_ID,
						 recip.G2G_SubSiteTo_ID, 
                         recip.IsCC, 
						 g2gOu3.Name AS MainToAr, 
						 g2gOu3.EnName AS MainTo,
						 g2gOu4.Name AS SubToAr, 
						 g2gOu4.EnName AS SubTo,
						 g2gstatus.Name AS Doc_status,
						COALESCE(CAST(g2gOu3.GCT_ID  AS BIGINT), 0) AS MainGctIdTo,
							COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS MainGctIdRedirectTo,
							COALESCE(CAST(g2gOu4.GCT_ID   AS BIGINT), 0) AS SubGctIdTo,
							COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS SubGctIdRedirectTo,
						  CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
                          CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
                          CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
						  SenderEntity.[NameAr] AS DOC_SENDER_FULL_NAME_AR,
    STRING_AGG(CAST(ReceiverEntity.NameAr AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT_FULL_NAME_AR,
                         G2G_SERIAL, 
						 G2G_REF_NO,
                         g2gdoc.G2G_MAIN_SITE_FROM_ID,
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         recip.G2G_MainSiteTo_ID G2G_MAIN_SITE_TO_ID,
						 recip.G2G_SubSiteTo_ID G2G_SUB_SITE_TO_ID,
                         'Outgoing' as G2G_Category,
						 g2gstatus.Name GStatus, 
						 g2gstatus.NameAr GStatusAr,
						 g2gdoc.IsLocked, 
						 g2gdoc.MaxAttempt,
                         g2gdoc.Status G2GStatusID,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME,  
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                          COALESCE(dbo.Document.Subject, g2gdoc.Subject) AS Correspondence,
						 '' as Correspondence_Name,

                         g2gdoc.G2G_CREATION_DATE AS SYSREGDATE,
						 g2gHistory.DateReg AS RejectedDate, 
                         tsf.TSF_FROM_STRUCTURE,
						 '' as ORIGINAL_FROM_STRUCTURE, 
                         '' G2G_VSID,  g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE
FROM      dbo.Document WITH (NOLOCK)
                         LEFT JOIN dbo.Category ON dbo.Document.CategoryId = dbo.Category.Id
    LEFT JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
    LEFT JOIN dbo.Privacy WITH (NOLOCK) ON dbo.Document.PrivacyId = dbo.Privacy.Id
						 INNER JOIN G2G.dbo.G2G_Documents AS g2gdoc with (nolock) ON g2gdoc.DOC_ID = dbo.Document.Id
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID 
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID 
						 INNER JOIN G2G.dbo.G2G_Status AS g2gstatus with (nolock) ON g2gdoc.Status = g2gstatus.ID 
						 INNER JOIN G2G.dbo.G2G_DOC_RECIPIENTS AS recip with (nolock) ON recip.DOC_ID = g2gdoc.DOC_ID 
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3 with (nolock) ON recip.G2G_MainSiteTo_ID = g2gOu3.G2G_ID
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 with (nolock) ON recip.G2G_SubSiteTo_ID = g2gOu4.G2G_ID 
						 LEFT JOIN G2G.dbo.G2G_Document_History AS g2gHistory with (nolock) ON dbo.Document.Id=g2gHistory.DOC_ID and g2gHistory.SubsiteToId = recip.G2G_SubSiteTo_ID
						  LEFT JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
						   LEFT JOIN dbo.Structure AS ReceiverEntity ON dbo.DocumentReceiverEntity.StructureId = ReceiverEntity.Id
						   LEFT JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
						   LEFT JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
						   LEFT JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
						    LEFT JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
						   LEFT JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
                         CROSS APPLY (
        SELECT 
            dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
            (
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
        FROM dbo.Transfer WITH (NOLOCK)
        LEFT JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        LEFT JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        LEFT JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        LEFT JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 8) and 0 = (select count(1) from G2G.dbo.G2G_Documents_Sent ss where ss.DOC_ID = dbo.Document.Id )
GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	recip.G2G_MainSiteTo_ID,
						 recip.G2G_SubSiteTo_ID,
                         recip.IsCC,
						  g2gdoc.ExportedBy, 
						 g2gdoc.ExportedByStcGctId,g2gHistory.DateReg,
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR
union

SELECT    -g2gdoc.ID G2G_Internal_ID, 
CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 5) AS SMALLINT)  DOC_CATEGORY,
    dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
                         CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
     docStatus.[Name] AS STATUS,
    COALESCE(dbo.Document.CreatedDate, g2gdoc.RegDate) AS RECEIVEDATE,
    dbo.Document.[Subject] AS SUBJECT,
    '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
                        dbo.Document.PrivacyId AS PRIVACYLEVEL,
    '' AS TAGS,
    dbo.Document.Form,
    '' AS DOC_TEXT8,
						'Active' AS DOC_TEXT15,
    dbo.Document.CreatedDate AS DOCUMENTDATE,
    dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
						dbo.Document.CreatedDate AS RegisteredDateFrom,
    STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
    STRING_AGG(CAST(ReceiverEntity.Name AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT,
						 dbo.Priority.Name AS DOC_PRIORITY,
    tsf.StatusId AS DOC_TRANSFER_STATUS,
	CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
						dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
    dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
    dbo.Document.DueDate AS DOC_DATE1,
                         'Active' AS Document_Status,
						 dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
    dbo.Privacy.Id,
  dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
						 CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
    CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
						 '' AS O_OCR_TEXT,
						  dbo.Document.DocumentTypeId AS DOC_TYPE,
    CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
    dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
						 dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						 dbo.Privacy.[Name] AS PrivacyState,
    SenderEntity.[Name] AS DOC_SENDER_FULL_NAME,
                         0 AS AttachmentsCount, 
						 g2gOu1.Name AS MainFromAr, 
						 g2gOu1.EnName AS MainFrom, 
						 g2gOu2.Name AS SubFromAr, 
						 g2gOu2.EnName AS SubFrom, 
						 g2gdoc.G2G_MainSiteTo_ID,
						 g2gdoc.G2G_SubSiteTo_ID, 
                         g2gdoc.IsCC,
						 g2gOu3.Name AS MainToAr,
						 g2gOu3.EnName AS MainTo, 
						 g2gOu4.Name AS SubToAr, 
						 g2gOu4.EnName AS SubTo,
						 g2gstatus.Name AS Doc_status,
						COALESCE(CAST(g2gOu3.GCT_ID  AS BIGINT), 0) AS MainGctIdTo,
							COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS MainGctIdRedirectTo,
							COALESCE(CAST(g2gOu4.GCT_ID   AS BIGINT), 0) AS SubGctIdTo,
							COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS SubGctIdRedirectTo,
						 CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
                          CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE, 
                          CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
						  SenderEntity.[NameAr] AS DOC_SENDER_FULL_NAME_AR,
    STRING_AGG(CAST(ReceiverEntity.NameAr AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT_FULL_NAME_AR,
                         '' G2G_SERIAL,
						 G2G_REF_NO,
                         g2gdoc.G2G_MAIN_SITE_FROM_ID,
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         g2gdoc.G2G_MainSiteTo_ID G2G_MAIN_SITE_TO_ID,
						 g2gdoc.G2G_SubSiteTo_ID G2G_SUB_SITE_TO_ID,
                         'Outgoing' as G2G_Category,
						 g2gstatus.Name GStatus,
						 g2gstatus.NameAr GStatusAr,
						 0 IsLocked,
						 0 MaxAttempt,
                         g2gdoc.Status G2GStatusID,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME, 
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                         COALESCE(dbo.Document.Subject, '') AS Correspondence,
						 '' as Correspondence_Name,
                         g2gdoc.RegDate AS SYSREGDATE,
						 g2gdoc.RejectDate AS RejectedDate, 
                         tsf.TSF_FROM_STRUCTURE,
						'' as ORIGINAL_FROM_STRUCTURE, 
                         g2gdoc.G2G_VSID, 
						 g2gdoc.RegDate AS TRANSFERDATE
FROM      dbo.Document WITH (NOLOCK)
  LEFT JOIN dbo.Category ON dbo.Document.CategoryId = dbo.Category.Id
    LEFT JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
    LEFT JOIN dbo.Privacy WITH (NOLOCK) ON dbo.Document.PrivacyId = dbo.Privacy.Id
						 INNER JOIN G2G.dbo.G2G_Documents_Sent AS g2gdoc with (nolock) ON g2gdoc.DOC_ID = dbo.Document.Id
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID 
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID
						 INNER JOIN G2G.dbo.G2G_SentHistory_Status AS g2gstatus with (nolock) ON g2gdoc.Status = g2gstatus.ID 
						 INNER JOIN  G2G.dbo.G2G_OrganizationUnit AS g2gOu3 with (nolock) ON g2gdoc.G2G_MainSiteTo_ID = g2gOu3.G2G_ID 
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 with (nolock) ON g2gdoc.G2G_SubSiteTo_ID = g2gOu4.G2G_ID 
						   INNER JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
    LEFT JOIN dbo.Structure AS ReceiverEntity ON dbo.DocumentReceiverEntity.StructureId = ReceiverEntity.Id
    LEFT JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
    LEFT JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
    LEFT JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
	 LEFT JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
    LEFT JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
                         cross apply
                               (SELECT    
			dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
			(
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
                               FROM dbo.Transfer WITH (NOLOCK)
                               INNER JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        LEFT JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        LEFT JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        LEFT JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 3) and 0 < (select count(1) from G2G.dbo.G2G_Documents_Sent ss where ss.DOC_ID = dbo.Document.Id )

GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.RegDate,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MainSiteTo_ID,
    g2gdoc.G2G_SubSiteTo_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    tsf.TSF_FROM_STRUCTURE,g2gdoc.IsCC,g2gdoc.ExportedBy,g2gdoc.ExportedByStcGctId,g2gdoc.G2G_VSID,g2gdoc.RejectDate,
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR

GO

-- G2G_DocumentInboxPendingToReceive --

if exists(select 1 from sys.views where name='G2G_DocumentInboxPendingToReceive' and type='v')
    drop view G2G_DocumentInboxPendingToReceive;
go

CREATE VIEW [dbo].[G2G_DocumentInboxPendingToReceive]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT    g2gdoc.ID G2G_Internal_ID,
CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 1) AS SMALLINT)  DOC_CATEGORY,
dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
                         CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
						docStatus.[Name] AS STATUS,
						 COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE, 
                        g2gdoc.[Subject] AS SUBJECT,
						 '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
                         convert(nvarchar(20),
						 g2lookup.[CTS_VALUE]) AS PRIVACYLEVEL,
						 '' AS TAGS,
    dbo.Document.Form,
						 '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15,
                         g2gdoc.G2G_SENT_DATE AS DOCUMENTDATE,
						 dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom,
                         concat(g2gOu3.EnName, N',', g2gOu4.EnName)  AS DOC_RECIPIENT,
						  STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
						dbo.Priority.Name AS DOC_PRIORITY,
                         tsf.StatusId AS DOC_TRANSFER_STATUS,
						  CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
						  dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
						 dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
						 dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
    dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
    dbo.Privacy.Id,
     dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
    CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
     CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
    dbo.Document.DocumentTypeId AS DOC_TYPE,
    CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
						 dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID, 
                         CONVERT(VARCHAR(10),
						 dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						 dbo.Privacy.[Name] AS PrivacyState,
                         concat(g2gOu1.EnName, N'\', g2gOu2.EnName) as DOC_SENDER_FULL_NAME, 
                         0 AS AttachmentsCount, 
						 g2gOu1.Name AS MainFromAr,
						 g2gOu1.EnName AS MainFrom, 
                         g2gOu2.Name AS SubFromAr,
						 g2gOu2.EnName AS SubFrom,
						 g2gOu3.Name AS MainToAr,
						 g2gOu3.EnName AS MainTo, 
                         g2gOu4.Name AS SubToAr,
						 g2gOu4.EnName AS SubTo, 
						  COALESCE(CAST(g2gOu3.GCT_ID  AS BIGINT), 0) AS MainGctIdTo,
							COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS MainGctIdRedirectTo,
							COALESCE(CAST(g2gOu4.GCT_ID   AS BIGINT), 0) AS SubGctIdTo,
							COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS SubGctIdRedirectTo,
						 tsf.TSF_TO,
						CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
                          CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
                         concat(g2gOu1.Name, N'\', g2gOu2.Name) as DOC_SENDER_FULL_NAME_AR, 
                         concat(g2gOu3.Name, N'\', g2gOu4.Name) as DOC_RECIPIENT_FULL_NAME_AR, 
						 G2G_SERIAL, 
						 G2G_REF_NO,
                         g2gdoc.G2G_MAIN_SITE_FROM_ID,
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         g2gdoc.G2G_MAIN_SITE_TO_ID,
						 g2gdoc.G2G_SUB_SITE_TO_ID,
                         'Incoming' as G2G_Category,
						 g2gstatus.Name GStatus,  
						 g2gstatus.NameAr GStatusAr,
						 g2gdoc.IsLocked,
						 g2gdoc.MaxAttempt,
                         g2gdoc.Status G2GStatusID,
						 CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME, 
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                          coalesce(dbo.Document.Subject, 
						 g2gdoc.Subject) Correspondence, 
						 '' as Correspondence_Name,
                         g2gdoc.G2G_CREATION_DATE AS SYSREGDATE,
						 CAST(tsf.TSF_FROM_STRUCTURE  AS BIGINT)AS TSF_FROM_STRUCTURE,
						 '' as ORIGINAL_FROM_STRUCTURE, 
                         '' G2G_VSID, 
						 g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE
FROM             G2G.dbo.G2G_Documents AS g2gdoc  with (nolock) 
						 left outer JOIN  dbo.Document ON g2gdoc.DOC_ID = dbo.Document.Id
						 left outer JOIN dbo.Category  with (nolock) ON dbo.Document.CategoryId = dbo.Category.Id
						 left outer JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
						 left outer JOIN G2G.dbo.G2G_Lookups g2lookup with (nolock) on g2gdoc.G2G_SECURITY_LEVEL=g2lookup.[G2G_LKEY]
						 and g2lookup.[G2G_CAT]=0 
						 left outer join dbo.Privacy with (nolock) ON g2lookup.[CTS_VALUE] = dbo.Privacy.Id
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID
						 left outer JOIN G2G.dbo.G2G_Status AS g2gstatus with (nolock) ON g2gdoc.Status = g2gstatus.ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3 with (nolock) ON g2gdoc.G2G_MAIN_SITE_TO_ID = g2gOu3.G2G_ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 with (nolock) ON g2gdoc.G2G_SUB_SITE_TO_ID = g2gOu4.G2G_ID
						 left JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
						 left JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
						 left JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
						 left JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
						 left JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
						 left JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
                         outer apply
                             (SELECT    
			dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
			(
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
                               FROM dbo.Transfer WITH (NOLOCK)
                               INNER JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        left JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        left JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        left JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 6 )

GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	g2gdoc.[Subject],
	g2lookup.[CTS_VALUE],
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
	g2gdoc.ExportedBy,
     g2gdoc.ExportedByStcGctId
GO

--G2G_DocumentInboxRecalled --

if exists(select 1 from sys.views where name='G2G_DocumentInboxRecalled' and type='v')
    drop view G2G_DocumentInboxRecalled;
go

CREATE VIEW [dbo].[G2G_DocumentInboxRecalled]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT     g2gdoc.ID G2G_Internal_ID, 
CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 5) AS SMALLINT)  DOC_CATEGORY,
 dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
                          CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
    docStatus.[Name] AS STATUS,
    COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE,
                         dbo.Document.[Subject] AS SUBJECT,
    '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
                          dbo.Document.PrivacyId AS PRIVACYLEVEL,
    '' AS TAGS,
    dbo.Document.Form,
						  '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15, 
                         dbo.Document.CreatedDate AS DOCUMENTDATE,
    dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom, 
                         STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
						STRING_AGG(CAST(ReceiverEntity.Name AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT,
						dbo.Priority.Name AS DOC_PRIORITY, 
                        tsf.StatusId AS DOC_TRANSFER_STATUS, 
						CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
						 dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
    dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
    dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
    dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
						 dbo.Privacy.Id,
   dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
						 CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
						 CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
						 dbo.Document.DocumentTypeId AS DOC_TYPE,
   CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
						  dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
						 '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
						dbo.Priority.Id AS PRIORITY_ID,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						 dbo.Privacy.[Name] AS PrivacyState,
    SenderEntity.[Name] AS DOC_SENDER_FULL_NAME,
                         0 AS AttachmentsCount, 
						 g2gOu1.Name AS MainFromAr,
						 g2gOu1.EnName AS MainFrom,
						 g2gOu2.Name AS SubFromAr, 
						 g2gOu2.EnName AS SubFrom, 
						 recip.G2G_MainSiteTo_ID,
						 recip.G2G_SubSiteTo_ID, 
                         recip.IsCC, 
						 g2gOu3.Name AS MainToAr, 
						 g2gOu3.EnName AS MainTo,
						 g2gOu4.Name AS SubToAr, 
						 g2gOu4.EnName AS SubTo,
						 g2gstatus.Name AS Doc_status,
						COALESCE(CAST(g2gOu3.GCT_ID  AS BIGINT), 0) AS MainGctIdTo,
							COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS MainGctIdRedirectTo,
							COALESCE(CAST(g2gOu4.GCT_ID   AS BIGINT), 0) AS SubGctIdTo,
							COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS SubGctIdRedirectTo,
						  CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
                         CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
                          CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
						  SenderEntity.[NameAr] AS DOC_SENDER_FULL_NAME_AR,
    STRING_AGG(CAST(ReceiverEntity.NameAr AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT_FULL_NAME_AR,
                         G2G_SERIAL, 
						 G2G_REF_NO,
                         g2gdoc.G2G_MAIN_SITE_FROM_ID,
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         recip.G2G_MainSiteTo_ID G2G_MAIN_SITE_TO_ID,
						 recip.G2G_SubSiteTo_ID G2G_SUB_SITE_TO_ID,
                         'Outgoing' as G2G_Category,
						 g2gstatus.Name GStatus, 
						 g2gstatus.NameAr GStatusAr,
						 g2gdoc.IsLocked, 
						 g2gdoc.MaxAttempt,
                         g2gdoc.Status G2GStatusID,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME,  
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                          COALESCE(dbo.Document.Subject, g2gdoc.Subject) AS Correspondence,
						 '' as Correspondence_Name,
                         g2gdoc.G2G_CREATION_DATE AS SYSREGDATE, 
						 tsf.TSF_FROM_STRUCTURE,
						 '' as ORIGINAL_FROM_STRUCTURE, 
                         '' G2G_VSID,
						 g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE
FROM      dbo.Document WITH (NOLOCK)
                         INNER JOIN dbo.Category ON dbo.Document.CategoryId = dbo.Category.Id
   
						 INNER JOIN G2G.dbo.G2G_Documents AS g2gdoc with (nolock) ON g2gdoc.DOC_ID = dbo.Document.Id
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID 
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID 
						 INNER JOIN G2G.dbo.G2G_Status AS g2gstatus with (nolock) ON g2gdoc.Status = g2gstatus.ID 
						 INNER JOIN G2G.dbo.G2G_DOC_RECIPIENTS AS recip with (nolock) ON recip.DOC_ID = g2gdoc.DOC_ID 
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3 with (nolock) ON recip.G2G_MainSiteTo_ID = g2gOu3.G2G_ID
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 with (nolock) ON recip.G2G_SubSiteTo_ID = g2gOu4.G2G_ID 
						  LEFT JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
							LEFT JOIN dbo.Privacy WITH (NOLOCK) ON dbo.Document.PrivacyId = dbo.Privacy.Id		
						  LEFT JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
						   LEFT JOIN dbo.Structure AS ReceiverEntity ON dbo.DocumentReceiverEntity.StructureId = ReceiverEntity.Id
						   LEFT JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
						   LEFT JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
						   LEFT JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
						    LEFT JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
						   LEFT JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
                         CROSS APPLY (
        SELECT 
            dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
            (
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
        FROM dbo.Transfer WITH (NOLOCK)
        LEFT JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        LEFT JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        LEFT JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        LEFT JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 7) and 0 = (select count(1) from G2G.dbo.G2G_Documents_Sent ss where ss.DOC_ID = dbo.Document.Id )
GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	recip.G2G_MainSiteTo_ID,
						 recip.G2G_SubSiteTo_ID,
                         recip.IsCC,
						  g2gdoc.ExportedBy, 
						 g2gdoc.ExportedByStcGctId,
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR
union 

SELECT     0 G2G_Internal_ID, 
CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 5) AS SMALLINT)  DOC_CATEGORY,
    dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
                          dbo.Document.PriorityId AS PRIORITY,
    docStatus.[Name] AS STATUS,
    COALESCE(dbo.Document.CreatedDate, g2gdoc.RegDate) AS RECEIVEDATE,
    dbo.Document.[Subject] AS SUBJECT,
    '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
                        dbo.Document.PrivacyId AS PRIVACYLEVEL,
    '' AS TAGS,
    dbo.Document.Form,
    '' AS DOC_TEXT8,
						'Active' AS DOC_TEXT15,
    dbo.Document.CreatedDate AS DOCUMENTDATE,
    dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
						dbo.Document.CreatedDate AS RegisteredDateFrom,
    STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
    STRING_AGG(CAST(ReceiverEntity.Name AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT,
						 dbo.Priority.Name AS DOC_PRIORITY,
    tsf.StatusId AS DOC_TRANSFER_STATUS,
	CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
						dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
    dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
    dbo.Document.DueDate AS DOC_DATE1,
                         'Active' AS Document_Status,
						 dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
    dbo.Privacy.Id,
    dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
						 CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
    CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
						 '' AS O_OCR_TEXT,
						  dbo.Document.DocumentTypeId AS DOC_TYPE,
  CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
    dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
						 dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						 dbo.Privacy.[Name] AS PrivacyState,
    SenderEntity.[Name] AS DOC_SENDER_FULL_NAME,
                         0 AS AttachmentsCount, 
						 g2gOu1.Name AS MainFromAr, 
						 g2gOu1.EnName AS MainFrom, 
						 g2gOu2.Name AS SubFromAr, 
						 g2gOu2.EnName AS SubFrom, 
						 g2gdoc.G2G_MainSiteTo_ID,
						 g2gdoc.G2G_SubSiteTo_ID, 
                         g2gdoc.IsCC,
						 g2gOu3.Name AS MainToAr,
						 g2gOu3.EnName AS MainTo, 
						 g2gOu4.Name AS SubToAr, 
						 g2gOu4.EnName AS SubTo,
						 g2gOu3.GCT_ID AS MainGctIdTo,
    g2gOu3.RedirectToGCT_IDonReceive AS MainGctIdRedirectTo,
    g2gOu4.GCT_ID AS SubGctIdTo,
    g2gOu4.RedirectToGCT_IDonReceive AS SubGctIdRedirectTo,
						 g2gstatus.Name AS Doc_status,
						 CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
                          CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE, 
                          CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
						  SenderEntity.[NameAr] AS DOC_SENDER_FULL_NAME_AR,
    STRING_AGG(CAST(ReceiverEntity.NameAr AS NVARCHAR(MAX)), ',') AS DOC_RECIPIENT_FULL_NAME_AR,
                         '' G2G_SERIAL,
						 G2G_REF_NO,
                         g2gdoc.G2G_MAIN_SITE_FROM_ID,
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         g2gdoc.G2G_MainSiteTo_ID G2G_MAIN_SITE_TO_ID,
						 g2gdoc.G2G_SubSiteTo_ID G2G_SUB_SITE_TO_ID,
                         'Outgoing' as G2G_Category,
						 g2gstatus.Name GStatus,
						 g2gstatus.NameAr GStatusAr,
						 0 IsLocked,
						 0 MaxAttempt,
                         g2gdoc.Status G2GStatusID,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME, 
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                         COALESCE(dbo.Document.Subject, '') AS Correspondence,
						 '' as Correspondence_Name,
                         g2gdoc.RegDate AS SYSREGDATE,
						 tsf.TSF_FROM_STRUCTURE,
						'' as ORIGINAL_FROM_STRUCTURE, 
                         g2gdoc.G2G_VSID, 
						 g2gdoc.RegDate AS TRANSFERDATE
FROM      dbo.Document WITH (NOLOCK)
  INNER JOIN dbo.Category ON dbo.Document.CategoryId = dbo.Category.Id
    
						 INNER JOIN G2G.dbo.G2G_Documents_Sent AS g2gdoc with (nolock) ON g2gdoc.DOC_ID = dbo.Document.Id
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID 
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID
						 INNER JOIN G2G.dbo.G2G_SentHistory_Status AS g2gstatus with (nolock) ON g2gdoc.Status = g2gstatus.ID 
						 INNER JOIN  G2G.dbo.G2G_OrganizationUnit AS g2gOu3 with (nolock) ON g2gdoc.G2G_MainSiteTo_ID = g2gOu3.G2G_ID 
						 INNER JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 with (nolock) ON g2gdoc.G2G_SubSiteTo_ID = g2gOu4.G2G_ID 
						 LEFT JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
						 LEFT JOIN dbo.Privacy WITH (NOLOCK) ON dbo.Document.PrivacyId = dbo.Privacy.Id
						   LEFT JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
						   LEFT JOIN dbo.Structure AS ReceiverEntity ON dbo.DocumentReceiverEntity.StructureId = ReceiverEntity.Id
						   LEFT JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
						   LEFT JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
						   LEFT JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
							 LEFT JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
						   LEFT JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
                         cross apply
                               (SELECT    
			dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
			(
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
                               FROM dbo.Transfer WITH (NOLOCK)
                               LEFT JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        LEFT JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        LEFT JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        LEFT JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 4) and 0 < (select count(1) from G2G.dbo.G2G_Documents_Sent ss where ss.DOC_ID = dbo.Document.Id )

GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.RegDate,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MainSiteTo_ID,
    g2gdoc.G2G_SubSiteTo_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    tsf.TSF_FROM_STRUCTURE,g2gdoc.IsCC,g2gdoc.ExportedBy,g2gdoc.ExportedByStcGctId,g2gdoc.G2G_VSID,
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR
GO

-- G2G_DocumentInboxIncomingReturned --

if exists(select 1 from sys.views where name='G2G_DocumentInboxIncomingReturned' and type='v')
    drop view G2G_DocumentInboxIncomingReturned;
go

CREATE VIEW [dbo].[G2G_DocumentInboxIncomingReturned]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT    g2gdoc.ID G2G_Internal_ID,
CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 1) AS SMALLINT)  DOC_CATEGORY,
dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
                         CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
						docStatus.[Name] AS STATUS,
						 COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE, 
                        g2gdoc.[Subject] AS SUBJECT,
						 '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
                         convert(nvarchar(20),
						 g2lookup.[CTS_VALUE]) AS PRIVACYLEVEL,
						 '' AS TAGS,
    dbo.Document.Form,
						 '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15,
                         g2gdoc.G2G_SENT_DATE AS DOCUMENTDATE,
						 dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom,
                         concat(g2gOu3.EnName, N',', g2gOu4.EnName)  AS DOC_RECIPIENT,
						  STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
						dbo.Priority.Name AS DOC_PRIORITY,
                         tsf.StatusId AS DOC_TRANSFER_STATUS,
						  CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
						  dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
						 dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
						 dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
    dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
    dbo.Privacy.Id,
     dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
    CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
     CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
    dbo.Document.DocumentTypeId AS DOC_TYPE,
    CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
						 dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID, 
                         CONVERT(VARCHAR(10),
						 dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						 dbo.Privacy.[Name] AS PrivacyState,
                         concat(g2gOu1.EnName, N'\', g2gOu2.EnName) as DOC_SENDER_FULL_NAME, 
                         0 AS AttachmentsCount, 
						 g2gOu1.Name AS MainFromAr,
						 g2gOu1.EnName AS MainFrom, 
                         g2gOu2.Name AS SubFromAr,
						 g2gOu2.EnName AS SubFrom,
						 g2gOu3.Name AS MainToAr,
						 g2gOu3.EnName AS MainTo, 
                         g2gOu4.Name AS SubToAr,
						 g2gOu4.EnName AS SubTo, 
						  COALESCE(CAST(g2gOu3.GCT_ID  AS BIGINT), 0) AS MainGctIdTo,
							COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS MainGctIdRedirectTo,
							COALESCE(CAST(g2gOu4.GCT_ID   AS BIGINT), 0) AS SubGctIdTo,
							COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS SubGctIdRedirectTo,
						 tsf.TSF_TO,
						CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
                          CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
                         concat(g2gOu1.Name, N'\', g2gOu2.Name) as DOC_SENDER_FULL_NAME_AR, 
                         concat(g2gOu3.Name, N'\', g2gOu4.Name) as DOC_RECIPIENT_FULL_NAME_AR, 
						 G2G_SERIAL, 
						 G2G_REF_NO,
                         g2gdoc.G2G_MAIN_SITE_FROM_ID,
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         g2gdoc.G2G_MAIN_SITE_TO_ID,
						 g2gdoc.G2G_SUB_SITE_TO_ID,
                         'Incoming' as G2G_Category,
						 g2gstatus.Name GStatus,  
						 g2gstatus.NameAr GStatusAr,
						 g2gdoc.IsLocked,
						 g2gdoc.MaxAttempt,
                         g2gdoc.Status G2GStatusID,
						 CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME, 
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                          coalesce(dbo.Document.Subject, 
						 g2gdoc.Subject) Correspondence, 
						 '' as Correspondence_Name,
                         g2gdoc.G2G_CREATION_DATE AS SYSREGDATE,
						 CAST(tsf.TSF_FROM_STRUCTURE  AS BIGINT)AS TSF_FROM_STRUCTURE,
						 '' as ORIGINAL_FROM_STRUCTURE, 
                         '' G2G_VSID, 
						 g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE
FROM             G2G.dbo.G2G_Documents AS g2gdoc  with (nolock) 
						 left outer JOIN  dbo.Document ON g2gdoc.DOC_ID = dbo.Document.Id
						 left outer JOIN dbo.Category  with (nolock) ON dbo.Document.CategoryId = dbo.Category.Id
						 left outer JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
						 left outer JOIN G2G.dbo.G2G_Lookups g2lookup with (nolock) on g2gdoc.G2G_SECURITY_LEVEL=g2lookup.[G2G_LKEY]
						 and g2lookup.[G2G_CAT]=0 
						 left outer join dbo.Privacy with (nolock) ON g2lookup.[CTS_VALUE] = dbo.Privacy.Id
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID
						 left outer JOIN G2G.dbo.G2G_Status AS g2gstatus with (nolock) ON g2gdoc.Status = g2gstatus.ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3 with (nolock) ON g2gdoc.G2G_MAIN_SITE_TO_ID = g2gOu3.G2G_ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 with (nolock) ON g2gdoc.G2G_SUB_SITE_TO_ID = g2gOu4.G2G_ID
						 left JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
						 left JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
						 left JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
						 left JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
						 left JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
						 left JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
                         outer apply
                             (SELECT    
			dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
			(
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
                               FROM dbo.Transfer WITH (NOLOCK)
                               INNER JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        left JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        left JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        left JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 10 )

GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	g2gdoc.[Subject],
	g2lookup.[CTS_VALUE],
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
	g2gdoc.ExportedBy,
     g2gdoc.ExportedByStcGctId

GO

-- G2G_DocumentInboxReceiveOrReject --

if exists(select 1 from sys.views where name='G2G_DocumentInboxReceiveOrReject' and type='v')
    drop view G2G_DocumentInboxReceiveOrReject;
go
 
CREATE VIEW [dbo].[G2G_DocumentInboxReceiveOrReject]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT    g2gdoc.ID G2G_Internal_ID,
CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 1) AS SMALLINT)  DOC_CATEGORY,
    dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
      CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
    docStatus.[Name] AS STATUS,
					 COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE,
                         g2gdoc.Subject AS SUBJECT,
						 '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
    dbo.Document.PrivacyId AS PRIVACYLEVEL,
    '' AS TAGS,
    dbo.Document.Form,
    '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15,
                         g2gdoc.G2G_SENT_DATE AS DOCUMENTDATE,
						 dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom,
                         concat(g2gOu3.EnName, N',', g2gOu4.EnName)  AS DOC_RECIPIENT,
						  STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
						 dbo.Priority.Name AS DOC_PRIORITY,
                          tsf.StatusId AS DOC_TRANSFER_STATUS,
						  CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
    dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
						  dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
    dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
						 dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
    dbo.Privacy.Id,
     dbo.Privacy.[Level],
                         SenderEntity.[Name] AS SENDER,
    CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
    CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
						 '' AS O_OCR_TEXT,
						 dbo.Document.DocumentTypeId AS DOC_TYPE,
    CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
						 dbo.Category.[Name] AS Category,
                          dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
						 dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID,
                         CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						 dbo.Privacy.[Name] AS PrivacyState,
                         concat(g2gOu1.EnName, N'\', g2gOu2.EnName) as DOC_SENDER_FULL_NAME, 
                         0 AS AttachmentsCount,
						 g2gOu1.Name AS MainFromAr,
						 g2gOu1.EnName AS MainFrom, 
                         g2gOu2.Name AS SubFromAr,
						 g2gOu2.EnName AS SubFrom,
						 g2gOu3.Name AS MainToAr,
						 g2gOu3.EnName AS MainTo, 
                         g2gOu4.Name AS SubToAr, 
						 g2gOu4.EnName AS SubTo, 
						 CAST(g2gOu3.GCT_ID  AS BIGINT)AS MainGctIdTo,
						CAST(g2gOu3.RedirectToGCT_IDonReceive  AS BIGINT)AS MainGctIdRedirectTo,
						CAST( g2gOu4.GCT_ID   AS BIGINT)AS SubGctIdTo,
						CAST(g2gOu4.RedirectToGCT_IDonReceive  AS BIGINT)AS SubGctIdRedirectTo,
						 tsf.TSF_TO,
						CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
                          CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
                         concat(g2gOu1.Name, N'\', g2gOu2.Name) as DOC_SENDER_FULL_NAME_AR, 
                         concat(g2gOu3.Name, N'\', g2gOu4.Name) as DOC_RECIPIENT_FULL_NAME_AR,
						 CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
						 G2G_SERIAL,
						 G2G_REF_NO,
                         g2gdoc.G2G_MAIN_SITE_FROM_ID,
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         g2gdoc.G2G_MAIN_SITE_TO_ID,
						 g2gdoc.G2G_SUB_SITE_TO_ID,
                         'Incoming' as G2G_Category,
						 g2gstatus.Name GStatus,
						 g2gstatus.NameAr GStatusAr,
						 g2gdoc.IsLocked, 
						 g2gdoc.MaxAttempt,
                         g2gdoc.Status G2GStatusID,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME, 
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                         coalesce(dbo.Document.Subject, g2gdoc.Subject) Correspondence,
						 '' as Correspondence_Name,
                         g2gdoc.G2G_CREATION_DATE AS SYSREGDATE,
						CAST(tsf.TSF_FROM_STRUCTURE  AS BIGINT)AS TSF_FROM_STRUCTURE,
						 '' as ORIGINAL_FROM_STRUCTURE, 
                         '' G2G_VSID,
						 g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE
FROM             G2G.dbo.G2G_Documents AS g2gdoc  with (nolock) 
                        left outer JOIN  dbo.Document ON g2gdoc.DOC_ID = dbo.Document.Id
						left outer JOIN dbo.Category  with (nolock) ON dbo.Document.CategoryId = dbo.Category.Id
						 left outer JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
						 left outer JOIN dbo.Privacy WITH (NOLOCK) ON dbo.Document.PrivacyId = dbo.Privacy.Id
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1  with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2  with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID
						 left outer JOIN G2G.dbo.G2G_Status AS g2gstatus  with (nolock) ON g2gdoc.Status = g2gstatus.ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3  with (nolock) ON g2gdoc.G2G_MAIN_SITE_TO_ID = g2gOu3.G2G_ID
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4  with (nolock) ON g2gdoc.G2G_SUB_SITE_TO_ID = g2gOu4.G2G_ID
						 left JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
						 left JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
						 left JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
						 left JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
						 left JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
						 left JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
                          outer apply
                             (SELECT        
							 dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
			(
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
                               FROM dbo.Transfer WITH (NOLOCK)
                               INNER JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        left JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        left JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        left JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 11) and RejectionTransactionId is null
GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	g2gdoc.Subject,
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
	g2gdoc.ExportedBy,
	g2gdoc.ExportedByStcGctId
GO

-- G2G_DocumentInboxReceiveOrRejectPendingRejection --

if exists(select 1 from sys.views where name='G2G_DocumentInboxReceiveOrRejectPendingRejection' and type='v')
    drop view G2G_DocumentInboxReceiveOrRejectPendingRejection;
go

CREATE VIEW [dbo].[G2G_DocumentInboxReceiveOrRejectPendingRejection]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT    g2gdoc.ID G2G_Internal_ID,
CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 1) AS SMALLINT)  DOC_CATEGORY,
dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
                         CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
						docStatus.[Name] AS STATUS,
						 COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE, 
                        g2gdoc.[Subject] AS SUBJECT,
						 '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
                         convert(nvarchar(20),
						 g2lookup.[CTS_VALUE]) AS PRIVACYLEVEL,
						 '' AS TAGS,
    dbo.Document.Form,
						 '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15,
                         g2gdoc.G2G_SENT_DATE AS DOCUMENTDATE,
						 dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom,
                         concat(g2gOu3.EnName, N',', g2gOu4.EnName)  AS DOC_RECIPIENT,
						  STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
						dbo.Priority.Name AS DOC_PRIORITY,
                         tsf.StatusId AS DOC_TRANSFER_STATUS,
						  CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
						  dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
						 dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
						 dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
    dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
    dbo.Privacy.Id,
     dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
    CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
     CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
    dbo.Document.DocumentTypeId AS DOC_TYPE,
    CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
						 dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID, 
                         CONVERT(VARCHAR(10),
						 dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						 dbo.Privacy.[Name] AS PrivacyState,
                         concat(g2gOu1.EnName, N'\', g2gOu2.EnName) as DOC_SENDER_FULL_NAME, 
                         0 AS AttachmentsCount, 
						 g2gOu1.Name AS MainFromAr,
						 g2gOu1.EnName AS MainFrom, 
                         g2gOu2.Name AS SubFromAr,
						 g2gOu2.EnName AS SubFrom,
						 g2gOu3.Name AS MainToAr,
						 g2gOu3.EnName AS MainTo, 
                         g2gOu4.Name AS SubToAr,
						 g2gOu4.EnName AS SubTo, 
						  COALESCE(CAST(g2gOu3.GCT_ID  AS BIGINT), 0) AS MainGctIdTo,
							COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS MainGctIdRedirectTo,
							COALESCE(CAST(g2gOu4.GCT_ID   AS BIGINT), 0) AS SubGctIdTo,
							COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS SubGctIdRedirectTo,
						 tsf.TSF_TO,
						CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
                          CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
                         concat(g2gOu1.Name, N'\', g2gOu2.Name) as DOC_SENDER_FULL_NAME_AR, 
                         concat(g2gOu3.Name, N'\', g2gOu4.Name) as DOC_RECIPIENT_FULL_NAME_AR, 
						 G2G_SERIAL, 
						 G2G_REF_NO,
                         g2gdoc.G2G_MAIN_SITE_FROM_ID,
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         g2gdoc.G2G_MAIN_SITE_TO_ID,
						 g2gdoc.G2G_SUB_SITE_TO_ID,
                         'Incoming' as G2G_Category,
						 g2gstatus.Name GStatus,  
						 g2gstatus.NameAr GStatusAr,
						 g2gdoc.IsLocked,
						 g2gdoc.MaxAttempt,
                         g2gdoc.Status G2GStatusID,
						 CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME, 
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                          coalesce(dbo.Document.Subject, 
						 g2gdoc.Subject) Correspondence, 
						 '' as Correspondence_Name,
                         g2gdoc.G2G_CREATION_DATE AS SYSREGDATE,
						 CAST(tsf.TSF_FROM_STRUCTURE  AS BIGINT)AS TSF_FROM_STRUCTURE,
						 '' as ORIGINAL_FROM_STRUCTURE, 
                         '' G2G_VSID, 
						 g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE
FROM             G2G.dbo.G2G_Documents AS g2gdoc  with (nolock) 
						 left outer JOIN  dbo.Document ON g2gdoc.DOC_ID = dbo.Document.Id
						 left outer JOIN dbo.Category  with (nolock) ON dbo.Document.CategoryId = dbo.Category.Id
						 left outer JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
						 left outer JOIN G2G.dbo.G2G_Lookups g2lookup with (nolock) on g2gdoc.G2G_SECURITY_LEVEL=g2lookup.[G2G_LKEY]
						 and g2lookup.[G2G_CAT]=0 
						 left outer join dbo.Privacy with (nolock) ON g2lookup.[CTS_VALUE] = dbo.Privacy.Id
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID
						 left outer JOIN G2G.dbo.G2G_Status AS g2gstatus with (nolock) ON g2gdoc.Status = g2gstatus.ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3 with (nolock) ON g2gdoc.G2G_MAIN_SITE_TO_ID = g2gOu3.G2G_ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 with (nolock) ON g2gdoc.G2G_SUB_SITE_TO_ID = g2gOu4.G2G_ID
						 left JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
						 left JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
						 left JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
						 left JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
						 left JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
						 left JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
                         outer apply
                             (SELECT    
			dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
			(
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
                               FROM dbo.Transfer WITH (NOLOCK)
                               INNER JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        left JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        left JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        left JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 11) and RejectionTransactionId is not null

GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	g2gdoc.[Subject],
	g2lookup.[CTS_VALUE],
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
	g2gdoc.ExportedBy,
     g2gdoc.ExportedByStcGctId
GO

-- G2G_DocumentInboxIncomingRejected --

if exists(select 1 from sys.views where name='G2G_DocumentInboxIncomingRejected' and type='v')
    drop view G2G_DocumentInboxIncomingRejected;
go

CREATE VIEW [dbo].[G2G_DocumentInboxIncomingRejected]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT     g2gdoc.ID G2G_Internal_ID, 
CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 1) AS SMALLINT)  DOC_CATEGORY,
dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
                         CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
						  docStatus.[Name] AS STATUS,
						 COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE,
                         '*******' AS SUBJECT, 
						  '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
    dbo.Document.PrivacyId AS PRIVACYLEVEL,
    '' AS TAGS,
    dbo.Document.Form,
						 '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15,
                         g2gdoc.G2G_SENT_DATE AS DOCUMENTDATE,
						 dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom,
                         concat(g2gOu3.EnName, N',', g2gOu4.EnName)  AS DOC_RECIPIENT,
						  STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
						 dbo.Priority.Name AS DOC_PRIORITY,
                         tsf.StatusId AS DOC_TRANSFER_STATUS,
						 CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
						  dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
						 dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
    dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
    dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
   CAST(dbo.Privacy.Id AS SMALLINT)As Id,
     dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
    CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
     CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
    dbo.Document.DocumentTypeId AS DOC_TYPE,
    CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
						 dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID, 
                         CONVERT(VARCHAR(10),
						 dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						 dbo.Privacy.[Name] AS PrivacyState,
                         concat(g2gOu1.EnName, N'\', g2gOu2.EnName) as DOC_SENDER_FULL_NAME, 
                         0 AS AttachmentsCount,
						 g2gOu1.Name AS MainFromAr,
						 g2gOu1.EnName AS MainFrom, 
                         g2gOu2.Name AS SubFromAr,
						 g2gOu2.EnName AS SubFrom,
						 g2gOu3.Name AS MainToAr, 
						 g2gOu3.EnName AS MainTo, 
                         g2gOu4.Name AS SubToAr,
						 g2gOu4.EnName AS SubTo, 
						COALESCE(CAST(g2gOu3.GCT_ID  AS BIGINT), 0) AS MainGctIdTo,
							COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS MainGctIdRedirectTo,
							COALESCE(CAST(g2gOu4.GCT_ID   AS BIGINT), 0) AS SubGctIdTo,
							COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS SubGctIdRedirectTo,
						 tsf.TSF_TO, 
						 CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
						CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
                         concat(g2gOu1.Name, N'\', g2gOu2.Name) as DOC_SENDER_FULL_NAME_AR, 
                         concat(g2gOu3.Name, N'\', g2gOu4.Name) as DOC_RECIPIENT_FULL_NAME_AR,
						 G2G_SERIAL, G2G_REF_NO,
                         g2gdoc.G2G_MAIN_SITE_FROM_ID,
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         g2gdoc.G2G_MAIN_SITE_TO_ID,
						 g2gdoc.G2G_SUB_SITE_TO_ID,
                         'Incoming' as G2G_Category,
						 g2gstatus.Name GStatus, 
						 g2gstatus.NameAr GStatusAr,
						 g2gdoc.IsLocked, 
						 g2gdoc.MaxAttempt,
                         g2gdoc.Status G2GStatusID,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME, 
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                         '********' Correspondence,
						 '' as Correspondence_Name,
                         g2gdoc.G2G_CREATION_DATE AS SYSREGDATE,
						  CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
						CAST(tsf.TSF_FROM_STRUCTURE  AS BIGINT)AS TSF_FROM_STRUCTURE,
						 '' AS ORIGINAL_FROM_STRUCTURE, 
                         '' G2G_VSID,
						 g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE
FROM             G2G.dbo.G2G_Documents AS g2gdoc  with (nolock)  
						left outer JOIN  dbo.Document ON g2gdoc.DOC_ID = dbo.Document.Id
						left outer JOIN dbo.Category  with (nolock) ON dbo.Document.CategoryId = dbo.Category.Id
						 left outer JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
						 left outer JOIN dbo.Privacy WITH (NOLOCK) ON dbo.Document.PrivacyId = dbo.Privacy.Id
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1  with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2  with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID 
						 left outer JOIN G2G.dbo.G2G_Status AS g2gstatus  with (nolock) ON g2gdoc.Status = g2gstatus.ID
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3  with (nolock) ON g2gdoc.G2G_MAIN_SITE_TO_ID = g2gOu3.G2G_ID
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4  with (nolock) ON g2gdoc.G2G_SUB_SITE_TO_ID = g2gOu4.G2G_ID
						 left JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
						 left JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
						 left JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
						 left JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
						 left JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
						 left JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
                         outer apply
                             (SELECT        
							 dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
			(
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
                               FROM dbo.Transfer WITH (NOLOCK)
                               INNER JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        left JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        left JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        left JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 10) 

GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
	g2gdoc.ExportedBy,
     g2gdoc.ExportedByStcGctId
GO

-- usp_GetG2GDocs --

IF EXISTS ( SELECT * 
            FROM   sysobjects 
            WHERE  id = object_id(N'[dbo].[usp_GetG2GDocs]') 
                   and OBJECTPROPERTY(id, N'IsProcedure') = 1 )
begin
    drop procedure  [dbo].[usp_GetG2GDocs];
end
go

CREATE PROCEDURE [dbo].[usp_GetG2GDocs]
    @ContactId as int,
    @StructureId as int,
    @StructureReceiver as bit,
    @SelectFields nvarchar(max),
    @ObjectName nvarchar(200),
    @WhereStatement  nvarchar(max),
    @OrderBy nvarchar(200), 
    @StartRow int,
    @PageSize int 
AS
BEGIN
    DECLARE @Query NVARCHAR(MAX)=N'select * from (
                SELECT ROW_NUMBER() OVER (ORDER BY '+@OrderBy+ ') as Number, '+@SelectFields+
                ' FROM '+@ObjectName+ 
                ' Where '+@WhereStatement+ 
              ' )V where V.Number between '+Convert(nvarchar(10), @StartRow)+' and '+Convert(nvarchar(10),@StartRow+@PageSize-1)
DECLARE @Parametres NVARCHAR(300)=N'@ContactId INT,@StructureId INT,@StructureReceiver bit'
 
EXEC sp_executesql @Query,@Parametres,@ContactId,@StructureId,@StructureReceiver
END


GO

-- G2G_CreateSenderEntity --

IF EXISTS (SELECT * 
            FROM   sysobjects 
            WHERE  id = object_id(N'[dbo].[G2G_CreateSenderEntity]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
begin
    drop procedure  [dbo].[G2G_CreateSenderEntity];
end
go

CREATE procedure [dbo].[G2G_CreateSenderEntity]
    @STC_NAME nvarchar(500),
    @STC_NAMEAR nvarchar(500),
    @stc_code nvarchar(200),
    @Myorg bit,
    @STCID_PARENT int,
    @serialPrefix nvarchar(100),
    @email nvarchar(500),
    @fax nvarchar(500),
    @phones nvarchar(500),
    @logo nvarchar(500)

AS
BEGIN
    declare @stcId int ;
    declare @AttId int ;
    
		set @AttId = (select Id  from IAM.dbo.Attribute WHERE [Name] = 'NameAr');

		INSERT INTO IAM.dbo.Structure 
           ([Code]
           ,[Name]
           ,[StructureParentId]
           ,[CreatedByUserId]
           ,[CreatedDate]
           ,[External])
     VALUES
           (@stc_code,
		   @STC_NAME,
            @STCID_PARENT,
           1,
		   GETDATE()
           ,1)
		   set @stcId = @@IDENTITY

		Insert into IAM.dbo.StructureAttributeValue ([StructureId], [AttributeId], [Value]) values (@stcId, @AttId, @STC_NAMEAR)

		Insert into CTS.dbo.[Structure]
           ([Id]
           ,[Code]
           ,[Name]
           ,[NameAr]
           ,[IsExternal]
           ,[ParentId])
     VALUES(@stcId, @stc_code, @STC_NAME, @STC_NAMEAR, 1, @STCID_PARENT)
	 select @stcId
END
go

-- G2GAllDocuments --

if exists(select 1 from sys.views where name='G2GAllDocuments' and type='v')
    drop view G2GAllDocuments;
go

create view [dbo].[G2GAllDocuments]

AS
    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, PRIVACYLEVEL, 
    NULL as RejectedDate, G2G_VSID, PrivacyState from G2G_DocumentInbox
    union all

    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, PRIVACYLEVEL, 
    NULL as RejectedDate, G2G_VSID, PrivacyState from G2G_DocumentInboxPendingToReceive
    union all

    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, PRIVACYLEVEL, 
    NULL as RejectedDate, G2G_VSID, PrivacyState from G2G_DocumentInboxIncomingReturned
    union all

    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, PRIVACYLEVEL, 
    NULL as RejectedDate, G2G_VSID, PrivacyState from G2G_DocumentInboxQueued
    union all

    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, PRIVACYLEVEL, 
    NULL as RejectedDate, G2G_VSID, PrivacyState from G2G_DocumentInboxRecalled
    union all

    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, PRIVACYLEVEL, 
    RejectedDate, G2G_VSID, PrivacyState from G2G_DocumentInboxRejected
    union all

    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, PRIVACYLEVEL, 
    NULL as RejectedDate, G2G_VSID, PrivacyState from G2G_DocumentInboxSent
    union all
    
    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, PRIVACYLEVEL, 
    NULL as RejectedDate, G2G_VSID, PrivacyState from G2G_DocumentInboxReceiveOrReject
    union all

    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, PRIVACYLEVEL, 
    NULL as RejectedDate, G2G_VSID, PrivacyState from G2G_DocumentInboxReceiveOrRejectPendingRejection
go

-- usp_GetG2GDocsWithoutAuthorization --

IF EXISTS (SELECT * 
            FROM   sysobjects 
            WHERE  id = object_id(N'[dbo].[usp_GetG2GDocsWithoutAuthorization]') and OBJECTPROPERTY(id, N'IsProcedure') = 1)
begin
    drop procedure  [dbo].[usp_GetG2GDocsWithoutAuthorization];
end
go

CREATE PROCEDURE [dbo].[usp_GetG2GDocsWithoutAuthorization]
    @SelectFields nvarchar(max),
    @WhereStatement  nvarchar(max),
    @OrderBy nvarchar(200), 
    @StartRow int,
    @PageSize int 
AS
BEGIN
    DECLARE @Query NVARCHAR(MAX)=N'select * from (
                SELECT ROW_NUMBER() OVER (ORDER BY '+@OrderBy+ ') as Number, '+@SelectFields+
                ' FROM G2GAllDocuments'+ 
                ' Where '+@WhereStatement+ 
              ' )V where V.Number between '+Convert(nvarchar(10), @StartRow)+' and '+Convert(nvarchar(10),@StartRow+@PageSize-1)
 
EXEC sp_executesql @Query
END
GO

-- G2G_DocumentInboxCombined --

if exists(select 1 from sys.views where name='G2G_DocumentInboxCombined' and type='v')
    drop view G2G_DocumentInboxCombined;
go

create view [dbo].[G2G_DocumentInboxCombined]

AS
    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, SubGctIdTo, 
    MainGctIdTo, MainGctIdRedirectTo, SubGctIdRedirectTo, PRIVACYLEVEL, TOUSER, FROMUSER, Correspondence_Name, SYSREGDATE, TRANSFERDATE,
    RECEIVEDATE, Correspondence, TSF_FROM_STRUCTURE,ORIGINAL_FROM_STRUCTURE,AttachmentsCount, PrivacyState  from G2G_DocumentInbox
    union all

    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, SubGctIdTo, 
    MainGctIdTo, MainGctIdRedirectTo, SubGctIdRedirectTo, PRIVACYLEVEL, TOUSER, FROMUSER, Correspondence_Name, SYSREGDATE, TRANSFERDATE,
    RECEIVEDATE, Correspondence, TSF_FROM_STRUCTURE,ORIGINAL_FROM_STRUCTURE,AttachmentsCount, PrivacyState from G2G_DocumentInboxPendingToReceive
    

go

-- G2G_DocumentOutboxCombined --

if exists(select 1 from sys.views where name='G2G_DocumentOutboxCombined' and type='v')
    drop view G2G_DocumentOutboxCombined;
go

create view [dbo].[G2G_DocumentOutboxCombined]

AS
    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, ExportedBy, ExportedByStcGctId, PRIVACYLEVEL,
    TSF_FROM_STRUCTURE,ORIGINAL_FROM_STRUCTURE,AttachmentsCount,
    Correspondence,SYSREGDATE, TRANSFERDATE,Correspondence_Name,FROMUSER,TOUSER,RECEIVEDATE, PrivacyState  from G2G_DocumentInboxQueued
    union all

    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, ExportedBy, ExportedByStcGctId, PRIVACYLEVEL,
    TSF_FROM_STRUCTURE,ORIGINAL_FROM_STRUCTURE,AttachmentsCount,
    Correspondence,SYSREGDATE, TRANSFERDATE,Correspondence_Name,FROMUSER,TOUSER,RECEIVEDATE, PrivacyState from G2G_DocumentInboxRecalled
    union all

    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, ExportedBy, ExportedByStcGctId, PRIVACYLEVEL,
    TSF_FROM_STRUCTURE,ORIGINAL_FROM_STRUCTURE,AttachmentsCount,
    Correspondence,SYSREGDATE, TRANSFERDATE,Correspondence_Name,FROMUSER,TOUSER,RECEIVEDATE, PrivacyState from G2G_DocumentInboxRejected
    union all

    SELECT G2G_Internal_ID, DOC_ID, DOC_CATEGORY, REFERENCE, TSF_ID, SUBJECT, DOCUMENTDATE, DOC_RECIPIENT,
    DOC_SENDER_FULL_NAME, DOC_SENDER_FULL_NAME_AR, MainTo, MainFrom, MainToAr, MainFromAr, SubTo, SubFrom, SubToAr,
    SubFromAr, DOC_RECIPIENT_FULL_NAME_AR, G2G_SERIAL, G2G_REF_NO, G2G_MAIN_SITE_FROM_ID,G2G_SUB_SITE_FROM_ID,
    G2G_MAIN_SITE_TO_ID,G2G_SUB_SITE_TO_ID,G2G_Category,GStatus,GStatusAr,IsLocked,MaxAttempt,G2GStatusID, ExportedBy, ExportedByStcGctId, PRIVACYLEVEL,
    TSF_FROM_STRUCTURE,ORIGINAL_FROM_STRUCTURE,AttachmentsCount,
    Correspondence,SYSREGDATE, TRANSFERDATE,Correspondence_Name,FROMUSER,TOUSER,RECEIVEDATE, PrivacyState from G2G_DocumentInboxSent

go

-- 

IF EXISTS ( SELECT * 
            FROM   sysobjects 
            WHERE  id = object_id(N'[dbo].[usp_GetG2GDocs]') 
                   and OBJECTPROPERTY(id, N'IsProcedure') = 1 )
begin
    drop procedure  [dbo].[usp_GetG2GDocs];
end
go

CREATE PROCEDURE [dbo].[usp_GetG2GDocs]
    @ContactId as int,
    @StructureId as int,
    @StructureReceiver as bit,
    @ContactPrivacyLevel as int,
    @SelectFields nvarchar(max),
    @ObjectName nvarchar(200),
    @WhereStatement  nvarchar(max),
    @OrderBy nvarchar(200), 
    @StartRow int,
    @PageSize int 
AS
BEGIN
    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET NOCOUNT ON;
    DECLARE @Query NVARCHAR(MAX)=N'select * from (
                SELECT ROW_NUMBER() OVER (ORDER BY '+@OrderBy+ ') as Number, '+@SelectFields+
                ' FROM '+@ObjectName+ 
                ' Where '+@WhereStatement+ 
              ' )V where V.Number between '+Convert(nvarchar(10), @StartRow)+' and '+Convert(nvarchar(10),@StartRow+@PageSize-1)
DECLARE @Parametres NVARCHAR(300)=N'@ContactId INT,@StructureId INT,@StructureReceiver bit,@ContactPrivacyLevel INT'
 
EXEC sp_executesql @Query,@Parametres,@ContactId,@StructureId,@StructureReceiver,@ContactPrivacyLevel
END
go

-- Insert into ActivityLogAction -- 
 
 set identity_insert [ActivityLogAction] on
 go

if not exists (select 1 from [ActivityLogAction] where [Name] = N'RejectG2GDocument')
begin

    insert into [ActivityLogAction] (id, [Name], [Description])
    values (1001, N'RejectG2GDocument', N'Reject G2G Document')
end
go

if not exists (select 1 from [ActivityLogAction] where [Name] = N'ReceiveG2GDocument')
begin

    insert into [ActivityLogAction] (id, [Name], [Description])
    values (1002, N'ReceiveG2GDocument', N'Receive G2G Document')
end
go

if not exists (select 1 from [ActivityLogAction] where [Name] = N'ExportToG2G')
begin

    insert into [ActivityLogAction] (id, [Name], [Description])
    values (1003, N'ExportToG2G', N'Export using G2G')
end
go


 set identity_insert [ActivityLogAction] off
 go

-- G2G_DocumentInboxIncomingRecalled --


if exists(select 1 from sys.views where name='G2G_DocumentInboxIncomingRecalled' and type='v')
    drop view G2G_DocumentInboxIncomingRecalled;
go
 
CREATE VIEW [dbo].[G2G_DocumentInboxIncomingRecalled]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT    g2gdoc.ID G2G_Internal_ID, 
CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 1) AS SMALLINT)  DOC_CATEGORY,
dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
   CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
  docStatus.[Name] AS STATUS,
  COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE,
    '*******' AS SUBJECT, 
   '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
    dbo.Document.PrivacyId AS PRIVACYLEVEL,
    '' AS TAGS,
    dbo.Document.Form,
						 '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15,
                         g2gdoc.G2G_SENT_DATE AS DOCUMENTDATE,
						 dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom,
                         concat(g2gOu3.EnName, N',', g2gOu4.EnName)  AS DOC_RECIPIENT,
						 STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
						 dbo.Priority.Name AS DOC_PRIORITY,
                         tsf.StatusId AS DOC_TRANSFER_STATUS,
						 CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
						  dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
						 dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
    dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
    dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
     CAST(dbo.Privacy.Id AS SMALLINT)As Id,
    dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
    CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
     CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
    dbo.Document.DocumentTypeId AS DOC_TYPE,
     CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
						 dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID, 
                         CONVERT(VARCHAR(10),
						 dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						 dbo.Privacy.[Name] AS PrivacyState,
                         concat(g2gOu1.EnName, N'\', g2gOu2.EnName) as DOC_SENDER_FULL_NAME, 
                         0 AS AttachmentsCount,
						 g2gOu1.Name AS MainFromAr, 
						 g2gOu1.EnName AS MainFrom, 
                         g2gOu2.Name AS SubFromAr,
						 g2gOu2.EnName AS SubFrom, g2gOu3.Name AS MainToAr,
						 g2gOu3.EnName AS MainTo, 
                         g2gOu4.Name AS SubToAr,
						 g2gOu4.EnName AS SubTo,
						 COALESCE(CAST(g2gOu3.GCT_ID  AS BIGINT), 0) AS MainGctIdTo,
							COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS MainGctIdRedirectTo,
							COALESCE(CAST(g2gOu4.GCT_ID   AS BIGINT), 0) AS SubGctIdTo,
							COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS SubGctIdRedirectTo,
						 tsf.TSF_TO, 
						 CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
						CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
                         concat(g2gOu1.Name, N'\', g2gOu2.Name) as DOC_SENDER_FULL_NAME_AR, 
                         concat(g2gOu3.Name, N'\', g2gOu4.Name) as DOC_RECIPIENT_FULL_NAME_AR, 
						 G2G_SERIAL, G2G_REF_NO,
						  CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
                         g2gdoc.G2G_MAIN_SITE_FROM_ID,
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         g2gdoc.G2G_MAIN_SITE_TO_ID,
						 g2gdoc.G2G_SUB_SITE_TO_ID,
                         'Incoming' as G2G_Category,
						 g2gstatus.Name GStatus, 
						 g2gstatus.NameAr GStatusAr,
						 g2gdoc.IsLocked, g2gdoc.MaxAttempt,
                         g2gdoc.Status G2GStatusID,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME,
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                         '*******' Correspondence,
						 '' as Correspondence_Name,
                         g2gdoc.G2G_CREATION_DATE AS SYSREGDATE,
						 CAST(tsf.TSF_FROM_STRUCTURE  AS BIGINT)AS TSF_FROM_STRUCTURE,
						  '' AS ORIGINAL_FROM_STRUCTURE,
                         '' G2G_VSID,
						 g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE
FROM             G2G.dbo.G2G_Documents AS g2gdoc  with (nolock)  
						 left outer JOIN  dbo.Document ON g2gdoc.DOC_ID = dbo.Document.Id
						 left outer JOIN dbo.Category  with (nolock) ON dbo.Document.CategoryId = dbo.Category.Id
						 left outer JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
						 left outer JOIN dbo.Privacy WITH (NOLOCK) ON dbo.Document.PrivacyId = dbo.Privacy.Id
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1  with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2  with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID 
						 left outer JOIN G2G.dbo.G2G_Status AS g2gstatus  with (nolock) ON g2gdoc.Status = g2gstatus.ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3  with (nolock) ON g2gdoc.G2G_MAIN_SITE_TO_ID = g2gOu3.G2G_ID
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4  with (nolock) ON g2gdoc.G2G_SUB_SITE_TO_ID = g2gOu4.G2G_ID
						 INNER JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
						 INNER JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
						 INNER JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
						 INNER JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
						 INNER JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
						 INNER JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
                         outer apply
                             (SELECT
							  dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
			(
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) as StatusId
			
                               FROM dbo.Transfer WITH (NOLOCK)
        LEFT JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        LEFT JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        LEFT JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        LEFT JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 12) 
GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
	  g2gdoc.ExportedBy,
     g2gdoc.ExportedByStcGctId

GO


--declare @parentNdeid int = (select id from node where name=N'G2G')

--declare @ActionId bigint;
--INSERT INTO [dbo].[Action] ([Name], [Order], [Icon], [Color], [Tooltip], [ParentActionId], [Type], [JsFunction],
--							[ShowInReadMode], [ShowInEditMode], [CreatedByUserId], [CreatedDate], [ModifiedDate], [CategoryIds])
--     VALUES ('Receive', 1, NULL, N'#00AE8D', 'Receive', NULL, 2, N'CTSCoreComponents.CustomActions.G2GBulkReceiveAction()',
--	 NULL, NULL, 1, GETDATE(), NULL, '')
--	 select @ActionId = @@IDENTITY

--	--INSERT [dbo].[ActionSecurity] ([ActionId], [RoleId]) VALUES (@ActionId, 1)
--	--INSERT [dbo].[ActionSecurity] ([ActionId], [RoleId]) VALUES (@ActionId, 2)
--	--INSERT [dbo].[ActionSecurity] ([ActionId], [RoleId]) VALUES (@ActionId, 3)
--	--INSERT [dbo].[ActionSecurity] ([ActionId], [RoleId]) VALUES (@ActionId, 4)

--	INSERT INTO [dbo].[ActionsNodes] ([ActionId], [NodeId])
--	select @ActionId, [Node].Id
--	from [Node] where [Name] = 'ReceiveOrReject' and ParentNodeId=@parentNdeid
--GO

--declare @parentNdeid int = (select id from node where name=N'G2G')

--declare @ActionId bigint;
--INSERT INTO [dbo].[Action] ([Name], [Order], [Icon], [Color], [Tooltip], [ParentActionId], [Type], [JsFunction],
--							[ShowInReadMode], [ShowInEditMode], [CreatedByUserId], [CreatedDate], [ModifiedDate], [CategoryIds])
--     VALUES ('Reject', 1, NULL, N'#ff050e', 'Reject', NULL, 2, N'CTSCoreComponents.CustomActions.G2GBulkRejectAction()',
--	 NULL, NULL, 1, GETDATE(), NULL, '')
--	 select @ActionId = @@IDENTITY
	 
--	--INSERT [dbo].[ActionSecurity] ([ActionId], [RoleId]) VALUES (@ActionId, 1)
--	--INSERT [dbo].[ActionSecurity] ([ActionId], [RoleId]) VALUES (@ActionId, 2)
--	--INSERT [dbo].[ActionSecurity] ([ActionId], [RoleId]) VALUES (@ActionId, 3)
--	--INSERT [dbo].[ActionSecurity] ([ActionId], [RoleId]) VALUES (@ActionId, 4)

--	INSERT INTO [dbo].[ActionsNodes] ([ActionId], [NodeId])
--	select @ActionId, [Node].Id
--	from [Node] where [Name] = 'ReceiveOrReject'  and ParentNodeId=@parentNdeid
--GO

If not exists (select * from [dbo].[Parameter]  where [Keyword] = N'MyClosedFollowUp')
Begin

  INSERT [dbo].[Parameter] ([Keyword], [Description], [Content], [IsSystem]) 
  VALUES (N'MyClosedFollowUp', N'Id of MyClosedFollowUp node', N'16', 0)
End 
GO


set identity_insert [Status] ON
go

if not exists (select 1 from [Status] where name=N'Receive or Reject')
INSERT INTO [dbo].[Status] ([id], [Name], [NameAr], [NameFr], [Color], [IsSystem]) VALUES (19, N'Receive or Reject', N'استقبال او رفض', N'Recevoir ou rejeter', NULL, 0)
GO


set identity_insert [Status] OFF
go
