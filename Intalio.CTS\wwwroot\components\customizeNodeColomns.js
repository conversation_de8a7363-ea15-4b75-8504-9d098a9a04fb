import Intalio from './common.js'
import { Categories, CategoryModel, Nodes } from './lookup.js'
import nodeIndex from './nodeIndex.js';
class CustomizeNodeColomns extends Intalio.Model
{
    constructor()
    {
        super();
        this.roles = [];
    }
}
var NodeInheritProperties = (function (E)
{
    E = {};
    E.Draft = "Draft";
    E.Inbox = "Inbox";
    E.Completed = "Completed";
    E.MyRequests = "MyRequests";
    <PERSON><PERSON>Sent = "Sent";
    E.Closed = "Closed";
  
    E.Columns = function (name)
    {
        switch (name)
        {
            case E.Draft:
                return ["Category", "Subject", "ReferenceNumber", "SendingEntity", "ReceivingEntity", "CreatedDate", "ModifiedDate"];
            case E.MyRequests:
                return ["Category", "Subject", "ReferenceNumber", "SendingEntity", "ReceivingEntity", "CreatedDate", "DueDate"];
            case E.Inbox:
                return ["Category", "ReferenceNumber", "Subject", "From", "TransferDate", "CreatedDate", "SendingEntity", "ReceivingEntity", "Purpose", "Priority", "Privacy", "CreatedBy", "OpenedDate", "DueDate","NumberOfDays","Owner"];
            case E.Completed:
                return ["Category", "ReferenceNumber", "Subject", "From", "TransferDate", "Owner", "SendingEntity", "ReceivingEntity", "Purpose", "Priority", "Privacy", "CreatedBy", "OpenedDate", "ClosedDate"];
            case E.Sent:
                return ["Category", "ReferenceNumber", "Subject", "To", "TransferDate", "Owner", "SendingEntity", "ReceivingEntity", "Purpose", "Priority", "Privacy", "CreatedBy", "OpenedDate", "ClosedDate"];
            case E.Closed:
                return ["Category", "Subject", "ReferenceNumber", "SendingEntity", "ReceivingEntity", "CreatedDate", "ClosedDate"];
        }
    };
   
    E.getColumns = function (name)
    {
        var columnList = E.Columns(name);
        var options = new Array();
        columnList.forEach(function (column, index)
        {
            options.push({
                id: column,
                text: Resources[column] || column
            });
        });
        return options;
    };
    E.initColumns = function (name)
    {
        $("#columnDiv").removeClass("col-md-3").addClass("col-md-6");
        if ($("#columnNumberDiv").length > 0)
        {
            if ($("#orderColumnContainer").hasClass("col-md-3"))
            {
                $("#orderColumnContainer").attr("class", "").addClass("col-md-6");
            } else
            {
                $("#orderColumnContainer").attr("class", "").addClass("col-md-3");
            }
        }
        $("#columnNumberDiv").remove();
        $("#columnFunctionDiv").remove();
        $("#tooltipCustom").remove();
       
    };
   
    return E;
}
(NodeInheritProperties));
//var NodeInheritProperties = nodeIndex.NodeInheritProperties;
var gColumnTable;
function initColumns(enableColumnDetail, firstInit)
{
    Common.gridCommon();
    if (!firstInit)
    {
        $('#columnsTable').DataTable().destroy();
    }
    gColumnTable = $('#columnsTable').DataTable({
        dom: 't',
        destroy: true,
        paging: false,
        ordering: false,
        columns: [
            {
                title: Resources.Name,
                "orderable": false,
                "render": function (data, type, row)
                {
                    var name = row.name;
                    var translated = Resources[row.name];
                    if (translated)
                    {
                        name = translated;
                    }
                    if (row.isCustom)
                    {
                        return "<span class='text-danger'>" + Resources.Custom + " : (" + Resources.Name + ": " + name + " , " + Resources.JsFunctionName + ": " + row.customFunctionName + ")</span>";
                    }
                    return name;
                }
            },
            {
                title: Resources.Order,
                'width': '100px',
                "orderable": false,
                "render": function (data, type, row)
                {
                    return row.order;
                }
            },
            {
                title: Resources.ColumnDetail,
                visible: true,
                'width': '100px',
                "orderable": false,
                "render": function (data, type, row)
                {
                    if (row.isColumnDetail !== "" && row.isColumnDetail !== null && row.isColumnDetail === true)
                    {
                        return '<em class="fa success fa-check"></em>';
                    }
                    return '<em class="fa danger fa-close"></em>';
                }
            },
            {
                "autoWidth": false,
                "bAutoWidth": false,
                'width': '10px',
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta)
                {
                    let btnedit = document.createElement("button");
                    btnedit.setAttribute("class", "btn btn-xs btn-primary");
                    btnedit.setAttribute("title", Resources.Edit);
                    btnedit.setAttribute("type", "button");
                    btnedit.setAttribute("action", "edit");
                    btnedit.innerHTML = "<i class='fa fa-edit fa-white'/>";
                    return btnedit.outerHTML;
                }
            },
            {
                "autoWidth": false,
                "bAutoWidth": false,
                'width': '10px',
                'orderable': false,
                'sortable': false,
                'render': function (data, type, full, meta)
                {
                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-danger");
                    btn.setAttribute("title", Resources.Delete);
                    btn.setAttribute("type", "button");
                    btn.setAttribute("action", "delete");
                    btn.innerHTML = "<i class='fa fa-trash-o'/>";
                    return btn.outerHTML;
                }
            }
        ]
    });
    $('#addColumn').off('click').on('click', function ()
    {
        var $form = $('#formPostNode');
        $form.parsley().reset({ group: 'columns' });
        var isValid = $form.parsley().validate({ group: 'columns' });
        if (isValid)
        {
            var columns = getColumns(gColumnTable);
            var name = $("#cmbColumn").val();
            var order = $("#txtColumnOrder").val();

            if (name !== '') {
                if ($(this).attr('action') === 'addRow') {
                    var nameExists = columns.some(item => item.name === name); // Check if name exists
                    var orderExists = columns.some(item => item.order === order); // Check if order exists

                    // Additional checks during editing to ignore the row being edited
                    var currentRowIndex = $('#addColumn').attr('rowindex'); // Get the current row index for edit
                    if (currentRowIndex) {
                        // Exclude the current row from the validation
                        nameExists = columns.some(item => item.name === name && item.rowIndex !== currentRowIndex);
                        orderExists = columns.some(item => item.order === order && item.rowIndex !== currentRowIndex);
                    }

                    // If both name and order are unique, proceed
                    if (!nameExists && !orderExists) {
                        var json = {};
                        if (name === "Custom") {
                            json.name = $("#txtColumnName").val();
                            json.customFunctionName = $("#txtColumnFunction").val();
                            json.isCustom = true;
                        } else {
                            json.name = name;
                        }
                        json.order = order;
                        json.isColumnDetail = $("#chkColumnDetail").prop("checked");

                        // Add the new row to the table
                        gColumnTable.row.add(json).draw();

                        // Reset form and change action back to 'addRow'
                        $('#addColumn').attr('action', 'addRow');
                        $('#cmbColumn').attr('required', 'required').val("").trigger("change");
                        $('#txtColumnOrder').val("");
                        $("#chkColumnDetail").prop("checked", false);
                    } else {
                        // Show appropriate alert messages if name or order already exists
                        if (nameExists) {
                            Common.alertMsg(Resources.AlreadyExists); // "Name already exists" message
                        }
                        if (orderExists) {
                            Common.alertMsg(Resources.OrderAlreadyExists); // "Order already exists" message
                        }
                    }
                }
            }

            if ($(this).attr('action') === 'confirmEdit')
            {
                const json = {};
                if (name === "Custom")
                {
                    json.name = $("#txtColumnName").val();
                    json.customFunctionName = $("#txtColumnFunction").val();
                    json.isCustom = true;
                } else
                {
                    json.name = name;
                }
                json.order = $("#txtColumnOrder").val();
                json.isColumnDetail = $("#chkColumnDetail").prop("checked");
                gColumnTable.row($(this).attr('rowindex')).data(json).draw();
                $('#addColumn').attr('action', 'addRow');
                $('#cmbColumn').attr('required', 'required')
                $('#cmbColumn').val("").trigger("change");
                $('#txtColumnOrder').val("");
                $("#chkColumnDetail").prop("checked", false);
            }
        }
    });
    $('#columnsTable').on('click', 'tbody tr button[action="edit"]', function () {
        const row = gColumnTable.row($(event.target).closest('tr'));
        $('#addColumn').attr('rowindex', row.index());
        $('#addColumn').attr('action', 'confirmEdit');
        var data = row.data();

        // Set default values for form fields
        $('#cmbColumn').attr('required', 'required');
        if (data.isCustom === true) {
            $('#cmbColumn').val("Custom").trigger("change");
            $("#txtColumnName").val(data.name);
            $("#txtColumnFunction").val(data.customFunctionName);
        } else {
            $('#cmbColumn').val(data.name).trigger("change");
        }
        $('#txtColumnOrder').val(data.order);
        $("#chkColumnDetail").prop("checked", data.isColumnDetail);

        // Add validation to check if order or name already exists during editing
        $('#addColumn').off('click').on('click', function () {
            var $form = $('#formPostNode');
            $form.parsley().reset({ group: 'columns' });
            var isValid = $form.parsley().validate({ group: 'columns' });

            if (isValid) {
                var columns = getColumns(gColumnTable); // Assume this is a method to get all column data
                var name = $("#cmbColumn").val();
                var order = $("#txtColumnOrder").val();
                var currentRowIndex = parseInt($('#addColumn').attr('rowindex'), 10);

                // Check if the order or name already exists in other rows excluding the current one
                var orderExists = columns.some((item, index) => item.order === order && index !== currentRowIndex);
                var nameExists = columns.some((item, index) => item.name === name && index !== currentRowIndex);

                if (orderExists) {
                    // Show alert if the order already exists
                    Common.alertMsg(Resources.OrderAlreadyExists); // "Order already exists" message
                } else if (nameExists) {
                    // Show alert if the name already exists
                    Common.alertMsg(Resources.NameAlreadyExists); // "Name already exists" message
                } else {
                    // Proceed with your column update
                    const json = {};
                    if (name === "Custom") {
                        json.name = $("#txtColumnName").val();
                        json.customFunctionName = $("#txtColumnFunction").val();
                        json.isCustom = true;
                    } else {
                        json.name = name;
                    }
                    json.order = order;
                    json.isColumnDetail = $("#chkColumnDetail").prop("checked");

                    // Update the row in the table
                    gColumnTable.row(currentRowIndex).data(json).draw(); // Here we update, not add.

                    // Reset form and change action back to 'addRow'
                    $('#addColumn').attr('action', 'addRow');
                    $('#cmbColumn').attr('required', 'required').val("").trigger("change");
                    $('#txtColumnOrder').val("");
                    $("#chkColumnDetail").prop("checked", false);
                }
            }
        });
    });

}
var gConditionTable;

function getColumns(datatable)
{
    var data = datatable.rows().data();
    var columns = [];
    data.each(function (value, index)
    {
        var jsonobj = {};
        jsonobj.name = value.name;
        jsonobj.order = value.order;
        jsonobj.isColumnDetail = value.isColumnDetail;
        jsonobj.customFunctionName = value.customFunctionName;
        jsonobj.isCustom = value.isCustom;
        columns.push(jsonobj);
    });
    return columns;
}

function changeInheritProperties(value) {
    
   
        if (value ) {
            
            $('#cmbColumn').select2().off("change");
            $('#cmbColumn').select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                data: NodeInheritProperties.getColumns(value),
                allowClear: true,
                placeholder: Resources.SelectColumn + " *",
                dropdownParent: $('#columnContainer'),
                templateResult: function (data) {
                    if (data.id === "Custom") {
                        return $("<span class='text-danger'>" + data.text + "</span>");
                    }
                    return data.text;
                }
            }).on('change', function () {
                $(this).trigger('input');
                NodeInheritProperties.initColumns($(this).val());
            });
            $("#cmbColumn").val('').trigger('change');


            initColumns(true,true);
            $("#columnDetailContainer").show();
            $("#orderColumnContainer").removeClass("col-md-6").addClass("col-md-3");

            
            
        } 
    
}
function openNodeNode(nodeId) {
    
        $('#txtColumnOrder').val("");
        $("#chkColumnDetail").prop('checked', false);
        $('#columnsTable').DataTable().rows().remove().draw();
        $("#columnDetailContainer").show();
        $("#orderColumnContainer").removeClass("col-md-6").addClass("col-md-3");

    Common.ajaxGet('/Node/ListTreeNode', null, function (data) {

        var node = data.children.filter(node => node.id === nodeId || node.children.some(child => child.id === nodeId));// .children.some(child => child.id === nodeId)
        
        document.getElementById('hdId').value = nodeId;
        var newColomns = new Nodes().getCustomcolumns(nodeId);

        if (newColomns) {
            var columns = JSON.parse(newColomns.content);
        }

        else if (node[0].children.length > 0) {
            var columns = JSON.parse(node[0].children[0].data.columns);
          
        }
        else {
            var columns = JSON.parse(node[0].data.columns);
        }

        //var columns = JSON.parse(node[0].children[0].data.columns);
        if (columns) {
            $('#columnsTable').DataTable().rows.add(columns);
            $('#columnsTable').DataTable().draw();
        }

        }, function () { Common.showScreenErrorMsg(); }, false);

}


class CustomizeNodeColomnsView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "customizeNodeColomns", model);
    }
    render()
    {
        var self = this;
        var model = self.model;
        var newColomns = new Nodes().getCustomcolumns(model.nodeId);
        if (newColomns)
            $("#btnRevertColomns").removeClass("disabled");

        var nodes = new Nodes().getTreeNodes();
        var node = nodes.children.filter(node => node.id === model.nodeId || node.children.some(child => child.id === model.nodeId));// children.some(child => child.id === model.nodeId));
        if (node[0].children.length > 0) {
            var columns = JSON.parse(node[0].children[0].data.columns);

        }
        else {
            var columns = JSON.parse(node[0].data.columns);
        }
        //var node = nodes.children.filter(node => node.children.some(child => child.id === model.nodeId));
        //var columns = JSON.parse(node[0].children[0].data.columns);

        
        $('#btnSaveColomns').on('click', function () {
            var $form = $('#formPostNode');
            $form.parsley().reset();
            var isValid = $form.parsley().validate({ group: 'primary' });
            if (isValid) {
                
                var parameter = {};
                var paramList = [];
                
                var columns = getColumns(gColumnTable);
                
                var btn = $('#btnSaveColomns');
                btn.button('loading');
                var btnClose = $('#btnClose');
                btnClose.attr('disabled', 'disabled');
                
                parameter.Keyword = "NodeColumns_" + model.nodeId;
                parameter.Content = columns ? JSON.stringify(columns) : "";
                parameter.Description = "Custom Node Columns";
                paramList.push(parameter);


                Common.ajaxPost('/UserParameter/UpdateCustomParameters', { parameters: paramList }, function (data) {
                   
                    if (data == "") {
                        Common.showScreenSuccessMsg();

                    }
                    btn.button('reset');
                    btnClose.removeAttr('disabled');
                    var revertBtn = $('#btnRevertColomns');
                    revertBtn.removeClass('disabled');
                    location.reload();
                }, function () { btn.button('reset'); btnClose.removeAttr('disabled'); Common.showScreenErrorMsg(); }, false);

            }
        });
        $('#btnRevertColomns').on('click', function () {
           
            var btn = $('#btnRevertColomns');
                var btnClose = $('#btnClose');
                btnClose.attr('disabled', 'disabled');

            var paramterId = new Nodes().getParamterIdCustomcolumns(model.nodeId);
            if (paramterId != 0) {
                Common.ajaxDelete('/UserParameter/Delete', { Id: paramterId }, function (data) {
                    
                    if (data == "") {
                        Common.showScreenSuccessMsg();
                        //$("#columnsTable").DataTable().ajax.reload();

                        $('#columnsTable').DataTable().rows().remove().draw();
                        if (columns) {
                                $('#columnsTable').DataTable().rows.add(columns);
                                $('#columnsTable').DataTable().draw();
                            }
                        //Common.ajaxGet('/Node/ListTreeNode', null, function (data) {

                        //    ;
                        //    //var node = data.children.filter(node => node.children.id == nodeId);
                        //    var node = data.children.filter(node =>
                        //        node.children.some(child => child.id === nodeId)
                        //    );
                        //    //document.getElementById('hdId').value = nodeId;
                        //    //var newColomns = new Nodes().getCustomcolumns(nodeId);
                        //    //if (newColomns) {
                        //    //    node[0].children[0].data.columns = newColomns.content;
                        //    //}

                        //    var columns = JSON.parse(node[0].children[0].data.columns);
                        //    if (columns) {
                        //        $('#columnsTable').DataTable().rows.add(columns);
                        //        $('#columnsTable').DataTable().draw();
                        //    }

                        //}, function () { Common.showScreenErrorMsg(); }, false);

                    }
                    btn.button('reset');
                    btnClose.removeAttr('disabled');
                    btn.attr('disabled', 'disabled');
                    location.reload();
                }, function () { btn.button('reset'); btnClose.removeAttr('disabled'); Common.showScreenErrorMsg(); }, false);
            }

               

            
        });
        $.fn.select2.defaults.set("theme", "bootstrap");
        changeInheritProperties(model.text);
        openNodeNode(model.nodeId);
        $('#cmbColumn').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: Resources.SelectColumn + " *",
            dropdownParent: $('#columnContainer'),
            templateResult: function (data)
            {
                if (data.id === "Custom")
                {
                    return $("<span class='text-danger'>" + data.text + "</span>");
                }
                return data.text;
            }
        });
        $("#cmbColumn").val('').trigger('change');
      
        initColumns(false, true);
        $('#columnsTable').on('click', 'tbody tr button[action="delete"]', function (event) {
            gColumnTable.row($(event.target).closest('tr')).remove().draw();
            $('#addColumn').attr('action', 'addRow');
        });

    }
}
export default { CustomizeNodeColomns, CustomizeNodeColomnsView };
