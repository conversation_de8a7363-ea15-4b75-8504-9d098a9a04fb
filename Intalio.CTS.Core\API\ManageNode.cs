﻿using Aspose.Cells.Drawing.Texts;
using Intalio.Core;
using Intalio.Core.DAL;
using Intalio.CTS.Core.Model;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Intalio.CTS.Core.API
{
    public static class ManageNode
    {
        #region Public Methods

        /// <summary>
        /// Get node conditions in expression builder
        /// </summary>
        /// <param name="nodeId"></param>
        /// <returns></returns>
        public static (ExpressionBuilderFilters Filters, bool? OverDue) GetNodeConditionsInExpressionBuilder(short nodeId)
        {
            ExpressionBuilderFilters filters = new ExpressionBuilderFilters();
            //List<long> requestStatus = new List<long>() { (long)RequestStatus.Accepted , 0 }; // 0 for default 
            bool? overdue = null;
            var node = new Node().Find(nodeId);
            if (!node.IsNull() && !string.IsNullOrEmpty(node.Conditions))
            {
                List<ConditionModel> models = Intalio.Core.Helper.DeserializeJson<List<ConditionModel>>(node.Conditions);
                foreach (var model in models)
                {
                    if (!model.IsNull())
                    {
                        object inherit;
                        Enum.TryParse(typeof(NodeInherit), node.Inherit, out inherit);
                      
                        switch ((NodeInherit)inherit)
                        {
                            case NodeInherit.Draft:
                                switch (model.Name)
                                {
                                    case "Category":
                                        //filters.Add("CategoryId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        filters.Add("CategoryId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => (short)t.Id).ToList(), Operator.AnyShort);
                                        break;
                                    case "DocumentCreatedDate":
                                        var dateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime date = new DateTime();
                                        if (dateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            date = DateTime.Today.AddDays(-dateObject.Number);
                                        }
                                        else if (dateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            date = DateTime.Today.AddMonths(-dateObject.Number);
                                        }
                                        if (dateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            date = DateTime.Today.AddYears(-dateObject.Number);
                                        }
                                        filters.Add("CreatedDate.Date", date, (dateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "CreatedByStructure":
                                        if ((bool)model.Value)
                                        {
                                            filters.Add("CreatedByStructure", true, Operator.Equals);
                                        }
                                        break;
                                    case "DraftStatus":
                                        filters.Add("DraftStatus", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                }
                                break;
                            case NodeInherit.MyRequests:
                                switch (model.Name)
                                {
                                    case "Category":
                                        filters.Add("CategoryId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "DocumentCreatedDate":
                                        var dateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime date = new DateTime();
                                        if (dateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            date = DateTime.Today.AddDays(-dateObject.Number);
                                        }
                                        else if (dateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            date = DateTime.Today.AddMonths(-dateObject.Number);
                                        }
                                        if (dateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            date = DateTime.Today.AddYears(-dateObject.Number);
                                        }
                                        filters.Add("CreatedDate.Date", date, (dateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                }
                                break;
                            case NodeInherit.Inbox:
                                
                                switch (model.Name)
                                {
                                    case "Category":
                                        filters.Add("Document.CategoryId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "RequestStatus":
                                        filters.Add("RequestStatus", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "DocumentCreatedDate":
                                        var dateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime date = new DateTime();
                                        if (dateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            date = DateTime.Today.AddDays(-dateObject.Number);
                                        }
                                        else if (dateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            date = DateTime.Today.AddMonths(-dateObject.Number);
                                        }
                                        if (dateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            date = DateTime.Today.AddYears(-dateObject.Number);
                                        }
                                        filters.Add("Document.CreatedDate.Date", date, (dateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "TransferCreatedDate":
                                        var taskDateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime taskDate = new DateTime();
                                        if (taskDateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            taskDate = DateTime.Today.AddDays(-taskDateObject.Number);
                                        }
                                        else if (taskDateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            taskDate = DateTime.Today.AddMonths(-taskDateObject.Number);
                                        }
                                        if (taskDateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            taskDate = DateTime.Today.AddYears(-taskDateObject.Number);
                                        }
                                        filters.Add("CreatedDate.Date", taskDate, (taskDateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "Read":
                                        var read = (bool)model.Value;
                                        if (read)
                                        {
                                            filters.Add("OpenedDate", null, Operator.NotEqual);
                                        }
                                        else
                                        {
                                            filters.Add("OpenedDate", null, Operator.Equals);
                                        }
                                        break;
                                    case "Locked":
                                        var locked = (bool)model.Value;
                                        if (locked)
                                        {
                                            filters.Add("OwnerUserId", null, Operator.NotEqual);
                                            filters.Add("ToUserId", null, Operator.Equals);
                                        }
                                        else
                                        {
                                            filters.Add("OwnerUserId", null, Operator.Equals);
                                        }
                                        break;
                                    case "OverDue":
                                        overdue = (bool)model.Value;
                                        break;
                                    case "ToUser":
                                        if((bool)model.Value)
                                        {
                                            filters.Add("ToUserId", UserContextAccessor.UserContext!.Id, Operator.Equals);
                                        }
                                        break;
                                    case "ToStructure":
                                        if((bool)model.Value)
                                        {
                                            filters.Add("ToUserId", null, Operator.Equals);
                                        }
                                        break;
                                    case "FromStructure":
                                        filters.Add("FromStructureId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "FromUser":
                                        filters.Add("FromUserId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "IsSigned":
                                        var isSigned = (bool)model.Value;
                                        filters.Add("IsSigned", isSigned, Operator.Equals);
                                        break; 
                                    case "IsExported":
                                        var isExported = (bool)model.Value;
                                        filters.Add("IsExported", isExported, Operator.Equals);
                                        break;

                                }
                                break;
                            case NodeInherit.StructureInbox:
                                switch (model.Name)
                                {
                                    case "Category":
                                        filters.Add("Document.CategoryId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "DocumentCreatedDate":
                                        var dateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime date = new DateTime();
                                        if (dateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            date = DateTime.Today.AddDays(-dateObject.Number);
                                        }
                                        else if (dateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            date = DateTime.Today.AddMonths(-dateObject.Number);
                                        }
                                        if (dateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            date = DateTime.Today.AddYears(-dateObject.Number);
                                        }
                                        filters.Add("Document.CreatedDate.Date", date, (dateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "TransferCreatedDate":
                                        var taskDateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime taskDate = new DateTime();
                                        if (taskDateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            taskDate = DateTime.Today.AddDays(-taskDateObject.Number);
                                        }
                                        else if (taskDateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            taskDate = DateTime.Today.AddMonths(-taskDateObject.Number);
                                        }
                                        if (taskDateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            taskDate = DateTime.Today.AddYears(-taskDateObject.Number);
                                        }
                                        filters.Add("CreatedDate.Date", taskDate, (taskDateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "Read":
                                        var read = (bool)model.Value;
                                        if (read)
                                        {
                                            filters.Add("OpenedDate", null, Operator.NotEqual);
                                        }
                                        else
                                        {
                                            filters.Add("OpenedDate", null, Operator.Equals);
                                        }
                                        break;
                                    case "Locked":
                                        var locked = (bool)model.Value;
                                        if (locked)
                                        {
                                            filters.Add("OwnerUserId", null, Operator.NotEqual);
                                            filters.Add("ToUserId", null, Operator.Equals);
                                        }
                                        else
                                        {
                                            filters.Add("OwnerUserId", null, Operator.Equals);
                                        }
                                        break;
                                    case "OverDue":
                                        overdue = (bool)model.Value;
                                        break;
                                    case "FromStructure":
                                        filters.Add("FromStructureId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "FromUser":
                                        filters.Add("FromUserId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "IsSigned":
                                        var isSigned = (bool)model.Value;
                                        filters.Add("IsSigned", isSigned, Operator.Equals);
                                        break;
                                    case "IsExported":
                                        var isExported = (bool)model.Value;
                                        filters.Add("IsExported", isExported, Operator.Equals);
                                        break;

                                }
                                break;
                            case NodeInherit.Completed:
                                switch (model.Name)
                                {
                                    case "Category":
                                        filters.Add("Document.CategoryId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "DocumentCreatedDate":
                                        var dateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime date = new DateTime();
                                        if (dateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            date = DateTime.Today.AddDays(-dateObject.Number);
                                        }
                                        else if (dateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            date = DateTime.Today.AddMonths(-dateObject.Number);
                                        }
                                        if (dateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            date = DateTime.Today.AddYears(-dateObject.Number);
                                        }
                                        filters.Add("Document.CreatedDate.Date", date, (dateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "TransferClosedDate":
                                        var taskDateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime taskDate = new DateTime();
                                        if (taskDateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            taskDate = DateTime.Today.AddDays(-taskDateObject.Number);
                                        }
                                        else if (taskDateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            taskDate = DateTime.Today.AddMonths(-taskDateObject.Number);
                                        }
                                        if (taskDateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            taskDate = DateTime.Today.AddYears(-taskDateObject.Number);
                                        }
                                        filters.Add("ClosedDate", taskDate, (taskDateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "CCed":
                                        var cced = (bool)model.Value;
                                        if (cced)
                                        {
                                            filters.Add("CCed", true, Operator.Equals);
                                        }
                                        else
                                        {
                                            filters.Add("CCed", true, Operator.NotEqual);
                                        }
                                        break;
                                }
                                break;
                            case NodeInherit.Sent:
                                switch (model.Name)
                                {
                                    case "Category":
                                        filters.Add("Document.CategoryId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "RequestStatus":
                                        filters.Add("RequestStatus", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "DocumentCreatedDate":
                                        var dateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime date = new DateTime();
                                        if (dateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            date = DateTime.Today.AddDays(-dateObject.Number);
                                        }
                                        else if (dateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            date = DateTime.Today.AddMonths(-dateObject.Number);
                                        }
                                        if (dateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            date = DateTime.Today.AddYears(-dateObject.Number);
                                        }
                                        filters.Add("Document.CreatedDate.Date", date, (dateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "TransferClosedDate":
                                        var taskDateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime taskDate = new DateTime();
                                        if (taskDateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            taskDate = DateTime.Today.AddDays(-taskDateObject.Number);
                                        }
                                        else if (taskDateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            taskDate = DateTime.Today.AddMonths(-taskDateObject.Number);
                                        }
                                        if (taskDateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            taskDate = DateTime.Today.AddYears(-taskDateObject.Number);
                                        }
                                        filters.Add("ClosedDate", taskDate, (taskDateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "IsClosedTransfer":
                                        var isClosedTransfer = (bool)model.Value;
                                        if (isClosedTransfer)
                                        {
                                            filters.Add("ClosedDate", null, Operator.NotEqual);
                                        }
                                        else
                                        {
                                            filters.Add("ClosedDate", null, Operator.Equals);
                                        }
                                        break;
                                    case "IsCompletedFollowUp":
                                        var isCompletedFollowUp = (bool)model.Value;
                                        if (isCompletedFollowUp)
                                        {
                                            filters.Add("StatusId", (short)DocumentStatus.Completed, Operator.Equals);
                                        }
                                        else
                                        {
                                            filters.Add("StatusId", (short)DocumentStatus.Completed, Operator.NotEqual);
                                        }
                                        break;
                                }
                                break;
                            case NodeInherit.StructureSent:
                                switch (model.Name)
                                {
                                    case "Category":
                                        filters.Add("Document.CategoryId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "DocumentCreatedDate":
                                        var dateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime date = new DateTime();
                                        if (dateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            date = DateTime.Today.AddDays(-dateObject.Number);
                                        }
                                        else if (dateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            date = DateTime.Today.AddMonths(-dateObject.Number);
                                        }
                                        if (dateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            date = DateTime.Today.AddYears(-dateObject.Number);
                                        }
                                        filters.Add("Document.CreatedDate.Date", date, (dateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "TransferClosedDate":
                                        var taskDateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime taskDate = new DateTime();
                                        if (taskDateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            taskDate = DateTime.Today.AddDays(-taskDateObject.Number);
                                        }
                                        else if (taskDateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            taskDate = DateTime.Today.AddMonths(-taskDateObject.Number);
                                        }
                                        if (taskDateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            taskDate = DateTime.Today.AddYears(-taskDateObject.Number);
                                        }
                                        filters.Add("ClosedDate", taskDate, (taskDateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "IsClosedTransfer":
                                        var isClosedTransfer = (bool)model.Value;
                                        if (isClosedTransfer)
                                        {
                                            filters.Add("ClosedDate", null, Operator.NotEqual);
                                        }
                                        else
                                        {
                                            filters.Add("ClosedDate", null, Operator.Equals);
                                        }
                                        break;
                                    case "IsCompletedFollowUp":
                                        var isCompletedFollowUp = (bool)model.Value;
                                        if (isCompletedFollowUp)
                                        {
                                            filters.Add("StatusId", (short)DocumentStatus.Completed, Operator.Equals);
                                        }
                                        else
                                        {
                                            filters.Add("StatusId", (short)DocumentStatus.Completed, Operator.NotEqual);
                                        }
                                        break;
                                }
                                break;
                            case NodeInherit.Closed:
                                switch (model.Name)
                                {
                                    case "Category":
                                        filters.Add("CategoryId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "DocumentCreatedDate":
                                        var dateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime date = new DateTime();
                                        if (dateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            date = DateTime.Today.AddDays(-dateObject.Number);
                                        }
                                        else if (dateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            date = DateTime.Today.AddMonths(-dateObject.Number);
                                        }
                                        if (dateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            date = DateTime.Today.AddYears(-dateObject.Number);
                                        }
                                        filters.Add("CreatedDate.Date", date, (dateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                    case "DocumentCompletedDate":
                                        var taskDateObject = ((JObject)model.Value).ToObject<ConditionDateModel>();
                                        DateTime taskDate = new DateTime();
                                        if (taskDateObject.Date == ConditionDate.Day.ToString())
                                        {
                                            taskDate = DateTime.Today.AddDays(-taskDateObject.Number);
                                        }
                                        else if (taskDateObject.Date == ConditionDate.Month.ToString())
                                        {
                                            taskDate = DateTime.Today.AddMonths(-taskDateObject.Number);
                                        }
                                        if (taskDateObject.Date == ConditionDate.Year.ToString())
                                        {
                                            taskDate = DateTime.Today.AddYears(-taskDateObject.Number);
                                        }
                                        filters.Add("ClosedDate", taskDate, (taskDateObject.Sign == "<=" ? Operator.LessThanOrEqualTo : Operator.LessThan));
                                        break;
                                }
                                break;
                            case NodeInherit.FollowUp:
                                switch (model.Name)
                                {
                                    case "Category":
                                        filters.Add("FollowUpDocument.CategoryId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break;
                                    case "FollowUpStatus":
                                        filters.Add("FollowUpStatusId", ((JArray)model.Value).ToObject<List<Intalio.Core.Model.ValueText>>().Select(t => t.Id).ToList(), Operator.Any);
                                        break; 
                                    case "IsHasNote":

                                        filters.Add("FollowUpDocument.Note.Count", 0, (bool)model.Value==true? Operator.GreaterThan:Operator.Equals);
                                        break;
                                    case "OverDue":
                                        overdue = (bool)model.Value;
                                        break;
                                }
                                break;

                        }
                    }
                }
            }
            return (filters, overdue);
        }
        public static Node findByName(string nodeName)
        {
            return new Node().FindWithIncludeByName(nodeName);
        }
        #endregion
    }
}
