﻿using System.Collections.Generic;

namespace Intalio.CTS.Core.Model
{
    public class TransferInboxListViewVipModel
    {
        public long Id { get; set; }
        public long DocumentId { get; set; }
        public long? attachmentId { get; set; }
        public string AttachmentVersion { get; set; }
        public int CategoryId { get; set; }
        public string Subject { get; set; }
        public string ReferenceNumber { get; set; }
        public string TransferDate { get; set; }
        public string DueDate { get; set; }
        public string? DocumentDueDate { get; set; }
        public string FromUser { get; set; }
        public string FromStructure { get; set; }
        public string OpenedDate { get; set; }
        public long? OwnerUserId { get; set; }
        public long? OwnerDelegatedUserId { get; set; }
        public long? delegationId { get; set; }
        public bool IsOverDue { get; set; }
        public bool IsRead { get; set; }
        public bool IsLocked { get; set; }
        public string LockedBy { get; set; }
        public string LockedByDelegatedUser { get; set; }
        public string LockedDate { get; set; }
        public long? FromUserId { get; set; }
        public long CreatedByUserId { get; set; }
        public bool SentToStructure { get; set; }
        public bool SentToUser { get; set; }
        public bool Cced { get; set; }
        public short? ImportanceId { get; set; }
        public long? ToStructureId { get; set; }
        public string ToStructure { get; set; }
        public string PurposeName { get; set; }
        public List<ReceivingEntityModel> ReceivingEntities { get; set; }
        public short? PriorityId { get; set; }
        public short? PrivacyId { get; set; }
        public string? RequestStatus { get; set; }
        public bool IsSigned { get; set; }

    }
    public class TransferCompletedListViewVipModel
    {
        public long Id { get; set; }
        public long DocumentId { get; set; }
        public int CategoryId { get; set; }
        public string Subject { get; set; }
        public string ReferenceNumber { get; set; }
        public string TransferDate { get; set; }
        public string FromUser { get; set; }
        public string FromStructure { get; set; }
        public bool IsRead { get; set; }
        public short? ImportanceId { get; set; }
        public string ToStructure { get; set; }
        public string PurposeName { get; set; }
        public short? PriorityId { get; set; }
        public short? PrivacyId { get; set; }
    }
    public class TransferSentListViewVipModel
    {
        public long Id { get; set; }
        public long DocumentId { get; set; }
        public int CategoryId { get; set; }
        public string Subject { get; set; }
        public string ReferenceNumber { get; set; }
        public string TransferDate { get; set; }
        public string ToUser { get; set; }
        public string ToStructure { get; set; }
        public bool IsRead { get; set; }
        public short? ImportanceId { get; set; }
        public string PurposeName { get; set; }
        public string FromStructure { get; set; }
        public short? PriorityId { get; set; }
        public short? PrivacyId { get; set; }

    }
    public class DocumentDraftListViewVipModel
    {
        public long Id { get; set; }
        public string Subject { get; set; }
        public string SendingEntity { get; set; }
        public int CategoryId { get; set; }
        public short? ImportanceId { get; set; }
        public string ReferenceNumber { get; set; }
        public string CreatedDate { get; set; }
        public short? PriorityId { get; set; }
        public short? PrivacyId { get; set; }
        public int DraftStatusId { get; set; }
    }
}