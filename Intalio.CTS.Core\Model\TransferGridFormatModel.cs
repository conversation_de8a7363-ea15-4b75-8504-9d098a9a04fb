﻿using System.Collections.Generic;

namespace Intalio.CTS.Core.Model
{
    public class TransferGridFormatModel
    {
        public long DocumentId { get; set; }
        public long? ParentTransferId { get; set; }
        public string SendingEntity { get; set; }
        public string ReceivingEntity { get; set; }
        public List<ReceivingEntityModel> ReceivingEntityId { get; set; }
        public long CreatedByStructureId { get; set; }
        public long? FromStructureId { get; set; }
        public string FromStructure { get; set; }
        public long? ToStructureId { get; set; }
        public string ToStructure { get; set; }
        public string FromUser { get; set; }
        public string ToUser { get; set; }
        public string Purpose { get; set; }
        public string Priority { get; set; }
        public string Subject { get; set; }
        public string Instruction { get; set; }
        public short? PurposeId { get; set; }
        public short? PriorityId { get; set; }
        public short? PrivacyId { get; set; }
        public short? ImportanceId { get; set; }
        public string DueDate { get; set; }
        public string? DocumentDueDate { get; set; }
        public string CreatedDate { get; set; }
        public string OpenedDate { get; set; }
        public string ClosedDate { get; set; }
        public string CreatedByUser { get; set; }
        public long? OwnerUserId { get; set; }
        public bool ViewMode { get; set; }
        public bool SentToUser { get; set; }
        public bool Cced { get; set; }
        public bool VoiceNote { get; set; }
        public bool? ByTemplate { get; set; }
        public bool ForSignature { get; set; }
        public long? WorkflowStepId { get; set; }
        public string InitiatorUser { get; set; }
        public bool IsWorkflowReturned { get; set; }
        public string nextStepUserName { get; set; }
        public bool HasReferenceNumber { get; set; }
        public bool IsFollowUp { get; set; }
        public bool? AssigneeOperationPermission { get; set; }
        public bool? FullControl { get; set; }
        public short? StatusId { get; set; }
        public bool? IsTaskCreator { get; set; }
        public bool? HasAttachments { get; set; }
        public bool? HasUserCofigureSignature { get; set; }
        public bool AllowSign { get; set; }
        public string ReferenceNumber { get; set; }
        public string AttachmentExtention { get; set; }
        public string AttachmentVersion { get; set; }
        public bool TemplateHasSignature { get; set; }
        public bool ShowAttachmentProperties { get; set; }
        public bool IsSigned { get; set; }
        public bool IsDocumentSigned { get; set; }
        public List<ReceivingEntityModel> DocumentCarbonCopy { get; set; }

        public TransferGridFormatModel()
        {
            ReceivingEntityId = new List<ReceivingEntityModel>();
        }
    }
}