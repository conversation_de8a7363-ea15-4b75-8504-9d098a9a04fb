param (
    [string]$SourceFolder = "wwwroot",
    [string]$OutputFolder = "publish/wwwroot",
    [string]$ManifestFile = ""
)


function Get-FileMD5Hash {
    param ([string]$filePath)
    $md5 = [System.Security.Cryptography.MD5]::Create()
    $stream = [System.IO.File]::OpenRead($filePath)
    try {
        $hashBytes = $md5.ComputeHash($stream)
    } finally {
        $stream.Dispose()
    }
    return -join ($hashBytes | ForEach-Object { $_.ToString("x2") })
}


$allowedExtensions = @(".js", ".css")
# Folders to cache
$folders = @("js", "css", "components")
$SourceWebRoot = Join-Path $SourceFolder "wwwroot"
$OutputWebRoot = Join-Path $SourceFolder $OutputFolder
$OutputWebRoot = Join-Path $OutputWebRoot "wwwroot"

if (-Not (Test-Path $OutputWebRoot)) {
    New-Item -ItemType Directory -Force -Path $OutputWebRoot | Out-Null
}

if (-not $ManifestFile) {
    $ManifestFile = Join-Path $OutputWebRoot "manifest.json"
}

$manifest = @{}
foreach ($folder in $folders) {
    $source = Join-Path $SourceWebRoot $folder
    $out = Join-Path $OutputWebRoot $folder

    if (-Not (Test-Path $source))
    {
        continue
    }
    New-Item -ItemType Directory -Force -Path $out | Out-Null

    Get-ChildItem -Path $source -Recurse -File | Where-Object {
        $allowedExtensions -contains $_.Extension.ToLower()
    } | ForEach-Object {
        Write-Host Caching: $_.FullName
        $hash = (Get-FileMD5Hash $_.FullName).Substring(0, 8)
        $newName = "$($_.BaseName).$hash$($_.Extension)"
        $relativePath = $_.FullName.Substring($sourceWebRoot.Length).TrimStart("\", "/")
        $newPath = Join-Path $out $newName

        Copy-Item $_.FullName $newPath -Force

        $originalUrl = "/" + $relativePath
        $hashedUrl   = "/" + $folder + "/" + $newName

        $manifest[$originalUrl.Replace("\", "/")] = $hashedUrl.Replace("\", "/")
    }
}
$manifest | ConvertTo-Json -Depth 5 | Set-Content -Encoding UTF8 -Path $ManifestFile
Write-Host "Asset hashing complete. Manifest created at: $ManifestFile"


