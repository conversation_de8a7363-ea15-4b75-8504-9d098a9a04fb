﻿IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'DeleteTeamConfirmation')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (
        N'DeleteTeamConfirmation',
        N'Are you sure you want to delete this team?',
        N'Êtes-vous sûr de vouloir supprimer cette équipe ?',
        N'هل أنت متأكد أنك تريد حذف هذا الفريق؟',
        1
    )
END
---------------------------------------------------------------------------

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'Created')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (
        N'Created',
        N'Created',
        N'Créé',
        N'تم الإنشاء',
        1
    )
END
--------------------------------------------------------------------------------
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'TeamHasFollowUp')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (
        N'TeamHasFollowUp',
        N'This team cannot be deleted because it has related follow-ups.',
        N'Cette équipe ne peut pas être supprimée car elle a des suivis associés.',
        N'لا يمكن حذف هذا الفريق لأنه يحتوي على متابعات مرتبطة به.',
        1
    )
END
------------------------------------------------------------------------------------
IF EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'Accepted')
BEGIN
    UPDATE TranslatorDictionary
    SET 
        [EN] = N'Accepted',
        [FR] = N'Accepté',
        [AR] = N'قبول',
        [IsSystem] = 1
    WHERE Keyword = N'Accepted'
END
ELSE
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (
        N'Accepted',
        N'Accepted',
        N'Accepté',
        N'قبول',
        1
    )
END
----------------------------------------------------------------------------------
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'GovernmentCorrespondence')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (
        N'GovernmentCorrespondence',
        N'Government Correspondence',
        N'Correspondance gouvernementale',
        N'مراسلة حكومية',
        1
    )
END
--------------------------------------------------------------------------------
IF NOT EXISTS (SELECT 1 FROM Parameter WHERE Keyword=N'RCVersion')
BEGIN
	INSERT INTO Parameter (Keyword,[Description], Content, IsSystem)
	VALUES (N'RCVersion','RC version of CTS', 'RC27', 0)
END

IF EXISTS (SELECT 1 FROM Parameter WHERE Keyword=N'RCVersion')
BEGIN
update Parameter set Content='RC27' where Keyword='RCVersion'

END