﻿using Intalio.Core.UI.Filters;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NPOI.SS.Formula.Functions;
using System;
using System.Linq;

namespace Intalio.CTS.Controllers
{
    /// <summary>
    /// Control Favorite Structure and distribution that used when transfer corresponding.
    /// </summary>
    [Route("[controller]/[action]")]
    public class FavoriteStructuresController : BaseController
    {
        /// <summary>
        ///  List of Favorite structure and distribution by the user
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Number of items to skipped</param>
        /// <param name="length">Number of items to be returned</param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult List([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {

                var retValue = ManageFavoriteStructure.List(
                     start, length,this.StructureIds, 
                     this.IsStructureSender, this.UserId, Language);
                var orderedData = retValue.Item2.OrderBy(x => x.Order).ToList();

                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = orderedData
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }
        /// <summary>
        /// List of favorite Structure that used when Transfer
        /// Get Favorite based On Selected User Strucute and Delegation id if exist
        /// </summary>
        /// <param name="userStructureId">Selected User Structure Id When Create Corresponding</param>
        /// <param name="delegationId">Delegation Id if Exist else null</param>
        /// <returns></returns>
        [HideInSwagger]
        [HttpGet]
        public IActionResult List(long userStructureId, long? delegationId)
        {
            try
            {

                var retValue = ManageFavoriteStructure.List(
                    userStructureId, this.StructureIds, this.IsStructureSender,
                    delegationId, this.UserId, Language);

                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }


        /// <summary>
        /// Create New Favorite Structure
        /// </summary>
        /// <param name="favoriteStructureModel"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult Add(FavoriteStructureModel favoriteStructureModel)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest("Invalid model data.");
                }

                if (ManageFavoriteStructure.IsExist(this.UserId, favoriteStructureModel))
                {
                    return Ok("ItemAlreadyExists");
                }
                if (ManageFavoriteStructure.IsOrderExist(this.UserId, favoriteStructureModel))
                {
                    return Ok("OrderAlreadyExists");
                }
                var result = ManageFavoriteStructure.Create(this.UserId, favoriteStructureModel);

                if (result != null)
                {
                    return Ok("Success");
                }
                else
                {
                    return BadRequest("Failed to create the favorite structure.");
                }
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

      


        /// <summary>
        /// Delete Favorite Structure
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult Delete(long id)
        {
            try
            {
                var retValue = ManageFavoriteStructure.Delete(id);

                return StatusCode((int)StatusCodes.Status200OK, retValue);

            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

    }
}
