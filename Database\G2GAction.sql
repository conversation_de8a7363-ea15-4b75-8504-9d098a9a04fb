declare @ActionId bigint;

INSERT INTO [dbo].[Action] ([Name], [Icon], [Color], [Tooltip], [Type], [JsFunction], [ShowInReadMode], [ShowInEditMode], [CreatedByUserId], [CreatedDate], [CategoryIds])
     select N'G2GAction', N'fa fa-outdent', N'#00AE8D', N'G2GAction', 4, N'CTSCoreComponents.CustomActions.G2GAction()', 1, 1, 1, GETDATE(), Category.Id
	 from Category
	 where [Name] like N'%Outgoing%'

select @ActionId = @@IDENTITY

INSERT INTO [dbo].[ActionSecurity] ([ActionId], [RoleId])
select @ActionId, [Role].Id
from [IAM].[dbo].[Role]

INSERT INTO [dbo].[ActionsNodes] ([ActionId], [NodeId])
select @ActionId, [Node].Id
from [Node] 
inner join [Node] as parentNode on parentNode.[Name] = N'Inbox'
where parentNode.Id = [Node].ParentNodeId and [Node].[Name] like N'%Outgoing%' 

Go

INSERT INTO [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) VALUES (N'G2GAction', N'G2G Action', N'G2G Action', N'G2G Action', 0)
Go