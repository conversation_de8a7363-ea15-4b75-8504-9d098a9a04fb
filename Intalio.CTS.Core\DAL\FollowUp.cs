﻿using AngleSharp.Common;
using Aspose.Words;
using Intalio.Core;
using Intalio.Core.DAL;
using Intalio.Core.Interfaces;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using LinqKit;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.EntityFrameworkCore;
using Microsoft.VisualBasic;
using NPOI.OpenXmlFormats;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection.Metadata;
using System.Threading.Tasks;
using Constants = Intalio.Core.Constants;


namespace Intalio.CTS.Core.DAL
{
    public partial class FollowUp : IDbObject<FollowUp>, IDisposable
    {

        #region Private Fields

        private CTSContext _ctx;

        #endregion
        #region Properties

        public long Id { get; set; }
        public long DocumentId { get; set; }
        public long OriginalDocumentId { get; set; }
        public DateTime? FollowUpFromDate { get; set; }
        public DateTime FollowUpToDate { get; set; }
        public long FollowUpStatusId { get; set; }
        public long? TeamId { get; set; }
        public bool? IsPrivate { get; set; }
        public DateTime CreatedDate { get; set; }
        public long CreatedByUserId { get; set; }

        public virtual Document FollowUpDocument { get; set; }
        public virtual Document OriginalDocument { get; set; }
        public virtual FollowUpStatus FollowUpStatus { get; set; }
        public virtual Team Team { get; set; }
        public virtual User CreatedByUser { get; set; }
        public virtual ICollection<FollowUpUsers> FollowUpUsers { get; set; }




        #endregion

        #region Public Methods

        public FollowUp Find(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FollowUp.AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }
        public async Task<FollowUp> FindWithIncludeAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.FollowUp
                                   .Include(f=>f.FollowUpDocument)
                                   .Include(f=>f.OriginalDocument)
                                   .Include(f=> f.FollowUpStatus)
                                   .Include(f=> f.Team)
                                   .Include(f=> f.CreatedByUser)
                                   .Include(f=> f.FollowUpUsers)
                                   .Include(f=> f.FollowUpUsers).ThenInclude(d=>d.FollowUpSecurity).AsNoTracking()
                                   .Include(f => f.FollowUpDocument).ThenInclude(d=>d.Priority).AsNoTracking()
                                   .Include(f => f.OriginalDocument).ThenInclude(d=>d.DocumentReceiverEntity).AsNoTracking()
                                   .Include(f => f.OriginalDocument).ThenInclude(d=>d.Category).AsNoTracking()
                                   .Include(f => f.OriginalDocument).ThenInclude(d=>d.DocumentReceiverEntity).ThenInclude(e=>e.Structure).AsNoTracking()
                                   .Include(f => f.OriginalDocument).ThenInclude(d=>d.SendingEntity).AsNoTrackingWithIdentityResolution()
                                   .Include(f => f.OriginalDocument).ThenInclude(d=>d.Note).AsNoTracking()
                                   .FirstOrDefaultAsync(t => t.Id == id);
            }
        }
        public async Task<FollowUp> Insert(FollowUp item)
        {
            item.Insert();
            return await item.FindWithIncludeAsync(item.Id);
        }
        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                CreatedDate = DateTime.Now;
                ctx.FollowUp.Add(this);
                ctx.SaveChanges();
            }
        }
        public void Update(long userId)
        {
            using (var ctx = new CTSContext())
            {
                foreach (var item in FollowUpUsers.Where(t => t.Id <= 0))
                {
                    ctx.FollowUpUsers.Add(item);
                }
                if (IsPrivate.HasValue && IsPrivate==true && FollowUpUsers.Count > 0)
                {
                        var detachedUsers = FollowUpUsers.Select(u => ctx.FollowUpUsers.AsNoTracking().FirstOrDefault(fu => fu.Id == u.Id && fu.UserId!=userId)).Where(u => u != null).ToList();
                        ctx.FollowUpUsers.RemoveRange(detachedUsers);
                }
                ctx.Entry(this).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }
        public void Update()
        {
            
        }
        public void Delete(long id)
        {
            using CTSContext ctx = new CTSContext();
            FollowUp entity = new FollowUp
            {
                Id = (short)id
            };
            ctx.FollowUp.Attach(entity);
            ctx.FollowUp.Remove(entity);
            ctx.SaveChanges();
        }
        public void Delete()
        {
            Delete(Id);
        }
        public List<FollowUp> List()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FollowUp.AsNoTracking().ToList();
            }
        }
        public List<FollowUp> ListWithInclude()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FollowUp.Include(f => f.FollowUpDocument)
                                   .Include(f => f.OriginalDocument)
                                   .Include(f => f.FollowUpStatus)
                                   .Include(f => f.Team)
                                   .Include(f => f.FollowUpUsers)
                                   .AsNoTracking().ToList();
            }
        }
        public async Task<List<FollowUp>> ListWithIncludeAsync(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.FollowUp
                                   .Include(f => f.FollowUpStatus)
                                   .Include(f => f.Team)
                                   .Include(f => f.FollowUpDocument)
                                   .Include(f => f.FollowUpDocument).ThenInclude(d => d.Priority)
                                   .Include(f => f.OriginalDocument)
                                   .Include(f => f.OriginalDocument).ThenInclude(d => d.DocumentReceiverEntity)
                                   .Include(f => f.OriginalDocument).ThenInclude(d => d.DocumentReceiverEntity).ThenInclude(e => e.Structure)
                                   .Include(f => f.OriginalDocument).ThenInclude(d => d.SendingEntity)
                                   .Include(f => f.OriginalDocument).ThenInclude(d => d.Category)
                                   .Include(f => f.OriginalDocument).ThenInclude(d => d.Note)
                                   .Include(f => f.CreatedByUser)
                                   .Include(f => f.FollowUpUsers)
                                   .Include(f => f.FollowUpUsers).ThenInclude(d => d.FollowUpSecurity)
                                   .Where(t => ids.Contains(t.Id)).ToListAsync();
            }
        }

        public async Task<List<long>> ListFollowUpIdsAsync(int startIndex, int pageSize, long userId, long structureId, Expression<Func<FollowUp, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<FollowUp> query = ctx.FollowUp;
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t =>  t.FollowUpUsers.Any(u => u.UserId == userId && u.StructureId == structureId));

                return pageSize < 0 ? await query.Select(d=>d.Id).ToListAsync() : await query.Skip(startIndex).Take(pageSize).Select(d => d.Id).ToListAsync();
            }
        }
        public async Task<List<FollowUp>> ListFollowUpAsync(int startIndex, int pageSize, long userId, long structureId, Expression<Func<FollowUp, bool>> filterExpression = null,
            List<SortExpression<FollowUp>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<FollowUp> query = ctx.FollowUp
                    .Include(t => t.FollowUpDocument)
                    .Include(t => t.OriginalDocument)
                    .Include(t => t.CreatedByUser)
                    .Include(t => t.FollowUpUsers)
                    .Include(t => t.Team)
                    .Include(t => t.FollowUpDocument).ThenInclude(f=>f.Note)
                    .Include(t => t.FollowUpStatus)
                    .Include(t => t.OriginalDocument).ThenInclude(t => t.DocumentReceiverEntity)
                    .Include(t => t.OriginalDocument).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(s=>s.Structure)
                    .Include(t => t.OriginalDocument).ThenInclude(t => t.SendingEntity).AsNoTracking();

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }

                query = query.Where(t => t.FollowUpUsers.Any(u=>u.UserId== userId&& u.StructureId== structureId));

                return pageSize < 0 ? await query.ToListAsync() : await query.Skip(startIndex).Take(pageSize).ToListAsync();

            }
        }
        public async Task<int>  GetFollowUpCountAsync(long userId,long structureId,  Expression<Func<FollowUp, bool>> filterExpression = null)
        {
            OpenDbContext();
            IQueryable<FollowUp> query = _ctx.FollowUp.Include(s=>s.FollowUpUsers).AsNoTracking();
            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            query = query.Where(t => t.FollowUpUsers.Any(u => u.UserId == userId && u.StructureId == structureId));
            return await query.CountAsync();
        }
        public async Task<int> GetFollowUpTodayCountAsync(long userId,long structureId, Expression<Func<FollowUp, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Today.Date;//to fix oracle issue
                IQueryable<FollowUp> query = ctx.FollowUp.Where(t => t.CreatedDate.Date == today).AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t => t.FollowUpUsers.Any(u => u.UserId == userId && u.StructureId == structureId));
                return await query.CountAsync();
            }
        }

        public async Task<List<FollowUp>> ListSearchAsync(int startIndex, int pageSize, long userId, long structureId, Expression<Func<FollowUp, bool>> filterExpression = null,
       List<SortExpression<FollowUp>> sortExpression = null, string keyword = null, string subject = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<FollowUp> query = ctx.FollowUp
                    .Include(t => t.FollowUpStatus)
                    .Include(t => t.FollowUpDocument)
                    .Include(t => t.OriginalDocument)
                    .Include(t => t.CreatedByUser)
                    .Include(t => t.FollowUpDocument).ThenInclude(t => t.Note)
                    .Include(t => t.OriginalDocument).ThenInclude(t => t.SendingEntity)
                    .Include(t => t.OriginalDocument).ThenInclude(t => t.DocumentReceiverEntity)
                    .Include(t => t.OriginalDocument).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.OriginalDocument).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .AsNoTracking();

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(t => (t.FollowUpDocument.DocumentForm != null && !string.IsNullOrEmpty(t.FollowUpDocument.DocumentForm.Keyword)) ? (Constants.SPLITTER + t.FollowUpDocument.DocumentForm.Keyword.ToLower() + Constants.SPLITTER).Contains(Constants.SPLITTER + keyword.ToLower() + Constants.SPLITTER) : false);
                } 
                
                if (!string.IsNullOrEmpty(subject))
                {
                    query = query.Where(t => (t.FollowUpDocument.Subject != null && !string.IsNullOrEmpty(t.FollowUpDocument.Subject)) ? t.FollowUpDocument.Subject.ToLower().Contains(subject.ToLower()): false);
                }

                query = query.Where(t => t.FollowUpUsers.Any(u => u.UserId == userId && u.StructureId == structureId));



                return await query.Skip(startIndex).Take(pageSize).ToListAsync();
            }
        }
        public async Task<int> GetSearchCount(long userId, long structureId, Expression<Func<FollowUp, bool>> filterExpression = null,
       List<SortExpression<FollowUp>> sortExpression = null, string keyword = null, string subject = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<FollowUp> query = ctx.FollowUp
                   .Include(t => t.FollowUpStatus)
                   .Include(t => t.FollowUpDocument)
                   .Include(t => t.OriginalDocument)
                   .Include(t => t.CreatedByUser)
                   .Include(t => t.OriginalDocument).ThenInclude(t => t.SendingEntity)
                   .Include(t => t.OriginalDocument).ThenInclude(t => t.DocumentReceiverEntity)
                   .Include(t => t.OriginalDocument).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                   .Include(t => t.OriginalDocument).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                   .AsNoTracking();

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(t => (t.FollowUpDocument.DocumentForm != null && !string.IsNullOrEmpty(t.FollowUpDocument.DocumentForm.Keyword)) ? (Constants.SPLITTER + t.FollowUpDocument.DocumentForm.Keyword.ToLower() + Constants.SPLITTER).Contains(Constants.SPLITTER + keyword.ToLower() + Constants.SPLITTER) : false);
                }

                if (!string.IsNullOrEmpty(subject))
                {
                    query = query.Where(t => (t.FollowUpDocument.Subject != null && !string.IsNullOrEmpty(t.FollowUpDocument.Subject)) ? t.FollowUpDocument.Subject.ToLower().Contains(subject.ToLower()) : false);
                }

                query = query.Where(t => t.FollowUpUsers.Any(u => u.UserId == userId && u.StructureId == structureId));

                return await query.CountAsync();
            }
        }

        public bool CheckHaveAccess(long documentId, long userId, long structureId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FollowUp.AsNoTracking().Any(t =>( t.DocumentId == documentId  ||t.OriginalDocumentId==documentId)&&
                (t.IsPrivate.HasValue && t.IsPrivate.Value && t.CreatedByUserId == userId) ||
                ((!t.IsPrivate.HasValue || !t.IsPrivate.Value) && t.FollowUpUsers.Any(u => u.UserId == userId && u.StructureId == structureId)));
            }
        }
        public async Task<bool> CheckIfExistsForSameUser(long documentId,long userId,long structureId)
        {
            using (var ctx = new CTSContext())
            {

                return await ctx.FollowUp.Include(f=>f.FollowUpUsers).AsNoTracking().AnyAsync(t => 
                   t.OriginalDocumentId == documentId &&
                   t.FollowUpStatusId != (long)Core.FollowUpStatus.Completed && 
                   t.FollowUpStatusId != (long)Core.FollowUpStatus.Canceled && 
                   (t.FollowUpUsers.Any(u=>u.UserId==userId  && u.StructureId==structureId)|| t.CreatedByUserId==userId));
            }
        }
        public async Task<bool> CheckIfUserCanAccess(long id, long userId,long structureId)
        {
            using (var ctx = new CTSContext())
            {

                return await ctx.FollowUp.Include(f=>f.FollowUpUsers).AsNoTracking().
                    Where(d => d.Id == id).AnyAsync(t =>(t.FollowUpUsers.Any(u=>u.UserId==userId && u.StructureId== structureId) || t.CreatedByUserId==userId));
            }
        }
        public async Task<bool> CheckIfDocumentHasFollowUpAccess(long userId,long? structureId,long documentId)
        {
            using (var ctx = new CTSContext())
            {

                return await ctx.FollowUp.Include(f => f.FollowUpUsers).AsNoTracking()
                    .Where(d => d.OriginalDocumentId == documentId || d.DocumentId == documentId).AnyAsync(t => 
                   (t.FollowUpUsers.Any(u=>u.UserId==userId && u.StructureId== structureId) || t.CreatedByUserId==userId));
            }
        }
        #endregion

        #region Private Methods

        private void OpenDbContext()
        {
            if (_ctx == null)
            {
                _ctx = new CTSContext();
            }
        }

        #endregion


        #region Dispose
        public void Dispose()
        {
            if (_ctx != null)
            {
                _ctx.Dispose();
            }
        }
        #endregion

        #region Conversion

        public static implicit operator FollowUpInfoModel(FollowUp item)
        {
            FollowUpInfoModel retValue = new FollowUpInfoModel();
            var language = Helper.GetLanguage();
            if (item != null)
            {

                retValue.FollowUpId = item.Id;
                retValue.IsPrivate = item.IsPrivate.HasValue ? item.IsPrivate.Value : false;
                retValue.TeamId = item.TeamId;
                retValue.FollowUpDocumentId = item.DocumentId;
                retValue.OriginalDocumentId = item.OriginalDocumentId;
                retValue.CategoryId = item.FollowUpDocument != null ? item.FollowUpDocument.CategoryId : (short)Configuration.FollowUpCategory;
                retValue.Subject = item.FollowUpDocument.Subject;
                retValue.FollowUpStatusId = item.FollowUpStatusId;
                retValue.DueDate = !item.FollowUpToDate.Date.IsNull() ? item.FollowUpToDate.Date.ToString() : string.Empty;
                retValue.CreatedByUserId = item.CreatedByUserId;
                retValue.Keyword = item.FollowUpDocument.DocumentForm != null ? item.FollowUpDocument.DocumentForm.Keyword : string.Empty;
                retValue.CreatedByStructureId = item.FollowUpDocument.CreatedByStructureId;
                retValue.CreatedByUser = item.CreatedByUser == null ? String.Empty :
                   (language == Language.EN ?
                   $"{item.CreatedByUser?.Firstname} {item.CreatedByUser?.Lastname}" :
                   $"{IdentityHelperExtension.GetFullName(item.CreatedByUserId, language)}");
            }
            return retValue;

        }
        #endregion
    }
}