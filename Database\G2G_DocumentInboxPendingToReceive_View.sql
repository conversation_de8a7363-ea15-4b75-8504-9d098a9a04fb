USE [ISFCTS2]
GO

/****** Object:  View [dbo].[G2G_DocumentInboxPendingToReceive]    Script Date: 9/29/2024 11:46:48 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE VIEW [dbo].[G2G_DocumentInboxPendingToReceive]
AS
WITH LatestTransfer AS (
    SELECT 
        DocumentId,
        MAX(Id) AS MaxId
    FROM dbo.Transfer WITH (NOLOCK)
    GROUP BY DocumentId
)
SELECT    g2gdoc.ID G2G_Internal_ID,
CAST(COALESCE(dbo.Document.Id, 0)  AS BIGINT)DOC_ID,
   CAST(COALESCE(dbo.Document.CategoryId, 1) AS SMALLINT)  DOC_CATEGORY,
dbo.Document.ReferenceNumber AS REFERENCE,
    dbo.Document.SendingEntityId AS DOC_SENDER,
                         CAST (dbo.Document.PriorityId   AS SMALLINT) AS PRIORITY,
						docStatus.[Name] AS STATUS,
						 COALESCE(dbo.Document.CreatedDate, g2gdoc.G2G_SENT_DATE) AS RECEIVEDATE, 
                        g2gdoc.[Subject] AS SUBJECT,
						 '' AS DOC_SUMMARY,
    '' AS DOC_FILPLANTABLE,
    '' AS DOCUMENTRECEPIENT,
                         convert(nvarchar(20),
						 g2lookup.[CTS_VALUE]) AS PRIVACYLEVEL,
						 '' AS TAGS,
    dbo.Document.Form,
						 '' AS DOC_TEXT8,
    'Active' AS DOC_TEXT15,
                         g2gdoc.G2G_SENT_DATE AS DOCUMENTDATE,
						 dbo.Document.ExternalReferenceNumber AS MAILNUMBER,
    dbo.Document.CreatedDate AS RegisteredDateFrom,
                         concat(g2gOu3.EnName, N',', g2gOu4.EnName)  AS DOC_RECIPIENT,
						  STRING_AGG(CAST(DocumentReceiverEntity.StructureId AS NVARCHAR(MAX)), '/') AS DOC_RECIPIENT_ID,
						dbo.Priority.Name AS DOC_PRIORITY,
                         tsf.StatusId AS DOC_TRANSFER_STATUS,
						  CAST(TSF_FROM AS BIGINT) AS TSF_FROM_UserId,
    CAST(TSF_TO AS BIGINT) AS TSF_TO_UserId,
    CAST(DELEGATE_FROM AS BIGINT) AS DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	CAST(TSF_PURPOSE_ID AS SMALLINT) AS TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
						  dbo.Document.CreatedByUserId AS DOC_REGISTEREDBY,
						 dbo.checkOverDue(dbo.Document.DueDate) AS duedate,
						 dbo.Document.DueDate AS DOC_DATE1,
    'Active' AS Document_Status,
    dbo.checkOverDue(dbo.Document.DueDate) AS overdue,
    dbo.Privacy.Id,
     dbo.Privacy.[Level],
    SenderEntity.[Name] AS SENDER,
    CreatedByStructure.[Name] AS DOC_REGISTERBY_STRUCTURE_FULLNAME,
    CreatedByUser.Firstname AS DOC_REGISTEREDBY_FULLNAME,
    dbo.Document.CreatedDate AS RegisteredDateTo,
     CAST(dbo.Document.CreatedByStructureId  AS BIGINT )AS DOC_REGISTERBY_STRUCTURE,
    '' AS O_OCR_TEXT,
    dbo.Document.DocumentTypeId AS DOC_TYPE,
    CAST(dbo.Document.StatusId  AS SMALLINT)AS DOC_STATUS_ID,
    dbo.Document.ClassificationId AS CLASSIFICATION,
						 dbo.Category.[Name] AS Category,
    dbo.Document.ReferenceSequence AS DOC_SEQUENCE,
    '' AS DOC_NUMBER_OF_OPENED_TRANSFERS,
    '' AS REGISTRATION_NUMBER,
    '' AS ID_CUSTOM,
    '' AS [NAME],
    dbo.Document.CreatedDate AS DOC_REGISTERDATE,
    dbo.Priority.Id AS PRIORITY_ID, 
                         CONVERT(VARCHAR(10),
						 dbo.Document.CreatedDate, 103) AS REGISTERDATE,
						 dbo.Privacy.[Name] AS PrivacyState,
                         concat(g2gOu1.EnName, N'\', g2gOu2.EnName) as DOC_SENDER_FULL_NAME, 
                         0 AS AttachmentsCount, 
						 g2gOu1.Name AS MainFromAr,
						 g2gOu1.EnName AS MainFrom, 
                         g2gOu2.Name AS SubFromAr,
						 g2gOu2.EnName AS SubFrom,
						 g2gOu3.Name AS MainToAr,
						 g2gOu3.EnName AS MainTo, 
                         g2gOu4.Name AS SubToAr,
						 g2gOu4.EnName AS SubTo, 
						  COALESCE(CAST(g2gOu3.GCT_ID  AS BIGINT), 0) AS MainGctIdTo,
							COALESCE(CAST(g2gOu3.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS MainGctIdRedirectTo,
							COALESCE(CAST(g2gOu4.GCT_ID   AS BIGINT), 0) AS SubGctIdTo,
							COALESCE(CAST(g2gOu4.RedirectToGCT_IDonReceive  AS BIGINT), 0) AS SubGctIdRedirectTo,
						 tsf.TSF_TO,
						CAST (tsf.TSF_TO_STRUCTURE AS BIGINT) AS TSF_TO_STRUCTURE,
                          CAST(COALESCE(tsf.TSF_ID, 0) AS BIGINT ) AS TSF_ID,
                         concat(g2gOu1.Name, N'\', g2gOu2.Name) as DOC_SENDER_FULL_NAME_AR, 
                         concat(g2gOu3.Name, N'\', g2gOu4.Name) as DOC_RECIPIENT_FULL_NAME_AR, 
						 G2G_SERIAL, 
						 G2G_REF_NO,
                         g2gdoc.G2G_MAIN_SITE_FROM_ID,
						 g2gdoc.G2G_SUB_SITE_FROM_ID, 
                         g2gdoc.G2G_MAIN_SITE_TO_ID,
						 g2gdoc.G2G_SUB_SITE_TO_ID,
                         'Incoming' as G2G_Category,
						 g2gstatus.Name GStatus,  
						 g2gstatus.NameAr GStatusAr,
						 g2gdoc.IsLocked,
						 g2gdoc.MaxAttempt,
                         g2gdoc.Status G2GStatusID,
						 CAST(g2gdoc.ExportedBy AS BIGINT) AS ExportedBy,
                         CAST(g2gdoc.ExportedByStcGctId AS BIGINT) AS ExportedByStcGctId,
                         coalesce(tsf.TSF_FROM_FULL_NAME, '') AS FROMUSER,
						 ISNULL(tsf.TSF_TO_FULL_NAME, 
						 coalesce(tsf.TSF_TO_STRUCT_STC_NAME, '')) AS TOUSER,
                          coalesce(dbo.Document.Subject, 
						 g2gdoc.Subject) Correspondence, 
						 '' as Correspondence_Name,
                         g2gdoc.G2G_CREATION_DATE AS SYSREGDATE,
						 CAST(tsf.TSF_FROM_STRUCTURE  AS BIGINT)AS TSF_FROM_STRUCTURE,
						 '' as ORIGINAL_FROM_STRUCTURE, 
                         '' G2G_VSID, 
						 g2gdoc.G2G_CREATION_DATE AS TRANSFERDATE
FROM             G2G.dbo.G2G_Documents AS g2gdoc  with (nolock) 
						 left outer JOIN  dbo.Document ON g2gdoc.DOC_ID = dbo.Document.Id
						 left outer JOIN dbo.Category  with (nolock) ON dbo.Document.CategoryId = dbo.Category.Id
						 left outer JOIN dbo.Priority WITH (NOLOCK) ON dbo.Document.PriorityId = dbo.Priority.Id
						 left outer JOIN G2G.dbo.G2G_Lookups g2lookup with (nolock) on g2gdoc.G2G_SECURITY_LEVEL=g2lookup.[G2G_LKEY]
						 and g2lookup.[G2G_CAT]=0 
						 left outer join dbo.Privacy with (nolock) ON g2lookup.[CTS_VALUE] = dbo.Privacy.Id
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu1 with (nolock) ON g2gdoc.G2G_MAIN_SITE_FROM_ID = g2gOu1.G2G_ID
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu2 with (nolock) ON g2gdoc.G2G_SUB_SITE_FROM_ID = g2gOu2.G2G_ID
						 left outer JOIN G2G.dbo.G2G_Status AS g2gstatus with (nolock) ON g2gdoc.Status = g2gstatus.ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu3 with (nolock) ON g2gdoc.G2G_MAIN_SITE_TO_ID = g2gOu3.G2G_ID 
						 left outer JOIN G2G.dbo.G2G_OrganizationUnit AS g2gOu4 with (nolock) ON g2gdoc.G2G_SUB_SITE_TO_ID = g2gOu4.G2G_ID
						 left JOIN dbo.DocumentReceiverEntity ON dbo.Document.Id = dbo.DocumentReceiverEntity.DocumentId
						 left JOIN dbo.Structure AS SenderEntity ON dbo.Document.SendingEntityId = SenderEntity.Id
						 left JOIN dbo.Structure AS CreatedByStructure ON dbo.Document.CreatedByStructureId = CreatedByStructure.Id
						 left JOIN dbo.[Status] AS docStatus ON dbo.Document.StatusId= docStatus.Id
						 left JOIN dbo.[User] AS CreatedByUser ON dbo.Document.CreatedByUserId = CreatedByUser.Id
						 left JOIN LatestTransfer lt ON dbo.Document.Id = lt.DocumentId
                         outer apply
                             (SELECT    
			dbo.Transfer.Id AS TSF_ID,
            DocumentId AS DOC_ID,
            FromStructureId AS TSF_FROM_STRUCTURE,
            FromStructure.[Name] AS TSF_FROM_STRUCT_STC_NAME,
            FromStructure.[NameAr] AS TSF_FROM_STRUCT_STC_NAME_AR,
            '' AS TSF_FROM_Group_Id,
            FromUserId AS TSF_FROM,
            FromUser.[Firstname] AS TSF_FROM_FULL_NAME,
            FromUser.[FirstnameAr] AS TSF_FROM_FULL_NAME_AR,
            ToUserId AS TSF_TO,
            ToUser.[Firstname] AS TSF_TO_FULL_NAME,
            ToUser.[FirstnameAr] AS TSF_TO_FULL_NAME_AR,
            ToStructureId AS TSF_TO_STRUCTURE,
            ToStructure.[Name] AS TSF_TO_STRUCT_STC_NAME,
            ToStructure.[NameAr] AS TSF_TO_STRUCT_STC_NAME_AR,
            '' AS TSF_TO_Group_Id,
            '' AS TSF_TO_G_STRUCTURE_ID,
            '' AS TSF_DIRECTION,
            CreatedDate AS TSF_DATE,
            DueDate AS TSF_DUE_DATE,
            Instruction AS TSF_DESCRIPTION,
            '' AS TSF_ACTION_REACTION_ID,
            '' AS TSF_ACTION_DATE,
            StatusId AS TSF_STATUS_ID,
            '' AS TSF_REPLY_PARENT_ID,
            '' AS TSF_CORRESPONDENCE_STATUS,
            '' AS TSF_CLOSE_STATUS,
            '' AS TSF_IS_EXTERNAL,
            '' AS UserID,
            OwnerDelegatedUserId AS DELEGATE_FROM,
            '' AS STATE_ID,
            '' AS TSF_PURPOSE,
            '' AS TSF_PURPOSE_AR,
            PurposeId AS TSF_PURPOSE_ID,
            '' AS TSF_ISREAD,
            '' AS TSF_TOCONTACT,
            '' AS FromWorkFlow,
            '' AS FromReply,
            '' AS FromInternalSend,
            ParentTransferId AS TSF_PARENT_TRANSFER,
            ClosedDate AS TSF_CLOSURE_DATE,
            '' AS ISDOCUMENTSET,
            '' AS TSF_PARENT_TRANSFER_DATE,
            '' AS TSF_AUTO_CLOSED,
            '' AS Workflow_ActivityInstanceId,
            '' AS PublicInstruction,
            '' AS TSF_CLOSURE_COMMENTS,
            '' AS CLOSE_DELEGATE_FROM,
            '' AS IsDismissed,
            '' AS ORIGINAL_FROM_STRUCTURE,
            '' AS IsRecalled,
            '' AS TSF_VOICE_INSTRUCTIONS,
            '' AS TSF_VOICE_FILENAME,
            '' AS PriorityId,
            LockedDate AS TSF_LOCKDATE,
			(
                SELECT MAX(StatusId)
                FROM dbo.Transfer
                WHERE Transfer.DocumentId = g2gdoc.DOC_ID
            ) AS StatusId
                               FROM dbo.Transfer WITH (NOLOCK)
                               INNER JOIN dbo.Structure AS FromStructure ON dbo.Transfer.FromStructureId = FromStructure.Id
        left JOIN dbo.Structure AS ToStructure ON dbo.Transfer.ToStructureId = ToStructure.Id
        left JOIN dbo.[User] AS FromUser ON dbo.Transfer.FromUserId = FromUser.Id
        left JOIN dbo.[User] AS ToUser ON dbo.Transfer.ToUserId = ToUser.Id
        WHERE dbo.Transfer.Id = lt.MaxId
    ) AS tsf
WHERE        (g2gdoc.Status = 6 )

GROUP BY 
    g2gdoc.ID,
    dbo.Document.Id,
    dbo.Document.CategoryId,
    dbo.Document.ReferenceNumber,
    dbo.Document.SendingEntityId,
    dbo.Document.PriorityId,
    dbo.Document.StatusId,
    dbo.Document.CreatedDate,
    g2gdoc.G2G_SENT_DATE,
    dbo.Document.[Subject],
    dbo.Document.PrivacyId,
    dbo.Document.Form,
    dbo.Document.CreatedDate,
    dbo.Document.ExternalReferenceNumber,
    dbo.Document.CreatedDate,
    dbo.Priority.Name,
    tsf.StatusId,
    dbo.Document.CreatedByUserId,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Document.DueDate,
    dbo.checkOverDue(dbo.Document.DueDate),
    dbo.Privacy.Id,
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    CreatedByStructure.[Name],
    CreatedByUser.Firstname,
    dbo.Document.CreatedDate,
    dbo.Document.CreatedByStructureId,
    dbo.Document.DocumentTypeId,
    dbo.Document.StatusId,
    dbo.Document.ClassificationId,
    dbo.Category.[Name],
    dbo.Document.ReferenceSequence,
    dbo.Document.CreatedDate,
    dbo.Priority.Id,
    CONVERT(VARCHAR(10), dbo.Document.CreatedDate, 103),
    dbo.Privacy.[Name],
    SenderEntity.[Name],
    g2gOu1.Name,
    g2gOu1.EnName,
    g2gOu2.Name,
    g2gOu2.EnName,
    g2gOu3.Name,
    g2gOu3.EnName,
    g2gOu4.Name,
    g2gOu4.EnName,
    g2gOu3.GCT_ID,
    g2gOu3.RedirectToGCT_IDonReceive,
    g2gOu4.GCT_ID,
    g2gOu4.RedirectToGCT_IDonReceive,
    tsf.TSF_TO,
    tsf.TSF_TO_STRUCTURE,
    tsf.TSF_ID,
    SenderEntity.[NameAr],
    G2G_SERIAL,
    G2G_REF_NO,
    g2gdoc.G2G_MAIN_SITE_FROM_ID,
    g2gdoc.G2G_SUB_SITE_FROM_ID,
    g2gdoc.G2G_MAIN_SITE_TO_ID,
    g2gdoc.G2G_SUB_SITE_TO_ID,
    g2gstatus.Name,
    g2gstatus.NameAr,
    g2gdoc.IsLocked,
    g2gdoc.MaxAttempt,
    g2gdoc.Status,
    tsf.TSF_FROM_FULL_NAME,
    ISNULL(tsf.TSF_TO_FULL_NAME, COALESCE(tsf.TSF_TO_STRUCT_STC_NAME, '')),
    COALESCE(dbo.Document.Subject, g2gdoc.Subject),
    g2gdoc.G2G_CREATION_DATE,
    tsf.TSF_FROM_STRUCTURE,
    g2gdoc.G2G_CREATION_DATE,
	g2gdoc.[Subject],
	g2lookup.[CTS_VALUE],
	docStatus.[Name],
	Privacy.[Level],
	TSF_FROM,
	TSF_TO,
	tsf.DELEGATE_FROM,
	tsf.TSF_DUE_DATE,
	tsf.TSF_CLOSURE_DATE,
	tsf.TSF_DATE,
	tsf.TSF_LOCKDATE,
	tsf.TSF_DESCRIPTION,
	tsf.TSF_PURPOSE_ID,
	tsf.PriorityId,
	tsf.TSF_FROM_STRUCT_STC_NAME,
	tsf.TSF_FROM_STRUCT_STC_NAME_AR,
	tsf.TSF_TO_STRUCT_STC_NAME,
	tsf.TSF_TO_STRUCT_STC_NAME_AR,
	g2gdoc.ExportedBy,
     g2gdoc.ExportedByStcGctId

GO