﻿@using System.Globalization
@using  Microsoft.AspNetCore.Authentication
@using Intalio.Core
<!DOCTYPE html>
<html @(CultureInfo.CurrentUICulture.Name.Equals("ar") ? "dir=rtl" : "")>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>@Intalio.CTS.Core.Configuration.ApplicationName</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    @if (IsSectionDefined("Styles"))
    {@RenderSection("Styles", required: false)}
    <environment names="Development,Production">
        <link href="~/lib/orgchart/getorgchart.min.css" rel="stylesheet" />
        <link href="~/lib/jquery-te/jquery-te.css" rel="stylesheet" />
        <link href="~/lib/dropzone/dropzone.min.css" rel="stylesheet" />
         <link href="~/lib/Slick/slick.min.css" rel="stylesheet" />
        @{
            if (CultureInfo.CurrentUICulture.Name.Equals("ar"))
            {
                <link href="~/lib/combobox/comboBox.min.css" rel="stylesheet" />
                <link href="~/css/core-css-rtl.min.css" rel="stylesheet" type="text/css" />
            }
            else
            {
                <link href="~/lib/combobox/comboBoxEN.min.css" rel="stylesheet" />
                <link href="~/css/core-css.min.css" rel="stylesheet" />
            }
        }
        <link href="~/css/style.css" rel="stylesheet" />
        @{
            if (CultureInfo.CurrentUICulture.Name.Equals("ar"))
            {
                <link href="~/css/style-rtl.css" rel="stylesheet" />
            }
        }
    </environment>
    <environment names="Staging">
        @{
            if (CultureInfo.CurrentUICulture.Name.Equals("ar"))
            {
                <link href="~/css/main-rtl.min.css?v=@Intalio.CTS.Core.Configuration.Version" rel="stylesheet" type="text/css" />
            }
            else
            {
                <link href="~/css/main.min.css?v=@Intalio.CTS.Core.Configuration.Version" rel="stylesheet" type="text/css" />
            }
        }
    </environment>
    @{
        var customcss = Intalio.CTS.Core.API.ManageCustomizationFile.FindByTypeId((byte)Intalio.Core.CustomizationFileType.CSS);
        if (customcss != null)
        {
            <style>
        @Html.Raw(customcss.Data)
            </style>
        }
    }
    <script type="text/javascript">
        @{
            var language = CultureInfo.CurrentUICulture.Name.Replace("en-GB", "en");
            var currentLanguage = Intalio.Core.Language.EN;
            if (language == "ar")
            {
                currentLanguage = Intalio.Core.Language.AR;
            }
            else if (language == "fr")
            {
                currentLanguage = Intalio.Core.Language.FR;
            }
            var user = Convert.ToInt64(User.Claims.First(t => t.Type == "Id").Value);
            string structureEnabledTemplates = "";

            UserModel userTemplatesInfo = Intalio.CTS.Core.Utility.IdentityHelperExtension.GetUser(Intalio.CTS.Core.Configuration.userId,
                        Intalio.CTS.Core.Configuration.IdentityAccessToken, Intalio.Core.Language.EN);

            var enablePersonalTemplates = Convert.ToBoolean(userTemplatesInfo.Attributes.Where(x => x.Text == "EnablePersonalTemplates").FirstOrDefault().Value);
            var enableStructureTemplates = Convert.ToBoolean(userTemplatesInfo.Attributes.Where(x => x.Text == "EnableStructureTemplates").FirstOrDefault().Value);
            var hasG2G = Convert.ToBoolean(userTemplatesInfo.Attributes.Where(x => x.Text == "CanExportG2G").FirstOrDefault()?.Value);

            if (enableStructureTemplates)
                structureEnabledTemplates = userTemplatesInfo.DefaultStructureId.Value.ToString();
            userTemplatesInfo.Structures.ForEach(
                 s =>
                 {
                     var isStructureEnableTemplates = s.UserAttributes.Any(f => f.Text == "EnableStructureTemplates" && Convert.ToBoolean(f.Value) == true);
                     enableStructureTemplates = enableStructureTemplates == false ? isStructureEnableTemplates : enableStructureTemplates;
                     structureEnabledTemplates = isStructureEnableTemplates == true ?
                     (structureEnabledTemplates == "" ? structureEnabledTemplates + s.Id : structureEnabledTemplates + "/" + s.Id) : structureEnabledTemplates;
                 });
        }
        window.language = "@CultureInfo.CurrentUICulture.Name.Replace("en-GB", "en")";
        window.IdentityUrl = "@Intalio.CTS.Core.Configuration.IdentityAuthorityUrl";
        window.IdentityAccessToken = "@Context.GetTokenAsync("access_token").Result";
        window.HideAuditTrail = "@(Intalio.CTS.Core.Configuration.AuditTrailMode == Intalio.Core.AuditTrailMode.None)";
        window.EnableConfirmationMessage = "@Intalio.CTS.Core.Configuration.EnableConfirmationMessage";
        window.InboxMode = "@Intalio.CTS.Core.Utility.IdentityHelperExtension.GetUserInboxMode("InboxMode",user)";
        window.ByFileMode = "@Intalio.CTS.Core.Configuration.ByFileMode";
        window.LinkedDeleteForCreatorOnly = "@Intalio.CTS.Core.Configuration.LinkedDeleteForCreatorOnly";
        window.StructureNameAr = "@Intalio.CTS.Core.Configuration.StructureNameAr";
        window.StructureNameFr = "@Intalio.CTS.Core.Configuration.StructureNameFr";
        window.HideStructureFromTransfer = "@Intalio.CTS.Core.Configuration.HideStructureFromTransfer";
        window.ByFileExtensions = "@Intalio.CTS.Core.Configuration.ByFileExtensions";
        window.UserStructureReceiver = "@Intalio.CTS.Core.Configuration.UserStructureReceiver";
        window.UserPrivacy = "@Intalio.CTS.Core.Configuration.UserPrivacy";
        window.SendWithoutStructureReceiverOrPrivacyLevel = "@Intalio.CTS.Core.Configuration.SendWithoutStructureReceiverOrPrivacyLevel";
        window.IsStructureSender = "@(Intalio.CTS.Core.Configuration.EnablePerStructure ? Intalio.CTS.Core.Utility.IdentityHelperExtension.GetUserStrucurePermission(user, currentLanguage).IsStrucureSender: User.Claims.Any(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender) && !string.IsNullOrEmpty(User.Claims.First(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender).Value) ? Convert.ToBoolean(User.Claims.First(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender).Value) : false)";//User.Claims.Any(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender) && !string.IsNullOrEmpty(User.Claims.First(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender).Value) ? Convert.ToBoolean(User.Claims.First(t => t.Type == Intalio.CTS.Core.Configuration.UserStructureSender).Value) : false)";
        window.EnableSendingRules = "@Intalio.CTS.Core.Configuration.EnableSendingRules";
        window.MaxFileSize = "@Intalio.CTS.Core.Configuration.MaxFileSize";
        window.ViewerUrl = "@Intalio.CTS.Core.Configuration.ViewerUrl";
        window.OpenCorrespondenceMode = "@Intalio.CTS.Core.Configuration.OpenCorrespondenceMode";
        window.AttachmentEditable = "@Intalio.CTS.Core.Configuration.AttachmentEditable";
        window.EnableTransferToUsers = "@Intalio.CTS.Core.Configuration.EnableTransferToUsers";
        window.EnablePersonalTemplates ="@enablePersonalTemplates";
        window.EnableStructureTemplates ="@enableStructureTemplates";
        window.HomePageMaximumWidget = "@Intalio.CTS.Core.Configuration.HomePageMaximumWidget";
        window.CalendarType = "@Intalio.CTS.Core.Configuration.CalendarType.ToString()";
        window.EnableOCR = "@Intalio.CTS.Core.Configuration.EnableOCR" === "True";
        window.EnablePerStructure = "@Intalio.CTS.Core.Configuration.EnablePerStructure" === "True";
        window.CrawlerServerUrl = "@string.Join(Intalio.Core.Constants.SPLITTER, Intalio.CTS.Core.Configuration.CrawlerServerUrls)";
        window.FirstNameAr = "@Intalio.CTS.Core.Configuration.FirstNameAr";
        window.LastNameAr = "@Intalio.CTS.Core.Configuration.LastNameAr";
        window.MiddleNameAr = "@Intalio.CTS.Core.Configuration.MiddleNameAr";
        window.FirstNameFr = "@Intalio.CTS.Core.Configuration.FirstNameFr";
        window.LastNameFr = "@Intalio.CTS.Core.Configuration.LastNameFr";
        window.MiddleNameFr = "@Intalio.CTS.Core.Configuration.MiddleNameFr";
        window.DSURL = "@Intalio.CTS.Core.Configuration.DSURL";
        window.PriorityPrivacyAction = "@Intalio.CTS.Core.Configuration.PriorityPrivacyAction";
        window.ViewerMode ="@Intalio.CTS.Core.Configuration.ViewerMode";
        window.ApplicationName ="@Html.Raw(Intalio.CTS.Core.Configuration.ApplicationName)";
        window.StructureEnabledTemplates ="@structureEnabledTemplates";
        window.AllowRowSelection="@Intalio.CTS.Core.Configuration.AllowRowSelection";
        window.HasG2G="@hasG2G";
        @foreach (var par in Intalio.CTS.Core.API.ManageParameter.CustomListParameters())
        {
           @:window["@par.Text"] = "@par.Value";
        }
    </script>    
</head>
<body class="@(Intalio.CTS.Core.Configuration.FixedLayout == true ? "layout-fixed" : "")">
     @{
        var userId = Convert.ToInt64(User.Claims.First(t => t.Type == "Id").Value);
        var delegationList = Intalio.CTS.Core.API.ManageDelegation.ListDelegationToUserIds(userId);
        var delegationIdsList = Intalio.CTS.Core.API.ManageDelegation.ListDelegationToUserIdsWithDelegationId(userId);
        var roleId = Intalio.CTS.Core.Configuration.EnablePerStructure ? Intalio.CTS.Core.Utility.IdentityHelperExtension.GetUserStrucurePermission(userId, Intalio.Core.Language.EN).RoleId : Convert.ToInt32(User.Claims.First(t => t.Type == "RoleId").Value);
        <input id="hdLoggedInRoleId" type="hidden" value="@roleId" />
        <input id="hdDelegatedUserIds" type="hidden" value="@delegationList" />
        <input id="hdDelegatedId" type="hidden" value="@delegationIdsList" />
        var editDesignatedPerson = Intalio.CTS.Core.Utility.IdentityHelperExtension.GetUserStrucurePermission(userId, Intalio.Core.Language.EN, false).EditDesignatedPerson;

        CacheUtility cache = new CacheUtility();
        var structureId = User.Claims.First(t => t.Type == "StructureId").Value;
        var userStructure = cache.GetCachedItem<UserStrucureModel>($"StrucurePrivacy-" + structureId + "-" + userId);
        var structurePrivacyLevel = userStructure?.PrivacyLevel ?? 1;
        var loggedStructurePrivacyLevel = Intalio.CTS.Core.Utility.IdentityHelperExtension.getCurrentLoggedInStructurePrivacyLevel(Convert.ToInt64(User.Claims.First(t => t.Type == "Id").Value));
    }
    <input id="hdUserId" type="hidden" value="@User.Claims.First(t => t.Type == "Id").Value" />
    <input id="hdStructureIds" type="hidden" value="@(Intalio.CTS.Core.Configuration.EnablePerStructure ? @Intalio.CTS.Core.Utility.IdentityHelperExtension.getCurrentLoggedInStructure(Convert.ToInt64( User.Claims.First(t => t.Type == "Id").Value)).ToString() : @User.Claims.First(t => t.Type == "StructureIds").Value)"/>
    <input id="hdGroupIds" type="hidden" value="@User.Claims.First(t => t.Type == "GroupIds").Value" />
    <input id="hdHasManager" type="hidden" value="@User.Claims.First(t => t.Type == "ManagerId").Value" />
    <input id="hdStructureId" type="hidden" value="@structureId" />
    <input id="hdLoggedInStructureId" type="hidden" value="@Intalio.CTS.Core.Utility.IdentityHelperExtension.getCurrentLoggedInStructure(Convert.ToInt64( User.Claims.First(t => t.Type == "Id").Value))"/>
    <input id="hdEditDesignatedPerson" type="hidden" value="@($"{editDesignatedPerson}")" />
    <input id="hdStructurePrivacyLevel" type="hidden" value="@($"{structurePrivacyLevel}")" />
    <input id="hdLoggedStructurePrivacyLevel" type="hidden" value="@($"{loggedStructurePrivacyLevel}")" />
    <input id="hdUserStructureIds" type="hidden" value="@User.Claims.First(t => t.Type == "StructureIds").Value" />
    <input id="hdCurrentLoggedInRoleName" type="hidden" value="@(User.Claims.Where(c => c.Type == System.Security.Claims.ClaimTypes.Role).Select(c => c.Value).FirstOrDefault() ?? "")" />
    <div class="wrapper">
        <header class="topnavbar-wrapper">
            <partial name="_TopNavbar">
        </header>
        
        <aside class="aside">
            <partial name="_Sidebar">
        </aside>
        <partial name="_RightSidebar">
       
        <section>
            <div class="content-wrapper">
                @RenderBody()
            </div>
        </section>
        <footer>
            
        </footer>
    </div>
    <div class="modal-window"></div>
    <div class="modal-documents"></div>
    @if (IsSectionDefined("BodyArea"))
    {
        @RenderSection("BodyArea", required: false)
    }
    @Html.AntiForgeryToken()
    <script src="/CoreJS?@<EMAIL>("en-GB","en")"></script>
    <script src="/JS?@<EMAIL>("en-GB","en")"></script>
    @RenderSection("scripts", required: false)
    <script asp-append-version="true" src="~/js/core-js.min.js?v=@Intalio.CTS.Core.Configuration.Version"></script>
    <script asp-append-version="true" src="~/js/templates.min.js?v=@Intalio.CTS.Core.Configuration.Version"></script>
    <script asp-append-version="true" src="~/js/core-components.min.js?v=@Intalio.CTS.Core.Configuration.Version"></script>
    <environment names="Development,Production">
        <script asp-append-version="true" src="~/js/app.js"></script>
        <script src="~/lib/combobox/Combo.js"></script>
        <script src="~/lib/datatableEditor/dataTables.editor.min.js"></script>
        <script src="~/lib/orgchart/getorgchart.js"></script>
        <script type="text/javascript" src="~/lib/ckeditor/ckeditor.js"></script>
        <script type="text/javascript" src="~/lib/DynamicWebTWAIN/dynamsoft.webtwain.initiate.js"></script>
        <script type="text/javascript" src="~/lib/DynamicWebTWAIN/dynamsoft.webtwain.config.js"></script>
        <script src="~/lib/ace/1.4.10/ace.js"></script>
        <script src="~/lib/dropzone/dropzone.min.js"></script>
        <script src="~/lib/jquery-te/jquery-te.js"></script>
        <script asp-append-version="true" asp-append-version="true" src="~/components/appComponent.js" type="module"></script>
        <script asp-append-version="true" src="~/js/custom.dev.js" type="module"></script>
        <script asp-append-version="true" src="~/js/reportDatatablesCustom.dev.js" type="module"></script>
        <script asp-append-version="true" src="~/js/CTSCoreComponents.dev.js" type="module"></script>
        <script src="~/lib/Slick/slick.min.js"></script>
    </environment>
    <environment names="Staging">
        <script asp-append-version="true" src="~/js/main.min.js?v=@Intalio.CTS.Core.Configuration.Version"></script>
        <script type="text/javascript" src="~/lib/ckeditor/ckeditor.js"></script>
        <script asp-append-version="true" src="~/js/bundle.min.js?v=@Intalio.CTS.Core.Configuration.Version" type="text/javascript"></script>
        <script asp-append-version="true" src="~/js/custom.js"></script>
        <script asp-append-version="true" src="~/js/reportDatatablesCustom.js"></script>
        <script asp-append-version="true" src="~/js/CTSCoreComponents.js"></script>
        @RenderSection("scripts", required: false)
    </environment>
    @{
        if (System.Globalization.CultureInfo.CurrentUICulture.Name.Equals("ar"))
        {
            <script asp-append-version="true" src="~/js/core-js.ar.min.js"></script>
        }
        if (System.Globalization.CultureInfo.CurrentUICulture.Name.Equals("fr"))
        {
            <script asp-append-version="true" src="~/js/core-js.fr.min.js"></script>
        }
    }
    @{
        var customjs = Intalio.CTS.Core.API.ManageCustomizationFile.FindByTypeId((byte)Intalio.Core.CustomizationFileType.Javascript);
        if (customjs != null)
        {
            <script>
            @Html.Raw(customjs.Data)
            </script>
        }
    }    
</body>
</html>
