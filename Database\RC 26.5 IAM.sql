IF NOT EXISTS (SELECT 1 FROM Attribute WHERE Name=N'CanExportG2G')
BEGIN
	Declare @ctsAppId BIGINT
	Declare @CanExportG2GId BIGINT
	Declare @CanExportG2GUserAttribute BIGINT

	SET @ctsAppId=(SELECT Id FROM [Application] WHERE NAME='CTS .NET'); -- be sure of cts application name in you IAM database

	INSERT INTO Attribute (Name, AttributeTypeId,UniqueConstraint,Mandatory,CreatedByUserId,CreatedDate) VALUES (N'CanExportG2G', 9,0,0,1, GETDATE());

	SELECT @CanExportG2GId = @@IDENTITY

	INSERT INTO [dbo].[UserAttribute]([AttributeId], [ByStructure], [IsProfile], [CreatedByUserId], [CreatedDate])
		VALUES (@CanExportG2GId, 0, 0, 1, GetDate())
	SELECT @CanExportG2GUserAttribute = @@IDENTITY

	INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
	([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
		VALUES (@ctsAppId, @CanExportG2GUserAttribute, 'CanExportG2G', 1, 1, GETDATE())

	INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
	([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
		VALUES (@ctsAppId, @CanExportG2GUserAttribute, 'CanExportG2G', 2, 1, GETDATE())

	INSERT INTO [dbo].[ApplicationUserAttributeMapping] 
	([ApplicationId], [UserAttributeId], [MappingName], [LanguageId], [CreatedByUserId], [CreatedDate])
		VALUES (@ctsAppId, @CanExportG2GUserAttribute, 'CanExportG2G', 3, 1, GETDATE())
END