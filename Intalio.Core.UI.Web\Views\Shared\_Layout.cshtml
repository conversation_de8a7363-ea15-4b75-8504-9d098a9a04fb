﻿@using System.Globalization
@using  Microsoft.AspNetCore.Authentication
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>Intalio.Core.UI.Web</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    @if (IsSectionDefined("Styles"))
    {
    @RenderSection("Styles", required: false)}
    @{
        if (CultureInfo.CurrentUICulture.Name.Equals("ar"))
        {
        <link href="~/css/core-css-rtl.min.css" rel="stylesheet" />
        }
        else
        {
        <link href="~/css/core-css.min.css" rel="stylesheet" />
        }
    }
    <script type="text/javascript">
        window.language = "@CultureInfo.CurrentUICulture.Name.Replace("en-GB", "en")";
        window.IdentityUrl = "@Configuration.IdentityAuthorityUrl";
        window.IdentityAccessToken = "@Context.GetTokenAsync("access_token").Result";
    </script>
    <script>
        var Resources = (function (R)
        {
            var R = {}; return R;
        })(Resources);
    </script>
</head>
<body>
    <input id="hdUserId" type="hidden" value="@User.Claims.First(t => t.Type == "Id").Value" />
    <input id="hdStructureIds" type="hidden" value="@User.Claims.First(t => t.Type == "StructureIds").Value" />
    <input id="hdGroupIds" type="hidden" value="@User.Claims.First(t => t.Type == "GroupIds").Value" />
    <input id="hdHasManager" type="hidden" value="@User.Claims.First(t => t.Type == "ManagerId").Value" />
    <div class="wrapper">
        <header class="topnavbar-wrapper">
            @Html.Partial("_TopNavbar")
        </header>
        <aside class="aside">
            @Html.Partial("_Sidebar")
        </aside>
        <aside class="offsidebar hide"></aside>
        <section>
            <div class="content-wrapper">
                @RenderBody()
            </div>
        </section>
        <footer>
            @Html.Partial("_Footer")
        </footer>
    </div>
    <div class="modal-window"></div>
    @if (IsSectionDefined("BodyArea"))
    {
    @RenderSection("BodyArea", required: false)
    }
    @Html.AntiForgeryToken()
    <script src="~/lib/ace/1.4.10/ace.js"></script>
    <script asp-append-version="true" src="~/js/core-js.min.js"></script>
    <script type="text/javascript" src="~/lib/ckeditor/ckeditor.js"></script>
    <script src="/CoreJS?@CultureInfo.CurrentUICulture.Name.Replace("en-GB","en")"></script>
    @RenderSection("scripts", required: false)
    @{
        if (System.Globalization.CultureInfo.CurrentUICulture.Name.Equals("ar"))
        {
        <script asp-append-version="true" src="~/js/core-js.ar.min.js"></script>
        }
        if (System.Globalization.CultureInfo.CurrentUICulture.Name.Equals("fr"))
        {
        <script asp-append-version="true" src="~/js/core-js.fr.min.js"></script>
        }
    }
    <script asp-append-version="true" src="~/js/site.js"></script>
    <script asp-append-version="true" src="~/js/core-components.js"></script>
    <script asp-append-version="true" src="~/js/appComponent.js"></script>
</body>
</html>
