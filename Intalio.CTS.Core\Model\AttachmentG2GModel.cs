using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.Model
{
    public class AttachmentG2GModel
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string Extention { get; set; }
        public double FileSize { get; set; }
        public byte[] Content { get; set; }
        public bool IsOriginalMail { get; set; }
        public string FileGUID { get; set; }
        public long? FolderId { get; set; }
        public bool IsFile { get; set; }
        public string FullPath { get; set; }
    }
}