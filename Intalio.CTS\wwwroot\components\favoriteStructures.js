import Intalio from './common.js'
import Distribution from './distributionList.js'
class FavoriteStructures extends Intalio.Model {
    constructor() {
        super();

    }
    getFavoritesStructuresPerUser(userStructureId, delegationId) {
        let favStructures = [];
        let params = {
            userStructureId: userStructureId,
            delegationId: delegationId,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()

        };
        Common.ajaxGet(
            "FavoriteStructures/List",
            params,
            function (data) {
                 favStructures = data;
            },
            function () { Common.showScreenErrorMsg(); },
            false,
            '',
            false);
        return favStructures;
    }
}
let tableFavoriteStructures, self;

class FavoriteStructuresView extends Intalio.View {
    constructor(element, model) {
        super(element, "favoriteStructures", model);
    }
    render() {

        self = this;

        $.fn.select2.defaults.set("theme", "bootstrap");
        self.createStructuresToSelect();

        self.inalizeFavoriteStructureTable();


        $("#btnClearFavoriteStructure").on('click', function () {
            self.clearInputs();

        });
        $("#btnAddFavoriteStructure").click(function () {
            self.addNewFavoriteStructure();
        });
        $(document).on('keydown', function (e) {
            if (e.key === "Enter") {
                e.preventDefault(); // Prevent form submission or default behavior
                self.addNewFavoriteStructure();
            }
        });

    }
    getNextOrderValue() {
    $.ajax({
        url: '/FavoriteStructures/List',
        type: 'POST',
        data: {
            draw: 1,
            start: 0,
            length: 1000, 
        },
        success: function (response) {
            let maxOrder = 0;  
            if (response.data && response.data.length > 0) {
                for (let i = 0; i < response.data.length; i++) {
                    let item = response.data[i];
                    if (item.order > maxOrder) {
                        maxOrder = item.order;
                    }
                }
                $('#txtFavoriteStructureOrder').val(maxOrder + 1);
            } else {
                $('#txtFavoriteStructureOrder').val(1);
            }
        },
        error: function (error) {
            console.error("Error fetching favorite structures", error);
        }
    });
}

    inalizeFavoriteStructureTable() {
        Common.gridCommon();
        tableFavoriteStructures = $("#grdFavoriteStructures").on('draw.dt',
            function () {
                $('#grdFavoriteStructures tbody tr td').each(function () {
                    this.setAttribute('title', $(this).text());
                });
            }).DataTable({
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/FavoriteStructures/List",
                    "type": "POST"
                },
                "order": [],
                "columns": [
                    { title: "Id", data: "id", visible: false, "orderable": false },

                    {
                        title: Resources.Structure, "orderable": false,
                        render: function (data, full, row) {
                            let icon = "";
                            if (row.distributionId != -1)
                                icon = "fa fa-group";
                            else if (row.toUserId == -1)
                                icon = "fa fa-building-o"
                            else
                                icon = "fa fa-user-o"
                            return '<div><i  class="' + icon + '"></i> ' + row.name + '</div>';
                        }
                    },

                    { title: Resources.Order, data: "order", "orderable": false },
                    {
                        "orderable": false,
                        render: function (data, full, row) {
                            let btnDelete = document.createElement("button");
                            btnDelete.setAttribute("class", "btn btn-danger arabic-btn-group-right deleteFavoriteStructure");
                            btnDelete.setAttribute("title", Resources.Delete);
                            btnDelete.innerHTML = '<span class="fa fa-trash"></span>';
                            return btnDelete.outerHTML;
                        }
                    },
                ],
                "fnInitComplete": function (settings, json) {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: 'trpi',
                buttons: []
            });
        tableFavoriteStructures.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        tableFavoriteStructures.on('click', '.deleteFavoriteStructure', function (e) {
            let data = tableFavoriteStructures.row(e.target.closest('tr')).data();

            self.deleteFavoriteStructure(data);
        });
   self.getNextOrderValue();
    }
    deleteFavoriteStructure(data) {
        Common.showConfirmMsg(Resources.DeleteToDoConfirmation, function () {
            let params = {
                id: data.id,
                '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()

            };
            Common.ajaxDelete('/FavoriteStructures/Delete', params,
                function (data) {
                    Common.showScreenSuccessMsg(Resources.ItemDeletedSuccessfully);
                    tableFavoriteStructures.ajax.reload();
                    self.getNextOrderValue();

                },
                function (error) {
                    Common.showScreenErrorMsg();
                }, false);
        });
    }

    addNewFavoriteStructure() {
        var $form = $(self.refs['formFavoriteStructures']);
        $form.parsley().reset();
        var isValid = $form.parsley().validate();
        if (isValid) {
            let transferToObj = $('#cmbStructures').select2('data');
            transferToObj = transferToObj[0];
            var isDistributionItem = typeof transferToObj.isDistributionItem === 'boolean';
            let toStructureId = null;
            let distributionId = null;
            let toUserId = null;
            if (isDistributionItem) {
                distributionId = isDistributionItem ?
                    parseInt(transferToObj.id.split("_")[1]) : null;
            } else {
                let selectedOption = $(transferToObj.element);
                let isStructure = typeof transferToObj.isStructure === 'undefined' ?
                    selectedOption.data('isStructure') :
                    transferToObj.isStructure;

                let toUserStructureId = typeof transferToObj.structureId === 'undefined' ?
                    selectedOption.data('structureId') :
                    transferToObj.structureId;

                toStructureId = isStructure ?
                    parseInt(transferToObj.id.split("Structure")[1]) :
                    parseInt(toUserStructureId);

                toUserId = isStructure ?
                    null :
                    parseInt(transferToObj.id.split("_")[2]);
            }
            let order = $("#txtFavoriteStructureOrder").val();
            let params = {
                toUserId: toUserId,
                toStructureId: toStructureId,
                distributionId: distributionId,
                order: order,
                '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()

            };
            Common.ajaxPost(
                '/FavoriteStructures/Add',
                params,
                function (data) {
                   
                    if (data === "ItemAlreadyExists") {
                        Common.alertMsg(Resources.ItemAlreadyExists);
                    } else if (data === "OrderAlreadyExists") {
                        Common.alertMsg(Resources.OrderAlreadyExists);
                    } else if (data === "Success") {
                        Common.showScreenSuccessMsg(Resources.FavoriteStructureAddedSuccessfully);
                        self.clearInputs();
                        tableFavoriteStructures.ajax.reload();
                        self.getNextOrderValue();
                    } else {
                        Common.alertMsg(Resources.UnexpectedErrorOccurred);
                    }
                },
                function (error) {
                    Common.alertMsg(error.statusText || Resources.UnexpectedErrorOccurred);
                },
                false
            );

        }
    }
    clearInputs() {
        $("#cmbStructures").val('').trigger('change');
        $("#txtFavoriteStructureOrder").val('');
    }


    createStructuresToSelect() {
        var headers = {};
        headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
        var structureSendingRulesIds = [];

        if (window.IsStructureSender != 'True') {
            structureSendingRulesIds = $("#hdStructureIds").val().split(window.Seperator);
        } else if (window.EnableSendingRules == "True") {
            structureSendingRulesIds = $("#hdStructureIds").val().split(window.Seperator);
            let tempStructureSendingRulesIds = []
            for (let i = 0; i < structureSendingRulesIds.length; i++) {
                let temp = new CoreComponents.Lookup.SendingRules().get(Number(structureSendingRulesIds[i]),
                    null);
                if (temp.length > 0) {
                    tempStructureSendingRulesIds.push(...temp);
                }
            }
            structureSendingRulesIds = tempStructureSendingRulesIds;
        }

        var url = window.IdentityUrl + '/api/GetUsersAndStructuresWithSearchAttributes';
        var selectComponent = $("#cmbStructures");
        var selectComponentParent = $("#AllStructureContainer");
        if (window.EnableTransferToUsers == "True") {
            if (window.EnableSendingRules == "True" && structureSendingRulesIds.length === 0) {

                selectComponent.select2({
                    allowClear: false,
                    placeholder: "",
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    dropdownParent: selectComponentParent,
                    width: "100%",
                    data: []
                });
            } else {
                selectComponent.select2({
                    minimumInputLength: 0,
                    allowClear: false,
                    placeholder: "",
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    dropdownParent: selectComponentParent,
                    width: "100%",
                    templateResult: function (option) {
                        let $option = self.renderTextInSelectStructures(option)
                        return $option;
                    },
                    ajax: {
                        delay: 400,
                        url: url,
                        type: "POST",
                        dataType: 'json',
                        headers: typeof headers !== "undefined" ? headers : "",
                        data: function (term) {
                            return {
                                "text": term.term ? term.term : "",
                                "ids": structureSendingRulesIds,
                                "language": window.language,
                                "attributes": [window.structureNameAr, window.StructureNameFr, window.HideStructureFromTransfer ], "showOnlyActiveUsers": true
                            };
                        },
                        processResults: function (data, term) {
                            let result = self.getStructuresUsersOptions(data, term.term);
                            return {
                                results: result
                            };
                        }
                    },
                    sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
                }).on('change', function () {
                    $(this).trigger('input');
                });
            }
        } else {
            url = structureSendingRulesIds.length > 0 ? window.IdentityUrl + '/api/ListStructuresByIds' : window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes';
            selectComponent.select2({
                minimumInputLength: 0,
                allowClear: false,
                placeholder: "",
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                dropdownParent: selectComponentParent,
                width: "100%",
                templateResult: function (option) {
                    let $option = self.renderTextInSelectStructures(option)
                    return $option;
                },
                ajax: {
                    delay: 400,
                    url: url,
                    type: "POST",
                    dataType: 'json',
                    headers: typeof headers !== "undefined" ? headers : "",
                    data: function (term) {
                        return {
                            "text": term.term ? term.term : "",
                            "ids": structureSendingRulesIds,
                            "language": window.language,
                            "attributes": [window.structureNameAr, window.StructureNameFr, window.HideStructureFromTransfer]
                        };
                    },
                    processResults: function (data, term) {
                        let listitemsMultiList = [];
                        let allDistribution = new Distribution.DistributionList().getMyDistributionList(term.term, null, null);
                        listitemsMultiList.push(...allDistribution);
                        listitemsMultiList.push(...self.getStructuresOptions(data));
                        return {
                            results: listitemsMultiList
                        };
                    }
                },
                sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
            }).on('change', function () {
                $(this).trigger('input');
            });
        }

    }
    getStructuresUsersOptions(data, term) {
        var listitemsMultiList = [];
        let allDistribution = new Distribution.DistributionList().getMyDistributionList(term, null, null);
        listitemsMultiList.push(...allDistribution);

        var allUSersWithStructures = self.getUsersWithStructureOptions(data);
        listitemsMultiList.push(...allUSersWithStructures);

        var allStructures = self.getStructuresOptions(data.structures);
        listitemsMultiList.push(...allStructures);

        return listitemsMultiList;

    }
    getStructuresOptions(data) {
        var listitemsMultiList = [];
        $.each(data, function (key, val) {
            var structureName = val.name;
            var hideFromTransfer = false;
            if (val.attributes != null && val.attributes.length > 0) {
                var attributeLang = $.grep(val.attributes, function (e) {
                    return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                });

                let hideFromTransferAttribut = val.attributes.find(e => e.text === window.HideStructureFromTransfer && e.value != null)
                   hideFromTransfer = (hideFromTransferAttribut !== undefined && hideFromTransferAttribut?.value.toString().toLowerCase() !== 'false');

                if (attributeLang.length > 0) {
                    structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                }
            }
            var item = {};
            item.id = "Structure" + val.id;
            item.text = structureName;
            item.isStructure = true;
            item.dataId = val.id;
            item.icon = "fa fa-building-o";
            if (!hideFromTransfer) 
                listitemsMultiList.push(item);
        });
        return listitemsMultiList

    }

    getUsersWithStructureOptions(data) {
        let listitemsMultiList = [];

        $.each(data.users, function (key, val) {
            if (val.id !== Number($("#hdUserId").val())) {
                var item = {};
                var hideFromTransfer = false;

                item.id = "User_" + val.structureIds[0] + "_" + val.id;
                var currentStructure = new CoreComponents.Lookup.Structure().getFullStructure(val.structureIds[0], window.language);
                var structureName = currentStructure.name;
                if (currentStructure.attributes != null && currentStructure.attributes.length > 0) {
                    var attributeLang = $.grep(currentStructure.attributes, function (e) {
                        return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                    });
                    if (attributeLang.length > 0) {
                        structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                    }

                    let hideFromTransferAttribut = currentStructure.attributes.find(e => e.text === window.HideStructureFromTransfer && e.value != null)
                    hideFromTransfer = (hideFromTransferAttribut !== undefined && hideFromTransferAttribut?.value.toString().toLowerCase() !== 'false');
                }
                item.text = structureName + " / " + val.fullName;
                item.icon = "fa fa-user-o";
                item.isStructure = false;
                item.dataId = val.id;
                item.structureId = val.structureIds.length > 0 ? val.structureIds[0] : val.defaultStructureId;
                item.structureIds = val.structureIds;
                item.defaultStructureId = val.defaultStructureId;
                if (!hideFromTransfer)
                    listitemsMultiList.push(item);
            }
        });
        return listitemsMultiList;
    }
    renderTextInSelectStructures(option) {
        var $option;
        if (typeof option.id !== "undefined") {
            $option = $(
                '<div><i style="font-size:12px;" class="' + option.icon + '"></i> ' + option.text + '</div>'
            );
        } else {
            $option = option;
        }
        return $option;
    }

}




export default { FavoriteStructures, FavoriteStructuresView };
