﻿using Aspose.Slides;
using Intalio.Core;
using Intalio.Core.API;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Crypto.Agreement;
using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static iTextSharp.text.pdf.AcroFields;
using Language = Intalio.Core.Language;

namespace Intalio.CTS.Core.API
{
    public static class ManageEvent
    {
        
        public static bool Create(EventModel model, long userId)
        {
            bool retValue = false;
                var newEvent = new Event
                {
                    Name = model.Name,
                    Location = model.Location,
                    FromDate = DateTime.Parse(model.FromDate),
                    ToDate = DateTime.Parse(model.ToDate),
                    CreatedByUserId = userId,
                    CreatedDate = DateTime.Now,

                    DocumentId = (long)model.DocumentId,
                    Description = model.Description
                    //TransferId = model.TransferId,
                };
            newEvent.Insert();
            model.Id = newEvent.Id;
            retValue = true;
            return retValue;
        }

        public static bool Edit(EventModel model, long userId)
        {
            bool retValue = false;
            Event item = new Event().Find((long)model.Id);
            if (item != null)
            {
                item.Name = model.Name;
                item.Location = model.Location;
                item.Description = model.Description;
                item.FromDate = DateTime.Parse(model.FromDate);
                item.ToDate = DateTime.Parse(model.ToDate);
                item.Update();
                retValue = true;
            }

            return retValue;
        }





        // Method to delete an event by its ID
        public static bool Delete(long id)
            {
            bool retValue = false;
            var item = new Event().Find(id);
            if (item != null)
                {
                item.Delete(id);
                retValue = true;
            }
            return retValue;
            }


        public static async Task<(int, List<EventListViewModel>)> List(int startIndex, int pageSize,
          long documentId, long userId, int roleId, Language language = Language.EN)
        {
            //var ifHaveManageCorrespondenceAccess = Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageCorrespondence") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageCorrespondence");

            var retValue = (0, new List<EventListViewModel>());
            

                Event item = new Event();
                var countResult = item.GetCount(documentId, userId);
                var itemList = await item.ListAsync(startIndex, pageSize, documentId, userId);
        
                retValue = (await countResult, itemList.Select(t => new EventListViewModel
                {
                    Id = t.Id,
                    Name = t.Name,
                    Location = t.Location,
                    Description = t.Description,
                    FromDate = t.FromDate.ToString(Constants.DATE_FORMAT),  
                    ToDate = t.ToDate.ToString(Constants.DATE_FORMAT)  ,     
                    CreatedDate = t.CreatedDate,
                    CreatedByUserId = t.CreatedByUserId,
               CreatedBy = t.CreatedByUser != null
                    ? (language == Language.EN ? $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}": IdentityHelperExtension.GetFullName(t.CreatedByUser.Id, language)): string.Empty
                    
                }).ToList());
           

            return retValue;
        }

           
        }
    }

