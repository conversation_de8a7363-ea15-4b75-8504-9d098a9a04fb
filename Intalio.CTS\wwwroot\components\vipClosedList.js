import Intalio from './common.js'
import VipDocumentDetails from './vipDocumentDetails.js'
import DocumentClosed from './closedList.js'
import { Categories } from './lookup.js'

class VipDocumentClosed extends Intalio.Model
{
    constructor()
    {
        super();
        this.title = null;
        this.nodeId = null;
        this.categories = null;
    }
}
function buildFilters(nodeId, categories)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var filters = node.filters !== "" && node.filters !== null ? JSON.parse(node.filters) : [];
    if (filters.length > 0)
    {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++)
        {
            var filter = filters[i];
            switch (filter)
            {
                case "ReferenceNumber":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
                        '<input type="text" id="txtFilterClosedReferenceNumber" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "FromDate":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label><div class="input-group date">' +
                        '<input id="filterClosedFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterClosedFromDateError">' +
                        '<span class="input-group-addon" id="filterClosedFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterClosedFromDateError"></div></div></div></div>'
                    break;
                case "ToDate":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label><div class="input-group date">' +
                        '<input id="filterClosedToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterClosedToDateError">' +
                        '<span class="input-group-addon" id="filterClosedToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterClosedToDateError"></div></div></div></div>';
                    break;
                case "Subject":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterClosedSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Category":
                    clearFix += 6;
                    var div = '<div class="col-md-6" id="categoryFilterClosedContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterClosedCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterClosedCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++)
                    {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterClosedCategoryError"></div></div></div>';
                    html += div;
                    break;
            }
            if (clearFix === 12 || clearFix === 11)
            {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '</div>';
        $('#filtersContainer').html(html);

        var clickedSearch = false;
        $('#collapseClosedIcon').click(function ()
        {
            $('#collapseClosedIcon').empty();
            if (clickedSearch)
            {
                $('#collapseClosedIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
                $('#collapseClosedPanel').attr('class', '');
                $('#collapseClosedPanel').addClass('panel-body panel-collapse collapse in');
                clickedSearch = false;
            } else
            {
                $('#collapseClosedIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
                $('#collapseClosedPanel').attr('class', '');
                $('#collapseClosedPanel').addClass('panel-body panel-collapse collapse');
                clickedSearch = true;
            }
        });
        $("#btnFilterClosedSearch").on('click', function ()
        {
            gFromSearch = true;
            gNoMoreData = false;
            gPageIndex = 0;
            if (!$(".filterInfoDiv").hasClass("hidden"))
            {
                $(".filterInfoDiv").addClass("hidden");
            }
            loadClosedList();
        });
        $("#btnFilterClosedClear").on('click', function ()
        {
            $("#cmbFilterClosedCategory").val('').trigger('change');
            $("#cmbFilterClosedStatus").val('').trigger('change');
            $("#txtFilterClosedReferenceNumber").val('');
            $("#txtFilterClosedSubject").val('');
            $("#cmbFilterClosedStatus").val('').trigger('change');
            fromDate.clear();
            toDate.clear();
            gFromSearch = true;
            gNoMoreData = false;
            gPageIndex = 0;
            if (!$(".filterInfoDiv").hasClass("hidden"))
            {
                $(".filterInfoDiv").addClass("hidden");
            }
            loadClosedList();
        });
        $('#cmbFilterClosedCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterClosedContainer')
        });
        $("#cmbFilterClosedCategory").val('').trigger('change');
        $('#cmbFilterClosedStatus').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#statusFilterClosedContainer')
        });
        $("#cmbFilterClosedStatus").val('').trigger('change');
        var fromDate = $('#filterClosedFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    maxDate: jQuery('#filterClosedToDate').val() && jQuery('#filterClosedToDate').val() !== "" ? jQuery('#filterClosedToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterClosedFromDate_img").click(function ()
        {
            fromDate.toggle();
        });
        var toDate = $('#filterClosedToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    minDate: jQuery('#filterClosedFromDate').val() && jQuery('#filterClosedFromDate').val() !== "" ? jQuery('#filterClosedFromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterClosedToDate_img").click(function ()
        {
            toDate.toggle();
        });
        $('#txtFilterClosedReferenceNumber').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterClosedSearch').focus();
                }
                else
                {
                    $('#filterClosedFromDate').focus();
                }
            }
        });
        $('#btnFilterClosedClear').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterClosedSearch').focus();
                }
                else
                {
                    $('#txtFilterClosedReferenceNumber').focus();
                }
            }
        });
    } else
    {
        $("#btnOpenSearchClosedModal").remove();
        $("#divSearchClosed").remove();
    }
}
function openDocument(id, nodeId)
{
    Common.ajaxGet('/Document/GetSearchDocument', { id: id }, function (response)
    {

        if (response && response === "NoAccess")
        {
            Common.alertMsg(Resources.NoPermission);
        } else
        {
            $('.card-max-width').addClass('card-min-width');
            $('.card-max-width').removeClass('card-max-width');
            $('input:checkbox').removeAttr('checked');
            $('#closedListContainer li').removeClass("active");
            $("input[data-id='" + id + "']").parent().parent().parent().addClass("active");
            $("input[data-id='" + id + "']").prop('checked', true);
            gSelectedRowId = id;
            gLocked = false;
            Common.setActiveSidebarMenu("liClosed" + nodeId);
            $(".delegation").removeClass("active");

            var model = new VipDocumentDetails.VipDocumentDetails();
            model.documentId = response.id;
            model.referenceNumber = response.referenceNumber;
            model.categoryName = response.categoryName;
            model.statusId = response.status;
            model.documentModel = response;
            model.readonly = true;
            model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            model.showMyTransfer = false;
            model.attachmentId = response.attachmentId;
            model.formData = (response.formData || "") !== "" ? eval("(" + response.formData + ")") : null;
            model.formDesigner = (response.formDesigner || "") !== "" ? JSON.parse(response.formDesigner) : null;
            model.formDesignerTranslation = (response.formDesignerTranslation || "") !== "" ? JSON.parse(response.formDesignerTranslation) : null;
            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
            var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
            model.tabs = $.grep(tabs, function (element, index)
            {
                return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                    !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                    !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                    !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
            });
            model.tabsWithStatic = tabs;
            model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
            model.showBackButton = false;
            model.nodeId = nodeId;
            model.showPreview = window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer" && response.attachmentId !== null;
            model.attachmentCount = response.attachmentCount;
            model.noteCount = response.notesCount;
            model.linkedCount = response.linkedCorrespondanceCount
            model.attachmentVersion = response.attachmentVersion;
            var wrapper = $("#closedDocumentDetailsContainer");
            wrapper.empty();
            $(".modal-window").empty();
            var view = new VipDocumentDetails.VipDocumentDetailsView(wrapper, model);
            view.render();
            $(".documentHeader").hide();
            $(".waitingBackground").removeClass("waitingBackground");
            $(".vipDetailsPanel").show();
            $(".vipCorrLeftPanel").removeClass("col-lg-12 col-md-12 col-sm-12 col-xs-12");
            $(".vipCorrLeftPanel").addClass("col-lg-3 col-md-4 col-sm-4 col-xs-12");
            $(".mdl-ul").removeAttr("style")
        }
    }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
}
function loadClosedList()
{
    if (!gNoMoreData)
    {
        Common.mask(document.getElementById('closedListContainer'), "closedListContainer-mask");
        var params = {};
        addFilters(params);
        params.NodeId = gSelf.model.nodeId;
        params.DelegationId = gSelf.model.delegationId;
        params.start = gPageIndex;
        params.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
        Common.ajaxPost('/Document/ListClosedVip', params, function (response)
        {
            if (response.length > 0)
            {
                gPageIndex += window.Paging;
                if (response.length < window.Paging)
                {
                    gNoMoreData = true;
                }
            } else
            {
                gNoMoreData = true;
            }
            createListData(response);
            gLocked = false;
            Common.unmask("closedListContainer-mask");
            if (gFromSearch)
            {
                $("#divSearchClosed").fadeOut();
            }
        }, function () { gLocked = false; Common.showScreenErrorMsg(); });
    } else
    {
        gLocked = false;
    }
}
function createListData(data)
{
    var html = '';
    if (data.length === 0 && gPageIndex === 0)
    {
        html = '<ul class="mdl-ul"><div class="nodata dataTables_empty pt-lg">' + Resources.NoDataToDisplay + '</div></ul>';
        $('#closedListContainer').html(html);
    } else if (data.length > 0)
    {
        var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        html = '<ul class="mdl-ul" style=" margin: 0px 5px 0 0;">';
        var htmlLi = '';
        var color = "";

        for (var i = 0; i < data.length; i++)
        {
            var document = data[i];
            var htmlIcons = "";
            if (document.importanceId)
            {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var j = 0; j < importances.length; j++)
                {
                    if (importances[j].id === document.importanceId)
                    {
                        htmlIcons += "<i class='fa fa-exclamation fa-lg mr-sm' title='" + importances[j].text + "' style='color:" + importances[j].color + "'></i>";
                    }
                }
            }
            if (window.PriorityPrivacyAction == "2") {
                for (var j = 0; j < privacies.length; j++) {
                    if (privacies[j].id === document.privacyId) {
                        color = privacies[j].color;
                        break;
                    }
                }
            } else {
                for (var j = 0; j < priorities.length; j++) {
                    if (priorities[j].id === document.priorityId) {
                        color = priorities[j].color;
                        break;
                    }
                }
            }
            document.referenceNumber = document.referenceNumber ?? "";
            //var to = document.sendingEntity !== "" ? document.toStructure + (document.toUser !== "" ? '/' + document.toUser : document.toUser) : document.toUser;
            htmlLi += '<li class="mdl-li">';
            htmlLi += '<div class="mdl-container-document">';
            htmlLi += '<div id="leftbox" class="pull-left">';
            htmlLi += '<input data-id=' + document.id + ' data-categoryid=' + document.categoryId + ' type="checkbox" class="pointer m-sm ml-10px" onclick="event.stopPropagation();">';
            htmlLi += "<input type='hidden' data-id=" + document.id + " value='" + JSON.stringify(document) + "'/>";
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-circle icon-primary'></i></span>"
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-arrow-left'></i></span>"
            htmlLi += '</div>';
            htmlLi += '<div id="middlebox" class="mdl-text-msg pull-left pl-sm card-max-width">';
            //htmlLi += '<span class="dot"></span>';
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + document.referenceNumber + '">' + document.referenceNumber + '</span>';
            htmlLi += '<span class="mdl-span text-primary bold" style="color:' + color + '" title="' + document.subject + '">' + document.subject + '</span>';
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + document.sendingEntity + '">' + document.sendingEntity + '</span>';
            htmlLi += '</div>';
            htmlLi += '<div id="rightbox" class="pull-right text-right"><div class="mdl-time mr-sm" title="' + Resources.CreatedDate + '">' + dateFormat(document.createdDate) + '</div>';

            if (htmlIcons !== "")
            {
                htmlLi += '<div class="mdl-action">' + htmlIcons + '</div>';
            }
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</li>';
        }
        html += htmlLi;
        html += '</ul>';
        if (gPageIndex === 15)
        {
            $('#closedListContainer').html(html);
        } else
        {
            $('#closedListContainer ul').append(htmlLi);
        }
    }
}
function addFilters(d)
{
    d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
    if (gFromSearch)
    {
        d.CategoryId = $("#cmbFilterClosedCategory").val() !== null && typeof $("#cmbFilterClosedCategory").val() !== "undefined" ? $("#cmbFilterClosedCategory").val() : "0";
        d.StatusId = $("#cmbFilterClosedStatus").val() !== null && typeof $("#cmbFilterClosedStatus").val() !== "undefined" ? $("#cmbFilterClosedStatus").val() : "0";
        d.ReferenceNumber = $("#txtFilterClosedReferenceNumber").val() !== "" && typeof $("#txtFilterClosedReferenceNumber").val() !== "undefined" ? $("#txtFilterClosedReferenceNumber").val() : "";
        d.Subject = $("#txtFilterClosedSubject").val() !== "" && typeof $("#txtFilterClosedSubject").val() !== "undefined" ? $("#txtFilterClosedSubject").val() : "";
        d.FromDate = $("#filterClosedFromDate").val() !== "" && typeof $("#filterClosedFromDate").val() !== "undefined" ? $("#filterClosedFromDate").val() : "";
        d.ToDate = $("#filterClosedToDate").val() !== "" && typeof $("#filterClosedToDate").val() !== "undefined" ? $("#filterClosedToDate").val() : "";
    }
}
function dateFormat(dateText)
{
    var dateFull = dateText.split(" ")[0].split("/");
    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth() + 1; //January is 0!
    var yy = today.getFullYear();
    var time = DateConverter.toHijriFormated(dateText.split(" ")[0], null, window.CalendarType);
    if (parseInt(dateFull[0]) === dd && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy)
    {
        time = "";
        var timeSeparator = ":";
        var hours = parseInt(dateText.split(" ")[1].split(":")[0]);
        var amPm = Resources.AM;
        if (hours > 12)
        {
            time += (hours - 12) + timeSeparator;
            amPm = Resources.PM;
        } else if (hours === 12)
        {
            time += "12" + timeSeparator;
            amPm = Resources.PM;
        } else
        {
            time += (hours < 10 ? '0' : '') + hours + timeSeparator;
            amPm = Resources.AM;
        }
        var minutes = parseInt(dateText.split(" ")[1].split(":")[1]);
        minutes = (minutes < 10 ? '0' : '') + minutes;
        time += minutes + " " + amPm;
    } else if (parseInt(dateFull[0]) === (dd - 1) && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy)
    {
        time = Resources.Yesterday;
    }
    return time;
}
var gLocked = false;
var gNoMoreData = false;
var gFromSearch = false;
var gPageIndex = 0;
var gSelectedRowId, gSelf;
var wrapperParent;
class VipDocumentClosedView extends Intalio.View
{
    constructor(element, model)
    {
        wrapperParent = model;
        super(element, "vipclosed", model);
    }
    render()
    {
        $.fn.select2.defaults.set("theme", "bootstrap");
        gSelf = this;
        gLocked = false;
        gPageIndex = 0;
        gNoMoreData = false;
        gFromSearch = false;
        gSelectedRowId = null;
        var followUpCategoryIndex = gSelf.model.categories.findIndex(item => item.id == window.FollowUpCategory);
        gSelf.model.categories.splice(followUpCategoryIndex, 1);

        buildFilters(gSelf.model.nodeId, gSelf.model.categories);
        loadClosedList();
        var lastScrollTop = 0;
        $('#closedListContainer').on('scroll', function ()
        {
            var div = $(this);
            var st = div.scrollTop();
            if (st > lastScrollTop && st + div.innerHeight() + 5 >= div[0].scrollHeight)
            {
                if (!gLocked)
                {
                    gLocked = true;
                    try
                    {
                        loadClosedList();
                    } catch (e)
                    {
                        gLocked = false;
                    }
                }
            }
            lastScrollTop = st;
        });
        $('#closedListContainer').on('click', 'li', function (e)
        {
            if (!gLocked)
            {
                gLocked = true;
                try
                {
                    var input = this.getElementsByTagName('input')[0];
                    if (typeof input !== "undefined")
                    {
                        if (!$(this).hasClass("active"))
                        {
                            var id = input.getAttribute("data-id");
                            if (gSelectedRowId !== id)
                            {
                                if (!$(".filterInfoDiv").hasClass("hidden"))
                                {
                                    $(".filterInfoDiv").addClass("hidden");
                                }
                                openDocument(id, gSelf.model.nodeId);
                            } else
                            {
                                gLocked = false;
                            }
                        } else
                        {
                            gLocked = false;
                        }
                    } else
                    {
                        gLocked = false;
                    }
                } catch (e)
                {
                    gLocked = false;
                }
            }
        });
        $('#closedListContainer').on('click', 'input', function ()
        {
            var input = this;
            if (typeof input !== "undefined")
            {
                input.checked = input.checked ? false : true;
            }
        });
        $('#chkAll').change(function ()
        {
            var checked = $(this).prop('checked');
            $('#closedListContainer input[type="checkbox"]').prop('checked', checked);
        });
        $(document).click(function (e)
        {
            if ($(e.target).hasClass("select2-selection__choice__remove") || e.target.tagName.toLowerCase() === "body")
            {
                return;
            }
        });
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        SecurityMatrix.initContextMenuVip(securityMatrix, gSelf.model.nodeId);
        SecurityMatrix.initToolbarMenuVip(securityMatrix, gSelf.model.nodeId, 'closedListContainer');

        $('.filterInfoDiv').draggable({ containment: 'window', cancel: '.cancelDrag' });
        $("#btnOpenSearchClosedModal").on("click", function ()
        {
            if (window.language == "ar")
            {
                if ($(".vipDetailsPanel").css('display') == 'none') {
                    var position = $("#btnOpenSearchClosedModal").position().left + $("#btnOpenSearchClosedModal").width() + 970;
                    $(".filterInfoDiv").attr('style', 'right:' + position + 'px;top:170px;');
                }
                else
                    $(".filterInfoDiv").attr('style', 'right:' + $("#btnOpenSearchClosedModal").position().right + 'px;top:170px;');
            }
            else
            {
                if ($(".vipDetailsPanel").css('display') == 'none') {
                    var position = $("#btnOpenSearchClosedModal").position().left - 220;
                    $(".filterInfoDiv").attr('style', 'left:' + position + 'px;top:170px;');

                }
                else
                    $(".filterInfoDiv").attr('style', 'left:' + $("#btnOpenSearchClosedModal").position().left + 'px;top:170px;');
            }
            if ($(".filterInfoDiv").hasClass("hidden"))
            {
                $(".filterInfoDiv").removeClass("hidden");
            } else
            {
                $(".filterInfoDiv").addClass("hidden");
            }
        });

        $("#btnFilterCloseIcon").on("click", function ()
        {
            $(".filterInfoDiv").addClass("hidden");
        });

        var actions = $.grep(securityMatrix.SecurityNodes[Number(gSelf.model.nodeId)].Actions, function (element, index)
        {
            return element.Type === Number(TypeAction.Toolbar);
        });
        if (actions.length === 0)
        {
            $("#closedRow").addClass("hideCheckbox");
        }
        $('#filtersContainer input').off('keydown');
        $('#filtersContainer input').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                $("#btnFilterClosedSearch").trigger('click');
            }
        });
        $('.toggleVIP').on('click', function () {
            if (window.InboxMode === "LocalVIPView") {
                window.InboxMode = "InboxDefault";
            } else if (window.InboxMode !== "InboxDefault") {
                window.InboxMode = "LocalInboxDefaultView";
            }

            let wrapper = $(".content-wrapper");
            let defaultmodel = new DocumentClosed.DocumentClosed();
            defaultmodel.nodeId = wrapperParent.nodeId;
            defaultmodel.delegationId = wrapperParent.delegationId;
            defaultmodel.categories = new Categories().get(window.language, wrapperParent.delegationId);
            defaultmodel.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            defaultmodel.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            defaultmodel.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            defaultmodel.title = $('.content-heading').text();
            let documentView = new DocumentClosed.DocumentClosedView(wrapper, defaultmodel);
            documentView.render();
        })
    }
}
export default { VipDocumentClosed, VipDocumentClosedView };