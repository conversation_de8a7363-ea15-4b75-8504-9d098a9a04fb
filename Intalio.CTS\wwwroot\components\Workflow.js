﻿import Intalio from './common.js'
import { IdentityService, Helper } from './lookup.js'

function createUsersDropdown(id, model) {
    //var headers = {};
    //headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    var url = /*window.IdentityUrl +*/ '/User/GetUsersFromCTS';
    var selectComponent = $("#" + id);
    if (model.enableTransferToUsers) {
        selectComponent.select2({
            minimumInputLength: 0,
            allowClear: true,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            dropdownParent: $('#modalWorkflow'),
            language: window.language,
            width: "100%",
            templateResult: function (option) {
                var $option;
                if (typeof option.id !== "undefined") {
                    $option = $(
                        '<div><i style="font-size:12px;" class="' + option.icon + '"></i> ' + option.text + '</div>'
                    );
                } else {
                    $option = option;
                }
                return $option;
            },
            ajax: {
                delay: 400,
                url: url,
                type: "GET",
                dataType: 'json',
                //headers: typeof headers !== "undefined" ? headers : "",
                data: function (params) {
                    //return {
                    //    "text": term.term ? term.term : "",
                    //    "ids": [],
                    //    "language": window.language,
                    //    "attributes": [window.StructureNameAr, window.StructureNameFr, window.FirstNameAr, window.LastNameAr],
                    //    "showOnlyActiveUsers": true
                    //};
                    return { "userName": params.term ? params.term : "", delegationId: null };
                },
                processResults: function (data, term) {
                    var listitemsMultiList = [];
                    var termSearch = term.term ? term.term : "";
                    let strcutredCached = new Map();
                    $.each(data, function (key, val) {
                        var currentStructure;
                        if (strcutredCached[val.userStructure[0].structure.id] != undefined) {
                            currentStructure = strcutredCached[val.userStructure[0].structure.id]
                        } else {
                            currentStructure = new IdentityService().getFullStructure(val.userStructure[0].structure.id, window.language);
                            strcutredCached[currentStructure.id] = currentStructure;
                        }
                        var structureName = currentStructure.name;
                        if (currentStructure.attributes != null && currentStructure.attributes.length > 0) {
                            var attributeLang = $.grep(currentStructure.attributes, function (e) {
                                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                            });
                            if (attributeLang.length > 0) {
                                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                            }
                        }

                        var fullName = (val.firstname + " " + val.lastname);
                        
                        if (window.language != 'en') {
                            if (window.language == 'er')
                                fullName = (val.firstnameAr + " " + val.lastnameAr);
                            else if (window.language == 'fr')
                                fullName = (val.firstnameFr + " " + val.lastnameFr);
                        }
                        fullName = fullName.trim() == "" ? (val.firstname + " " + val.lastname) : fullName;
                        var item = {};
                        item.id = "User_" + val.userStructure[0].structureId + "_" + val.id;
                        item.text = structureName + " / " + fullName;
                        item.icon = "fa fa-user-o";
                        item.isStructure = false;
                        item.dataId = val.id;
                        item.structureId = val.userStructure[0].structure.id > 0 ? val.userStructure[0].structure.id : val.userStructure[0].structureId;
                        //item.structureIds = val.structureIds;
                        item.defaultStructureId = val.userStructure[0].structureId;
                        listitemsMultiList.push(item);
                    });

                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        })
    }
};

function UpdateOrderDropDown() {
    gOrderData = [];
    if (gUsersObject) {
        if (gUsersObject.length > 0) {
            for (var i = 0; i <= gUsersObject.length; i++) {
                gOrderData.push(i + 1);
            }
        } else {
            gOrderData.push(1);
        }
    } else {
        gOrderData.push(1);
    }

    $('#cmbOrder').select2('destroy').empty().select2({
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        minimumResultsForSearch: -1,
        placeholder: Resources.SelectOrder,
        width: "100%",
        allowClear: false,
        data: gOrderData,
        dropdownParent: $('#cmbOrderContainer')
    }).on('select2:select', function () {
        $('#cmbOrder').parsley().validate();
    });
    $('#cmbOrder').val(gOrderData.length).trigger('change');
}

function InsertUserRow(User, order, purpose,stepId) {
    var usersTable = $('#usersTable').DataTable();
    var RevieverStructure = User.id.split('_')[1];
    var refRows = usersTable.rows();
    var resultSameUser = gUsersObject.filter(obj => {
        return obj.userId === User.dataId
    })
    if (resultSameUser.length > 0 && resultSameUser[0].order == order - 1) {
        Common.alertMsg(Resources.NotAllowedToAddSameUserToNextStep);
        return false;
    }
    for (var i = 0; i < gUsersObject.length; i++) {
        if (gUsersObject[i].order >= order) {
            gUsersObject[i].order++;
        }
    }

    var refRowsIds = [];
    for (var i = 0; i < refRows.data().length; i++) {
        refRowsIds.push(parseInt(usersTable.row(i).data().rowId));
    }
    var result = gUsersObject.filter(obj => {
        return obj.userId === User.dataId && obj.structureId === RevieverStructure
    })
   
    for (var i = 0; i < gUsersObject.length; i++) {
        for (var j = 0; j < refRowsIds.length; j++) {
            if (gUsersObject[i].rowId === refRowsIds[j]) {
                usersTable.row('#' + refRowsIds[j]).data({ 'id': gUsersObject[i].id, 'rowId': gUsersObject[i].rowId, "structureId": gUsersObject[i].structureId, "userId": gUsersObject[i].userId, "text": gUsersObject[i].text, "purposeId": gUsersObject[i].purposeId, "purposeForSignature": gUsersObject[i].purposeForSignature, "purpose": gUsersObject[i].purpose, "order": gUsersObject[i].order, "isCompleted": gUsersObject[i].isCompleted }).draw();
                break;
            }
        }
    }
    
    if (result.length == 0) {
        var lastItem = gUsersObject[gUsersObject.length - 1];
        if (lastItem) {
            var maxid = 0;
            for (var i = 0; i < gUsersObject.length; i++) {
                if (maxid < gUsersObject[i].rowId) {
                    maxid = gUsersObject[i].rowId;
                }
            }

            usersTable.row.add({ 'id': stepId, 'rowId': maxid + 1, "structureId": RevieverStructure, "userId": User.dataId, "purposeId": purpose.id, "purposeForSignature": purpose.forSignature, "purpose": purpose.text, "text": User.text, "order": order, "isCompleted": false }).draw();
            //gUsersObject.push({ 'id': maxid + 1, 'type': type, 'content': content, "order": order, "resetByYear": resetByYear, "category": category });
        } else {
            usersTable.row.add({ 'id': stepId, 'rowId': 1, "structureId": RevieverStructure, "userId": User.dataId, "text": User.text, "purposeId": purpose.id, "purposeForSignature": purpose.forSignature, "purpose": purpose.text, "order": order, "isCompleted": false }).draw();
            //gUsersObject.push({ 'id': 1, 'type': type, 'content': content, "order": order, "resetByYear": resetByYear, "category": category });
        }
    }
    else {
        //UserAlreadyExists
        Common.alertMsg(Resources.UserAlreadyExists);
    }


    //UpdateSessionStorage();
    UpdateOrderDropDown();
}

function UpdateUserRow(User, order, purpose,self) {

    var usersTable = $('#usersTable').DataTable();
    var targetbtn = $("#edit-" + $("#rowId").val());
    var currentOrder = usersTable.row(targetbtn.parents("tr")).data().order;
    var userRows = usersTable.rows();
    var RevieverStructure = User.id.split('_')[1];
    if (order <= allowedOrderToEdit && currentOrder != order && self.model.workflowId && !self.model.isDraft) {
        Common.showScreenErrorMsg(Resources.InvalidOrder);
        return false;
    }
    if (User.dataId == undefined) {
        User.dataId = User.id.split('_')[2];
    }
    var resultSameUser = gUsersObject.filter(obj => {
        return obj.userId === User.dataId && obj.rowId != $("#rowId").val();
    })

    if (resultSameUser.length > 0 && Math.abs(parseInt(resultSameUser[0].order) - parseInt(order)) == 1) {
        Common.alertMsg(Resources.NotAllowedToAddSameUserToNextStep);
        return false;
    }

    for (var i = 0; i < gUsersObject.length; i++) {
        if (gUsersObject[i].order >= order && gUsersObject[i].order < currentOrder) {
            gUsersObject[i].order++;
        } else if (gUsersObject[i].order <= order && gUsersObject[i].order >= currentOrder) {
            gUsersObject[i].order--;
        }
    }
    var refRowsIds = [];
    for (var i = 0; i < userRows.data().length; i++) {
        refRowsIds.push(parseInt(usersTable.row(i).data().rowId));
    }
    for (var i = 0; i < gUsersObject.length; i++) {
        for (var j = 0; j < refRowsIds.length; j++) {
            if (gUsersObject[i].rowId === refRowsIds[j] && gUsersObject[i].rowId !== parseInt($("#rowId").val())) {
                usersTable.row('#' + refRowsIds[j]).data({ 'id': usersTable.row('#' + refRowsIds[j]).data().id != "" ? parseInt(usersTable.row('#' + refRowsIds[j]).data().id) : "", 'rowId': parseInt(usersTable.row('#' + refRowsIds[j]).data().rowId), "structureId": usersTable.row('#' + refRowsIds[j]).data().structureId, "userId": usersTable.row('#' + refRowsIds[j]).data().userId, "purposeId": usersTable.row('#' + refRowsIds[j]).data().purposeId, "purposeForSignature": gUsersObject[i].purposeForSignature, "purpose": gUsersObject[i].purpose, "text": gUsersObject[i].text, "order": gUsersObject[i].order, "isCompleted": gUsersObject[i].isCompleted }).draw();
                break;
            }
        }
    }
    usersTable.row(targetbtn.parents("tr")).data({ 'id': $("#hdStepId").val() != "" ? parseInt($("#hdStepId").val()) : "", 'rowId': parseInt($("#rowId").val()), "structureId": RevieverStructure, "userId": User.dataId, "purposeId": purpose.id, "purposeForSignature": purpose.forSignature, "purpose": purpose.text, "text": User.text, "order": order, "isCompleted": false }).draw();
    for (var i = 0; i < gUsersObject.length; i++) {
        if (gUsersObject[i].rowId === parseInt($("#rowId").val())) {
            gUsersObject[i].structureId = RevieverStructure;
            if (User.dataId != null && User.dataId != undefined) {
                gUsersObject[i].userId = User.dataId;
            }
            else {
                gUsersObject[i].userId = User.id.split('_')[2];
            }
            gUsersObject[i].username = User.text;
            gUsersObject[i].order = order;
            gUsersObject[i].purposeId = purpose.id;
            gUsersObject[i].purposeForSignature = purpose.forSignature;
            gUsersObject[i].isCompleted = false;
            break;
        }
    }
    UpdateOrderDropDown();
}

function DrawTable(self) {
    $('#usersTable').on('draw.dt',
        function () {
            $('#usersTable tbody tr td').each(function () {
                this.setAttribute('title', $(this).text());
            });
        }).DataTable({
            "order": [[8, "asc"]],
            rowId: 'rowId',
            "bPaginate": false,
            "bInfo": false,
            
            "language": {
                "lengthMenu": Resources.DatatableLengthMenu,
                "zeroRecords": Resources.DatatableZeroRecords,
                "infoEmpty": Resources.DatatableInfoEmpty,
                "infoFiltered": Resources.DatatableinfoFiltered,
                "paginate": {
                    "previous": Resources.Previous,
                    "next": Resources.Next,
                }
            },
            columns: [
                { data: "id", visible: false, 'orderable': false, 'sortable': false },
                { data: "rowId", visible: false, 'orderable': false, 'sortable': false },
                { data: "structureId", visible: false, 'orderable': false, 'sortable': false },
                { data: "userId", visible: false, 'orderable': false, 'sortable': false },

                { title: Resources.User, data: "text", 'orderable': false, 'sortable': false },
                { data: "purposeId", visible: false, 'orderable': false, 'sortable': false },
                { data: "purposeForSignature", visible: false, 'orderable': false, 'sortable': false },
                { title: Resources.Purpose, data: "purpose", 'orderable': false, 'sortable': false },
                { title: Resources.Order, data: "order", className: "width100", 'orderable': true },
                { data: "isCompleted", visible: false, 'orderable': false, 'sortable': false },
                {
                    data: "",
                    className: "dt-center width20",
                    'orderable': false,
                    'sortable': false,
                    render: function (data, type, row) {
                        if ((!self.model.readOnly && (row.rowId > disabledRows.length + 1 || self.model.workflowId == undefined)) || self.model.isDraft) {
                            var btn = document.createElement('button');
                            btn.setAttribute('class', 'btn btn-xs btn-primary js-edit');
                            btn.setAttribute('id', 'edit-' + row.rowId);
                            btn.setAttribute("type", "button");
                            btn.setAttribute('clickattr', 'EditStep(' + JSON.stringify(row) + ',this' + ')');
                            btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                            return btn.outerHTML;
                        } else {
                            return null;
                        }

                    },
                    width: "10px"
                },
                {
                    data: "",
                    className: "dt-center width20",
                    'orderable': false,
                    'sortable': false,
                    render: function (data, type, row) {
                        if ((!self.model.readOnly && (row.rowId > disabledRows.length + 1 || self.model.workflowId == undefined)) || self.model.isDraft) {
                            var btn = document.createElement('button');
                            btn.setAttribute('class', 'btn btn-xs btn-danger js-delete');
                            btn.setAttribute('id', 'delete-' + row.rowId);
                            btn.setAttribute("type", "button");
                            btn.setAttribute('clickattr', 'DeleteReference(' + JSON.stringify(row) + ',this,' + self.model.workflowId + ',' + self.model.documentId + ',' + self.model.transferId + ',' + self.model.delegationId 
                                +')');
                            btn.innerHTML = "<i class='fa fa-trash-o'/>";
                            return btn.outerHTML;
                        } else {
                            return null;
                        }

                    },
                    width: "10px"
                }
            ],
            dom: 'trpi',
            "createdRow": function (row, data, index) {
                gUsersObject.push(data);
            }
        });
    $('#usersTable tbody').on('click', ".btn", function () {
        var onclick = $(this).attr("clickattr");
        eval(onclick);
    });
}

function ClearForm() {
    UpdateOrderDropDown();
    $("#formPost").parsley().reset();
    $('#cmbOrder').val($('#cmbOrder')[0].options.length).trigger('change');
    $('#cmbUsers').val('').trigger('change');
    $('#cmbPurpose').val('1').trigger('change');
    $('#rowId').val('');
}

function DeleteReference(row, rowdata,workflowId,documentId,transferId,delegationId) {
    let deleteParams = {
        'stepId': row.id,
        'workflowId': workflowId,
        'documentId': documentId,
        'transferId': transferId,
        'delegationId':delegationId
    }

    Common.ajaxDelete('/WorkflowStep/DeleteStep', deleteParams, function (retVal) {
        if ($("#rowId").val() !== "") {
            ClearForm();
        }
        var usersTable = $('#usersTable').DataTable();
        usersTable.row($(rowdata).closest("tr")).remove().draw();
        for (var i = 0; i < gUsersObject.length; i++) {
            if (gUsersObject[i].rowId === row.rowId) {
                gUsersObject.splice(i, 1);
                break;
            }
        }
        for (var i = 0; i < gUsersObject.length; i++) {
            if (gUsersObject[i].order > row.order) {
                gUsersObject[i].order--;
            }
        }
        var refRows = usersTable.rows();
        var refRowsIds = [];
        for (var i = 0; i < refRows.data().length; i++) {
            refRowsIds.push(parseInt(usersTable.row(i).data().rowId));
        }

        for (var i = 0; i < gUsersObject.length; i++) {
            for (var j = 0; j < refRowsIds.length; j++) {
                if (gUsersObject[i].rowId === refRowsIds[j]) {
                    usersTable.row('#' + refRowsIds[j]).data({ 'id': parseInt(usersTable.row('#' + refRowsIds[j]).data().id), 'rowId': usersTable.row('#' + refRowsIds[j]).data().rowId, "structureId": usersTable.row('#' + refRowsIds[j]).data().structureId, "userId": usersTable.row('#' + refRowsIds[j]).data().userId, "text": usersTable.row('#' + refRowsIds[j]).data().text, "purposeId": usersTable.row('#' + refRowsIds[j]).data().purposeId, "purposeForSignature": gUsersObject[i].purposeForSignature, "purpose": gUsersObject[i].purpose, "order": gUsersObject[i].order, "isCompleted": gUsersObject[i].isCompleted }).draw();
                    break;
                }
            }
        }

        UpdateOrderDropDown();
    })

   
}

function EditStep(row, rowdata) {
    ClearForm();
    UpdateOrderDropDownOnEdit();
    $("#hdStepId").val(row.id);
    $("#rowId").val(row.rowId);

    var user = new IdentityService().getFullUser(row.userId, window.language);
    var item = {};
    item.id = "User_" + row.structureId + "_" + row.userId;
    item.text = row.text;
    item.icon = "fa fa-user-o";
    item.isStructure = false;
    item.dataId = row.userId;
    item.structureId = row.structureId;
    item.structureIds = user.structureIds;
    item.defaultStructureId = user.defaultStructureId;
    //var selectedData = [];
    //selectedData.push(item);

    var newOption = new Option(item.text, item.id, true, true);
    $('#cmbUsers').append(newOption).trigger('change');

    //$("#cmbUsers").select2({
    //    data: selectedData
    //});
    $('#cmbUsers').val(item.id).trigger('change');
    $('#cmbPurpose').val(row.purposeId).trigger('change');
    $('#cmbOrder').val(row.order).trigger('change');

    //return false;
}

function UpdateOrderDropDownOnEdit() {
    gOrderData = [];
    if (gUsersObject) {
        if (gUsersObject.length > 0) {
            for (var i = 0; i < gUsersObject.length; i++) {
                gOrderData.push(i + 1);
            }
        } else {
            gOrderData.push(1);
        }
    } else {
        gOrderData.push(1);
    }

    $('#cmbOrder').select2('destroy').empty().select2({
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        minimumResultsForSearch: -1,
        placeholder: Resources.SelectOrder,
        width: "100%",
        allowClear: false,
        data: gOrderData,
        dropdownParent: $('#cmbOrderContainer')
    }).on('select2:select', function () {
        $('#cmbOrder').parsley().validate();
    });
    $('#cmbOrder').val(gOrderData.length).trigger('change');
}

var gLocked = false;
var self;
var gUsersObject = [];
let gOrderData = [];
var purposes = [];
var disabledRows = [];
var allowedOrderToEdit = 0;

class Workflow extends Intalio.Model {
    constructor() {
        super();
        this.documentId;
        this.transferId;
        this.workflowId;
        this.workflowSteps = [];
        this.enableTransferToUsers = window.EnableTransferToUsers;
        this.readOnly = false;
        this.workflowstepId
        this.viewMode = false;
        this.edit = false;
        this.isDraft = false;
        this.callBack = null;
    }
}
class WorkflowView extends Intalio.View {
    constructor(element, model) {
        super(element, "workflow", model);
    }
    render() {
        self = this;
        var model = this.model;
        gUsersObject = [];
        purposes = new Helper().getPurpose(window.language).filter(obj => {
            return obj.cCed != true
        });
        $.fn.select2.defaults.set("theme", "bootstrap");
        createUsersDropdown('cmbUsers', model);
        if (model.readOnly) {
            $('#SelectUsersDiv').addClass('hidden');
            $("#btnSubmit").addClass('hidden');
            $("#btnWorkflowSave").addClass('hidden');
        }

        $('#cmbPurpose').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            dropdownParent: $('#PurposeContainer'),
            data: purposes
        });

        $('#cmbOrder').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#modalWorkflow')
        });
        $("#cmbOrder").val('').trigger('change');

        UpdateOrderDropDownOnEdit(); 
        DrawTable(self);

        $("#rowId").val("");

        $("#btnSubmit").on('click', function () {
            if (gUsersObject.length == 0) {
                Common.alertMsg(Resources.NoWorkflowStepsAdded);
                return;
            }
            var params = {
                'WorkflowId': self.model.workflowId,
                "DocumentId": self.model.documentId,
                'TransferId': self.model.transferId,
                'WorkflowUsers': gUsersObject,
                'delegationId': self.model.delegationId

            };
            if (self.model.edit) {
                Common.ajaxPost('/Workflow/UpdateWorkflow', params, function (data) {
                    if (!data.success) {

                        if (data.message === 'FileInUse') {
                            Common.alertMsg(Resources.FileInUse);
                        }
                        else if (data.message === 'OriginalFileInUse') {
                            Common.alertMsg(Resources.OriginalFileInUse);
                        }
                        else if (data.message === 'NoWorkflowStepsAdded') {
                            Common.alertMsg(Resources.NoWorkflowStepsAdded);
                        }
                        else if (data.message === 'NotAllUserHasSignatureRegion') {
                            Common.alertMsg(Resources.NotAllUserHasSignatureRegion);
                        }
                        else if (data.message === 'DocumentIsCompleted') {
                            Common.alertMsg(Resources.DocumentIsCompleted);
                        }
                        else if (data.message === 'TransferIsExist') {
                            Common.alertMsg(Resources.TransferIsExist);
                        }
                        else {
                            Common.showScreenErrorMsg(data.message);
                        }
                    }
                    else if (data.success) {
                        self.model.callBack();
                        $('#btnClose').click();
                        Common.showScreenSuccessMsg();
                    }

                }, function () {
                    Common.showScreenErrorMsg();
                });
            }
            else {
                Common.ajaxPost('/Workflow/StartWorkflow', params, function (data) {
                    if (!data.success) {

                        if (data.message === 'FileInUse') {
                            Common.alertMsg(Resources.FileInUse);
                        }
                        else if (data.message === 'OriginalFileInUse') {
                            Common.alertMsg(Resources.OriginalFileInUse);
                        }
                        else if (data.message === 'NoWorkflowStepsAdded') {
                            Common.alertMsg(Resources.NoWorkflowStepsAdded);
                        }
                        else if (data.message === 'NotAllUserHasSignatureRegion') {
                            Common.alertMsg(Resources.NotAllUserHasSignatureRegion);
                        }
                        else if (data.message === 'DocumentIsCompleted') {
                            Common.alertMsg(Resources.DocumentIsCompleted);
                        }
                        else if (data.message === 'TransferIsExist') {
                            Common.alertMsg(Resources.TransferIsExist);
                        }
                        else if (data.message === 'NotAllowedToAddYourSelf') {
                            Common.alertMsg(Resources.NotAllowedToAddYourSelf);
                        }
                        else if (data.message === 'SynchronizUsersData') {
                            Common.alertMsg(Resources.SynchronizUsersData);
                        }
                        else {
                            Common.showScreenErrorMsg(data.message);
                        }
                    }
                    else if (data.success) {
                        $('#WorkflowClose').click();
                        var nodeId = $('[data-inherit="' + TreeNode.Sent + '"]').first().data("id");
                        var redirectTo = '#sent/' + nodeId;

                        if (typeof self.model.callBack === 'function') {
                            self.model.callBack();
                        }
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        $(".close-correspondence").trigger("click");
                        $("[ref=btnCloseTransfer]").trigger("click");

                        //if ($('li .active').attr("data-id") == undefined) {
                        //    window.location.href = "/";
                        //}
                        //else {
                           // window.location.reload();
                       // }

                        //window.location.href = redirectTo;
                        Common.showScreenSuccessMsg();
                        //$('.modal-documents').empty();
                    }

                }, function () {
                    Common.showScreenErrorMsg();
                });
            }


        });

        $("#btnAdd").click(function () {
            $("#formPost").parsley().reset();
            $("#cmbUsers").parsley().validate();
            $("#cmbPurpose").parsley().validate();
            $("#cmbOrder").parsley().validate();
            var obj = [];
            var userObj = $("#cmbUsers").select2('data');
            let val = $("#cmbUsers").val();
            var purposeObj = $("#cmbPurpose").select2('data');
            var purposeVal = $("#cmbPurpose").val();
            obj.order = $('#cmbOrder').val();
            obj.user = val;
            obj.purpose = purposeVal;

            if (obj.user && obj.order && obj.purpose ) {
                if ($("#rowId").val() === "") {

                    let postParams = {
                        "StructureId": userObj[0].id.split('_')[1],
                        'UserId': userObj[0].dataId,
                        'order': obj.order,
                        'PurposeId': purposeObj[0].id,
                        'PurposeForSignature': purposeObj[0].forSignature,
                        'IsCompleted':false
                    }
                    Common.ajaxPost('/WorkflowStep/SaveStep?workflowId=' + self.model.workflowId + '&documentId=' + self.model.documentId + '&transferId=' + self.model.transferId + '&delegationId=' + self.model.delegationId, postParams, function (retVal) {

                        if (!retVal.success) {
                            if (retVal.message === 'NotAllowedToAddYourSelf') {
                                Common.alertMsg(Resources.NotAllowedToAddYourSelf);
                            }
                            else if (retVal.message === 'SynchronizUsersData') {
                                Common.alertMsg(Resources.SynchronizUsersData);
                            }
                            else if (retVal.message === 'UserAlreadyExist') {
                                Common.alertMsg(Resources.UserAlreadyExists);
                            }
                            else {
                                Common.showScreenErrorMsg(retVal.message);
                            }
                        }
                        else if (retVal.success) {
                            InsertUserRow(userObj[0], parseInt(obj.order), purposeObj[0], retVal.data);
                            Common.showScreenSuccessMsg();
                            ClearForm();
                        }

                    })                    
                }
                else {

                    let postParams = {
                        'Id': $("#hdStepId").val(),
                        "StructureId": userObj[0].id.split('_')[1],
                        'UserId': userObj[0].id.split('_')[2],
                        'order': obj.order,
                        'PurposeId': purposeObj[0].id,
                        'PurposeForSignature': purposeObj[0].forSignature,
                        'IsCompleted': false
                    }

                    Common.ajaxPost('/WorkflowStep/UpdateStep?workflowId=' + self.model.workflowId + '&documentId=' + self.model.documentId + '&transferId=' + self.model.transferId + '&delegationId=' + self.model.delegationId, postParams, function (retVal) {

                        if (!retVal.success) {
                            if (retVal.message === 'NotAllowedToAddYourSelf') {
                                Common.alertMsg(Resources.NotAllowedToAddYourSelf);
                            }
                            else if (retVal.message === 'SynchronizUsersData') {
                                Common.alertMsg(Resources.SynchronizUsersData);
                            }
                            else if (retVal.message === 'UserAlreadyExist') {
                                Common.alertMsg(Resources.UserAlreadyExists);
                            }
                            else {
                                Common.showScreenErrorMsg(data.message);
                            }
                        }
                        else if (retVal.success) {
                            Common.showScreenSuccessMsg();
                            UpdateUserRow(userObj[0], parseInt(obj.order), purposeObj[0], self);

                            ClearForm();

                        }

                    })
                    
                }
            }
        });

        $("#btnformClear").click(function () {
            ClearForm();
        });
        var i = 0;
        if (self.model.workflowSteps.length > 0) {           
            self.model.workflowSteps.forEach((element) => {
                var structure = new IdentityService().getFullStructure(element.structureId, window.language);
                var user = new IdentityService().getFullUser(element.userId, window.language);
                var purpose = purposes.filter(function (item) { return item.id == element.purposeId });
                let obj = {
                    'id': element.id,
                    'rowId': element.id,
                    'structureId': element.structureId,
                    'userId': element.userId,
                    'text': structure.name + '/' + user.fullName,
                    'purposeId': element.purposeId,
                    'purposeForSignature': purpose[0].forSignature,
                    'purpose': purpose[0].text,
                    'order': element.order,
                    'isCompleted': element.isCompleted
                }
                disabledRows = self.model.workflowSteps.filter((item) => item.isCompleted == true);

                var usersTable = $("#usersTable").DataTable();
                usersTable.row.add({ 'id': obj.id, 'rowId': i + 1, "structureId": obj.structureId, "userId": obj.userId, "purposeId": obj.purposeId, "purposeForSignature": obj.purposeForSignature, "purpose": obj.purpose, "text": obj.text, "order": obj.order, "isCompleted": obj.isCompleted }).draw();
                i = i + 1;


            })
            allowedOrderToEdit = disabledRows.length + 1;
            UpdateOrderDropDown();

        }

        $("#btnWorkflowSave").on('click', function () {
            if (gUsersObject.length == 0) {
                Common.alertMsg(Resources.NoWorkflowStepsAdded);
                return;
            }
            var params = {
                'WorkflowId': self.model.workflowId,
                "DocumentId": self.model.documentId,
                'TransferId': self.model.transferId,
                'WorkflowUsers': gUsersObject,
                'delegationId':self.model.delegationId
            };
            //if (self.model.edit) {
            //    Common.ajaxPost('/Workflow/Save', params, function (data) {
            //        if (!data.success) {

                        
            //            if (data.message === 'NoWorkflowStepsAdded') {
            //                Common.alertMsg(Resources.NoWorkflowStepsAdded);
            //            }
            //            else if (data.message === 'NotAllUserHasSignatureRegion') {
            //                Common.alertMsg(Resources.NotAllUserHasSignatureRegion);
            //            }
            //            else if (data.message === 'DocumentIsCompleted') {
            //                Common.alertMsg(Resources.DocumentIsCompleted);
            //            }
            //            else if (data.message === 'TransferIsExist') {
            //                Common.alertMsg(Resources.TransferIsExist);
            //            }
            //            else {
            //                Common.showScreenErrorMsg(data.message);
            //            }
            //        }
            //        else if (data.success) {
            //            Common.showScreenSuccessMsg();
            //        }

            //    }, function () {
            //        Common.showScreenErrorMsg();
            //    });
            //}
            //else {
                Common.ajaxPost('/Workflow/Save', params, function (data) {
                    if (!data.success) {

                        if (data.message === 'NoWorkflowStepsAdded') {
                            Common.alertMsg(Resources.NoWorkflowStepsAdded);
                        }
                        else if (data.message === 'NotAllUserHasSignatureRegion') {
                            Common.alertMsg(Resources.NotAllUserHasSignatureRegion);
                        }
                        else if (data.message === 'DocumentIsCompleted') {
                            Common.alertMsg(Resources.DocumentIsCompleted);
                        }
                        else if (data.message === 'TransferIsExist') {
                            Common.alertMsg(Resources.TransferIsExist);
                        }
                        else if (data.message === 'NotAllowedToAddYourSelf') {
                            Common.alertMsg(Resources.NotAllowedToAddYourSelf);
                        }
                        else if (data.message === 'SynchronizUsersData') {
                            Common.alertMsg(Resources.SynchronizUsersData);
                        }
                        else {
                            Common.showScreenErrorMsg(data.message);
                        }
                    }
                    else if (data.success) {                        
                        Common.showScreenSuccessMsg();
                    }

                }, function () {
                    Common.showScreenErrorMsg();
                });
            //}


        });
    }
}
export default { Workflow, WorkflowView };
