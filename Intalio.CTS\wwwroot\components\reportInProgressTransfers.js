﻿import Intalio from './common.js'
import { Categories } from './lookup.js'
class ReportInProgressTransfers extends Intalio.Model
{
    constructor()
    {
        super();
    }
}
var gArabic = /[\u0600-\u06FF]/;
var gfirstTime = true;
var gDataChanged = true;
var gAction = "";
var gFilterChanged = false;
var gFilterStructure = "";
var gFilterFromDate = "";
var gFilterToDate = "";
var gFilterUser = "";
var gFilterOverdue = "";
var gOverdue = false;
var clear = false;
var gReportName = "Transfers InProgress Report";
function buildColumns()
{
    var gridcolumns = [];
    gridcolumns.push({
        title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"], width: "150px",
        render: function (data, type, full, meta)
        {
            var categories = new Categories().get(window.language);
            for (var i = 0; i < categories.length; i++)
            {
                if (categories[i].id === data)
                {
                    return categories[i].text;
                }
            }
            return "";
        }
    });
    gridcolumns.push({ title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": false });
    gridcolumns.push({ title: Resources.Subject, data: "subject", "orderable": true, render: $.fn.dataTable.render.text(), orderSequence: ["asc", "desc"], width: "150px" });
    gridcolumns.push({
        title: Resources.From, "orderable": false,
        "render": function (data, type, full, meta)
        {
            var retValue = "";
            if (full.fromStructure)
            {
                retValue += full.fromStructure;
            }
            if (full.fromUser)
            {
                var user = full.fromUser;
                retValue += retValue !== "" ? "/" + user : user;
            }
            return retValue;
        }
    });
    gridcolumns.push({
        title: Resources.To, "orderable": false,
        "render": function (data, type, full, meta)
        {
            var retValue = "";
            if (full.toStructure)
            {
                retValue += full.toStructure;
            }
            if (full.toUser)
            {
                var user = full.toUser;
                retValue += retValue !== "" ? "/" + user : user;
            }
            return retValue;
        }
    });
    gridcolumns.push({
        title: Resources.TransferDate, data: "transferDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
        render: function (data, type, full, meta)
        {
            return DateConverter.toHijriFormated(full.transferDate, null, window.CalendarType);
        }
    });
    gridcolumns.push({
        title: Resources.OverDue,
        "className": "text-right",
        "autoWidth": false,
        "bAutoWidth": false,
        width: "16px",
        'orderable': false,
        'sortable': false,
        'render': function (data, type, full, meta)
        {
            var html = "<div class='mr-sm'>✖</div>";
            if (full.isOverDue)
            {
                html = "<div class='mr-sm'>✔</div>";
            }
            return "<div id='divLock" + full.id + "' style='display: inline-flex;'>" + html + "</div>";
        }
    });
    return gridcolumns;
}
function exportTable()
{
    if (gfirstTime || clear || gOverdue)
    {
        if ($.fn.dataTable.isDataTable("#grdItemsHidden"))
        {
            if (clear)
            {
                clear = false;
            }
            $("#grdItemsHidden").DataTable().clear().destroy();
        }
        $("#grdItemsHidden").on('draw.dt',
            function ()
            {

                $('#grdItems tbody tr td').each(function ()
                {
                    this.setAttribute('title', $(this).text());
                });

                if (gAction === "print")
                {
                    $("#grdItemsHidden_wrapper .buttons-print").click();
                    addAuditAction(gReportName, AuditTrailAction.PrintReport);
                } else if (gAction === "excel")
                {
                    $("#grdItemsHidden_wrapper .buttons-excel").click();
                    addAuditAction(gReportName, AuditTrailAction.ExportReportToExcel);
                } else if (gAction === "pdf")
                {
                    $("#grdItemsHidden_wrapper .buttons-pdf").click();
                    addAuditAction(gReportName, AuditTrailAction.ExportReportToPdf);
                }
                gAction = "";
                gfirstTime = false;
            }).DataTable({
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: -1,
                "ajax": {
                    "timeout": 30000,
                    "url": "/Report/ListInProgressTransfersRetrievingAllData",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d)
                    {
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.StructureIds = gFilterChanged ? gFilterStructure : "";
                        d.UserIds = gFilterChanged ? gFilterUser : "";
                        d.FromDate = gFilterChanged ? gFilterFromDate : "01/01/" + new Date().getFullYear();
                        d.ToDate = gFilterChanged ? gFilterToDate : moment().format('DD/MM/YYYY');
                        d.Overdue = gFilterChanged ? gFilterOverdue : false;
                        return d;
                    }
                },
                "order": [],
                "columns": buildColumns(),
                "fnInitComplete": function (settings, json)
                {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons"B>trpi',
                buttons: [{
                    extend: 'print',
                    title: Resources.InProgressTransfers,
                    text: Resources.InProgressTransfers,
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5, 6]
                    },
                    customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportinprogresstransfers.print
                },
                {
                    extend: 'excelHtml5',
                    title: Resources.InProgressTransfers,
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5, 6]
                    },
                    customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportinprogresstransfers.excelHTML5
                },
                {
                    extend: 'pdfHtml5',
                    title: Resources.InProgressTransfers,
                    exportOptions: {
                        //columns: [0, 1, 2, 3, 4, 5, 6]
                        columns: [6, 5, 4, 3, 2, 1, 0]
                    },
                    customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportinprogresstransfers.pdfHTML5
                }]
            });

    } else
    {
        if (gDataChanged)
        {
            $("#grdItemsHidden").DataTable().ajax.reload();
        } else
        {
            if (gAction === "print")
            {
                $("#grdItemsHidden_wrapper .buttons-print").click();
                addAuditAction(gReportName, AuditTrailAction.PrintReport);
            } else if (gAction === "excel")
            {
                $("#grdItemsHidden_wrapper .buttons-excel").click();
                addAuditAction(gReportName, AuditTrailAction.ExportReportToExcel);
            } else if (gAction === "pdf")
            {
                $("#grdItemsHidden_wrapper .buttons-pdf").click();
                addAuditAction(gReportName, AuditTrailAction.ExportReportToPdf);
            }
        }
    }
    gDataChanged = false;
}
function addAuditAction(actionName, actionId)
{
    var originalValue = "";
    if (gFilterFromDate || gFilterToDate || gFilterStructure || gFilterUser || gFilterOverdue)
    {
        var structureNames = "";
        var userNames = "";
        var selectedStructures = gFilterStructure === null ? [] : $("#cmbStructure").select2('data');
        var selectedUsers = gFilterUser === null ? [] : $("#cmbUser").select2('data');
        for (var i = 0; i < selectedStructures.length; i++)
        {
            structureNames += selectedStructures[i].text + ", ";
        }
        structureNames = structureNames.trim();
        structureNames = structureNames.slice(0, -1);

        for (var i = 0; i < selectedUsers.length; i++)
        {
            userNames += " " + selectedUsers[i].text + ",";
        }
        userNames = userNames.trim();
        userNames = userNames.slice(0, -1);

        originalValue = JSON.stringify({
            'structures': structureNames,
            'users': userNames,
            'fromDate': gFilterFromDate === null ? "" : gFilterFromDate,
            'toDate': gFilterToDate === null ? "" : gFilterToDate,
            'overdue': gFilterOverdue === null ? "" : gFilterOverdue
        });
    }
    var model = {
        'actionId': actionId,
        'note': actionName,
        'originalValue': originalValue
    }
    Common.ajaxPost('/ActivityLog/AddFullAction', model, null, function (msg)
    {
        console.log(msg);
    });

}
function updateGlobalFilters()
{
    gFilterStructure = ($("#cmbStructure").val() !== null && typeof $("#cmbStructure").val() !== "undefined" ? $("#cmbStructure").val().toString() : "");
    gFilterUser = ($("#cmbUser").val() !== null && typeof $("#cmbUser").val() !== "undefined" ? $("#cmbUser").val().toString() : "");
    gFilterFromDate = ($("#fromDate").val() !== "" && typeof $("#fromDate").val() !== "undefined" ? $("#fromDate").val() : "01/01/" + new Date().getFullYear());
    gFilterToDate = ($("#toDate").val() !== "" && typeof $("#toDate").val() !== "undefined" ? $("#toDate").val() : moment().format('DD/MM/YYYY'));
    gFilterOverdue = $("#chkOverdue").is(":checked");
}
function createUsersSelect2()
{
    var headers = {};
    var url = window.IdentityUrl + '/api/SearchUsers';
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    $("#cmbUser").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#userContainer'),
        multiple: true,
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "Get",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term)
            {
                //return { "text": "", "language": window.language };
                return { "text": term.term ? term.term : "", "language": window.language };
            },
            processResults: function (data, term)
            {
                return {
                    results: userDataForSelect2(data, term)
                };
            }
        }
    });
    $("#cmbUser").val('').trigger('change');
}
function userDataForSelect2(data, term)
{
    var termSearch = term.term ? term.term : "";
    var structures = $('#cmbStructure').val();
    var retVal = [];
    $.each(data, function (key, val) {
        if (structures !== "" && structures !== null &&
            !((val.structureIds != null && val.structureIds.some(r => structures.indexOf(String(r)) >= 0)) ||
                structures.includes(val.defaultStructureId))) {
            return;
        }
        var fullName = val.fullName;
        if (window.language != 'en') {
            fullName = getFullNameByLangauge(val);
            fullName = fullName.trim() == "" ? val.fullName : fullName;;
        }
        var allNames = getFullNameInAllLangauge(val);
        if (allNames.length == 0) allNames.push(fullName);
        if (termSearch != "" &&
            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
            return;
        }
        let isExist = retVal.some(function (usr) { return usr.id === val.id; });
        if (!isExist) {
            var item = {};
            item.id = val.id;
            item.text = fullName;
            item.isStructure = false;
            item.dataId = val.id;
            retVal.push(item);
        }
    });
    return retVal;
}
function createStructuresSelect2()
{
    var headers = {};
    var url = window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes';
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    $("#cmbStructure").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#structureContainer'),
        multiple: true,
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "POST",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term)
            {
                return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr] };
            },
            processResults: function (data)
            {
                return {
                    results: structureDataForSelect2(data)
                };
            }
        }
    });
    $("#cmbStructure").val('').trigger('change');
}
function getStructureName(data)
{
    var structureName = data.name;
    if (data.attributes != null && data.attributes.length > 0)
    {
        var attributeLang = $.grep(data.attributes, function (e)
        {
            return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
        });
        if (attributeLang.length > 0)
        {
            structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
        }
    }
    return structureName;
}
function structureDataForSelect2(data)
{
    var retVal = [];
    if (typeof data !== 'undefined' && data.items)
    {
        for (var i = 0; i < data.items.length; i++)
        {
            retVal.push({
                id: data.items[i].id,
                text: getStructureName(data.items[i])
            });
        }
    } else if (data)
    {
        for (var i = 0; i < data.length; i++)
        {
            retVal.push({
                id: data[i].id,
                text: getStructureName(data[i])
            });
        }
    }
    return retVal;
}
class ReportInProgressTransfersView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "reportinprogresstransfers", model);
    }
    render()
    {
        var self = this;
        gfirstTime = true;
        gDataChanged = true;
        gAction = "";
        gFilterChanged = false;
        gFilterStructure = "";
        gFilterFromDate = "";
        gFilterToDate = "";
        gFilterUser = "";
        gFilterOverdue = "";
        gOverdue = false;
        clear = false;
        gReportName = "Transfers InProgress Report";
        $.fn.select2.defaults.set("theme", "bootstrap");
        var clickedSearch = false;
        $('#collapseIcon').click(function ()
        {
            $('#collapseIcon').empty();
            if (clickedSearch)
            {
                $('#collapseIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
                $('#collapsePanel').attr('class', '');
                clickedSearch = false;
            } else
            {
                $('#collapseIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
                $('#collapsePanel').attr('class', '');
                $('#collapsePanel').addClass('panel-body panel-collapse collapse');
                clickedSearch = true;
            }
        });
        $("#btnSearch").on('click', function ()
        {
            $("#grdItems").DataTable().ajax.reload();
            gDataChanged = true;
            gFilterChanged = true;
            updateGlobalFilters();
        });
        $("#btnClear").on('click', function ()
        {
            if (gOverdue)
            {
                clear = true;
            } else
            {
                clear = false;
            }
            $("#cmbStructure").val('').trigger('change');
            $("#cmbUser").val('').trigger('change');
            $("#chkOverdue").attr("checked", false);
            fromDate.clear();
            toDate.clear();
            $("#grdItems").DataTable().ajax.reload();
            gDataChanged = true;
            gFilterChanged = true;
            updateGlobalFilters();
        });
        var fromDate = $('#fromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    maxDate: jQuery('#toDate').val() && jQuery('#toDate').val() !== "" ? jQuery('#toDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#fromDate_img").click(function ()
        {
            fromDate.toggle();
        });
        var toDate = $('#toDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    minDate: jQuery('#fromDate').val() && jQuery('#fromDate').val() !== "" ? jQuery('#fromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#toDate_img").click(function ()
        {
            toDate.toggle();
        });
        createUsersSelect2();
        createStructuresSelect2();
        $('#fromDate').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnClear').focus();
                }
                else
                {
                    $('#toDate').focus();
                }
            }
        });
        $('#btnClear').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnSearch').focus();
                }
                else
                {
                    $('#fromDate').focus();
                }
            }
        });

        $("#cmbStructure").on("select2:select", function (e)
        {
            $("#cmbUser").val('').trigger('change');
            createUsersSelect2();
            
        });
        Common.gridCommon();
        let table = $("#grdItems").on('draw.dt',
            function ()
            {
                $('#grdItems tbody tr td').each(function ()
                {
                    this.setAttribute('title', $(this).text());
                });
            }).DataTable({
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/Report/ListInProgressTransfers",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d)
                    {
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.StructureIds = $("#cmbStructure").val() !== null && typeof $("#cmbStructure").val() !== "undefined" ? $("#cmbStructure").val().toString() : "";
                        d.UserIds = $("#cmbUser").val() !== null && typeof $("#cmbUser").val() !== "undefined" ? $("#cmbUser").val().toString() : "";
                        d.FromDate = $("#fromDate").val() !== "" && typeof $("#fromDate").val() !== "undefined" ? $("#fromDate").val() : "01/01/" + new Date().getFullYear();
                        d.ToDate = $("#toDate").val() !== "" && typeof $("#toDate").val() !== "undefined" ? $("#toDate").val() : moment().format('DD/MM/YYYY');
                        d.Overdue = $("#chkOverdue").is(":checked");
                        return d;
                    }
                },
                "order": [],
                "columns": [
                    {
                        title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"], width: "150px",
                        render: function (data, type, full, meta)
                        {
                            var categories = new Categories().get(window.language);
                            for (var i = 0; i < categories.length; i++)
                            {
                                if (categories[i].id === data)
                                {
                                    return categories[i].text;
                                }
                            }
                            return "";
                        }
                    },
                    { title: Resources.ReferenceNumber, data: "referenceNumber", "orderable": false, width: "150px" },
                    { title: Resources.Subject, data: "subject", "orderable": true, orderSequence: ["asc", "desc"], width: "150px" },
                    {
                        title: Resources.From, "orderable": false,
                        "render": function (data, type, full, meta)
                        {
                            var retValue = "";
                            if (full.fromStructure)
                            {
                                retValue += full.fromStructure;
                            }
                            if (full.fromUser)
                            {
                                var user = full.fromUser;
                                retValue += retValue !== "" ? "/" + user : user;
                            }
                            return retValue;
                        }
                    },
                    {
                        title: Resources.To, "orderable": false,
                        "render": function (data, type, full, meta)
                        {
                            var retValue = "";
                            if (full.toStructure)
                            {
                                retValue += full.toStructure;
                            }
                            if (full.toUser)
                            {
                                var user = full.toUser;
                                retValue += retValue !== "" ? "/" + user : user;
                            }
                            return retValue;
                        }
                    },
                    {
                        title: Resources.TransferDate, data: "transferDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                        render: function (data, type, full, meta)
                        {
                            return DateConverter.toHijriFormated(full.transferDate, null, window.CalendarType);
                        }
                    },
                    {
                        "className": "text-right",
                        "autoWidth": false,
                        "bAutoWidth": false,
                        width: "10px",
                        'orderable': false,
                        'sortable': false,
                        'render': function (data, type, full, meta)
                        {
                            var html = "";
                            if ($("#chkOverdue").is(":checked") === false && full.isOverDue)
                            {
                                gOverdue = false;
                                html += "<div class='mr-sm' title='" + Resources.OverDue + "'><i class='fa fa-clock-o fa-lg text-danger'></i></div>&nbsp;";
                                return "<div id='divOverDue_" + full.id + "' style='display: inline-flex;'>" + html + "</div>";
                            } else if ($("#chkOverdue").is(":checked") === true && full.isOverDue)
                            {
                                gOverdue = true;
                                return "";
                            } else
                            {
                                return "";
                            }
                        }
                    }
                ],
                "fnInitComplete": function (settings, json)
                {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons"B>trpi',
                buttons: [
                    {
                        className: 'btn-sm btn-primary',
                        text: Resources.Print,
                        action: function (e, dt, node, config)
                        {
                            gAction = "print";
                            exportTable();
                        }
                    }, {
                        className: 'btn-sm btn-primary',
                        text: "Excel",
                        action: function (e, dt, node, config)
                        {
                            gAction = "excel";
                            exportTable();
                        }
                    }, {
                        className: 'btn-sm btn-primary',
                        text: "Pdf",
                        action: function (e, dt, node, config)
                        {
                            gAction = "pdf";
                            exportTable();
                        }
                    }
                ]
            });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
    }
}
export default { ReportInProgressTransfers, ReportInProgressTransfersView };
