﻿using Intalio.CTS.Core.API;
using Intalio.CTS.Core.Model;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Intalio.CTS.Controllers
{
    [Route("CTS/[controller]/[action]")]
    public class StructureController : BaseController
    {
        [HttpPost]
        public IActionResult UpdateLoggedInStrucure(long structureId, long oldStrutureId)
        {
            try
            {
                var NewstructureUser = ManageUser.GetUserStructureByStructureId(structureId, UserId);
                if(NewstructureUser != null )
                    ManageUser.UpdateLoggedInStructure(UserId, structureId, true);
                else
                    ManageUser.InsertLoggedInStructure(UserId, structureId, true);

                var OldstructureUser = ManageUser.GetUserStructureByStructureId(oldStrutureId, UserId);
                if (OldstructureUser != null)
                    ManageUser.UpdateLoggedInStructure(UserId, oldStrutureId, false);
                else
                    ManageUser.InsertLoggedInStructure(UserId, oldStrutureId, false);
                return Ok(RoleId);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        public IActionResult GetLoggedInStrucure()
        {
            try
            {
                return Ok(ManageUser.GetLoggedInStructure(UserId));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        public IActionResult ListStructureUsers(int draw, int start, int length, string search)
        {
            try
            {
                var data = ManageUserStructure.GetStructureUsers(start, length, StructureId, search);
                return Ok(new { draw = draw, recordsTotal = data.totalCount, recordsFiltered = data.totalCount, data = data.Item2 });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        public IActionResult ListStructureUsersAllData(int draw, string search)
        {
            try
            {
                var data = ManageUserStructure.GetStructureUsers(StructureId, search);
                return Ok(new { draw = draw, recordsTotal = data.totalCount, recordsFiltered = data.totalCount, data = data.Item2 });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        public IActionResult ListAllStructureUsers(int draw, int start, int length, string search, long[] structureIds)
        {
            try
            {
                var data = ManageUserStructure.GetAllStructureUsers(start, length, search, structureIds);
                return Ok(new { draw = draw, recordsTotal = data.totalCount, recordsFiltered = data.totalCount, data = data.Item2 });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        public IActionResult ListAllStructureUsersAllData(int draw, string search, long[] structureIds)
        {
            try
            {
                var data = ManageUserStructure.GetAllStructureUsers(search, structureIds);
                return Ok(new { draw = draw, recordsTotal = data.totalCount, recordsFiltered = data.totalCount, data = data.Item2 });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        public IActionResult GetAllUserStructures(string searchValue)
        {
            var result = ManageUserStructure.GetAllUserStructures(searchValue);
            return Ok(result);
        }

        [HttpGet]
        public IActionResult GetAllUserStructuresForTree()
        {
            var result = ManageUserStructure.GetAllUserStructuresForTree();
            return Ok(result);
        }
        
        [HttpGet]
        public IActionResult GetAllUserStructuresData()
        {
            var result = ManageUserStructure.GetAllUserStructuresData();
            return Ok(result);
        }

        [HttpGet]
        public IActionResult GetDepartmentByStructureByid(long Id)
        {
            var result = ManageUserStructure.GetDepartmentByStructureByid(Id);
            return Ok(result);
        }

        [HttpGet]
        public async Task<IActionResult> GetStructure(long id)
        {
            var result = Intalio.CTS.Core.API.ManageStructure.GetStructure(id, Language);
            return Ok(result);
        }

    }
}
