import Intalio from './common.js'

var gLocked = false;
function generateLetter(generateLetterOptions, generateReceivingEntity, generateCarbonCopy)
{
    var params = generateLetterOptions.params;
    params.generateReceivingEntity = generateReceivingEntity;
    params.generateCarbonCopy = generateCarbonCopy;
    Common.ajaxGet(generateLetterOptions.url, params, function (response)
    {
        gLocked = false;
        $('#btnGenerateLetter').button('reset');
        $('#btnPreviewGeneratedLetter').removeAttr('disabled');
        $("#btnGenerateLetterClose").removeAttr('disabled');
        $(".generateLetterModalClose").removeAttr('disabled');
        if (typeof response !== "undefined")
        {
            if (typeof generateLetterOptions.errorCallback === 'function')
            {
                generateLetterOptions.errorCallback(response);
            }
            else
            {
                Common.showScreenErrorMsg();
            }
        }
        else
        {
            if (typeof generateLetterOptions.callback === 'function')
            {
                generateLetterOptions.callback(response);
            }
        }
    }, function (msg)
    {
        gLocked = false;
        $('#btnGenerateLetter').button('reset');
        $('#btnPreviewGeneratedLetter').removeAttr('disabled');
        $("#btnGenerateLetterClose").removeAttr('disabled');
        $(".generateLetterModalClose").removeAttr('disabled');
        Common.showScreenErrorMsg();
    }, false, '', null);
}
function generateLetterValidation(generateLetterOptions, generateReceivingEntity, generateCarbonCopy)
{
    var params = generateLetterOptions.params;
    params.generateReceivingEntity = generateReceivingEntity;
    params.generateCarbonCopy = generateCarbonCopy;
    Common.ajaxGet("/Attachment/GenerateLetterValidation", params, function (response)
    {
        gLocked = false;
        $('#btnPreviewGeneratedLetter').button('reset');
        $('#btnGenerateLetter').removeAttr('disabled');
        $("#btnGenerateLetterClose").removeAttr('disabled');
        $(".generateLetterModalClose").removeAttr('disabled');
        if (response === "OriginalFileInUse")
        {
            setTimeout(function ()
            {
                Common.alertMsg(Resources.OriginalFileInUse);
            }, 400);
        } else if (response === "GenerateLetterNotComplete")
        {
            setTimeout(function ()
            {
                Common.alertMsg(Resources.GenerateLetterNotComplete);
            }, 400);
        } else if (response === "SelectReceivingEntityOrCarbonCopy")
        {
            setTimeout(function ()
            {
                Common.alertMsg(Resources.SelectReceivingEntityOrCarbonCopy);
            }, 400);
        } else if (response === "AtLeastOneReceivingEntityOrCarbonCopy")
        {
            setTimeout(function ()
            {
                Common.alertMsg(Resources.AtLeastOneReceivingEntityOrCarbonCopy);
            }, 400);
        } else
        {
            let url = '/Attachment/PreviewGenerateLetter?id=' + generateLetterOptions.params.id + "&generateReceivingEntity=" + generateReceivingEntity + "&generateCarbonCopy=" + generateCarbonCopy + "&delegationId=" + generateLetterOptions.params.delegationId;
            window.open(url, '_blank');
        }
    }, function (msg)
    {
        gLocked = false;
        $('#btnPreviewGeneratedLetter').button('reset');
        $('#btnGenerateLetter').removeAttr('disabled');
        $("#btnGenerateLetterClose").removeAttr('disabled');
        $(".generateLetterModalClose").removeAttr('disabled');
        Common.showScreenErrorMsg();
    }, false, '', null);
}
class GenerateLetterView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "generateletter", model);
    }
    render(generateLetterOptions)
    {
        $('#btnGenerateLetterClose').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnGenerateLetter').focus();
                }
                else
                {
                    $('#fileOriginalMail').focus();
                }
            }
        });
        $("#btnGenerateLetter").on('click', function ()
        {
            if (!gLocked)
            {
                let generateReceivingEntity = $('#chkGenerateLetterReceivingEntities').prop('checked');
                let generateCarbonCopy = $('#chkGenerateLetterCCed').prop('checked');
                if (!generateReceivingEntity && !generateCarbonCopy)
                {
                    gLocked = false;
                    Common.alertMsg(Resources.SelectReceivingEntityOrCarbonCopy);
                } else
                {
                    if (window.EnableConfirmationMessage === "True")
                    {
                        Common.showConfirmMsg(Resources.ProceedConfirmation, function ()
                        {
                            try
                            {
                                gLocked = true;
                                $("#btnGenerateLetter").button('loading');
                                $('#btnPreviewGeneratedLetter').attr('disabled', 'disabled');
                                $('#btnGenerateLetterClose').attr('disabled', 'disabled');
                                $(".generateLetterModalClose").attr('disabled', 'disabled');
                                generateLetter(generateLetterOptions, generateReceivingEntity, generateCarbonCopy);
                            } catch (e)
                            {
                                gLocked = false;
                            }
                        });
                    } else
                    {
                        try
                        {
                            gLocked = true;
                            $("#btnGenerateLetter").button('loading');
                            $('#btnPreviewGeneratedLetter').attr('disabled', 'disabled');
                            $('#btnGenerateLetterClose').attr('disabled', 'disabled');
                            $(".generateLetterModalClose").attr('disabled', 'disabled');
                            generateLetter(generateLetterOptions, generateReceivingEntity, generateCarbonCopy);
                        } catch (e)
                        {
                            gLocked = false;
                        }
                    }
                }
            }
        });
        $("#btnPreviewGeneratedLetter").on('click', function ()
        {
            if (!gLocked)
            {
                try
                {
                    let generateReceivingEntity = $('#chkGenerateLetterReceivingEntities').prop('checked');
                    let generateCarbonCopy = $('#chkGenerateLetterCCed').prop('checked');

                    if (!generateReceivingEntity && !generateCarbonCopy)
                    {
                        gLocked = false;
                        Common.alertMsg(Resources.SelectReceivingEntityOrCarbonCopy);
                    } else
                    {
                        try
                        {
                            gLocked = true;
                            $("#btnPreviewGeneratedLetter").button('loading');
                            $('#btnGenerateLetter').attr('disabled', 'disabled');
                            $('#btnGenerateLetterClose').attr('disabled', 'disabled');
                            $(".generateLetterModalClose").attr('disabled', 'disabled');
                            generateLetterValidation(generateLetterOptions, generateReceivingEntity, generateCarbonCopy);
                        } catch (e)
                        {
                            gLocked = false;
                        }
                    }
                } catch (e)
                {
                    gLocked = false;
                }
            }
        });
        $("#btnGenerateLetterClose, .generateLetterModalClose").on('click', function ()
        {
            $('#modalGenerateLetter').modal('hide');
        });
    }
}
export default { GenerateLetterView };
