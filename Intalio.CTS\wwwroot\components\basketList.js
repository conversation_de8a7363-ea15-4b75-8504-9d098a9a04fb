﻿import Intalio from './common.js'
import DocumentDetails from './documentDetails.js'
import AddBasketCorrespondenceIndex from './addBasketCorrespondenceIndex.js'
import DocumentSearch from './search.js'
import AddBasketIndex from './addBasketIndex.js'
import { Categories } from './lookup.js'
import ReportCorrespondenceDetailExport from './reportCorrespondenceDetailExport.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
class DocumentBasket extends Intalio.Model
{
    constructor()
    {
        super();
        this.categories = null;
        this.statuses = null;
    }
}
function openLinkWindow(basketId) {
    
        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        var wrapper = $(".modal-window");
    var modelIndex = new AddBasketCorrespondenceIndex.AddBasketCorrespondenceIndex();
    modelIndex.basketId = basketId;
        var addBasketCorrespondenceIndexView = new AddBasketCorrespondenceIndex.AddBasketCorrespondenceView(wrapper, modelIndex);
        addBasketCorrespondenceIndexView.render();
        var wrapper = $("#linkSearchDiv");
        let model = new DocumentSearch.DocumentSearch();
        model.categories = new Categories().get(window.language).filter(item => item.id != window.FollowUpCategory);
        model.statuses = statuses.filter(function (el) { return el.text !== Resources.Draft; });
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.delegationUsers = [];
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.importances = new CoreComponents.Lookup.Importances().get(window.language);
        model.fromLink = true;
        let view = new DocumentSearch.DocumentSearchView(wrapper, model);
        view.render();
        $('#modalBasketCorrespondenceTitle').html(Resources.New);
        $('#modalBasketCorrespondence').modal("show");
        $('#modalBasketCorrespondence').off("hidden.bs.modal");
        $('#modalBasketCorrespondence').off("shown.bs.modal");
        $('#modalBasketCorrespondence').on('shown.bs.modal', function () {
            $('#searchContainerDiv').find("h3").hide();
            setTimeout(function () { $('#cmbSearchFilterCategory').focus(); }, 200);
        });
        $('#modalBasketCorrespondence').on('hidden.bs.modal', function () {
            $('.search').fadeIn();
            $('.gridResult').hide();
            $('.btn-scroll').hide();
            GridCommon.Clear("grdSearchItems");
            $("#btnSearchFilterClear").trigger('click');
            $('#modalBasketCorrespondence').remove();
            swal.close();
            //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
            //    $('body').addClass('modal-open');
            //}
        });
}
function openEditBasketWindow(row) {
    var wrapper = $(".modal-window");
    var modelIndex = new AddBasketIndex.AddBasketIndex();
    modelIndex.itemsNames = [];
    var grpIndex = new AddBasketIndex.AddBasketIndexView(wrapper, modelIndex);
    grpIndex.render();

    $("#selectedItemsTable").DataTable().clear().draw();
    if (row) {
        $('#modalBasketTitle').html(Resources.Edit);
        $('#hdId').val(row.id);
        $('#txtName').val(row.name);
        $('#txtNameAr').val(row.nameAr);
        $('#txtNameFr').val(row.nameFr);
        var lan = "ltr";
        if (window.language == "ar") {
            lan = "rtl";
        }
        var lstPrivacies = new CoreComponents.Lookup.Privacies().get(window.language);
        var privacyName = '';
        var rolename = "";
        if (row.basketUsersModel.length > 0) {
            var itemsNames = [];
            for (var i = 0; i < row.basketUsersModel.length; i++) {
                if (row.basketUsersModel[i].roleId == "1") {
                    rolename = "ReadOnly";
                } else if (row.basketUsersModel[i].roleId == "3")
                {
                    rolename = "Administrator";
                }
                else {
                    rolename = "Edit";
                }
                for (var j = 0; j < lstPrivacies.length; j++) {
                    if (Number(row.basketUsersModel[i].privacyId) == lstPrivacies[j].id) {
                        privacyName = lstPrivacies[j].text;
                        break;
                    }
                }
                var userName = getUserFullNameById(row.basketUsersModel[i].userId)
                if (userName!=null) {
                    itemsNames.push({
                        'UserId': row.basketUsersModel[i].userId,
                        'name': '<span dir="' + lan + '">' + userName + '</span>' , 'nameId': row.basketUsersModel[i].userId,
                        'role': '<span dir="' + lan + '">' + Resources[rolename] + '</span>',
                        'roleId': row.basketUsersModel[i].roleId,
                        'isUser': false,
                        'privacy': '<span dir="' + lan + '">' + privacyName + '</span>',
                        'privacyId': row.basketUsersModel[i].privacyId
                    });
                }
            }
            $('#cmbIndexBasket').val(2).text;
            $('#cmbIndexBasket').trigger('change');
            $("#selectedItemsTable").DataTable().rows.add(itemsNames).draw();
            grpIndex.setData(itemsNames);
            modelIndex.itemsNames = itemsNames;
        }
    } else {
        $('#modalBasketTitle').html(Resources.New);
    }
    $('#modalBasket').addClass('modalScroll');
    $('#modalBasket').modal('show');
    $("#modalBasket").off("hidden.bs.modal");
    $("#modalBasket").off("shown.bs.modal");
    $('#modalBasket').on('shown.bs.modal', function () {
        document.getElementById('txtName').focus();
    });
    $('#modalBasket').on('hidden.bs.modal', function () {
        $('#formBasketPost').parsley().reset();
        document.getElementById('hdId').value = null;
        document.getElementById('txtName').value = null;
        document.getElementById('txtNameAr').value = null;
        document.getElementById('txtNameFr').value = null;
        document.getElementById("chkAllItems").checked = false;
        $('#indexBasketContainer').show();
        swal.close();
        modelIndex.itemsNames = [];
        $("#modalBasket").remove();
        swal.close();
    });
}

function format(row)
{
    return '<table style="width:100%" cellspacing="0" border="0">' + buildColumnsDetails(row) + '</table>';
}
function openDocument(id, basketId)
{
    var params = { id: id, basketId: basketId };
    Common.ajaxGet('/Document/GetDocument', params, function (response)
    {
        gLocked = false;

        Common.setActiveSidebarMenu("liBasket" + basketId);
        $(".delegation").removeClass("active");
       
        var wrapper = $(".modal-documents");
        var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
        linkedCorrespondenceModel.reference = response.referenceNumber;
        linkedCorrespondenceModel.subject = response.subject;
        linkedCorrespondenceModel.documentId = response.id;
        var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
        linkedCorrespondenceDocument.render();

        var model = new DocumentDetails.DocumentDetails();
        model.documentId = response.id;
        model.basketId = basketId;
        model.referenceNumber = response.referenceNumber;
        model.categoryName = response.categoryName;
        model.statusId = response.status;
        model.documentModel = response;
        model.readonly = true;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.showMyTransfer = false;
        model.attachmentId = response.attachmentId;
        model.formData = (response.formData || "") !== "" ? eval("(" + response.formData + ")") : null;
        model.formDesigner = (response.formDesigner || "") !== "" ? JSON.parse(response.formDesigner) : null;
        model.formDesignerTranslation = (response.formDesignerTranslation || "") !== "" ? JSON.parse(response.formDesignerTranslation) : null;
        model.showBackButton = false;
        model.isModal = true;
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var tabs = securityMatrix.SecurityNodes[window.basketnode].SecurityCategories[response.categoryId].Tabs;
        model.tabs = $.grep(tabs, function (element, index)
        {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                !element.Name.includes("BasketDocument") && !element.Name.includes("nonArchivedAttachments");
        });
        model.tabsWithStatic = tabs;
        model.tabsActions = securityMatrix.SecurityNodes[window.basketnode].SecurityCategories[response.categoryId].SecurityTabs;
    
        wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);
     
        var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
        view.render();
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
            $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
            if ($(this).data("remove") != true)
                return;
            $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
            swal.close();
        });
        $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

        $(document).off('click', '.btn-back');
        $(document).on('click', '.btn-back', function ()
        {
            $("#gridContainerDiv").show();
            view.remove();
            $(".toRemove").remove();
        });
        $(document).off('click', '.btn-export');
        $(document).on('click', '.btn-export', function ()
        {
            var wrapper = $(".modal-window");
            var model = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExport();
            model.documentId = response.id;
            var reportCorrespondenceDetailExportView = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExportView(wrapper, model);
            reportCorrespondenceDetailExportView.render();

            $("#modalReportCorrespondenceDetailExport").off("hidden.bs.modal");
            $("#modalReportCorrespondenceDetailExport").off("shown.bs.modal");
            $("#modalReportCorrespondenceDetailExport").on('shown.bs.modal', function ()
            { });
            $("#modalReportCorrespondenceDetailExport").on('hidden.bs.modal', function ()
            { });
            $("#modalReportCorrespondenceDetailExport").modal("show");
        });
    }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
}
function buildFilters()
{
    var filtersJson = '["ReferenceNumber","FromDate","ToDate","Category","Subject"]';
    var filters = JSON.parse(filtersJson);

    if (filters.length > 0)
    {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++)
        {
            var filter = filters[i];
            switch (filter)
            {
                case "ReferenceNumber":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
                        '<input type="text" id="txtFilterMyRequestsReferenceNumber" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "FromDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label><div class="input-group date">' +
                        '<input id="filterMyRequestsFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterMyRequestsFromDateError">' +
                        '<span class="input-group-addon" id="filterMyRequestsFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterMyRequestsFromDateError"></div></div></div></div>'
                    break;
                case "ToDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label><div class="input-group date">' +
                        '<input id="filterMyRequestsToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterMyRequestsToDateError">' +
                        '<span class="input-group-addon" id="filterMyRequestsToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterMyRequestsToDateError"></div></div></div></div>';
                    break;
                case "Subject":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterMyRequestsSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Category":
                    clearFix += 3;
                    var categories = new Categories().get(window.language);
                    var div = '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="categoryFilterMyRequestsContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterMyRequestsCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterMyRequestsCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++)
                    {
                        var categoryId = categories[j].id;
                        if (categoryId === 4 || categoryId === 5 || categoryId === 6 || categoryId === 8) {
                            continue;
                        }

                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterMyRequestsCategoryError"></div></div></div>';
                    html += div;
                    break;
                case "OverDue":
                    clearFix += 2;
                    html += '<div class="col-lg-2 col-md-2 col-sm-6 col-xs-6"><div class="form-group"><label class="control-label"></label><div class="checkbox c-checkbox">' +
                        '<label><input tabindex="' + i + '" type="checkbox" id="chkFilterMyRequestsOverdue" name="Overdue"><span class="fa fa-check"></span>' + Resources.OverDue + '</label></div></div></div>';
                    break;
            }
            if (clearFix === 12 || clearFix === 11)
            {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 list-action-filter"><button id="btnFilterMyRequestsSearch" tabindex="5" type="button" class="btn btn-primary">' + Resources.Search + '</button>' +
            ' <button id="btnFilterMyRequestsClear" tabindex="6" type="button" class="btn btn-clear">' + Resources.Clear + '</button></div>';
        html += '</div>';
        $('#filtersContainer').html(html);

        $(document).ready(function () {
            var clickedSearch = false; 

            $('#collapseMyRequestsIcon').empty().append('<i class="fa fa-angle-down fa-lg"></i>');
            $('#collapseMyRequestsPanel').addClass('panel-body panel-collapse collapse');

            $('#collapseMyRequestsIcon').click(function () {
                $('#collapseMyRequestsIcon').empty();

                if (clickedSearch) {
                    $('#collapseMyRequestsIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
                    $('#collapseMyRequestsPanel').removeClass('collapse').addClass('collapse in');
                } else {
                    $('#collapseMyRequestsIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
                    $('#collapseMyRequestsPanel').removeClass('collapse in').addClass('collapse');
                }

                clickedSearch = !clickedSearch;
            });
        });

        $("#btnFilterMyRequestsSearch").on('click', function ()
        {
            GridCommon.Refresh(gTableName);
        });
        $("#btnFilterMyRequestsClear").on('click', function ()
        {
            $("#cmbFilterMyRequestsCategory").val('').trigger('change');
            $("#cmbFilterMyRequestsStatus").val('').trigger('change');
            $("#txtFilterMyRequestsReferenceNumber").val('');
            $("#txtFilterMyRequestsSubject").val('');
            $("#cmbFilterMyRequestsStatus").val('').trigger('change');
            $("#chkFilterMyRequestsOverdue").prop("checked", false);
            fromDate.clear();
            toDate.clear();
            GridCommon.Refresh(gTableName);
        });
        $('#cmbFilterMyRequestsCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterMyRequestsContainer')
        });
        $("#cmbFilterMyRequestsCategory").val('').trigger('change');
        $('#cmbFilterMyRequestsStatus').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#statusFilterMyRequestsContainer')
        });
        $("#cmbFilterMyRequestsStatus").val('').trigger('change');
        var fromDate = $('#filterMyRequestsFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    maxDate: jQuery('#filterMyRequestsToDate').val() && jQuery('#filterMyRequestsToDate').val() !== "" ? jQuery('#filterMyRequestsToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterMyRequestsFromDate_img").click(function ()
        {
            fromDate.toggle();
        });
        var toDate = $('#filterMyRequestsToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    minDate: jQuery('#filterMyRequestsFromDate').val() && jQuery('#filterMyRequestsFromDate').val() !== "" ? jQuery('#filterMyRequestsFromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterMyRequestsToDate_img").click(function ()
        {
            toDate.toggle();
        });
        $('#txtFilterMyRequestsReferenceNumber').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterMyRequestsSearch').focus();
                }
                else
                {
                    $('#filterMyRequestsFromDate').focus();
                }
            }
        });
        $('#btnFilterMyRequestsClear').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterMyRequestsSearch').focus();
                }
                else
                {
                    $('#txtFilterMyRequestsReferenceNumber').focus();
                }
            }
        });
    } else
    {
       $('.searchToRemove > div:first-child').hide();
        $("#gridPanel").removeAttr("style");
        $("#gridPanel").addClass("panel-default");
    }
}
function buildColumns(gridcolumns)
{
    
    var columnJson = '[{"name":"Category","order":"1","isColumnDetail":false},{"name":"ReferenceNumber","order":"2","isColumnDetail":false},{ "name": "SendingEntity", "order": "3", "isColumnDetail": false }, { "name": "ReceivingEntity", "order": "4", "isColumnDetail": false },{ "name": "Subject", "order": "5", "isColumnDetail": false }, { "name": "CreatedDate", "order": "6", "isColumnDetail": false }]';
    var columns = JSON.parse(columnJson);
    var columnDetails = $.grep(columns, function (element, index) {
        return element.isColumnDetail === true;
    });

    if (columnDetails.length > 0)
    {
        gridcolumns.push({
            "className": 'details-control',
            "orderable": false,
            "data": null,
            "defaultContent": '',
            width: '16px'
        });
    }
    gridcolumns.push(
        {
            "className": "text-left",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            data: "importanceId",
            'render': function (data, type, full, meta)
            {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var i = 0; i < importances.length; i++)
                {
                    if (importances[i].id === data)
                    {
                        let btnView = document.createElement("i");
                        btnView.setAttribute("class", "fa fa-exclamation fa-lg");
                        btnView.setAttribute("style", "color:" + importances[i].color);
                        btnView.setAttribute("title", importances[i].text);
                        return btnView.outerHTML;
                    }
                }
                return "";
            }
        });
    columns.sort((a, b) => (a.order > b.order) ? 1 : -1);
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (!column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                customColumn.push({ columnName: column.name, functionName: column.customFunctionName });
                var customColumnKeyword = column.customFunctionName;
                var escapedVariable = column.customFunctionName.replace(/"/g, '&quot;');

                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                gridcolumns.push({
                    title: Resources[column.name] || column.name, "orderable": false, 'defaultContent': '<div data-function="' + escapedVariable + '"></div>',
                    "createdCell": function (td, cellData, rowData, row, col)
                    {
                        var htmlCell = "";
                        var div = td.children[0];
                        var customFunctionName = div !== null && typeof div !== "undefined" ? $(div).attr("data-function") : "";
                        //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                        //{
                        //    var functionName = customFunctionName.split("(")[0];
                        //    htmlCell = eval(functionName + "(" + JSON.stringify(rowData) + ")");
                        //}

                        var customColumns = JSON.parse(rowData.documentForm.form);
                        var columnName = customColumn.find(f => f.functionName == customFunctionName).columnName;

                        var customData = customColumns[columnName]

                        if (customFunctionName.includes("()")) {
                            if (customFunctionName == "getCommitteeName()") {
                                customData = getCommitteeName(customData);
                            }

                            if (customFunctionName == "getMeetingLocation()") {
                                customData = getMeetingLocation(customData);
                            }
                        }

                        htmlCell = htmlCell == "" && customColumns != null ? (customData == undefined ? "" : customData) : htmlCell;
                        $(td).html(htmlCell);
                    }
                });
            } else
            {
                switch (column.name)
                {
                    case "Category":
                        gridcolumns.push({
                            title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"],
                            render: function (data, type, full, meta)
                            {
                                var categories = new Categories().get(window.language);
                                for (var i = 0; i < categories.length; i++)
                                {
                                    if (categories[i].id === data)
                                    {
                                        return categories[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "ReferenceNumber":
                        gridcolumns.push({ title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": false, "className": "min-max-width-50-150" });
                        break;
                    case "Subject":
                        gridcolumns.push({ title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"], "className": "min-max-width-50-150" });
                        break;
                    case "SendingEntity":
                        gridcolumns.push({ title: Resources.SendingEntity, data: "sendingEntity", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"], "className": "min-max-width-50-150" });
                        break;
                    case "ReceivingEntity":
                        gridcolumns.push({ title: Resources.ReceivingEntity, data: "receivingEntity", render: $.fn.dataTable.render.text(), "orderable": false, "className": "min-max-width-50-150" });
                        break;
                    case "CreatedDate":
                        gridcolumns.push({
                            title: Resources.CreatedDate, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
                            }
                        });
                        break;
                    case "ModifiedDate":
                        gridcolumns.push({
                            title: Resources.ModifiedDate, data: "modifiedDate", "orderable": false, width: "100px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.modifiedDate, null, window.CalendarType);
                            }
                        });
                        break;
                    case "DueDate":
                        gridcolumns.push({
                            title: Resources.DueDate, data: "dueDate", "orderable": false, width: "100px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.dueDate, null, window.CalendarType);
                            }
                        });
                        break;
                }
            }
        }
    }

}
function buildColumnsDetails(row)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var columnsJson = '[{ "name":"Category","order":"1","isColumnDetail":false},{"name":"ReferenceNumber","order":"2","isColumnDetail":false},{"name":"From","order":"3","isColumnDetail":false},{"name":"Subject","order":"4","isColumnDetail":false},{"name":"TransferDate","order":"5","isColumnDetail":false},{"name":"Owner","order":"6","isColumnDetail":false},{"name":"Purpose","order":"3","isColumnDetail":true},{"name":"Priority","order":"4","isColumnDetail":true},{"name":"Privacy","order":"5","isColumnDetail":true},{"name":"CreatedBy","order":"6","isColumnDetail":true},{"name":"OpenedDate","order":"7","isColumnDetail":true}]';
    var columns = JSON.parse(columnsJson);

    columns.sort((a, b) => (a.order > b.order) ? 1 : -1);
    var html = "";
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                var customColumn = column.name;
                var customColumnKeyword = column.customFunctionName;

                var htmlCell = "";
                var customFunctionName = column.customFunctionName;
                //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                //{
                //    var functionName = customFunctionName.split("(")[0];
                //    htmlCell = eval(functionName + "(" + JSON.stringify(row.data()) + ")");
                //}

                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                var customColumns = JSON.parse(row.data().documentForm.form);

                customColumns = customColumns[customColumn];

                if (customFunctionName.includes("()")) {
                    if (customFunctionName == "getCommitteeName()") {
                        customColumns = getCommitteeName(customColumns);
                    }

                    if (customFunctionName == "getMeetingLocation()") {
                        customColumns = getMeetingLocation(customColumns);
                    }
                }

                htmlCell = htmlCell == "" && customColumns != null ? (customColumns == undefined ? "" : customColumns) : htmlCell;

                html += '<tr><th style="width: 10%;padding:5px">' + (GetCustomAttributeTranslationByLangauge(customColumnKeyword)) + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + htmlCell + '</td></tr>';

            } else
            {
                switch (column.name)
                {
                    case "Category":
                        var category = "";
                        var categories = new Categories().get(window.language);
                        for (var i = 0; i < categories.length; i++)
                        {
                            if (categories[i].id === row.data().categoryId)
                            {
                                category = categories[i].text;
                            }
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Category + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + category + '</td></tr>';
                        break;
                    case "ReferenceNumber":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReferenceNumber + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().referenceNumber || '') + '</td></tr>';
                        break;
                    case "Subject":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Subject + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().subject || '') + '</td></tr>';
                        break;
                    case "SendingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.SendingEntity + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().sendingEntity || '') + '</td></tr>';
                        break;
                    case "ReceivingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReceivingEntity + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().receivingEntity || '') + '</td></tr>';
                        break;
                    case "CreatedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.CreatedDate + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().createdDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "ModifiedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ModifiedDate + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().modifiedDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "DueDate":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.DueDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().dueDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                }
            }
        }
    }
    return html;
}

function UsersData() {
    Common.ajaxGet('/User/GetUsersFromCTS', {
        "userName": "",
        "delegationId": null,
    }, function (response) {
        gUsersData = response;
    }, function (msg) {
        console.log(msg);
    }, false, '');

//    $.ajax({
//        type: 'GET',
//        url: window.IdentityUrl + '/api/SearchUsers',
//        headers: {
//            "Authorization": 'Bearer ' + window.IdentityAccessToken
//        },
//        dataType: 'json',
//        data: {
//            'text': '',
//            'language': '',
//            'showOnlyActiveUsers': true
//        },
//        success: function (data) {
            
//            gUsersData = data;
//        },
//        error: function (jqXHR, textStatus, errorThrown) {
//            console.error("AJAX Error: " + errorThrown);
//        }
//    });
}
function getUserFullNameById(id) {
    
    for (var i = 0; i < gUsersData.length; i++) {
        if (gUsersData[i].id === id) {
            return `${gUsersData[i].firstname} ${gUsersData[i].lastname}`;
        }
    }
    return null; // Return null if ID not found
}
//async function getUserPermission(params) {
//    return new Promise((resolve, reject) => {
//        Common.ajaxGet('/Basket/getUserPermition', params, resolve, reject, true);
//    });
//}

function getUserPermission(self, params) {
    var basketName = self.model.basketName;
    IsReadOnly = true;
    
    Common.ajaxGet('/Basket/getUserPermition', params, function (response) {
        if (window.language == 'en') {
            basketName = response.name;
        }
        else if (window.language == 'ar') {
            basketName = response.nameAr ? response.nameAr : response.name;
        }
        else if (window.language == 'fr') {
            basketName = response.nameFr ? response.nameFr : response.name;
        }

        IsOwner = response.isOwmer;
        IsAdmin = response.isAdmin;
        if (!IsOwner && !IsAdmin && response.roleId == 2) {
            IsReadOnly = false;
        }



        UsersData();
        Common.setActiveSidebarMenu("liBasket" + self.model.basketId);
        document.getElementById("BasketName").innerText = basketName;
        $.fn.select2.defaults.set("theme", "bootstrap");
        buildFilters();
        Common.gridCommon();
        var buttons = [];
        var columns = [{ title: '<input id="chkAll" type="checkbox" />', width: '16px', "orderable": false, "render": function (data, type, row) { return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />"; } },

        { title: "Id", data: "id", visible: false, "orderable": false }];
        buildColumns(columns);
        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = "";
                if (full.isOverDue) {
                    html += "<div class='mr-sm' title='" + Resources.OverDue + "'><i class='fa fa-clock-o fa-lg text-danger'></i></div>&nbsp;";
                }
                return "<div id='divLock" + full.id + "' style='display: inline-flex;'>" + html + "</div>";
            }
        });
        var buttons = [];
        buttons.push(
            {
                
                className: 'btn-sm btn-primary',
                text: Resources.Add,
                action: function (e, dt, node, config) {
                    openLinkWindow(self.model.basketId);
                }
            },
            {
                className: 'btn-sm btn-danger hidden',
                text: Resources.Delete,
                action: function (e, dt, node, config) {
                    var ids = GridCommon.GetSelectedRows(gTableName);
                    if (ids.length > 0) {
                        Common.showConfirmMsg(Resources.DeleteToDoConfirmation, function () {
                            Common.ajaxDelete('/Basket/DeleteBasketDocuments',
                                {
                                    'documentIds': ids, 'basketId': self.model.basketId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                                },
                                function (result) {
                                    if (result != null) {

                                        swal.close()
                                        if (result.length == 0) {
                                            setTimeout(function () {
                                                Common.alertMsg(Resources.CouldNotDeleteDocument);
                                            }, 500);
                                        }
                                        else {
                                            $(".html5buttons .btn-danger").addClass("hidden");
                                            Common.showScreenSuccessMsg();
                                        }
                                        GridCommon.Refresh(gTableName);
                                    } else {
                                        Common.showScreenErrorMsg();
                                    }

                                }, null, false);
                        });
                    }
                    else {
                        Common.alertMsg(Resources.NoRowSelected);
                    }
                }
            }

        );
        if (IsAdmin) {
            $("#btnDeleteBasket").addClass("hidden");
        }
        else if (!IsOwner) {
            $("#btnDeleteBasket").addClass("hidden");
            $("#btnEditBasket").addClass("hidden");
            if (IsReadOnly) {
                buttons = [];
            }
        }
        else {
           $("#btnRemoveBasket").addClass("hidden");
        }
        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                let btnView = document.createElement("button");
                btnView.setAttribute("class", "btn btn-xs btn-warning view");
                btnView.setAttribute("title", Resources.View);
                btnView.setAttribute("clickattr", "openDocument(" + full.id + "," + self.model.basketId + ")");
                btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                return btnView.outerHTML;
            }
        });
        var table = $("#" + gTableName)
            .on('draw.dt', function () {
                $('#' + gTableName + " td input[type='checkbox']").on('click', function () {
                    if ($(this).is(":checked"))
                        $(".html5buttons .btn-danger").removeClass("hidden");
                    else if (GridCommon.GetSelectedRows(gTableName).length == 1)
                        $(".html5buttons .btn-danger").addClass("hidden");
                });

                $('#' + gTableName + ' tbody tr td').each(function () {
                    this.setAttribute('title', $(this).text());
                });
                GridCommon.CheckSelectedRows(gTableName);
            })
            .DataTable({
                "createdRow": function (row, data, dataIndex) {
                    var color = "";
                    var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                    for (var i = 0; i < priorities.length; i++) {
                        if (priorities[i].id === data.priorityId) {
                            color = priorities[i].color;
                        }
                    }
                    if (color !== "") {
                        $(row).attr('style', "color:" + color);
                    }
                },
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/Document/ListCustomBasketDocuments",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d) {
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.CategoryId = $("#cmbFilterMyRequestsCategory").val() !== null && typeof $("#cmbFilterMyRequestsCategory").val() !== "undefined" ? $("#cmbFilterMyRequestsCategory").val() : "0";
                        d.StatusId = $("#cmbFilterMyRequestsStatus").val() !== null && typeof $("#cmbFilterMyRequestsStatus").val() !== "undefined" ? $("#cmbFilterMyRequestsStatus").val() : "0";
                        d.ReferenceNumber = $("#txtFilterMyRequestsReferenceNumber").val() !== "" && typeof $("#txtFilterMyRequestsReferenceNumber").val() !== "undefined" ? $("#txtFilterMyRequestsReferenceNumber").val() : "";
                        d.Subject = $("#txtFilterMyRequestsSubject").val() !== "" && typeof $("#txtFilterMyRequestsSubject").val() !== "undefined" ? $("#txtFilterMyRequestsSubject").val() : "";
                        d.FromDate = $("#filterMyRequestsFromDate").val() !== "" && typeof $("#filterMyRequestsFromDate").val() !== "undefined" ? $("#filterMyRequestsFromDate").val() : "";
                        d.ToDate = $("#filterMyRequestsToDate").val() !== "" && typeof $("#filterMyRequestsToDate").val() !== "undefined" ? $("#filterMyRequestsToDate").val() : "";
                        d.Overdue = $("#chkFilterMyRequestsOverdue").is(':checked');
                        d.BasketId = self.model.basketId;
                        return d;
                    }
                },
                "order": [],
                "columns": columns,
                "fnInitComplete": function (settings, json) {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons "B>ltrpi',
                buttons: buttons
            });
        GridCommon.AddCheckBoxEvents(gTableName);
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        GridCommon.AddCheckBoxEvents(gTableName);
        $('#' + gTableName + ' tbody').on('click', ".view", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#' + gTableName + ' tbody').on('dblclick', 'tr', function () {
            if (!gLocked) {
                gLocked = true;
                try {
                    var onclick = $(this).find(".view").attr("clickattr");
                    eval(onclick);
                } catch (e) {
                    gLocked = false;
                }
            }
        });
        $('#' + gTableName + ' tbody').on('click', 'td.details-control', function () {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            }
            else {
                row.child(format(row)).show();
                tr.addClass('shown');
            }
        });

        $('#' + gTableName + ' #chkAll').on('click', function () {
            let isChecked = $(this).is(":checked");
            if (isChecked) {
                $(".html5buttons .btn-danger").removeClass("hidden");
            }
            else 
                $(".html5buttons .btn-danger").addClass("hidden");

            let table = $('#' + gTableName).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', isChecked);
        });
        $('#' + gTableName).on('change', 'input[type="checkbox"]', function () {
            let table = $('#' + gTableName).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();

            let total = $('input[type="checkbox"]', pageNodes).length;
            let checked = $('input[type="checkbox"]:checked', pageNodes).length;

            $('#' + gTableName + ' #chkAll').prop('checked', total === checked);

            if (checked > 0) {
                $(".html5buttons .btn-danger").removeClass("hidden");
            } else {
                $(".html5buttons .btn-danger").addClass("hidden");
            }
        });

        table.on('draw', function () {
            $('#chkAll').prop('checked', false);

            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', false);

            $(".html5buttons .btn-danger").addClass("hidden");
        });

        $(document).off('keydown', function (e) { });
        $(document).on('keydown', function (e) {
            var code = e.keyCode || e.which;
            if (code === 13) {
                $("#btnFilterMyRequestsSearch").trigger('click');
            }
        });
        $("#btnEditBasket").click(function (e) {
            var params = { id: self.model.basketId };
            Common.ajaxGet('/Basket/GetBasketOwner', params, function (response) {
                openEditBasketWindow(response);
            }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
        });
        $("#btnDeleteBasket").click(function (e) {

            Common.showConfirmMsg(Resources.DeleteBasketConfirmation, function () {
                Common.ajaxDelete('/Basket/Delete',
                    {
                        'id': self.model.basketId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    function (result) {
                        if (result != null) {
                            swal.close()
                            if (result.length == 0) {
                                setTimeout(function () {
                                    Common.alertMsg(Resources.CouldNotDeleteDocument);
                                }, 500);
                            }
                            window.location.href = window.location.origin;
                        } else {
                            Common.showScreenErrorMsg();
                        }

                    }, null, false);
            });
        });
        $("#btnRemoveBasket").click(function (e) {

            Common.showConfirmMsg(Resources.RemoveFromBasket, function () {
                Common.ajaxDelete('/Basket/Remove',
                    {
                        'id': self.model.basketId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    function (result) {
                        if (result != null) {

                            swal.close()
                            if (result.length == 0) {
                                setTimeout(function () {
                                    Common.alertMsg(Resources.CouldNotDeleteDocument);
                                }, 500);
                            }
                            window.location.href = window.location.origin;
                        } else {
                            Common.showScreenErrorMsg();
                        }

                    }, null, false);
            });
        });
    });
}

var gUsersData = [];
var gTableName = "grdBasketItems";
var gLocked = false;
var IsOwner = false;
var IsReadOnly = true;
var IsAdmin = false;
class DocumentBasketView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "basket", model);
    }
     render() {
        var self = this;
        
         var params = { basketId: self.model.basketId };
        getUserPermission(self, params);
    }
}
export default { DocumentBasket, DocumentBasketView };