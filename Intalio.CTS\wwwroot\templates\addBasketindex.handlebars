<div id="modalBasket" tabindex="-1" role="dialog" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" id="BasketClose" class="close" data-dismiss="modal">&times;</button>
                <h4 id="modalBasketTitle" class="modal-title"></h4>
            </div>
            <div class="modal-body">
                <form id="formBasketPost" method="post" data-parsley-validate="" novalidate="">
                    <input type="hidden" id="hdId" />
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label field-required">{{Localizer 'Name'}} </label>
                                <input type="text" id="txtName" class="form-control" autocomplete="off" tabindex="1" required="required" data-parsley-length="[2, 150]" maxlength="150" >
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label ">{{Localizer 'NameInAr'}} </label>
                                <input type="text" id="txtNameAr" class="form-control" autocomplete="off" tabindex="2"  data-parsley-length="[2, 150]" maxlength="150" >
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="control-label ">{{Localizer 'NameInFr'}} </label>
                                <input type="text" id="txtNameFr" class="form-control" autocomplete="off" tabindex="3"  data-parsley-length="[2, 150]" maxlength="150" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4" id="indexBasketContainer">
                            <div class="form-group">
                                <label class="control-label">{{Localizer 'Users'}} </label>
                                <select id="cmbIndexBasket" tabindex="4"
                                        data-parsley-errors-container="#cmbIndexBasketError" class="form-control"></select>
                                <div id="cmbIndexBasketError"></div>
                            </div>
                        </div>
                        <div class="col-md-4" id="indexBasketRoleContainer">
                            <div class="form-group">
                                <label class="control-label">{{Localizer 'Role'}} </label>
                                <select required="required" id="cmbIndexBasketRole" tabindex="5"
                                        data-parsley-errors-container="#cmbIndexBasketRoleError" class="form-control">
                                    <option value="1">{{Localizer 'ReadOnly'}}</option>
                                    <option value="2">{{Localizer 'Edit'}}</option>
                                    <option value="3">{{Localizer 'Administrator'}}</option>

                                </select>
                                <div id="cmbIndexBasketRoleError"></div>
                            </div>
                        </div>
                        <div class="col-md-4" ref="privacyIndexContainer" id="privacyIndexContainer">
                            <div class="form-group">
                                <label class="control-label">{{Localizer 'Privacy'}} </label>
                                <select required="required" ref="cmbPrivacyIndex"  id="cmbPrivacyIndex" tabindex="5"
                                        data-parsley-errors-container="#cmbPrivacyIndexError" class="form-control">
                                </select>
                                <div id="cmbPrivacyIndexError"></div>
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-md-10"></div>
                        <div class="col-md-2 d-flex align-items-center justify-content-end">
                            <div class="btn-group">
                                <button type="button" class="btn btn-success arabic-btn-group-right" id="btnAdd" title="{{Localizer 'Add'}}">
                                    <span class="fa fa-plus"></span>
                                </button>
                                <button type="button" title="{{Localizer 'Delete'}}" class="btn btn-danger arabic-btn-group-left" id="btnDelete">
                                    <span class="fa fa-trash-o"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row" id="selectedItemsContainer">
                        <div class="col-md-12 pull-left" style="margin-top: 8px">
                            <table id="selectedItemsTable" class="table table-striped table-hover" cellspacing="0" width="100%">
                            </table>
                        </div>
                    </div>
                </form>
                <div class="required"><span class="text-danger">*</span> {{Localizer 'RequiredFields'}}</div>
            </div>
            <div class="modal-footer">
                <button tabindex="4" id="btnSubmitBasket" type="button" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin'></i> {{Localizer 'Saving'}}">{{Localizer 'Submit'}}</button>
                <button tabindex="5" id="btnCloseBasket" type="button" data-dismiss="modal" class="btn btn-default">{{Localizer 'Close'}}</button>
            </div>
        </div>
    </div>
</div>
