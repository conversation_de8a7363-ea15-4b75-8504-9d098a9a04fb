﻿<button type="button" class="btn btn-primary pull-right" ref="btnNewDelegation">
    <em class="fa fa-plus-circle mr-sm"></em>{{Localizer 'New'}}
</button>
<div ref="{{ComponentId}}">
    <h3 class="content-heading">
        {{Localizer 'Delegation'}}
    </h3>
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-default" style="margin-bottom:0px;border-bottom-left-radius:0px;border-bottom-right-radius:0px;">
                <div class="panel-heading">
                    {{Localizer 'Search'}}
                    <span ref="collapseIcon" data-target="#collapsePanel" class="pull-right clickable"><i class="fa fa-angle-up fa-lg"></i></span>
                </div>
                <div ref="collapsePanel">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-3 col-sm-6" ref="toUserContainer">
                                <div class="form-group">
                                    <label class="control-label">{{Localizer 'FullName'}}</label>
                                    <select ref="cmbToUser" tabindex="1"
                                            data-parsley-errors-container="#cmbToUserError" class="form-control">
                                    </select>
                                    <div id="cmbToUserError"></div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="control-label">{{Localizer 'FromDate'}}</label>
                                    <div class="input-group date">
                                        <input ref="fromDate" type="text" tabindex="2" autocomplete="off" class="form-control" name="Date"
                                               data-parsley-errors-container="#fromDateError">
                                        <span class="input-group-addon" ref="fromDate_img" style="cursor:pointer">
                                            <i class="fa fa-calendar" aria-hidden="true"></i>
                                        </span>
                                        <div id="fromDateError"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label class="control-label">{{Localizer 'ToDate'}}</label>
                                    <div class="input-group date">
                                        <input ref="toDate" type="text" tabindex="3" autocomplete="off" class="form-control" name="Date"
                                               data-parsley-errors-container="#toDateError">
                                        <span class="input-group-addon" ref="toDate_img" style="cursor:pointer">
                                            <i class="fa fa-calendar" aria-hidden="true"></i>
                                        </span>
                                        <div id="toDateError"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 list-action-filter">
                                <button ref="btnSearch" tabindex="4" type="button" class="btn btn-primary">{{Localizer 'Search'}}</button>
                                <button ref="btnClear" tabindex="5" type="button" class="btn btn-clear">{{Localizer 'Clear'}}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="panel" style="border-top-left-radius:0px;border-top-right-radius:0px;border-color: #cfdbe2;border-top-width: 0px;">
                <div class="panel-body">
                    <div class="table-responsive glyphicon-container-color">
                        <table ref="grdItems" class="table table-striped table-hover"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div ref="modalDelegation" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" ref="delegationClose" class="close" data-dismiss="modal">&times;</button>
                    <h4 ref="modalDelegationTitle" class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <form ref="formPost" method="post" data-parsley-validate="" novalidate="">
                        <input type="hidden" ref="hdId" />
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label field-required">{{Localizer 'FromDate'}} </label>
                                    <div class="input-group date">
                                        <input required="required" ref="indexFromDate" type="text" tabindex="11" autocomplete="off" class="form-control" name="Date"
                                               data-parsley-errors-container="#indexFromDateError">
                                        <span class="input-group-addon" ref="indexFromDate_img" style="cursor:pointer">
                                            <i class="fa fa-calendar" aria-hidden="true"></i>
                                        </span>
                                    </div>
                                    <div id="indexFromDateError"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="control-label field-required">{{Localizer 'ToDate'}} </label>
                                    <div class="input-group date">
                                        <input required="required" ref="indexToDate" type="text" tabindex="12" autocomplete="off" class="form-control" name="Date"
                                               data-parsley-errors-container="#indexToDateError">
                                        <span class="input-group-addon" ref="indexToDate_img" style="cursor:pointer">
                                            <i class="fa fa-calendar" aria-hidden="true"></i>
                                        </span>
                                    </div>
                                    <div id="indexToDateError"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6" ref="indexToUserContainer">
                                <div class="form-group">
                                    <label class="control-label field-required">{{Localizer 'FullName'}} </label>
                                    <select required="required" ref="cmbIndexToUser" tabindex="13"
                                            data-parsley-errors-container="#cmbIndexToUserError" class="form-control">
                                    </select>
                                    <div id="cmbIndexToUserError"></div>
                                </div>
                            </div>
                            {{#if managerId}}
                            <div ref="managerContainer" class="col-md-6 mt-26px">
                                <div class="form-group">
                                    <div class="input-group">
                                        <div class="checkbox c-checkbox">
                                            <label>
                                                <input ref="chkDelegateToManager" tabindex="14" type="checkbox" name="DelegateToManager" value="true">
                                                <span class="fa fa-check"></span>{{Localizer 'DelegateToManager'}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{/if}}
                        </div>
                        <div class="row">
                            <div class="col-md-11 col-sm-10 col-xs-10" ref="indexCategoryContainer">
                                <div class="form-group">
                                    <label class="control-label field-required">{{Localizer securityMatrixCategory}} </label>
                                    <select required="required" ref="cmbIndexCategory" tabindex="15"
                                            data-parsley-errors-container="#cmbIndexCategoryError" class="form-control"></select>
                                    <div id="cmbIndexCategoryError"></div>
                                </div>
                            </div>
                            <div class="col-md-1 col-sm-2 col-xs-2 mt-26px p0">
                                <button tabindex="16" ref="chkAllCategories" type="button" class="btn btn-md btn-warning" title="{{Localizer 'AllCategories'}}"><em class="fa fa-plus"></em></button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6" ref="privacyIndexContainer">
                                <div class="form-group">
                                    <label class="control-label field-required">{{Localizer 'Privacy'}}</label>
                                    <select required="required" ref="cmbPrivacyIndex" tabindex="1"
                                            data-parsley-errors-container="#cmbPrivacyIndexError" class="form-control">
                                    </select>
                                    <div id="cmbPrivacyIndexError"></div>
                                </div>
                            </div>
                            <!--<div class="col-lg-3 col-md-3 col-sm-3" ref="signContainer">
                                <div class="form-group">
                                    <div class="input-group">
                                        <div class="checkbox c-checkbox">
                                            <label>
                                                <input ref="chksign" tabindex="14" type="checkbox" name="Sign" value="true">
                                                <span class="fa fa-check"></span>{{Localizer 'Sign'}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>-->
                            <div class="col-lg-3 col-md-3 col-sm-3" ref="draftInboxContainer">
                                <div class="form-group">
                                    <div class="input-group">
                                        <div class="checkbox c-checkbox">
                                            <label>
                                                <input ref="chkDraftInbox" tabindex="14" type="checkbox" name="DraftInbox" value="true">
                                                <span class="fa fa-check"></span>{{Localizer 'DraftInbox'}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12" ref="notesContainer">
                                <div class="form-group">
                                    <label class="control-label">{{Localizer 'Notes'}}</label>
                                    <textarea ref="notes" tabindex="14" data-parsley-errors-container="#notesError" class="form-control" rows="4"></textarea>
                                    <div id="notesError"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6" ref="oldCorrespondenceContainer">
                                <div class="form-group">
                                    <div class="input-group">
                                        <div class="checkbox c-checkbox">
                                            <label>
                                                <input ref="chkOldCorrespondence" tabindex="14" type="checkbox" name="ShowOldCorrespondence" value="true">
                                                <span class="fa fa-check"></span>{{Localizer 'ShowOldCorrespondence'}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6" ref="indexStartDateContainer">
                                <div class="form-group">
                                    <label class="control-label">{{Localizer 'StartDate'}} </label>
                                    <div class="input-group date">
                                        <input ref="indexStartDate" type="text" tabindex="12" autocomplete="off" class="form-control" name="Date"
                                               data-parsley-errors-container="#indexStartDateError">
                                        <span class="input-group-addon" ref="indexStartDate_img" style="cursor:pointer">
                                            <i class="fa fa-calendar" aria-hidden="true"></i>
                                        </span>
                                    </div>
                                    <div id="indexStartDateError"></div>
                                </div>
                            </div>


                        </div>
                    </form>
                    <div class="required"><span class="text-danger">*</span> {{Localizer 'RequiredFields'}}</div>
                </div>
                <div class="modal-footer">
                    <button tabindex="17" ref="btnSubmit" type="button" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin'></i> {{Localizer 'Saving'}}">{{Localizer 'Submit'}}</button>
                    <button tabindex="18" ref="btnClose" type="button" data-dismiss="modal" class="btn btn-default">{{Localizer 'Close'}}</button>
                </div>
            </div>
        </div>
    </div>
</div>
