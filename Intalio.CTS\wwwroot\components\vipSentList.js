﻿import Intalio from './common.js'
import VipDocumentDetails from './vipDocumentDetails.js'
import DocumentSent from './sentList.js'
import { Categories } from './lookup.js'
class VipDocumentSent extends Intalio.Model {
    constructor() {
        super();
        this.title = null;
        this.nodeId = null;
        this.delegationId = null;
        this.categories = null;
        this.fromStructureSent = false;

    }
}
function buildFilters(nodeId, categories) {
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index) {
        return element.id.toString() === nodeId;
    })[0];
    var filters = node.filters !== "" && node.filters !== null ? JSON.parse(node.filters) : [];
    if (filters.length > 0) {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++) {
            var filter = filters[i];
            switch (filter) {
                case "ReferenceNumber":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
                        '<input type="text" id="txtFilterSentReferenceNumber" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "FromDate":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label>' +
                        '<div class="input-group date"><input id="filterSentFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterSentFromDateError">' +
                        '<span class="input-group-addon" id="filterSentFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterSentFromDateError"></div></div></div></div>';
                    break;
                case "ToDate":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label>' +
                        '<div class="input-group date"><input id="filterSentToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterSentToDateError">' +
                        '<span class="input-group-addon" id="filterSentToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterSentToDateError"></div></div></div></div>';
                    break;
                case "Category":
                    clearFix += 6;
                    var div = '<div class="col-md-6" id="categoryFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterSentCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++) {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterSentCategoryError"></div></div></div>';
                    html += div;
                    break;
                case "Subject":
                    clearFix += 6;
                    html += '<div class="col-md-6"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterSentSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Purpose":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="purposeFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.Purpose + '</label>' +
                        '<select id="cmbFilterSentPurpose" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentPurposeError" class="form-control"></select></div></div>';
                    break;
                case "Priority":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="priorityFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.Priority + '</label>' +
                        '<select id="cmbFilterSentPriority" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentPriorityError" class="form-control"></select></div></div>';
                    break;
                case "Privacy":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="privacyFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.Privacy + '</label>' +
                        '<select id="cmbFilterSentPrivacy" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentPrivacyError" class="form-control"></select></div></div>';
                    break;
                case "Structure":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="structureFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.Structure + '</label>' +
                        '<select id="cmbFilterSentStructure" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentStructureError" class="form-control"></select></div></div>';
                    break;
                case "User":
                    clearFix += 6;
                    html += '<div class="col-md-6" id="userFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.User + '</label>' +
                        '<select id="cmbFilterSentUser" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentUserError" class="form-control"></select></div></div>';
                    break;
            }
            if (clearFix === 12 || clearFix === 11) {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '</div>';
        $('#filtersContainer').html(html);

        var clickedSearch = false;
        $('#collapseSentIcon').click(function () {
            $('#collapseSentIcon').empty();
            if (clickedSearch) {
                $('#collapseSentIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
                $('#collapseSentPanel').attr('class', '');
                $('#collapseSentPanel').addClass('panel-body panel-collapse collapse in');
                clickedSearch = false;
            } else {
                $('#collapseSentIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
                $('#collapseSentPanel').attr('class', '');
                $('#collapseSentPanel').addClass('panel-body panel-collapse collapse');
                clickedSearch = true;
            }
        });
        $("#btnFilterSentSearch").on('click', function () {
            gFromSearch = true;
            gNoMoreData = false;
            gPageIndex = 0;
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            loadSentList();
        });
        $("#btnFilterSentClear").on('click', function () {
            $("#cmbFilterSentPurpose").val('').trigger('change');
            $("#cmbFilterSentPriority").val('').trigger('change');
            $("#cmbFilterSentPrivacy").val('').trigger('change');
            $("#cmbFilterSentCategory").val('').trigger('change');
            $("#cmbFilterSentStatus").val('').trigger('change');
            $("#txtFilterSentReferenceNumber").val('');
            $("#txtFilterSentSubject").val('');
            fromDate.clear();
            toDate.clear();
            $("#cmbFilterSentStructure").val('').trigger('change');
            $("#cmbFilterSentUser").val('').trigger('change');

            gFromSearch = true;
            gNoMoreData = false;
            gPageIndex = 0;
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            loadSentList();
        });
        $('#cmbFilterSentPurpose').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Purposes().get(window.language),
            dropdownParent: $('#purposeFilterSentContainer')
        });
        $("#cmbFilterSentPurpose").val('').trigger('change');

        $('#cmbFilterSentPriority').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Priorities().get(window.language),
            dropdownParent: $('#priorityFilterSentContainer')
        });
        $("#cmbFilterSentPriority").val('').trigger('change');

        $('#cmbFilterSentPrivacy').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Privacies().get(window.language),
            dropdownParent: $('#privacyFilterSentContainer')
        });
        $("#cmbFilterSentPrivacy").val('').trigger('change');

        $('#cmbFilterSentCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterSentContainer')
        });
        $("#cmbFilterSentCategory").val('').trigger('change');
        $('#cmbFilterSentStatus').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#statusFilterSentContainer')
        });
        $("#cmbFilterSentStatus").val('').trigger('change');

        $('#cmbFilterSentStructure').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#structureFilterSentContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes',
                type: "POST",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term) {
                    return {
                        "text": term.term ? term.term : "", "language": window.language, "attributes": [window.StructureNameAr, window.StructureNameFr]
                    };
                },
                processResults: function (data) {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        var structureName = val.name;
                        if (val.attributes != null && val.attributes.length > 0) {
                            var attributeLang = $.grep(val.attributes, function (e) {
                                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                            });
                            if (attributeLang.length > 0) {
                                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                            }
                        }
                        var item = {};
                        item.id = val.id;
                        item.text = structureName;
                        item.isStructure = true;
                        item.dataId = val.id;
                        listitemsMultiList.push(item);
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        }).on("change", function () {
            if (document.getElementById('cmbFilterSentUser') !== null) {
                var type = "GET";
                var url = '/api/SearchUsers';
                //var structures = $('#cmbFilterSentStructure').val();
                //if (structures !== "" && structures !== null)
                //{
                //    type = "POST";
                //    url = '/api/SearchUsersByStructureIds';
                //}
                $('#cmbFilterSentUser').select2({
                    minimumInputLength: 0,
                    allowClear: false,
                    placeholder: "",
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    dropdownParent: $("#userFilterSentContainer"),
                    multiple: true,
                    width: "100%",
                    ajax: {
                        delay: 400,
                        url: window.IdentityUrl + url,
                        type: type,
                        dataType: 'json',
                        headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                        data: function (term) {
                            var params = { "text": "", "language": window.language };
                            //var params = { "text": term.term ? term.term : "", "language": window.language };
                            //var structures = $('#cmbFilterSentStructure').val();
                            //if (structures !== "" && structures !== null)
                            //{
                            //    params.ids = $('#cmbFilterSentStructure').val();
                            //}
                            return params;
                        },
                        processResults: function (data, term) {
                            var termSearch = term.term ? term.term : "";

                            var structures = $('#cmbFilterSentStructure').val();
                            var listitemsMultiList = [];
                            $.each(data, function (key, val) {
                                if (structures !== "" && structures !== null &&
                                    !((val.structureIds != null && val.structureIds.some(r => structures.indexOf(String(r)) >= 0)) ||
                                        structures.includes(val.defaultStructureId))) {
                                    return;
                                }
                                var fullName = val.fullName;
                                if (window.language != 'en') {
                                    fullName = getFullNameByLangauge(val);
                                    fullName = fullName.trim() == "" ? val.fullName : fullName;
                                }
                                var allNames = getFullNameInAllLangauge(val);
                                if (allNames.length == 0) allNames.push(fullName);
                                if (termSearch != "" &&
                                    !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                                    return;
                                }
                                let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                                if (!isExist) {
                                    var item = {};
                                    item.id = val.id;
                                    item.text = fullName;
                                    item.isStructure = false;
                                    item.dataId = val.id;
                                    listitemsMultiList.push(item);
                                }
                            });
                            return {
                                results: listitemsMultiList
                            };
                        }
                    },
                    sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
                }).val("").trigger("change");
            }
        });
        $("#cmbFilterSentStructure").val('').trigger('change');
        $('#cmbFilterSentUser').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#userFilterSentContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchUsers',
                type: "GET",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term) {
                    return { "text": term.term ? term.term : "", "language": window.language };
                    //return { "text": term.term ? term.term : "", "language": window.language };
                },
                processResults: function (data, term) {
                    var termSearch = term.term ? term.term : "";

                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        var fullName = val.fullName;
                        if (window.language != 'en') {
                            fullName = getFullNameByLangauge(val);
                            fullName = fullName.trim() == "" ? val.fullName : fullName;
                        }
                        var allNames = getFullNameInAllLangauge(val);
                        if (allNames.length == 0) allNames.push(fullName);
                        if (termSearch != "" &&
                            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                            return;
                        }
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = fullName;
                            item.isStructure = false;
                            item.dataId = val.id;
                            listitemsMultiList.push(item);
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });
        $("#cmbFilterSentUser").val('').trigger('change');
        var fromDate = $('#filterSentFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery('#filterSentToDate').val() && jQuery('#filterSentToDate').val() !== "" ? jQuery('#filterSentToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterSentFromDate_img").click(function () {
            fromDate.toggle();
        });
        var toDate = $('#filterSentToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery('#filterSentFromDate').val() && jQuery('#filterSentFromDate').val() !== "" ? jQuery('#filterSentFromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterSentToDate_img").click(function () {
            toDate.toggle();
        });
        $('#txtFilterSentReferenceNumber').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterSentSearch').focus();
                }
                else {
                    $('#filterSentFromDate').focus();
                }
            }
        });
        $('#btnFilterSentClear').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterSentSearch').focus();
                }
                else {
                    $('#filterSentFromDate').focus();
                }
            }
        });
    } else {
        $("#btnOpenSearchSentModal").remove();
        $("#divSearchSent").remove();
    }
}
function openDocument(id, delegationId, nodeId) {
    var params = { id: id };
    if (delegationId !== null) {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetDocumentBasicInfoByTransferId', params, function (response) {
        $('.card-max-width').addClass('card-min-width');
        $('.card-max-width').removeClass('card-max-width');
        $('input:checkbox').removeAttr('checked');
        $('#sentListContainer li').removeClass("active");
        $("input[data-id='" + id + "']").parent().parent().parent().addClass("active");
        $("input[data-id='" + id + "']").prop('checked', true);
        gSelectedRowId = id;
        gLocked = false;
        let item = "liSent" + nodeId;
        if (delegationId !== null) {
            item = "sent-" + nodeId;
        }
        Common.setActiveSidebarMenu(item);
        $(".delegation").removeClass("active");

        var model = new VipDocumentDetails.VipDocumentDetails();
        model.readonly = true;
        model.delegationId = delegationId;
        model.id = id;
        model.documentId = response.id;
        model.referenceNumber = response.referenceNumber;
        model.categoryName = response.categoryName;
        model.statusId = response.status;
        model.createdByUser = response.createdByUser;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.showMyTransfer = true;
        model.attachmentId = response.attachmentId;
        model.categoryId = response.categoryId;
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
        model.tabs = $.grep(tabs, function (element, index) {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
        });
        model.tabsWithStatic = tabs;
        model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
        model.fromSent = true;
        model.showBackButton = false;
        model.nodeId = nodeId;
        model.showPreview = window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer" && response.attachmentId !== null;
        model.attachmentCount = response.attachmentCount;
        model.noteCount = response.notesCount;
        model.linkedCount = response.linkedCorrespondanceCount
        model.attachmentVersion = response.attachmentVersion;

        var wrapper = $("#sentDocumentDetailsContainer");
        wrapper.empty();
        $(".modal-window").empty();
        var view = new VipDocumentDetails.VipDocumentDetailsView(wrapper, model);
        view.render();
        $(".documentHeader").hide();
        $(".waitingBackground").removeClass("waitingBackground");
        $(".vipDetailsPanel").show();
        $(".vipCorrLeftPanel").removeClass("col-lg-12 col-md-12 col-sm-12 col-xs-12");
        $(".vipCorrLeftPanel").addClass("col-lg-3 col-md-4 col-sm-4 col-xs-12");
        $(".mdl-ul").removeAttr("style")

    }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
}

function openRecallReasonVIP(callback) {
    // The modal has a header having >> "modal-header" div contains the closing button icon that closes the modal
    // The modal also has a textarea for notes, and two buttons: "Submit" and "Cancel".
    const modal = $(`
                        <div data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade RecallVIP" id="RecallVIPModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                        <div class="modal-header" style="border-bottom:0px; padding-bottom:3px;">
                        <button type="button" ref="recallCloseVIP" id="closeRecallVIP" class="close" data-dismiss="modal">&times;</button>
                        <h4 ref="RecallTitleVIP" class="modal-title"></h4>
                        </div> 
                        <div class="modal-body" style="padding-top: 2px; ">
                        <form ref="formPost" method="post" data-parsley-validate="" novalidate="">
                        <div class="row">
                        <div class="col-md-12" ref="recallReasonContainerVIP">
                        <label class="control-label field-required" style="font-size: medium; ">${Resources.RecallReason}</label>
                        <textarea id="recallReasonVIP" rows="3" data-parsley-required="true"class="form-control" required></textarea>
                        <div class="invalid-feedback" style="display:none; color:red;">
                                        ${Resources.RequiredField}
                        </div>
                        </div>
                        </div>
                        </form>
                        </div>
                        <div class="modal-footer" style="border-top:0px;">
                        <button type="button" class="btn btn-primary" id="submitRecallReasonVIP">${Resources.Recall}</button>
                        <button type="button" class="btn btn-secondary" id="cancelRecallVIP" data-bs-dismiss="modal">${Resources.Cancel}</button>
                        </div>
                        </div>
                        </div>
                        </div>
                        `);
    // UI Production: The modal is dynamically created using a jQuery template literal and appended to the body.

    $('body').append(modal); // This body is the default screen html body, so we basically append this modal template into the screen content

    modal.modal('show');   

    modal.find('#submitRecallReasonVIP').on('click', function () {
        const textarea = modal.find('#recallReasonVIP');
        const recallVIP = textarea.val().trim();
        const errorMsg = textarea.siblings('.invalid-feedback');

        if (!recallVIP) {
            textarea.addClass('is-invalid');  // Adds red border
            errorMsg.show();  // Shows the error message
            modal.find('form').addClass('was-validated'); // Ensure Bootstrap applies styles
            return;
        }
        else {
            textarea.removeClass('is-invalid'); // Removes red border
            errorMsg.hide(); // Hides error message
            $("#closeRecallVIP").trigger("click");  //  triggering the close button to close the entire modal with its shadow
            callback(recallVIP);
            //  modal.modal('hide');
        }
    });
    // Remove validation styles on input change
    modal.find('#recallReasonVIP').on('input', function () {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').hide();
    });
    modal.find('#cancelRecallVIP').on('click', function () {
        modal.find('#recallReasonVIP').val('');
        $("#closeRecallVIP").trigger("click");
        gLocked = false;
    });

    modal.on('hidden.bs.modal', function () {
        modal.remove();
    });
}
function recall(id, delegationId) {
    openRecallReasonVIP(function (recallReasonVIP) {
        console.log("Recall reason received:", recallReasonVIP);

        //Common.showConfirmMsg(Resources.RecallConfirmation, function () {
        Common.ajaxPost('/Transfer/Recall',
            {
                'id': id, 'delegationId': delegationId, 'note': recallReasonVIP, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function () {
                gLocked = false;
                swal.close()
                TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                $($("input[data-id='" + id + "']").parents("li")[0]).fadeOut().remove();
                if (gSelectedRowId === id) {
                    $(".withBorders-o").addClass("waitingBackground");
                    $("#sentDocumentDetailsContainer").empty();
                }
            }, function () { gLocked = false; Common.showScreenErrorMsg(); }, false);
    }, function () { gLocked = false; });
}



function recallRows(id, delegationId) {
    Common.ajaxPost('/Transfer/Recall',
        {
            'id': id, 'delegationId': delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        function (result) {
            swal.close()
            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
            $($("input[data-id='" + id + "']").parents("li")[0]).fadeOut().remove();
            if (gSelectedRowId === id) {
                $(".withBorders-o").addClass("waitingBackground");
                $("#sentDocumentDetailsContainer").empty();
            }
        }, function () {
            gLocked = false; Common.showScreenErrorMsg();
        }, function () { gLocked = false; });
}

function loadSentList() {
    if (!gNoMoreData) {
        Common.mask(document.getElementById('sentListContainer'), "sentListContainer-mask");
        var params = {};
        addFilters(params);
        params.NodeId = gSelf.model.nodeId;
        params.DelegationId = gSelf.model.delegationId;
        params.start = gPageIndex;
        params.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
        params.FromStructure = fromStructureSent;
        Common.ajaxPost('/Transfer/ListSentVip', params, function (response) {
            if (response.length > 0) {
                gPageIndex += window.Paging;
                if (response.length < window.Paging) {
                    gNoMoreData = true;
                }
            } else {
                gNoMoreData = true;
            }
            createListData(response);
            gLocked = false;
            Common.unmask("sentListContainer-mask");
            if (gFromSearch) {
                $("#divSearchSent").fadeOut();
            }

        }, function () { gLocked = false; Common.showScreenErrorMsg(); });
    } else {
        gLocked = false;
    }
}
function createListData(data) {
    var html = '';
    if (data.length === 0 && gPageIndex === 0) {
        html = '<ul class="mdl-ul"><div class="nodata dataTables_empty pt-lg">' + Resources.NoDataToDisplay + '</div></ul>';
        $('#sentListContainer').html(html);
    } else if (data.length > 0) {
        html = '<ul class="mdl-ul" style="margin: 0px 5px 0 0;">';
        var htmlLi = "";
        var color = "";

        for (var i = 0; i < data.length; i++) {
            var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            var privacies = new CoreComponents.Lookup.Privacies().get(window.language);


            var transfer = data[i];
            if (window.PriorityPrivacyAction == "2") {
                for (var j = 0; j < privacies.length; j++) {
                    if (privacies[j].id === transfer.privacyId) {
                        color = privacies[j].color;
                        break;
                    }
                }
            } else {
                for (var j = 0; j < priorities.length; j++) {
                    if (priorities[j].id === transfer.priorityId) {
                        color = priorities[j].color;
                        break;
                    }
                }
            }
            var liClass = "mdl-li";
            if (!transfer.isRead) {
                liClass += " unread";
            }
            var htmlIcons = "";
            if (!transfer.isRead) {
                htmlIcons += " <button class='btn btn-xs btn-success mr-sm recallIcon' style='padding: 0px 3px;' title='" + Resources.Recall + "'><i class='fa fa-repeat fa-white'></i></button>";
            }
            if (transfer.importanceId) {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var j = 0; j < importances.length; j++) {
                    if (importances[j].id === transfer.importanceId) {
                        htmlIcons += "<i class='fa fa-exclamation fa-lg mr-sm' title='" + importances[j].text + "' style='color:" + importances[j].color + "'></i>";
                    }
                }
            }

            transfer.referenceNumber = transfer.referenceNumber ?? "";
            var to = transfer.toStructure !== "" ? transfer.toStructure + (transfer.toUser !== "" ? '/' + transfer.toUser : transfer.toUser) : transfer.toUser;
            htmlLi += '<li class="' + liClass + '" style="color:' + color + '">';  // Apply color to the entire list item
            htmlLi += '<div class="mdl-container" style="color:' + color + '">';  // Apply color to the entire container
            htmlLi += '<div id="leftbox" class="pull-left" style="color:' + color + '"> ';  // Apply color to leftbox
            htmlLi += '<div class="inside_color_line pull_left" style="color:' + color + '"></div>';  // Apply color to inside line
            if (transfer.subject != null && transfer.subject != '')
                htmlLi += '<input data-id=' + transfer.id + ' data-categoryid=' + transfer.categoryId + ' data-subject=' + transfer.subject + '  type="checkbox"  onclick="event.stopPropagation();" class="pointer m-sm ml-10px" style="margin-left:10px;"/>';  // Apply color to checkbox
            else
                htmlLi += '<input data-id=' + transfer.id + ' data-categoryid=' + transfer.categoryId + ' data-subject=""  type="checkbox"  onclick="event.stopPropagation();" class="pointer m-sm ml-10px" style="margin-left:10px;"/>';  // Apply color to checkbox

            htmlLi += "<input type='hidden' data-id=" + transfer.id + " value='" + JSON.stringify(transfer) + "'/>";
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px; color:" + color + "'><i class='fa fa-circle icon-primary'></i></span>";  // Apply color to span
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px; color:" + color + "'><i class='fa fa-arrow-left'></i></span>";  // Apply color to arrow-left icon
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px; color:" + color + "'><i class='fa fa-arrow-right'></i></span>";  // Apply color to arrow-right icon
            htmlLi += '</div>';
            htmlLi += '<div id="middlebox" class="mdl-text-msg pull-left pl-sm card-max-width" style="color:' + color + '">';  // Apply color to middlebox
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + transfer.referenceNumber + '">' + transfer.referenceNumber + '</span>';  // Apply color to reference number
            if (transfer.subject != null && transfer.subject != '')
                htmlLi += '<span class="mdl-span text-primary bold" style="color:' + color + '" title="' + transfer.subject + '">' + transfer.subject + '</span>';  // Apply color to subject
            else
                htmlLi += '<span class="mdl-span text-primary bold" style="color:' + color + '" title=""></span>';  // Apply color to subject

            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + transfer.fromStructure + '">' + transfer.fromStructure + '</span>';  // Apply color to fromStructure
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + to + '">' + to + '</span>';  // Apply color to to structure
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + transfer.purposeName + '">' + transfer.purposeName + '</span>';  // Apply color to purpose name
            htmlLi += '</div>';
            htmlLi += '<div id="rightbox" class="pull-right text-right" style="color:' + color + '"><div class="mdl-time mr-sm" title="' + Resources.TransferDate + '">' + dateFormat(transfer.transferDate) + '</div>';  // Apply color to rightbox and transfer date

            if (htmlIcons !== "") {
                htmlLi += '<div class="mdl-action">' + htmlIcons + '</div>';
            }
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</li>';
        }
        html += htmlLi;
        html += '</ul>';
        if (gPageIndex === 15) {
            $('#sentListContainer').html(html);
        } else {
            $('#sentListContainer ul').append(htmlLi);
        }
    }
}
function addFilters(d) {
    d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
    if (gFromSearch) {
        d.PriorityId = $("#cmbFilterSentPriority").val() !== null && typeof $("#cmbFilterSentPriority").val() !== "undefined" ? $("#cmbFilterSentPriority").val() : "0";
        d.PrivacyId = $("#cmbFilterSentPrivacy").val() !== null && typeof $("#cmbFilterSentPrivacy").val() !== "undefined" ? $("#cmbFilterSentPrivacy").val() : "0";
        d.PurposeId = $("#cmbFilterSentPurpose").val() !== null && typeof $("#cmbFilterSentPurpose").val() !== "undefined" ? $("#cmbFilterSentPurpose").val() : "0";
        d.CategoryId = $("#cmbFilterSentCategory").val() !== null && typeof $("#cmbFilterSentCategory").val() !== "undefined" ? $("#cmbFilterSentCategory").val() : "0";
        d.StatusId = $("#cmbFilterSentStatus").val() !== null && typeof $("#cmbFilterSentStatus").val() !== "undefined" ? $("#cmbFilterSentStatus").val() : "0";
        d.ReferenceNumber = $("#txtFilterSentReferenceNumber").val() !== "" && typeof $("#txtFilterSentReferenceNumber").val() !== "undefined" ? $("#txtFilterSentReferenceNumber").val() : "";
        d.Subject = $("#txtFilterSentSubject").val() !== "" && typeof $("#txtFilterSentSubject").val() !== "undefined" ? $("#txtFilterSentSubject").val() : "";
        d.FromDate = $("#filterSentFromDate").val() !== "" && typeof $("#filterSentFromDate").val() !== "undefined" ? $("#filterSentFromDate").val() : "";
        d.ToDate = $("#filterSentToDate").val() !== "" && typeof $("#filterSentToDate").val() !== "undefined" ? $("#filterSentToDate").val() : "";
        d.StructureIds = $("#cmbFilterSentStructure").val() !== null && typeof $("#cmbFilterSentStructure").val() !== "undefined" ? $("#cmbFilterSentStructure").val() : [];
        d.UserIds = $("#cmbFilterSentUser").val() !== null && typeof $("#cmbFilterSentUser").val() !== "undefined" ? $("#cmbFilterSentUser").val() : [];
    }
}
function dateFormat(dateText) {
    var dateFull = dateText.split(" ")[0].split("/");
    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth() + 1; //January is 0!
    var yy = today.getFullYear();
    var time = DateConverter.toHijriFormated(dateText.split(" ")[0], null, window.CalendarType);
    if (parseInt(dateFull[0]) === dd && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy) {
        time = "";
        var timeSeparator = ":";
        var hours = parseInt(dateText.split(" ")[1].split(":")[0]);
        var amPm = Resources.AM;
        if (hours > 12) {
            time += (hours - 12) + timeSeparator;
            amPm = Resources.PM;
        } else if (hours === 12) {
            time += "12" + timeSeparator;
            amPm = Resources.PM;
        } else {
            time += (hours < 10 ? '0' : '') + hours + timeSeparator;
            amPm = Resources.AM;
        }
        var minutes = parseInt(dateText.split(" ")[1].split(":")[1]);
        minutes = (minutes < 10 ? '0' : '') + minutes;
        time += minutes + " " + amPm;
    } else if (parseInt(dateFull[0]) === (dd - 1) && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy) {
        time = Resources.Yesterday;
    }
    return time;
}
var gLocked = false;
// This glocked boolean flag represents the green button of the locked and unlocked inbox/received Correspondence state or Sent Correspondences state
// Where when the 

var gNoMoreData = false;
var gFromSearch = false;
var gPageIndex = 0;
var gSelectedRowId, gSelf;
var wrapperParent;
var fromStructureSent = false;

class VipDocumentSentView extends Intalio.View {
    constructor(element, model) {
        wrapperParent = model;
        super(element, "vipsent", model);
    }
    render() {
        $.fn.select2.defaults.set("theme", "bootstrap");
        gSelf = this;
        fromStructureSent = gSelf.model.fromStructureSent;
        gLocked = false;
        gPageIndex = 0;
        gNoMoreData = false;
        gFromSearch = false;
        gSelectedRowId = null;
        var followUpCategoryIndex = gSelf.model.categories.findIndex(item => item.id == window.FollowUpCategory);
        gSelf.model.categories.splice(followUpCategoryIndex, 1);

        buildFilters(gSelf.model.nodeId, gSelf.model.categories);
        loadSentList();
        var lastScrollTop = 0;
        $('#sentListContainer').on('scroll', function () {
            var div = $(this);
            var st = div.scrollTop();
            if (st > lastScrollTop && st + div.innerHeight() + 5 >= div[0].scrollHeight) {
                if (!gLocked) {
                    gLocked = true;
                    try {
                        loadSentList();
                    } catch (e) {
                        gLocked = false;
                    }
                }
            }
            lastScrollTop = st;
        });

        $('#sentListContainer').on('click', 'li', function (e)  // Exceutes when any li is clicked, except when clicking on element that has a class .recallIcon or its parent having class .recallIcon
        {
            
            if ($(e.target).hasClass("recallIcon") || $(e.target).parent().hasClass("recallIcon")) {
                return; // Clicking on the recall icon will not trigger or return anything in this event handler.
                // Clicking anywhere inside <li> tag (but not on 'recallIcon' class) will exceute this event handler on any Sent Correpondence in the SentList, even if this list has a .recallIcon class inside it.
            }
            // This is useful if you want a general click event on list items. But we want to exclude specific elements inside the list like a button inside the list with class "recallIcon" from triggering the list click event on the Sent Correspondence List.
            // Executes when a <li> inside #sentListContainer having lists of sent Correspondences is clicked.

            if (!gLocked) {
                gLocked = true;
                try {
                    // Clicking anywhere inside <li> will trigger this handler except when clicking on an element of class "recallIcon" even inside the <li>
                    // Clicking on .recallIcon will not exceute the function
                    var input = this.getElementsByTagName('input')[0];
                    if (typeof input !== "undefined") {
                        if (!$(this).hasClass("active")) {
                            var id = input.getAttribute("data-id");
                            if (gSelectedRowId !== id) {
                                if (!$(".filterInfoDiv").hasClass("hidden")) {
                                    $(".filterInfoDiv").addClass("hidden");
                                }
                                openDocument(id, gSelf.model.delegationId, gSelf.model.nodeId);
                            } else {
                                gLocked = false;
                            }
                        } else {
                            gLocked = false;
                        }
                    } else {
                        gLocked = false;
                    }
                } catch (e) {
                    gLocked = false;
                }
            }
        });
        $('#sentListContainer').on('click', '.recallIcon', function () {
            // This event handler listens for a click event on any element with the class .recallIcon and Exceutes only if this recallIcon class is inside an html element of id >> #sentListContainer.
            // Then the function exceutes when element with class 'recallIcon' is clicked
            if (!gLocked) {
                gLocked = true;
                try {
                    // Listens for clicks on elements with class 'recallIcon' inside #sentListContainer
                    var input = $($(this).parents("li")[0]).find('input');
                    //var input = $(this).closest("li").find('input'); // Simplified lookup
                    //if (input.length) { // Ensure input exists
                    if (typeof input !== "undefined") {
                        var id = input.attr("data-id");
                        if (!$(".filterInfoDiv").hasClass("hidden")) {
                            $(".filterInfoDiv").addClass("hidden");
                        }
                        // Check if confirmation message is enabled
                        if (window.EnableConfirmationMessage === "True") {
                            Common.showConfirmMsg(Resources.RecallConfirmation, function () {
                                // confirm msg
                                recall(id, gSelf.model.delegationId);
                                gLocked = false;   // Once the Recall is done unlock the 
                            }, function () { // Handle cancel case (when the user cancels the confirmation dialog) >> reset the state to be unlocked
                                // cancel msg case
                                gLocked = false;  // to allow future clicks.
                            });
                        } else {
                            recall(id, gSelf.model.delegationId);
                        }

                    } else {
                        gLocked = false;  // Reset if input(sent correpondence) is not found
                    }
                } catch (e) {
                    gLocked = false; // Reset in case of error
                }
            }
        });

        $('#sentListContainer').on('click', 'input', function () {
            var input = this;
            if (typeof input !== "undefined") {
                input.checked = input.checked ? false : true;
            }
        });
        $('#chkAll').change(function () {
            var checked = $(this).prop('checked');
            $('#sentListContainer input[type="checkbox"]').prop('checked', checked);
        });
        $('#sentListContainer').on('change', 'input[type="checkbox"]:not(#chkAll)', function () {
            let allCheckboxes = $('#sentListContainer input[type="checkbox"]:not(#chkAll)');
            let allChecked = allCheckboxes.length === allCheckboxes.filter(':checked').length;

            $('#chkAll').prop('checked', allChecked);
        });
        $(document).click(function (e) {
            if ($(e.target).hasClass("select2-selection__choice__remove") || e.target.tagName.toLowerCase() === "body") {
                return;
            }
        });
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        SecurityMatrix.initContextMenuVip(securityMatrix, gSelf.model.nodeId);
        SecurityMatrix.initToolbarMenuVip(securityMatrix, gSelf.model.nodeId, 'sentListContainer');



        $('.filterInfoDiv').draggable({ containment: 'window', cancel: '.cancelDrag' });

        $("#btnOpenSearchSentModal").on("click", function () {
            if (window.language == "ar") {
                if ($(".vipDetailsPanel").css('display') == 'none') {
                    var position = $("#btnOpenSearchSentModal").position().left + $("#btnOpenSearchSentModal").width() + 970;
                    $(".filterInfoDiv").attr('style', 'right:' + position + 'px;top:170px;');
                }
                else
                    $(".filterInfoDiv").attr('style', 'right:' + $("#btnOpenSearchSentModal").position().right + 'px;top:170px;');
            }
            else {
                if ($(".vipDetailsPanel").css('display') == 'none') {
                    var position = $("#btnOpenSearchSentModal").position().left - 220;
                    $(".filterInfoDiv").attr('style', 'left:' + position + 'px;top:170px;');

                }
                else
                    $(".filterInfoDiv").attr('style', 'left:' + $("#btnOpenSearchSentModal").position().left + 'px;top:170px;');
            }
            if ($(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").removeClass("hidden");
            } else {
                $(".filterInfoDiv").addClass("hidden");
            }
        });

        $("#btnFilterCloseIcon").on("click", function () {
            $(".filterInfoDiv").addClass("hidden");
        });

        var actions = $.grep(securityMatrix.SecurityNodes[Number(gSelf.model.nodeId)].Actions, function (element, index) {
            return element.Type === Number(TypeAction.Toolbar);
        });
        if (actions.length === 0) {
            //$("#sentRow").addClass("hideCheckbox");
        }
        $('#filtersContainer input').off('keydown');
        $('#filtersContainer input').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 13) {
                $("#btnFilterSentSearch").trigger('click');
            }
        });
        $('.toggleVIP').on('click', function () {
            if (window.InboxMode === "LocalVIPView") {
                window.InboxMode = "InboxDefault";
            } else if (window.InboxMode !== "InboxDefault") {
                window.InboxMode = "LocalInboxDefaultView";
            }

            let wrapper = $(".content-wrapper");
            let defaultmodel = new DocumentSent.DocumentSent();
            defaultmodel.nodeId = wrapperParent.nodeId;
            defaultmodel.delegationId = wrapperParent.delegationId;
            defaultmodel.categories = new Categories().get(window.language, wrapperParent.delegationId);
            defaultmodel.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            defaultmodel.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            defaultmodel.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            defaultmodel.title = $('.content-heading').text();
            let documentView = new DocumentSent.DocumentSentView(wrapper, defaultmodel);
            documentView.render();
        })
    }
}
export default { VipDocumentSent, VipDocumentSentView };