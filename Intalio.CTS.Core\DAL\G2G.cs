using Aspose.Pdf;
using Intalio.CTS.Core.Model;
using Intalio.Core;
using Microsoft.EntityFrameworkCore;
using NPOI.OpenXmlFormats.Spreadsheet;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using NPOI.SS.Formula.Functions;
using MathNet.Numerics;
using static Org.BouncyCastle.Asn1.Cmp.Challenge;

namespace Intalio.CTS.Core.DAL
{
    public class G2G
    {

        public List<G2GDocumentModel> List(string ViewName, int startIndex, int pageSize, long userId, long structureId, bool isStructureReceiver,
          short privacyLevel, Expression<Func<G2GDocumentModel, bool>> filterExpression = null,
          List<SortExpression<G2GDocumentModel>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {

                var query = ctx.Set<G2GDocumentModel>().FromSqlRaw("SELECT * FROM " + ViewName);
                query = ApplyViewNameFilters(query, ViewName, userId, structureId, isStructureReceiver, privacyLevel);

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return query.Skip(startIndex).Take(pageSize).ToList();
            }
        }
        public static int GetG2GCount(string ViewName, long userId, long structureId, bool isStructureReceiver, short privacyLevel,
            Expression<Func<G2GDocumentModel, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var query = ctx.Set<G2GDocumentModel>().FromSqlRaw("SELECT * FROM " + ViewName);
                query = ApplyViewNameFilters(query, ViewName, userId, structureId, isStructureReceiver, privacyLevel);

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return query.Count();
            }
        }

        public static int GetG2GTodayCount(string ViewName, long userId, long structureId, bool isStructureReceiver, short privacyLevel, Expression<Func<G2GDocumentModel, bool>> filterExpression = null)
        {
            var today = DateTime.Today.Date;
            using (var ctx = new CTSContext())
            {
                var query = ctx.Set<G2GDocumentModel>().FromSqlRaw("SELECT * FROM " + ViewName);
                query = ApplyViewNameFilters(query, ViewName, userId, structureId, isStructureReceiver, privacyLevel);

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(x => x.TSF_DATE.HasValue && x.TSF_DATE.Value.Date == today);

                return query.Count();
            }
        }

        private static IQueryable<G2GDocumentModel> ApplyViewNameFilters(
     IQueryable<G2GDocumentModel> query, string ViewName, long userId, long structureId, bool isStructureReceiver, short privacyLevel)
        {
            if (ViewName.Contains("DocumentInboxRejected") || ViewName.Contains("DocumentInboxRecalled") || ViewName.Contains("DocumentInboxQueued") || ViewName.Contains("DocumentInboxSent"))
            {
                query = query.Where(x => x.Level <= privacyLevel && (
                                         (isStructureReceiver && x.TSF_TO_STRUCTURE.HasValue && structureId == x.TSF_TO_STRUCTURE.Value) ||
                                         (x.ExportedBy.HasValue && x.ExportedBy.Value == userId && x.TSF_TO_STRUCTURE.HasValue && structureId == x.TSF_TO_STRUCTURE.Value)));
            }
            else if (ViewName.Contains("DocumentInboxReceiveOrReject") || ViewName.Contains("DocumentInboxIncomingRejected") || ViewName.Contains("DocumentInboxIncomingRecalled"))
            {
                query = query.Where(x => x.Level <= privacyLevel && (
                                         (isStructureReceiver && x.SubGctIdRedirectTo.HasValue && structureId == x.SubGctIdRedirectTo.Value) ||
                                         ((x.SubGctIdTo.HasValue && structureId == x.SubGctIdTo.Value) || (x.TSF_TO_STRUCTURE.HasValue && structureId == x.TSF_TO_STRUCTURE.Value))));
            }
            else if (ViewName.Contains("DocumentInboxPendingToReceive"))
            {
                query = query.Where(x => x.Level <= privacyLevel);
            }
            else if (ViewName.Contains("DocumentInbox"))
            {
                query = query.Where(x => x.Level <= privacyLevel && (
                                         (isStructureReceiver && x.MainGctIdTo.HasValue && structureId == x.MainGctIdTo.Value) ||
                                         (isStructureReceiver && x.SubGctIdTo.HasValue && structureId == x.SubGctIdTo.Value) ||
                                         (isStructureReceiver && x.SubGctIdRedirectTo.HasValue && structureId == x.SubGctIdRedirectTo.Value) ||
                                         (isStructureReceiver && x.MainGctIdRedirectTo.HasValue && structureId == x.MainGctIdRedirectTo.Value)));
            }
            return query;
        }
    }
}
