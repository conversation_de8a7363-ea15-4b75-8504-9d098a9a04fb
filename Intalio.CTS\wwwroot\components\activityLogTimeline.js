import activityLogInfo from '../components/activityLogInfo.js'
//import ActivityLog from '../components/activityLogGrid.js'
//import { Categories, IdentityService, CategoryModel, Types, DelegationUsers } from '../components/lookup.js'

import Intalio from './common.js'
class ActivityLogTimeline extends Intalio.Model
{
    constructor()
    {
        super();
        this.statuses = null;
        this.documentId = null;
        this.delegationId = null;
        this.timelineOnly = false;
    }
}
var startIndex = 0;
var length = 15;
var ActivityLogDate;
function getActivityLogs(gSelfIndex,documentId, delegationId, statuses)
{
    var params = {
        start: startIndex, length: length, id: documentId
    };
    if (delegationId !== null)
    {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/ActivityLog/ListByDocumentId', params, function (response)
    {
        ActivityLogDate = response;

        if (response.length > 0)
        {
            var i, j, k;
            var draw = false;
            var itemPosition = 0;
            var listActions = [];
            var listPeriods = [];
            for (i = 0; i < response.length; i++) 
            {
                if (listActions.indexOf(response[i].action) === -1)
                {
                    listActions.push(response[i].action);
                }
                if (jQuery.grep(listPeriods, function (n, c) { return n.createdDate === DateConverter.toHijriFormated(response[i].createdDate, null, window.CalendarType); }).length === 0)
                {
                    listPeriods.push({ id: response[i].id, createdDate: DateConverter.toHijriFormated(response[i].createdDate, null, window.CalendarType) });
                }
            }

            var footer = '<li id="' + gSelfIndex.model.ComponentId + '_footer" title="' + Resources.LoadMore +
                '" class="timeline-end loadmore"><a class="timeline-badge pointer"><em class="fa fa-refresh"></em></a></li>';

            if (response.length < length)
            {
                footer = '<li id="' + gSelfIndex.model.ComponentId + '_footer" title="' + Resources.End +
                    '" class="timeline-end"><a class="timeline-badge"><em class="fa fa-close"></em></a></li>';
                $(gSelfIndex.refs['timelineUL']).addClass('loaded');
            }
            $(gSelfIndex.refs['footer']).remove();
            for (i = 0; i < listPeriods.length; i++)
            {
                var dateBox = '<li id="' + gSelfIndex.model.ComponentId + '_period_' + listPeriods[i].id + '" data-datetime="' + listPeriods[i].createdDate + '" class="timeline-separator"></li>';
                $(gSelfIndex.refs['timelineUL']).append(dateBox);
                for (j = 0; j < listActions.length; j++)
                {
                    draw = false;
                    let html = '<div><table style="width:100%"><tbody>';
                    for (k = 0; k < response.length; k++)
                    {
                        if (response[k].action === listActions[j] && DateConverter.toHijriFormated(response[k].createdDate, null, window.CalendarType) === listPeriods[i].createdDate)
                        {
                            draw = true;
                            html += '<tr style="height:16px">';
                            //html += '<td id="' + gSelfIndex.model.ComponentId + '_timeLineItem' + response[k].id + '"><div id="xmpl-3" class="example ddd-truncated"><p data-id="'
                            //    + gSelfIndex.model.ComponentId + '_' + response[k].user + '">' + response[k].user + ' <span class="time">' + response[k].note + '</span>'+
                            //    ' <span class="time pull-right">' + response[k].time + '</span></p></div></td></tr>';

                            //html += '<td id="' + gSelfIndex.model.ComponentId + '_timeLineItem' + response[k].id + '">' +
                            //    '<div id="xmpl-3" class="example ddd-truncated">' +
                            //    '<p data-id="' + gSelfIndex.model.ComponentId + '_' + response[k].user + '">' +
                            //    response[k].user +
                            //    ' <span class="time">' + response[k].note + '</span>' +
                            //    ' <span class="time pull-right">' + response[k].time + '</span>' +
                            //    '</p>' +
                            //    '<button class="btn-action" onclick="handleClickAction(' + response[k].id + ')">Action</button>' +
                            //    '</div></td></tr>';
                         
                            var note = ""
                            if (response[k].note.length < 20)
                                note = response[k].note;

                            html += '<td class="btn-action-activitylog" style="cursor: pointer; padding:5px;"  id="' + gSelfIndex.model.ComponentId + '_timeLineItem-' + response[k].id +'">' +
                              
                                '<div id="xmpl-3" class="example ddd-truncated">' +
                                '<p data-id="' + gSelfIndex.model.ComponentId + '_' + response[k].user + '">' +response[k].user +
                               
                                '</p>' +
                                ' <span class="time">' + note + '</span>' +
                                ' <span class="time pull-right">' + response[k].time + '</span>' +
                                '</div></td></tr>';


                        }
                    }
                    html += '</tbody></table></div>';
                    if (draw === true)
                    {
                        var li = '<li id="' + gSelfIndex.model.ComponentId + '_roadmap_' + listPeriods[i].id + "_" + listActions[j] + '">';
                        for (var x = 0; x < statuses.length; x++)
                        {
                            if (statuses[x].id === response[j].statusId)
                            {
                                li += '<div class="timeline-badge" style="background-color:' + (statuses[x].color !== null ? statuses[x].color : "#27c24c") + '" title="' + statuses[x].text + '"><em class="fa fa-hourglass-2"></em></div>';
                            }
                        }

                        //li += '<div class="timeline-panel"><div class="popover"><h4 class="popover-title">' + listActions[j] + '</h4>' +
                        //    '<div class="popover-content"><p>' + html + '</p></div></div></div></li>';

                        li += '<div class="timeline-panel"><div class="popover"><h4 class="popover-title">' + listActions[j] + '</h4>' +
                            '<div class="popover-content"><p>' + html + '</p>' +
                           ' </div></div></div></li>';




                        $(gSelfIndex.refs['timelineUL']).append(li);
                        itemPosition++;
                    }
                    if (itemPosition % 2 === 0)
                        $('#' + gSelfIndex.model.ComponentId + '_roadmap_' + listPeriods[i].id + "_" + listActions[j]).addClass('timeline-inverted');
                }
            }
            $(gSelfIndex.refs['timelineUL']).append(footer);
        } else
        {
            $(gSelfIndex.refs['footer']).remove();
            $(gSelfIndex.refs['timelineUL']).append('<li id="' + gSelfIndex.model.ComponentId + '_footer" title="' + Resources.End +
                '" class="timeline-end"><a class="timeline-badge"><em class="fa fa-close"></em></a></li>');
            $(gSelfIndex.refs['timelineUL']).addClass('loaded');
        }

        EventReceiver.ActivityLogAfterRender(gSelfIndex);
    });
}

function getActivityLogsInfo(documentId, delegationId, statuses) {
    var params = {
        start: startIndex, 
        length: length,   
        id: documentId
    };

 

    Common.ajaxGet('/ActivityLog/ListByDocumentId', params, function (response) {
        var wrapper = $(".modal-window");
        var model = new activityLogInfo.ActivityLogInfo();

        var ActivityLogInfo = new activityLogInfo.ActivityLogInfoView(wrapper, model);

        ActivityLogInfo.render();
        $('#modalActivityLogInfo').modal('show');
        $("#modalActivityLogInfo").off("hidden.bs.modal");
        $("#modalActivityLogInfo").off("shown.bs.modal");
        $("#modalActivityLogInfo").on('shown.bs.modal', function () {
        });
        $('#modalActivityLogInfo').on('hidden.bs.modal', function () {
            $('#modalActivityLogInfo').remove();
        });

    

    });
}


function handleClickAction(id) {
    var parts = id.split('-');
    var lastPart = parts[parts.length - 1];

    return lastPart;
}



class ActivityLogTimelineView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "activitylogtimeline", model);
    }
    render()
    {
        var gSelfIndex = this;
        // make activityLog tab reload every time 
        var tabs = $("div[ref='" + gSelfIndex.model.parentComponentId + "'] ul[ref='tabDocumentDetails'] li a")
        var refreshtab = $.grep(tabs, function (element) {
            return $(element).data("id") == gSelfIndex.model.tabId;
        })[0];

        $(refreshtab).data("loaded", false)
        $(refreshtab).data("customloaded", false)

        startIndex = 0;
        $.fn.select2.defaults.set("theme", "bootstrap");

        getActivityLogs(gSelfIndex,gSelfIndex.model.documentId, gSelfIndex.model.delegationId, gSelfIndex.model.statuses);
        $(document).off("click", ".loadmore");
        $(document).on("click", ".loadmore", function ()
        {
            if (!$(gSelfIndex.refs['timelineUL']).hasClass('loaded'))
            {
                startIndex += length;
                getActivityLogs(gSelfIndex,gSelfIndex.model.documentId, gSelfIndex.model.delegationId, gSelfIndex.model.statuses);
            }
        });

        $(document).on('click', '.btn-action-activitylog', function () {

            var id = this.id;
            id = handleClickAction(id);
            var item = $.grep(ActivityLogDate, function (element) { return element.id == id });
            
            var wrapper = $(".modal-window");
            var model = new activityLogInfo.ActivityLogInfo();

            model.note = item[0].note;
            model.action = item[0].action;
            model.originalValue = item[0].originalValue;
            model.newValue = item[0].newOriginalValue;
            var ActivityLogInfo = new activityLogInfo.ActivityLogInfoView(wrapper, model);

            ActivityLogInfo.render();

            $('#modalActivityLogInfo').modal('show');
            $("#modalActivityLogInfo").off("hidden.bs.modal");
            $("#modalActivityLogInfo").off("shown.bs.modal");
            $("#modalActivityLogInfo").on('shown.bs.modal', function () {
            });
            $('#modalActivityLogInfo').on('hidden.bs.modal', function () {
                $('#modalActivityLogInfo').remove();
            });
   
        });
        $('#' + gSelfIndex.model.ComponentId + '_btnActivityLogGrid').click(function () {
            
            $(gSelfIndex.refs[gSelfIndex.model.ComponentId]).hide();
            $(gSelfIndex.refs['activityLogGridActionsContainer']).show();
            $(gSelfIndex.refs['activityLogGridContainer']).show();
            $(gSelfIndex.refs['btnActivityLogTimeline']).show();
            $(gSelfIndex.refs['btnActivityLogGrid']).hide();


        });
        $('#' + gSelfIndex.model.ComponentId + '_btnActivityLogTimeline').click(function () {

            $(gSelfIndex.refs[gSelfIndex.model.ComponentId]).show();
            $(gSelfIndex.refs['btnActivityLogGrid']).show();
            $(gSelfIndex.refs['activityLogGridActionsContainer']).hide();
            $(gSelfIndex.refs['activityLogGridContainer']).hide();
            $(gSelfIndex.refs['btnActivityLogTimeline']).hide();


        });

        $(gSelfIndex.refs[ gSelfIndex.model.ComponentId + '_btnSearch']).on('click', function () {
            table.ajax.reload();
        });
        $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_btnClear"]).on('click', function () {
            
            $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_cmbActivityLogGridAction"]).val('').trigger('change');
            table.ajax.reload();
        });
            Common.ajaxGet('/ActivityLog/ListActions', null, function (data) {
                $(gSelfIndex.refs[gSelfIndex.model.ComponentId + '_cmbActivityLogGridAction']).select2({
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    width: "100%",
                    allowClear: false,
                    placeholder: "",
                    dropdownParent: $(gSelfIndex.refs[gSelfIndex.model.ComponentId + '_activityLogGridActionContainer']),
                    data: data,
                });
                $(gSelfIndex.refs[gSelfIndex.model.ComponentId + '_cmbActivityLogGridAction']).val('').trigger('change');
            });
        
        $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_btnClear"]).keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $(gSelfIndex.refs[gSelfIndex.model.ComponentId + '_btnSearch']).focus();
                }
                else {
                    $(gSelfIndex.refs[gSelfIndex.model.ComponentId + '_cmbActivityLogGridAction']).focus();
                }
            }
        });
        Common.gridCommon();
        
        let table = $(gSelfIndex.refs[gSelfIndex.model.ComponentId + '_grdItems']).on('draw.dt',
            function () {
                $(gSelfIndex.refs[gSelfIndex.model.ComponentId + '_grdItems']).find('tbody tr td').each(function () {
                    this.setAttribute('title', $(this).text());
                });
            }).DataTable({
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/ActivityLog/LisActivityLogGridtByDocumentId",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d) {

                        d.DelegationId = gSelfIndex.model.delegationId == undefined ? null : gSelfIndex.model.delegationId;
                        d.DocumentId = gSelfIndex.model.documentId;
                        d.ActivityLogActionId = $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_cmbActivityLogGridAction"]).val() !== null && typeof $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_cmbActivityLogGridAction"]).val() !== "undefined" ? $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_cmbActivityLogGridAction"]).val() : "0";
                        return d;
                       
                    }
                },
                "order": [],
                "columns": [
                    { title: "Id", data: "id", visible: false, "orderable": false },
                   
                    { title: Resources.User, data: "user", render: $.fn.dataTable.render.text(), "orderable": false },
                    
                    { title: Resources.Action, data: "action", "orderable": true, orderSequence: ["asc", "desc"], width: "150px" },
                    {
                        title: Resources.CreatedDate, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                        render: function (data, type, full, meta) {
                            return DateConverter.toHijriFormated(full.createdDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                        }
                    },
                    { title: Resources.Note, data: "note", render: $.fn.dataTable.render.text(), "orderable": false },

                ],
                "fnInitComplete": function (settings, json) {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons "B>ltr<"pull-left"p><"pull-right"i>',
                buttons: [{
                    extend: 'excelHtml5',
                    className:'btn-excel',
                    action: function (e, dt, node, config) {
                        var tableData = dt.rows({ search: 'applied' }).data().toArray();
                        var referenceNumber = tableData.length > 0 ? tableData[0].referenceNumber : 'default_filename';
                        var subject = tableData.length > 0 ? tableData[0].subject : 'default_filename';
                        const order = dt.order();
                        let columnIndex = 0;
                        let direction = 'desc';

                        if (order.length > 0) {
                            columnIndex = order[0][0];
                            direction = order[0][1];
                        }

                        const columnName = dt.settings()[0].aoColumns[columnIndex].data;

                        fetch('/Export/ExportActivityLogGrid', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                format: 'excel', activityLogActionId: $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_cmbActivityLogGridAction"]).val() !== null && typeof $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_cmbActivityLogGridAction"]).val() !== "undefined" ? $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_cmbActivityLogGridAction"]).val() : "0", documentId: gSelfIndex.model.documentId, delegationId: gSelfIndex.model.delegationId, orderColumn: columnName, orderDir: direction
}) 
                        })
                            .then(response => response.blob()) 
                            .then(blob => {
                                const url = window.URL.createObjectURL(blob);
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = referenceNumber ? referenceNumber + '_' + new Date().toISOString() + '.xlsx' : subject + '_' + new Date().toISOString() + '.xlsx'; 
                                a.click();
                                window.URL.revokeObjectURL(url);
                            })
                            .catch(error => console.error('Error exporting to Excel:', error));
                    }
                }, {
                    extend: 'pdfHtml5',
                    className: 'btn-pdf',
                        action: function (e, dt, node, config) {
                            var tableData = dt.rows({ search: 'applied' }).data().toArray(); 
                            var referenceNumber = tableData.length > 0 ? tableData[0].referenceNumber : 'default_filename';
                            var subject = tableData.length > 0 ? tableData[0].subject : 'default_filename';
                            const order = dt.order();
                            let columnIndex = 0;
                            let direction = 'desc';

                            if (order.length > 0) {
                                columnIndex = order[0][0];
                                direction = order[0][1];
                            }

                            const columnName = dt.settings()[0].aoColumns[columnIndex].data;

                            fetch('/Export/ExportActivityLogGrid', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ format: 'pdf', activityLogActionId: $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_cmbActivityLogGridAction"]).val() !== null && typeof $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_cmbActivityLogGridAction"]).val() !== "undefined" ? $(gSelfIndex.refs[gSelfIndex.model.ComponentId + "_cmbActivityLogGridAction"]).val() : "0", documentId: gSelfIndex.model.documentId, delegationId: gSelfIndex.model.delegationId, orderColumn: columnName, orderDir: direction }) 
                            })
                                .then(response => response.blob())
                                .then(blob => {
                                    const url = window.URL.createObjectURL(blob);
                                    const a = document.createElement('a');
                                    a.href = url;
                                    a.download = referenceNumber ? referenceNumber + '_' + new Date().toISOString() + '.pdf' : subject +'_'+ new Date().toISOString() + '.pdf'; 
                                    document.body.appendChild(a);
                                    a.click();
                                    window.URL.revokeObjectURL(url);
                                    document.body.removeChild(a); 
                                })
                                .catch(error => console.error('Error exporting to pdf:', error));
                        }
                    }]
            });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
    }


}
export default { ActivityLogTimeline, ActivityLogTimelineView };
