﻿using Intalio.Core.Interfaces;
using Intalio.Core.Model;
using Intalio.CTS.Core.Model;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Intalio.CTS.Core.DAL
{
    /// <summary>
    /// Favorite Structure Model
    /// </summary>
    public class FavoriteStructure : IDbObject<FavoriteStructure>
    {
        public long Id { get; set; }

        public DateTime CreatedDate { get; set; }
        public long CreatedByUserId { get; set; }
        public long? ToUserId { get; set; }

        public long? ToStructureId { get; set; }
        public long OrderId { get; set; }
        public long? DistributionId { get; set; }
        public virtual Structure ToStructure { get; set; }
        public virtual Distribution Distribution { get; set; }

        public void Delete(long id)
        {
            using (var ctx = new CTSContext())
            {
                FavoriteStructure item = new FavoriteStructure { Id = id };
                ctx.FavoriteStructure.Attach(item);
                ctx.FavoriteStructure.Remove(item);
                ctx.SaveChanges();
            }
        }
        public void Delete()
        {
            Delete(Id);
        }

        public FavoriteStructure Find(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FavoriteStructure.AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }
        public List<FavoriteStructure> FindFavoriteDistribution(long distributionId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FavoriteStructure.AsNoTracking()
                    .Where(
                    t => t.DistributionId == distributionId)?.ToList();
            }
        }

        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                CreatedDate = DateTime.Now;
                ctx.FavoriteStructure.Add(this);
                ctx.SaveChanges();
            }
        }

        public void Update()
        {
            using (var ctx = new CTSContext())
            {
                ctx.Entry(this).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }
        public List<FavoriteStructure> FindAll(long userId, List<long> structureIds, bool enableTransferToUsers)
        {
            using (var ctx = new CTSContext())
            {
                if (enableTransferToUsers)
                    return ctx.FavoriteStructure
                        .Include(x => x.ToStructure)
                        .Include(x => x.Distribution)
                        .ThenInclude(x => x.DistributionStructures)
                        .AsNoTracking().Where(
                        t => t.CreatedByUserId == userId &&

                         (
                             (t.DistributionId == null &&
                             structureIds.Contains(t.ToStructureId.Value))

                        ||

                            (t.DistributionId != null &&
                            t.Distribution.DistributionStructures.Any(x =>
                            structureIds.Contains(x.ToStructureId)))
                        )
                        )?.ToList() ?? new List<FavoriteStructure>();
                else
                    return ctx.FavoriteStructure
                        .Include(x => x.Distribution)
                        .ThenInclude(x => x.DistributionStructures)
                        .AsNoTracking().Where(
                        t => t.CreatedByUserId == userId &&

                         (
                             (t.DistributionId == null &&
                             structureIds.Contains(t.ToStructureId.Value) &&
                            ((!enableTransferToUsers && t.ToUserId == null)))

                        ||

                            (t.DistributionId != null &&
                            t.Distribution.DistributionStructures.Any(x =>
                            structureIds.Contains(x.ToStructureId) &&
                            ((!enableTransferToUsers && x.ToUserId == null))))
                        )
                        )?.ToList() ?? new List<FavoriteStructure>();
            }
        }
        public List<FavoriteStructure> FindAll(long userId, bool enableTransferToUsers)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FavoriteStructure
                    .Include(x => x.ToStructure)
                    .Include(x => x.Distribution)
                    .ThenInclude(x => x.DistributionStructures)
                    .AsNoTracking().Where(
                    t => t.CreatedByUserId == userId &&
                    (
                    (t.DistributionId == null && (!enableTransferToUsers && t.ToUserId == null) || enableTransferToUsers)
                    ||
                    (t.DistributionId != null &&
                    (!enableTransferToUsers && t.Distribution.DistributionStructures.Any(x => x.ToUserId == null)) || enableTransferToUsers)

                    )


                    )?.ToList() ?? new List<FavoriteStructure>();
            }
        }
        public FavoriteStructure Find(long userId, FavoriteStructureModel favoriteStructure)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FavoriteStructure.Include(x => x.ToStructure).AsNoTracking().FirstOrDefault(t =>
                t.CreatedByUserId == userId &&
                t.ToStructureId == favoriteStructure.ToStructureId &&
                t.ToUserId == favoriteStructure.ToUserId &&
                favoriteStructure.DistributionId == t.DistributionId);
            }
        }
        public FavoriteStructure FindWithOrder(long userId, FavoriteStructureModel favoriteStructure)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.FavoriteStructure.AsNoTracking().FirstOrDefault(t =>
                t.CreatedByUserId == userId && t.OrderId == favoriteStructure.Order);
            }
        }


    }
}
