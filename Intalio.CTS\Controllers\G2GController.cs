using Aspose.Pdf.Operators;
using Intalio.Core;
using Intalio.Core.Model;
using Intalio.Core.UI.Filters;
using Intalio.CTS.Core;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using Intalio.CTS.Filters;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
namespace Intalio.CTS.Controllers
{
    [Route("[controller]/[action]")]
    public class G2GController : BaseController
    {
        #region Fields

        private readonly IHubContext<CommunicationHub> _hub;

        #endregion

        #region Ctor

        public G2GController(IHubContext<CommunicationHub> hub)
        {
            _hub = hub;
        }

        #endregion


        #region Ajax

        /// <summary>
        /// List G2G transfers
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<G2GListViewModel>), 200)]
        //[ValidateAntiForgeryToken]
        public JsonResult List([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                string NodeName = Request.Form["ViewName"].Count > 0 ? Convert.ToString(Request.Form["ViewName"][0]) : string.Empty;
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                long? delegationId = null;
                if (Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]))
                {
                    delegationId = Convert.ToInt64(Request.Form["DelegationId"][0]);
                }
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                short priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt16(Request.Form["PriorityId"][0]) : default;
                short privacyId = Request.Form["PrivacyId"].Count > 0 ? Convert.ToInt16(Request.Form["PrivacyId"][0]) : default;
                short purposeId = Request.Form["PurposeId"].Count > 0 ? Convert.ToInt16(Request.Form["PurposeId"][0]) : default;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt16(Request.Form["StructureId"][0]) : default;
                List<long> structureIds = Request.Form["StructureIds[]"].Count > 0 ? Request.Form["StructureIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                List<long> userIds = Request.Form["UserIds[]"].Count > 0 ? Request.Form["UserIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();

                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Json(new
                    {
                        draw,
                        recordsTotal = 0,
                        recordsFiltered = 0,
                        data = new List<G2GListViewModel>()
                    });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (categoryId != default)
                {
                    filter.Add("DOC_CATEGORY", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("REFERENCE", referenceNumber, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("SUBJECT", subject, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("RegisteredDateFrom", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("RegisteredDateFrom", to.Date, Operator.LessThanOrEqualTo);
                }

                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Category" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "SUBJECT" });
                            break;
                        case "transferDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "TSF_DATE" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "TSF_CLOSURE_DATE" });
                }
                var retValue = (0, new List<G2GListViewModel>());
                retValue = ManageG2G.List(NodeName, start, length, UserId, StructureId, IsStructureReceiver, PrivacyLevel, delegationId, filter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex, draw);
            }
        }



        /// <summary>
        /// Get g2g transfer count
        /// </summary>
        /// <param name="ViewName">Node id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <param name="loggedInStructureId">loggedIn Structure Id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetG2GCounts(string NodeName, string ViewName, long? delegationId, long? loggedInStructureId)
        {
            try
            {
                (int Total, int Today, int NodeId) retValue = ManageG2G.GetG2GCounts(NodeName, UserId, StructureId, loggedInStructureId, IsStructureReceiver, PrivacyLevel, delegationId, null);
                return Ok(new { retValue.Total, retValue.Today, retValue.NodeId });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        [HttpPost]
        public IActionResult GetG2GbulkActionGuid(string viewName, string ids, string type)
        {
            var result = ManageG2G.GenerateGuidBulkActions(viewName, ids, type, UserId);
            return Ok(result);
        }
        #endregion

    }
}
