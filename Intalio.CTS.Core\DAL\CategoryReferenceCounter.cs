﻿using Intalio.Core;
using Intalio.Core.Interfaces;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.Model;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.DAL
{
    public partial class CategoryReferenceCounter : IDbObject<CategoryReferenceCounter>, IDisposable
    {
        #region Private Fields

        private CTSContext _ctx;

        #endregion

        #region Fields

        private const string MSSQL = @"Exec usp_IncrementReferenceCounter {0},{1}";
        private const string Oracle = @"SELECT * from fn_IncrementReferenceCounter({0},'{1}');";
        private const string Postgresql = @"SELECT * from public.fn_incrementreferencecounter({0},'{1}');";

        #endregion

        #region Properties

        public int Id { get; set; }
        public string Name { get; set; }
        public int Value { get; set; }
        public bool? Active { get; set; }
        public bool ResetByYear { get; set; }
        public long CreatedByUserId { get; set; }
        public DateTime CreatedDate { get; set; }

        #endregion

        #region Private Methods

        private void OpenDbContext()
        {
            if (_ctx == null)
            {
                _ctx = new CTSContext();
            }
        }

        #endregion

        #region Public Methods

        public void Delete(long id)
        {
            using (var ctx = new CTSContext())
            {
                var item = new CategoryReferenceCounter { Id = (int)id };
                ctx.CategoryReferenceCounter.Attach(item);
                ctx.CategoryReferenceCounter.Remove(item);
                ctx.SaveChanges();
            }
        }

        public void Delete(List<int> ids)
        {
            using (var ctx = new CTSContext())
            {
                foreach (var id in ids)
                {
                    var item = new CategoryReferenceCounter { Id = id };
                    ctx.CategoryReferenceCounter.Attach(item);
                    ctx.CategoryReferenceCounter.Remove(item);
                }
                ctx.SaveChanges();
            }
        }

        public void Delete()
        {
            this.Delete(this.Id);
        }

        public CategoryReferenceCounter Find(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.CategoryReferenceCounter.AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        public CategoryReferenceCounter FindByName(string name)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.CategoryReferenceCounter.AsNoTracking().FirstOrDefault(x => x.Name.ToLower() == name.ToLower());
            }
        }

        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                CreatedDate = DateTime.Now;
                ctx.CategoryReferenceCounter.Add(this);
                ctx.SaveChanges();
            }
        }

        public void Update()
        {
            using (var ctx = new CTSContext())
            {
                ctx.Entry(this).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        public List<CategoryReferenceCounter> List()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.CategoryReferenceCounter.ToList();
            }
        }

        public List<CategoryReferenceCounter> ListWithCaching()
        {
            List<CategoryReferenceCounter> retValue = new CacheUtility().GetCachedItem<List<CategoryReferenceCounter>>(typeof(CategoryReferenceCounter).Name);
            if (retValue == null)
            {
                retValue = new CacheUtility().InsertCachedItem<List<CategoryReferenceCounter>>(List(), typeof(CategoryReferenceCounter).Name);
            }
            return retValue;
        }

        public bool CheckIfExists(string name)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.CategoryReferenceCounter.AsNoTracking().Any(t => t.Name.ToLower() == name.ToLower());
            }
        }

        public void UpdateValue()
        {
            using (var ctx = new CTSContext())
            {
                ctx.Entry(this).Property(x => x.Value).IsModified = true;
                ctx.SaveChanges();
            }
        }

        public List<CategoryReferenceCounter> List(int startIndex, int pageSize, Expression<Func<CategoryReferenceCounter, bool>> filterExpression = null,
            List<SortExpression<CategoryReferenceCounter>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<CategoryReferenceCounter> query = ctx.CategoryReferenceCounter.AsNoTracking();
                // .Include(t => t.Category);

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public Task<int> GetCount(Expression<Func<CategoryReferenceCounter, bool>> filterExpression = null)
        {
            OpenDbContext();
            IQueryable<CategoryReferenceCounter> query = _ctx.CategoryReferenceCounter.AsNoTracking();
            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            return query.CountAsync();
        }

        public List<CategoryReferenceCounter> ListByIds(List<int> ids)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.CategoryReferenceCounter.Where(t => ids.Contains(t.Id)).ToList();
            }
        }


        public int GetCounter(int categoryId, string counterName)
        {
            int retValue = 0;
            var query = string.Empty;
            counterName = "'" + counterName + "'";
            switch (Configuration.DatabaseType)
            {
                case DatabaseType.MSSQL: query = string.Format(MSSQL, categoryId, counterName); break;
                case DatabaseType.PostgreSQL: query = string.Format(Postgresql, categoryId, counterName); break;
                case DatabaseType.Oracle: query = string.Format(Oracle, categoryId, counterName); break;
            }
            using (var ctx = new CTSContext())
            {
                retValue = ctx.CategoryReferenceCounter.FromSqlRaw(query).AsEnumerable().First().Value;
            }
            return retValue;
        }
        public int GetCounterForPreview(string counterName)
        {
            int retValue = 0;
            
           
            using (var ctx = new CTSContext())
            {
                retValue = ctx.CategoryReferenceCounter.FirstOrDefault(c=>c.Name== counterName && c.Active==true).Value;
            }
            return retValue +1;
        }
        public (int Sequence, string Reference) Generate(short categoryId, long userId, long? StructureId,long? DocumentId,long? transferId, Intalio.Core.Language language, bool forReview = false)
        {
            var forPreview = forReview;
            var referenceConfiguration = ManageCategoryReferenceNumber.GetByCategoryId(categoryId);
            StringBuilder sb = new StringBuilder();
            int sequence = 0;
            var orderedReferenceConfiguration = referenceConfiguration.OrderBy(t => t.Order).ToList();
            foreach (var item in orderedReferenceConfiguration)
            {
                switch (item.ReferenceNumberType)
                {
                    case ReferenceNumberType.Year:
                        sb.Append(Intalio.Core.Helper.ConvertGregorianToHijriDate(DateTime.Now).Year); break;
                    case ReferenceNumberType.Month:
                        sb.Append(Intalio.Core.Helper.ConvertGregorianToHijriDate(DateTime.Now).Month); break;
                    case ReferenceNumberType.String:
                        sb.Append(item.Content); break;
                    case ReferenceNumberType.Separator:
                        sb.Append(item.Content); break;
                    case ReferenceNumberType.Counter:

                        sequence= forPreview ==false ? GetCounter(categoryId, item.Content): GetCounterForPreview(item.Content);
                        
                        if (sequence != default(int))
                        {
                            sb.Append(sequence.ToString().PadLeft(Configuration.ReferenceCounterLength, '0'));
                        }
                        else
                        {
                            var beforeElement = orderedReferenceConfiguration.ElementAtOrDefault(orderedReferenceConfiguration.IndexOf(item) - 1);
                            if (beforeElement != null && beforeElement.ReferenceNumberType == ReferenceNumberType.Separator)
                            {
                                sb.Remove(sb.Length - 1, 1);
                            }
                        }
                        break;
                    case ReferenceNumberType.StructureCode:
                        var StructureCode = Intalio.CTS.Core.API.ManageStructure.GetStructure(StructureId.Value, language);
                        if (StructureCode != null)
                            sb.Append(StructureCode.Code);
                        break;
                    case ReferenceNumberType.Dynamic:
                        var result = Intalio.CTS.Core.DAL.Document.GetDynamicReferenceNumberTypeContent(item.Content, userId, StructureId, DocumentId, transferId);
                        if (result != null && result!="")
                            sb.Append(result);
                        break;
                    case ReferenceNumberType.DynamicProcedure:
                        var resultDynamicProcedure = Intalio.CTS.Core.DAL.Document.ExecuteDynamicStoredProcedure(item.Content, userId, StructureId, DocumentId, transferId);
                        if (resultDynamicProcedure != null && resultDynamicProcedure != "")
                            sb.Append(resultDynamicProcedure);
                        break;
                }
            }
            return (sequence, sb.ToString());
        }

        #endregion

        #region Dispose

        public void Dispose()
        {
            if (_ctx != null)
            {
                _ctx.Dispose();
            }
        }

        #endregion

        #region Conversion

        public static implicit operator CategoryReferenceCounterListViewModel(CategoryReferenceCounter item)
        {
            CategoryReferenceCounterListViewModel retValue = null;
            if (item != null)
            {
                retValue = new CategoryReferenceCounterListViewModel
                {
                    Id = item.Id,
                    Name = item.Name,
                    Value = item.Value,
                    Editable = !item.ResetByYear ? true : item.CreatedDate.Year == DateTime.Now.Year,
                    Active = Convert.ToBoolean(item.Active),
                    CreatedDate = item.CreatedDate.ToString(Constants.DATE_FORMAT)
                };
            }
            return retValue;
        }

        #endregion
    }
}
