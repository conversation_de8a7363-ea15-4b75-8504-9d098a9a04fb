<div ref="{{ComponentId}}">
    <div ref="followUpDetailsContainer" class="row">
        <div class="col-lg-4">
            <div id="followUpInfo" class="tab-pane">
                <div class="panel panel-default borderTop-1">
                    <div class="panel-body">

                        <div>
                            <h4>
                                {{Localizer 'FollowUpDetails'}}
                                {{#unless readonly}}
                                <button ref="btnEditFollowUp" title="{{Localizer 'Edit'}}" class="btn btn-xs btn-primary">{{Localizer 'Edit'}}</button>
                                {{/unless}}
                            </h4>
                        </div>
                        <hr class="metadata-hr" />
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group col-md-6 col-xs-4">
                                    <label class="control-label">{{Localizer 'FollowUpSendingEntity'}}</label>
                                </div>
                                <div class="form-group col-md-6 col-xs-8 break-word-class">
                                    <div ref="followUpSendingEntity"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group col-md-6 col-xs-4">
                                    <label class="control-label">{{Localizer 'FollowUpReceivingEntity'}}</label>
                                </div>
                                <div class="form-group col-md-6 col-xs-8 break-word-class">
                                    <div ref="followUpReceivingEntity"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group col-md-6 col-xs-4">
                                    <label class="control-label">{{Localizer 'FollowUpPriority'}}</label>
                                </div>
                                <div class="form-group col-md-6 col-xs-8 break-word-class">
                                    <div ref="followUpPriority"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group col-md-6 col-xs-4">
                                    <label class="control-label">{{Localizer 'FollowUpSubject'}}</label>
                                </div>
                                <div class="form-group col-md-6 col-xs-8 break-word-class">
                                    <div ref="followUpSubject"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group col-md-6 col-xs-4">
                                    <label class="control-label">{{Localizer 'FollowUpIsPrivate'}}</label>
                                </div>
                                <div class="form-group col-md-6 col-xs-8 break-word-class">
                                    <div ref="followUpIsPrivate"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="followUpTimeLineContainer" class="tab-pane">
                <div class="panel panel-default borderTop-1">
                    <div class="panel-body">
                        <div id="followUpTimeLine" ref="followUpTimeLine" style="overflow:auto; height:300px;"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-8">
            <div id="followUpDocumentTransfers" class="tab-pane">
                <div class="panel panel-default borderTop-1">
                    <div class="panel-body">
                        <div id="gridPanel">
                            <div class="table-responsive glyphicon-container-color">
                                <table id="{{ComponentId}}_grdFollowUpDocumentTransfersItems" ref="grdFollowUpDocumentTransfersItems" class="table table-striped table-hover"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div ref="followUpMetaDataContainer" hidden class="row hidden">
        <div class="col-lg-12">
                <div class="tab-pane">
            <div class="row">
                    <div class='btn-group pull-right toRemove'>
                        <button type='button' class='btn-back btn btn-warning toRemoveBtn p-2 mb-3'><em class='fa fa-reply {{#ifEquals Language "ar"}}flipHorizental{{/ifEquals}} mr-sm'></em>{{Localizer 'Back'}}</button>
                    </div>
                    <div ref="{{ComponentId}}_followUpMetaData"></div>
                </div>
            </div>
            </div>
            <div class="col-lg-12">
                <div id="followUpActivtyLogsContainer" class="tab-pane">
                    <div class="row">
                        <div class="panel panel-default borderTop-1">
                            <div class="panel-body">
                                <div id="gridPanel">
                                    <div >
                                        <div class="table-responsive glyphicon-container-color">
                                            <table id="grdFollowUpActivtyLogsItems"ref="grdFollowUpActivtyLogsItems" class="table table-striped table-hover"></table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>